import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseDomainEvent } from './base-domain-event';

/**
 * Domain Event Publisher
 * 
 * Publishes domain events to the event bus for handling by event handlers.
 * Provides a clean abstraction over the underlying event emitter.
 */
@Injectable()
export class DomainEventPublisher {
  constructor(private readonly eventEmitter: EventEmitter2) {}

  /**
   * Publish a single domain event
   */
  async publish(event: BaseDomainEvent): Promise<void> {
    await this.eventEmitter.emitAsync(event.eventName, event);
  }

  /**
   * Publish multiple domain events
   */
  async publishAll(events: BaseDomainEvent[]): Promise<void> {
    const promises = events.map(event => this.publish(event));
    await Promise.all(promises);
  }

  /**
   * Publish event synchronously (fire and forget)
   */
  publishSync(event: BaseDomainEvent): void {
    this.eventEmitter.emit(event.eventName, event);
  }

  /**
   * Publish multiple events synchronously
   */
  publishAllSync(events: BaseDomainEvent[]): void {
    events.forEach(event => this.publishSync(event));
  }
}
