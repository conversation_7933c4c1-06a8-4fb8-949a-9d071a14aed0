import { BaseAggregateRoot, UniqueEntityId } from '../../../../shared-kernel';
import { EventMetadata } from '../value-objects/event-metadata/event-metadata.value-object';
import { EventType } from '../enums/event-type.enum';
import { EventSeverity } from '../enums/event-severity.enum';
import { EventStatus } from '../enums/event-status.enum';
import { EventProcessingStatus } from '../enums/event-processing-status.enum';
import { CorrelationStatus } from '../enums/correlation-status.enum';
import { ConfidenceLevel } from '../enums/confidence-level.enum';
import { CorrelatedEventCreatedDomainEvent } from '../events/correlated-event-created.domain-event';
import { CorrelatedEventStatusChangedDomainEvent } from '../events/correlated-event-status-changed.domain-event';
import { CorrelatedEventCorrelationFailedDomainEvent } from '../events/correlated-event-correlation-failed.domain-event';

// Export CorrelationStatus for use in other files
export { CorrelationStatus };

/**
 * Correlation Rule Interface
 */
export interface CorrelationRule {
  /** Rule identifier */
  id: string;
  /** Rule name */
  name: string;
  /** Rule description */
  description: string;
  /** Rule type (temporal, spatial, pattern, etc.) */
  type: CorrelationRuleType;
  /** Rule priority (higher number = higher priority) */
  priority: number;
  /** Whether the rule is required for successful correlation */
  required: boolean;
  /** Time window for correlation in milliseconds */
  timeWindowMs: number;
  /** Maximum events to correlate */
  maxEvents?: number;
  /** Minimum confidence threshold */
  minConfidence: number;
  /** Rule configuration */
  config?: Record<string, any>;
  /** Rule timeout in milliseconds */
  timeoutMs?: number;
}

/**
 * Correlation Rule Type Enum
 */
export enum CorrelationRuleType {
  TEMPORAL = 'TEMPORAL',           // Time-based correlation
  SPATIAL = 'SPATIAL',             // Location/network-based correlation
  PATTERN = 'PATTERN',             // Pattern matching correlation
  BEHAVIORAL = 'BEHAVIORAL',       // Behavioral analysis correlation
  SIGNATURE = 'SIGNATURE',         // Signature-based correlation
  STATISTICAL = 'STATISTICAL',     // Statistical correlation
  SEMANTIC = 'SEMANTIC',           // Semantic/content correlation
  CAUSAL = 'CAUSAL',              // Causal relationship correlation
}

/**
 * Correlation Match Interface
 */
export interface CorrelationMatch {
  /** Matched event ID */
  eventId: UniqueEntityId;
  /** Match confidence score (0-100) */
  confidence: number;
  /** Match type */
  matchType: CorrelationMatchType;
  /** Correlation rule that created this match */
  ruleId: string;
  /** Match details */
  details: Record<string, any>;
  /** When the match was found */
  timestamp: Date;
  /** Match weight in correlation */
  weight: number;
  /** Additional metadata */
  metadata?: Record<string, any>;
}

/**
 * Correlation Match Type Enum
 */
export enum CorrelationMatchType {
  EXACT = 'EXACT',                 // Exact match
  PARTIAL = 'PARTIAL',             // Partial match
  FUZZY = 'FUZZY',                // Fuzzy match
  PATTERN = 'PATTERN',             // Pattern-based match
  TEMPORAL = 'TEMPORAL',           // Time-based match
  SPATIAL = 'SPATIAL',             // Location-based match
  SEMANTIC = 'SEMANTIC',           // Semantic match
  STATISTICAL = 'STATISTICAL',     // Statistical correlation
}

/**
 * Correlation Result Interface
 */
export interface CorrelationResult {
  /** Whether correlation was successful */
  success: boolean;
  /** Applied correlation rules */
  appliedRules: string[];
  /** Failed correlation rules */
  failedRules: string[];
  /** Correlation warnings */
  warnings: string[];
  /** Correlation errors */
  errors: string[];
  /** Processing duration in milliseconds */
  processingDurationMs: number;
  /** Overall correlation confidence score (0-100) */
  confidenceScore: number;
  /** Number of correlation rules used */
  rulesUsed: number;
  /** Total matches found */
  matchesFound: number;
  /** Correlation patterns identified */
  patternsIdentified: string[];
  /** Attack chain indicators */
  attackChainIndicators?: string[];
}

/**
 * Attack Chain Interface
 */
export interface AttackChain {
  /** Chain identifier */
  id: string;
  /** Chain name/title */
  name: string;
  /** Chain description */
  description: string;
  /** Attack stages in chronological order */
  stages: AttackStage[];
  /** Overall chain confidence */
  confidence: ConfidenceLevel;
  /** Chain severity */
  severity: EventSeverity;
  /** Chain tactics (MITRE ATT&CK) */
  tactics?: string[];
  /** Chain techniques (MITRE ATT&CK) */
  techniques?: string[];
  /** Chain timeline */
  timeline: {
    startTime: Date;
    endTime: Date;
    duration: number;
  };
  /** Chain metadata */
  metadata?: Record<string, any>;
}

/**
 * Attack Stage Interface
 */
export interface AttackStage {
  /** Stage identifier */
  id: string;
  /** Stage name */
  name: string;
  /** Stage description */
  description: string;
  /** Events in this stage */
  eventIds: UniqueEntityId[];
  /** Stage order in attack chain */
  order: number;
  /** Stage confidence */
  confidence: ConfidenceLevel;
  /** Stage timestamp */
  timestamp: Date;
  /** MITRE ATT&CK tactic */
  tactic?: string;
  /** MITRE ATT&CK techniques */
  techniques?: string[];
  /** Stage metadata */
  metadata?: Record<string, any>;
}

/**
 * CorrelatedEvent Entity Properties
 */
export interface CorrelatedEventProps {
  /** Original enriched event ID that was correlated */
  enrichedEventId: UniqueEntityId;
  /** Event metadata containing timestamp, source, and processing information */
  metadata: EventMetadata;
  /** Type of the security event */
  type: EventType;
  /** Severity level of the event */
  severity: EventSeverity;
  /** Current status of the event */
  status: EventStatus;
  /** Current processing status in the pipeline */
  processingStatus: EventProcessingStatus;
  /** Current correlation status */
  correlationStatus: CorrelationStatus;
  /** Original enriched event data */
  enrichedData: Record<string, any>;
  /** Correlated event data with correlation context */
  correlatedData: Record<string, any>;
  /** Event title/summary */
  title: string;
  /** Detailed description of the event */
  description?: string;
  /** Tags for categorization and filtering */
  tags?: string[];
  /** Risk score (0-100) */
  riskScore?: number;
  /** Confidence level */
  confidenceLevel: ConfidenceLevel;
  /** Additional custom attributes */
  attributes?: Record<string, any>;
  /** Event correlation ID for grouping related events */
  correlationId: string;
  /** Parent event ID if this is a child event */
  parentEventId?: UniqueEntityId;
  /** Child event IDs if this is a parent event */
  childEventIds: UniqueEntityId[];
  /** Applied correlation rules */
  appliedRules: CorrelationRule[];
  /** Correlation matches found */
  correlationMatches: CorrelationMatch[];
  /** Correlation result details */
  correlationResult?: CorrelationResult;
  /** When correlation started */
  correlationStartedAt?: Date;
  /** When correlation completed */
  correlationCompletedAt?: Date;
  /** Correlation processing attempts */
  correlationAttempts?: number;
  /** Last correlation error */
  lastCorrelationError?: string;
  /** Attack chain information */
  attackChain?: AttackChain;
  /** Related event IDs (correlated events) */
  relatedEventIds: UniqueEntityId[];
  /** Correlation patterns identified */
  correlationPatterns: string[];
  /** Temporal correlation data */
  temporalCorrelation?: {
    timeWindow: number;
    eventSequence: UniqueEntityId[];
    patternType: string;
    confidence: number;
  };
  /** Spatial correlation data */
  spatialCorrelation?: {
    sourceIps: string[];
    targetIps: string[];
    networkSegments: string[];
    geographicRegions: string[];
    confidence: number;
  };
  /** Behavioral correlation data */
  behavioralCorrelation?: {
    userPatterns: string[];
    systemPatterns: string[];
    anomalyScore: number;
    baselineDeviation: number;
    confidence: number;
  };
  /** Whether the correlated event requires manual review */
  requiresManualReview?: boolean;
  /** Manual review notes */
  reviewNotes?: string;
  /** Who reviewed the event */
  reviewedBy?: string;
  /** When the event was reviewed */
  reviewedAt?: Date;
  /** Correlation quality score (0-100) */
  correlationQualityScore?: number;
  /** Validation errors found during correlation */
  validationErrors?: string[];
}

/**
 * CorrelatedEvent Entity
 * 
 * Represents a security event that has been processed through the correlation pipeline.
 * Correlated events have relationships with other events and provide context about
 * attack patterns, sequences, and coordinated activities.
 * 
 * Key responsibilities:
 * - Maintain correlated event state and lifecycle
 * - Enforce correlation business rules and data quality standards
 * - Track correlation process and applied rules
 * - Generate domain events for correlation state changes
 * - Support attack chain analysis and pattern recognition
 * - Manage manual review workflow for complex correlations
 * - Maintain relationships with related events
 * 
 * Business Rules:
 * - Correlated events must reference a valid enriched event
 * - Correlation matches must include confidence scores and rule attribution
 * - Attack chains must have chronological ordering and valid stages
 * - High-confidence correlations may trigger automated responses
 * - Correlation attempts are tracked and limited
 * - Failed correlation must preserve data integrity and provide fallback
 */
export class CorrelatedEvent extends BaseAggregateRoot<CorrelatedEventProps> {
  private static readonly MAX_CORRELATION_ATTEMPTS = 3;
  private static readonly MIN_CORRELATION_QUALITY_SCORE = 70;
  private static readonly HIGH_CONFIDENCE_THRESHOLD = 85;
  private static readonly MAX_VALIDATION_ERRORS = 10;
  private static readonly MAX_CORRELATION_MATCHES = 100;
  private static readonly MAX_RELATED_EVENTS = 500;

  constructor(props: CorrelatedEventProps, id?: UniqueEntityId) {
    super(props, id);
    this.validateInvariants();
  }

  /**
   * Create a new CorrelatedEvent
   */
  static create(props: CorrelatedEventProps, id?: UniqueEntityId): CorrelatedEvent {
    const correlatedEvent = new CorrelatedEvent(props, id);
    
    // Add domain event for correlated event creation
    correlatedEvent.addDomainEvent(new CorrelatedEventCreatedDomainEvent(
      correlatedEvent.id,
      {
        enrichedEventId: props.enrichedEventId,
        eventType: props.type,
        severity: props.severity,
        correlationStatus: props.correlationStatus,
        correlationQualityScore: props.correlationQualityScore,
        appliedRulesCount: props.appliedRules.length,
        correlationMatchesCount: props.correlationMatches.length,
        relatedEventsCount: props.relatedEventIds.length,
        confidenceLevel: props.confidenceLevel,
        hasAttackChain: !!props.attackChain,
        requiresManualReview: props.requiresManualReview || false,
      }
    ));

    return correlatedEvent;
  }

  protected validateInvariants(): void {
    super.validateInvariants();

    if (!this.props.enrichedEventId) {
      throw new Error('CorrelatedEvent must reference an enriched event');
    }

    if (!this.props.metadata) {
      throw new Error('CorrelatedEvent must have metadata');
    }

    if (!this.props.type) {
      throw new Error('CorrelatedEvent must have a type');
    }

    if (!this.props.severity) {
      throw new Error('CorrelatedEvent must have a severity');
    }

    if (!this.props.status) {
      throw new Error('CorrelatedEvent must have a status');
    }

    if (!this.props.processingStatus) {
      throw new Error('CorrelatedEvent must have a processing status');
    }

    if (!this.props.correlationStatus) {
      throw new Error('CorrelatedEvent must have a correlation status');
    }

    if (!this.props.enrichedData) {
      throw new Error('CorrelatedEvent must have enriched data');
    }

    if (!this.props.correlatedData) {
      throw new Error('CorrelatedEvent must have correlated data');
    }

    if (!this.props.title || this.props.title.trim().length === 0) {
      throw new Error('CorrelatedEvent must have a non-empty title');
    }

    if (!this.props.confidenceLevel) {
      throw new Error('CorrelatedEvent must have a confidence level');
    }

    if (!this.props.correlationId || this.props.correlationId.trim().length === 0) {
      throw new Error('CorrelatedEvent must have a correlation ID');
    }

    if (!Array.isArray(this.props.childEventIds)) {
      throw new Error('CorrelatedEvent must have child event IDs array');
    }

    if (!Array.isArray(this.props.appliedRules)) {
      throw new Error('CorrelatedEvent must have applied rules array');
    }

    if (!Array.isArray(this.props.correlationMatches)) {
      throw new Error('CorrelatedEvent must have correlation matches array');
    }

    if (!Array.isArray(this.props.relatedEventIds)) {
      throw new Error('CorrelatedEvent must have related event IDs array');
    }

    if (!Array.isArray(this.props.correlationPatterns)) {
      throw new Error('CorrelatedEvent must have correlation patterns array');
    }

    if (this.props.correlationMatches.length > CorrelatedEvent.MAX_CORRELATION_MATCHES) {
      throw new Error(`CorrelatedEvent cannot have more than ${CorrelatedEvent.MAX_CORRELATION_MATCHES} correlation matches`);
    }

    if (this.props.relatedEventIds.length > CorrelatedEvent.MAX_RELATED_EVENTS) {
      throw new Error(`CorrelatedEvent cannot have more than ${CorrelatedEvent.MAX_RELATED_EVENTS} related events`);
    }

    if (this.props.correlationQualityScore !== undefined && 
        (this.props.correlationQualityScore < 0 || this.props.correlationQualityScore > 100)) {
      throw new Error('Correlation quality score must be between 0 and 100');
    }

    if (this.props.riskScore !== undefined && 
        (this.props.riskScore < 0 || this.props.riskScore > 100)) {
      throw new Error('Risk score must be between 0 and 100');
    }

    if (this.props.correlationAttempts !== undefined && this.props.correlationAttempts < 0) {
      throw new Error('Correlation attempts cannot be negative');
    }

    if (this.props.validationErrors && 
        this.props.validationErrors.length > CorrelatedEvent.MAX_VALIDATION_ERRORS) {
      throw new Error(`Cannot have more than ${CorrelatedEvent.MAX_VALIDATION_ERRORS} validation errors`);
    }

    // Validate correlation status consistency
    this.validateCorrelationStatusConsistency();

    // Validate correlation data integrity
    this.validateCorrelationDataIntegrity();

    // Validate attack chain if present
    if (this.props.attackChain) {
      this.validateAttackChain();
    }
  }

  private validateCorrelationStatusConsistency(): void {
    // If correlation is completed, it should have completion timestamp
    if (this.props.correlationStatus === CorrelationStatus.COMPLETED) {
      if (!this.props.correlationCompletedAt) {
        throw new Error('Completed correlation must have completion timestamp');
      }
      if (!this.props.correlationResult) {
        throw new Error('Completed correlation must have result');
      }
    }

    // If correlation failed, it should have error information
    if (this.props.correlationStatus === CorrelationStatus.FAILED) {
      if (!this.props.lastCorrelationError && 
          (!this.props.correlationResult || this.props.correlationResult.errors.length === 0)) {
        throw new Error('Failed correlation must have error information');
      }
    }

    // If correlation is in progress, it should have started timestamp
    if (this.props.correlationStatus === CorrelationStatus.IN_PROGRESS) {
      if (!this.props.correlationStartedAt) {
        throw new Error('In-progress correlation must have start timestamp');
      }
    }

    // Manual review consistency
    if (this.props.requiresManualReview && this.props.reviewedAt) {
      if (!this.props.reviewedBy) {
        throw new Error('Reviewed events must have reviewer information');
      }
    }
  }

  private validateCorrelationDataIntegrity(): void {
    // Validate correlation matches
    for (const match of this.props.correlationMatches) {
      if (!match.eventId) {
        throw new Error('Correlation match must have an event ID');
      }
      if (match.confidence < 0 || match.confidence > 100) {
        throw new Error('Correlation match confidence must be between 0 and 100');
      }
      if (!match.ruleId) {
        throw new Error('Correlation match must have a rule ID');
      }
      if (!match.timestamp) {
        throw new Error('Correlation match must have a timestamp');
      }
      if (match.weight < 0 || match.weight > 1) {
        throw new Error('Correlation match weight must be between 0 and 1');
      }
    }

    // Validate temporal correlation if present
    if (this.props.temporalCorrelation) {
      const temporal = this.props.temporalCorrelation;
      if (temporal.timeWindow <= 0) {
        throw new Error('Temporal correlation time window must be positive');
      }
      if (temporal.confidence < 0 || temporal.confidence > 100) {
        throw new Error('Temporal correlation confidence must be between 0 and 100');
      }
    }

    // Validate spatial correlation if present
    if (this.props.spatialCorrelation) {
      const spatial = this.props.spatialCorrelation;
      if (spatial.confidence < 0 || spatial.confidence > 100) {
        throw new Error('Spatial correlation confidence must be between 0 and 100');
      }
    }

    // Validate behavioral correlation if present
    if (this.props.behavioralCorrelation) {
      const behavioral = this.props.behavioralCorrelation;
      if (behavioral.anomalyScore < 0 || behavioral.anomalyScore > 100) {
        throw new Error('Behavioral correlation anomaly score must be between 0 and 100');
      }
      if (behavioral.confidence < 0 || behavioral.confidence > 100) {
        throw new Error('Behavioral correlation confidence must be between 0 and 100');
      }
    }
  }

  private validateAttackChain(): void {
    const attackChain = this.props.attackChain!;
    
    if (!attackChain.id || attackChain.id.trim().length === 0) {
      throw new Error('Attack chain must have an ID');
    }

    if (!attackChain.name || attackChain.name.trim().length === 0) {
      throw new Error('Attack chain must have a name');
    }

    if (!Array.isArray(attackChain.stages) || attackChain.stages.length === 0) {
      throw new Error('Attack chain must have at least one stage');
    }

    if (!attackChain.timeline) {
      throw new Error('Attack chain must have timeline information');
    }

    // Validate stages are in chronological order
    for (let i = 1; i < attackChain.stages.length; i++) {
      const prevStage = attackChain.stages[i - 1];
      const currentStage = attackChain.stages[i];
      
      if (currentStage.order <= prevStage.order) {
        throw new Error('Attack chain stages must be in chronological order');
      }
      
      if (currentStage.timestamp < prevStage.timestamp) {
        throw new Error('Attack chain stage timestamps must be chronological');
      }
    }

    // Validate timeline consistency
    const timeline = attackChain.timeline;
    if (timeline.endTime < timeline.startTime) {
      throw new Error('Attack chain end time must be after start time');
    }

    const calculatedDuration = timeline.endTime.getTime() - timeline.startTime.getTime();
    if (Math.abs(timeline.duration - calculatedDuration) > 1000) { // Allow 1 second tolerance
      throw new Error('Attack chain duration must match start and end times');
    }
  }

  // Getters
  get enrichedEventId(): UniqueEntityId {
    return this.props.enrichedEventId;
  }

  get metadata(): EventMetadata {
    return this.props.metadata;
  }

  get type(): EventType {
    return this.props.type;
  }

  get severity(): EventSeverity {
    return this.props.severity;
  }

  get status(): EventStatus {
    return this.props.status;
  }

  get processingStatus(): EventProcessingStatus {
    return this.props.processingStatus;
  }

  get correlationStatus(): CorrelationStatus {
    return this.props.correlationStatus;
  }

  get enrichedData(): Record<string, any> {
    return { ...this.props.enrichedData };
  }

  get correlatedData(): Record<string, any> {
    return { ...this.props.correlatedData };
  }

  get title(): string {
    return this.props.title;
  }

  get description(): string | undefined {
    return this.props.description;
  }

  get tags(): string[] {
    return this.props.tags ? [...this.props.tags] : [];
  }

  get riskScore(): number | undefined {
    return this.props.riskScore;
  }

  get confidenceLevel(): ConfidenceLevel {
    return this.props.confidenceLevel;
  }

  get attributes(): Record<string, any> {
    return this.props.attributes ? { ...this.props.attributes } : {};
  }

  get correlationId(): string {
    return this.props.correlationId;
  }

  get parentEventId(): UniqueEntityId | undefined {
    return this.props.parentEventId;
  }

  get childEventIds(): UniqueEntityId[] {
    return [...this.props.childEventIds];
  }

  get appliedRules(): CorrelationRule[] {
    return [...this.props.appliedRules];
  }

  get correlationMatches(): CorrelationMatch[] {
    return [...this.props.correlationMatches];
  }

  get correlationResult(): CorrelationResult | undefined {
    return this.props.correlationResult ? { ...this.props.correlationResult } : undefined;
  }

  get correlationStartedAt(): Date | undefined {
    return this.props.correlationStartedAt;
  }

  get correlationCompletedAt(): Date | undefined {
    return this.props.correlationCompletedAt;
  }

  get correlationAttempts(): number {
    return this.props.correlationAttempts || 0;
  }

  get lastCorrelationError(): string | undefined {
    return this.props.lastCorrelationError;
  }

  get attackChain(): AttackChain | undefined {
    return this.props.attackChain ? { ...this.props.attackChain } : undefined;
  }

  get relatedEventIds(): UniqueEntityId[] {
    return [...this.props.relatedEventIds];
  }

  get correlationPatterns(): string[] {
    return [...this.props.correlationPatterns];
  }

  get temporalCorrelation(): typeof this.props.temporalCorrelation {
    return this.props.temporalCorrelation ? { ...this.props.temporalCorrelation } : undefined;
  }

  get spatialCorrelation(): typeof this.props.spatialCorrelation {
    return this.props.spatialCorrelation ? { ...this.props.spatialCorrelation } : undefined;
  }

  get behavioralCorrelation(): typeof this.props.behavioralCorrelation {
    return this.props.behavioralCorrelation ? { ...this.props.behavioralCorrelation } : undefined;
  }

  get requiresManualReview(): boolean {
    return this.props.requiresManualReview || false;
  }

  get reviewNotes(): string | undefined {
    return this.props.reviewNotes;
  }

  get reviewedBy(): string | undefined {
    return this.props.reviewedBy;
  }

  get reviewedAt(): Date | undefined {
    return this.props.reviewedAt;
  }

  get correlationQualityScore(): number | undefined {
    return this.props.correlationQualityScore;
  }

  get validationErrors(): string[] {
    return this.props.validationErrors ? [...this.props.validationErrors] : [];
  }

  // Business methods

  /**
   * Start correlation process
   */
  startCorrelation(): void {
    if (this.props.correlationStatus !== CorrelationStatus.PENDING) {
      throw new Error('Can only start correlation for pending events');
    }

    this.props.correlationStatus = CorrelationStatus.IN_PROGRESS;
    this.props.correlationStartedAt = new Date();
    this.props.correlationAttempts = (this.props.correlationAttempts || 0) + 1;

    this.validateInvariants();
  }

  /**
   * Complete correlation process
   */
  completeCorrelation(result: CorrelationResult): void {
    if (this.props.correlationStatus !== CorrelationStatus.IN_PROGRESS) {
      throw new Error('Can only complete correlation for in-progress events');
    }

    this.props.correlationStatus = result.success ? CorrelationStatus.COMPLETED : CorrelationStatus.PARTIAL;
    this.props.correlationCompletedAt = new Date();
    this.props.correlationResult = result;
    this.props.lastCorrelationError = undefined;

    // Calculate correlation quality score based on result
    this.calculateCorrelationQualityScore(result);

    // Determine if manual review is required
    this.determineManualReviewRequirement();

    this.addDomainEvent(new CorrelatedEventStatusChangedDomainEvent(
      this.id,
      {
        oldStatus: CorrelationStatus.IN_PROGRESS,
        newStatus: this.props.correlationStatus,
        result,
        correlationQualityScore: this.props.correlationQualityScore,
        requiresManualReview: this.props.requiresManualReview || false,
      }
    ));

    this.validateInvariants();
  }

  /**
   * Fail correlation process
   */
  failCorrelation(error: string, result?: Partial<CorrelationResult>): void {
    if (this.props.correlationStatus !== CorrelationStatus.IN_PROGRESS) {
      throw new Error('Can only fail correlation for in-progress events');
    }

    this.props.correlationStatus = CorrelationStatus.FAILED;
    this.props.lastCorrelationError = error;
    
    if (result) {
      this.props.correlationResult = {
        success: false,
        appliedRules: result.appliedRules || [],
        failedRules: result.failedRules || [],
        warnings: result.warnings || [],
        errors: result.errors || [error],
        processingDurationMs: result.processingDurationMs || 0,
        confidenceScore: result.confidenceScore || 0,
        rulesUsed: result.rulesUsed || 0,
        matchesFound: result.matchesFound || 0,
        patternsIdentified: result.patternsIdentified || [],
      };
    }

    this.addDomainEvent(new CorrelatedEventCorrelationFailedDomainEvent(
      this.id,
      {
        enrichedEventId: this.props.enrichedEventId,
        error,
        attempt: this.correlationAttempts,
        maxAttemptsExceeded: this.hasExceededMaxCorrelationAttempts(),
      }
    ));

    this.validateInvariants();
  }

  /**
   * Skip correlation process
   */
  skipCorrelation(reason: string): void {
    if (![CorrelationStatus.PENDING, CorrelationStatus.FAILED].includes(this.props.correlationStatus)) {
      throw new Error('Can only skip correlation for pending or failed events');
    }

    this.props.correlationStatus = CorrelationStatus.SKIPPED;
    this.props.lastCorrelationError = undefined;
    this.props.reviewNotes = reason;

    this.validateInvariants();
  }

  /**
   * Reset correlation for retry
   */
  resetCorrelation(): void {
    if (this.hasExceededMaxCorrelationAttempts()) {
      throw new Error('Cannot reset correlation: maximum attempts exceeded');
    }

    this.props.correlationStatus = CorrelationStatus.PENDING;
    this.props.correlationStartedAt = undefined;
    this.props.correlationCompletedAt = undefined;
    this.props.lastCorrelationError = undefined;
    this.props.correlationResult = undefined;

    this.validateInvariants();
  }

  /**
   * Add correlation match
   */
  addCorrelationMatch(match: CorrelationMatch): void {
    if (this.props.correlationMatches.length >= CorrelatedEvent.MAX_CORRELATION_MATCHES) {
      throw new Error(`Cannot add more than ${CorrelatedEvent.MAX_CORRELATION_MATCHES} correlation matches`);
    }

    // Check for duplicate match
    const existingMatch = this.props.correlationMatches.find(
      m => m.eventId.equals(match.eventId) && m.ruleId === match.ruleId
    );

    if (existingMatch) {
      // Update existing match with higher confidence
      if (match.confidence > existingMatch.confidence) {
        Object.assign(existingMatch, match);
      }
    } else {
      // Add new match
      this.props.correlationMatches.push(match);
    }

    this.validateInvariants();
  }

  /**
   * Add related event
   */
  addRelatedEvent(eventId: UniqueEntityId): void {
    if (this.props.relatedEventIds.length >= CorrelatedEvent.MAX_RELATED_EVENTS) {
      throw new Error(`Cannot add more than ${CorrelatedEvent.MAX_RELATED_EVENTS} related events`);
    }

    // Check for duplicate
    const exists = this.props.relatedEventIds.some(id => id.equals(eventId));
    if (!exists) {
      this.props.relatedEventIds.push(eventId);
    }
  }

  /**
   * Add child event
   */
  addChildEvent(eventId: UniqueEntityId): void {
    // Check for duplicate
    const exists = this.props.childEventIds.some(id => id.equals(eventId));
    if (!exists) {
      this.props.childEventIds.push(eventId);
    }
  }

  /**
   * Set attack chain
   */
  setAttackChain(attackChain: AttackChain): void {
    this.props.attackChain = attackChain;
    this.validateInvariants();
  }

  /**
   * Add correlation pattern
   */
  addCorrelationPattern(pattern: string): void {
    if (!this.props.correlationPatterns.includes(pattern)) {
      this.props.correlationPatterns.push(pattern);
    }
  }

  /**
   * Update temporal correlation
   */
  updateTemporalCorrelation(temporal: NonNullable<CorrelatedEventProps['temporalCorrelation']>): void {
    this.props.temporalCorrelation = temporal;
    this.validateInvariants();
  }

  /**
   * Update spatial correlation
   */
  updateSpatialCorrelation(spatial: NonNullable<CorrelatedEventProps['spatialCorrelation']>): void {
    this.props.spatialCorrelation = spatial;
    this.validateInvariants();
  }

  /**
   * Update behavioral correlation
   */
  updateBehavioralCorrelation(behavioral: NonNullable<CorrelatedEventProps['behavioralCorrelation']>): void {
    this.props.behavioralCorrelation = behavioral;
    this.validateInvariants();
  }

  /**
   * Update correlated data
   */
  updateCorrelatedData(correlatedData: Record<string, any>): void {
    this.props.correlatedData = { ...this.props.correlatedData, ...correlatedData };
  }

  /**
   * Add applied correlation rule
   */
  addAppliedRule(rule: CorrelationRule): void {
    const existingRule = this.props.appliedRules.find(r => r.id === rule.id);
    if (!existingRule) {
      this.props.appliedRules.push(rule);
    }
  }

  /**
   * Update correlation quality score
   */
  updateCorrelationQualityScore(score: number): void {
    if (score < 0 || score > 100) {
      throw new Error('Correlation quality score must be between 0 and 100');
    }

    this.props.correlationQualityScore = score;
    this.determineManualReviewRequirement();
  }

  /**
   * Add validation errors
   */
  addValidationErrors(errors: string[]): void {
    const currentErrors = this.props.validationErrors || [];
    const newErrors = [...currentErrors, ...errors];

    if (newErrors.length > CorrelatedEvent.MAX_VALIDATION_ERRORS) {
      throw new Error(`Cannot have more than ${CorrelatedEvent.MAX_VALIDATION_ERRORS} validation errors`);
    }

    this.props.validationErrors = newErrors;
  }

  /**
   * Clear validation errors
   */
  clearValidationErrors(): void {
    this.props.validationErrors = [];
  }

  /**
   * Mark for manual review
   */
  markForManualReview(reason: string): void {
    this.props.requiresManualReview = true;
    this.props.reviewNotes = reason;
  }

  /**
   * Complete manual review
   */
  completeManualReview(reviewedBy: string, notes?: string): void {
    if (!this.props.requiresManualReview) {
      throw new Error('Event is not marked for manual review');
    }

    this.props.reviewedBy = reviewedBy;
    this.props.reviewedAt = new Date();
    if (notes) {
      this.props.reviewNotes = notes;
    }
  }

  // Query methods

  /**
   * Check if correlation is completed
   */
  isCorrelationCompleted(): boolean {
    return this.props.correlationStatus === CorrelationStatus.COMPLETED;
  }

  /**
   * Check if correlation failed
   */
  isCorrelationFailed(): boolean {
    return this.props.correlationStatus === CorrelationStatus.FAILED;
  }

  /**
   * Check if correlation is in progress
   */
  isCorrelationInProgress(): boolean {
    return this.props.correlationStatus === CorrelationStatus.IN_PROGRESS;
  }

  /**
   * Check if correlation was skipped
   */
  isCorrelationSkipped(): boolean {
    return this.props.correlationStatus === CorrelationStatus.SKIPPED;
  }

  /**
   * Check if correlation is partial
   */
  isCorrelationPartial(): boolean {
    return this.props.correlationStatus === CorrelationStatus.PARTIAL;
  }

  /**
   * Check if event has high correlation quality
   */
  hasHighCorrelationQuality(): boolean {
    return (this.props.correlationQualityScore || 0) >= CorrelatedEvent.MIN_CORRELATION_QUALITY_SCORE;
  }

  /**
   * Check if event has validation errors
   */
  hasValidationErrors(): boolean {
    return (this.props.validationErrors?.length || 0) > 0;
  }

  /**
   * Check if event has exceeded max correlation attempts
   */
  hasExceededMaxCorrelationAttempts(): boolean {
    return this.correlationAttempts >= CorrelatedEvent.MAX_CORRELATION_ATTEMPTS;
  }

  /**
   * Check if event is ready for next processing stage
   */
  isReadyForNextStage(): boolean {
    return (this.isCorrelationCompleted() || this.isCorrelationPartial()) && 
           this.hasHighCorrelationQuality() && 
           !this.hasValidationErrors() &&
           (!this.requiresManualReview || this.reviewedAt !== undefined);
  }

  /**
   * Check if event has high confidence correlation
   */
  isHighConfidenceCorrelation(): boolean {
    return (this.props.correlationResult?.confidenceScore || 0) >= CorrelatedEvent.HIGH_CONFIDENCE_THRESHOLD;
  }

  /**
   * Check if event has attack chain
   */
  hasAttackChain(): boolean {
    return !!this.props.attackChain;
  }

  /**
   * Check if event has temporal correlation
   */
  hasTemporalCorrelation(): boolean {
    return !!this.props.temporalCorrelation;
  }

  /**
   * Check if event has spatial correlation
   */
  hasSpatialCorrelation(): boolean {
    return !!this.props.spatialCorrelation;
  }

  /**
   * Check if event has behavioral correlation
   */
  hasBehavioralCorrelation(): boolean {
    return !!this.props.behavioralCorrelation;
  }

  /**
   * Get correlation duration
   */
  getCorrelationDuration(): number | null {
    if (!this.props.correlationStartedAt) {
      return null;
    }

    const endTime = this.props.correlationCompletedAt || new Date();
    return endTime.getTime() - this.props.correlationStartedAt.getTime();
  }

  /**
   * Get applied rule names
   */
  getAppliedRuleNames(): string[] {
    return this.props.appliedRules.map(rule => rule.name);
  }

  /**
   * Check if specific rule was applied
   */
  hasAppliedRule(ruleId: string): boolean {
    return this.props.appliedRules.some(rule => rule.id === ruleId);
  }

  /**
   * Get matches by rule
   */
  getMatchesByRule(ruleId: string): CorrelationMatch[] {
    return this.props.correlationMatches.filter(match => match.ruleId === ruleId);
  }

  /**
   * Get matches by type
   */
  getMatchesByType(matchType: CorrelationMatchType): CorrelationMatch[] {
    return this.props.correlationMatches.filter(match => match.matchType === matchType);
  }

  /**
   * Get average match confidence
   */
  getAverageMatchConfidence(): number | null {
    if (this.props.correlationMatches.length === 0) return null;
    
    const totalConfidence = this.props.correlationMatches.reduce((sum, match) => sum + match.confidence, 0);
    return totalConfidence / this.props.correlationMatches.length;
  }

  /**
   * Get highest confidence match
   */
  getHighestConfidenceMatch(): CorrelationMatch | null {
    if (this.props.correlationMatches.length === 0) return null;
    
    return this.props.correlationMatches.reduce((highest, match) => 
      match.confidence > highest.confidence ? match : highest
    );
  }

  // Private helper methods

  private calculateCorrelationQualityScore(result: CorrelationResult): void {
    let score = 100;

    // Reduce score for failed rules
    const failedRulesPenalty = result.failedRules.length * 15;
    score -= failedRulesPenalty;

    // Reduce score for warnings
    const warningsPenalty = result.warnings.length * 5;
    score -= warningsPenalty;

    // Reduce score for errors
    const errorsPenalty = result.errors.length * 20;
    score -= errorsPenalty;

    // Reduce score for low confidence
    if (result.confidenceScore < 70) {
      score -= (70 - result.confidenceScore);
    }

    // Boost score for patterns identified
    const patternsBonus = Math.min(result.patternsIdentified.length * 5, 20);
    score += patternsBonus;

    // Boost score for matches found
    const matchesBonus = Math.min(result.matchesFound * 2, 15);
    score += matchesBonus;

    this.props.correlationQualityScore = Math.max(0, Math.min(100, score));
  }

  private determineManualReviewRequirement(): void {
    let requiresReview = false;

    // High confidence correlations with attack chains require review
    if (this.hasAttackChain() && this.isHighConfidenceCorrelation()) {
      requiresReview = true;
    }

    // Low correlation quality requires review
    if (!this.hasHighCorrelationQuality()) {
      requiresReview = true;
    }

    // Critical events require review
    if (this.severity === EventSeverity.CRITICAL) {
      requiresReview = true;
    }

    // Too many validation errors require review
    if (this.hasValidationErrors() && this.validationErrors.length > 5) {
      requiresReview = true;
    }

    this.props.requiresManualReview = requiresReview;
  }

  /**
   * Get all event IDs involved in this correlation
   */
  get allEventIds(): UniqueEntityId[] {
    const eventIds: UniqueEntityId[] = [this.props.primaryEventId];
    eventIds.push(...this.props.relatedEventIds);
    return eventIds;
  }

  /**
   * Get correlation type
   */
  get correlationType(): CorrelationType {
    return this.props.correlationType;
  }

  /**
   * Get correlation score
   */
  get score(): number {
    return this.props.correlationQualityScore || this.props.confidenceLevel || 0;
  }

  /**
   * Get event count
   */
  get eventCount(): number {
    return this.props.relatedEventIds.length + 1; // +1 for primary event
  }
}