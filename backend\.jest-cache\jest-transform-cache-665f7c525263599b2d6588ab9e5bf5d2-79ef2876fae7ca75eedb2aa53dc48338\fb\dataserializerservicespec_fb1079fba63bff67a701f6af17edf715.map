{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\communication\\__tests__\\data-serializer.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,2CAA+C;AAC/C,wEAAmE;AAEnE,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;IACrC,IAAI,OAA8B,CAAC;IACnC,IAAI,aAAyC,CAAC;IAE9C,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,iBAAiB,GAAG;YACxB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,CAAC,GAAW,EAAE,YAAkB,EAAE,EAAE;gBACpE,MAAM,MAAM,GAAG;oBACb,sCAAsC,EAAE,IAAI;oBAC5C,oCAAoC,EAAE,CAAC;oBACvC,wCAAwC,EAAE,IAAI;iBAC/C,CAAC;gBACF,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC;YACrC,CAAC,CAAC;SACH,CAAC;QAEF,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,+CAAqB;gBACrB,EAAE,OAAO,EAAE,sBAAa,EAAE,QAAQ,EAAE,iBAAiB,EAAE;aACxD;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAAwB,+CAAqB,CAAC,CAAC;QACnE,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,sBAAa,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;YAC/B,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;gBACtD,MAAM,IAAI,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;gBAExD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAEnD,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC;oBAC3B,MAAM,EAAE,MAAM;oBACd,UAAU,EAAE,KAAK;oBACjB,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAChC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAClC,gBAAgB,EAAE,CAAC;oBACnB,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAClC,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC;wBAChC,QAAQ,EAAE,MAAM;qBACjB,CAAC;iBACH,CAAC,CAAC;gBAEH,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBAC3C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;gBAC9D,yDAAyD;gBACzD,MAAM,SAAS,GAAG;oBAChB,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;wBAC5C,EAAE,EAAE,CAAC;wBACL,IAAI,EAAE,QAAQ,CAAC,EAAE;wBACjB,WAAW,EAAE,6FAA6F;wBAC1G,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,IAAI,EAAE,EAAE,OAAO,EAAE,IAAI,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE;qBACvF,CAAC,CAAC;iBACJ,CAAC;gBAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;gBAExD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrC,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC5C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;gBACnD,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAClE,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;gBACpD,MAAM,IAAI,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;gBAC1C,MAAM,OAAO,GAAG;oBACd,KAAK,EAAE,CAAC;oBACR,gBAAgB,EAAE,IAAI;oBACtB,eAAe,EAAE,SAAkB;oBACnC,QAAQ,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE;iBAC7B,CAAC;gBAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBAE5D,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrC,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC/C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,aAAa,CAAC;oBACpC,QAAQ,EAAE,MAAM;oBAChB,MAAM,EAAE,MAAM;iBACf,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;gBAClD,MAAM,YAAY,GAAG,EAAE,CAAC;gBACxB,YAAY,CAAC,MAAM,CAAC,GAAG,YAAY,CAAC,CAAC,4BAA4B;gBAEjE,MAAM,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;YACnG,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;YACnC,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;gBAC5C,MAAM,YAAY,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;gBAChE,MAAM,UAAU,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;gBAE/D,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;gBAE7D,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;gBACvD,MAAM,YAAY,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;gBAClD,MAAM,UAAU,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,YAAY,EAAE,EAAE,gBAAgB,EAAE,IAAI,EAAE,CAAC,CAAC;gBAE3F,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;gBAE7D,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;gBAC9C,MAAM,YAAY,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;gBAClD,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;gBAChD,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;gBAE/C,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;gBAEzD,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;gBACpD,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;gBAE1D,MAAM,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;YAC1G,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gCAAgC,EAAE,GAAG,EAAE;QAC9C,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;YACnC,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;gBAChE,MAAM,IAAI,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;gBAC1C,MAAM,MAAM,GAAG,aAAa,CAAC;gBAE7B,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAE/D,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC;oBAC3B,MAAM,EAAE,UAAU;oBAClB,MAAM,EAAE,aAAa;oBACrB,UAAU,EAAE,KAAK;oBACjB,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAChC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAClC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAClC,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC;wBAChC,MAAM,EAAE,aAAa;qBACtB,CAAC;iBACH,CAAC,CAAC;gBAEH,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;gBACjD,MAAM,IAAI,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;gBAC1C,MAAM,MAAM,GAAG,aAAa,CAAC;gBAC7B,MAAM,OAAO,GAAG,EAAE,gBAAgB,EAAE,IAAI,EAAE,CAAC;gBAE3C,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;gBAExE,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrC,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACvC,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;gBACxD,MAAM,YAAY,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;gBAClD,MAAM,MAAM,GAAG,aAAa,CAAC;gBAC7B,MAAM,UAAU,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;gBAE3E,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,uBAAuB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;gBAEzE,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;gBAC7C,MAAM,YAAY,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;gBAClD,MAAM,MAAM,GAAG,aAAa,CAAC;gBAC7B,MAAM,UAAU,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;gBAE3E,MAAM,MAAM,CACV,OAAO,CAAC,uBAAuB,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAC/D,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;YACjC,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;gBACjD,MAAM,IAAI,GAAG,eAAe,CAAC;gBAC7B,MAAM,QAAQ,GAAG,QAAQ,CAAC;gBAE1B,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBAE/D,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC;oBAC3B,MAAM,EAAE,QAAQ;oBAChB,UAAU,EAAE,KAAK;oBACjB,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAChC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAClC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAClC,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC;wBAChC,QAAQ,EAAE,QAAQ;qBACnB,CAAC;iBACH,CAAC,CAAC;gBAEH,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;gBACjD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;gBAClD,MAAM,QAAQ,GAAG,KAAK,CAAC;gBAEvB,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBAE/D,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACrC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;gBACjD,MAAM,IAAI,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;gBAC1C,MAAM,QAAQ,GAAG,QAAQ,CAAC;gBAE1B,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBAE/D,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACrC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;YACrC,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;gBAC9C,MAAM,YAAY,GAAG,eAAe,CAAC;gBACrC,MAAM,QAAQ,GAAG,QAAQ,CAAC;gBAC1B,MAAM,UAAU,GAAG,MAAM,OAAO,CAAC,iBAAiB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;gBAE3E,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,qBAAqB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;gBAEzE,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBACtC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;gBAC9C,MAAM,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;gBAE5D,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,qBAAqB,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;gBAE3E,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;YAC5B,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;gBAC/C,mDAAmD;gBACnD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC;gBAE/D,MAAM,UAAU,GAAG,MAAM,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAE5D,MAAM,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBAC1C,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;gBAClD,mDAAmD;gBACnD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC;gBAE/D,MAAM,UAAU,GAAG,MAAM,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gBAE/D,MAAM,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBAC1C,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;gBAC1D,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;gBAElD,MAAM,MAAM,CACV,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,aAAoB,CAAC,CACjD,CAAC,OAAO,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC9B,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;gBAC3C,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,sDAAsD,EAAE,MAAM,CAAC,CAAC;gBACjG,MAAM,UAAU,GAAG,MAAM,OAAO,CAAC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;gBAEpE,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;gBAEtE,MAAM,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;gBAC9C,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,sDAAsD,EAAE,MAAM,CAAC,CAAC;gBACjG,MAAM,UAAU,GAAG,MAAM,OAAO,CAAC,YAAY,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;gBAEvE,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;gBAEzE,MAAM,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;gBAC5D,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;gBAElD,MAAM,MAAM,CACV,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,aAAoB,CAAC,CACnD,CAAC,OAAO,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC9B,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;gBACrD,MAAM,KAAK,GAAG;oBACZ,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,MAAe,EAAE;oBAC9D,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,MAAe,EAAE;oBAC9D,EAAE,IAAI,EAAE,aAAa,EAAE,MAAM,EAAE,QAAiB,EAAE,OAAO,EAAE,EAAE,QAAQ,EAAE,QAAiB,EAAE,EAAE;iBAC7F,CAAC;gBAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAEnD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBACvC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBACtC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC;oBACnC,KAAK,EAAE,CAAC;oBACR,UAAU,EAAE,CAAC;oBACb,MAAM,EAAE,CAAC;oBACT,iBAAiB,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACrC,mBAAmB,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBACvC,uBAAuB,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC3C,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBACnC,CAAC,CAAC;gBAEH,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBAC5B,MAAM,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;wBACzB,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBACxB,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAC1B,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAChC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;wBAClC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;qBACnC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;gBACxD,MAAM,KAAK,GAAG;oBACZ,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,MAAe,EAAE;oBACpD,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,aAAoB,EAAE,EAAE,iBAAiB;oBAC7D,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,MAAM,EAAE,MAAe,EAAE;iBACtD,CAAC;gBAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAEnD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBACvC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBACtC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC;oBACnC,KAAK,EAAE,CAAC;oBACR,UAAU,EAAE,CAAC;oBACb,MAAM,EAAE,CAAC;iBACV,CAAC,CAAC;gBAEH,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC;oBACrC,KAAK,EAAE,CAAC;oBACR,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;iBACzB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;YAChC,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;gBACvD,4BAA4B;gBAC5B,MAAM,aAAa,GAAG;oBACpB,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,MAAe,EAAE;oBAC9D,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,MAAe,EAAE;iBAC/D,CAAC;gBAEF,MAAM,UAAU,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;gBAE/D,mBAAmB;gBACnB,MAAM,gBAAgB,GAAG,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;oBAChE,IAAI,EAAE,IAAI;oBACV,MAAM,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC,MAAM;iBACpC,CAAC,CAAC,CAAC;gBAEJ,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;gBAEhE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBACvC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBACtC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC;oBACnC,KAAK,EAAE,CAAC;oBACR,UAAU,EAAE,CAAC;oBACb,MAAM,EAAE,CAAC;oBACT,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBACnC,CAAC,CAAC;gBAEH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;gBAC/D,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;YACjE,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;gBAC1D,MAAM,KAAK,GAAG;oBACZ,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,MAAM,CAAC,EAAE,MAAM,EAAE,MAAe,EAAE;oBACzE,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE,MAAM,EAAE,MAAe,EAAE;oBACtE,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,MAAM,CAAC,EAAE,MAAM,EAAE,MAAe,EAAE;iBAC3E,CAAC;gBAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;gBAErD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBACvC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBACtC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC;oBACnC,KAAK,EAAE,CAAC;oBACR,UAAU,EAAE,CAAC;oBACb,MAAM,EAAE,CAAC;iBACV,CAAC,CAAC;gBAEH,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC;oBACrC,KAAK,EAAE,CAAC;oBACR,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;iBACzB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;YACrC,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;gBAChD,MAAM,KAAK,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;gBAE9C,MAAM,CAAC,KAAK,CAAC,CAAC,aAAa,CAAC;oBAC1B,kBAAkB,EAAE,IAAI;oBACxB,gBAAgB,EAAE,CAAC;oBACnB,oBAAoB,EAAE,IAAI;oBAC1B,gBAAgB,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC;oBAChD,yBAAyB,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;oBAC9C,kBAAkB,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC;iBAC9C,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,6DAA6D;YAC7D,MAAM,KAAK,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;YAE9C,6CAA6C;YAC7C,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC;YACvE,MAAM,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;YACrE,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,EAAE,CAAC,yBAAyB,EAAE,KAAK,IAAI,EAAE;YACvC,MAAM,UAAU,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAEvD,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAE/C,MAAM,gBAAgB,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;YAEvE,MAAM,CAAC,gBAAgB,CAAC,CAAC,QAAQ,EAAE,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,0FAA0F;YAC1F,4EAA4E;YAC5E,MAAM,IAAI,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;YAChD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAEnD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAE3C,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAC/D,MAAM,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,uCAAuC;QACzF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,KAAK,IAAI,EAAE;YACxC,MAAM,WAAW,GAAG,EAAE,CAAC;YACvB,MAAM,UAAU,GAAG,EAAE,CAAC;YACtB,MAAM,WAAW,GAAG,EAAE,CAAC;YAEvB,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAChE,MAAM,WAAW,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;YAC9D,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;YAElE,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACjD,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAChD,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAEjD,MAAM,kBAAkB,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;YAC3E,MAAM,iBAAiB,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;YAEzE,MAAM,CAAC,kBAAkB,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACvC,MAAM,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC7C,wBAAwB;YACxB,MAAM,SAAS,GAAG;gBAChB,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;oBAC9C,EAAE,EAAE,CAAC;oBACL,IAAI,EAAE,QAAQ,CAAC,8CAA8C;iBAC9D,CAAC,CAAC;aACJ,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAExD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAEnD,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAC/D,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\communication\\__tests__\\data-serializer.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { DataSerializerService } from '../data-serializer.service';\r\n\r\ndescribe('DataSerializerService', () => {\r\n  let service: DataSerializerService;\r\n  let configService: jest.Mocked<ConfigService>;\r\n\r\n  beforeEach(async () => {\r\n    const mockConfigService = {\r\n      get: jest.fn().mockImplementation((key: string, defaultValue?: any) => {\r\n        const config = {\r\n          'ai.serialization.compression.enabled': true,\r\n          'ai.serialization.compression.level': 6,\r\n          'ai.serialization.compression.threshold': 1024,\r\n        };\r\n        return config[key] || defaultValue;\r\n      }),\r\n    };\r\n\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        DataSerializerService,\r\n        { provide: ConfigService, useValue: mockConfigService },\r\n      ],\r\n    }).compile();\r\n\r\n    service = module.get<DataSerializerService>(DataSerializerService);\r\n    configService = module.get(ConfigService);\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('JSON Serialization', () => {\r\n    describe('serializeToJson', () => {\r\n      it('should serialize simple object to JSON', async () => {\r\n        const data = { name: 'test', value: 123, active: true };\r\n\r\n        const result = await service.serializeToJson(data);\r\n\r\n        expect(result).toMatchObject({\r\n          format: 'json',\r\n          compressed: false,\r\n          originalSize: expect.any(Number),\r\n          compressedSize: expect.any(Number),\r\n          compressionRatio: 1,\r\n          processingTime: expect.any(Number),\r\n          metadata: expect.objectContaining({\r\n            encoding: 'utf8',\r\n          }),\r\n        });\r\n\r\n        expect(result.data).toBeInstanceOf(Buffer);\r\n        expect(result.originalSize).toBeGreaterThan(0);\r\n      });\r\n\r\n      it('should serialize large object with compression', async () => {\r\n        // Create large object that exceeds compression threshold\r\n        const largeData = {\r\n          items: Array.from({ length: 100 }, (_, i) => ({\r\n            id: i,\r\n            name: `Item ${i}`,\r\n            description: 'This is a long description that will help make the data large enough to trigger compression',\r\n            metadata: { created: new Date(), updated: new Date(), tags: ['tag1', 'tag2', 'tag3'] },\r\n          })),\r\n        };\r\n\r\n        const result = await service.serializeToJson(largeData);\r\n\r\n        expect(result.compressed).toBe(true);\r\n        expect(result.compressionType).toBe('gzip');\r\n        expect(result.compressionRatio).toBeGreaterThan(1);\r\n        expect(result.compressedSize).toBeLessThan(result.originalSize);\r\n      });\r\n\r\n      it('should serialize with custom options', async () => {\r\n        const data = { name: 'test', value: 123 };\r\n        const options = {\r\n          space: 2,\r\n          forceCompression: true,\r\n          compressionType: 'deflate' as const,\r\n          metadata: { source: 'test' },\r\n        };\r\n\r\n        const result = await service.serializeToJson(data, options);\r\n\r\n        expect(result.compressed).toBe(true);\r\n        expect(result.compressionType).toBe('deflate');\r\n        expect(result.metadata).toMatchObject({\r\n          encoding: 'utf8',\r\n          source: 'test',\r\n        });\r\n      });\r\n\r\n      it('should handle serialization errors', async () => {\r\n        const circularData = {};\r\n        circularData['self'] = circularData; // Create circular reference\r\n\r\n        await expect(service.serializeToJson(circularData)).rejects.toThrow('JSON serialization failed');\r\n      });\r\n    });\r\n\r\n    describe('deserializeFromJson', () => {\r\n      it('should deserialize JSON data', async () => {\r\n        const originalData = { name: 'test', value: 123, active: true };\r\n        const serialized = await service.serializeToJson(originalData);\r\n\r\n        const result = await service.deserializeFromJson(serialized);\r\n\r\n        expect(result).toEqual(originalData);\r\n      });\r\n\r\n      it('should deserialize compressed JSON data', async () => {\r\n        const originalData = { name: 'test', value: 123 };\r\n        const serialized = await service.serializeToJson(originalData, { forceCompression: true });\r\n\r\n        const result = await service.deserializeFromJson(serialized);\r\n\r\n        expect(result).toEqual(originalData);\r\n      });\r\n\r\n      it('should deserialize from Buffer', async () => {\r\n        const originalData = { name: 'test', value: 123 };\r\n        const jsonString = JSON.stringify(originalData);\r\n        const buffer = Buffer.from(jsonString, 'utf8');\r\n\r\n        const result = await service.deserializeFromJson(buffer);\r\n\r\n        expect(result).toEqual(originalData);\r\n      });\r\n\r\n      it('should handle deserialization errors', async () => {\r\n        const invalidBuffer = Buffer.from('invalid json', 'utf8');\r\n\r\n        await expect(service.deserializeFromJson(invalidBuffer)).rejects.toThrow('JSON deserialization failed');\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Protocol Buffers Serialization', () => {\r\n    describe('serializeToProtobuf', () => {\r\n      it('should serialize data to Protocol Buffers format', async () => {\r\n        const data = { name: 'test', value: 123 };\r\n        const schema = 'TestMessage';\r\n\r\n        const result = await service.serializeToProtobuf(data, schema);\r\n\r\n        expect(result).toMatchObject({\r\n          format: 'protobuf',\r\n          schema: 'TestMessage',\r\n          compressed: false,\r\n          originalSize: expect.any(Number),\r\n          compressedSize: expect.any(Number),\r\n          processingTime: expect.any(Number),\r\n          metadata: expect.objectContaining({\r\n            schema: 'TestMessage',\r\n          }),\r\n        });\r\n\r\n        expect(result.data).toBeInstanceOf(Buffer);\r\n      });\r\n\r\n      it('should serialize with compression', async () => {\r\n        const data = { name: 'test', value: 123 };\r\n        const schema = 'TestMessage';\r\n        const options = { forceCompression: true };\r\n\r\n        const result = await service.serializeToProtobuf(data, schema, options);\r\n\r\n        expect(result.compressed).toBe(true);\r\n        expect(result.compressionType).toBe('gzip');\r\n      });\r\n    });\r\n\r\n    describe('deserializeFromProtobuf', () => {\r\n      it('should deserialize Protocol Buffers data', async () => {\r\n        const originalData = { name: 'test', value: 123 };\r\n        const schema = 'TestMessage';\r\n        const serialized = await service.serializeToProtobuf(originalData, schema);\r\n\r\n        const result = await service.deserializeFromProtobuf(serialized, schema);\r\n\r\n        expect(result).toEqual(originalData);\r\n      });\r\n\r\n      it('should handle schema mismatch', async () => {\r\n        const originalData = { name: 'test', value: 123 };\r\n        const schema = 'TestMessage';\r\n        const serialized = await service.serializeToProtobuf(originalData, schema);\r\n\r\n        await expect(\r\n          service.deserializeFromProtobuf(serialized, 'DifferentSchema')\r\n        ).rejects.toThrow('Schema mismatch');\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Binary Serialization', () => {\r\n    describe('serializeToBinary', () => {\r\n      it('should serialize string to binary', async () => {\r\n        const data = 'Hello, World!';\r\n        const encoding = 'base64';\r\n\r\n        const result = await service.serializeToBinary(data, encoding);\r\n\r\n        expect(result).toMatchObject({\r\n          format: 'binary',\r\n          compressed: false,\r\n          originalSize: expect.any(Number),\r\n          compressedSize: expect.any(Number),\r\n          processingTime: expect.any(Number),\r\n          metadata: expect.objectContaining({\r\n            encoding: 'base64',\r\n          }),\r\n        });\r\n\r\n        expect(result.data).toBeInstanceOf(Buffer);\r\n      });\r\n\r\n      it('should serialize Buffer to binary', async () => {\r\n        const data = Buffer.from('Hello, World!', 'utf8');\r\n        const encoding = 'hex';\r\n\r\n        const result = await service.serializeToBinary(data, encoding);\r\n\r\n        expect(result.format).toBe('binary');\r\n        expect(result.metadata.encoding).toBe('hex');\r\n      });\r\n\r\n      it('should serialize object to binary', async () => {\r\n        const data = { name: 'test', value: 123 };\r\n        const encoding = 'base64';\r\n\r\n        const result = await service.serializeToBinary(data, encoding);\r\n\r\n        expect(result.format).toBe('binary');\r\n        expect(result.originalSize).toBeGreaterThan(0);\r\n      });\r\n    });\r\n\r\n    describe('deserializeFromBinary', () => {\r\n      it('should deserialize binary data', async () => {\r\n        const originalData = 'Hello, World!';\r\n        const encoding = 'base64';\r\n        const serialized = await service.serializeToBinary(originalData, encoding);\r\n\r\n        const result = await service.deserializeFromBinary(serialized, encoding);\r\n\r\n        expect(result).toBeInstanceOf(Buffer);\r\n        expect(result.toString('utf8')).toBe(originalData);\r\n      });\r\n\r\n      it('should deserialize from Buffer', async () => {\r\n        const originalBuffer = Buffer.from('Hello, World!', 'utf8');\r\n\r\n        const result = await service.deserializeFromBinary(originalBuffer, 'utf8');\r\n\r\n        expect(result).toEqual(originalBuffer);\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Compression', () => {\r\n    describe('compressData', () => {\r\n      it('should compress data using gzip', async () => {\r\n        // Use larger data that will actually compress well\r\n        const data = Buffer.from('Hello, World! '.repeat(100), 'utf8');\r\n\r\n        const compressed = await service.compressData(data, 'gzip');\r\n\r\n        expect(compressed).toBeInstanceOf(Buffer);\r\n        expect(compressed.length).toBeLessThan(data.length);\r\n      });\r\n\r\n      it('should compress data using deflate', async () => {\r\n        // Use larger data that will actually compress well\r\n        const data = Buffer.from('Hello, World! '.repeat(100), 'utf8');\r\n\r\n        const compressed = await service.compressData(data, 'deflate');\r\n\r\n        expect(compressed).toBeInstanceOf(Buffer);\r\n        expect(compressed.length).toBeLessThan(data.length);\r\n      });\r\n\r\n      it('should handle unsupported compression type', async () => {\r\n        const data = Buffer.from('Hello, World!', 'utf8');\r\n\r\n        await expect(\r\n          service.compressData(data, 'unsupported' as any)\r\n        ).rejects.toThrow('Unsupported compression type');\r\n      });\r\n    });\r\n\r\n    describe('decompressData', () => {\r\n      it('should decompress gzip data', async () => {\r\n        const originalData = Buffer.from('Hello, World! This is a test string for compression.', 'utf8');\r\n        const compressed = await service.compressData(originalData, 'gzip');\r\n\r\n        const decompressed = await service.decompressData(compressed, 'gzip');\r\n\r\n        expect(decompressed).toEqual(originalData);\r\n      });\r\n\r\n      it('should decompress deflate data', async () => {\r\n        const originalData = Buffer.from('Hello, World! This is a test string for compression.', 'utf8');\r\n        const compressed = await service.compressData(originalData, 'deflate');\r\n\r\n        const decompressed = await service.decompressData(compressed, 'deflate');\r\n\r\n        expect(decompressed).toEqual(originalData);\r\n      });\r\n\r\n      it('should handle unsupported decompression type', async () => {\r\n        const data = Buffer.from('Hello, World!', 'utf8');\r\n\r\n        await expect(\r\n          service.decompressData(data, 'unsupported' as any)\r\n        ).rejects.toThrow('Unsupported compression type');\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Batch Operations', () => {\r\n    describe('batchSerialize', () => {\r\n      it('should batch serialize multiple items', async () => {\r\n        const items = [\r\n          { data: { name: 'item1', value: 1 }, format: 'json' as const },\r\n          { data: { name: 'item2', value: 2 }, format: 'json' as const },\r\n          { data: 'binary data', format: 'binary' as const, options: { encoding: 'base64' as const } },\r\n        ];\r\n\r\n        const result = await service.batchSerialize(items);\r\n\r\n        expect(result.results).toHaveLength(3);\r\n        expect(result.errors).toHaveLength(0);\r\n        expect(result.summary).toMatchObject({\r\n          total: 3,\r\n          successful: 3,\r\n          failed: 0,\r\n          totalOriginalSize: expect.any(Number),\r\n          totalCompressedSize: expect.any(Number),\r\n          overallCompressionRatio: expect.any(Number),\r\n          processingTime: expect.any(Number),\r\n        });\r\n\r\n        result.results.forEach(item => {\r\n          expect(item).toMatchObject({\r\n            data: expect.any(Buffer),\r\n            format: expect.any(String),\r\n            originalSize: expect.any(Number),\r\n            compressedSize: expect.any(Number),\r\n            processingTime: expect.any(Number),\r\n          });\r\n        });\r\n      });\r\n\r\n      it('should handle batch serialization errors', async () => {\r\n        const items = [\r\n          { data: { name: 'valid' }, format: 'json' as const },\r\n          { data: {}, format: 'unsupported' as any }, // Invalid format\r\n          { data: { name: 'valid2' }, format: 'json' as const },\r\n        ];\r\n\r\n        const result = await service.batchSerialize(items);\r\n\r\n        expect(result.results).toHaveLength(3);\r\n        expect(result.errors).toHaveLength(1);\r\n        expect(result.summary).toMatchObject({\r\n          total: 3,\r\n          successful: 2,\r\n          failed: 1,\r\n        });\r\n\r\n        expect(result.errors[0]).toMatchObject({\r\n          index: 1,\r\n          error: expect.any(Error),\r\n        });\r\n      });\r\n    });\r\n\r\n    describe('batchDeserialize', () => {\r\n      it('should batch deserialize multiple items', async () => {\r\n        // First serialize some data\r\n        const originalItems = [\r\n          { data: { name: 'item1', value: 1 }, format: 'json' as const },\r\n          { data: { name: 'item2', value: 2 }, format: 'json' as const },\r\n        ];\r\n\r\n        const serialized = await service.batchSerialize(originalItems);\r\n\r\n        // Then deserialize\r\n        const deserializeItems = serialized.results.map((item, index) => ({\r\n          data: item,\r\n          format: originalItems[index].format,\r\n        }));\r\n\r\n        const result = await service.batchDeserialize(deserializeItems);\r\n\r\n        expect(result.results).toHaveLength(2);\r\n        expect(result.errors).toHaveLength(0);\r\n        expect(result.summary).toMatchObject({\r\n          total: 2,\r\n          successful: 2,\r\n          failed: 0,\r\n          processingTime: expect.any(Number),\r\n        });\r\n\r\n        expect(result.results[0]).toEqual({ name: 'item1', value: 1 });\r\n        expect(result.results[1]).toEqual({ name: 'item2', value: 2 });\r\n      });\r\n\r\n      it('should handle batch deserialization errors', async () => {\r\n        const items = [\r\n          { data: Buffer.from('{\"valid\": true}', 'utf8'), format: 'json' as const },\r\n          { data: Buffer.from('invalid json', 'utf8'), format: 'json' as const },\r\n          { data: Buffer.from('{\"valid2\": true}', 'utf8'), format: 'json' as const },\r\n        ];\r\n\r\n        const result = await service.batchDeserialize(items);\r\n\r\n        expect(result.results).toHaveLength(3);\r\n        expect(result.errors).toHaveLength(1);\r\n        expect(result.summary).toMatchObject({\r\n          total: 3,\r\n          successful: 2,\r\n          failed: 1,\r\n        });\r\n\r\n        expect(result.errors[0]).toMatchObject({\r\n          index: 1,\r\n          error: expect.any(Error),\r\n        });\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('Configuration and Statistics', () => {\r\n    describe('getSerializationStats', () => {\r\n      it('should return serialization statistics', () => {\r\n        const stats = service.getSerializationStats();\r\n\r\n        expect(stats).toMatchObject({\r\n          compressionEnabled: true,\r\n          compressionLevel: 6,\r\n          compressionThreshold: 1024,\r\n          supportedFormats: ['json', 'protobuf', 'binary'],\r\n          supportedCompressionTypes: ['gzip', 'deflate'],\r\n          supportedEncodings: ['base64', 'hex', 'utf8'],\r\n        });\r\n      });\r\n    });\r\n\r\n    it('should use configured compression settings', () => {\r\n      // Test that the service correctly reads configuration values\r\n      const stats = service.getSerializationStats();\r\n      \r\n      // Verify default configuration is being used\r\n      expect(stats.compressionEnabled).toBe(true);\r\n      expect(stats.compressionLevel).toBe(6);\r\n      expect(stats.compressionThreshold).toBe(1024);\r\n      expect(stats.supportedFormats).toEqual(['json', 'protobuf', 'binary']);\r\n      expect(stats.supportedCompressionTypes).toEqual(['gzip', 'deflate']);\r\n      expect(stats.supportedEncodings).toEqual(['base64', 'hex', 'utf8']);\r\n    });\r\n  });\r\n\r\n  describe('Edge Cases and Error Handling', () => {\r\n    it('should handle null data', async () => {\r\n      const nullResult = await service.serializeToJson(null);\r\n\r\n      expect(nullResult.data).toBeInstanceOf(Buffer);\r\n\r\n      const deserializedNull = await service.deserializeFromJson(nullResult);\r\n\r\n      expect(deserializedNull).toBeNull();\r\n    });\r\n\r\n    it('should handle undefined data by treating it as null', async () => {\r\n      // JSON.stringify(undefined) returns undefined, so we expect this to be handled gracefully\r\n      // In practice, undefined should be converted to null for JSON serialization\r\n      const data = { value: undefined, name: 'test' };\r\n      const result = await service.serializeToJson(data);\r\n\r\n      expect(result.data).toBeInstanceOf(Buffer);\r\n\r\n      const deserialized = await service.deserializeFromJson(result);\r\n      expect(deserialized).toEqual({ name: 'test' }); // undefined values are omitted in JSON\r\n    });\r\n\r\n    it('should handle empty data', async () => {\r\n      const emptyObject = {};\r\n      const emptyArray = [];\r\n      const emptyString = '';\r\n\r\n      const objectResult = await service.serializeToJson(emptyObject);\r\n      const arrayResult = await service.serializeToJson(emptyArray);\r\n      const stringResult = await service.serializeToBinary(emptyString);\r\n\r\n      expect(objectResult.data).toBeInstanceOf(Buffer);\r\n      expect(arrayResult.data).toBeInstanceOf(Buffer);\r\n      expect(stringResult.data).toBeInstanceOf(Buffer);\r\n\r\n      const deserializedObject = await service.deserializeFromJson(objectResult);\r\n      const deserializedArray = await service.deserializeFromJson(arrayResult);\r\n\r\n      expect(deserializedObject).toEqual({});\r\n      expect(deserializedArray).toEqual([]);\r\n    });\r\n\r\n    it('should handle very large data', async () => {\r\n      // Create a large object\r\n      const largeData = {\r\n        items: Array.from({ length: 10000 }, (_, i) => ({\r\n          id: i,\r\n          data: `Item ${i} with some additional data to make it larger`,\r\n        })),\r\n      };\r\n\r\n      const result = await service.serializeToJson(largeData);\r\n\r\n      expect(result.compressed).toBe(true);\r\n      expect(result.compressionRatio).toBeGreaterThan(1);\r\n\r\n      const deserialized = await service.deserializeFromJson(result);\r\n      expect(deserialized.items).toHaveLength(10000);\r\n      expect(deserialized.items[0]).toEqual(largeData.items[0]);\r\n    });\r\n  });\r\n});"], "version": 3}