c3cee302a6c02309791c8887a335cc0d
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ModelSelectionService_1;
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelSelectionService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const ai_model_repository_interface_1 = require("../../../domain/repositories/ai-model.repository.interface");
const model_performance_service_1 = require("../optimization/model-performance.service");
const ai_cache_service_1 = require("../caching/ai-cache.service");
/**
 * Model Selection Service
 *
 * Intelligently selects the optimal AI model for specific tasks
 * based on performance metrics, cost, latency requirements, and availability.
 * Implements sophisticated model routing and recommendation algorithms.
 */
let ModelSelectionService = ModelSelectionService_1 = class ModelSelectionService {
    constructor(modelRepository, performanceService, cacheService, configService) {
        this.modelRepository = modelRepository;
        this.performanceService = performanceService;
        this.cacheService = cacheService;
        this.configService = configService;
        this.logger = new common_1.Logger(ModelSelectionService_1.name);
        this.modelSelectionCache = new Map();
    }
    /**
     * Selects optimal model for analysis request
     */
    async selectOptimalModel(request) {
        this.logger.debug(`Selecting optimal model for request type: ${request.taskType}`);
        try {
            // Check cache for recent selection
            const cachedSelection = await this.getCachedSelection(request);
            if (cachedSelection && this.isCacheValid(cachedSelection)) {
                return cachedSelection.configuration;
            }
            // Get available models for task type
            const availableModels = await this.getAvailableModels(request.taskType);
            if (availableModels.length === 0) {
                throw new ModelSelectionError(`No models available for task type: ${request.taskType}`);
            }
            // Score and rank models
            const scoredModels = await this.scoreModels(availableModels, request);
            // Select best model based on requirements
            const selectedModel = this.selectBestModel(scoredModels, request);
            // Cache selection result
            await this.cacheSelection(request, selectedModel);
            this.logger.debug(`Selected model: ${selectedModel.modelId} for task: ${request.taskType}`);
            return selectedModel;
        }
        catch (error) {
            this.logger.error(`Model selection failed for task: ${request.taskType}`, error);
            throw new ModelSelectionError(`Model selection failed: ${error.message}`);
        }
    }
    /**
     * Selects model optimized for real-time processing
     */
    async selectRealTimeModel(request) {
        const realTimeRequest = {
            ...request,
            requirements: {
                ...request.requirements,
                maxLatency: 1000, // 1 second max
                priority: 'latency',
                realTime: true,
            },
        };
        return this.selectOptimalModel(realTimeRequest);
    }
    /**
     * Selects model optimized for batch processing
     */
    async selectBatchModel(request) {
        const batchRequest = {
            ...request,
            requirements: {
                ...request.requirements,
                priority: 'throughput',
                batch: true,
                costOptimized: true,
            },
        };
        return this.selectOptimalModel(batchRequest);
    }
    /**
     * Selects model optimized for high accuracy
     */
    async selectHighAccuracyModel(request) {
        const accuracyRequest = {
            ...request,
            requirements: {
                ...request.requirements,
                priority: 'accuracy',
                minAccuracy: 0.95,
                highAccuracy: true,
            },
        };
        return this.selectOptimalModel(accuracyRequest);
    }
    /**
     * Gets model recommendations for a task type
     */
    async getModelRecommendations(taskType) {
        try {
            const availableModels = await this.getAvailableModels(taskType);
            const recommendations = [];
            for (const model of availableModels) {
                const performance = await this.performanceService.getModelMetrics(model.id);
                const recommendation = this.createRecommendation(model, performance);
                recommendations.push(recommendation);
            }
            // Sort by overall score
            recommendations.sort((a, b) => b.overallScore - a.overallScore);
            return recommendations.slice(0, 5); // Top 5 recommendations
        }
        catch (error) {
            this.logger.error(`Failed to get model recommendations for: ${taskType}`, error);
            throw new ModelSelectionError(`Recommendations failed: ${error.message}`);
        }
    }
    /**
     * Evaluates model performance for selection
     */
    async evaluateModelPerformance(modelId) {
        try {
            const model = await this.modelRepository.findById(modelId);
            if (!model) {
                throw new Error(`Model not found: ${modelId}`);
            }
            const metrics = await this.performanceService.getModelMetrics(modelId);
            const costMetrics = await this.performanceService.getCostMetrics(modelId);
            return {
                modelId,
                accuracy: metrics.accuracy,
                precision: metrics.precision,
                recall: metrics.recall,
                f1Score: metrics.f1Score,
                latency: metrics.averageLatency,
                throughput: metrics.throughput,
                costPerRequest: costMetrics.costPerRequest,
                availability: metrics.availability,
                lastUpdated: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`Model evaluation failed for: ${modelId}`, error);
            throw new ModelSelectionError(`Evaluation failed: ${error.message}`);
        }
    }
    /**
     * Updates model selection strategy based on feedback
     */
    async updateSelectionStrategy(feedback) {
        try {
            // Update model performance metrics
            await this.performanceService.updateModelMetrics(feedback.modelId, {
                accuracy: feedback.actualAccuracy,
                latency: feedback.actualLatency,
                userSatisfaction: feedback.userRating,
            });
            // Adjust selection weights based on feedback
            await this.adjustSelectionWeights(feedback);
            // Clear related cache entries
            await this.invalidateSelectionCache(feedback.modelId);
            this.logger.debug(`Updated selection strategy for model: ${feedback.modelId}`);
        }
        catch (error) {
            this.logger.error(`Failed to update selection strategy`, error);
            throw new ModelSelectionError(`Strategy update failed: ${error.message}`);
        }
    }
    // Private helper methods
    async getAvailableModels(taskType) {
        const models = await this.modelRepository.findByTaskType(taskType);
        // Filter by availability and health status
        return models.filter(model => model.status === 'active' &&
            model.healthStatus === 'healthy');
    }
    async scoreModels(models, request) {
        const scoredModels = [];
        for (const model of models) {
            const score = await this.calculateModelScore(model, request);
            scoredModels.push({ model, score });
        }
        return scoredModels.sort((a, b) => b.score.total - a.score.total);
    }
    async calculateModelScore(model, request) {
        const performance = await this.performanceService.getModelMetrics(model.id);
        const requirements = request.requirements || {};
        // Calculate individual scores (0-1 scale)
        const accuracyScore = this.calculateAccuracyScore(performance.accuracy, requirements);
        const latencyScore = this.calculateLatencyScore(performance.averageLatency, requirements);
        const costScore = this.calculateCostScore(performance.costPerRequest, requirements);
        const availabilityScore = this.calculateAvailabilityScore(performance.availability);
        const throughputScore = this.calculateThroughputScore(performance.throughput, requirements);
        // Apply weights based on requirements priority
        const weights = this.getSelectionWeights(requirements);
        const total = (accuracyScore * weights.accuracy +
            latencyScore * weights.latency +
            costScore * weights.cost +
            availabilityScore * weights.availability +
            throughputScore * weights.throughput);
        return {
            accuracy: accuracyScore,
            latency: latencyScore,
            cost: costScore,
            availability: availabilityScore,
            throughput: throughputScore,
            total,
        };
    }
    selectBestModel(scoredModels, request) {
        if (scoredModels.length === 0) {
            throw new Error('No scored models available');
        }
        const bestModel = scoredModels[0];
        return {
            modelId: bestModel.model.id,
            providerType: bestModel.model.providerType,
            modelType: bestModel.model.type,
            version: bestModel.model.version,
            parameters: this.getOptimalParameters(bestModel.model, request),
            score: bestModel.score.total,
            selectedAt: new Date(),
        };
    }
    getOptimalParameters(model, request) {
        const baseParams = model.defaultParameters || {};
        const requirements = request.requirements || {};
        // Adjust parameters based on requirements
        if (requirements.realTime) {
            return {
                ...baseParams,
                temperature: 0.1, // Lower temperature for consistency
                maxTokens: Math.min(baseParams.maxTokens || 1000, 500),
            };
        }
        if (requirements.highAccuracy) {
            return {
                ...baseParams,
                temperature: 0.0, // Deterministic output
                topP: 0.9,
            };
        }
        return baseParams;
    }
    calculateAccuracyScore(accuracy, requirements) {
        const minAccuracy = requirements.minAccuracy || 0.7;
        return Math.max(0, Math.min(1, (accuracy - minAccuracy) / (1 - minAccuracy)));
    }
    calculateLatencyScore(latency, requirements) {
        const maxLatency = requirements.maxLatency || 5000; // 5 seconds default
        return Math.max(0, Math.min(1, (maxLatency - latency) / maxLatency));
    }
    calculateCostScore(cost, requirements) {
        const maxCost = requirements.maxCost || 0.01; // $0.01 per request default
        return Math.max(0, Math.min(1, (maxCost - cost) / maxCost));
    }
    calculateAvailabilityScore(availability) {
        return availability; // Already 0-1 scale
    }
    calculateThroughputScore(throughput, requirements) {
        const minThroughput = requirements.minThroughput || 10; // 10 requests/second default
        return Math.min(1, throughput / minThroughput);
    }
    getSelectionWeights(requirements) {
        const priority = requirements.priority || 'balanced';
        const weightProfiles = {
            accuracy: { accuracy: 0.5, latency: 0.1, cost: 0.1, availability: 0.2, throughput: 0.1 },
            latency: { accuracy: 0.1, latency: 0.5, cost: 0.1, availability: 0.2, throughput: 0.1 },
            cost: { accuracy: 0.2, latency: 0.1, cost: 0.5, availability: 0.1, throughput: 0.1 },
            throughput: { accuracy: 0.1, latency: 0.1, cost: 0.1, availability: 0.2, throughput: 0.5 },
            balanced: { accuracy: 0.25, latency: 0.25, cost: 0.2, availability: 0.2, throughput: 0.1 },
        };
        return weightProfiles[priority] || weightProfiles.balanced;
    }
    async getCachedSelection(request) {
        const cacheKey = this.generateSelectionCacheKey(request);
        return this.modelSelectionCache.get(cacheKey) || null;
    }
    isCacheValid(selection) {
        const cacheTimeout = this.configService.get('ai.modelSelectionCacheTtl', 300000); // 5 minutes
        return Date.now() - selection.timestamp.getTime() < cacheTimeout;
    }
    async cacheSelection(request, configuration) {
        const cacheKey = this.generateSelectionCacheKey(request);
        const result = {
            configuration,
            timestamp: new Date(),
        };
        this.modelSelectionCache.set(cacheKey, result);
        // Clean up old cache entries periodically
        if (this.modelSelectionCache.size > 1000) {
            this.cleanupCache();
        }
    }
    generateSelectionCacheKey(request) {
        const keyData = {
            taskType: request.taskType,
            requirements: request.requirements,
        };
        return require('crypto')
            .createHash('md5')
            .update(JSON.stringify(keyData))
            .digest('hex');
    }
    cleanupCache() {
        const entries = Array.from(this.modelSelectionCache.entries());
        const cutoff = Date.now() - 600000; // 10 minutes
        for (const [key, value] of entries) {
            if (value.timestamp.getTime() < cutoff) {
                this.modelSelectionCache.delete(key);
            }
        }
    }
    createRecommendation(model, performance) {
        return {
            modelId: model.id,
            modelName: model.name,
            providerType: model.providerType,
            accuracy: performance.accuracy,
            latency: performance.averageLatency,
            costPerRequest: performance.costPerRequest,
            overallScore: this.calculateOverallScore(performance),
            strengths: this.identifyModelStrengths(model, performance),
            weaknesses: this.identifyModelWeaknesses(model, performance),
            useCases: model.recommendedUseCases || [],
        };
    }
    calculateOverallScore(performance) {
        // Simple weighted average for overall score
        return (performance.accuracy * 0.3 +
            (1 - performance.averageLatency / 10000) * 0.3 + // Normalize latency
            (1 - performance.costPerRequest * 1000) * 0.2 + // Normalize cost
            performance.availability * 0.2);
    }
    identifyModelStrengths(model, performance) {
        const strengths = [];
        if (performance.accuracy > 0.9)
            strengths.push('High accuracy');
        if (performance.averageLatency < 1000)
            strengths.push('Low latency');
        if (performance.costPerRequest < 0.001)
            strengths.push('Cost effective');
        if (performance.availability > 0.99)
            strengths.push('High availability');
        return strengths;
    }
    identifyModelWeaknesses(model, performance) {
        const weaknesses = [];
        if (performance.accuracy < 0.8)
            weaknesses.push('Lower accuracy');
        if (performance.averageLatency > 5000)
            weaknesses.push('Higher latency');
        if (performance.costPerRequest > 0.01)
            weaknesses.push('Higher cost');
        if (performance.availability < 0.95)
            weaknesses.push('Availability concerns');
        return weaknesses;
    }
    async adjustSelectionWeights(feedback) {
        // Implementation for adjusting selection algorithm weights based on feedback
        // This would involve machine learning or statistical analysis
    }
    async invalidateSelectionCache(modelId) {
        // Remove cache entries related to the specific model
        for (const [key, value] of this.modelSelectionCache.entries()) {
            if (value.configuration.modelId === modelId) {
                this.modelSelectionCache.delete(key);
            }
        }
    }
};
exports.ModelSelectionService = ModelSelectionService;
exports.ModelSelectionService = ModelSelectionService = ModelSelectionService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(ai_model_repository_interface_1.AI_MODEL_REPOSITORY)),
    __metadata("design:paramtypes", [typeof (_a = typeof ai_model_repository_interface_1.AiModelRepository !== "undefined" && ai_model_repository_interface_1.AiModelRepository) === "function" ? _a : Object, typeof (_b = typeof model_performance_service_1.ModelPerformanceService !== "undefined" && model_performance_service_1.ModelPerformanceService) === "function" ? _b : Object, typeof (_c = typeof ai_cache_service_1.AiCacheService !== "undefined" && ai_cache_service_1.AiCacheService) === "function" ? _c : Object, typeof (_d = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _d : Object])
], ModelSelectionService);
class ModelSelectionError extends Error {
    constructor(message) {
        super(message);
        this.name = 'ModelSelectionError';
    }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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