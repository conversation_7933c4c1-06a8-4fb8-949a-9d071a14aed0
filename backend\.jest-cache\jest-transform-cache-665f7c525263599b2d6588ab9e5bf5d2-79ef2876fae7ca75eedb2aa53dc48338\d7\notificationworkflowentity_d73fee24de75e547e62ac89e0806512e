179e1a9f7a0efd8dc3cafb76305803f9
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationWorkflow = void 0;
const typeorm_1 = require("typeorm");
const workflow_execution_entity_1 = require("./workflow-execution.entity");
const workflow_schedule_entity_1 = require("./workflow-schedule.entity");
/**
 * Notification Workflow Entity
 *
 * Represents a notification workflow with comprehensive definition,
 * configuration, and execution tracking capabilities.
 */
let NotificationWorkflow = class NotificationWorkflow {
    // Computed properties
    get stepCount() {
        return this.definition?.steps?.length || 0;
    }
    get hasScheduledTriggers() {
        return this.definition?.triggers?.some(trigger => trigger.type === 'cron') || false;
    }
    get hasEventTriggers() {
        return this.definition?.triggers?.some(trigger => trigger.type === 'event') || false;
    }
    get hasWebhookTriggers() {
        return this.definition?.triggers?.some(trigger => trigger.type === 'webhook') || false;
    }
    get isActive() {
        return this.status === 'active';
    }
    get isDraft() {
        return this.status === 'draft';
    }
    get complexity() {
        if (this.metadata?.complexity) {
            return this.metadata.complexity;
        }
        // Auto-calculate complexity based on step count and structure
        const stepCount = this.stepCount;
        const hasConditionalLogic = this.definition?.steps?.some(step => step.nextSteps && step.nextSteps.length > 1) || false;
        const hasErrorHandling = this.definition?.errorHandling ||
            this.definition?.steps?.some(step => step.errorHandling) || false;
        if (stepCount <= 3 && !hasConditionalLogic && !hasErrorHandling) {
            return 'low';
        }
        else if (stepCount <= 10 && (!hasConditionalLogic || !hasErrorHandling)) {
            return 'medium';
        }
        else {
            return 'high';
        }
    }
    /**
     * Get workflow summary
     */
    getWorkflowSummary() {
        return {
            id: this.id,
            name: this.name,
            description: this.description,
            status: this.status,
            category: this.category,
            version: this.version,
            isTemplate: this.isTemplate,
            stepCount: this.stepCount,
            complexity: this.complexity,
            hasScheduledTriggers: this.hasScheduledTriggers,
            hasEventTriggers: this.hasEventTriggers,
            hasWebhookTriggers: this.hasWebhookTriggers,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }
    /**
     * Get trigger summary
     */
    getTriggerSummary() {
        return this.definition?.triggers?.map(trigger => ({
            type: trigger.type,
            cron: trigger.cron,
            eventType: trigger.eventType,
            webhookPath: trigger.webhookPath,
            hasConditions: trigger.conditions && trigger.conditions.length > 0,
        })) || [];
    }
    /**
     * Get step types used in workflow
     */
    getStepTypes() {
        const stepTypes = this.definition?.steps?.map(step => step.type) || [];
        return [...new Set(stepTypes)];
    }
    /**
     * Check if workflow uses specific step type
     */
    usesStepType(stepType) {
        return this.definition?.steps?.some(step => step.type === stepType) || false;
    }
    /**
     * Get notification channels used in workflow
     */
    getNotificationChannels() {
        const channels = [];
        this.definition?.steps?.forEach(step => {
            if (step.type === 'notification' && step.config?.channel) {
                channels.push(step.config.channel);
            }
        });
        return [...new Set(channels)];
    }
    /**
     * Check if workflow is ready for execution
     */
    isReadyForExecution() {
        return this.status === 'active' &&
            this.definition?.startStep &&
            this.definition?.steps?.length > 0;
    }
    /**
     * Get estimated execution duration
     */
    getEstimatedDuration() {
        if (this.metadata?.estimatedDuration) {
            return this.metadata.estimatedDuration;
        }
        // Simple estimation based on step count and types
        let duration = 0;
        this.definition?.steps?.forEach(step => {
            switch (step.type) {
                case 'notification':
                    duration += 30000; // 30 seconds
                    break;
                case 'delay':
                    duration += step.config?.delay || 60000; // Default 1 minute
                    break;
                case 'condition':
                    duration += 5000; // 5 seconds
                    break;
                case 'http_request':
                    duration += 15000; // 15 seconds
                    break;
                default:
                    duration += 10000; // 10 seconds default
            }
        });
        return duration;
    }
    /**
     * Validate workflow definition
     */
    validateDefinition() {
        const errors = [];
        if (!this.definition) {
            errors.push('Workflow definition is required');
            return { valid: false, errors };
        }
        if (!this.definition.startStep) {
            errors.push('Start step is required');
        }
        if (!this.definition.steps || this.definition.steps.length === 0) {
            errors.push('At least one step is required');
        }
        // Validate start step exists
        if (this.definition.startStep && this.definition.steps) {
            const startStepExists = this.definition.steps.some(step => step.id === this.definition.startStep);
            if (!startStepExists) {
                errors.push(`Start step '${this.definition.startStep}' not found in steps`);
            }
        }
        // Validate step references
        if (this.definition.steps) {
            const stepIds = this.definition.steps.map(step => step.id);
            this.definition.steps.forEach(step => {
                if (step.nextStep && !stepIds.includes(step.nextStep)) {
                    errors.push(`Step '${step.id}' references non-existent next step '${step.nextStep}'`);
                }
                if (step.nextSteps) {
                    step.nextSteps.forEach(nextStep => {
                        if (!stepIds.includes(nextStep.stepId)) {
                            errors.push(`Step '${step.id}' references non-existent next step '${nextStep.stepId}'`);
                        }
                    });
                }
            });
        }
        return { valid: errors.length === 0, errors };
    }
};
exports.NotificationWorkflow = NotificationWorkflow;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], NotificationWorkflow.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'name' }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], NotificationWorkflow.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'description', type: 'text', nullable: true }),
    __metadata("design:type", String)
], NotificationWorkflow.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'definition', type: 'jsonb' }),
    __metadata("design:type", Object)
], NotificationWorkflow.prototype, "definition", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'status', default: 'draft' }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], NotificationWorkflow.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'category', nullable: true }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], NotificationWorkflow.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'tags', type: 'text', array: true, default: '{}' }),
    __metadata("design:type", Array)
], NotificationWorkflow.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'version', default: '1.0.0' }),
    __metadata("design:type", String)
], NotificationWorkflow.prototype, "version", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_template', default: false }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", Boolean)
], NotificationWorkflow.prototype, "isTemplate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'configuration', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], NotificationWorkflow.prototype, "configuration", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'metadata', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], NotificationWorkflow.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by' }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], NotificationWorkflow.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by' }),
    __metadata("design:type", String)
], NotificationWorkflow.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], NotificationWorkflow.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], NotificationWorkflow.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => workflow_execution_entity_1.WorkflowExecution, execution => execution.workflow),
    __metadata("design:type", Array)
], NotificationWorkflow.prototype, "executions", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => workflow_schedule_entity_1.WorkflowSchedule, schedule => schedule.workflow),
    __metadata("design:type", Array)
], NotificationWorkflow.prototype, "schedules", void 0);
exports.NotificationWorkflow = NotificationWorkflow = __decorate([
    (0, typeorm_1.Entity)('notification_workflows'),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['category']),
    (0, typeorm_1.Index)(['createdBy']),
    (0, typeorm_1.Index)(['isTemplate']),
    (0, typeorm_1.Index)(['createdAt']),
    (0, typeorm_1.Index)(['updatedAt'])
], NotificationWorkflow);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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