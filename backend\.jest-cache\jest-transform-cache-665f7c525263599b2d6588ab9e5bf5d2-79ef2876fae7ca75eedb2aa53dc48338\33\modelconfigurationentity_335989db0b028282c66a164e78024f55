8998125a0580f08d891e76db99ba5e47
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelConfiguration = void 0;
const typeorm_1 = require("typeorm");
const analysis_job_entity_1 = require("./analysis-job.entity");
/**
 * Model Configuration entity
 * Represents AI/ML model configurations and settings
 */
let ModelConfiguration = class ModelConfiguration {
    /**
     * Check if model is available for use
     */
    get isAvailable() {
        return this.status === 'active';
    }
    /**
     * Check if model supports batch processing
     */
    get supportsBatch() {
        return this.capabilities?.supportsBatch || false;
    }
    /**
     * Check if model supports streaming
     */
    get supportsStreaming() {
        return this.capabilities?.supportsStreaming || false;
    }
    /**
     * Get current cost per token
     */
    get costPerToken() {
        return this.costConfig?.inputTokenCost || 0;
    }
    /**
     * Get rate limit for requests per minute
     */
    get requestsPerMinute() {
        return this.rateLimits?.requestsPerMinute || 60;
    }
    /**
     * Calculate estimated cost for a request
     */
    calculateEstimatedCost(inputTokens, outputTokens = 0) {
        if (!this.costConfig)
            return 0;
        const inputCost = (this.costConfig.inputTokenCost || 0) * inputTokens;
        const outputCost = (this.costConfig.outputTokenCost || 0) * outputTokens;
        const requestCost = this.costConfig.requestCost || 0;
        return inputCost + outputCost + requestCost;
    }
    /**
     * Update usage statistics
     */
    updateUsageStats(tokens, responseTime, cost, success) {
        if (!this.usageStats) {
            this.usageStats = {
                totalRequests: 0,
                totalTokens: 0,
                totalCost: 0,
                averageResponseTime: 0,
                successRate: 0,
            };
        }
        this.usageStats.totalRequests = (this.usageStats.totalRequests || 0) + 1;
        this.usageStats.totalTokens = (this.usageStats.totalTokens || 0) + tokens;
        this.usageStats.totalCost = (this.usageStats.totalCost || 0) + cost;
        this.usageStats.lastUsed = new Date().toISOString();
        // Update average response time
        const totalRequests = this.usageStats.totalRequests;
        const currentAvg = this.usageStats.averageResponseTime || 0;
        this.usageStats.averageResponseTime = (currentAvg * (totalRequests - 1) + responseTime) / totalRequests;
        // Update success rate
        const successfulRequests = Math.round((this.usageStats.successRate || 0) * (totalRequests - 1) / 100);
        const newSuccessfulRequests = successfulRequests + (success ? 1 : 0);
        this.usageStats.successRate = (newSuccessfulRequests / totalRequests) * 100;
    }
    /**
     * Update performance metrics
     */
    updatePerformanceMetrics(metrics) {
        if (!this.performanceMetrics) {
            this.performanceMetrics = {};
        }
        Object.assign(this.performanceMetrics, metrics);
    }
    /**
     * Check if rate limit is exceeded
     */
    isRateLimitExceeded(currentUsage) {
        if (!this.rateLimits)
            return false;
        if (this.rateLimits.requestsPerMinute && currentUsage.requestsPerMinute) {
            if (currentUsage.requestsPerMinute >= this.rateLimits.requestsPerMinute) {
                return true;
            }
        }
        if (this.rateLimits.tokensPerMinute && currentUsage.tokensPerMinute) {
            if (currentUsage.tokensPerMinute >= this.rateLimits.tokensPerMinute) {
                return true;
            }
        }
        return false;
    }
    /**
     * Validate model configuration
     */
    validate() {
        const errors = [];
        if (!this.name) {
            errors.push('Model name is required');
        }
        if (!this.type) {
            errors.push('Model type is required');
        }
        if (!this.provider) {
            errors.push('Provider is required');
        }
        if (!this.version) {
            errors.push('Version is required');
        }
        if (this.provider !== 'custom' && !this.apiKey) {
            errors.push('API key is required for external providers');
        }
        return errors;
    }
};
exports.ModelConfiguration = ModelConfiguration;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ModelConfiguration.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true, length: 255 }),
    __metadata("design:type", String)
], ModelConfiguration.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'display_name', length: 255 }),
    __metadata("design:type", String)
], ModelConfiguration.prototype, "displayName", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ModelConfiguration.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: [
            'vulnerability_scanner',
            'threat_classifier',
            'risk_assessor',
            'anomaly_detector',
            'nlp_processor',
            'image_analyzer',
            'behavioral_analyzer',
            'predictive_model',
        ],
    }),
    __metadata("design:type", String)
], ModelConfiguration.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['openai', 'azure_openai', 'anthropic', 'google_ai', 'aws_bedrock', 'huggingface', 'custom'],
        default: 'openai',
    }),
    __metadata("design:type", String)
], ModelConfiguration.prototype, "provider", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100 }),
    __metadata("design:type", String)
], ModelConfiguration.prototype, "version", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['active', 'inactive', 'deprecated', 'training', 'testing'],
        default: 'active',
    }),
    __metadata("design:type", String)
], ModelConfiguration.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'endpoint_url', nullable: true }),
    __metadata("design:type", String)
], ModelConfiguration.prototype, "endpointUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'api_key', nullable: true }),
    __metadata("design:type", String)
], ModelConfiguration.prototype, "apiKey", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ModelConfiguration.prototype, "parameters", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'rate_limits', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ModelConfiguration.prototype, "rateLimits", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cost_config', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ModelConfiguration.prototype, "costConfig", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'performance_metrics', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ModelConfiguration.prototype, "performanceMetrics", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ModelConfiguration.prototype, "capabilities", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'circuit_breaker', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ModelConfiguration.prototype, "circuitBreaker", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'retry_config', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ModelConfiguration.prototype, "retryConfig", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'training_info', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ModelConfiguration.prototype, "trainingInfo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'usage_stats', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ModelConfiguration.prototype, "usageStats", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], ModelConfiguration.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['development', 'staging', 'production'],
        default: 'development',
    }),
    __metadata("design:type", String)
], ModelConfiguration.prototype, "environment", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_default', default: false }),
    __metadata("design:type", Boolean)
], ModelConfiguration.prototype, "isDefault", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_a = typeof Record !== "undefined" && Record) === "function" ? _a : Object)
], ModelConfiguration.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', type: 'uuid' }),
    __metadata("design:type", String)
], ModelConfiguration.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], ModelConfiguration.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], ModelConfiguration.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], ModelConfiguration.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => analysis_job_entity_1.AnalysisJob, job => job.modelConfiguration),
    __metadata("design:type", Array)
], ModelConfiguration.prototype, "analysisJobs", void 0);
exports.ModelConfiguration = ModelConfiguration = __decorate([
    (0, typeorm_1.Entity)('model_configurations'),
    (0, typeorm_1.Index)(['name'], { unique: true }),
    (0, typeorm_1.Index)(['type']),
    (0, typeorm_1.Index)(['provider']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['version'])
], ModelConfiguration);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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