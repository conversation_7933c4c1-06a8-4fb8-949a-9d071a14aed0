{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\orchestration\\load-balancer.service.ts", "mappings": ";;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAC/C,+CAAwD;AAExD;;;;;;GAMG;AAEI,IAAM,mBAAmB,2BAAzB,MAAM,mBAAmB;IAM9B,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QALxC,WAAM,GAAG,IAAI,eAAM,CAAC,qBAAmB,CAAC,IAAI,CAAC,CAAC;QAC9C,cAAS,GAAG,IAAI,GAAG,EAAwB,CAAC;QAC5C,sBAAiB,GAAG,IAAI,GAAG,EAA2B,CAAC;QACvD,iBAAY,GAAG,IAAI,GAAG,EAA6B,CAAC;QAGnE,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CAAC,YAAoB;QAC9C,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;aACtD,MAAM,CAAC,QAAQ,CAAC,EAAE,CACjB,QAAQ,CAAC,IAAI,KAAK,YAAY;YAC9B,QAAQ,CAAC,MAAM,KAAK,SAAS;YAC7B,QAAQ,CAAC,OAAO,CACjB,CAAC;QAEJ,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4CAA4C,YAAY,EAAE,CAAC,CAAC;YAC7E,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,kCAAkC;QAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;QACvD,OAAO,IAAI,CAAC,uBAAuB,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAClB,YAAoB,EACpB,OAA6B;QAE7B,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;QAE1E,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;QACvD,MAAM,gBAAgB,GAAG,IAAI,CAAC,wBAAwB,CACpD,kBAAkB,EAClB,QAAQ,EACR,OAAO,CACR,CAAC;QAEF,+BAA+B;QAC/B,MAAM,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAEtD,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,sBAAsB,gBAAgB,CAAC,EAAE,cAAc,YAAY,EAAE,CACtE,CAAC;QAEF,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAC3B,UAAkB,EAClB,QAAgB,EAChB,OAAgB;QAEhB,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6CAA6C,UAAU,EAAE,CAAC,CAAC;YAC5E,OAAO;QACT,CAAC;QAED,0BAA0B;QAC1B,QAAQ,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;QACjC,QAAQ,CAAC,OAAO,CAAC,mBAAmB,GAAG,IAAI,CAAC,sBAAsB,CAChE,QAAQ,CAAC,OAAO,CAAC,mBAAmB,EACpC,QAAQ,EACR,QAAQ,CAAC,OAAO,CAAC,aAAa,CAC/B,CAAC;QAEF,IAAI,OAAO,EAAE,CAAC;YACZ,QAAQ,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QACxC,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QACpC,CAAC;QAED,QAAQ,CAAC,OAAO,CAAC,WAAW;YAC1B,QAAQ,CAAC,OAAO,CAAC,kBAAkB,GAAG,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC;QAEvE,wBAAwB;QACxB,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;QAE9C,mDAAmD;QACnD,MAAM,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QAE5C,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,iCAAiC,UAAU,cAAc,OAAO,eAAe,QAAQ,IAAI,CAC5F,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB;QACvB,MAAM,aAAa,GAAsC,EAAE,CAAC;QAE5D,KAAK,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACpD,IAAI,CAAC;gBACH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;gBAC7D,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;gBAChD,aAAa,CAAC,UAAU,CAAC,GAAG,YAAY,CAAC;gBAEzC,+CAA+C;gBAC/C,QAAQ,CAAC,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC;YAEnE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,UAAU,EAAE,EAAE,KAAK,CAAC,CAAC;gBAC5E,MAAM,YAAY,GAAsB;oBACtC,OAAO,EAAE,KAAK;oBACd,YAAY,EAAE,CAAC,CAAC;oBAChB,KAAK,EAAE,KAAK,CAAC,OAAO;oBACpB,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC;gBAEF,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;gBAChD,aAAa,CAAC,UAAU,CAAC,GAAG,YAAY,CAAC;gBACzC,QAAQ,CAAC,MAAM,GAAG,WAAW,CAAC;YAChC,CAAC;QACH,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,cAA8B;QAC9C,MAAM,QAAQ,GAAiB;YAC7B,EAAE,EAAE,cAAc,CAAC,EAAE;YACrB,IAAI,EAAE,cAAc,CAAC,IAAI;YACzB,IAAI,EAAE,cAAc,CAAC,IAAI;YACzB,QAAQ,EAAE,cAAc,CAAC,QAAQ;YACjC,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,cAAc,CAAC,QAAQ,IAAI,CAAC;YACtC,MAAM,EAAE,cAAc,CAAC,MAAM,IAAI,CAAC;YAClC,qBAAqB,EAAE,cAAc,CAAC,qBAAqB,IAAI,GAAG;YAClE,WAAW,EAAE,CAAC;YACd,OAAO,EAAE;gBACP,aAAa,EAAE,CAAC;gBAChB,kBAAkB,EAAE,CAAC;gBACrB,cAAc,EAAE,CAAC;gBACjB,mBAAmB,EAAE,CAAC;gBACtB,WAAW,EAAE,GAAG;aACjB;YACD,MAAM,EAAE,cAAc,CAAC,MAAM,IAAI,EAAE;SACpC,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QAE1C,+BAA+B;QAC/B,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAExC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mBAAmB,QAAQ,CAAC,EAAE,KAAK,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,UAAkB;QACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,uBAAuB,UAAU,EAAE,CAAC,CAAC;QACvD,CAAC;QAED,8CAA8C;QAC9C,QAAQ,CAAC,OAAO,GAAG,KAAK,CAAC;QAEzB,wCAAwC;QACxC,MAAM,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QAE5C,kBAAkB;QAClB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAClC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAErC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qBAAqB,UAAU,EAAE,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACxB,UAAkB,EAClB,OAAgC;QAEhC,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,uBAAuB,UAAU,EAAE,CAAC,CAAC;QACvD,CAAC;QAED,6BAA6B;QAC7B,IAAI,OAAO,CAAC,IAAI;YAAE,QAAQ,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAC/C,IAAI,OAAO,CAAC,QAAQ;YAAE,QAAQ,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAC3D,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS;YAAE,QAAQ,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACzE,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS;YAAE,QAAQ,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QACnE,IAAI,OAAO,CAAC,qBAAqB,KAAK,SAAS,EAAE,CAAC;YAChD,QAAQ,CAAC,qBAAqB,GAAG,OAAO,CAAC,qBAAqB,CAAC;QACjE,CAAC;QACD,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,QAAQ,CAAC,MAAM,GAAG,EAAE,GAAG,QAAQ,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;QAC9D,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,UAAU,EAAE,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,MAAM,KAAK,GAAuB;YAChC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;YACnC,gBAAgB,EAAE,CAAC;YACnB,kBAAkB,EAAE,CAAC;YACrB,SAAS,EAAE,CAAC;YACZ,mBAAmB,EAAE,CAAC;YACtB,kBAAkB,EAAE,CAAC;YACrB,aAAa,EAAE,EAAE;SAClB,CAAC;QAEF,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,eAAe,GAAG,CAAC,CAAC;QACxB,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAC1B,IAAI,oBAAoB,GAAG,CAAC,CAAC;QAE7B,KAAK,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACpD,IAAI,QAAQ,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBAClC,KAAK,CAAC,gBAAgB,EAAE,CAAC;YAC3B,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,kBAAkB,EAAE,CAAC;YAC7B,CAAC;YAED,KAAK,CAAC,SAAS,IAAI,QAAQ,CAAC,WAAW,CAAC;YAExC,IAAI,QAAQ,CAAC,OAAO,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;gBACvC,aAAa,IAAI,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC;gBAChD,eAAe,IAAI,QAAQ,CAAC,OAAO,CAAC,kBAAkB,CAAC;gBACvD,iBAAiB,IAAI,QAAQ,CAAC,OAAO,CAAC,mBAAmB,CAAC;gBAC1D,oBAAoB,EAAE,CAAC;YACzB,CAAC;YAED,KAAK,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG;gBAChC,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,WAAW,EAAE,QAAQ,CAAC,WAAW;gBACjC,OAAO,EAAE,QAAQ,CAAC,OAAO;aAC1B,CAAC;QACJ,CAAC;QAED,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;YACtB,KAAK,CAAC,kBAAkB,GAAG,eAAe,GAAG,aAAa,CAAC;QAC7D,CAAC;QAED,IAAI,oBAAoB,GAAG,CAAC,EAAE,CAAC;YAC7B,KAAK,CAAC,mBAAmB,GAAG,iBAAiB,GAAG,oBAAoB,CAAC;QACvE,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IAEG,AAAN,KAAK,CAAC,2BAA2B;QAC/B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IAEG,AAAN,KAAK,CAAC,qBAAqB;QACzB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAE/C,+CAA+C;QAC/C,KAAK,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACpD,IAAI,QAAQ,CAAC,OAAO,CAAC,aAAa,GAAG,EAAE,EAAE,CAAC;gBACxC,MAAM,gBAAgB,GAAG,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC;gBAClE,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;YACpD,CAAC;QACH,CAAC;IACH,CAAC;IAED,yBAAyB;IAEjB,mBAAmB;QACzB,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAmB,cAAc,EAAE,EAAE,CAAC,CAAC;QAErF,KAAK,MAAM,MAAM,IAAI,eAAe,EAAE,CAAC;YACrC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBACrC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,MAAM,CAAC,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;YAC1E,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,2BAA2B;QACjC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,aAAa,EAAE;YACxC,IAAI,EAAE,aAAa;YACnB,QAAQ,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC;SAC7C,CAAC,CAAC;QAEH,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,sBAAsB,EAAE;YACjD,IAAI,EAAE,sBAAsB;YAC5B,QAAQ,EAAE,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,IAAI,CAAC;SACrD,CAAC,CAAC;QAEH,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,mBAAmB,EAAE;YAC9C,IAAI,EAAE,mBAAmB;YACzB,QAAQ,EAAE,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC;SACnD,CAAC,CAAC;QAEH,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,eAAe,EAAE;YAC1C,IAAI,EAAE,uBAAuB;YAC7B,QAAQ,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC;SAC/C,CAAC,CAAC;QAEH,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE;YACnC,IAAI,EAAE,QAAQ;YACd,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;SACzC,CAAC,CAAC;IACL,CAAC;IAEO,kBAAkB,CAAC,YAAoB;QAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CACzC,oBAAoB,YAAY,WAAW,EAC3C,sBAAsB,CACvB,CAAC;QAEF,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,YAAY,CAAC;YACxC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,sBAAsB,CAAE,CAAC;IAC7D,CAAC;IAEO,uBAAuB,CAC7B,SAAyB,EACzB,QAAyB;QAEzB,OAAO,QAAQ,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IAC5C,CAAC;IAEO,wBAAwB,CAC9B,SAAyB,EACzB,QAAyB,EACzB,OAA6B;QAE7B,MAAM,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACrD,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;IAEO,kBAAkB,CACxB,SAAyB,EACzB,OAAoC;QAEpC,6CAA6C;QAC7C,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;IACrF,CAAC;IAEO,0BAA0B,CAChC,SAAyB,EACzB,OAAoC;QAEpC,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC7B,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;YAC9C,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;YAC9C,OAAO,MAAM,GAAG,MAAM,CAAC;QACzB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,wBAAwB,CAC9B,SAAyB,EACzB,OAAoC;QAEpC,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC;IACjE,CAAC;IAEO,oBAAoB,CAC1B,SAAyB,EACzB,OAAoC;QAEpC,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAC7B,CAAC,CAAC,OAAO,CAAC,mBAAmB,GAAG,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAC9D,CAAC;IACJ,CAAC;IAEO,cAAc,CACpB,SAAyB,EACzB,OAAoC;QAEpC,MAAM,QAAQ,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC;QAChC,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC9C,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,UAAkB,EAAE,KAAa;QAChE,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,QAAQ,EAAE,CAAC;YACb,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,UAAkB;QACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,+CAA+C;QAC/C,IAAI,QAAQ,CAAC,OAAO,CAAC,aAAa,GAAG,EAAE,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;YAC9E,QAAQ,CAAC,MAAM,GAAG,WAAW,CAAC;QAChC,CAAC;QAED,iDAAiD;QACjD,IAAI,QAAQ,CAAC,OAAO,CAAC,mBAAmB,GAAG,KAAK,EAAE,CAAC,CAAC,aAAa;YAC/D,QAAQ,CAAC,MAAM,GAAG,WAAW,CAAC;QAChC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,QAAsB;QACrD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,qDAAqD;YACrD,sCAAsC;YACtC,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE5C,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,YAAY;gBACZ,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACpC,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,sBAAsB,CAC5B,cAAsB,EACtB,QAAgB,EAChB,KAAa;QAEb,OAAO,CAAC,CAAC,cAAc,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC;IAC7D,CAAC;IAEO,yBAAyB,CAAC,QAAsB;QACtD,MAAM,aAAa,GAAG,GAAG,CAAC;QAC1B,MAAM,kBAAkB,GAAG,GAAG,CAAC;QAC/B,MAAM,kBAAkB,GAAG,GAAG,CAAC;QAE/B,MAAM,YAAY,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC;QAClD,MAAM,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,mBAAmB,GAAG,KAAK,CAAC,CAAC,CAAC;QAC1F,MAAM,iBAAiB,GAAG,QAAQ,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEhE,OAAO,CACL,YAAY,GAAG,aAAa;YAC5B,iBAAiB,GAAG,kBAAkB;YACtC,iBAAiB,GAAG,kBAAkB,CACvC,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,UAAkB;QACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,MAAM,WAAW,GAAG,KAAK,CAAC,CAAC,aAAa;QACxC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,OAAO,QAAQ,CAAC,WAAW,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,GAAG,WAAW,EAAE,CAAC;YACxE,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;CACF,CAAA;AA1eY,kDAAmB;AAoRxB;IADL,IAAA,eAAI,EAAC,yBAAc,CAAC,gBAAgB,CAAC;;;wDACD,OAAO,oBAAP,OAAO;sEAG3C;AAMK;IADL,IAAA,eAAI,EAAC,yBAAc,CAAC,YAAY,CAAC;;;wDACH,OAAO,oBAAP,OAAO;gEAUrC;8BAvSU,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;yDAOiC,sBAAa,oBAAb,sBAAa;GAN9C,mBAAmB,CA0e/B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\orchestration\\load-balancer.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { Cron, CronExpression } from '@nestjs/schedule';\r\n\r\n/**\r\n * Load Balancer Service\r\n * \r\n * Manages load balancing across multiple AI providers and models,\r\n * implementing intelligent routing strategies, health monitoring,\r\n * and automatic failover capabilities for optimal performance.\r\n */\r\n@Injectable()\r\nexport class LoadBalancerService {\r\n  private readonly logger = new Logger(LoadBalancerService.name);\r\n  private readonly providers = new Map<string, ProviderInfo>();\r\n  private readonly routingStrategies = new Map<string, RoutingStrategy>();\r\n  private readonly healthChecks = new Map<string, HealthCheckResult>();\r\n\r\n  constructor(private readonly configService: ConfigService) {\r\n    this.initializeProviders();\r\n    this.initializeRoutingStrategies();\r\n  }\r\n\r\n  /**\r\n   * Gets available providers for a specific provider type with load balancing\r\n   */\r\n  async getAvailableProviders(providerType: string): Promise<ProviderInfo[]> {\r\n    const typeProviders = Array.from(this.providers.values())\r\n      .filter(provider => \r\n        provider.type === providerType && \r\n        provider.status === 'healthy' &&\r\n        provider.enabled\r\n      );\r\n\r\n    if (typeProviders.length === 0) {\r\n      this.logger.warn(`No healthy providers available for type: ${providerType}`);\r\n      return [];\r\n    }\r\n\r\n    // Sort by load balancing strategy\r\n    const strategy = this.getRoutingStrategy(providerType);\r\n    return this.sortProvidersByStrategy(typeProviders, strategy);\r\n  }\r\n\r\n  /**\r\n   * Selects the best provider for a request using load balancing\r\n   */\r\n  async selectProvider(\r\n    providerType: string,\r\n    request: LoadBalancingRequest\r\n  ): Promise<ProviderInfo | null> {\r\n    const availableProviders = await this.getAvailableProviders(providerType);\r\n    \r\n    if (availableProviders.length === 0) {\r\n      return null;\r\n    }\r\n\r\n    const strategy = this.getRoutingStrategy(providerType);\r\n    const selectedProvider = this.selectProviderByStrategy(\r\n      availableProviders,\r\n      strategy,\r\n      request\r\n    );\r\n\r\n    // Update provider load metrics\r\n    await this.updateProviderLoad(selectedProvider.id, 1);\r\n\r\n    this.logger.debug(\r\n      `Selected provider: ${selectedProvider.id} for type: ${providerType}`\r\n    );\r\n\r\n    return selectedProvider;\r\n  }\r\n\r\n  /**\r\n   * Reports request completion to update load metrics\r\n   */\r\n  async reportRequestCompletion(\r\n    providerId: string,\r\n    duration: number,\r\n    success: boolean\r\n  ): Promise<void> {\r\n    const provider = this.providers.get(providerId);\r\n    if (!provider) {\r\n      this.logger.warn(`Provider not found for completion report: ${providerId}`);\r\n      return;\r\n    }\r\n\r\n    // Update provider metrics\r\n    provider.metrics.totalRequests++;\r\n    provider.metrics.averageResponseTime = this.calculateMovingAverage(\r\n      provider.metrics.averageResponseTime,\r\n      duration,\r\n      provider.metrics.totalRequests\r\n    );\r\n\r\n    if (success) {\r\n      provider.metrics.successfulRequests++;\r\n    } else {\r\n      provider.metrics.failedRequests++;\r\n    }\r\n\r\n    provider.metrics.successRate = \r\n      provider.metrics.successfulRequests / provider.metrics.totalRequests;\r\n\r\n    // Decrease current load\r\n    await this.updateProviderLoad(providerId, -1);\r\n\r\n    // Update health status based on recent performance\r\n    await this.updateProviderHealth(providerId);\r\n\r\n    this.logger.debug(\r\n      `Updated metrics for provider: ${providerId}, success: ${success}, duration: ${duration}ms`\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Checks health of all providers\r\n   */\r\n  async checkProviderHealth(): Promise<Record<string, HealthCheckResult>> {\r\n    const healthResults: Record<string, HealthCheckResult> = {};\r\n\r\n    for (const [providerId, provider] of this.providers) {\r\n      try {\r\n        const healthResult = await this.performHealthCheck(provider);\r\n        this.healthChecks.set(providerId, healthResult);\r\n        healthResults[providerId] = healthResult;\r\n\r\n        // Update provider status based on health check\r\n        provider.status = healthResult.healthy ? 'healthy' : 'unhealthy';\r\n\r\n      } catch (error) {\r\n        this.logger.error(`Health check failed for provider: ${providerId}`, error);\r\n        const failedResult: HealthCheckResult = {\r\n          healthy: false,\r\n          responseTime: -1,\r\n          error: error.message,\r\n          timestamp: new Date(),\r\n        };\r\n        \r\n        this.healthChecks.set(providerId, failedResult);\r\n        healthResults[providerId] = failedResult;\r\n        provider.status = 'unhealthy';\r\n      }\r\n    }\r\n\r\n    return healthResults;\r\n  }\r\n\r\n  /**\r\n   * Adds a new provider to the load balancer\r\n   */\r\n  async addProvider(providerConfig: ProviderConfig): Promise<void> {\r\n    const provider: ProviderInfo = {\r\n      id: providerConfig.id,\r\n      name: providerConfig.name,\r\n      type: providerConfig.type,\r\n      endpoint: providerConfig.endpoint,\r\n      status: 'unknown',\r\n      enabled: true,\r\n      priority: providerConfig.priority || 1,\r\n      weight: providerConfig.weight || 1,\r\n      maxConcurrentRequests: providerConfig.maxConcurrentRequests || 100,\r\n      currentLoad: 0,\r\n      metrics: {\r\n        totalRequests: 0,\r\n        successfulRequests: 0,\r\n        failedRequests: 0,\r\n        averageResponseTime: 0,\r\n        successRate: 1.0,\r\n      },\r\n      config: providerConfig.config || {},\r\n    };\r\n\r\n    this.providers.set(provider.id, provider);\r\n\r\n    // Perform initial health check\r\n    await this.performHealthCheck(provider);\r\n\r\n    this.logger.log(`Added provider: ${provider.id} (${provider.type})`);\r\n  }\r\n\r\n  /**\r\n   * Removes a provider from the load balancer\r\n   */\r\n  async removeProvider(providerId: string): Promise<void> {\r\n    const provider = this.providers.get(providerId);\r\n    if (!provider) {\r\n      throw new Error(`Provider not found: ${providerId}`);\r\n    }\r\n\r\n    // Disable provider first to stop new requests\r\n    provider.enabled = false;\r\n\r\n    // Wait for current requests to complete\r\n    await this.waitForProviderDrain(providerId);\r\n\r\n    // Remove provider\r\n    this.providers.delete(providerId);\r\n    this.healthChecks.delete(providerId);\r\n\r\n    this.logger.log(`Removed provider: ${providerId}`);\r\n  }\r\n\r\n  /**\r\n   * Updates provider configuration\r\n   */\r\n  async updateProviderConfig(\r\n    providerId: string,\r\n    updates: Partial<ProviderConfig>\r\n  ): Promise<void> {\r\n    const provider = this.providers.get(providerId);\r\n    if (!provider) {\r\n      throw new Error(`Provider not found: ${providerId}`);\r\n    }\r\n\r\n    // Update provider properties\r\n    if (updates.name) provider.name = updates.name;\r\n    if (updates.endpoint) provider.endpoint = updates.endpoint;\r\n    if (updates.priority !== undefined) provider.priority = updates.priority;\r\n    if (updates.weight !== undefined) provider.weight = updates.weight;\r\n    if (updates.maxConcurrentRequests !== undefined) {\r\n      provider.maxConcurrentRequests = updates.maxConcurrentRequests;\r\n    }\r\n    if (updates.config) {\r\n      provider.config = { ...provider.config, ...updates.config };\r\n    }\r\n\r\n    this.logger.log(`Updated provider config: ${providerId}`);\r\n  }\r\n\r\n  /**\r\n   * Gets load balancing statistics\r\n   */\r\n  getLoadBalancingStats(): LoadBalancingStats {\r\n    const stats: LoadBalancingStats = {\r\n      totalProviders: this.providers.size,\r\n      healthyProviders: 0,\r\n      unhealthyProviders: 0,\r\n      totalLoad: 0,\r\n      averageResponseTime: 0,\r\n      overallSuccessRate: 0,\r\n      providerStats: {},\r\n    };\r\n\r\n    let totalRequests = 0;\r\n    let totalSuccessful = 0;\r\n    let totalResponseTime = 0;\r\n    let providersWithMetrics = 0;\r\n\r\n    for (const [providerId, provider] of this.providers) {\r\n      if (provider.status === 'healthy') {\r\n        stats.healthyProviders++;\r\n      } else {\r\n        stats.unhealthyProviders++;\r\n      }\r\n\r\n      stats.totalLoad += provider.currentLoad;\r\n\r\n      if (provider.metrics.totalRequests > 0) {\r\n        totalRequests += provider.metrics.totalRequests;\r\n        totalSuccessful += provider.metrics.successfulRequests;\r\n        totalResponseTime += provider.metrics.averageResponseTime;\r\n        providersWithMetrics++;\r\n      }\r\n\r\n      stats.providerStats[providerId] = {\r\n        status: provider.status,\r\n        currentLoad: provider.currentLoad,\r\n        metrics: provider.metrics,\r\n      };\r\n    }\r\n\r\n    if (totalRequests > 0) {\r\n      stats.overallSuccessRate = totalSuccessful / totalRequests;\r\n    }\r\n\r\n    if (providersWithMetrics > 0) {\r\n      stats.averageResponseTime = totalResponseTime / providersWithMetrics;\r\n    }\r\n\r\n    return stats;\r\n  }\r\n\r\n  /**\r\n   * Periodic health check for all providers\r\n   */\r\n  @Cron(CronExpression.EVERY_30_SECONDS)\r\n  async performPeriodicHealthChecks(): Promise<void> {\r\n    this.logger.debug('Performing periodic health checks');\r\n    await this.checkProviderHealth();\r\n  }\r\n\r\n  /**\r\n   * Periodic load balancing optimization\r\n   */\r\n  @Cron(CronExpression.EVERY_MINUTE)\r\n  async optimizeLoadBalancing(): Promise<void> {\r\n    this.logger.debug('Optimizing load balancing');\r\n    \r\n    // Adjust provider weights based on performance\r\n    for (const [providerId, provider] of this.providers) {\r\n      if (provider.metrics.totalRequests > 10) {\r\n        const performanceScore = this.calculatePerformanceScore(provider);\r\n        provider.weight = Math.max(0.1, performanceScore);\r\n      }\r\n    }\r\n  }\r\n\r\n  // Private helper methods\r\n\r\n  private initializeProviders(): void {\r\n    const providerConfigs = this.configService.get<ProviderConfig[]>('ai.providers', []);\r\n    \r\n    for (const config of providerConfigs) {\r\n      this.addProvider(config).catch(error => {\r\n        this.logger.error(`Failed to initialize provider: ${config.id}`, error);\r\n      });\r\n    }\r\n  }\r\n\r\n  private initializeRoutingStrategies(): void {\r\n    this.routingStrategies.set('round-robin', {\r\n      name: 'Round Robin',\r\n      selector: this.roundRobinSelector.bind(this),\r\n    });\r\n\r\n    this.routingStrategies.set('weighted-round-robin', {\r\n      name: 'Weighted Round Robin',\r\n      selector: this.weightedRoundRobinSelector.bind(this),\r\n    });\r\n\r\n    this.routingStrategies.set('least-connections', {\r\n      name: 'Least Connections',\r\n      selector: this.leastConnectionsSelector.bind(this),\r\n    });\r\n\r\n    this.routingStrategies.set('response-time', {\r\n      name: 'Fastest Response Time',\r\n      selector: this.responseTimeSelector.bind(this),\r\n    });\r\n\r\n    this.routingStrategies.set('random', {\r\n      name: 'Random',\r\n      selector: this.randomSelector.bind(this),\r\n    });\r\n  }\r\n\r\n  private getRoutingStrategy(providerType: string): RoutingStrategy {\r\n    const strategyName = this.configService.get<string>(\r\n      `ai.loadBalancing.${providerType}.strategy`,\r\n      'weighted-round-robin'\r\n    );\r\n\r\n    return this.routingStrategies.get(strategyName) || \r\n           this.routingStrategies.get('weighted-round-robin')!;\r\n  }\r\n\r\n  private sortProvidersByStrategy(\r\n    providers: ProviderInfo[],\r\n    strategy: RoutingStrategy\r\n  ): ProviderInfo[] {\r\n    return strategy.selector(providers, null);\r\n  }\r\n\r\n  private selectProviderByStrategy(\r\n    providers: ProviderInfo[],\r\n    strategy: RoutingStrategy,\r\n    request: LoadBalancingRequest\r\n  ): ProviderInfo {\r\n    const sorted = strategy.selector(providers, request);\r\n    return sorted[0];\r\n  }\r\n\r\n  private roundRobinSelector(\r\n    providers: ProviderInfo[],\r\n    request: LoadBalancingRequest | null\r\n  ): ProviderInfo[] {\r\n    // Simple round-robin based on total requests\r\n    return providers.sort((a, b) => a.metrics.totalRequests - b.metrics.totalRequests);\r\n  }\r\n\r\n  private weightedRoundRobinSelector(\r\n    providers: ProviderInfo[],\r\n    request: LoadBalancingRequest | null\r\n  ): ProviderInfo[] {\r\n    return providers.sort((a, b) => {\r\n      const aScore = a.weight / (a.currentLoad + 1);\r\n      const bScore = b.weight / (b.currentLoad + 1);\r\n      return bScore - aScore;\r\n    });\r\n  }\r\n\r\n  private leastConnectionsSelector(\r\n    providers: ProviderInfo[],\r\n    request: LoadBalancingRequest | null\r\n  ): ProviderInfo[] {\r\n    return providers.sort((a, b) => a.currentLoad - b.currentLoad);\r\n  }\r\n\r\n  private responseTimeSelector(\r\n    providers: ProviderInfo[],\r\n    request: LoadBalancingRequest | null\r\n  ): ProviderInfo[] {\r\n    return providers.sort((a, b) => \r\n      a.metrics.averageResponseTime - b.metrics.averageResponseTime\r\n    );\r\n  }\r\n\r\n  private randomSelector(\r\n    providers: ProviderInfo[],\r\n    request: LoadBalancingRequest | null\r\n  ): ProviderInfo[] {\r\n    const shuffled = [...providers];\r\n    for (let i = shuffled.length - 1; i > 0; i--) {\r\n      const j = Math.floor(Math.random() * (i + 1));\r\n      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];\r\n    }\r\n    return shuffled;\r\n  }\r\n\r\n  private async updateProviderLoad(providerId: string, delta: number): Promise<void> {\r\n    const provider = this.providers.get(providerId);\r\n    if (provider) {\r\n      provider.currentLoad = Math.max(0, provider.currentLoad + delta);\r\n    }\r\n  }\r\n\r\n  private async updateProviderHealth(providerId: string): Promise<void> {\r\n    const provider = this.providers.get(providerId);\r\n    if (!provider) return;\r\n\r\n    // Mark as unhealthy if success rate is too low\r\n    if (provider.metrics.totalRequests > 10 && provider.metrics.successRate < 0.8) {\r\n      provider.status = 'unhealthy';\r\n    }\r\n\r\n    // Mark as unhealthy if response time is too high\r\n    if (provider.metrics.averageResponseTime > 10000) { // 10 seconds\r\n      provider.status = 'unhealthy';\r\n    }\r\n  }\r\n\r\n  private async performHealthCheck(provider: ProviderInfo): Promise<HealthCheckResult> {\r\n    const startTime = Date.now();\r\n    \r\n    try {\r\n      // Perform actual health check based on provider type\r\n      // This is a simplified implementation\r\n      const responseTime = Date.now() - startTime;\r\n      \r\n      return {\r\n        healthy: true,\r\n        responseTime,\r\n        timestamp: new Date(),\r\n      };\r\n      \r\n    } catch (error) {\r\n      return {\r\n        healthy: false,\r\n        responseTime: Date.now() - startTime,\r\n        error: error.message,\r\n        timestamp: new Date(),\r\n      };\r\n    }\r\n  }\r\n\r\n  private calculateMovingAverage(\r\n    currentAverage: number,\r\n    newValue: number,\r\n    count: number\r\n  ): number {\r\n    return ((currentAverage * (count - 1)) + newValue) / count;\r\n  }\r\n\r\n  private calculatePerformanceScore(provider: ProviderInfo): number {\r\n    const successWeight = 0.4;\r\n    const responseTimeWeight = 0.3;\r\n    const availabilityWeight = 0.3;\r\n\r\n    const successScore = provider.metrics.successRate;\r\n    const responseTimeScore = Math.max(0, 1 - (provider.metrics.averageResponseTime / 10000));\r\n    const availabilityScore = provider.status === 'healthy' ? 1 : 0;\r\n\r\n    return (\r\n      successScore * successWeight +\r\n      responseTimeScore * responseTimeWeight +\r\n      availabilityScore * availabilityWeight\r\n    );\r\n  }\r\n\r\n  private async waitForProviderDrain(providerId: string): Promise<void> {\r\n    const provider = this.providers.get(providerId);\r\n    if (!provider) return;\r\n\r\n    const maxWaitTime = 30000; // 30 seconds\r\n    const startTime = Date.now();\r\n\r\n    while (provider.currentLoad > 0 && Date.now() - startTime < maxWaitTime) {\r\n      await new Promise(resolve => setTimeout(resolve, 1000));\r\n    }\r\n  }\r\n}\r\n\r\n// Type definitions\r\ninterface ProviderInfo {\r\n  id: string;\r\n  name: string;\r\n  type: string;\r\n  endpoint: string;\r\n  status: 'healthy' | 'unhealthy' | 'unknown';\r\n  enabled: boolean;\r\n  priority: number;\r\n  weight: number;\r\n  maxConcurrentRequests: number;\r\n  currentLoad: number;\r\n  metrics: ProviderMetrics;\r\n  config: any;\r\n}\r\n\r\ninterface ProviderConfig {\r\n  id: string;\r\n  name: string;\r\n  type: string;\r\n  endpoint: string;\r\n  priority?: number;\r\n  weight?: number;\r\n  maxConcurrentRequests?: number;\r\n  config?: any;\r\n}\r\n\r\ninterface ProviderMetrics {\r\n  totalRequests: number;\r\n  successfulRequests: number;\r\n  failedRequests: number;\r\n  averageResponseTime: number;\r\n  successRate: number;\r\n}\r\n\r\ninterface HealthCheckResult {\r\n  healthy: boolean;\r\n  responseTime: number;\r\n  error?: string;\r\n  timestamp: Date;\r\n}\r\n\r\ninterface LoadBalancingRequest {\r\n  priority?: 'low' | 'medium' | 'high';\r\n  timeout?: number;\r\n  requirements?: any;\r\n}\r\n\r\ninterface RoutingStrategy {\r\n  name: string;\r\n  selector: (providers: ProviderInfo[], request: LoadBalancingRequest | null) => ProviderInfo[];\r\n}\r\n\r\ninterface LoadBalancingStats {\r\n  totalProviders: number;\r\n  healthyProviders: number;\r\n  unhealthyProviders: number;\r\n  totalLoad: number;\r\n  averageResponseTime: number;\r\n  overallSuccessRate: number;\r\n  providerStats: Record<string, {\r\n    status: string;\r\n    currentLoad: number;\r\n    metrics: ProviderMetrics;\r\n  }>;\r\n}\r\n"], "version": 3}