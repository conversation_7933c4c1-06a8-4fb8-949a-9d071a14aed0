import { BaseValueObject } from '../../../../shared-kernel/value-objects/base-value-object';

/**
 * Confidence Score Value Object
 * 
 * Represents a confidence score for AI predictions and analysis results.
 * Ensures the score is within valid bounds (0.0 to 1.0) and provides
 * utility methods for confidence level classification.
 */
export class ConfidenceScore extends BaseValueObject<number> {
  public static readonly MIN_CONFIDENCE = 0.0;
  public static readonly MAX_CONFIDENCE = 1.0;
  
  // Confidence level thresholds
  public static readonly LOW_THRESHOLD = 0.3;
  public static readonly MEDIUM_THRESHOLD = 0.7;
  public static readonly HIGH_THRESHOLD = 0.9;

  constructor(value: number) {
    super(value);
  }

  protected validate(): void {
    super.validate();
    
    if (typeof this._value !== 'number') {
      throw new Error('Confidence score must be a number');
    }

    if (isNaN(this._value)) {
      throw new Error('Confidence score cannot be NaN');
    }

    if (!isFinite(this._value)) {
      throw new Error('Confidence score must be finite');
    }

    if (this._value < ConfidenceScore.MIN_CONFIDENCE) {
      throw new Error(`Confidence score cannot be less than ${ConfidenceScore.MIN_CONFIDENCE}`);
    }

    if (this._value > ConfidenceScore.MAX_CONFIDENCE) {
      throw new Error(`Confidence score cannot be greater than ${ConfidenceScore.MAX_CONFIDENCE}`);
    }
  }

  /**
   * Creates a confidence score from a percentage (0-100)
   */
  public static fromPercentage(percentage: number): ConfidenceScore {
    if (percentage < 0 || percentage > 100) {
      throw new Error('Percentage must be between 0 and 100');
    }
    return new ConfidenceScore(percentage / 100);
  }

  /**
   * Creates a confidence score from a fraction (numerator/denominator)
   */
  public static fromFraction(numerator: number, denominator: number): ConfidenceScore {
    if (denominator === 0) {
      throw new Error('Denominator cannot be zero');
    }
    if (numerator < 0 || denominator < 0) {
      throw new Error('Numerator and denominator must be non-negative');
    }
    return new ConfidenceScore(numerator / denominator);
  }

  /**
   * Creates a low confidence score
   */
  public static low(): ConfidenceScore {
    return new ConfidenceScore(ConfidenceScore.LOW_THRESHOLD);
  }

  /**
   * Creates a medium confidence score
   */
  public static medium(): ConfidenceScore {
    return new ConfidenceScore(ConfidenceScore.MEDIUM_THRESHOLD);
  }

  /**
   * Creates a high confidence score
   */
  public static high(): ConfidenceScore {
    return new ConfidenceScore(ConfidenceScore.HIGH_THRESHOLD);
  }

  /**
   * Creates a maximum confidence score
   */
  public static maximum(): ConfidenceScore {
    return new ConfidenceScore(ConfidenceScore.MAX_CONFIDENCE);
  }

  /**
   * Creates a minimum confidence score
   */
  public static minimum(): ConfidenceScore {
    return new ConfidenceScore(ConfidenceScore.MIN_CONFIDENCE);
  }

  /**
   * Gets the confidence level as a string
   */
  public getLevel(): 'very_low' | 'low' | 'medium' | 'high' | 'very_high' {
    if (this._value < 0.2) {
      return 'very_low';
    } else if (this._value < ConfidenceScore.LOW_THRESHOLD) {
      return 'low';
    } else if (this._value < ConfidenceScore.MEDIUM_THRESHOLD) {
      return 'medium';
    } else if (this._value < ConfidenceScore.HIGH_THRESHOLD) {
      return 'high';
    } else {
      return 'very_high';
    }
  }

  /**
   * Checks if the confidence is low
   */
  public isLow(): boolean {
    return this._value < ConfidenceScore.LOW_THRESHOLD;
  }

  /**
   * Checks if the confidence is medium
   */
  public isMedium(): boolean {
    return this._value >= ConfidenceScore.LOW_THRESHOLD && this._value < ConfidenceScore.MEDIUM_THRESHOLD;
  }

  /**
   * Checks if the confidence is high
   */
  public isHigh(): boolean {
    return this._value >= ConfidenceScore.HIGH_THRESHOLD;
  }

  /**
   * Checks if the confidence meets a minimum threshold
   */
  public meetsThreshold(threshold: number): boolean {
    return this._value >= threshold;
  }

  /**
   * Gets the confidence as a percentage
   */
  public toPercentage(): number {
    return this._value * 100;
  }

  /**
   * Gets the confidence as a percentage string
   */
  public toPercentageString(decimals: number = 1): string {
    return `${(this._value * 100).toFixed(decimals)}%`;
  }

  /**
   * Combines this confidence with another using weighted average
   */
  public combineWith(other: ConfidenceScore, weight: number = 0.5): ConfidenceScore {
    if (weight < 0 || weight > 1) {
      throw new Error('Weight must be between 0 and 1');
    }
    
    const combinedValue = this._value * weight + other._value * (1 - weight);
    return new ConfidenceScore(combinedValue);
  }

  /**
   * Gets the inverse confidence (1 - confidence)
   */
  public inverse(): ConfidenceScore {
    return new ConfidenceScore(1 - this._value);
  }

  /**
   * Multiplies this confidence by a factor
   */
  public multiply(factor: number): ConfidenceScore {
    if (factor < 0) {
      throw new Error('Factor cannot be negative');
    }
    
    const result = this._value * factor;
    return new ConfidenceScore(Math.min(result, ConfidenceScore.MAX_CONFIDENCE));
  }

  /**
   * Adds another confidence score (capped at maximum)
   */
  public add(other: ConfidenceScore): ConfidenceScore {
    const result = this._value + other._value;
    return new ConfidenceScore(Math.min(result, ConfidenceScore.MAX_CONFIDENCE));
  }

  /**
   * Subtracts another confidence score (floored at minimum)
   */
  public subtract(other: ConfidenceScore): ConfidenceScore {
    const result = this._value - other._value;
    return new ConfidenceScore(Math.max(result, ConfidenceScore.MIN_CONFIDENCE));
  }

  /**
   * Gets the absolute difference between this and another confidence score
   */
  public differenceFrom(other: ConfidenceScore): number {
    return Math.abs(this._value - other._value);
  }

  /**
   * Checks if this confidence is greater than another
   */
  public isGreaterThan(other: ConfidenceScore): boolean {
    return this._value > other._value;
  }

  /**
   * Checks if this confidence is less than another
   */
  public isLessThan(other: ConfidenceScore): boolean {
    return this._value < other._value;
  }

  /**
   * Rounds the confidence to a specified number of decimal places
   */
  public round(decimals: number = 2): ConfidenceScore {
    const factor = Math.pow(10, decimals);
    const rounded = Math.round(this._value * factor) / factor;
    return new ConfidenceScore(rounded);
  }

  /**
   * Converts to a human-readable string
   */
  public toString(): string {
    return `${this.toPercentageString()} (${this.getLevel()})`;
  }

  /**
   * Converts to JSON representation
   */
  public toJSON(): Record<string, any> {
    return {
      value: this._value,
      percentage: this.toPercentage(),
      level: this.getLevel(),
      isHigh: this.isHigh(),
      isMedium: this.isMedium(),
      isLow: this.isLow(),
    };
  }

  /**
   * Creates a ConfidenceScore from JSON
   */
  public static fromJSON(json: Record<string, any>): ConfidenceScore {
    return new ConfidenceScore(json.value);
  }

  /**
   * Calculates the average of multiple confidence scores
   */
  public static average(scores: ConfidenceScore[]): ConfidenceScore {
    if (scores.length === 0) {
      throw new Error('Cannot calculate average of empty array');
    }
    
    const sum = scores.reduce((acc, score) => acc + score._value, 0);
    return new ConfidenceScore(sum / scores.length);
  }

  /**
   * Calculates the weighted average of multiple confidence scores
   */
  public static weightedAverage(scores: ConfidenceScore[], weights: number[]): ConfidenceScore {
    if (scores.length === 0) {
      throw new Error('Cannot calculate weighted average of empty array');
    }
    
    if (scores.length !== weights.length) {
      throw new Error('Scores and weights arrays must have the same length');
    }
    
    const totalWeight = weights.reduce((acc, weight) => acc + weight, 0);
    if (totalWeight === 0) {
      throw new Error('Total weight cannot be zero');
    }
    
    const weightedSum = scores.reduce((acc, score, index) => acc + score._value * weights[index], 0);
    return new ConfidenceScore(weightedSum / totalWeight);
  }

  /**
   * Gets the maximum confidence from an array
   */
  public static max(scores: ConfidenceScore[]): ConfidenceScore {
    if (scores.length === 0) {
      throw new Error('Cannot find maximum of empty array');
    }
    
    const maxValue = Math.max(...scores.map(score => score._value));
    return new ConfidenceScore(maxValue);
  }

  /**
   * Gets the minimum confidence from an array
   */
  public static min(scores: ConfidenceScore[]): ConfidenceScore {
    if (scores.length === 0) {
      throw new Error('Cannot find minimum of empty array');
    }
    
    const minValue = Math.min(...scores.map(score => score._value));
    return new ConfidenceScore(minValue);
  }
}