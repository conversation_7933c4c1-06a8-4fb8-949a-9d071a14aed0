11a2f81dbe5cac4c3d0bc83fd611578e
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// Mock axios
jest.mock('axios');
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const ai_http_client_1 = require("../ai-http.client");
const axios_1 = __importDefault(require("axios"));
const mockedAxios = axios_1.default;
describe('AiHttpClient', () => {
    let client;
    let configService;
    let mockAxiosInstance;
    const mockAxiosResponse = (data, status = 200) => ({
        data,
        status,
        statusText: 'OK',
        headers: {},
        config: {},
    });
    const mockAxiosError = (status, message) => ({
        name: 'AxiosError',
        message,
        response: {
            status,
            statusText: message,
            data: { message },
            headers: {},
            config: {},
        },
        config: {},
        isAxiosError: true,
        toJSON: () => ({}),
    });
    beforeEach(async () => {
        // Reset mocks
        jest.clearAllMocks();
        // Mock axios instance
        mockAxiosInstance = {
            request: jest.fn(),
        };
        // Mock axios.create to return our mock instance
        mockedAxios.create.mockReturnValue(mockAxiosInstance);
        const mockConfigService = {
            get: jest.fn().mockImplementation((key, defaultValue) => {
                const config = {
                    'ai.http.timeout': 30000,
                    'ai.http.retries': 3,
                };
                return config[key] || defaultValue;
            }),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                ai_http_client_1.AiHttpClient,
                { provide: config_1.ConfigService, useValue: mockConfigService },
            ],
        }).compile();
        client = module.get(ai_http_client_1.AiHttpClient);
        configService = module.get(config_1.ConfigService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('sendAnalysisRequest', () => {
        it('should send analysis request successfully', async () => {
            const mockResponse = {
                id: 'analysis-123',
                result: { threat_level: 'high', confidence: 0.95 },
                confidence: 0.95,
                model: 'threat-detector-v1',
                timestamp: new Date().toISOString(),
            };
            mockAxiosInstance.request.mockResolvedValue(mockAxiosResponse(mockResponse));
            const payload = {
                data: { event: 'suspicious_login' },
                model: 'threat-detector-v1',
            };
            const result = await client.sendAnalysisRequest('http://ai-service/analyze', payload);
            expect(result).toMatchObject({
                id: expect.any(String),
                result: { threat_level: 'high', confidence: 0.95 },
                confidence: 0.95,
                metadata: expect.objectContaining({
                    model: 'threat-detector-v1',
                }),
                timestamp: expect.any(Date),
            });
            expect(mockAxiosInstance.request).toHaveBeenCalledWith(expect.objectContaining({
                method: 'POST',
                url: 'http://ai-service/analyze',
                data: payload,
                timeout: 30000,
                headers: expect.objectContaining({
                    'Content-Type': 'application/json',
                    'User-Agent': 'Sentinel-AI-Client/1.0',
                    'X-Request-ID': expect.any(String),
                }),
            }));
        });
        it('should handle analysis request failure', async () => {
            const error = mockAxiosError(500, 'Internal Server Error');
            mockAxiosInstance.request.mockRejectedValue(error);
            const payload = { data: { event: 'test' } };
            await expect(client.sendAnalysisRequest('http://ai-service/analyze', payload)).rejects.toThrow('Analysis request failed');
        });
        it('should use custom timeout and headers', async () => {
            const mockResponse = { id: 'test', result: {}, confidence: 0.8 };
            mockAxiosInstance.request.mockResolvedValue(mockAxiosResponse(mockResponse));
            const payload = { data: { event: 'test' } };
            const options = {
                timeout: 60000,
                apiKey: 'test-key',
                customHeaders: { 'X-Custom': 'value' },
            };
            await client.sendAnalysisRequest('http://ai-service/analyze', payload, options);
            expect(mockAxiosInstance.request).toHaveBeenCalledWith(expect.objectContaining({
                timeout: 60000,
                headers: expect.objectContaining({
                    'Authorization': 'Bearer test-key',
                    'X-Custom': 'value',
                }),
            }));
        });
    });
    describe('sendTrainingRequest', () => {
        it('should send training request successfully', async () => {
            const mockResponse = {
                jobId: 'job-123',
                status: 'completed',
                modelId: 'model-456',
                metrics: { accuracy: 0.95 },
                timestamp: new Date().toISOString(),
            };
            mockAxiosInstance.request.mockResolvedValue(mockAxiosResponse(mockResponse));
            const payload = {
                trainingData: [{ input: 'test', output: 'result' }],
                modelConfig: { type: 'classifier' },
            };
            const result = await client.sendTrainingRequest('http://ai-service/train', payload);
            expect(result).toMatchObject({
                jobId: 'job-123',
                status: 'completed',
                modelId: 'model-456',
                metrics: { accuracy: 0.95 },
                timestamp: expect.any(Date),
            });
            expect(mockAxiosInstance.request).toHaveBeenCalledWith(expect.objectContaining({
                method: 'POST',
                url: 'http://ai-service/train',
                data: payload,
                timeout: 300000, // 5 minutes default for training
            }));
        });
        it('should handle training request failure', async () => {
            const error = mockAxiosError(400, 'Invalid training data');
            mockAxiosInstance.request.mockRejectedValue(error);
            const payload = {
                trainingData: [],
                modelConfig: { type: 'classifier' },
            };
            await expect(client.sendTrainingRequest('http://ai-service/train', payload)).rejects.toThrow('Training request failed');
        });
    });
    describe('sendPredictionRequest', () => {
        it('should send prediction request successfully', async () => {
            const mockResponse = {
                prediction: 'malicious',
                confidence: 0.87,
                alternatives: ['benign'],
                model: 'classifier-v2',
                latency: 150,
                timestamp: new Date().toISOString(),
            };
            mockAxiosInstance.request.mockResolvedValue(mockAxiosResponse(mockResponse));
            const payload = {
                input: { features: [1, 2, 3] },
                model: 'classifier-v2',
            };
            const result = await client.sendPredictionRequest('http://ai-service/predict', payload);
            expect(result).toMatchObject({
                prediction: 'malicious',
                confidence: 0.87,
                alternatives: ['benign'],
                metadata: expect.objectContaining({
                    model: 'classifier-v2',
                    latency: 150,
                }),
                timestamp: expect.any(Date),
            });
            expect(mockAxiosInstance.request).toHaveBeenCalledWith(expect.objectContaining({
                method: 'POST',
                url: 'http://ai-service/predict',
                data: payload,
                timeout: 5000, // 5 seconds default for predictions
            }));
        });
        it('should handle prediction request failure', async () => {
            const error = mockAxiosError(422, 'Invalid input format');
            mockAxiosInstance.request.mockRejectedValue(error);
            const payload = { input: null };
            await expect(client.sendPredictionRequest('http://ai-service/predict', payload)).rejects.toThrow('Prediction request failed');
        });
    });
    describe('sendHealthCheckRequest', () => {
        it('should send health check request successfully', async () => {
            const mockResponse = {
                healthy: true,
                status: 'operational',
                responseTime: 45,
                version: '1.2.3',
            };
            mockAxiosInstance.request.mockResolvedValue(mockAxiosResponse(mockResponse));
            const result = await client.sendHealthCheckRequest('http://ai-service');
            expect(result).toMatchObject({
                healthy: true,
                status: 'operational',
                responseTime: 45,
                version: '1.2.3',
                timestamp: expect.any(Date),
            });
            expect(mockAxiosInstance.request).toHaveBeenCalledWith(expect.objectContaining({
                method: 'GET',
                url: 'http://ai-service/health',
                timeout: 5000,
            }));
        });
        it('should handle health check failure gracefully', async () => {
            const error = mockAxiosError(503, 'Service Unavailable');
            mockAxiosInstance.request.mockRejectedValue(error);
            const result = await client.sendHealthCheckRequest('http://ai-service');
            expect(result).toMatchObject({
                healthy: false,
                status: 'unhealthy',
                error: expect.any(String),
                timestamp: expect.any(Date),
            });
        });
        it('should consider 200 status as healthy when no explicit healthy field', async () => {
            const mockResponse = { status: 'ok' };
            mockAxiosInstance.request.mockResolvedValue(mockAxiosResponse(mockResponse, 200));
            const result = await client.sendHealthCheckRequest('http://ai-service');
            expect(result.healthy).toBe(true);
        });
    });
    describe('sendBatchRequest', () => {
        it('should send batch request successfully', async () => {
            const mockResponse = {
                batchId: 'batch-123',
                results: [
                    { id: '1', result: 'success' },
                    { id: '2', result: 'success' },
                ],
                total: 2,
                successful: 2,
                failed: 0,
                processingTime: 1500,
                timestamp: new Date().toISOString(),
            };
            mockAxiosInstance.request.mockResolvedValue(mockAxiosResponse(mockResponse));
            const payloads = [
                { id: '1', data: { input: 'test1' }, type: 'analysis' },
                { id: '2', data: { input: 'test2' }, type: 'analysis' },
            ];
            const result = await client.sendBatchRequest('http://ai-service/batch', payloads);
            expect(result).toMatchObject({
                batchId: 'batch-123',
                results: expect.arrayContaining([
                    { id: '1', result: 'success' },
                    { id: '2', result: 'success' },
                ]),
                summary: {
                    total: 2,
                    successful: 2,
                    failed: 0,
                    processingTime: 1500,
                },
                timestamp: expect.any(Date),
            });
            expect(mockAxiosInstance.request).toHaveBeenCalledWith(expect.objectContaining({
                method: 'POST',
                url: 'http://ai-service/batch',
                data: expect.objectContaining({
                    requests: payloads,
                    batchId: expect.any(String),
                    timestamp: expect.any(String),
                }),
                timeout: 120000, // 2 minutes default for batch
            }));
        });
        it('should handle batch request failure', async () => {
            const error = mockAxiosError(413, 'Payload Too Large');
            mockAxiosInstance.request.mockRejectedValue(error);
            const payloads = [{ id: '1', data: {}, type: 'test' }];
            await expect(client.sendBatchRequest('http://ai-service/batch', payloads)).rejects.toThrow('Batch request failed');
        });
    });
    describe('uploadFile', () => {
        it('should upload file successfully', async () => {
            const mockResponse = {
                fileId: 'file-123',
                filename: 'test.csv',
                size: 1024,
                url: 'http://storage/file-123',
            };
            mockAxiosInstance.request.mockResolvedValue(mockAxiosResponse(mockResponse));
            const fileBuffer = Buffer.from('test,data\n1,2\n3,4');
            const result = await client.uploadFile('http://ai-service/upload', fileBuffer, 'test.csv');
            expect(result).toEqual(mockResponse);
            expect(mockAxiosInstance.request).toHaveBeenCalledWith(expect.objectContaining({
                method: 'POST',
                url: 'http://ai-service/upload',
                data: expect.any(FormData),
                timeout: 60000,
                headers: expect.objectContaining({
                    'Content-Type': 'multipart/form-data',
                }),
            }));
        });
        it('should handle file upload failure', async () => {
            const error = mockAxiosError(413, 'File Too Large');
            mockAxiosInstance.request.mockRejectedValue(error);
            const fileBuffer = Buffer.from('test data');
            await expect(client.uploadFile('http://ai-service/upload', fileBuffer, 'test.txt')).rejects.toThrow('File upload failed');
        });
    });
    describe('downloadFile', () => {
        it('should download file successfully', async () => {
            const fileData = new ArrayBuffer(1024);
            const mockResponse = mockAxiosResponse(fileData);
            mockResponse.headers = {
                'content-type': 'application/octet-stream',
                'content-disposition': 'attachment; filename="downloaded.bin"',
            };
            mockAxiosInstance.request.mockResolvedValue(mockResponse);
            const result = await client.downloadFile('http://ai-service/files', 'file-123');
            expect(result).toMatchObject({
                data: expect.any(Buffer),
                filename: 'downloaded.bin',
                contentType: 'application/octet-stream',
                size: 1024,
            });
            expect(mockAxiosInstance.request).toHaveBeenCalledWith(expect.objectContaining({
                method: 'GET',
                url: 'http://ai-service/files/file-123',
                responseType: 'arraybuffer',
                timeout: 60000,
            }));
        });
        it('should handle file download failure', async () => {
            const error = mockAxiosError(404, 'File Not Found');
            mockAxiosInstance.request.mockRejectedValue(error);
            await expect(client.downloadFile('http://ai-service/files', 'nonexistent')).rejects.toThrow('File download failed');
        });
        it('should use default filename when content-disposition header is missing', async () => {
            const fileData = new ArrayBuffer(512);
            const mockResponse = mockAxiosResponse(fileData);
            mockResponse.headers = { 'content-type': 'text/plain' };
            mockAxiosInstance.request.mockResolvedValue(mockResponse);
            const result = await client.downloadFile('http://ai-service/files', 'file-123');
            expect(result.filename).toBe('download');
        });
    });
    describe('streamRequest', () => {
        it('should handle stream request successfully', async () => {
            const mockStream = new ReadableStream();
            mockAxiosInstance.request.mockResolvedValue(mockAxiosResponse(mockStream));
            const payload = { data: 'streaming test' };
            const result = await client.streamRequest('http://ai-service/stream', payload);
            expect(result).toBe(mockStream);
            expect(mockAxiosInstance.request).toHaveBeenCalledWith(expect.objectContaining({
                method: 'POST',
                url: 'http://ai-service/stream',
                data: payload,
                responseType: 'stream',
            }));
        });
        it('should handle stream request failure', async () => {
            const error = mockAxiosError(500, 'Stream Error');
            mockAxiosInstance.request.mockRejectedValue(error);
            const payload = { data: 'test' };
            await expect(client.streamRequest('http://ai-service/stream', payload)).rejects.toThrow('Stream request failed');
        });
    });
    describe('error handling', () => {
        it('should handle timeout errors', async () => {
            const timeoutError = new Error('timeout of 30000ms exceeded');
            timeoutError['code'] = 'ECONNABORTED';
            mockAxiosInstance.request.mockRejectedValue(timeoutError);
            await expect(client.sendAnalysisRequest('http://ai-service/analyze', { data: {} })).rejects.toThrow('Request timeout');
        });
        it('should handle connection refused errors', async () => {
            const connectionError = new Error('connect ECONNREFUSED');
            connectionError['code'] = 'ECONNREFUSED';
            mockAxiosInstance.request.mockRejectedValue(connectionError);
            await expect(client.sendAnalysisRequest('http://ai-service/analyze', { data: {} })).rejects.toThrow('Connection refused');
        });
        it('should handle unknown errors', async () => {
            const unknownError = new Error('Unknown error');
            mockAxiosInstance.request.mockRejectedValue(unknownError);
            await expect(client.sendAnalysisRequest('http://ai-service/analyze', { data: {} })).rejects.toThrow('Unknown error');
        });
    });
    describe('request configuration', () => {
        it('should generate unique request IDs', async () => {
            const mockResponse = { id: 'test', result: {}, confidence: 0.5 };
            mockAxiosInstance.request.mockResolvedValue(mockAxiosResponse(mockResponse));
            await client.sendAnalysisRequest('http://ai-service/analyze', { data: {} });
            await client.sendAnalysisRequest('http://ai-service/analyze', { data: {} });
            const calls = mockAxiosInstance.request.mock.calls;
            const requestId1 = calls[0][0].headers['X-Request-ID'];
            const requestId2 = calls[1][0].headers['X-Request-ID'];
            expect(requestId1).toBeDefined();
            expect(requestId2).toBeDefined();
            expect(requestId1).not.toBe(requestId2);
        });
        it('should use configured timeout and retries', async () => {
            configService.get.mockImplementation((key, defaultValue) => {
                const config = {
                    'ai.http.timeout': 45000,
                    'ai.http.retries': 5,
                };
                return config[key] || defaultValue;
            });
            // Create new instance to pick up new config
            const newClient = new ai_http_client_1.AiHttpClient(configService);
            const mockResponse = { id: 'test', result: {}, confidence: 0.5 };
            mockAxiosInstance.request.mockResolvedValue(mockAxiosResponse(mockResponse));
            await newClient.sendAnalysisRequest('http://ai-service/analyze', { data: {} });
            expect(mockAxiosInstance.request).toHaveBeenCalledWith(expect.objectContaining({
                timeout: 45000,
            }));
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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