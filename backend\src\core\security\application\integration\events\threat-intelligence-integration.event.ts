import { BaseIntegrationEvent } from '../../../../../shared-kernel/integration/base-integration-event';

/**
 * Threat Intelligence Integration Event Data Interface
 */
export interface ThreatIntelligenceEventData {
  threatId: string;
  threatType: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  source: string;
  indicators: Array<{
    type: string;
    value: string;
    confidence: number;
  }>;
  description: string;
  tags: string[];
  firstSeen: Date;
  lastSeen: Date;
  correlationId?: string;
  metadata?: Record<string, any>;
}

/**
 * Threat Intelligence Integration Event
 * 
 * Event fired when threat intelligence data is integrated into the system
 */
export class ThreatIntelligenceIntegrationEvent extends BaseIntegrationEvent<ThreatIntelligenceEventData> {
  constructor(eventData: ThreatIntelligenceEventData) {
    super('threat-intelligence-integration', eventData, {
      source: 'threat-intelligence-module',
      version: '1.0'
    });
  }

  /**
   * Validate threat intelligence event data
   */
  validate(): boolean {
    if (!this.eventData.threatId || !this.eventData.threatType || !this.eventData.severity) {
      return false;
    }

    if (!this.eventData.indicators || this.eventData.indicators.length === 0) {
      return false;
    }

    // Validate indicators
    for (const indicator of this.eventData.indicators) {
      if (!indicator.type || !indicator.value || typeof indicator.confidence !== 'number') {
        return false;
      }

      if (indicator.confidence < 0 || indicator.confidence > 1) {
        return false;
      }
    }

    return true;
  }

  /**
   * Get event priority based on threat severity
   */
  getPriority(): 'low' | 'medium' | 'high' | 'critical' {
    return this.eventData.severity;
  }

  /**
   * Check if event requires immediate processing
   */
  requiresImmediateProcessing(): boolean {
    return this.eventData.severity === 'critical' ||
           this.eventData.severity === 'high';
  }

  /**
   * Get threat ID
   */
  getThreatId(): string {
    return this.eventData.threatId;
  }

  /**
   * Get threat type
   */
  getThreatType(): string {
    return this.eventData.threatType;
  }

  /**
   * Get threat severity
   */
  getThreatSeverity(): string {
    return this.eventData.severity;
  }

  /**
   * Get threat indicators
   */
  getThreatIndicators(): Array<{ type: string; value: string; confidence: number }> {
    return this.eventData.indicators || [];
  }

  /**
   * Get threat source
   */
  getThreatSource(): string {
    return this.eventData.source;
  }

  /**
   * Check if threat is high confidence
   */
  isHighConfidence(): boolean {
    const indicators = this.getThreatIndicators();
    return indicators.some(indicator => indicator.confidence >= 0.8);
  }

  /**
   * Check if threat requires immediate action
   */
  requiresImmediateAction(): boolean {
    return this.requiresImmediateProcessing() && this.isHighConfidence();
  }

  /**
   * Get threat description
   */
  getThreatDescription(): string {
    return this.eventData.description || '';
  }

  /**
   * Get first seen timestamp
   */
  getFirstSeen(): Date {
    return this.eventData.firstSeen;
  }

  /**
   * Get last seen timestamp
   */
  getLastSeen(): Date {
    return this.eventData.lastSeen;
  }

  /**
   * Get threat age in hours
   */
  getThreatAgeInHours(): number {
    const now = new Date();
    const firstSeen = this.getFirstSeen();
    return Math.floor((now.getTime() - firstSeen.getTime()) / (1000 * 60 * 60));
  }

  /**
   * Check if threat is recent (within last 24 hours)
   */
  isRecentThreat(): boolean {
    return this.getThreatAgeInHours() <= 24;
  }
}
