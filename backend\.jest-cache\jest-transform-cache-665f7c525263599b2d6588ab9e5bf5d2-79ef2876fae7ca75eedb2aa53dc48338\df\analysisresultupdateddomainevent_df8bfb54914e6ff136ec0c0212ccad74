498f6604d28fd5a462bcd468f5427bdc
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalysisResultUpdatedEvent = void 0;
const base_domain_event_1 = require("../../../../shared-kernel/domain/base-domain-event");
/**
 * Analysis Result Updated Domain Event
 *
 * Published when an analysis result is updated.
 * This event can trigger various downstream processes such as
 * cache invalidation, index updates, and change tracking.
 */
class AnalysisResultUpdatedEvent extends base_domain_event_1.BaseDomainEvent {
    constructor(analysisResultId, requestId, updateType, eventId, occurredOn) {
        super(eventId, occurredOn);
        this.analysisResultId = analysisResultId;
        this.requestId = requestId;
        this.updateType = updateType;
    }
    getEventName() {
        return 'AnalysisResultUpdated';
    }
    getEventVersion() {
        return '1.0';
    }
    getEventData() {
        return {
            analysisResultId: this.analysisResultId.toString(),
            requestId: this.requestId,
            updateType: this.updateType,
        };
    }
}
exports.AnalysisResultUpdatedEvent = AnalysisResultUpdatedEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxtb2R1bGVzXFxhaVxcZG9tYWluXFxldmVudHNcXGFuYWx5c2lzLXJlc3VsdC11cGRhdGVkLmRvbWFpbi1ldmVudC50cyIsIm1hcHBpbmdzIjoiOzs7QUFBQSwwRkFBcUY7QUFHckY7Ozs7OztHQU1HO0FBQ0gsTUFBYSwwQkFBMkIsU0FBUSxtQ0FBZTtJQUM3RCxZQUNrQixnQkFBZ0MsRUFDaEMsU0FBaUIsRUFDakIsVUFBa0IsRUFDbEMsT0FBd0IsRUFDeEIsVUFBaUI7UUFFakIsS0FBSyxDQUFDLE9BQU8sRUFBRSxVQUFVLENBQUMsQ0FBQztRQU5YLHFCQUFnQixHQUFoQixnQkFBZ0IsQ0FBZ0I7UUFDaEMsY0FBUyxHQUFULFNBQVMsQ0FBUTtRQUNqQixlQUFVLEdBQVYsVUFBVSxDQUFRO0lBS3BDLENBQUM7SUFFTSxZQUFZO1FBQ2pCLE9BQU8sdUJBQXVCLENBQUM7SUFDakMsQ0FBQztJQUVNLGVBQWU7UUFDcEIsT0FBTyxLQUFLLENBQUM7SUFDZixDQUFDO0lBRU0sWUFBWTtRQUNqQixPQUFPO1lBQ0wsZ0JBQWdCLEVBQUUsSUFBSSxDQUFDLGdCQUFnQixDQUFDLFFBQVEsRUFBRTtZQUNsRCxTQUFTLEVBQUUsSUFBSSxDQUFDLFNBQVM7WUFDekIsVUFBVSxFQUFFLElBQUksQ0FBQyxVQUFVO1NBQzVCLENBQUM7SUFDSixDQUFDO0NBQ0Y7QUExQkQsZ0VBMEJDIiwibmFtZXMiOltdLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTHVrYVxcc2VudGluZWxcXGJhY2tlbmRcXHNyY1xcbW9kdWxlc1xcYWlcXGRvbWFpblxcZXZlbnRzXFxhbmFseXNpcy1yZXN1bHQtdXBkYXRlZC5kb21haW4tZXZlbnQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQmFzZURvbWFpbkV2ZW50IH0gZnJvbSAnLi4vLi4vLi4vLi4vc2hhcmVkLWtlcm5lbC9kb21haW4vYmFzZS1kb21haW4tZXZlbnQnO1xyXG5pbXBvcnQgeyBVbmlxdWVFbnRpdHlJZCB9IGZyb20gJy4uLy4uLy4uLy4uL3NoYXJlZC1rZXJuZWwvdmFsdWUtb2JqZWN0cy91bmlxdWUtZW50aXR5LWlkLnZhbHVlLW9iamVjdCc7XHJcblxyXG4vKipcclxuICogQW5hbHlzaXMgUmVzdWx0IFVwZGF0ZWQgRG9tYWluIEV2ZW50XHJcbiAqIFxyXG4gKiBQdWJsaXNoZWQgd2hlbiBhbiBhbmFseXNpcyByZXN1bHQgaXMgdXBkYXRlZC5cclxuICogVGhpcyBldmVudCBjYW4gdHJpZ2dlciB2YXJpb3VzIGRvd25zdHJlYW0gcHJvY2Vzc2VzIHN1Y2ggYXNcclxuICogY2FjaGUgaW52YWxpZGF0aW9uLCBpbmRleCB1cGRhdGVzLCBhbmQgY2hhbmdlIHRyYWNraW5nLlxyXG4gKi9cclxuZXhwb3J0IGNsYXNzIEFuYWx5c2lzUmVzdWx0VXBkYXRlZEV2ZW50IGV4dGVuZHMgQmFzZURvbWFpbkV2ZW50IHtcclxuICBjb25zdHJ1Y3RvcihcclxuICAgIHB1YmxpYyByZWFkb25seSBhbmFseXNpc1Jlc3VsdElkOiBVbmlxdWVFbnRpdHlJZCxcclxuICAgIHB1YmxpYyByZWFkb25seSByZXF1ZXN0SWQ6IHN0cmluZyxcclxuICAgIHB1YmxpYyByZWFkb25seSB1cGRhdGVUeXBlOiBzdHJpbmcsXHJcbiAgICBldmVudElkPzogVW5pcXVlRW50aXR5SWQsXHJcbiAgICBvY2N1cnJlZE9uPzogRGF0ZVxyXG4gICkge1xyXG4gICAgc3VwZXIoZXZlbnRJZCwgb2NjdXJyZWRPbik7XHJcbiAgfVxyXG5cclxuICBwdWJsaWMgZ2V0RXZlbnROYW1lKCk6IHN0cmluZyB7XHJcbiAgICByZXR1cm4gJ0FuYWx5c2lzUmVzdWx0VXBkYXRlZCc7XHJcbiAgfVxyXG5cclxuICBwdWJsaWMgZ2V0RXZlbnRWZXJzaW9uKCk6IHN0cmluZyB7XHJcbiAgICByZXR1cm4gJzEuMCc7XHJcbiAgfVxyXG5cclxuICBwdWJsaWMgZ2V0RXZlbnREYXRhKCk6IFJlY29yZDxzdHJpbmcsIGFueT4ge1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgYW5hbHlzaXNSZXN1bHRJZDogdGhpcy5hbmFseXNpc1Jlc3VsdElkLnRvU3RyaW5nKCksXHJcbiAgICAgIHJlcXVlc3RJZDogdGhpcy5yZXF1ZXN0SWQsXHJcbiAgICAgIHVwZGF0ZVR5cGU6IHRoaXMudXBkYXRlVHlwZSxcclxuICAgIH07XHJcbiAgfVxyXG59Il0sInZlcnNpb24iOjN9