aa5c7b3c8794f3cc38e265b34d3c9583
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ModelPerformanceService_1;
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelPerformanceService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
/**
 * Model Performance Service
 *
 * Tracks and analyzes AI model performance metrics including
 * accuracy, latency, cost, and availability. Provides performance
 * optimization recommendations and trend analysis.
 */
let ModelPerformanceService = ModelPerformanceService_1 = class ModelPerformanceService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(ModelPerformanceService_1.name);
        this.performanceData = new Map();
        this.costData = new Map();
    }
    /**
     * Gets performance metrics for a model
     */
    async getModelMetrics(modelId) {
        const data = this.performanceData.get(modelId);
        if (!data) {
            // Return default metrics for new models
            return {
                accuracy: 0.8,
                precision: 0.8,
                recall: 0.8,
                f1Score: 0.8,
                averageLatency: 2000,
                throughput: 10,
                availability: 0.95,
                totalRequests: 0,
                successfulRequests: 0,
                failedRequests: 0,
                lastUpdated: new Date(),
            };
        }
        return this.calculateMetrics(data);
    }
    /**
     * Gets cost metrics for a model
     */
    async getCostMetrics(modelId) {
        const data = this.costData.get(modelId);
        if (!data) {
            return {
                costPerRequest: 0.01,
                totalCost: 0,
                costTrend: 'stable',
                lastUpdated: new Date(),
            };
        }
        return {
            costPerRequest: data.totalCost / Math.max(data.totalRequests, 1),
            totalCost: data.totalCost,
            costTrend: this.calculateCostTrend(data),
            lastUpdated: data.lastUpdated,
        };
    }
    /**
     * Updates model performance metrics
     */
    async updateModelMetrics(modelId, update) {
        let data = this.performanceData.get(modelId);
        if (!data) {
            data = {
                modelId,
                accuracyHistory: [],
                latencyHistory: [],
                requestHistory: [],
                errorHistory: [],
                createdAt: new Date(),
                lastUpdated: new Date(),
            };
            this.performanceData.set(modelId, data);
        }
        // Update accuracy if provided
        if (update.accuracy !== undefined) {
            data.accuracyHistory.push({
                value: update.accuracy,
                timestamp: new Date(),
            });
            this.trimHistory(data.accuracyHistory);
        }
        // Update latency if provided
        if (update.latency !== undefined) {
            data.latencyHistory.push({
                value: update.latency,
                timestamp: new Date(),
            });
            this.trimHistory(data.latencyHistory);
        }
        // Update user satisfaction if provided
        if (update.userSatisfaction !== undefined) {
            // Store user satisfaction as a special metric
            data.accuracyHistory.push({
                value: update.userSatisfaction / 5, // Convert 1-5 scale to 0-1
                timestamp: new Date(),
                type: 'user_satisfaction',
            });
        }
        data.lastUpdated = new Date();
        this.logger.debug(`Updated performance metrics for model: ${modelId}`);
    }
    /**
     * Records a request completion
     */
    async recordRequest(modelId, request) {
        let data = this.performanceData.get(modelId);
        if (!data) {
            data = {
                modelId,
                accuracyHistory: [],
                latencyHistory: [],
                requestHistory: [],
                errorHistory: [],
                createdAt: new Date(),
                lastUpdated: new Date(),
            };
            this.performanceData.set(modelId, data);
        }
        // Record request
        data.requestHistory.push({
            timestamp: new Date(),
            success: request.success,
            latency: request.latency,
            cost: request.cost,
        });
        this.trimHistory(data.requestHistory);
        // Record latency
        if (request.latency) {
            data.latencyHistory.push({
                value: request.latency,
                timestamp: new Date(),
            });
            this.trimHistory(data.latencyHistory);
        }
        // Record error if request failed
        if (!request.success && request.error) {
            data.errorHistory.push({
                error: request.error,
                timestamp: new Date(),
            });
            this.trimHistory(data.errorHistory);
        }
        // Update cost data
        if (request.cost) {
            await this.updateCostData(modelId, request.cost);
        }
        data.lastUpdated = new Date();
    }
    /**
     * Gets performance trends for a model
     */
    async getPerformanceTrends(modelId) {
        const data = this.performanceData.get(modelId);
        if (!data) {
            return {
                accuracyTrend: 'stable',
                latencyTrend: 'stable',
                errorRateTrend: 'stable',
                throughputTrend: 'stable',
            };
        }
        return {
            accuracyTrend: this.calculateTrend(data.accuracyHistory.map(h => h.value)),
            latencyTrend: this.calculateTrend(data.latencyHistory.map(h => h.value)),
            errorRateTrend: this.calculateErrorRateTrend(data),
            throughputTrend: this.calculateThroughputTrend(data),
        };
    }
    /**
     * Gets performance comparison between models
     */
    async compareModels(modelIds) {
        const comparisons = [];
        for (const modelId of modelIds) {
            const metrics = await this.getModelMetrics(modelId);
            const costMetrics = await this.getCostMetrics(modelId);
            const trends = await this.getPerformanceTrends(modelId);
            comparisons.push({
                modelId,
                metrics,
                costMetrics,
                trends,
                overallScore: this.calculateOverallScore(metrics, costMetrics),
            });
        }
        return comparisons.sort((a, b) => b.overallScore - a.overallScore);
    }
    /**
     * Gets performance alerts for models
     */
    async getPerformanceAlerts() {
        const alerts = [];
        for (const [modelId, data] of this.performanceData) {
            const metrics = this.calculateMetrics(data);
            // Check accuracy degradation
            if (metrics.accuracy < 0.8) {
                alerts.push({
                    modelId,
                    type: 'accuracy_degradation',
                    severity: metrics.accuracy < 0.7 ? 'high' : 'medium',
                    message: `Model accuracy dropped to ${(metrics.accuracy * 100).toFixed(1)}%`,
                    timestamp: new Date(),
                });
            }
            // Check high latency
            if (metrics.averageLatency > 5000) {
                alerts.push({
                    modelId,
                    type: 'high_latency',
                    severity: metrics.averageLatency > 10000 ? 'high' : 'medium',
                    message: `Model latency increased to ${metrics.averageLatency}ms`,
                    timestamp: new Date(),
                });
            }
            // Check high error rate
            const errorRate = metrics.failedRequests / Math.max(metrics.totalRequests, 1);
            if (errorRate > 0.1) {
                alerts.push({
                    modelId,
                    type: 'high_error_rate',
                    severity: errorRate > 0.2 ? 'high' : 'medium',
                    message: `Model error rate increased to ${(errorRate * 100).toFixed(1)}%`,
                    timestamp: new Date(),
                });
            }
        }
        return alerts;
    }
    /**
     * Gets optimization recommendations for a model
     */
    async getOptimizationRecommendations(modelId) {
        const metrics = await this.getModelMetrics(modelId);
        const trends = await this.getPerformanceTrends(modelId);
        const recommendations = [];
        // Accuracy recommendations
        if (metrics.accuracy < 0.85) {
            recommendations.push({
                type: 'accuracy_improvement',
                priority: 'high',
                description: 'Consider retraining the model with more diverse data',
                expectedImpact: 'Improve accuracy by 5-10%',
                effort: 'high',
            });
        }
        // Latency recommendations
        if (metrics.averageLatency > 3000) {
            recommendations.push({
                type: 'latency_optimization',
                priority: 'medium',
                description: 'Optimize model parameters or use model quantization',
                expectedImpact: 'Reduce latency by 20-30%',
                effort: 'medium',
            });
        }
        // Cost recommendations
        const costMetrics = await this.getCostMetrics(modelId);
        if (costMetrics.costPerRequest > 0.02) {
            recommendations.push({
                type: 'cost_optimization',
                priority: 'medium',
                description: 'Consider using a more cost-effective model variant',
                expectedImpact: 'Reduce cost by 30-50%',
                effort: 'low',
            });
        }
        return recommendations;
    }
    // Private helper methods
    calculateMetrics(data) {
        const recentRequests = data.requestHistory.slice(-100); // Last 100 requests
        const recentLatencies = data.latencyHistory.slice(-50); // Last 50 latency measurements
        const recentAccuracies = data.accuracyHistory.slice(-20); // Last 20 accuracy measurements
        const totalRequests = recentRequests.length;
        const successfulRequests = recentRequests.filter(r => r.success).length;
        const failedRequests = totalRequests - successfulRequests;
        const averageLatency = recentLatencies.length > 0
            ? recentLatencies.reduce((sum, l) => sum + l.value, 0) / recentLatencies.length
            : 2000;
        const accuracy = recentAccuracies.length > 0
            ? recentAccuracies.reduce((sum, a) => sum + a.value, 0) / recentAccuracies.length
            : 0.8;
        const throughput = totalRequests > 0
            ? totalRequests / Math.max(1, (Date.now() - data.createdAt.getTime()) / 1000)
            : 0;
        return {
            accuracy,
            precision: accuracy * 0.95, // Approximation
            recall: accuracy * 0.9, // Approximation
            f1Score: accuracy * 0.92, // Approximation
            averageLatency,
            throughput,
            availability: successfulRequests / Math.max(totalRequests, 1),
            totalRequests,
            successfulRequests,
            failedRequests,
            lastUpdated: data.lastUpdated,
        };
    }
    async updateCostData(modelId, cost) {
        let data = this.costData.get(modelId);
        if (!data) {
            data = {
                modelId,
                totalCost: 0,
                totalRequests: 0,
                costHistory: [],
                createdAt: new Date(),
                lastUpdated: new Date(),
            };
            this.costData.set(modelId, data);
        }
        data.totalCost += cost;
        data.totalRequests++;
        data.costHistory.push({
            cost,
            timestamp: new Date(),
        });
        this.trimHistory(data.costHistory);
        data.lastUpdated = new Date();
    }
    calculateCostTrend(data) {
        if (data.costHistory.length < 10) {
            return 'stable';
        }
        const recent = data.costHistory.slice(-10);
        const older = data.costHistory.slice(-20, -10);
        const recentAvg = recent.reduce((sum, c) => sum + c.cost, 0) / recent.length;
        const olderAvg = older.reduce((sum, c) => sum + c.cost, 0) / older.length;
        const change = (recentAvg - olderAvg) / olderAvg;
        if (change > 0.1)
            return 'increasing';
        if (change < -0.1)
            return 'decreasing';
        return 'stable';
    }
    calculateTrend(values) {
        if (values.length < 5) {
            return 'stable';
        }
        const recent = values.slice(-5);
        const older = values.slice(-10, -5);
        if (older.length === 0) {
            return 'stable';
        }
        const recentAvg = recent.reduce((sum, v) => sum + v, 0) / recent.length;
        const olderAvg = older.reduce((sum, v) => sum + v, 0) / older.length;
        const change = (recentAvg - olderAvg) / olderAvg;
        if (change > 0.05)
            return 'improving';
        if (change < -0.05)
            return 'degrading';
        return 'stable';
    }
    calculateErrorRateTrend(data) {
        const recentRequests = data.requestHistory.slice(-20);
        const olderRequests = data.requestHistory.slice(-40, -20);
        if (recentRequests.length === 0 || olderRequests.length === 0) {
            return 'stable';
        }
        const recentErrorRate = recentRequests.filter(r => !r.success).length / recentRequests.length;
        const olderErrorRate = olderRequests.filter(r => !r.success).length / olderRequests.length;
        const change = recentErrorRate - olderErrorRate;
        if (change > 0.05)
            return 'degrading';
        if (change < -0.05)
            return 'improving';
        return 'stable';
    }
    calculateThroughputTrend(data) {
        const now = Date.now();
        const oneHourAgo = now - 3600000;
        const twoHoursAgo = now - 7200000;
        const recentRequests = data.requestHistory.filter(r => r.timestamp.getTime() > oneHourAgo).length;
        const olderRequests = data.requestHistory.filter(r => r.timestamp.getTime() > twoHoursAgo && r.timestamp.getTime() <= oneHourAgo).length;
        if (olderRequests === 0) {
            return 'stable';
        }
        const change = (recentRequests - olderRequests) / olderRequests;
        if (change > 0.2)
            return 'improving';
        if (change < -0.2)
            return 'degrading';
        return 'stable';
    }
    calculateOverallScore(metrics, costMetrics) {
        const accuracyScore = metrics.accuracy;
        const latencyScore = Math.max(0, 1 - (metrics.averageLatency / 10000));
        const availabilityScore = metrics.availability;
        const costScore = Math.max(0, 1 - (costMetrics.costPerRequest / 0.1));
        return (accuracyScore * 0.3 +
            latencyScore * 0.25 +
            availabilityScore * 0.25 +
            costScore * 0.2);
    }
    trimHistory(history, maxLength = 1000) {
        if (history.length > maxLength) {
            history.splice(0, history.length - maxLength);
        }
    }
};
exports.ModelPerformanceService = ModelPerformanceService;
exports.ModelPerformanceService = ModelPerformanceService = ModelPerformanceService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], ModelPerformanceService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxtb2R1bGVzXFxhaVxcYXBwbGljYXRpb25cXHNlcnZpY2VzXFxvcHRpbWl6YXRpb25cXG1vZGVsLXBlcmZvcm1hbmNlLnNlcnZpY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBQSwyQ0FBb0Q7QUFDcEQsMkNBQStDO0FBRS9DOzs7Ozs7R0FNRztBQUVJLElBQU0sdUJBQXVCLCtCQUE3QixNQUFNLHVCQUF1QjtJQUtsQyxZQUE2QixhQUE0QjtRQUE1QixrQkFBYSxHQUFiLGFBQWEsQ0FBZTtRQUp4QyxXQUFNLEdBQUcsSUFBSSxlQUFNLENBQUMseUJBQXVCLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDbEQsb0JBQWUsR0FBRyxJQUFJLEdBQUcsRUFBZ0MsQ0FBQztRQUMxRCxhQUFRLEdBQUcsSUFBSSxHQUFHLEVBQXlCLENBQUM7SUFFRCxDQUFDO0lBRTdEOztPQUVHO0lBQ0gsS0FBSyxDQUFDLGVBQWUsQ0FBQyxPQUFlO1FBQ25DLE1BQU0sSUFBSSxHQUFHLElBQUksQ0FBQyxlQUFlLENBQUMsR0FBRyxDQUFDLE9BQU8sQ0FBQyxDQUFDO1FBRS9DLElBQUksQ0FBQyxJQUFJLEVBQUUsQ0FBQztZQUNWLHdDQUF3QztZQUN4QyxPQUFPO2dCQUNMLFFBQVEsRUFBRSxHQUFHO2dCQUNiLFNBQVMsRUFBRSxHQUFHO2dCQUNkLE1BQU0sRUFBRSxHQUFHO2dCQUNYLE9BQU8sRUFBRSxHQUFHO2dCQUNaLGNBQWMsRUFBRSxJQUFJO2dCQUNwQixVQUFVLEVBQUUsRUFBRTtnQkFDZCxZQUFZLEVBQUUsSUFBSTtnQkFDbEIsYUFBYSxFQUFFLENBQUM7Z0JBQ2hCLGtCQUFrQixFQUFFLENBQUM7Z0JBQ3JCLGNBQWMsRUFBRSxDQUFDO2dCQUNqQixXQUFXLEVBQUUsSUFBSSxJQUFJLEVBQUU7YUFDeEIsQ0FBQztRQUNKLENBQUM7UUFFRCxPQUFPLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsQ0FBQztJQUNyQyxDQUFDO0lBRUQ7O09BRUc7SUFDSCxLQUFLLENBQUMsY0FBYyxDQUFDLE9BQWU7UUFDbEMsTUFBTSxJQUFJLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxHQUFHLENBQUMsT0FBTyxDQUFDLENBQUM7UUFFeEMsSUFBSSxDQUFDLElBQUksRUFBRSxDQUFDO1lBQ1YsT0FBTztnQkFDTCxjQUFjLEVBQUUsSUFBSTtnQkFDcEIsU0FBUyxFQUFFLENBQUM7Z0JBQ1osU0FBUyxFQUFFLFFBQVE7Z0JBQ25CLFdBQVcsRUFBRSxJQUFJLElBQUksRUFBRTthQUN4QixDQUFDO1FBQ0osQ0FBQztRQUVELE9BQU87WUFDTCxjQUFjLEVBQUUsSUFBSSxDQUFDLFNBQVMsR0FBRyxJQUFJLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxhQUFhLEVBQUUsQ0FBQyxDQUFDO1lBQ2hFLFNBQVMsRUFBRSxJQUFJLENBQUMsU0FBUztZQUN6QixTQUFTLEVBQUUsSUFBSSxDQUFDLGtCQUFrQixDQUFDLElBQUksQ0FBQztZQUN4QyxXQUFXLEVBQUUsSUFBSSxDQUFDLFdBQVc7U0FDOUIsQ0FBQztJQUNKLENBQUM7SUFFRDs7T0FFRztJQUNILEtBQUssQ0FBQyxrQkFBa0IsQ0FDdEIsT0FBZSxFQUNmLE1BQTBCO1FBRTFCLElBQUksSUFBSSxHQUFHLElBQUksQ0FBQyxlQUFlLENBQUMsR0FBRyxDQUFDLE9BQU8sQ0FBQyxDQUFDO1FBRTdDLElBQUksQ0FBQyxJQUFJLEVBQUUsQ0FBQztZQUNWLElBQUksR0FBRztnQkFDTCxPQUFPO2dCQUNQLGVBQWUsRUFBRSxFQUFFO2dCQUNuQixjQUFjLEVBQUUsRUFBRTtnQkFDbEIsY0FBYyxFQUFFLEVBQUU7Z0JBQ2xCLFlBQVksRUFBRSxFQUFFO2dCQUNoQixTQUFTLEVBQUUsSUFBSSxJQUFJLEVBQUU7Z0JBQ3JCLFdBQVcsRUFBRSxJQUFJLElBQUksRUFBRTthQUN4QixDQUFDO1lBQ0YsSUFBSSxDQUFDLGVBQWUsQ0FBQyxHQUFHLENBQUMsT0FBTyxFQUFFLElBQUksQ0FBQyxDQUFDO1FBQzFDLENBQUM7UUFFRCw4QkFBOEI7UUFDOUIsSUFBSSxNQUFNLENBQUMsUUFBUSxLQUFLLFNBQVMsRUFBRSxDQUFDO1lBQ2xDLElBQUksQ0FBQyxlQUFlLENBQUMsSUFBSSxDQUFDO2dCQUN4QixLQUFLLEVBQUUsTUFBTSxDQUFDLFFBQVE7Z0JBQ3RCLFNBQVMsRUFBRSxJQUFJLElBQUksRUFBRTthQUN0QixDQUFDLENBQUM7WUFDSCxJQUFJLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQztRQUN6QyxDQUFDO1FBRUQsNkJBQTZCO1FBQzdCLElBQUksTUFBTSxDQUFDLE9BQU8sS0FBSyxTQUFTLEVBQUUsQ0FBQztZQUNqQyxJQUFJLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQztnQkFDdkIsS0FBSyxFQUFFLE1BQU0sQ0FBQyxPQUFPO2dCQUNyQixTQUFTLEVBQUUsSUFBSSxJQUFJLEVBQUU7YUFDdEIsQ0FBQyxDQUFDO1lBQ0gsSUFBSSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsY0FBYyxDQUFDLENBQUM7UUFDeEMsQ0FBQztRQUVELHVDQUF1QztRQUN2QyxJQUFJLE1BQU0sQ0FBQyxnQkFBZ0IsS0FBSyxTQUFTLEVBQUUsQ0FBQztZQUMxQyw4Q0FBOEM7WUFDOUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxJQUFJLENBQUM7Z0JBQ3hCLEtBQUssRUFBRSxNQUFNLENBQUMsZ0JBQWdCLEdBQUcsQ0FBQyxFQUFFLDJCQUEyQjtnQkFDL0QsU0FBUyxFQUFFLElBQUksSUFBSSxFQUFFO2dCQUNyQixJQUFJLEVBQUUsbUJBQW1CO2FBQzFCLENBQUMsQ0FBQztRQUNMLENBQUM7UUFFRCxJQUFJLENBQUMsV0FBVyxHQUFHLElBQUksSUFBSSxFQUFFLENBQUM7UUFFOUIsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsMENBQTBDLE9BQU8sRUFBRSxDQUFDLENBQUM7SUFDekUsQ0FBQztJQUVEOztPQUVHO0lBQ0gsS0FBSyxDQUFDLGFBQWEsQ0FDakIsT0FBZSxFQUNmLE9BQXNCO1FBRXRCLElBQUksSUFBSSxHQUFHLElBQUksQ0FBQyxlQUFlLENBQUMsR0FBRyxDQUFDLE9BQU8sQ0FBQyxDQUFDO1FBRTdDLElBQUksQ0FBQyxJQUFJLEVBQUUsQ0FBQztZQUNWLElBQUksR0FBRztnQkFDTCxPQUFPO2dCQUNQLGVBQWUsRUFBRSxFQUFFO2dCQUNuQixjQUFjLEVBQUUsRUFBRTtnQkFDbEIsY0FBYyxFQUFFLEVBQUU7Z0JBQ2xCLFlBQVksRUFBRSxFQUFFO2dCQUNoQixTQUFTLEVBQUUsSUFBSSxJQUFJLEVBQUU7Z0JBQ3JCLFdBQVcsRUFBRSxJQUFJLElBQUksRUFBRTthQUN4QixDQUFDO1lBQ0YsSUFBSSxDQUFDLGVBQWUsQ0FBQyxHQUFHLENBQUMsT0FBTyxFQUFFLElBQUksQ0FBQyxDQUFDO1FBQzFDLENBQUM7UUFFRCxpQkFBaUI7UUFDakIsSUFBSSxDQUFDLGNBQWMsQ0FBQyxJQUFJLENBQUM7WUFDdkIsU0FBUyxFQUFFLElBQUksSUFBSSxFQUFFO1lBQ3JCLE9BQU8sRUFBRSxPQUFPLENBQUMsT0FBTztZQUN4QixPQUFPLEVBQUUsT0FBTyxDQUFDLE9BQU87WUFDeEIsSUFBSSxFQUFFLE9BQU8sQ0FBQyxJQUFJO1NBQ25CLENBQUMsQ0FBQztRQUNILElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDO1FBRXRDLGlCQUFpQjtRQUNqQixJQUFJLE9BQU8sQ0FBQyxPQUFPLEVBQUUsQ0FBQztZQUNwQixJQUFJLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQztnQkFDdkIsS0FBSyxFQUFFLE9BQU8sQ0FBQyxPQUFPO2dCQUN0QixTQUFTLEVBQUUsSUFBSSxJQUFJLEVBQUU7YUFDdEIsQ0FBQyxDQUFDO1lBQ0gsSUFBSSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsY0FBYyxDQUFDLENBQUM7UUFDeEMsQ0FBQztRQUVELGlDQUFpQztRQUNqQyxJQUFJLENBQUMsT0FBTyxDQUFDLE9BQU8sSUFBSSxPQUFPLENBQUMsS0FBSyxFQUFFLENBQUM7WUFDdEMsSUFBSSxDQUFDLFlBQVksQ0FBQyxJQUFJLENBQUM7Z0JBQ3JCLEtBQUssRUFBRSxPQUFPLENBQUMsS0FBSztnQkFDcEIsU0FBUyxFQUFFLElBQUksSUFBSSxFQUFFO2FBQ3RCLENBQUMsQ0FBQztZQUNILElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDO1FBQ3RDLENBQUM7UUFFRCxtQkFBbUI7UUFDbkIsSUFBSSxPQUFPLENBQUMsSUFBSSxFQUFFLENBQUM7WUFDakIsTUFBTSxJQUFJLENBQUMsY0FBYyxDQUFDLE9BQU8sRUFBRSxPQUFPLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDbkQsQ0FBQztRQUVELElBQUksQ0FBQyxXQUFXLEdBQUcsSUFBSSxJQUFJLEVBQUUsQ0FBQztJQUNoQyxDQUFDO0lBRUQ7O09BRUc7SUFDSCxLQUFLLENBQUMsb0JBQW9CLENBQUMsT0FBZTtRQUN4QyxNQUFNLElBQUksR0FBRyxJQUFJLENBQUMsZUFBZSxDQUFDLEdBQUcsQ0FBQyxPQUFPLENBQUMsQ0FBQztRQUUvQyxJQUFJLENBQUMsSUFBSSxFQUFFLENBQUM7WUFDVixPQUFPO2dCQUNMLGFBQWEsRUFBRSxRQUFRO2dCQUN2QixZQUFZLEVBQUUsUUFBUTtnQkFDdEIsY0FBYyxFQUFFLFFBQVE7Z0JBQ3hCLGVBQWUsRUFBRSxRQUFRO2FBQzFCLENBQUM7UUFDSixDQUFDO1FBRUQsT0FBTztZQUNMLGFBQWEsRUFBRSxJQUFJLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQzFFLFlBQVksRUFBRSxJQUFJLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxjQUFjLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFDO1lBQ3hFLGNBQWMsRUFBRSxJQUFJLENBQUMsdUJBQXVCLENBQUMsSUFBSSxDQUFDO1lBQ2xELGVBQWUsRUFBRSxJQUFJLENBQUMsd0JBQXdCLENBQUMsSUFBSSxDQUFDO1NBQ3JELENBQUM7SUFDSixDQUFDO0lBRUQ7O09BRUc7SUFDSCxLQUFLLENBQUMsYUFBYSxDQUFDLFFBQWtCO1FBQ3BDLE1BQU0sV0FBVyxHQUFzQixFQUFFLENBQUM7UUFFMUMsS0FBSyxNQUFNLE9BQU8sSUFBSSxRQUFRLEVBQUUsQ0FBQztZQUMvQixNQUFNLE9BQU8sR0FBRyxNQUFNLElBQUksQ0FBQyxlQUFlLENBQUMsT0FBTyxDQUFDLENBQUM7WUFDcEQsTUFBTSxXQUFXLEdBQUcsTUFBTSxJQUFJLENBQUMsY0FBYyxDQUFDLE9BQU8sQ0FBQyxDQUFDO1lBQ3ZELE1BQU0sTUFBTSxHQUFHLE1BQU0sSUFBSSxDQUFDLG9CQUFvQixDQUFDLE9BQU8sQ0FBQyxDQUFDO1lBRXhELFdBQVcsQ0FBQyxJQUFJLENBQUM7Z0JBQ2YsT0FBTztnQkFDUCxPQUFPO2dCQUNQLFdBQVc7Z0JBQ1gsTUFBTTtnQkFDTixZQUFZLEVBQUUsSUFBSSxDQUFDLHFCQUFxQixDQUFDLE9BQU8sRUFBRSxXQUFXLENBQUM7YUFDL0QsQ0FBQyxDQUFDO1FBQ0wsQ0FBQztRQUVELE9BQU8sV0FBVyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFDLENBQUMsQ0FBQyxZQUFZLEdBQUcsQ0FBQyxDQUFDLFlBQVksQ0FBQyxDQUFDO0lBQ3JFLENBQUM7SUFFRDs7T0FFRztJQUNILEtBQUssQ0FBQyxvQkFBb0I7UUFDeEIsTUFBTSxNQUFNLEdBQXVCLEVBQUUsQ0FBQztRQUV0QyxLQUFLLE1BQU0sQ0FBQyxPQUFPLEVBQUUsSUFBSSxDQUFDLElBQUksSUFBSSxDQUFDLGVBQWUsRUFBRSxDQUFDO1lBQ25ELE1BQU0sT0FBTyxHQUFHLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUU1Qyw2QkFBNkI7WUFDN0IsSUFBSSxPQUFPLENBQUMsUUFBUSxHQUFHLEdBQUcsRUFBRSxDQUFDO2dCQUMzQixNQUFNLENBQUMsSUFBSSxDQUFDO29CQUNWLE9BQU87b0JBQ1AsSUFBSSxFQUFFLHNCQUFzQjtvQkFDNUIsUUFBUSxFQUFFLE9BQU8sQ0FBQyxRQUFRLEdBQUcsR0FBRyxDQUFDLENBQUMsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLFFBQVE7b0JBQ3BELE9BQU8sRUFBRSw2QkFBNkIsQ0FBQyxPQUFPLENBQUMsUUFBUSxHQUFHLEdBQUcsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsR0FBRztvQkFDNUUsU0FBUyxFQUFFLElBQUksSUFBSSxFQUFFO2lCQUN0QixDQUFDLENBQUM7WUFDTCxDQUFDO1lBRUQscUJBQXFCO1lBQ3JCLElBQUksT0FBTyxDQUFDLGNBQWMsR0FBRyxJQUFJLEVBQUUsQ0FBQztnQkFDbEMsTUFBTSxDQUFDLElBQUksQ0FBQztvQkFDVixPQUFPO29CQUNQLElBQUksRUFBRSxjQUFjO29CQUNwQixRQUFRLEVBQUUsT0FBTyxDQUFDLGNBQWMsR0FBRyxLQUFLLENBQUMsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsUUFBUTtvQkFDNUQsT0FBTyxFQUFFLDhCQUE4QixPQUFPLENBQUMsY0FBYyxJQUFJO29CQUNqRSxTQUFTLEVBQUUsSUFBSSxJQUFJLEVBQUU7aUJBQ3RCLENBQUMsQ0FBQztZQUNMLENBQUM7WUFFRCx3QkFBd0I7WUFDeEIsTUFBTSxTQUFTLEdBQUcsT0FBTyxDQUFDLGNBQWMsR0FBRyxJQUFJLENBQUMsR0FBRyxDQUFDLE9BQU8sQ0FBQyxhQUFhLEVBQUUsQ0FBQyxDQUFDLENBQUM7WUFDOUUsSUFBSSxTQUFTLEdBQUcsR0FBRyxFQUFFLENBQUM7Z0JBQ3BCLE1BQU0sQ0FBQyxJQUFJLENBQUM7b0JBQ1YsT0FBTztvQkFDUCxJQUFJLEVBQUUsaUJBQWlCO29CQUN2QixRQUFRLEVBQUUsU0FBUyxHQUFHLEdBQUcsQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxRQUFRO29CQUM3QyxPQUFPLEVBQUUsaUNBQWlDLENBQUMsU0FBUyxHQUFHLEdBQUcsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsR0FBRztvQkFDekUsU0FBUyxFQUFFLElBQUksSUFBSSxFQUFFO2lCQUN0QixDQUFDLENBQUM7WUFDTCxDQUFDO1FBQ0gsQ0FBQztRQUVELE9BQU8sTUFBTSxDQUFDO0lBQ2hCLENBQUM7SUFFRDs7T0FFRztJQUNILEtBQUssQ0FBQyw4QkFBOEIsQ0FBQyxPQUFlO1FBQ2xELE1BQU0sT0FBTyxHQUFHLE1BQU0sSUFBSSxDQUFDLGVBQWUsQ0FBQyxPQUFPLENBQUMsQ0FBQztRQUNwRCxNQUFNLE1BQU0sR0FBRyxNQUFNLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxPQUFPLENBQUMsQ0FBQztRQUN4RCxNQUFNLGVBQWUsR0FBaUMsRUFBRSxDQUFDO1FBRXpELDJCQUEyQjtRQUMzQixJQUFJLE9BQU8sQ0FBQyxRQUFRLEdBQUcsSUFBSSxFQUFFLENBQUM7WUFDNUIsZUFBZSxDQUFDLElBQUksQ0FBQztnQkFDbkIsSUFBSSxFQUFFLHNCQUFzQjtnQkFDNUIsUUFBUSxFQUFFLE1BQU07Z0JBQ2hCLFdBQVcsRUFBRSxzREFBc0Q7Z0JBQ25FLGNBQWMsRUFBRSwyQkFBMkI7Z0JBQzNDLE1BQU0sRUFBRSxNQUFNO2FBQ2YsQ0FBQyxDQUFDO1FBQ0wsQ0FBQztRQUVELDBCQUEwQjtRQUMxQixJQUFJLE9BQU8sQ0FBQyxjQUFjLEdBQUcsSUFBSSxFQUFFLENBQUM7WUFDbEMsZUFBZSxDQUFDLElBQUksQ0FBQztnQkFDbkIsSUFBSSxFQUFFLHNCQUFzQjtnQkFDNUIsUUFBUSxFQUFFLFFBQVE7Z0JBQ2xCLFdBQVcsRUFBRSxxREFBcUQ7Z0JBQ2xFLGNBQWMsRUFBRSwwQkFBMEI7Z0JBQzFDLE1BQU0sRUFBRSxRQUFRO2FBQ2pCLENBQUMsQ0FBQztRQUNMLENBQUM7UUFFRCx1QkFBdUI7UUFDdkIsTUFBTSxXQUFXLEdBQUcsTUFBTSxJQUFJLENBQUMsY0FBYyxDQUFDLE9BQU8sQ0FBQyxDQUFDO1FBQ3ZELElBQUksV0FBVyxDQUFDLGNBQWMsR0FBRyxJQUFJLEVBQUUsQ0FBQztZQUN0QyxlQUFlLENBQUMsSUFBSSxDQUFDO2dCQUNuQixJQUFJLEVBQUUsbUJBQW1CO2dCQUN6QixRQUFRLEVBQUUsUUFBUTtnQkFDbEIsV0FBVyxFQUFFLG9EQUFvRDtnQkFDakUsY0FBYyxFQUFFLHVCQUF1QjtnQkFDdkMsTUFBTSxFQUFFLEtBQUs7YUFDZCxDQUFDLENBQUM7UUFDTCxDQUFDO1FBRUQsT0FBTyxlQUFlLENBQUM7SUFDekIsQ0FBQztJQUVELHlCQUF5QjtJQUVqQixnQkFBZ0IsQ0FBQyxJQUEwQjtRQUNqRCxNQUFNLGNBQWMsR0FBRyxJQUFJLENBQUMsY0FBYyxDQUFDLEtBQUssQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsb0JBQW9CO1FBQzVFLE1BQU0sZUFBZSxHQUFHLElBQUksQ0FBQyxjQUFjLENBQUMsS0FBSyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQywrQkFBK0I7UUFDdkYsTUFBTSxnQkFBZ0IsR0FBRyxJQUFJLENBQUMsZUFBZSxDQUFDLEtBQUssQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsZ0NBQWdDO1FBRTFGLE1BQU0sYUFBYSxHQUFHLGNBQWMsQ0FBQyxNQUFNLENBQUM7UUFDNUMsTUFBTSxrQkFBa0IsR0FBRyxjQUFjLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxDQUFDLE1BQU0sQ0FBQztRQUN4RSxNQUFNLGNBQWMsR0FBRyxhQUFhLEdBQUcsa0JBQWtCLENBQUM7UUFFMUQsTUFBTSxjQUFjLEdBQUcsZUFBZSxDQUFDLE1BQU0sR0FBRyxDQUFDO1lBQy9DLENBQUMsQ0FBQyxlQUFlLENBQUMsTUFBTSxDQUFDLENBQUMsR0FBRyxFQUFFLENBQUMsRUFBRSxFQUFFLENBQUMsR0FBRyxHQUFHLENBQUMsQ0FBQyxLQUFLLEVBQUUsQ0FBQyxDQUFDLEdBQUcsZUFBZSxDQUFDLE1BQU07WUFDL0UsQ0FBQyxDQUFDLElBQUksQ0FBQztRQUVULE1BQU0sUUFBUSxHQUFHLGdCQUFnQixDQUFDLE1BQU0sR0FBRyxDQUFDO1lBQzFDLENBQUMsQ0FBQyxnQkFBZ0IsQ0FBQyxNQUFNLENBQUMsQ0FBQyxHQUFHLEVBQUUsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxHQUFHLEdBQUcsQ0FBQyxDQUFDLEtBQUssRUFBRSxDQUFDLENBQUMsR0FBRyxnQkFBZ0IsQ0FBQyxNQUFNO1lBQ2pGLENBQUMsQ0FBQyxHQUFHLENBQUM7UUFFUixNQUFNLFVBQVUsR0FBRyxhQUFhLEdBQUcsQ0FBQztZQUNsQyxDQUFDLENBQUMsYUFBYSxHQUFHLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLEdBQUcsRUFBRSxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsT0FBTyxFQUFFLENBQUMsR0FBRyxJQUFJLENBQUM7WUFDN0UsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUVOLE9BQU87WUFDTCxRQUFRO1lBQ1IsU0FBUyxFQUFFLFFBQVEsR0FBRyxJQUFJLEVBQUUsZ0JBQWdCO1lBQzVDLE1BQU0sRUFBRSxRQUFRLEdBQUcsR0FBRyxFQUFFLGdCQUFnQjtZQUN4QyxPQUFPLEVBQUUsUUFBUSxHQUFHLElBQUksRUFBRSxnQkFBZ0I7WUFDMUMsY0FBYztZQUNkLFVBQVU7WUFDVixZQUFZLEVBQUUsa0JBQWtCLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxhQUFhLEVBQUUsQ0FBQyxDQUFDO1lBQzdELGFBQWE7WUFDYixrQkFBa0I7WUFDbEIsY0FBYztZQUNkLFdBQVcsRUFBRSxJQUFJLENBQUMsV0FBVztTQUM5QixDQUFDO0lBQ0osQ0FBQztJQUVPLEtBQUssQ0FBQyxjQUFjLENBQUMsT0FBZSxFQUFFLElBQVk7UUFDeEQsSUFBSSxJQUFJLEdBQUcsSUFBSSxDQUFDLFFBQVEsQ0FBQyxHQUFHLENBQUMsT0FBTyxDQUFDLENBQUM7UUFFdEMsSUFBSSxDQUFDLElBQUksRUFBRSxDQUFDO1lBQ1YsSUFBSSxHQUFHO2dCQUNMLE9BQU87Z0JBQ1AsU0FBUyxFQUFFLENBQUM7Z0JBQ1osYUFBYSxFQUFFLENBQUM7Z0JBQ2hCLFdBQVcsRUFBRSxFQUFFO2dCQUNmLFNBQVMsRUFBRSxJQUFJLElBQUksRUFBRTtnQkFDckIsV0FBVyxFQUFFLElBQUksSUFBSSxFQUFFO2FBQ3hCLENBQUM7WUFDRixJQUFJLENBQUMsUUFBUSxDQUFDLEdBQUcsQ0FBQyxPQUFPLEVBQUUsSUFBSSxDQUFDLENBQUM7UUFDbkMsQ0FBQztRQUVELElBQUksQ0FBQyxTQUFTLElBQUksSUFBSSxDQUFDO1FBQ3ZCLElBQUksQ0FBQyxhQUFhLEVBQUUsQ0FBQztRQUNyQixJQUFJLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQztZQUNwQixJQUFJO1lBQ0osU0FBUyxFQUFFLElBQUksSUFBSSxFQUFFO1NBQ3RCLENBQUMsQ0FBQztRQUVILElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLFdBQVcsQ0FBQyxDQUFDO1FBQ25DLElBQUksQ0FBQyxXQUFXLEdBQUcsSUFBSSxJQUFJLEVBQUUsQ0FBQztJQUNoQyxDQUFDO0lBRU8sa0JBQWtCLENBQUMsSUFBbUI7UUFDNUMsSUFBSSxJQUFJLENBQUMsV0FBVyxDQUFDLE1BQU0sR0FBRyxFQUFFLEVBQUUsQ0FBQztZQUNqQyxPQUFPLFFBQVEsQ0FBQztRQUNsQixDQUFDO1FBRUQsTUFBTSxNQUFNLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQyxLQUFLLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQztRQUMzQyxNQUFNLEtBQUssR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDLEtBQUssQ0FBQyxDQUFDLEVBQUUsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDO1FBRS9DLE1BQU0sU0FBUyxHQUFHLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxHQUFHLEVBQUUsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxHQUFHLEdBQUcsQ0FBQyxDQUFDLElBQUksRUFBRSxDQUFDLENBQUMsR0FBRyxNQUFNLENBQUMsTUFBTSxDQUFDO1FBQzdFLE1BQU0sUUFBUSxHQUFHLEtBQUssQ0FBQyxNQUFNLENBQUMsQ0FBQyxHQUFHLEVBQUUsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxHQUFHLEdBQUcsQ0FBQyxDQUFDLElBQUksRUFBRSxDQUFDLENBQUMsR0FBRyxLQUFLLENBQUMsTUFBTSxDQUFDO1FBRTFFLE1BQU0sTUFBTSxHQUFHLENBQUMsU0FBUyxHQUFHLFFBQVEsQ0FBQyxHQUFHLFFBQVEsQ0FBQztRQUVqRCxJQUFJLE1BQU0sR0FBRyxHQUFHO1lBQUUsT0FBTyxZQUFZLENBQUM7UUFDdEMsSUFBSSxNQUFNLEdBQUcsQ0FBQyxHQUFHO1lBQUUsT0FBTyxZQUFZLENBQUM7UUFDdkMsT0FBTyxRQUFRLENBQUM7SUFDbEIsQ0FBQztJQUVPLGNBQWMsQ0FBQyxNQUFnQjtRQUNyQyxJQUFJLE1BQU0sQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUM7WUFDdEIsT0FBTyxRQUFRLENBQUM7UUFDbEIsQ0FBQztRQUVELE1BQU0sTUFBTSxHQUFHLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUNoQyxNQUFNLEtBQUssR0FBRyxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUMsRUFBRSxFQUFFLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFFcEMsSUFBSSxLQUFLLENBQUMsTUFBTSxLQUFLLENBQUMsRUFBRSxDQUFDO1lBQ3ZCLE9BQU8sUUFBUSxDQUFDO1FBQ2xCLENBQUM7UUFFRCxNQUFNLFNBQVMsR0FBRyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsR0FBRyxFQUFFLENBQUMsRUFBRSxFQUFFLENBQUMsR0FBRyxHQUFHLENBQUMsRUFBRSxDQUFDLENBQUMsR0FBRyxNQUFNLENBQUMsTUFBTSxDQUFDO1FBQ3hFLE1BQU0sUUFBUSxHQUFHLEtBQUssQ0FBQyxNQUFNLENBQUMsQ0FBQyxHQUFHLEVBQUUsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxHQUFHLEdBQUcsQ0FBQyxFQUFFLENBQUMsQ0FBQyxHQUFHLEtBQUssQ0FBQyxNQUFNLENBQUM7UUFFckUsTUFBTSxNQUFNLEdBQUcsQ0FBQyxTQUFTLEdBQUcsUUFBUSxDQUFDLEdBQUcsUUFBUSxDQUFDO1FBRWpELElBQUksTUFBTSxHQUFHLElBQUk7WUFBRSxPQUFPLFdBQVcsQ0FBQztRQUN0QyxJQUFJLE1BQU0sR0FBRyxDQUFDLElBQUk7WUFBRSxPQUFPLFdBQVcsQ0FBQztRQUN2QyxPQUFPLFFBQVEsQ0FBQztJQUNsQixDQUFDO0lBRU8sdUJBQXVCLENBQUMsSUFBMEI7UUFDeEQsTUFBTSxjQUFjLEdBQUcsSUFBSSxDQUFDLGNBQWMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQztRQUN0RCxNQUFNLGFBQWEsR0FBRyxJQUFJLENBQUMsY0FBYyxDQUFDLEtBQUssQ0FBQyxDQUFDLEVBQUUsRUFBRSxDQUFDLEVBQUUsQ0FBQyxDQUFDO1FBRTFELElBQUksY0FBYyxDQUFDLE1BQU0sS0FBSyxDQUFDLElBQUksYUFBYSxDQUFDLE1BQU0sS0FBSyxDQUFDLEVBQUUsQ0FBQztZQUM5RCxPQUFPLFFBQVEsQ0FBQztRQUNsQixDQUFDO1FBRUQsTUFBTSxlQUFlLEdBQUcsY0FBYyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxDQUFDLE1BQU0sR0FBRyxjQUFjLENBQUMsTUFBTSxDQUFDO1FBQzlGLE1BQU0sY0FBYyxHQUFHLGFBQWEsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxNQUFNLEdBQUcsYUFBYSxDQUFDLE1BQU0sQ0FBQztRQUUzRixNQUFNLE1BQU0sR0FBRyxlQUFlLEdBQUcsY0FBYyxDQUFDO1FBRWhELElBQUksTUFBTSxHQUFHLElBQUk7WUFBRSxPQUFPLFdBQVcsQ0FBQztRQUN0QyxJQUFJLE1BQU0sR0FBRyxDQUFDLElBQUk7WUFBRSxPQUFPLFdBQVcsQ0FBQztRQUN2QyxPQUFPLFFBQVEsQ0FBQztJQUNsQixDQUFDO0lBRU8sd0JBQXdCLENBQUMsSUFBMEI7UUFDekQsTUFBTSxHQUFHLEdBQUcsSUFBSSxDQUFDLEdBQUcsRUFBRSxDQUFDO1FBQ3ZCLE1BQU0sVUFBVSxHQUFHLEdBQUcsR0FBRyxPQUFPLENBQUM7UUFDakMsTUFBTSxXQUFXLEdBQUcsR0FBRyxHQUFHLE9BQU8sQ0FBQztRQUVsQyxNQUFNLGNBQWMsR0FBRyxJQUFJLENBQUMsY0FBYyxDQUFDLE1BQU0sQ0FDL0MsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLE9BQU8sRUFBRSxHQUFHLFVBQVUsQ0FDeEMsQ0FBQyxNQUFNLENBQUM7UUFFVCxNQUFNLGFBQWEsR0FBRyxJQUFJLENBQUMsY0FBYyxDQUFDLE1BQU0sQ0FDOUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUFDLE9BQU8sRUFBRSxHQUFHLFdBQVcsSUFBSSxDQUFDLENBQUMsU0FBUyxDQUFDLE9BQU8sRUFBRSxJQUFJLFVBQVUsQ0FDaEYsQ0FBQyxNQUFNLENBQUM7UUFFVCxJQUFJLGFBQWEsS0FBSyxDQUFDLEVBQUUsQ0FBQztZQUN4QixPQUFPLFFBQVEsQ0FBQztRQUNsQixDQUFDO1FBRUQsTUFBTSxNQUFNLEdBQUcsQ0FBQyxjQUFjLEdBQUcsYUFBYSxDQUFDLEdBQUcsYUFBYSxDQUFDO1FBRWhFLElBQUksTUFBTSxHQUFHLEdBQUc7WUFBRSxPQUFPLFdBQVcsQ0FBQztRQUNyQyxJQUFJLE1BQU0sR0FBRyxDQUFDLEdBQUc7WUFBRSxPQUFPLFdBQVcsQ0FBQztRQUN0QyxPQUFPLFFBQVEsQ0FBQztJQUNsQixDQUFDO0lBRU8scUJBQXFCLENBQzNCLE9BQXFCLEVBQ3JCLFdBQTZCO1FBRTdCLE1BQU0sYUFBYSxHQUFHLE9BQU8sQ0FBQyxRQUFRLENBQUM7UUFDdkMsTUFBTSxZQUFZLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxHQUFHLENBQUMsT0FBTyxDQUFDLGNBQWMsR0FBRyxLQUFLLENBQUMsQ0FBQyxDQUFDO1FBQ3ZFLE1BQU0saUJBQWlCLEdBQUcsT0FBTyxDQUFDLFlBQVksQ0FBQztRQUMvQyxNQUFNLFNBQVMsR0FBRyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUMsRUFBRSxDQUFDLEdBQUcsQ0FBQyxXQUFXLENBQUMsY0FBYyxHQUFHLEdBQUcsQ0FBQyxDQUFDLENBQUM7UUFFdEUsT0FBTyxDQUNMLGFBQWEsR0FBRyxHQUFHO1lBQ25CLFlBQVksR0FBRyxJQUFJO1lBQ25CLGlCQUFpQixHQUFHLElBQUk7WUFDeEIsU0FBUyxHQUFHLEdBQUcsQ0FDaEIsQ0FBQztJQUNKLENBQUM7SUFFTyxXQUFXLENBQUksT0FBWSxFQUFFLFlBQW9CLElBQUk7UUFDM0QsSUFBSSxPQUFPLENBQUMsTUFBTSxHQUFHLFNBQVMsRUFBRSxDQUFDO1lBQy9CLE9BQU8sQ0FBQyxNQUFNLENBQUMsQ0FBQyxFQUFFLE9BQU8sQ0FBQyxNQUFNLEdBQUcsU0FBUyxDQUFDLENBQUM7UUFDaEQsQ0FBQztJQUNILENBQUM7Q0FDRixDQUFBO0FBMWRZLDBEQUF1QjtrQ0FBdkIsdUJBQXVCO0lBRG5DLElBQUEsbUJBQVUsR0FBRTt5REFNaUMsc0JBQWEsb0JBQWIsc0JBQWE7R0FMOUMsdUJBQXVCLENBMGRuQyIsIm5hbWVzIjpbXSwic291cmNlcyI6WyJDOlxcVXNlcnNcXEx1a2FcXHNlbnRpbmVsXFxiYWNrZW5kXFxzcmNcXG1vZHVsZXNcXGFpXFxhcHBsaWNhdGlvblxcc2VydmljZXNcXG9wdGltaXphdGlvblxcbW9kZWwtcGVyZm9ybWFuY2Uuc2VydmljZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBJbmplY3RhYmxlLCBMb2dnZXIgfSBmcm9tICdAbmVzdGpzL2NvbW1vbic7XHJcbmltcG9ydCB7IENvbmZpZ1NlcnZpY2UgfSBmcm9tICdAbmVzdGpzL2NvbmZpZyc7XHJcblxyXG4vKipcclxuICogTW9kZWwgUGVyZm9ybWFuY2UgU2VydmljZVxyXG4gKiBcclxuICogVHJhY2tzIGFuZCBhbmFseXplcyBBSSBtb2RlbCBwZXJmb3JtYW5jZSBtZXRyaWNzIGluY2x1ZGluZ1xyXG4gKiBhY2N1cmFjeSwgbGF0ZW5jeSwgY29zdCwgYW5kIGF2YWlsYWJpbGl0eS4gUHJvdmlkZXMgcGVyZm9ybWFuY2VcclxuICogb3B0aW1pemF0aW9uIHJlY29tbWVuZGF0aW9ucyBhbmQgdHJlbmQgYW5hbHlzaXMuXHJcbiAqL1xyXG5ASW5qZWN0YWJsZSgpXHJcbmV4cG9ydCBjbGFzcyBNb2RlbFBlcmZvcm1hbmNlU2VydmljZSB7XHJcbiAgcHJpdmF0ZSByZWFkb25seSBsb2dnZXIgPSBuZXcgTG9nZ2VyKE1vZGVsUGVyZm9ybWFuY2VTZXJ2aWNlLm5hbWUpO1xyXG4gIHByaXZhdGUgcmVhZG9ubHkgcGVyZm9ybWFuY2VEYXRhID0gbmV3IE1hcDxzdHJpbmcsIE1vZGVsUGVyZm9ybWFuY2VEYXRhPigpO1xyXG4gIHByaXZhdGUgcmVhZG9ubHkgY29zdERhdGEgPSBuZXcgTWFwPHN0cmluZywgTW9kZWxDb3N0RGF0YT4oKTtcclxuXHJcbiAgY29uc3RydWN0b3IocHJpdmF0ZSByZWFkb25seSBjb25maWdTZXJ2aWNlOiBDb25maWdTZXJ2aWNlKSB7fVxyXG5cclxuICAvKipcclxuICAgKiBHZXRzIHBlcmZvcm1hbmNlIG1ldHJpY3MgZm9yIGEgbW9kZWxcclxuICAgKi9cclxuICBhc3luYyBnZXRNb2RlbE1ldHJpY3MobW9kZWxJZDogc3RyaW5nKTogUHJvbWlzZTxNb2RlbE1ldHJpY3M+IHtcclxuICAgIGNvbnN0IGRhdGEgPSB0aGlzLnBlcmZvcm1hbmNlRGF0YS5nZXQobW9kZWxJZCk7XHJcbiAgICBcclxuICAgIGlmICghZGF0YSkge1xyXG4gICAgICAvLyBSZXR1cm4gZGVmYXVsdCBtZXRyaWNzIGZvciBuZXcgbW9kZWxzXHJcbiAgICAgIHJldHVybiB7XHJcbiAgICAgICAgYWNjdXJhY3k6IDAuOCxcclxuICAgICAgICBwcmVjaXNpb246IDAuOCxcclxuICAgICAgICByZWNhbGw6IDAuOCxcclxuICAgICAgICBmMVNjb3JlOiAwLjgsXHJcbiAgICAgICAgYXZlcmFnZUxhdGVuY3k6IDIwMDAsXHJcbiAgICAgICAgdGhyb3VnaHB1dDogMTAsXHJcbiAgICAgICAgYXZhaWxhYmlsaXR5OiAwLjk1LFxyXG4gICAgICAgIHRvdGFsUmVxdWVzdHM6IDAsXHJcbiAgICAgICAgc3VjY2Vzc2Z1bFJlcXVlc3RzOiAwLFxyXG4gICAgICAgIGZhaWxlZFJlcXVlc3RzOiAwLFxyXG4gICAgICAgIGxhc3RVcGRhdGVkOiBuZXcgRGF0ZSgpLFxyXG4gICAgICB9O1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiB0aGlzLmNhbGN1bGF0ZU1ldHJpY3MoZGF0YSk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXRzIGNvc3QgbWV0cmljcyBmb3IgYSBtb2RlbFxyXG4gICAqL1xyXG4gIGFzeW5jIGdldENvc3RNZXRyaWNzKG1vZGVsSWQ6IHN0cmluZyk6IFByb21pc2U8TW9kZWxDb3N0TWV0cmljcz4ge1xyXG4gICAgY29uc3QgZGF0YSA9IHRoaXMuY29zdERhdGEuZ2V0KG1vZGVsSWQpO1xyXG4gICAgXHJcbiAgICBpZiAoIWRhdGEpIHtcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBjb3N0UGVyUmVxdWVzdDogMC4wMSxcclxuICAgICAgICB0b3RhbENvc3Q6IDAsXHJcbiAgICAgICAgY29zdFRyZW5kOiAnc3RhYmxlJyxcclxuICAgICAgICBsYXN0VXBkYXRlZDogbmV3IERhdGUoKSxcclxuICAgICAgfTtcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBjb3N0UGVyUmVxdWVzdDogZGF0YS50b3RhbENvc3QgLyBNYXRoLm1heChkYXRhLnRvdGFsUmVxdWVzdHMsIDEpLFxyXG4gICAgICB0b3RhbENvc3Q6IGRhdGEudG90YWxDb3N0LFxyXG4gICAgICBjb3N0VHJlbmQ6IHRoaXMuY2FsY3VsYXRlQ29zdFRyZW5kKGRhdGEpLFxyXG4gICAgICBsYXN0VXBkYXRlZDogZGF0YS5sYXN0VXBkYXRlZCxcclxuICAgIH07XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBVcGRhdGVzIG1vZGVsIHBlcmZvcm1hbmNlIG1ldHJpY3NcclxuICAgKi9cclxuICBhc3luYyB1cGRhdGVNb2RlbE1ldHJpY3MoXHJcbiAgICBtb2RlbElkOiBzdHJpbmcsIFxyXG4gICAgdXBkYXRlOiBNb2RlbE1ldHJpY3NVcGRhdGVcclxuICApOiBQcm9taXNlPHZvaWQ+IHtcclxuICAgIGxldCBkYXRhID0gdGhpcy5wZXJmb3JtYW5jZURhdGEuZ2V0KG1vZGVsSWQpO1xyXG4gICAgXHJcbiAgICBpZiAoIWRhdGEpIHtcclxuICAgICAgZGF0YSA9IHtcclxuICAgICAgICBtb2RlbElkLFxyXG4gICAgICAgIGFjY3VyYWN5SGlzdG9yeTogW10sXHJcbiAgICAgICAgbGF0ZW5jeUhpc3Rvcnk6IFtdLFxyXG4gICAgICAgIHJlcXVlc3RIaXN0b3J5OiBbXSxcclxuICAgICAgICBlcnJvckhpc3Rvcnk6IFtdLFxyXG4gICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKSxcclxuICAgICAgICBsYXN0VXBkYXRlZDogbmV3IERhdGUoKSxcclxuICAgICAgfTtcclxuICAgICAgdGhpcy5wZXJmb3JtYW5jZURhdGEuc2V0KG1vZGVsSWQsIGRhdGEpO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFVwZGF0ZSBhY2N1cmFjeSBpZiBwcm92aWRlZFxyXG4gICAgaWYgKHVwZGF0ZS5hY2N1cmFjeSAhPT0gdW5kZWZpbmVkKSB7XHJcbiAgICAgIGRhdGEuYWNjdXJhY3lIaXN0b3J5LnB1c2goe1xyXG4gICAgICAgIHZhbHVlOiB1cGRhdGUuYWNjdXJhY3ksXHJcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLFxyXG4gICAgICB9KTtcclxuICAgICAgdGhpcy50cmltSGlzdG9yeShkYXRhLmFjY3VyYWN5SGlzdG9yeSk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gVXBkYXRlIGxhdGVuY3kgaWYgcHJvdmlkZWRcclxuICAgIGlmICh1cGRhdGUubGF0ZW5jeSAhPT0gdW5kZWZpbmVkKSB7XHJcbiAgICAgIGRhdGEubGF0ZW5jeUhpc3RvcnkucHVzaCh7XHJcbiAgICAgICAgdmFsdWU6IHVwZGF0ZS5sYXRlbmN5LFxyXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSxcclxuICAgICAgfSk7XHJcbiAgICAgIHRoaXMudHJpbUhpc3RvcnkoZGF0YS5sYXRlbmN5SGlzdG9yeSk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gVXBkYXRlIHVzZXIgc2F0aXNmYWN0aW9uIGlmIHByb3ZpZGVkXHJcbiAgICBpZiAodXBkYXRlLnVzZXJTYXRpc2ZhY3Rpb24gIT09IHVuZGVmaW5lZCkge1xyXG4gICAgICAvLyBTdG9yZSB1c2VyIHNhdGlzZmFjdGlvbiBhcyBhIHNwZWNpYWwgbWV0cmljXHJcbiAgICAgIGRhdGEuYWNjdXJhY3lIaXN0b3J5LnB1c2goe1xyXG4gICAgICAgIHZhbHVlOiB1cGRhdGUudXNlclNhdGlzZmFjdGlvbiAvIDUsIC8vIENvbnZlcnQgMS01IHNjYWxlIHRvIDAtMVxyXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSxcclxuICAgICAgICB0eXBlOiAndXNlcl9zYXRpc2ZhY3Rpb24nLFxyXG4gICAgICB9KTtcclxuICAgIH1cclxuXHJcbiAgICBkYXRhLmxhc3RVcGRhdGVkID0gbmV3IERhdGUoKTtcclxuICAgIFxyXG4gICAgdGhpcy5sb2dnZXIuZGVidWcoYFVwZGF0ZWQgcGVyZm9ybWFuY2UgbWV0cmljcyBmb3IgbW9kZWw6ICR7bW9kZWxJZH1gKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIFJlY29yZHMgYSByZXF1ZXN0IGNvbXBsZXRpb25cclxuICAgKi9cclxuICBhc3luYyByZWNvcmRSZXF1ZXN0KFxyXG4gICAgbW9kZWxJZDogc3RyaW5nLFxyXG4gICAgcmVxdWVzdDogUmVxdWVzdFJlY29yZFxyXG4gICk6IFByb21pc2U8dm9pZD4ge1xyXG4gICAgbGV0IGRhdGEgPSB0aGlzLnBlcmZvcm1hbmNlRGF0YS5nZXQobW9kZWxJZCk7XHJcbiAgICBcclxuICAgIGlmICghZGF0YSkge1xyXG4gICAgICBkYXRhID0ge1xyXG4gICAgICAgIG1vZGVsSWQsXHJcbiAgICAgICAgYWNjdXJhY3lIaXN0b3J5OiBbXSxcclxuICAgICAgICBsYXRlbmN5SGlzdG9yeTogW10sXHJcbiAgICAgICAgcmVxdWVzdEhpc3Rvcnk6IFtdLFxyXG4gICAgICAgIGVycm9ySGlzdG9yeTogW10sXHJcbiAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpLFxyXG4gICAgICAgIGxhc3RVcGRhdGVkOiBuZXcgRGF0ZSgpLFxyXG4gICAgICB9O1xyXG4gICAgICB0aGlzLnBlcmZvcm1hbmNlRGF0YS5zZXQobW9kZWxJZCwgZGF0YSk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gUmVjb3JkIHJlcXVlc3RcclxuICAgIGRhdGEucmVxdWVzdEhpc3RvcnkucHVzaCh7XHJcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSxcclxuICAgICAgc3VjY2VzczogcmVxdWVzdC5zdWNjZXNzLFxyXG4gICAgICBsYXRlbmN5OiByZXF1ZXN0LmxhdGVuY3ksXHJcbiAgICAgIGNvc3Q6IHJlcXVlc3QuY29zdCxcclxuICAgIH0pO1xyXG4gICAgdGhpcy50cmltSGlzdG9yeShkYXRhLnJlcXVlc3RIaXN0b3J5KTtcclxuXHJcbiAgICAvLyBSZWNvcmQgbGF0ZW5jeVxyXG4gICAgaWYgKHJlcXVlc3QubGF0ZW5jeSkge1xyXG4gICAgICBkYXRhLmxhdGVuY3lIaXN0b3J5LnB1c2goe1xyXG4gICAgICAgIHZhbHVlOiByZXF1ZXN0LmxhdGVuY3ksXHJcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLFxyXG4gICAgICB9KTtcclxuICAgICAgdGhpcy50cmltSGlzdG9yeShkYXRhLmxhdGVuY3lIaXN0b3J5KTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBSZWNvcmQgZXJyb3IgaWYgcmVxdWVzdCBmYWlsZWRcclxuICAgIGlmICghcmVxdWVzdC5zdWNjZXNzICYmIHJlcXVlc3QuZXJyb3IpIHtcclxuICAgICAgZGF0YS5lcnJvckhpc3RvcnkucHVzaCh7XHJcbiAgICAgICAgZXJyb3I6IHJlcXVlc3QuZXJyb3IsXHJcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLFxyXG4gICAgICB9KTtcclxuICAgICAgdGhpcy50cmltSGlzdG9yeShkYXRhLmVycm9ySGlzdG9yeSk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gVXBkYXRlIGNvc3QgZGF0YVxyXG4gICAgaWYgKHJlcXVlc3QuY29zdCkge1xyXG4gICAgICBhd2FpdCB0aGlzLnVwZGF0ZUNvc3REYXRhKG1vZGVsSWQsIHJlcXVlc3QuY29zdCk7XHJcbiAgICB9XHJcblxyXG4gICAgZGF0YS5sYXN0VXBkYXRlZCA9IG5ldyBEYXRlKCk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXRzIHBlcmZvcm1hbmNlIHRyZW5kcyBmb3IgYSBtb2RlbFxyXG4gICAqL1xyXG4gIGFzeW5jIGdldFBlcmZvcm1hbmNlVHJlbmRzKG1vZGVsSWQ6IHN0cmluZyk6IFByb21pc2U8UGVyZm9ybWFuY2VUcmVuZHM+IHtcclxuICAgIGNvbnN0IGRhdGEgPSB0aGlzLnBlcmZvcm1hbmNlRGF0YS5nZXQobW9kZWxJZCk7XHJcbiAgICBcclxuICAgIGlmICghZGF0YSkge1xyXG4gICAgICByZXR1cm4ge1xyXG4gICAgICAgIGFjY3VyYWN5VHJlbmQ6ICdzdGFibGUnLFxyXG4gICAgICAgIGxhdGVuY3lUcmVuZDogJ3N0YWJsZScsXHJcbiAgICAgICAgZXJyb3JSYXRlVHJlbmQ6ICdzdGFibGUnLFxyXG4gICAgICAgIHRocm91Z2hwdXRUcmVuZDogJ3N0YWJsZScsXHJcbiAgICAgIH07XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgYWNjdXJhY3lUcmVuZDogdGhpcy5jYWxjdWxhdGVUcmVuZChkYXRhLmFjY3VyYWN5SGlzdG9yeS5tYXAoaCA9PiBoLnZhbHVlKSksXHJcbiAgICAgIGxhdGVuY3lUcmVuZDogdGhpcy5jYWxjdWxhdGVUcmVuZChkYXRhLmxhdGVuY3lIaXN0b3J5Lm1hcChoID0+IGgudmFsdWUpKSxcclxuICAgICAgZXJyb3JSYXRlVHJlbmQ6IHRoaXMuY2FsY3VsYXRlRXJyb3JSYXRlVHJlbmQoZGF0YSksXHJcbiAgICAgIHRocm91Z2hwdXRUcmVuZDogdGhpcy5jYWxjdWxhdGVUaHJvdWdocHV0VHJlbmQoZGF0YSksXHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0cyBwZXJmb3JtYW5jZSBjb21wYXJpc29uIGJldHdlZW4gbW9kZWxzXHJcbiAgICovXHJcbiAgYXN5bmMgY29tcGFyZU1vZGVscyhtb2RlbElkczogc3RyaW5nW10pOiBQcm9taXNlPE1vZGVsQ29tcGFyaXNvbltdPiB7XHJcbiAgICBjb25zdCBjb21wYXJpc29uczogTW9kZWxDb21wYXJpc29uW10gPSBbXTtcclxuICAgIFxyXG4gICAgZm9yIChjb25zdCBtb2RlbElkIG9mIG1vZGVsSWRzKSB7XHJcbiAgICAgIGNvbnN0IG1ldHJpY3MgPSBhd2FpdCB0aGlzLmdldE1vZGVsTWV0cmljcyhtb2RlbElkKTtcclxuICAgICAgY29uc3QgY29zdE1ldHJpY3MgPSBhd2FpdCB0aGlzLmdldENvc3RNZXRyaWNzKG1vZGVsSWQpO1xyXG4gICAgICBjb25zdCB0cmVuZHMgPSBhd2FpdCB0aGlzLmdldFBlcmZvcm1hbmNlVHJlbmRzKG1vZGVsSWQpO1xyXG4gICAgICBcclxuICAgICAgY29tcGFyaXNvbnMucHVzaCh7XHJcbiAgICAgICAgbW9kZWxJZCxcclxuICAgICAgICBtZXRyaWNzLFxyXG4gICAgICAgIGNvc3RNZXRyaWNzLFxyXG4gICAgICAgIHRyZW5kcyxcclxuICAgICAgICBvdmVyYWxsU2NvcmU6IHRoaXMuY2FsY3VsYXRlT3ZlcmFsbFNjb3JlKG1ldHJpY3MsIGNvc3RNZXRyaWNzKSxcclxuICAgICAgfSk7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHJldHVybiBjb21wYXJpc29ucy5zb3J0KChhLCBiKSA9PiBiLm92ZXJhbGxTY29yZSAtIGEub3ZlcmFsbFNjb3JlKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIEdldHMgcGVyZm9ybWFuY2UgYWxlcnRzIGZvciBtb2RlbHNcclxuICAgKi9cclxuICBhc3luYyBnZXRQZXJmb3JtYW5jZUFsZXJ0cygpOiBQcm9taXNlPFBlcmZvcm1hbmNlQWxlcnRbXT4ge1xyXG4gICAgY29uc3QgYWxlcnRzOiBQZXJmb3JtYW5jZUFsZXJ0W10gPSBbXTtcclxuICAgIFxyXG4gICAgZm9yIChjb25zdCBbbW9kZWxJZCwgZGF0YV0gb2YgdGhpcy5wZXJmb3JtYW5jZURhdGEpIHtcclxuICAgICAgY29uc3QgbWV0cmljcyA9IHRoaXMuY2FsY3VsYXRlTWV0cmljcyhkYXRhKTtcclxuICAgICAgXHJcbiAgICAgIC8vIENoZWNrIGFjY3VyYWN5IGRlZ3JhZGF0aW9uXHJcbiAgICAgIGlmIChtZXRyaWNzLmFjY3VyYWN5IDwgMC44KSB7XHJcbiAgICAgICAgYWxlcnRzLnB1c2goe1xyXG4gICAgICAgICAgbW9kZWxJZCxcclxuICAgICAgICAgIHR5cGU6ICdhY2N1cmFjeV9kZWdyYWRhdGlvbicsXHJcbiAgICAgICAgICBzZXZlcml0eTogbWV0cmljcy5hY2N1cmFjeSA8IDAuNyA/ICdoaWdoJyA6ICdtZWRpdW0nLFxyXG4gICAgICAgICAgbWVzc2FnZTogYE1vZGVsIGFjY3VyYWN5IGRyb3BwZWQgdG8gJHsobWV0cmljcy5hY2N1cmFjeSAqIDEwMCkudG9GaXhlZCgxKX0lYCxcclxuICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSxcclxuICAgICAgICB9KTtcclxuICAgICAgfVxyXG4gICAgICBcclxuICAgICAgLy8gQ2hlY2sgaGlnaCBsYXRlbmN5XHJcbiAgICAgIGlmIChtZXRyaWNzLmF2ZXJhZ2VMYXRlbmN5ID4gNTAwMCkge1xyXG4gICAgICAgIGFsZXJ0cy5wdXNoKHtcclxuICAgICAgICAgIG1vZGVsSWQsXHJcbiAgICAgICAgICB0eXBlOiAnaGlnaF9sYXRlbmN5JyxcclxuICAgICAgICAgIHNldmVyaXR5OiBtZXRyaWNzLmF2ZXJhZ2VMYXRlbmN5ID4gMTAwMDAgPyAnaGlnaCcgOiAnbWVkaXVtJyxcclxuICAgICAgICAgIG1lc3NhZ2U6IGBNb2RlbCBsYXRlbmN5IGluY3JlYXNlZCB0byAke21ldHJpY3MuYXZlcmFnZUxhdGVuY3l9bXNgLFxyXG4gICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLFxyXG4gICAgICAgIH0pO1xyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICAvLyBDaGVjayBoaWdoIGVycm9yIHJhdGVcclxuICAgICAgY29uc3QgZXJyb3JSYXRlID0gbWV0cmljcy5mYWlsZWRSZXF1ZXN0cyAvIE1hdGgubWF4KG1ldHJpY3MudG90YWxSZXF1ZXN0cywgMSk7XHJcbiAgICAgIGlmIChlcnJvclJhdGUgPiAwLjEpIHtcclxuICAgICAgICBhbGVydHMucHVzaCh7XHJcbiAgICAgICAgICBtb2RlbElkLFxyXG4gICAgICAgICAgdHlwZTogJ2hpZ2hfZXJyb3JfcmF0ZScsXHJcbiAgICAgICAgICBzZXZlcml0eTogZXJyb3JSYXRlID4gMC4yID8gJ2hpZ2gnIDogJ21lZGl1bScsXHJcbiAgICAgICAgICBtZXNzYWdlOiBgTW9kZWwgZXJyb3IgcmF0ZSBpbmNyZWFzZWQgdG8gJHsoZXJyb3JSYXRlICogMTAwKS50b0ZpeGVkKDEpfSVgLFxyXG4gICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLFxyXG4gICAgICAgIH0pO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHJldHVybiBhbGVydHM7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXRzIG9wdGltaXphdGlvbiByZWNvbW1lbmRhdGlvbnMgZm9yIGEgbW9kZWxcclxuICAgKi9cclxuICBhc3luYyBnZXRPcHRpbWl6YXRpb25SZWNvbW1lbmRhdGlvbnMobW9kZWxJZDogc3RyaW5nKTogUHJvbWlzZTxPcHRpbWl6YXRpb25SZWNvbW1lbmRhdGlvbltdPiB7XHJcbiAgICBjb25zdCBtZXRyaWNzID0gYXdhaXQgdGhpcy5nZXRNb2RlbE1ldHJpY3MobW9kZWxJZCk7XHJcbiAgICBjb25zdCB0cmVuZHMgPSBhd2FpdCB0aGlzLmdldFBlcmZvcm1hbmNlVHJlbmRzKG1vZGVsSWQpO1xyXG4gICAgY29uc3QgcmVjb21tZW5kYXRpb25zOiBPcHRpbWl6YXRpb25SZWNvbW1lbmRhdGlvbltdID0gW107XHJcbiAgICBcclxuICAgIC8vIEFjY3VyYWN5IHJlY29tbWVuZGF0aW9uc1xyXG4gICAgaWYgKG1ldHJpY3MuYWNjdXJhY3kgPCAwLjg1KSB7XHJcbiAgICAgIHJlY29tbWVuZGF0aW9ucy5wdXNoKHtcclxuICAgICAgICB0eXBlOiAnYWNjdXJhY3lfaW1wcm92ZW1lbnQnLFxyXG4gICAgICAgIHByaW9yaXR5OiAnaGlnaCcsXHJcbiAgICAgICAgZGVzY3JpcHRpb246ICdDb25zaWRlciByZXRyYWluaW5nIHRoZSBtb2RlbCB3aXRoIG1vcmUgZGl2ZXJzZSBkYXRhJyxcclxuICAgICAgICBleHBlY3RlZEltcGFjdDogJ0ltcHJvdmUgYWNjdXJhY3kgYnkgNS0xMCUnLFxyXG4gICAgICAgIGVmZm9ydDogJ2hpZ2gnLFxyXG4gICAgICB9KTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgLy8gTGF0ZW5jeSByZWNvbW1lbmRhdGlvbnNcclxuICAgIGlmIChtZXRyaWNzLmF2ZXJhZ2VMYXRlbmN5ID4gMzAwMCkge1xyXG4gICAgICByZWNvbW1lbmRhdGlvbnMucHVzaCh7XHJcbiAgICAgICAgdHlwZTogJ2xhdGVuY3lfb3B0aW1pemF0aW9uJyxcclxuICAgICAgICBwcmlvcml0eTogJ21lZGl1bScsXHJcbiAgICAgICAgZGVzY3JpcHRpb246ICdPcHRpbWl6ZSBtb2RlbCBwYXJhbWV0ZXJzIG9yIHVzZSBtb2RlbCBxdWFudGl6YXRpb24nLFxyXG4gICAgICAgIGV4cGVjdGVkSW1wYWN0OiAnUmVkdWNlIGxhdGVuY3kgYnkgMjAtMzAlJyxcclxuICAgICAgICBlZmZvcnQ6ICdtZWRpdW0nLFxyXG4gICAgICB9KTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgLy8gQ29zdCByZWNvbW1lbmRhdGlvbnNcclxuICAgIGNvbnN0IGNvc3RNZXRyaWNzID0gYXdhaXQgdGhpcy5nZXRDb3N0TWV0cmljcyhtb2RlbElkKTtcclxuICAgIGlmIChjb3N0TWV0cmljcy5jb3N0UGVyUmVxdWVzdCA+IDAuMDIpIHtcclxuICAgICAgcmVjb21tZW5kYXRpb25zLnB1c2goe1xyXG4gICAgICAgIHR5cGU6ICdjb3N0X29wdGltaXphdGlvbicsXHJcbiAgICAgICAgcHJpb3JpdHk6ICdtZWRpdW0nLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnQ29uc2lkZXIgdXNpbmcgYSBtb3JlIGNvc3QtZWZmZWN0aXZlIG1vZGVsIHZhcmlhbnQnLFxyXG4gICAgICAgIGV4cGVjdGVkSW1wYWN0OiAnUmVkdWNlIGNvc3QgYnkgMzAtNTAlJyxcclxuICAgICAgICBlZmZvcnQ6ICdsb3cnLFxyXG4gICAgICB9KTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgcmV0dXJuIHJlY29tbWVuZGF0aW9ucztcclxuICB9XHJcblxyXG4gIC8vIFByaXZhdGUgaGVscGVyIG1ldGhvZHNcclxuXHJcbiAgcHJpdmF0ZSBjYWxjdWxhdGVNZXRyaWNzKGRhdGE6IE1vZGVsUGVyZm9ybWFuY2VEYXRhKTogTW9kZWxNZXRyaWNzIHtcclxuICAgIGNvbnN0IHJlY2VudFJlcXVlc3RzID0gZGF0YS5yZXF1ZXN0SGlzdG9yeS5zbGljZSgtMTAwKTsgLy8gTGFzdCAxMDAgcmVxdWVzdHNcclxuICAgIGNvbnN0IHJlY2VudExhdGVuY2llcyA9IGRhdGEubGF0ZW5jeUhpc3Rvcnkuc2xpY2UoLTUwKTsgLy8gTGFzdCA1MCBsYXRlbmN5IG1lYXN1cmVtZW50c1xyXG4gICAgY29uc3QgcmVjZW50QWNjdXJhY2llcyA9IGRhdGEuYWNjdXJhY3lIaXN0b3J5LnNsaWNlKC0yMCk7IC8vIExhc3QgMjAgYWNjdXJhY3kgbWVhc3VyZW1lbnRzXHJcbiAgICBcclxuICAgIGNvbnN0IHRvdGFsUmVxdWVzdHMgPSByZWNlbnRSZXF1ZXN0cy5sZW5ndGg7XHJcbiAgICBjb25zdCBzdWNjZXNzZnVsUmVxdWVzdHMgPSByZWNlbnRSZXF1ZXN0cy5maWx0ZXIociA9PiByLnN1Y2Nlc3MpLmxlbmd0aDtcclxuICAgIGNvbnN0IGZhaWxlZFJlcXVlc3RzID0gdG90YWxSZXF1ZXN0cyAtIHN1Y2Nlc3NmdWxSZXF1ZXN0cztcclxuICAgIFxyXG4gICAgY29uc3QgYXZlcmFnZUxhdGVuY3kgPSByZWNlbnRMYXRlbmNpZXMubGVuZ3RoID4gMFxyXG4gICAgICA/IHJlY2VudExhdGVuY2llcy5yZWR1Y2UoKHN1bSwgbCkgPT4gc3VtICsgbC52YWx1ZSwgMCkgLyByZWNlbnRMYXRlbmNpZXMubGVuZ3RoXHJcbiAgICAgIDogMjAwMDtcclxuICAgIFxyXG4gICAgY29uc3QgYWNjdXJhY3kgPSByZWNlbnRBY2N1cmFjaWVzLmxlbmd0aCA+IDBcclxuICAgICAgPyByZWNlbnRBY2N1cmFjaWVzLnJlZHVjZSgoc3VtLCBhKSA9PiBzdW0gKyBhLnZhbHVlLCAwKSAvIHJlY2VudEFjY3VyYWNpZXMubGVuZ3RoXHJcbiAgICAgIDogMC44O1xyXG4gICAgXHJcbiAgICBjb25zdCB0aHJvdWdocHV0ID0gdG90YWxSZXF1ZXN0cyA+IDAgXHJcbiAgICAgID8gdG90YWxSZXF1ZXN0cyAvIE1hdGgubWF4KDEsIChEYXRlLm5vdygpIC0gZGF0YS5jcmVhdGVkQXQuZ2V0VGltZSgpKSAvIDEwMDApXHJcbiAgICAgIDogMDtcclxuICAgIFxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgYWNjdXJhY3ksXHJcbiAgICAgIHByZWNpc2lvbjogYWNjdXJhY3kgKiAwLjk1LCAvLyBBcHByb3hpbWF0aW9uXHJcbiAgICAgIHJlY2FsbDogYWNjdXJhY3kgKiAwLjksIC8vIEFwcHJveGltYXRpb25cclxuICAgICAgZjFTY29yZTogYWNjdXJhY3kgKiAwLjkyLCAvLyBBcHByb3hpbWF0aW9uXHJcbiAgICAgIGF2ZXJhZ2VMYXRlbmN5LFxyXG4gICAgICB0aHJvdWdocHV0LFxyXG4gICAgICBhdmFpbGFiaWxpdHk6IHN1Y2Nlc3NmdWxSZXF1ZXN0cyAvIE1hdGgubWF4KHRvdGFsUmVxdWVzdHMsIDEpLFxyXG4gICAgICB0b3RhbFJlcXVlc3RzLFxyXG4gICAgICBzdWNjZXNzZnVsUmVxdWVzdHMsXHJcbiAgICAgIGZhaWxlZFJlcXVlc3RzLFxyXG4gICAgICBsYXN0VXBkYXRlZDogZGF0YS5sYXN0VXBkYXRlZCxcclxuICAgIH07XHJcbiAgfVxyXG5cclxuICBwcml2YXRlIGFzeW5jIHVwZGF0ZUNvc3REYXRhKG1vZGVsSWQ6IHN0cmluZywgY29zdDogbnVtYmVyKTogUHJvbWlzZTx2b2lkPiB7XHJcbiAgICBsZXQgZGF0YSA9IHRoaXMuY29zdERhdGEuZ2V0KG1vZGVsSWQpO1xyXG4gICAgXHJcbiAgICBpZiAoIWRhdGEpIHtcclxuICAgICAgZGF0YSA9IHtcclxuICAgICAgICBtb2RlbElkLFxyXG4gICAgICAgIHRvdGFsQ29zdDogMCxcclxuICAgICAgICB0b3RhbFJlcXVlc3RzOiAwLFxyXG4gICAgICAgIGNvc3RIaXN0b3J5OiBbXSxcclxuICAgICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCksXHJcbiAgICAgICAgbGFzdFVwZGF0ZWQ6IG5ldyBEYXRlKCksXHJcbiAgICAgIH07XHJcbiAgICAgIHRoaXMuY29zdERhdGEuc2V0KG1vZGVsSWQsIGRhdGEpO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICBkYXRhLnRvdGFsQ29zdCArPSBjb3N0O1xyXG4gICAgZGF0YS50b3RhbFJlcXVlc3RzKys7XHJcbiAgICBkYXRhLmNvc3RIaXN0b3J5LnB1c2goe1xyXG4gICAgICBjb3N0LFxyXG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksXHJcbiAgICB9KTtcclxuICAgIFxyXG4gICAgdGhpcy50cmltSGlzdG9yeShkYXRhLmNvc3RIaXN0b3J5KTtcclxuICAgIGRhdGEubGFzdFVwZGF0ZWQgPSBuZXcgRGF0ZSgpO1xyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBjYWxjdWxhdGVDb3N0VHJlbmQoZGF0YTogTW9kZWxDb3N0RGF0YSk6ICdpbmNyZWFzaW5nJyB8ICdkZWNyZWFzaW5nJyB8ICdzdGFibGUnIHtcclxuICAgIGlmIChkYXRhLmNvc3RIaXN0b3J5Lmxlbmd0aCA8IDEwKSB7XHJcbiAgICAgIHJldHVybiAnc3RhYmxlJztcclxuICAgIH1cclxuICAgIFxyXG4gICAgY29uc3QgcmVjZW50ID0gZGF0YS5jb3N0SGlzdG9yeS5zbGljZSgtMTApO1xyXG4gICAgY29uc3Qgb2xkZXIgPSBkYXRhLmNvc3RIaXN0b3J5LnNsaWNlKC0yMCwgLTEwKTtcclxuICAgIFxyXG4gICAgY29uc3QgcmVjZW50QXZnID0gcmVjZW50LnJlZHVjZSgoc3VtLCBjKSA9PiBzdW0gKyBjLmNvc3QsIDApIC8gcmVjZW50Lmxlbmd0aDtcclxuICAgIGNvbnN0IG9sZGVyQXZnID0gb2xkZXIucmVkdWNlKChzdW0sIGMpID0+IHN1bSArIGMuY29zdCwgMCkgLyBvbGRlci5sZW5ndGg7XHJcbiAgICBcclxuICAgIGNvbnN0IGNoYW5nZSA9IChyZWNlbnRBdmcgLSBvbGRlckF2ZykgLyBvbGRlckF2ZztcclxuICAgIFxyXG4gICAgaWYgKGNoYW5nZSA+IDAuMSkgcmV0dXJuICdpbmNyZWFzaW5nJztcclxuICAgIGlmIChjaGFuZ2UgPCAtMC4xKSByZXR1cm4gJ2RlY3JlYXNpbmcnO1xyXG4gICAgcmV0dXJuICdzdGFibGUnO1xyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBjYWxjdWxhdGVUcmVuZCh2YWx1ZXM6IG51bWJlcltdKTogJ2ltcHJvdmluZycgfCAnZGVncmFkaW5nJyB8ICdzdGFibGUnIHtcclxuICAgIGlmICh2YWx1ZXMubGVuZ3RoIDwgNSkge1xyXG4gICAgICByZXR1cm4gJ3N0YWJsZSc7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIGNvbnN0IHJlY2VudCA9IHZhbHVlcy5zbGljZSgtNSk7XHJcbiAgICBjb25zdCBvbGRlciA9IHZhbHVlcy5zbGljZSgtMTAsIC01KTtcclxuICAgIFxyXG4gICAgaWYgKG9sZGVyLmxlbmd0aCA9PT0gMCkge1xyXG4gICAgICByZXR1cm4gJ3N0YWJsZSc7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIGNvbnN0IHJlY2VudEF2ZyA9IHJlY2VudC5yZWR1Y2UoKHN1bSwgdikgPT4gc3VtICsgdiwgMCkgLyByZWNlbnQubGVuZ3RoO1xyXG4gICAgY29uc3Qgb2xkZXJBdmcgPSBvbGRlci5yZWR1Y2UoKHN1bSwgdikgPT4gc3VtICsgdiwgMCkgLyBvbGRlci5sZW5ndGg7XHJcbiAgICBcclxuICAgIGNvbnN0IGNoYW5nZSA9IChyZWNlbnRBdmcgLSBvbGRlckF2ZykgLyBvbGRlckF2ZztcclxuICAgIFxyXG4gICAgaWYgKGNoYW5nZSA+IDAuMDUpIHJldHVybiAnaW1wcm92aW5nJztcclxuICAgIGlmIChjaGFuZ2UgPCAtMC4wNSkgcmV0dXJuICdkZWdyYWRpbmcnO1xyXG4gICAgcmV0dXJuICdzdGFibGUnO1xyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBjYWxjdWxhdGVFcnJvclJhdGVUcmVuZChkYXRhOiBNb2RlbFBlcmZvcm1hbmNlRGF0YSk6ICdpbXByb3ZpbmcnIHwgJ2RlZ3JhZGluZycgfCAnc3RhYmxlJyB7XHJcbiAgICBjb25zdCByZWNlbnRSZXF1ZXN0cyA9IGRhdGEucmVxdWVzdEhpc3Rvcnkuc2xpY2UoLTIwKTtcclxuICAgIGNvbnN0IG9sZGVyUmVxdWVzdHMgPSBkYXRhLnJlcXVlc3RIaXN0b3J5LnNsaWNlKC00MCwgLTIwKTtcclxuICAgIFxyXG4gICAgaWYgKHJlY2VudFJlcXVlc3RzLmxlbmd0aCA9PT0gMCB8fCBvbGRlclJlcXVlc3RzLmxlbmd0aCA9PT0gMCkge1xyXG4gICAgICByZXR1cm4gJ3N0YWJsZSc7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIGNvbnN0IHJlY2VudEVycm9yUmF0ZSA9IHJlY2VudFJlcXVlc3RzLmZpbHRlcihyID0+ICFyLnN1Y2Nlc3MpLmxlbmd0aCAvIHJlY2VudFJlcXVlc3RzLmxlbmd0aDtcclxuICAgIGNvbnN0IG9sZGVyRXJyb3JSYXRlID0gb2xkZXJSZXF1ZXN0cy5maWx0ZXIociA9PiAhci5zdWNjZXNzKS5sZW5ndGggLyBvbGRlclJlcXVlc3RzLmxlbmd0aDtcclxuICAgIFxyXG4gICAgY29uc3QgY2hhbmdlID0gcmVjZW50RXJyb3JSYXRlIC0gb2xkZXJFcnJvclJhdGU7XHJcbiAgICBcclxuICAgIGlmIChjaGFuZ2UgPiAwLjA1KSByZXR1cm4gJ2RlZ3JhZGluZyc7XHJcbiAgICBpZiAoY2hhbmdlIDwgLTAuMDUpIHJldHVybiAnaW1wcm92aW5nJztcclxuICAgIHJldHVybiAnc3RhYmxlJztcclxuICB9XHJcblxyXG4gIHByaXZhdGUgY2FsY3VsYXRlVGhyb3VnaHB1dFRyZW5kKGRhdGE6IE1vZGVsUGVyZm9ybWFuY2VEYXRhKTogJ2ltcHJvdmluZycgfCAnZGVncmFkaW5nJyB8ICdzdGFibGUnIHtcclxuICAgIGNvbnN0IG5vdyA9IERhdGUubm93KCk7XHJcbiAgICBjb25zdCBvbmVIb3VyQWdvID0gbm93IC0gMzYwMDAwMDtcclxuICAgIGNvbnN0IHR3b0hvdXJzQWdvID0gbm93IC0gNzIwMDAwMDtcclxuICAgIFxyXG4gICAgY29uc3QgcmVjZW50UmVxdWVzdHMgPSBkYXRhLnJlcXVlc3RIaXN0b3J5LmZpbHRlcihcclxuICAgICAgciA9PiByLnRpbWVzdGFtcC5nZXRUaW1lKCkgPiBvbmVIb3VyQWdvXHJcbiAgICApLmxlbmd0aDtcclxuICAgIFxyXG4gICAgY29uc3Qgb2xkZXJSZXF1ZXN0cyA9IGRhdGEucmVxdWVzdEhpc3RvcnkuZmlsdGVyKFxyXG4gICAgICByID0+IHIudGltZXN0YW1wLmdldFRpbWUoKSA+IHR3b0hvdXJzQWdvICYmIHIudGltZXN0YW1wLmdldFRpbWUoKSA8PSBvbmVIb3VyQWdvXHJcbiAgICApLmxlbmd0aDtcclxuICAgIFxyXG4gICAgaWYgKG9sZGVyUmVxdWVzdHMgPT09IDApIHtcclxuICAgICAgcmV0dXJuICdzdGFibGUnO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICBjb25zdCBjaGFuZ2UgPSAocmVjZW50UmVxdWVzdHMgLSBvbGRlclJlcXVlc3RzKSAvIG9sZGVyUmVxdWVzdHM7XHJcbiAgICBcclxuICAgIGlmIChjaGFuZ2UgPiAwLjIpIHJldHVybiAnaW1wcm92aW5nJztcclxuICAgIGlmIChjaGFuZ2UgPCAtMC4yKSByZXR1cm4gJ2RlZ3JhZGluZyc7XHJcbiAgICByZXR1cm4gJ3N0YWJsZSc7XHJcbiAgfVxyXG5cclxuICBwcml2YXRlIGNhbGN1bGF0ZU92ZXJhbGxTY29yZShcclxuICAgIG1ldHJpY3M6IE1vZGVsTWV0cmljcywgXHJcbiAgICBjb3N0TWV0cmljczogTW9kZWxDb3N0TWV0cmljc1xyXG4gICk6IG51bWJlciB7XHJcbiAgICBjb25zdCBhY2N1cmFjeVNjb3JlID0gbWV0cmljcy5hY2N1cmFjeTtcclxuICAgIGNvbnN0IGxhdGVuY3lTY29yZSA9IE1hdGgubWF4KDAsIDEgLSAobWV0cmljcy5hdmVyYWdlTGF0ZW5jeSAvIDEwMDAwKSk7XHJcbiAgICBjb25zdCBhdmFpbGFiaWxpdHlTY29yZSA9IG1ldHJpY3MuYXZhaWxhYmlsaXR5O1xyXG4gICAgY29uc3QgY29zdFNjb3JlID0gTWF0aC5tYXgoMCwgMSAtIChjb3N0TWV0cmljcy5jb3N0UGVyUmVxdWVzdCAvIDAuMSkpO1xyXG4gICAgXHJcbiAgICByZXR1cm4gKFxyXG4gICAgICBhY2N1cmFjeVNjb3JlICogMC4zICtcclxuICAgICAgbGF0ZW5jeVNjb3JlICogMC4yNSArXHJcbiAgICAgIGF2YWlsYWJpbGl0eVNjb3JlICogMC4yNSArXHJcbiAgICAgIGNvc3RTY29yZSAqIDAuMlxyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIHByaXZhdGUgdHJpbUhpc3Rvcnk8VD4oaGlzdG9yeTogVFtdLCBtYXhMZW5ndGg6IG51bWJlciA9IDEwMDApOiB2b2lkIHtcclxuICAgIGlmIChoaXN0b3J5Lmxlbmd0aCA+IG1heExlbmd0aCkge1xyXG4gICAgICBoaXN0b3J5LnNwbGljZSgwLCBoaXN0b3J5Lmxlbmd0aCAtIG1heExlbmd0aCk7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyBUeXBlIGRlZmluaXRpb25zXHJcbmludGVyZmFjZSBNb2RlbFBlcmZvcm1hbmNlRGF0YSB7XHJcbiAgbW9kZWxJZDogc3RyaW5nO1xyXG4gIGFjY3VyYWN5SGlzdG9yeTogQXJyYXk8eyB2YWx1ZTogbnVtYmVyOyB0aW1lc3RhbXA6IERhdGU7IHR5cGU/OiBzdHJpbmcgfT47XHJcbiAgbGF0ZW5jeUhpc3Rvcnk6IEFycmF5PHsgdmFsdWU6IG51bWJlcjsgdGltZXN0YW1wOiBEYXRlIH0+O1xyXG4gIHJlcXVlc3RIaXN0b3J5OiBBcnJheTx7IHRpbWVzdGFtcDogRGF0ZTsgc3VjY2VzczogYm9vbGVhbjsgbGF0ZW5jeT86IG51bWJlcjsgY29zdD86IG51bWJlciB9PjtcclxuICBlcnJvckhpc3Rvcnk6IEFycmF5PHsgZXJyb3I6IHN0cmluZzsgdGltZXN0YW1wOiBEYXRlIH0+O1xyXG4gIGNyZWF0ZWRBdDogRGF0ZTtcclxuICBsYXN0VXBkYXRlZDogRGF0ZTtcclxufVxyXG5cclxuaW50ZXJmYWNlIE1vZGVsQ29zdERhdGEge1xyXG4gIG1vZGVsSWQ6IHN0cmluZztcclxuICB0b3RhbENvc3Q6IG51bWJlcjtcclxuICB0b3RhbFJlcXVlc3RzOiBudW1iZXI7XHJcbiAgY29zdEhpc3Rvcnk6IEFycmF5PHsgY29zdDogbnVtYmVyOyB0aW1lc3RhbXA6IERhdGUgfT47XHJcbiAgY3JlYXRlZEF0OiBEYXRlO1xyXG4gIGxhc3RVcGRhdGVkOiBEYXRlO1xyXG59XHJcblxyXG5pbnRlcmZhY2UgTW9kZWxNZXRyaWNzIHtcclxuICBhY2N1cmFjeTogbnVtYmVyO1xyXG4gIHByZWNpc2lvbjogbnVtYmVyO1xyXG4gIHJlY2FsbDogbnVtYmVyO1xyXG4gIGYxU2NvcmU6IG51bWJlcjtcclxuICBhdmVyYWdlTGF0ZW5jeTogbnVtYmVyO1xyXG4gIHRocm91Z2hwdXQ6IG51bWJlcjtcclxuICBhdmFpbGFiaWxpdHk6IG51bWJlcjtcclxuICB0b3RhbFJlcXVlc3RzOiBudW1iZXI7XHJcbiAgc3VjY2Vzc2Z1bFJlcXVlc3RzOiBudW1iZXI7XHJcbiAgZmFpbGVkUmVxdWVzdHM6IG51bWJlcjtcclxuICBsYXN0VXBkYXRlZDogRGF0ZTtcclxufVxyXG5cclxuaW50ZXJmYWNlIE1vZGVsQ29zdE1ldHJpY3Mge1xyXG4gIGNvc3RQZXJSZXF1ZXN0OiBudW1iZXI7XHJcbiAgdG90YWxDb3N0OiBudW1iZXI7XHJcbiAgY29zdFRyZW5kOiAnaW5jcmVhc2luZycgfCAnZGVjcmVhc2luZycgfCAnc3RhYmxlJztcclxuICBsYXN0VXBkYXRlZDogRGF0ZTtcclxufVxyXG5cclxuaW50ZXJmYWNlIE1vZGVsTWV0cmljc1VwZGF0ZSB7XHJcbiAgYWNjdXJhY3k/OiBudW1iZXI7XHJcbiAgbGF0ZW5jeT86IG51bWJlcjtcclxuICB1c2VyU2F0aXNmYWN0aW9uPzogbnVtYmVyO1xyXG59XHJcblxyXG5pbnRlcmZhY2UgUmVxdWVzdFJlY29yZCB7XHJcbiAgc3VjY2VzczogYm9vbGVhbjtcclxuICBsYXRlbmN5PzogbnVtYmVyO1xyXG4gIGNvc3Q/OiBudW1iZXI7XHJcbiAgZXJyb3I/OiBzdHJpbmc7XHJcbn1cclxuXHJcbmludGVyZmFjZSBQZXJmb3JtYW5jZVRyZW5kcyB7XHJcbiAgYWNjdXJhY3lUcmVuZDogJ2ltcHJvdmluZycgfCAnZGVncmFkaW5nJyB8ICdzdGFibGUnO1xyXG4gIGxhdGVuY3lUcmVuZDogJ2ltcHJvdmluZycgfCAnZGVncmFkaW5nJyB8ICdzdGFibGUnO1xyXG4gIGVycm9yUmF0ZVRyZW5kOiAnaW1wcm92aW5nJyB8ICdkZWdyYWRpbmcnIHwgJ3N0YWJsZSc7XHJcbiAgdGhyb3VnaHB1dFRyZW5kOiAnaW1wcm92aW5nJyB8ICdkZWdyYWRpbmcnIHwgJ3N0YWJsZSc7XHJcbn1cclxuXHJcbmludGVyZmFjZSBNb2RlbENvbXBhcmlzb24ge1xyXG4gIG1vZGVsSWQ6IHN0cmluZztcclxuICBtZXRyaWNzOiBNb2RlbE1ldHJpY3M7XHJcbiAgY29zdE1ldHJpY3M6IE1vZGVsQ29zdE1ldHJpY3M7XHJcbiAgdHJlbmRzOiBQZXJmb3JtYW5jZVRyZW5kcztcclxuICBvdmVyYWxsU2NvcmU6IG51bWJlcjtcclxufVxyXG5cclxuaW50ZXJmYWNlIFBlcmZvcm1hbmNlQWxlcnQge1xyXG4gIG1vZGVsSWQ6IHN0cmluZztcclxuICB0eXBlOiAnYWNjdXJhY3lfZGVncmFkYXRpb24nIHwgJ2hpZ2hfbGF0ZW5jeScgfCAnaGlnaF9lcnJvcl9yYXRlJztcclxuICBzZXZlcml0eTogJ2xvdycgfCAnbWVkaXVtJyB8ICdoaWdoJztcclxuICBtZXNzYWdlOiBzdHJpbmc7XHJcbiAgdGltZXN0YW1wOiBEYXRlO1xyXG59XHJcblxyXG5pbnRlcmZhY2UgT3B0aW1pemF0aW9uUmVjb21tZW5kYXRpb24ge1xyXG4gIHR5cGU6ICdhY2N1cmFjeV9pbXByb3ZlbWVudCcgfCAnbGF0ZW5jeV9vcHRpbWl6YXRpb24nIHwgJ2Nvc3Rfb3B0aW1pemF0aW9uJztcclxuICBwcmlvcml0eTogJ2xvdycgfCAnbWVkaXVtJyB8ICdoaWdoJztcclxuICBkZXNjcmlwdGlvbjogc3RyaW5nO1xyXG4gIGV4cGVjdGVkSW1wYWN0OiBzdHJpbmc7XHJcbiAgZWZmb3J0OiAnbG93JyB8ICdtZWRpdW0nIHwgJ2hpZ2gnO1xyXG59Il0sInZlcnNpb24iOjN9