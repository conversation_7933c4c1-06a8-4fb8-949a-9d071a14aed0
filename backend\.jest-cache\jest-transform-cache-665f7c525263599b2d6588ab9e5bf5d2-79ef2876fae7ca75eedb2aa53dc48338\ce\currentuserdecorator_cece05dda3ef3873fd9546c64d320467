99c08db1ddc1d41ff6a900a8d3a6aa8c
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CurrentUserRoles = exports.CurrentUserId = exports.CurrentUser = void 0;
const common_1 = require("@nestjs/common");
/**
 * Current User decorator to extract the authenticated user from the request
 *
 * @param data - Optional property name to extract from the user object
 * @returns The authenticated user object or a specific property
 *
 * @example
 * ```typescript
 * @Get('/profile')
 * getProfile(@CurrentUser() user: AuthenticatedUser) {
 *   return user;
 * }
 *
 * @Get('/user-id')
 * getUserId(@CurrentUser('id') userId: string) {
 *   return { userId };
 * }
 * ```
 */
exports.CurrentUser = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;
    if (!user) {
        return null;
    }
    return data ? user[data] : user;
});
/**
 * Current User ID decorator - shorthand for getting just the user ID
 *
 * @example
 * ```typescript
 * @Get('/my-data')
 * getMyData(@CurrentUserId() userId: string) {
 *   return this.service.getUserData(userId);
 * }
 * ```
 */
exports.CurrentUserId = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;
    return user?.id;
});
/**
 * Current User Roles decorator - shorthand for getting user roles
 *
 * @example
 * ```typescript
 * @Get('/check-roles')
 * checkRoles(@CurrentUserRoles() roles: string[]) {
 *   return { roles };
 * }
 * ```
 */
exports.CurrentUserRoles = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;
    return user?.roles || [];
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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