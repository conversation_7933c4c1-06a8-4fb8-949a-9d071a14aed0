927342c8db1a9dfd29c3415c3eb0da7e
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThreatIndicator = exports.ThreatIndicatorStatus = exports.ThreatIndicatorConfidence = exports.ThreatIndicatorType = void 0;
const typeorm_1 = require("typeorm");
const threat_intelligence_entity_1 = require("./threat-intelligence.entity");
/**
 * Threat Indicator Types
 */
var ThreatIndicatorType;
(function (ThreatIndicatorType) {
    ThreatIndicatorType["IP_ADDRESS"] = "ip_address";
    ThreatIndicatorType["DOMAIN"] = "domain";
    ThreatIndicatorType["URL"] = "url";
    ThreatIndicatorType["FILE_HASH"] = "file_hash";
    ThreatIndicatorType["EMAIL"] = "email";
    ThreatIndicatorType["REGISTRY_KEY"] = "registry_key";
    ThreatIndicatorType["MUTEX"] = "mutex";
    ThreatIndicatorType["USER_AGENT"] = "user_agent";
    ThreatIndicatorType["CERTIFICATE"] = "certificate";
    ThreatIndicatorType["ASN"] = "asn";
})(ThreatIndicatorType || (exports.ThreatIndicatorType = ThreatIndicatorType = {}));
/**
 * Threat Indicator Confidence Levels
 */
var ThreatIndicatorConfidence;
(function (ThreatIndicatorConfidence) {
    ThreatIndicatorConfidence["LOW"] = "low";
    ThreatIndicatorConfidence["MEDIUM"] = "medium";
    ThreatIndicatorConfidence["HIGH"] = "high";
    ThreatIndicatorConfidence["VERY_HIGH"] = "very_high";
})(ThreatIndicatorConfidence || (exports.ThreatIndicatorConfidence = ThreatIndicatorConfidence = {}));
/**
 * Threat Indicator Status
 */
var ThreatIndicatorStatus;
(function (ThreatIndicatorStatus) {
    ThreatIndicatorStatus["ACTIVE"] = "active";
    ThreatIndicatorStatus["INACTIVE"] = "inactive";
    ThreatIndicatorStatus["EXPIRED"] = "expired";
    ThreatIndicatorStatus["UNDER_REVIEW"] = "under_review";
})(ThreatIndicatorStatus || (exports.ThreatIndicatorStatus = ThreatIndicatorStatus = {}));
/**
 * Threat Indicator Entity
 *
 * Represents individual indicators of compromise (IoCs) and threat indicators
 * that can be used for threat detection and analysis
 */
let ThreatIndicator = class ThreatIndicator {
    /**
     * Check if the indicator is currently active
     */
    isActive() {
        if (this.status !== ThreatIndicatorStatus.ACTIVE) {
            return false;
        }
        if (this.expiresAt && this.expiresAt < new Date()) {
            return false;
        }
        return true;
    }
    /**
     * Check if the indicator has expired
     */
    isExpired() {
        return this.expiresAt ? this.expiresAt < new Date() : false;
    }
    /**
     * Get the age of the indicator in days
     */
    getAgeInDays() {
        const now = new Date();
        const created = new Date(this.createdAt);
        const diffTime = Math.abs(now.getTime() - created.getTime());
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
    /**
     * Update the last seen timestamp and increment observation count
     */
    recordObservation() {
        this.lastSeen = new Date();
        this.observationCount += 1;
    }
};
exports.ThreatIndicator = ThreatIndicator;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ThreatIndicator.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ThreatIndicatorType,
        comment: 'Type of threat indicator',
    }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], ThreatIndicator.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'text',
        comment: 'The actual indicator value (IP, domain, hash, etc.)',
    }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], ThreatIndicator.prototype, "value", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'text',
        nullable: true,
        comment: 'Human-readable description of the indicator',
    }),
    __metadata("design:type", String)
], ThreatIndicator.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ThreatIndicatorConfidence,
        default: ThreatIndicatorConfidence.MEDIUM,
        comment: 'Confidence level of the indicator',
    }),
    __metadata("design:type", String)
], ThreatIndicator.prototype, "confidence", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ThreatIndicatorStatus,
        default: ThreatIndicatorStatus.ACTIVE,
        comment: 'Current status of the indicator',
    }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], ThreatIndicator.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'jsonb',
        nullable: true,
        comment: 'Additional metadata and context for the indicator',
    }),
    __metadata("design:type", typeof (_a = typeof Record !== "undefined" && Record) === "function" ? _a : Object)
], ThreatIndicator.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'text',
        array: true,
        default: '{}',
        comment: 'Tags associated with this indicator',
    }),
    __metadata("design:type", Array)
], ThreatIndicator.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'text',
        nullable: true,
        comment: 'Source of the threat indicator',
    }),
    __metadata("design:type", String)
], ThreatIndicator.prototype, "source", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'timestamp',
        nullable: true,
        comment: 'When this indicator was first observed',
    }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], ThreatIndicator.prototype, "firstSeen", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'timestamp',
        nullable: true,
        comment: 'When this indicator was last observed',
    }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], ThreatIndicator.prototype, "lastSeen", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'timestamp',
        nullable: true,
        comment: 'When this indicator expires and should no longer be considered active',
    }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], ThreatIndicator.prototype, "expiresAt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'integer',
        default: 0,
        comment: 'Number of times this indicator has been observed',
    }),
    __metadata("design:type", Number)
], ThreatIndicator.prototype, "observationCount", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'decimal',
        precision: 3,
        scale: 2,
        nullable: true,
        comment: 'Severity score from 0.00 to 10.00',
    }),
    __metadata("design:type", Number)
], ThreatIndicator.prototype, "severityScore", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'uuid',
        nullable: true,
        comment: 'Associated threat intelligence record',
    }),
    __metadata("design:type", String)
], ThreatIndicator.prototype, "threatIntelligenceId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => threat_intelligence_entity_1.ThreatIntelligence, (threat) => threat.indicators, {
        onDelete: 'CASCADE',
    }),
    (0, typeorm_1.JoinColumn)({ name: 'threatIntelligenceId' }),
    __metadata("design:type", typeof (_e = typeof threat_intelligence_entity_1.ThreatIntelligence !== "undefined" && threat_intelligence_entity_1.ThreatIntelligence) === "function" ? _e : Object)
], ThreatIndicator.prototype, "threatIntelligence", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({
        type: 'timestamp',
        default: () => 'CURRENT_TIMESTAMP',
        comment: 'When the indicator was created',
    }),
    __metadata("design:type", typeof (_f = typeof Date !== "undefined" && Date) === "function" ? _f : Object)
], ThreatIndicator.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({
        type: 'timestamp',
        default: () => 'CURRENT_TIMESTAMP',
        onUpdate: 'CURRENT_TIMESTAMP',
        comment: 'When the indicator was last updated',
    }),
    __metadata("design:type", typeof (_g = typeof Date !== "undefined" && Date) === "function" ? _g : Object)
], ThreatIndicator.prototype, "updatedAt", void 0);
exports.ThreatIndicator = ThreatIndicator = __decorate([
    (0, typeorm_1.Entity)('threat_indicators'),
    (0, typeorm_1.Index)(['type', 'value']),
    (0, typeorm_1.Index)(['confidence', 'status']),
    (0, typeorm_1.Index)(['threatIntelligenceId']),
    (0, typeorm_1.Index)(['firstSeen', 'lastSeen'])
], ThreatIndicator);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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