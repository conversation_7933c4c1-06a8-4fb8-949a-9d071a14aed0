{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\caching\\ai-cache.service.ts", "mappings": ";;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAE/C;;;;;;GAMG;AAEI,IAAM,cAAc,sBAApB,MAAM,cAAc;IAKzB,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAJxC,WAAM,GAAG,IAAI,eAAM,CAAC,gBAAc,CAAC,IAAI,CAAC,CAAC;QACzC,UAAK,GAAG,IAAI,GAAG,EAAsB,CAAC;QAIrD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,qBAAqB,EAAE,OAAO,CAAC,CAAC,CAAC,SAAS;IAC7F,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,GAAG,CAAI,GAAW;QACtB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAElC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,IAAI,CAAC;QACd,CAAC;QAED,6BAA6B;QAC7B,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACvB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,6BAA6B;QAC7B,KAAK,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;QAChC,KAAK,CAAC,WAAW,EAAE,CAAC;QAEpB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,GAAG,EAAE,CAAC,CAAC;QAC/C,OAAO,KAAK,CAAC,KAAU,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,GAAG,CAAI,GAAW,EAAE,KAAQ,EAAE,GAAY;QAC9C,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;QAElE,MAAM,KAAK,GAAe;YACxB,GAAG;YACH,KAAK;YACL,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,YAAY,EAAE,IAAI,IAAI,EAAE;YACxB,SAAS;YACT,WAAW,EAAE,CAAC;YACd,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;SAChC,CAAC;QAEF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,GAAG,UAAU,GAAG,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC;QAEjF,uCAAuC;QACvC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,KAAK,CAAC,EAAE,CAAC;YAChC,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,GAAW;QACtB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACvC,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,GAAG,EAAE,CAAC,CAAC;QACrD,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,GAAG,CAAC,GAAW;QACnB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACvB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACnB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QAChD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QAEvB,MAAM,KAAK,GAAe;YACxB,YAAY,EAAE,OAAO,CAAC,MAAM;YAC5B,cAAc,EAAE,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM;YACrE,SAAS,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;YAC9D,kBAAkB,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC;gBACpC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM;gBAC7E,CAAC,CAAC,CAAC;YACL,WAAW,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC;gBAC7B,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC9D,CAAC,CAAC,IAAI;YACR,WAAW,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC;gBAC7B,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC9D,CAAC,CAAC,IAAI;SACT,CAAC;QAEF,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW;QACf,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7C,MAAM,SAAS,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;YAEjC,aAAa;YACb,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;YAEzC,YAAY;YACZ,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAE1C,cAAc;YACd,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE3B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;YAEpC,OAAO;gBACL,MAAM,EAAE,SAAS;gBACjB,YAAY,EAAE,CAAC,EAAE,6BAA6B;gBAC9C,KAAK;gBACL,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACtD,OAAO;gBACL,MAAM,EAAE,WAAW;gBACnB,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,OAAe;QACrC,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YAC/B,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;gBACpB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBACvB,YAAY,EAAE,CAAC;YACjB,CAAC;QACH,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,YAAY,oCAAoC,OAAO,EAAE,CAAC,CAAC;QAC5F,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CACZ,GAAW,EACX,OAAyB,EACzB,GAAY;QAEZ,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,GAAG,CAAI,GAAG,CAAC,CAAC;QACtC,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;YACpB,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,MAAM,KAAK,GAAG,MAAM,OAAO,EAAE,CAAC;QAC9B,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;QAChC,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CAAC,GAAW,EAAE,GAAY;QACrC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACf,CAAC;QAED,KAAK,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;QAClE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,GAAG,EAAE,CAAC,CAAC;QACrD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,yBAAyB;IAEjB,SAAS,CAAC,KAAiB;QACjC,OAAO,IAAI,IAAI,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC;IACtC,CAAC;IAEO,aAAa,CAAC,KAAU;QAC9B,4CAA4C;QAC5C,IAAI,CAAC;YACH,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;QACtC,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,CAAC,CAAC;QACX,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,OAAO;QACnB,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACtC,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC1B,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACxB,CAAC;QACH,CAAC;QAED,KAAK,MAAM,GAAG,IAAI,WAAW,EAAE,CAAC;YAC9B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACzB,CAAC;QAED,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,WAAW,CAAC,MAAM,wBAAwB,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;CACF,CAAA;AA7OY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;yDAMiC,sBAAa,oBAAb,sBAAa;GAL9C,cAAc,CA6O1B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\caching\\ai-cache.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\n\r\n/**\r\n * AI Cache Service\r\n * \r\n * Provides caching capabilities for AI operations including\r\n * analysis results, model metadata, and request responses.\r\n * Implements intelligent cache strategies and performance optimization.\r\n */\r\n@Injectable()\r\nexport class AiCacheService {\r\n  private readonly logger = new Logger(AiCacheService.name);\r\n  private readonly cache = new Map<string, CacheEntry>();\r\n  private readonly defaultTtl: number;\r\n\r\n  constructor(private readonly configService: ConfigService) {\r\n    this.defaultTtl = this.configService.get<number>('ai.cache.defaultTtl', 3600000); // 1 hour\r\n  }\r\n\r\n  /**\r\n   * Gets a value from cache\r\n   */\r\n  async get<T>(key: string): Promise<T | null> {\r\n    const entry = this.cache.get(key);\r\n    \r\n    if (!entry) {\r\n      return null;\r\n    }\r\n\r\n    // Check if entry has expired\r\n    if (this.isExpired(entry)) {\r\n      this.cache.delete(key);\r\n      return null;\r\n    }\r\n\r\n    // Update access time for LRU\r\n    entry.lastAccessed = new Date();\r\n    entry.accessCount++;\r\n\r\n    this.logger.debug(`Cache hit for key: ${key}`);\r\n    return entry.value as T;\r\n  }\r\n\r\n  /**\r\n   * Sets a value in cache\r\n   */\r\n  async set<T>(key: string, value: T, ttl?: number): Promise<void> {\r\n    const expiresAt = new Date(Date.now() + (ttl || this.defaultTtl));\r\n    \r\n    const entry: CacheEntry = {\r\n      key,\r\n      value,\r\n      createdAt: new Date(),\r\n      lastAccessed: new Date(),\r\n      expiresAt,\r\n      accessCount: 0,\r\n      size: this.calculateSize(value),\r\n    };\r\n\r\n    this.cache.set(key, entry);\r\n    this.logger.debug(`Cache set for key: ${key}, TTL: ${ttl || this.defaultTtl}ms`);\r\n\r\n    // Cleanup expired entries periodically\r\n    if (this.cache.size % 100 === 0) {\r\n      await this.cleanup();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Deletes a value from cache\r\n   */\r\n  async delete(key: string): Promise<boolean> {\r\n    const deleted = this.cache.delete(key);\r\n    if (deleted) {\r\n      this.logger.debug(`Cache deleted for key: ${key}`);\r\n    }\r\n    return deleted;\r\n  }\r\n\r\n  /**\r\n   * Checks if a key exists in cache\r\n   */\r\n  async has(key: string): Promise<boolean> {\r\n    const entry = this.cache.get(key);\r\n    if (!entry) {\r\n      return false;\r\n    }\r\n\r\n    if (this.isExpired(entry)) {\r\n      this.cache.delete(key);\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Clears all cache entries\r\n   */\r\n  async clear(): Promise<void> {\r\n    this.cache.clear();\r\n    this.logger.log('Cache cleared');\r\n  }\r\n\r\n  /**\r\n   * Gets cache statistics\r\n   */\r\n  async getStats(): Promise<CacheStats> {\r\n    const entries = Array.from(this.cache.values());\r\n    const now = new Date();\r\n    \r\n    const stats: CacheStats = {\r\n      totalEntries: entries.length,\r\n      expiredEntries: entries.filter(entry => this.isExpired(entry)).length,\r\n      totalSize: entries.reduce((sum, entry) => sum + entry.size, 0),\r\n      averageAccessCount: entries.length > 0 \r\n        ? entries.reduce((sum, entry) => sum + entry.accessCount, 0) / entries.length \r\n        : 0,\r\n      oldestEntry: entries.length > 0 \r\n        ? Math.min(...entries.map(entry => entry.createdAt.getTime()))\r\n        : null,\r\n      newestEntry: entries.length > 0 \r\n        ? Math.max(...entries.map(entry => entry.createdAt.getTime()))\r\n        : null,\r\n    };\r\n\r\n    return stats;\r\n  }\r\n\r\n  /**\r\n   * Performs cache health check\r\n   */\r\n  async checkHealth(): Promise<CacheHealthStatus> {\r\n    try {\r\n      const testKey = 'health-check-' + Date.now();\r\n      const testValue = { test: true };\r\n      \r\n      // Test write\r\n      await this.set(testKey, testValue, 1000);\r\n      \r\n      // Test read\r\n      const retrieved = await this.get(testKey);\r\n      \r\n      // Test delete\r\n      await this.delete(testKey);\r\n      \r\n      const stats = await this.getStats();\r\n      \r\n      return {\r\n        status: 'healthy',\r\n        responseTime: 0, // In-memory cache is instant\r\n        stats,\r\n        timestamp: new Date(),\r\n      };\r\n      \r\n    } catch (error) {\r\n      this.logger.error('Cache health check failed', error);\r\n      return {\r\n        status: 'unhealthy',\r\n        error: error.message,\r\n        timestamp: new Date(),\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Invalidates cache entries matching a pattern\r\n   */\r\n  async invalidatePattern(pattern: string): Promise<number> {\r\n    const regex = new RegExp(pattern);\r\n    let deletedCount = 0;\r\n    \r\n    for (const [key] of this.cache) {\r\n      if (regex.test(key)) {\r\n        this.cache.delete(key);\r\n        deletedCount++;\r\n      }\r\n    }\r\n    \r\n    this.logger.debug(`Invalidated ${deletedCount} cache entries matching pattern: ${pattern}`);\r\n    return deletedCount;\r\n  }\r\n\r\n  /**\r\n   * Gets or sets a value with a factory function\r\n   */\r\n  async getOrSet<T>(\r\n    key: string, \r\n    factory: () => Promise<T>, \r\n    ttl?: number\r\n  ): Promise<T> {\r\n    const cached = await this.get<T>(key);\r\n    if (cached !== null) {\r\n      return cached;\r\n    }\r\n\r\n    const value = await factory();\r\n    await this.set(key, value, ttl);\r\n    return value;\r\n  }\r\n\r\n  /**\r\n   * Refreshes cache entry with new TTL\r\n   */\r\n  async refresh(key: string, ttl?: number): Promise<boolean> {\r\n    const entry = this.cache.get(key);\r\n    if (!entry) {\r\n      return false;\r\n    }\r\n\r\n    entry.expiresAt = new Date(Date.now() + (ttl || this.defaultTtl));\r\n    this.logger.debug(`Cache refreshed for key: ${key}`);\r\n    return true;\r\n  }\r\n\r\n  // Private helper methods\r\n\r\n  private isExpired(entry: CacheEntry): boolean {\r\n    return new Date() > entry.expiresAt;\r\n  }\r\n\r\n  private calculateSize(value: any): number {\r\n    // Simple size calculation - can be improved\r\n    try {\r\n      return JSON.stringify(value).length;\r\n    } catch {\r\n      return 0;\r\n    }\r\n  }\r\n\r\n  private async cleanup(): Promise<void> {\r\n    const expiredKeys: string[] = [];\r\n    \r\n    for (const [key, entry] of this.cache) {\r\n      if (this.isExpired(entry)) {\r\n        expiredKeys.push(key);\r\n      }\r\n    }\r\n    \r\n    for (const key of expiredKeys) {\r\n      this.cache.delete(key);\r\n    }\r\n    \r\n    if (expiredKeys.length > 0) {\r\n      this.logger.debug(`Cleaned up ${expiredKeys.length} expired cache entries`);\r\n    }\r\n  }\r\n}\r\n\r\n// Type definitions\r\ninterface CacheEntry {\r\n  key: string;\r\n  value: any;\r\n  createdAt: Date;\r\n  lastAccessed: Date;\r\n  expiresAt: Date;\r\n  accessCount: number;\r\n  size: number;\r\n}\r\n\r\ninterface CacheStats {\r\n  totalEntries: number;\r\n  expiredEntries: number;\r\n  totalSize: number;\r\n  averageAccessCount: number;\r\n  oldestEntry: number | null;\r\n  newestEntry: number | null;\r\n}\r\n\r\ninterface CacheHealthStatus {\r\n  status: 'healthy' | 'unhealthy';\r\n  responseTime?: number;\r\n  stats?: CacheStats;\r\n  error?: string;\r\n  timestamp: Date;\r\n}"], "version": 3}