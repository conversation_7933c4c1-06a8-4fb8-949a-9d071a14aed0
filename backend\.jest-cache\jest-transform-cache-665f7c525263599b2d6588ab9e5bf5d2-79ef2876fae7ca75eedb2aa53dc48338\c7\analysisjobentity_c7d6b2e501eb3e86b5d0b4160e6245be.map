{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai-ml\\domain\\entities\\analysis-job.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCASiB;AACjB,6EAAkE;AAElE;;;GAGG;AAQI,IAAM,WAAW,GAAjB,MAAM,WAAW;IAgNtB;;OAEG;IACH,IAAI,UAAU;QACZ,OAAO,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC/E,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ;QACV,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO,IAAI,CAAC;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,IAAI,EAAE,CAAC;QAC/C,OAAO,OAAO,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,IAAI,sBAAsB;QACxB,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,UAAU;YAAE,OAAO,IAAI,CAAC;QAC9D,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAClE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,QAAgB,EAAE,KAAc;QAC7C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;QACrD,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC5B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,aAAa;QACX,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QACxB,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC5B,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,UAAgC;QAC9C,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;QAC1B,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QAC9B,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;QACpB,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC/B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,KAAa,EAAE,OAA6B;QACvD,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QAC9B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;QAC9B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,MAAe;QAC7B,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;QAC1B,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QAC9B,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;QAC7B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QACxB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,KAA4C;QAC3D,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QAC1B,CAAC;QACD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,OAA+C;QAC/D,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QAC3B,CAAC;QACD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,4BAA4B;QAC1B,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAC7D,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;YAChC,OAAO;QACT,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACtD,MAAM,cAAc,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC;QACvD,MAAM,SAAS,GAAG,cAAc,GAAG,OAAO,CAAC;QAE3C,IAAI,CAAC,mBAAmB,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;IAC9D,CAAC;CACF,CAAA;AArWY,kCAAW;AAEtB;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;uCACpB;AAkBX;IAbC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE;YACJ,wBAAwB;YACxB,gCAAgC;YAChC,iBAAiB;YACjB,mBAAmB;YACnB,gBAAgB;YAChB,kBAAkB;YAClB,kBAAkB;YAClB,qBAAqB;SACtB;KACF,CAAC;;yCACW;AAMb;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;0CACV;AAMd;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gDACpB;AAUrB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC;QACrF,OAAO,EAAE,SAAS;KACnB,CAAC;;2CAC0F;AAU5F;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC;QACzC,OAAO,EAAE,QAAQ;KAClB,CAAC;;6CAC6C;AAM/C;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;kDACnC,MAAM,oBAAN,MAAM;8CAAc;AAM/B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAClD,MAAM,oBAAN,MAAM;+CAAc;AAMjC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDAQxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;6CACvB;AAMjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;iDAC5B;AAMtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gDAC7C;AAMrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,sBAAsB,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACrE,IAAI,oBAAJ,IAAI;wDAAC;AAM3B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACrE,IAAI,oBAAJ,IAAI;gDAAC;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACrE,IAAI,oBAAJ,IAAI;8CAAC;AAMjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACrE,IAAI,oBAAJ,IAAI;gDAAC;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;iDAC1C;AAMtB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAClD,MAAM,oBAAN,MAAM;iDAAc;AAMnC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;+CAC1C;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;+CAC1C;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDAQhE;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDAOjE;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yCAC1B;AAMhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAC/B,MAAM,oBAAN,MAAM;6CAAc;AAM/B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;8CAC3B;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;8CAC1C;AAGnB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;8CAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;8CAAC;AAKhB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,+CAAkB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACvD,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,wBAAwB,EAAE,CAAC;kDAC1B,+CAAkB,oBAAlB,+CAAkB;uDAAC;AAGxC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,wBAAwB,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yDAC3C;sBA9MnB,WAAW;IAPvB,IAAA,gBAAM,EAAC,eAAe,CAAC;IACvB,IAAA,eAAK,EAAC,CAAC,MAAM,CAAC,CAAC;IACf,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,WAAW,CAAC,CAAC;IACpB,IAAA,eAAK,EAAC,CAAC,aAAa,CAAC,CAAC;IACtB,IAAA,eAAK,EAAC,CAAC,aAAa,CAAC,CAAC;GACV,WAAW,CAqWvB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai-ml\\domain\\entities\\analysis-job.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  ManyToOne,\r\n  JoinColumn,\r\n} from 'typeorm';\r\nimport { ModelConfiguration } from './model-configuration.entity';\r\n\r\n/**\r\n * Analysis Job entity\r\n * Represents AI/ML analysis tasks and their execution status\r\n */\r\n@Entity('analysis_jobs')\r\n@Index(['type'])\r\n@Index(['status'])\r\n@Index(['priority'])\r\n@Index(['createdAt'])\r\n@Index(['scheduledAt'])\r\n@Index(['completedAt'])\r\nexport class AnalysisJob {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Job type\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: [\r\n      'vulnerability_analysis',\r\n      'threat_intelligence_processing',\r\n      'risk_assessment',\r\n      'report_generation',\r\n      'ioc_enrichment',\r\n      'malware_analysis',\r\n      'network_analysis',\r\n      'behavioral_analysis',\r\n    ],\r\n  })\r\n  type: string;\r\n\r\n  /**\r\n   * Job title/name\r\n   */\r\n  @Column({ length: 255 })\r\n  title: string;\r\n\r\n  /**\r\n   * Job description\r\n   */\r\n  @Column({ type: 'text', nullable: true })\r\n  description?: string;\r\n\r\n  /**\r\n   * Job status\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['pending', 'queued', 'running', 'completed', 'failed', 'cancelled', 'timeout'],\r\n    default: 'pending',\r\n  })\r\n  status: 'pending' | 'queued' | 'running' | 'completed' | 'failed' | 'cancelled' | 'timeout';\r\n\r\n  /**\r\n   * Job priority\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['low', 'normal', 'high', 'urgent'],\r\n    default: 'normal',\r\n  })\r\n  priority: 'low' | 'normal' | 'high' | 'urgent';\r\n\r\n  /**\r\n   * Input data for the analysis\r\n   */\r\n  @Column({ name: 'input_data', type: 'jsonb' })\r\n  inputData: Record<string, any>;\r\n\r\n  /**\r\n   * Analysis results\r\n   */\r\n  @Column({ name: 'output_data', type: 'jsonb', nullable: true })\r\n  outputData?: Record<string, any>;\r\n\r\n  /**\r\n   * Job configuration parameters\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  configuration?: {\r\n    timeout?: number;\r\n    retries?: number;\r\n    batchSize?: number;\r\n    modelParameters?: Record<string, any>;\r\n    outputFormat?: string;\r\n    confidenceThreshold?: number;\r\n  };\r\n\r\n  /**\r\n   * Progress percentage (0-100)\r\n   */\r\n  @Column({ type: 'integer', default: 0 })\r\n  progress: number;\r\n\r\n  /**\r\n   * Current processing stage\r\n   */\r\n  @Column({ name: 'current_stage', nullable: true })\r\n  currentStage?: string;\r\n\r\n  /**\r\n   * Total number of stages\r\n   */\r\n  @Column({ name: 'total_stages', type: 'integer', nullable: true })\r\n  totalStages?: number;\r\n\r\n  /**\r\n   * Estimated completion time\r\n   */\r\n  @Column({ name: 'estimated_completion', type: 'timestamp with time zone', nullable: true })\r\n  estimatedCompletion?: Date;\r\n\r\n  /**\r\n   * When the job was scheduled to run\r\n   */\r\n  @Column({ name: 'scheduled_at', type: 'timestamp with time zone', nullable: true })\r\n  scheduledAt?: Date;\r\n\r\n  /**\r\n   * When the job started processing\r\n   */\r\n  @Column({ name: 'started_at', type: 'timestamp with time zone', nullable: true })\r\n  startedAt?: Date;\r\n\r\n  /**\r\n   * When the job completed\r\n   */\r\n  @Column({ name: 'completed_at', type: 'timestamp with time zone', nullable: true })\r\n  completedAt?: Date;\r\n\r\n  /**\r\n   * Error message if job failed\r\n   */\r\n  @Column({ name: 'error_message', type: 'text', nullable: true })\r\n  errorMessage?: string;\r\n\r\n  /**\r\n   * Error details and stack trace\r\n   */\r\n  @Column({ name: 'error_details', type: 'jsonb', nullable: true })\r\n  errorDetails?: Record<string, any>;\r\n\r\n  /**\r\n   * Number of retry attempts\r\n   */\r\n  @Column({ name: 'retry_count', type: 'integer', default: 0 })\r\n  retryCount: number;\r\n\r\n  /**\r\n   * Maximum number of retries allowed\r\n   */\r\n  @Column({ name: 'max_retries', type: 'integer', default: 3 })\r\n  maxRetries: number;\r\n\r\n  /**\r\n   * Resource usage metrics\r\n   */\r\n  @Column({ name: 'resource_usage', type: 'jsonb', nullable: true })\r\n  resourceUsage?: {\r\n    cpuTime?: number;\r\n    memoryUsage?: number;\r\n    gpuTime?: number;\r\n    apiCalls?: number;\r\n    tokensUsed?: number;\r\n    cost?: number;\r\n  };\r\n\r\n  /**\r\n   * Quality metrics for the analysis\r\n   */\r\n  @Column({ name: 'quality_metrics', type: 'jsonb', nullable: true })\r\n  qualityMetrics?: {\r\n    confidence?: number;\r\n    accuracy?: number;\r\n    precision?: number;\r\n    recall?: number;\r\n    f1Score?: number;\r\n  };\r\n\r\n  /**\r\n   * Tags for categorization\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  tags?: string[];\r\n\r\n  /**\r\n   * Additional metadata\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  metadata?: Record<string, any>;\r\n\r\n  /**\r\n   * User who created the job\r\n   */\r\n  @Column({ name: 'created_by', type: 'uuid' })\r\n  createdBy: string;\r\n\r\n  /**\r\n   * User who last updated the job\r\n   */\r\n  @Column({ name: 'updated_by', type: 'uuid', nullable: true })\r\n  updatedBy?: string;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @ManyToOne(() => ModelConfiguration, { nullable: true })\r\n  @JoinColumn({ name: 'model_configuration_id' })\r\n  modelConfiguration?: ModelConfiguration;\r\n\r\n  @Column({ name: 'model_configuration_id', type: 'uuid', nullable: true })\r\n  modelConfigurationId?: string;\r\n\r\n  /**\r\n   * Check if job is in a terminal state\r\n   */\r\n  get isTerminal(): boolean {\r\n    return ['completed', 'failed', 'cancelled', 'timeout'].includes(this.status);\r\n  }\r\n\r\n  /**\r\n   * Check if job is currently running\r\n   */\r\n  get isRunning(): boolean {\r\n    return ['queued', 'running'].includes(this.status);\r\n  }\r\n\r\n  /**\r\n   * Check if job can be retried\r\n   */\r\n  get canRetry(): boolean {\r\n    return this.status === 'failed' && this.retryCount < this.maxRetries;\r\n  }\r\n\r\n  /**\r\n   * Get job duration in milliseconds\r\n   */\r\n  get duration(): number | null {\r\n    if (!this.startedAt) return null;\r\n    const endTime = this.completedAt || new Date();\r\n    return endTime.getTime() - this.startedAt.getTime();\r\n  }\r\n\r\n  /**\r\n   * Get job duration in seconds\r\n   */\r\n  get durationSeconds(): number | null {\r\n    const duration = this.duration;\r\n    return duration ? Math.round(duration / 1000) : null;\r\n  }\r\n\r\n  /**\r\n   * Get estimated time remaining in milliseconds\r\n   */\r\n  get estimatedTimeRemaining(): number | null {\r\n    if (!this.estimatedCompletion || this.isTerminal) return null;\r\n    const remaining = this.estimatedCompletion.getTime() - Date.now();\r\n    return Math.max(0, remaining);\r\n  }\r\n\r\n  /**\r\n   * Update job progress\r\n   */\r\n  updateProgress(progress: number, stage?: string): void {\r\n    this.progress = Math.max(0, Math.min(100, progress));\r\n    if (stage) {\r\n      this.currentStage = stage;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Mark job as started\r\n   */\r\n  markAsStarted(): void {\r\n    this.status = 'running';\r\n    this.startedAt = new Date();\r\n    this.progress = 0;\r\n  }\r\n\r\n  /**\r\n   * Mark job as completed\r\n   */\r\n  markAsCompleted(outputData?: Record<string, any>): void {\r\n    this.status = 'completed';\r\n    this.completedAt = new Date();\r\n    this.progress = 100;\r\n    if (outputData) {\r\n      this.outputData = outputData;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Mark job as failed\r\n   */\r\n  markAsFailed(error: string, details?: Record<string, any>): void {\r\n    this.status = 'failed';\r\n    this.completedAt = new Date();\r\n    this.errorMessage = error;\r\n    if (details) {\r\n      this.errorDetails = details;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Mark job as cancelled\r\n   */\r\n  markAsCancelled(reason?: string): void {\r\n    this.status = 'cancelled';\r\n    this.completedAt = new Date();\r\n    if (reason) {\r\n      this.errorMessage = reason;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Increment retry count\r\n   */\r\n  incrementRetry(): void {\r\n    this.retryCount += 1;\r\n    this.status = 'pending';\r\n    this.startedAt = null;\r\n    this.completedAt = null;\r\n    this.errorMessage = null;\r\n    this.errorDetails = null;\r\n    this.progress = 0;\r\n  }\r\n\r\n  /**\r\n   * Add resource usage data\r\n   */\r\n  addResourceUsage(usage: Partial<AnalysisJob['resourceUsage']>): void {\r\n    if (!this.resourceUsage) {\r\n      this.resourceUsage = {};\r\n    }\r\n    Object.assign(this.resourceUsage, usage);\r\n  }\r\n\r\n  /**\r\n   * Add quality metrics\r\n   */\r\n  addQualityMetrics(metrics: Partial<AnalysisJob['qualityMetrics']>): void {\r\n    if (!this.qualityMetrics) {\r\n      this.qualityMetrics = {};\r\n    }\r\n    Object.assign(this.qualityMetrics, metrics);\r\n  }\r\n\r\n  /**\r\n   * Calculate estimated completion time based on progress\r\n   */\r\n  calculateEstimatedCompletion(): void {\r\n    if (!this.startedAt || this.progress <= 0 || this.isTerminal) {\r\n      this.estimatedCompletion = null;\r\n      return;\r\n    }\r\n\r\n    const elapsed = Date.now() - this.startedAt.getTime();\r\n    const estimatedTotal = (elapsed / this.progress) * 100;\r\n    const remaining = estimatedTotal - elapsed;\r\n\r\n    this.estimatedCompletion = new Date(Date.now() + remaining);\r\n  }\r\n}\r\n"], "version": 3}