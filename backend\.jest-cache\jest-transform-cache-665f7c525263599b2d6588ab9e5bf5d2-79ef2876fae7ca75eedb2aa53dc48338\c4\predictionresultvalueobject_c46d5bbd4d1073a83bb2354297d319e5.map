{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\value-objects\\prediction-result.value-object.ts", "mappings": ";;;AAAA,iGAA4F;AAC5F,mFAAkE;AAkBlE,MAAa,gBAA0B,SAAQ,mCAAyC;IACtF,YACE,UAAa,EACb,UAA2B,EAC3B,YAA+D,EAC/D,QAA8B,EAC9B,SAAgB;QAEhB,KAAK,CAAC;YACJ,UAAU;YACV,UAAU;YACV,YAAY,EAAE,YAAY,IAAI,EAAE;YAChC,QAAQ,EAAE,QAAQ,IAAI,EAAE;YACxB,SAAS,EAAE,SAAS,IAAI,IAAI,IAAI,EAAE;SACnC,CAAC,CAAC;IACL,CAAC;IAES,QAAQ;QAChB,KAAK,CAAC,QAAQ,EAAE,CAAC;QAEjB,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;YAC5E,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,YAAY,+CAAe,CAAC,EAAE,CAAC;YACzD,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC;YAC7C,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,YAAY,IAAI,CAAC,EAAE,CAAC;YAC7C,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,wBAAwB;QACxB,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YACnD,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,CAAC;gBACtF,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;YAChF,CAAC;YAED,IAAI,CAAC,CAAC,WAAW,CAAC,UAAU,YAAY,+CAAe,CAAC,EAAE,CAAC;gBACzD,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;YAC/E,CAAC;QACH,CAAC;QAED,sEAAsE;QACtE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7D,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC;gBAClG,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;YACnF,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,MAAM,CAAI,UAAa,EAAE,UAAkB;QACvD,OAAO,IAAI,gBAAgB,CAAC,UAAU,EAAE,IAAI,+CAAe,CAAC,UAAU,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,gBAAgB,CAC5B,UAAa,EACb,UAAkB,EAClB,YAAqD;QAErD,MAAM,kBAAkB,GAAG,YAAY;aACpC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,+CAAe,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;aACnF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAE3D,OAAO,IAAI,gBAAgB,CACzB,UAAU,EACV,IAAI,+CAAe,CAAC,UAAU,CAAC,EAC/B,kBAAkB,CACnB,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,MAAM,CAClB,UAAmB,EACnB,UAAkB,EAClB,gBAAwB,UAAU,EAClC,gBAAwB,UAAU;QAElC,MAAM,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC;QACzD,MAAM,gBAAgB,GAAG,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC;QACpE,MAAM,qBAAqB,GAAG,CAAC,GAAG,UAAU,CAAC;QAE7C,OAAO,IAAI,gBAAgB,CACzB,KAAK,EACL,IAAI,+CAAe,CAAC,UAAU,CAAC,EAC/B,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,UAAU,EAAE,IAAI,+CAAe,CAAC,qBAAqB,CAAC,EAAE,CAAC,CACtF,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,UAAU,CACtB,WAAoD,EACpD,QAA8B;QAE9B,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACzD,CAAC;QAED,qCAAqC;QACrC,MAAM,MAAM,GAAG,WAAW;aACvB,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,+CAAe,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;aACtF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAE3D,MAAM,aAAa,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAErC,OAAO,IAAI,gBAAgB,CACzB,aAAa,CAAC,KAAK,EACnB,aAAa,CAAC,UAAU,EACxB,YAAY,EACZ,QAAQ,CACT,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,UAAU,CACtB,KAAa,EACb,UAAkB,EAClB,KAAoC,EACpC,QAA8B;QAE9B,MAAM,cAAc,GAAG,EAAE,GAAG,QAAQ,EAAE,CAAC;QACvC,IAAI,KAAK,EAAE,CAAC;YACV,cAAc,CAAC,KAAK,GAAG,KAAK,CAAC;YAC7B,cAAc,CAAC,WAAW,GAAG,KAAK,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,IAAI,KAAK,CAAC,GAAG,CAAC;QACxE,CAAC;QAED,OAAO,IAAI,gBAAgB,CACzB,KAAK,EACL,IAAI,+CAAe,CAAC,UAAU,CAAC,EAC/B,EAAE,EACF,cAAc,CACf,CAAC;IACJ,CAAC;IAED,UAAU;IACV,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;IAChC,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;IAChC,CAAC;IAED,IAAI,YAAY;QACd,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;IACvC,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;IACrC,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,iBAAiB,CAAC,YAAoB,GAAG;QAC9C,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,YAAoB,GAAG;QAC7C,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,GAAG,SAAS,CAAC;IAClD,CAAC;IAED;;OAEG;IACI,mBAAmB;QACxB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;IACzC,CAAC;IAED;;OAEG;IACI,eAAe;QACpB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,CAAS;QACjC,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACI,6BAA6B,CAAC,SAAiB;QACpD,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC;IAC1F,CAAC;IAED;;OAEG;IACI,gBAAgB;QACrB,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC;QACtC,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACpD,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,GAAG,eAAe,CAAC,UAAU,CAAC,KAAK,CAAC;IACzE,CAAC;IAED;;OAEG;IACI,qBAAqB,CAAC,YAAoB,GAAG;QAClD,OAAO,IAAI,CAAC,gBAAgB,EAAE,IAAI,SAAS,CAAC;IAC9C,CAAC;IAED;;OAEG;IACI,UAAU;QACf,MAAM,cAAc,GAAG;YACrB,EAAE,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;YACtC,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY;SAC5B,CAAC;QAEF,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,KAAK,MAAM,IAAI,IAAI,cAAc,EAAE,CAAC;YAClC,MAAM,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;YAChC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;gBACV,OAAO,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,YAAoB,GAAG;QACxC,OAAO,IAAI,CAAC,gBAAgB,EAAE,GAAG,SAAS,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;IACtD,CAAC;IAED;;OAEG;IACI,OAAO,CAAC,QAAgB;QAC7B,OAAO,IAAI,CAAC,MAAM,EAAE,IAAI,QAAQ,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,QAA6B;QAC/C,OAAO,IAAI,gBAAgB,CACzB,IAAI,CAAC,MAAM,CAAC,UAAU,EACtB,IAAI,CAAC,MAAM,CAAC,UAAU,EACtB,IAAI,CAAC,MAAM,CAAC,YAAY,EACxB,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,QAAQ,EAAE,EACxC,IAAI,CAAC,MAAM,CAAC,SAAS,CACtB,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,UAA2B;QAC/C,OAAO,IAAI,gBAAgB,CACzB,IAAI,CAAC,MAAM,CAAC,UAAU,EACtB,UAAU,EACV,IAAI,CAAC,MAAM,CAAC,YAAY,EACxB,IAAI,CAAC,MAAM,CAAC,QAAQ,EACpB,IAAI,CAAC,MAAM,CAAC,SAAS,CACtB,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,YAA8D;QACpF,MAAM,kBAAkB,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAErG,OAAO,IAAI,gBAAgB,CACzB,IAAI,CAAC,MAAM,CAAC,UAAU,EACtB,IAAI,CAAC,MAAM,CAAC,UAAU,EACtB,kBAAkB,EAClB,IAAI,CAAC,MAAM,CAAC,QAAQ,EACpB,IAAI,CAAC,MAAM,CAAC,SAAS,CACtB,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,KAA0B;QACzC,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC;IACtE,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,KAA0B;QAC5C,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,MAAM,aAAa,GAAG,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,QAAQ;YAC9D,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;YACxC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAEnC,IAAI,MAAM,GAAG,eAAe,aAAa,KAAK,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,CAAC;QAEnF,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY;iBACpC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,0BAA0B;iBACtC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,UAAU,CAAC,kBAAkB,EAAE,GAAG,CAAC;iBACnE,IAAI,CAAC,IAAI,CAAC,CAAC;YACd,MAAM,IAAI,oBAAoB,MAAM,EAAE,CAAC;QACzC,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;YAClC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE;YAC3C,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBACjD,KAAK,EAAE,GAAG,CAAC,KAAK;gBAChB,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE;aACpC,CAAC,CAAC;YACH,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;YAC9B,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE;YAC9C,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE;YAC3C,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EAAE;YACzC,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE;YACtC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE;YAC/B,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;YAC1B,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE;SACnB,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,QAAQ,CAAI,IAAyB;QACjD,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,CAAC;YACzD,KAAK,EAAE,GAAG,CAAC,KAAK;YAChB,UAAU,EAAE,+CAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC;SACrD,CAAC,CAAC,IAAI,EAAE,CAAC;QAEV,OAAO,IAAI,gBAAgB,CACzB,IAAI,CAAC,UAAU,EACf,+CAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,EACzC,YAAY,EACZ,IAAI,CAAC,QAAQ,EACb,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CACzB,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,QAAQ,CACpB,OAA8B,EAC9B,SAAyC,SAAS,EAClD,OAAkB;QAElB,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,MAAM,KAAK,UAAU,IAAI,CAAC,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAC7E,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;QAC9E,CAAC;QAED,iFAAiF;QACjF,8EAA8E;QAC9E,MAAM,aAAa,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAC3F,MAAM,UAAU,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;QAEpC,oCAAoC;QACpC,MAAM,gBAAgB,GAAwB,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QACpH,gBAAgB,CAAC,cAAc,GAAG,MAAM,CAAC;QACzC,gBAAgB,CAAC,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC;QAE/C,OAAO,IAAI,gBAAgB,CACzB,UAAU,CAAC,UAAU,EACrB,UAAU,CAAC,UAAU,EACrB,UAAU,CAAC,YAAY,EACvB,gBAAgB,CACjB,CAAC;IACJ,CAAC;CACF;AAvaD,4CAuaC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\value-objects\\prediction-result.value-object.ts"], "sourcesContent": ["import { BaseValueObject } from '../../../../shared-kernel/value-objects/base-value-object';\r\nimport { ConfidenceScore } from './confidence-score.value-object';\r\n\r\n/**\r\n * Prediction Result Value Object\r\n * \r\n * Represents the result of an AI prediction with the predicted value,\r\n * confidence score, and additional metadata. Provides utility methods\r\n * for result validation, comparison, and interpretation.\r\n */\r\n\r\nexport interface PredictionResultProps<T = any> {\r\n  prediction: T;\r\n  confidence: ConfidenceScore;\r\n  alternatives?: Array<{ value: T; confidence: ConfidenceScore }>;\r\n  metadata?: Record<string, any>;\r\n  timestamp: Date;\r\n}\r\n\r\nexport class PredictionResult<T = any> extends BaseValueObject<PredictionResultProps<T>> {\r\n  constructor(\r\n    prediction: T,\r\n    confidence: ConfidenceScore,\r\n    alternatives?: Array<{ value: T; confidence: ConfidenceScore }>,\r\n    metadata?: Record<string, any>,\r\n    timestamp?: Date\r\n  ) {\r\n    super({\r\n      prediction,\r\n      confidence,\r\n      alternatives: alternatives || [],\r\n      metadata: metadata || {},\r\n      timestamp: timestamp || new Date(),\r\n    });\r\n  }\r\n\r\n  protected validate(): void {\r\n    super.validate();\r\n    \r\n    if (this._value.prediction === undefined || this._value.prediction === null) {\r\n      throw new Error('Prediction value cannot be null or undefined');\r\n    }\r\n\r\n    if (!(this._value.confidence instanceof ConfidenceScore)) {\r\n      throw new Error('Confidence must be a ConfidenceScore instance');\r\n    }\r\n\r\n    if (!Array.isArray(this._value.alternatives)) {\r\n      throw new Error('Alternatives must be an array');\r\n    }\r\n\r\n    if (!(this._value.timestamp instanceof Date)) {\r\n      throw new Error('Timestamp must be a Date instance');\r\n    }\r\n\r\n    // Validate alternatives\r\n    for (const alternative of this._value.alternatives) {\r\n      if (!alternative.hasOwnProperty('value') || !alternative.hasOwnProperty('confidence')) {\r\n        throw new Error('Each alternative must have value and confidence properties');\r\n      }\r\n      \r\n      if (!(alternative.confidence instanceof ConfidenceScore)) {\r\n        throw new Error('Alternative confidence must be a ConfidenceScore instance');\r\n      }\r\n    }\r\n\r\n    // Validate that alternatives are sorted by confidence (highest first)\r\n    for (let i = 0; i < this._value.alternatives.length - 1; i++) {\r\n      if (this._value.alternatives[i].confidence.isLessThan(this._value.alternatives[i + 1].confidence)) {\r\n        throw new Error('Alternatives must be sorted by confidence in descending order');\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Creates a simple prediction result with just a value and confidence\r\n   */\r\n  public static simple<T>(prediction: T, confidence: number): PredictionResult<T> {\r\n    return new PredictionResult(prediction, new ConfidenceScore(confidence));\r\n  }\r\n\r\n  /**\r\n   * Creates a prediction result with alternatives\r\n   */\r\n  public static withAlternatives<T>(\r\n    prediction: T,\r\n    confidence: number,\r\n    alternatives: Array<{ value: T; confidence: number }>\r\n  ): PredictionResult<T> {\r\n    const sortedAlternatives = alternatives\r\n      .map(alt => ({ value: alt.value, confidence: new ConfidenceScore(alt.confidence) }))\r\n      .sort((a, b) => b.confidence.value - a.confidence.value);\r\n\r\n    return new PredictionResult(\r\n      prediction,\r\n      new ConfidenceScore(confidence),\r\n      sortedAlternatives\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Creates a binary classification result\r\n   */\r\n  public static binary(\r\n    prediction: boolean,\r\n    confidence: number,\r\n    positiveLabel: string = 'positive',\r\n    negativeLabel: string = 'negative'\r\n  ): PredictionResult<string> {\r\n    const label = prediction ? positiveLabel : negativeLabel;\r\n    const alternativeLabel = prediction ? negativeLabel : positiveLabel;\r\n    const alternativeConfidence = 1 - confidence;\r\n\r\n    return new PredictionResult(\r\n      label,\r\n      new ConfidenceScore(confidence),\r\n      [{ value: alternativeLabel, confidence: new ConfidenceScore(alternativeConfidence) }]\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Creates a multi-class classification result\r\n   */\r\n  public static multiClass<T>(\r\n    predictions: Array<{ value: T; confidence: number }>,\r\n    metadata?: Record<string, any>\r\n  ): PredictionResult<T> {\r\n    if (predictions.length === 0) {\r\n      throw new Error('At least one prediction is required');\r\n    }\r\n\r\n    // Sort by confidence (highest first)\r\n    const sorted = predictions\r\n      .map(pred => ({ value: pred.value, confidence: new ConfidenceScore(pred.confidence) }))\r\n      .sort((a, b) => b.confidence.value - a.confidence.value);\r\n\r\n    const topPrediction = sorted[0];\r\n    const alternatives = sorted.slice(1);\r\n\r\n    return new PredictionResult(\r\n      topPrediction.value,\r\n      topPrediction.confidence,\r\n      alternatives,\r\n      metadata\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Creates a regression result\r\n   */\r\n  public static regression(\r\n    value: number,\r\n    confidence: number,\r\n    range?: { min: number; max: number },\r\n    metadata?: Record<string, any>\r\n  ): PredictionResult<number> {\r\n    const resultMetadata = { ...metadata };\r\n    if (range) {\r\n      resultMetadata.range = range;\r\n      resultMetadata.withinRange = value >= range.min && value <= range.max;\r\n    }\r\n\r\n    return new PredictionResult(\r\n      value,\r\n      new ConfidenceScore(confidence),\r\n      [],\r\n      resultMetadata\r\n    );\r\n  }\r\n\r\n  // Getters\r\n  get prediction(): T {\r\n    return this._value.prediction;\r\n  }\r\n\r\n  get confidence(): ConfidenceScore {\r\n    return this._value.confidence;\r\n  }\r\n\r\n  get alternatives(): Array<{ value: T; confidence: ConfidenceScore }> {\r\n    return [...this._value.alternatives];\r\n  }\r\n\r\n  get metadata(): Record<string, any> {\r\n    return { ...this._value.metadata };\r\n  }\r\n\r\n  get timestamp(): Date {\r\n    return this._value.timestamp;\r\n  }\r\n\r\n  /**\r\n   * Checks if the prediction has high confidence\r\n   */\r\n  public hasHighConfidence(threshold: number = 0.8): boolean {\r\n    return this._value.confidence.meetsThreshold(threshold);\r\n  }\r\n\r\n  /**\r\n   * Checks if the prediction has low confidence\r\n   */\r\n  public hasLowConfidence(threshold: number = 0.3): boolean {\r\n    return this._value.confidence.value < threshold;\r\n  }\r\n\r\n  /**\r\n   * Gets the number of alternatives\r\n   */\r\n  public getAlternativeCount(): number {\r\n    return this._value.alternatives.length;\r\n  }\r\n\r\n  /**\r\n   * Checks if there are alternatives available\r\n   */\r\n  public hasAlternatives(): boolean {\r\n    return this._value.alternatives.length > 0;\r\n  }\r\n\r\n  /**\r\n   * Gets the top N alternatives\r\n   */\r\n  public getTopAlternatives(n: number): Array<{ value: T; confidence: ConfidenceScore }> {\r\n    return this._value.alternatives.slice(0, n);\r\n  }\r\n\r\n  /**\r\n   * Gets alternatives above a confidence threshold\r\n   */\r\n  public getAlternativesAboveThreshold(threshold: number): Array<{ value: T; confidence: ConfidenceScore }> {\r\n    return this._value.alternatives.filter(alt => alt.confidence.meetsThreshold(threshold));\r\n  }\r\n\r\n  /**\r\n   * Gets the confidence gap between the prediction and the best alternative\r\n   */\r\n  public getConfidenceGap(): number {\r\n    if (this._value.alternatives.length === 0) {\r\n      return this._value.confidence.value;\r\n    }\r\n    \r\n    const bestAlternative = this._value.alternatives[0];\r\n    return this._value.confidence.value - bestAlternative.confidence.value;\r\n  }\r\n\r\n  /**\r\n   * Checks if the prediction is significantly better than alternatives\r\n   */\r\n  public isSignificantlyBetter(threshold: number = 0.1): boolean {\r\n    return this.getConfidenceGap() >= threshold;\r\n  }\r\n\r\n  /**\r\n   * Gets the entropy of the prediction distribution\r\n   */\r\n  public getEntropy(): number {\r\n    const allPredictions = [\r\n      { confidence: this._value.confidence },\r\n      ...this._value.alternatives\r\n    ];\r\n\r\n    let entropy = 0;\r\n    for (const pred of allPredictions) {\r\n      const p = pred.confidence.value;\r\n      if (p > 0) {\r\n        entropy -= p * Math.log2(p);\r\n      }\r\n    }\r\n\r\n    return entropy;\r\n  }\r\n\r\n  /**\r\n   * Checks if the prediction is ambiguous (low confidence gap)\r\n   */\r\n  public isAmbiguous(threshold: number = 0.1): boolean {\r\n    return this.getConfidenceGap() < threshold;\r\n  }\r\n\r\n  /**\r\n   * Gets the age of the prediction in milliseconds\r\n   */\r\n  public getAge(): number {\r\n    return Date.now() - this._value.timestamp.getTime();\r\n  }\r\n\r\n  /**\r\n   * Checks if the prediction is fresh (within time limit)\r\n   */\r\n  public isFresh(maxAgeMs: number): boolean {\r\n    return this.getAge() <= maxAgeMs;\r\n  }\r\n\r\n  /**\r\n   * Adds metadata to the prediction result\r\n   */\r\n  public withMetadata(metadata: Record<string, any>): PredictionResult<T> {\r\n    return new PredictionResult(\r\n      this._value.prediction,\r\n      this._value.confidence,\r\n      this._value.alternatives,\r\n      { ...this._value.metadata, ...metadata },\r\n      this._value.timestamp\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Updates the confidence score\r\n   */\r\n  public withConfidence(confidence: ConfidenceScore): PredictionResult<T> {\r\n    return new PredictionResult(\r\n      this._value.prediction,\r\n      confidence,\r\n      this._value.alternatives,\r\n      this._value.metadata,\r\n      this._value.timestamp\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Adds alternatives to the prediction result\r\n   */\r\n  public withAlternatives(alternatives: Array<{ value: T; confidence: ConfidenceScore }>): PredictionResult<T> {\r\n    const sortedAlternatives = [...alternatives].sort((a, b) => b.confidence.value - a.confidence.value);\r\n    \r\n    return new PredictionResult(\r\n      this._value.prediction,\r\n      this._value.confidence,\r\n      sortedAlternatives,\r\n      this._value.metadata,\r\n      this._value.timestamp\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Compares this prediction result with another\r\n   */\r\n  public compareTo(other: PredictionResult<T>): number {\r\n    return this._value.confidence.value - other._value.confidence.value;\r\n  }\r\n\r\n  /**\r\n   * Checks if this prediction is better than another\r\n   */\r\n  public isBetterThan(other: PredictionResult<T>): boolean {\r\n    return this._value.confidence.isGreaterThan(other._value.confidence);\r\n  }\r\n\r\n  /**\r\n   * Converts to a human-readable string\r\n   */\r\n  public toString(): string {\r\n    const predictionStr = typeof this._value.prediction === 'object' \r\n      ? JSON.stringify(this._value.prediction) \r\n      : String(this._value.prediction);\r\n    \r\n    let result = `Prediction: ${predictionStr} (${this._value.confidence.toString()})`;\r\n    \r\n    if (this._value.alternatives.length > 0) {\r\n      const altStr = this._value.alternatives\r\n        .slice(0, 3) // Show top 3 alternatives\r\n        .map(alt => `${alt.value} (${alt.confidence.toPercentageString()})`)\r\n        .join(', ');\r\n      result += ` | Alternatives: ${altStr}`;\r\n    }\r\n    \r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Converts to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      prediction: this._value.prediction,\r\n      confidence: this._value.confidence.toJSON(),\r\n      alternatives: this._value.alternatives.map(alt => ({\r\n        value: alt.value,\r\n        confidence: alt.confidence.toJSON(),\r\n      })),\r\n      metadata: this._value.metadata,\r\n      timestamp: this._value.timestamp.toISOString(),\r\n      hasHighConfidence: this.hasHighConfidence(),\r\n      hasLowConfidence: this.hasLowConfidence(),\r\n      confidenceGap: this.getConfidenceGap(),\r\n      isAmbiguous: this.isAmbiguous(),\r\n      entropy: this.getEntropy(),\r\n      age: this.getAge(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Creates a PredictionResult from JSON\r\n   */\r\n  public static fromJSON<T>(json: Record<string, any>): PredictionResult<T> {\r\n    const alternatives = json.alternatives?.map((alt: any) => ({\r\n      value: alt.value,\r\n      confidence: ConfidenceScore.fromJSON(alt.confidence),\r\n    })) || [];\r\n\r\n    return new PredictionResult(\r\n      json.prediction,\r\n      ConfidenceScore.fromJSON(json.confidence),\r\n      alternatives,\r\n      json.metadata,\r\n      new Date(json.timestamp)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Combines multiple prediction results using ensemble methods\r\n   */\r\n  public static ensemble<T>(\r\n    results: PredictionResult<T>[],\r\n    method: 'average' | 'weighted' | 'max' = 'average',\r\n    weights?: number[]\r\n  ): PredictionResult<T> {\r\n    if (results.length === 0) {\r\n      throw new Error('Cannot create ensemble from empty results');\r\n    }\r\n\r\n    if (method === 'weighted' && (!weights || weights.length !== results.length)) {\r\n      throw new Error('Weights array must have the same length as results array');\r\n    }\r\n\r\n    // For simplicity, we'll use the most confident prediction as the ensemble result\r\n    // In a real implementation, this would depend on the specific ensemble method\r\n    const sortedResults = [...results].sort((a, b) => b.confidence.value - a.confidence.value);\r\n    const bestResult = sortedResults[0];\r\n\r\n    // Combine metadata from all results\r\n    const combinedMetadata: Record<string, any> = results.reduce((acc, result) => ({ ...acc, ...result.metadata }), {});\r\n    combinedMetadata.ensembleMethod = method;\r\n    combinedMetadata.ensembleSize = results.length;\r\n\r\n    return new PredictionResult(\r\n      bestResult.prediction,\r\n      bestResult.confidence,\r\n      bestResult.alternatives,\r\n      combinedMetadata\r\n    );\r\n  }\r\n}"], "version": 3}