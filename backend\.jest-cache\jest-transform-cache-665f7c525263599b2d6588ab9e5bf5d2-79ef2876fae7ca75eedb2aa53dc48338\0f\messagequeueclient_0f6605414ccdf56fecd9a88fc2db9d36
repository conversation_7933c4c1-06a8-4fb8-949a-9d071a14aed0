431acae619e7ad2de26e2793d24421d5
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var MessageQueueClient_1;
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageQueueClient = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const bull_1 = require("@nestjs/bull");
const bull_2 = require("bull");
/**
 * Message Queue Client for AI Operations
 *
 * Handles asynchronous AI request processing through message queues.
 * Implements queue-based communication patterns, message persistence,
 * and delivery guarantees for reliable AI operations.
 */
let MessageQueueClient = MessageQueueClient_1 = class MessageQueueClient {
    constructor(analysisQueue, trainingQueue, predictionQueue, batchQueue, configService) {
        this.analysisQueue = analysisQueue;
        this.trainingQueue = trainingQueue;
        this.predictionQueue = predictionQueue;
        this.batchQueue = batchQueue;
        this.configService = configService;
        this.logger = new common_1.Logger(MessageQueueClient_1.name);
        this.defaultJobOptions = {
            attempts: this.configService.get('ai.queue.attempts', 3),
            backoff: {
                type: 'exponential',
                delay: this.configService.get('ai.queue.backoffDelay', 2000),
            },
            removeOnComplete: this.configService.get('ai.queue.removeOnComplete', 100),
            removeOnFail: this.configService.get('ai.queue.removeOnFail', 50),
        };
    }
    /**
     * Queues an analysis request for asynchronous processing
     */
    async queueAnalysisRequest(payload, options = {}) {
        this.logger.debug(`Queuing analysis request: ${payload.requestId}`);
        try {
            const jobOptions = this.buildJobOptions(options);
            const job = await this.analysisQueue.add('analyze-data', payload, jobOptions);
            this.logger.debug(`Analysis request queued: ${payload.requestId}, jobId: ${job.id}`);
            return {
                jobId: job.id.toString(),
                requestId: payload.requestId,
                queueName: 'ai-analysis',
                status: 'queued',
                priority: jobOptions.priority || 0,
                delay: jobOptions.delay || 0,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`Failed to queue analysis request: ${payload.requestId}`, error);
            throw new MessageQueueError(`Failed to queue analysis request: ${error.message}`, payload.requestId, error);
        }
    }
    /**
     * Queues a training request for asynchronous processing
     */
    async queueTrainingRequest(payload, options = {}) {
        this.logger.debug(`Queuing training request: ${payload.requestId}`);
        try {
            const jobOptions = this.buildJobOptions({
                ...options,
                // Training jobs typically have higher priority and longer timeout
                priority: options.priority || 10,
                timeout: options.timeout || 3600000, // 1 hour
            });
            const job = await this.trainingQueue.add('train-model', payload, jobOptions);
            this.logger.debug(`Training request queued: ${payload.requestId}, jobId: ${job.id}`);
            return {
                jobId: job.id.toString(),
                requestId: payload.requestId,
                queueName: 'ai-training',
                status: 'queued',
                priority: jobOptions.priority || 0,
                delay: jobOptions.delay || 0,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`Failed to queue training request: ${payload.requestId}`, error);
            throw new MessageQueueError(`Failed to queue training request: ${error.message}`, payload.requestId, error);
        }
    }
    /**
     * Queues a prediction request for asynchronous processing
     */
    async queuePredictionRequest(payload, options = {}) {
        this.logger.debug(`Queuing prediction request: ${payload.requestId}`);
        try {
            const jobOptions = this.buildJobOptions({
                ...options,
                // Prediction jobs typically have high priority and short timeout
                priority: options.priority || 5,
                timeout: options.timeout || 30000, // 30 seconds
            });
            const job = await this.predictionQueue.add('predict', payload, jobOptions);
            this.logger.debug(`Prediction request queued: ${payload.requestId}, jobId: ${job.id}`);
            return {
                jobId: job.id.toString(),
                requestId: payload.requestId,
                queueName: 'ai-prediction',
                status: 'queued',
                priority: jobOptions.priority || 0,
                delay: jobOptions.delay || 0,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`Failed to queue prediction request: ${payload.requestId}`, error);
            throw new MessageQueueError(`Failed to queue prediction request: ${error.message}`, payload.requestId, error);
        }
    }
    /**
     * Queues a batch processing request for asynchronous processing
     */
    async queueBatchRequest(payload, options = {}) {
        this.logger.debug(`Queuing batch request: ${payload.requestId}, items: ${payload.items.length}`);
        try {
            const jobOptions = this.buildJobOptions({
                ...options,
                // Batch jobs typically have lower priority but longer timeout
                priority: options.priority || 1,
                timeout: options.timeout || 1800000, // 30 minutes
            });
            const job = await this.batchQueue.add('process-batch', payload, jobOptions);
            this.logger.debug(`Batch request queued: ${payload.requestId}, jobId: ${job.id}`);
            return {
                jobId: job.id.toString(),
                requestId: payload.requestId,
                queueName: 'ai-batch',
                status: 'queued',
                priority: jobOptions.priority || 0,
                delay: jobOptions.delay || 0,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`Failed to queue batch request: ${payload.requestId}`, error);
            throw new MessageQueueError(`Failed to queue batch request: ${error.message}`, payload.requestId, error);
        }
    }
    /**
     * Gets the status of a queued job
     */
    async getJobStatus(jobId, queueName) {
        this.logger.debug(`Getting job status: ${jobId} from queue: ${queueName}`);
        try {
            const queue = this.getQueueByName(queueName);
            const job = await queue.getJob(jobId);
            if (!job) {
                return {
                    jobId,
                    queueName,
                    status: 'not_found',
                    timestamp: new Date(),
                };
            }
            const state = await job.getState();
            const progress = job.progress();
            const logs = await queue.getJobLogs(jobId);
            return {
                jobId,
                queueName,
                status: this.mapBullStateToStatus(state),
                progress: typeof progress === 'number' ? progress : 0,
                data: job.data,
                result: job.returnvalue,
                error: job.failedReason,
                attempts: job.attemptsMade,
                maxAttempts: job.opts.attempts || this.defaultJobOptions.attempts,
                createdAt: new Date(job.timestamp),
                processedAt: job.processedOn ? new Date(job.processedOn) : undefined,
                finishedAt: job.finishedOn ? new Date(job.finishedOn) : undefined,
                logs: logs.logs,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`Failed to get job status: ${jobId}`, error);
            throw new MessageQueueError(`Failed to get job status: ${error.message}`, jobId, error);
        }
    }
    /**
     * Cancels a queued job
     */
    async cancelJob(jobId, queueName) {
        this.logger.debug(`Cancelling job: ${jobId} from queue: ${queueName}`);
        try {
            const queue = this.getQueueByName(queueName);
            const job = await queue.getJob(jobId);
            if (!job) {
                this.logger.warn(`Job not found for cancellation: ${jobId}`);
                return false;
            }
            const state = await job.getState();
            if (state === 'completed' || state === 'failed') {
                this.logger.warn(`Cannot cancel job in state: ${state}, jobId: ${jobId}`);
                return false;
            }
            await job.remove();
            this.logger.debug(`Job cancelled successfully: ${jobId}`);
            return true;
        }
        catch (error) {
            this.logger.error(`Failed to cancel job: ${jobId}`, error);
            throw new MessageQueueError(`Failed to cancel job: ${error.message}`, jobId, error);
        }
    }
    /**
     * Retries a failed job
     */
    async retryJob(jobId, queueName) {
        this.logger.debug(`Retrying job: ${jobId} from queue: ${queueName}`);
        try {
            const queue = this.getQueueByName(queueName);
            const job = await queue.getJob(jobId);
            if (!job) {
                this.logger.warn(`Job not found for retry: ${jobId}`);
                return false;
            }
            const state = await job.getState();
            if (state !== 'failed') {
                this.logger.warn(`Cannot retry job in state: ${state}, jobId: ${jobId}`);
                return false;
            }
            await job.retry();
            this.logger.debug(`Job retried successfully: ${jobId}`);
            return true;
        }
        catch (error) {
            this.logger.error(`Failed to retry job: ${jobId}`, error);
            throw new MessageQueueError(`Failed to retry job: ${error.message}`, jobId, error);
        }
    }
    /**
     * Gets queue statistics
     */
    async getQueueStats(queueName) {
        this.logger.debug(`Getting queue statistics: ${queueName}`);
        try {
            const queue = this.getQueueByName(queueName);
            const [waiting, active, completed, failed, delayed, paused] = await Promise.all([
                queue.getWaiting(),
                queue.getActive(),
                queue.getCompleted(),
                queue.getFailed(),
                queue.getDelayed(),
                queue.getPaused(),
            ]);
            return {
                queueName,
                counts: {
                    waiting: waiting.length,
                    active: active.length,
                    completed: completed.length,
                    failed: failed.length,
                    delayed: delayed.length,
                    paused: paused.length,
                },
                isPaused: await queue.isPaused(),
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`Failed to get queue statistics: ${queueName}`, error);
            throw new MessageQueueError(`Failed to get queue statistics: ${error.message}`, queueName, error);
        }
    }
    /**
     * Gets jobs by status from a queue
     */
    async getJobsByStatus(queueName, status, start = 0, end = 10) {
        this.logger.debug(`Getting jobs by status: ${status} from queue: ${queueName}`);
        try {
            const queue = this.getQueueByName(queueName);
            let jobs = [];
            switch (status) {
                case 'waiting':
                    jobs = await queue.getWaiting(start, end);
                    break;
                case 'active':
                    jobs = await queue.getActive(start, end);
                    break;
                case 'completed':
                    jobs = await queue.getCompleted(start, end);
                    break;
                case 'failed':
                    jobs = await queue.getFailed(start, end);
                    break;
                case 'delayed':
                    jobs = await queue.getDelayed(start, end);
                    break;
                case 'paused':
                    jobs = await queue.getPaused(start, end);
                    break;
                default:
                    throw new Error(`Invalid job status: ${status}`);
            }
            return Promise.all(jobs.map(async (job) => ({
                jobId: job.id.toString(),
                queueName,
                status: this.mapBullStateToStatus(await job.getState()),
                data: job.data,
                result: job.returnvalue,
                error: job.failedReason,
                progress: typeof job.progress() === 'number' ? job.progress() : 0,
                attempts: job.attemptsMade,
                maxAttempts: job.opts.attempts || this.defaultJobOptions.attempts,
                createdAt: new Date(job.timestamp),
                processedAt: job.processedOn ? new Date(job.processedOn) : undefined,
                finishedAt: job.finishedOn ? new Date(job.finishedOn) : undefined,
            })));
        }
        catch (error) {
            this.logger.error(`Failed to get jobs by status: ${status} from queue: ${queueName}`, error);
            throw new MessageQueueError(`Failed to get jobs by status: ${error.message}`, queueName, error);
        }
    }
    /**
     * Cleans up old jobs from a queue
     */
    async cleanQueue(queueName, grace = 24 * 60 * 60 * 1000, // 24 hours
    status = 'completed') {
        this.logger.debug(`Cleaning queue: ${queueName}, grace: ${grace}ms, status: ${status}`);
        try {
            const queue = this.getQueueByName(queueName);
            const cleanedJobs = await queue.clean(grace, status);
            this.logger.debug(`Cleaned ${cleanedJobs.length} jobs from queue: ${queueName}`);
            return cleanedJobs.length;
        }
        catch (error) {
            this.logger.error(`Failed to clean queue: ${queueName}`, error);
            throw new MessageQueueError(`Failed to clean queue: ${error.message}`, queueName, error);
        }
    }
    /**
     * Pauses a queue
     */
    async pauseQueue(queueName) {
        this.logger.debug(`Pausing queue: ${queueName}`);
        try {
            const queue = this.getQueueByName(queueName);
            await queue.pause();
            this.logger.debug(`Queue paused: ${queueName}`);
        }
        catch (error) {
            this.logger.error(`Failed to pause queue: ${queueName}`, error);
            throw new MessageQueueError(`Failed to pause queue: ${error.message}`, queueName, error);
        }
    }
    /**
     * Resumes a paused queue
     */
    async resumeQueue(queueName) {
        this.logger.debug(`Resuming queue: ${queueName}`);
        try {
            const queue = this.getQueueByName(queueName);
            await queue.resume();
            this.logger.debug(`Queue resumed: ${queueName}`);
        }
        catch (error) {
            this.logger.error(`Failed to resume queue: ${queueName}`, error);
            throw new MessageQueueError(`Failed to resume queue: ${error.message}`, queueName, error);
        }
    }
    /**
     * Adds a job with scheduled execution
     */
    async scheduleJob(queueName, jobType, payload, scheduleTime, options = {}) {
        this.logger.debug(`Scheduling job: ${jobType} in queue: ${queueName} for: ${scheduleTime}`);
        try {
            const queue = this.getQueueByName(queueName);
            const delay = scheduleTime.getTime() - Date.now();
            if (delay < 0) {
                throw new Error('Schedule time must be in the future');
            }
            const jobOptions = this.buildJobOptions({
                ...options,
                delay,
            });
            const job = await queue.add(jobType, payload, jobOptions);
            this.logger.debug(`Job scheduled: ${jobType}, jobId: ${job.id}, delay: ${delay}ms`);
            return {
                jobId: job.id.toString(),
                requestId: payload.requestId || this.generateRequestId(),
                queueName,
                status: 'delayed',
                priority: jobOptions.priority || 0,
                delay: jobOptions.delay || 0,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`Failed to schedule job: ${jobType} in queue: ${queueName}`, error);
            throw new MessageQueueError(`Failed to schedule job: ${error.message}`, queueName, error);
        }
    }
    // Private helper methods
    getQueueByName(queueName) {
        switch (queueName) {
            case 'ai-analysis':
                return this.analysisQueue;
            case 'ai-training':
                return this.trainingQueue;
            case 'ai-prediction':
                return this.predictionQueue;
            case 'ai-batch':
                return this.batchQueue;
            default:
                throw new Error(`Unknown queue name: ${queueName}`);
        }
    }
    buildJobOptions(options) {
        return {
            ...this.defaultJobOptions,
            priority: options.priority,
            delay: options.delay,
            timeout: options.timeout,
            attempts: options.attempts,
            backoff: options.backoff ? {
                type: options.backoff.type || 'exponential',
                delay: options.backoff.delay || 2000,
            } : this.defaultJobOptions.backoff,
            removeOnComplete: options.removeOnComplete ?? this.defaultJobOptions.removeOnComplete,
            removeOnFail: options.removeOnFail ?? this.defaultJobOptions.removeOnFail,
            jobId: options.jobId,
        };
    }
    mapBullStateToStatus(state) {
        switch (state) {
            case 'waiting':
                return 'waiting';
            case 'active':
                return 'active';
            case 'completed':
                return 'completed';
            case 'failed':
                return 'failed';
            case 'delayed':
                return 'delayed';
            case 'paused':
                return 'paused';
            default:
                return 'unknown';
        }
    }
    generateRequestId() {
        return `mq-req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
};
exports.MessageQueueClient = MessageQueueClient;
exports.MessageQueueClient = MessageQueueClient = MessageQueueClient_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, bull_1.InjectQueue)('ai-analysis')),
    __param(1, (0, bull_1.InjectQueue)('ai-training')),
    __param(2, (0, bull_1.InjectQueue)('ai-prediction')),
    __param(3, (0, bull_1.InjectQueue)('ai-batch')),
    __metadata("design:paramtypes", [typeof (_a = typeof bull_2.Queue !== "undefined" && bull_2.Queue) === "function" ? _a : Object, typeof (_b = typeof bull_2.Queue !== "undefined" && bull_2.Queue) === "function" ? _b : Object, typeof (_c = typeof bull_2.Queue !== "undefined" && bull_2.Queue) === "function" ? _c : Object, typeof (_d = typeof bull_2.Queue !== "undefined" && bull_2.Queue) === "function" ? _d : Object, typeof (_e = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _e : Object])
], MessageQueueClient);
class MessageQueueError extends Error {
    constructor(message, identifier, originalError) {
        super(message);
        this.identifier = identifier;
        this.originalError = originalError;
        this.name = 'MessageQueueError';
    }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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