{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\value-objects\\__tests__\\prediction-result.value-object.spec.ts", "mappings": ";;AAAA,sFAAqE;AACrE,oFAAmE;AAEnE,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;IAC7C,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,UAAU,GAAG,IAAI,+CAAe,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,MAAM,GAAG,IAAI,iDAAgB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;YAE5D,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACpC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,UAAU,GAAG,IAAI,+CAAe,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,YAAY,GAAG;gBACnB,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,+CAAe,CAAC,IAAI,CAAC,EAAE;aAC7D,CAAC;YACF,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC;YACzC,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAE7B,MAAM,MAAM,GAAG,IAAI,iDAAgB,CAAC,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;YAE/F,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,UAAU,GAAG,IAAI,+CAAe,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,iDAAgB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,8CAA8C,CAAC,CAAC;YAC7G,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,iDAAgB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,8CAA8C,CAAC,CAAC;QACpH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,iDAAgB,CAAC,UAAU,EAAE,IAAW,CAAC,CAAC,CAAC,OAAO,CAAC,+CAA+C,CAAC,CAAC;QACvH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,UAAU,GAAG,IAAI,+CAAe,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,iDAAgB,CAAC,UAAU,EAAE,UAAU,EAAE,SAAgB,CAAC,CAAC;iBACzE,OAAO,CAAC,+BAA+B,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,UAAU,GAAG,IAAI,+CAAe,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,iDAAgB,CAAC,UAAU,EAAE,UAAU,EAAE,EAAE,EAAE,EAAE,EAAE,SAAgB,CAAC,CAAC;iBACjF,OAAO,CAAC,mCAAmC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;YAC9D,MAAM,UAAU,GAAG,IAAI,+CAAe,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,mBAAmB,GAAG,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,qBAAqB;YAE1E,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,iDAAgB,CAAC,UAAU,EAAE,UAAU,EAAE,mBAA0B,CAAC,CAAC;iBACnF,OAAO,CAAC,4DAA4D,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,UAAU,GAAG,IAAI,+CAAe,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,oBAAoB,GAAG;gBAC3B,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,+CAAe,CAAC,GAAG,CAAC,EAAE;gBAC3D,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,+CAAe,CAAC,IAAI,CAAC,EAAE;aAC5D,CAAC;YAEF,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,iDAAgB,CAAC,UAAU,EAAE,UAAU,EAAE,oBAAoB,CAAC,CAAC;iBAC7E,OAAO,CAAC,+DAA+D,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,MAAM,GAAG,iDAAgB,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;YAEzD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,MAAM,GAAG,iDAAgB,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,EAAE;gBACjE,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,EAAE;gBACrC,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE;aACxC,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,0BAA0B;YAChF,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,MAAM,GAAG,iDAAgB,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAEnD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,MAAM,MAAM,GAAG,iDAAgB,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;YAElE,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,WAAW,GAAG;gBAClB,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE;gBACjC,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE;gBACjC,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,EAAE;aACnC,CAAC;YAEF,MAAM,MAAM,GAAG,iDAAgB,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;YAExD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,CAAC,GAAG,EAAE,CAAC,iDAAgB,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,qCAAqC,CAAC,CAAC;QAC/F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,MAAM,GAAG,iDAAgB,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAEtD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,MAAM,GAAG,iDAAgB,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;YAE5E,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;YAC5D,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,cAAc,GAAG,iDAAgB,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YAChE,MAAM,aAAa,GAAG,iDAAgB,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YAE/D,MAAM,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,cAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,cAAc,GAAG,iDAAgB,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YAChE,MAAM,aAAa,GAAG,iDAAgB,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YAE/D,MAAM,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,MAAM,GAAG,iDAAgB,CAAC,gBAAgB,CAAC,UAAU,EAAE,GAAG,EAAE;gBAChE,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,EAAE;aACvC,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,MAAM,GAAG,iDAAgB,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YACxD,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,WAAW,GAAG,iDAAgB,CAAC,gBAAgB,CAAC,UAAU,EAAE,GAAG,EAAE;gBACrE,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,EAAE;aACvC,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,iDAAgB,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,EAAE;gBACzE,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE;aACxC,CAAC,CAAC;YAEH,MAAM,CAAC,WAAW,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,CAAC,cAAc,CAAC,qBAAqB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,KAAK,GAAG,iDAAgB,CAAC,gBAAgB,CAAC,UAAU,EAAE,GAAG,EAAE;gBAC/D,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,EAAE;aACvC,CAAC,CAAC;YAEH,MAAM,SAAS,GAAG,iDAAgB,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,EAAE;gBACpE,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE;aACxC,CAAC,CAAC;YAEH,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,QAAQ,GAAG,iDAAgB,CAAC,gBAAgB,CAAC,UAAU,EAAE,GAAG,EAAE;gBAClE,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,EAAE;aACvC,CAAC,CAAC;YACH,MAAM,WAAW,GAAG,iDAAgB,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YAE7D,MAAM,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,MAAM,GAAG,iDAAgB,CAAC,gBAAgB,CAAC,UAAU,EAAE,GAAG,EAAE;gBAChE,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE;gBACvC,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,EAAE;aACvC,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,MAAM,GAAG,iDAAgB,CAAC,gBAAgB,CAAC,UAAU,EAAE,GAAG,EAAE;gBAChE,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,EAAE;gBACtC,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,EAAE;aACtC,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,MAAM,GAAG,iDAAgB,CAAC,gBAAgB,CAAC,UAAU,EAAE,GAAG,EAAE;gBAChE,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE;gBACvC,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,EAAE;aACvC,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,MAAM,CAAC,6BAA6B,CAAC,GAAG,CAAC,CAAC;YACjE,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,MAAM,GAAG,iDAAgB,CAAC,gBAAgB,CAAC,UAAU,EAAE,GAAG,EAAE;gBAChE,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,EAAE;aACvC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;YACpC,MAAM,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,oCAAoC;QACvE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,gBAAgB;YAC9D,MAAM,MAAM,GAAG,IAAI,iDAAgB,CAAC,UAAU,EAAE,IAAI,+CAAe,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;YAE5F,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,MAAM,CAAC,GAAG,CAAC,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uBAAuB,EAAE,GAAG,EAAE;YAC/B,MAAM,YAAY,GAAG,iDAAgB,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YAC9D,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,iBAAiB;YAC/D,MAAM,SAAS,GAAG,IAAI,iDAAgB,CAAC,UAAU,EAAE,IAAI,+CAAe,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;YAE9F,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,QAAQ,GAAG,iDAAgB,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YAC1D,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;YAE9D,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACtC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,QAAQ,GAAG,iDAAgB,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YAC1D,MAAM,aAAa,GAAG,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC;YAC/C,MAAM,OAAO,GAAG,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAEvD,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC5C,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,QAAQ,GAAG,iDAAgB,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YAC1D,MAAM,YAAY,GAAG,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,+CAAe,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACnF,MAAM,QAAQ,GAAG,QAAQ,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;YAEzD,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC9C,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,OAAO,GAAG,iDAAgB,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YACzD,MAAM,OAAO,GAAG,iDAAgB,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YAEzD,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACtD,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,MAAM,GAAG,iDAAgB,CAAC,gBAAgB,CAAC,UAAU,EAAE,GAAG,EAAE;gBAChE,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE;gBACvC,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,EAAE;aACvC,CAAC,CAAC;YAEH,MAAM,GAAG,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC9B,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;YAC9C,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YAC/B,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YACvC,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,gBAAgB,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;YAClE,MAAM,MAAM,GAAG,iDAAgB,CAAC,MAAM,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;YAE9D,MAAM,GAAG,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC9B,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YACrC,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,MAAM,GAAG,iDAAgB,CAAC,gBAAgB,CAAC,UAAU,EAAE,GAAG,EAAE;gBAChE,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,EAAE;aACvC,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAE7B,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,IAAI,GAAG;gBACX,UAAU,EAAE,UAAU;gBACtB,UAAU,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE;gBAC1B,YAAY,EAAE;oBACZ,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE;iBAClD;gBACD,QAAQ,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE;gBAC3B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;YAEF,MAAM,MAAM,GAAG,iDAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAE/C,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3C,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,OAAO,GAAG;gBACd,iDAAgB,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC;gBACxC,iDAAgB,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC;gBACxC,iDAAgB,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC;aACzC,CAAC;YAEF,MAAM,QAAQ,GAAG,iDAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAEpD,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,qBAAqB;YACnE,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACzD,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,CAAC,GAAG,EAAE,CAAC,iDAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,2CAA2C,CAAC,CAAC;QACnG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,OAAO,GAAG;gBACd,iDAAgB,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC;gBACxC,iDAAgB,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC;aACzC,CAAC;YACF,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAE3B,MAAM,QAAQ,GAAG,iDAAgB,CAAC,QAAQ,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;YACzE,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,OAAO,GAAG,CAAC,iDAAgB,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC;YAC3D,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAE3B,MAAM,CAAC,GAAG,EAAE,CAAC,iDAAgB,CAAC,QAAQ,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;iBAClE,OAAO,CAAC,0DAA0D,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,YAAY,GAAG,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,+CAAe,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACnF,MAAM,QAAQ,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;YACnC,MAAM,MAAM,GAAG,IAAI,iDAAgB,CAAC,UAAU,EAAE,IAAI,+CAAe,CAAC,GAAG,CAAC,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;YAElG,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;YACzC,MAAM,gBAAgB,GAAG,MAAM,CAAC,QAAQ,CAAC;YAEzC,YAAY,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,+CAAe,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC9E,gBAAgB,CAAC,QAAQ,GAAG,IAAI,CAAC;YAEjC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\value-objects\\__tests__\\prediction-result.value-object.spec.ts"], "sourcesContent": ["import { PredictionResult } from '../prediction-result.value-object';\r\nimport { ConfidenceScore } from '../confidence-score.value-object';\r\n\r\ndescribe('PredictionResult Value Object', () => {\r\n  describe('Construction', () => {\r\n    it('should create a valid prediction result', () => {\r\n      const confidence = new ConfidenceScore(0.85);\r\n      const result = new PredictionResult('positive', confidence);\r\n      \r\n      expect(result.prediction).toBe('positive');\r\n      expect(result.confidence.equals(confidence)).toBe(true);\r\n      expect(result.alternatives).toEqual([]);\r\n      expect(result.metadata).toEqual({});\r\n      expect(result.timestamp).toBeInstanceOf(Date);\r\n    });\r\n\r\n    it('should create with alternatives and metadata', () => {\r\n      const confidence = new ConfidenceScore(0.85);\r\n      const alternatives = [\r\n        { value: 'negative', confidence: new ConfidenceScore(0.15) }\r\n      ];\r\n      const metadata = { model: 'test-model' };\r\n      const timestamp = new Date();\r\n      \r\n      const result = new PredictionResult('positive', confidence, alternatives, metadata, timestamp);\r\n      \r\n      expect(result.prediction).toBe('positive');\r\n      expect(result.alternatives).toHaveLength(1);\r\n      expect(result.metadata.model).toBe('test-model');\r\n      expect(result.timestamp).toBe(timestamp);\r\n    });\r\n\r\n    it('should throw error for null prediction', () => {\r\n      const confidence = new ConfidenceScore(0.85);\r\n      expect(() => new PredictionResult(null, confidence)).toThrow('Prediction value cannot be null or undefined');\r\n      expect(() => new PredictionResult(undefined, confidence)).toThrow('Prediction value cannot be null or undefined');\r\n    });\r\n\r\n    it('should throw error for invalid confidence', () => {\r\n      expect(() => new PredictionResult('positive', 0.85 as any)).toThrow('Confidence must be a ConfidenceScore instance');\r\n    });\r\n\r\n    it('should throw error for invalid alternatives', () => {\r\n      const confidence = new ConfidenceScore(0.85);\r\n      expect(() => new PredictionResult('positive', confidence, 'invalid' as any))\r\n        .toThrow('Alternatives must be an array');\r\n    });\r\n\r\n    it('should throw error for invalid timestamp', () => {\r\n      const confidence = new ConfidenceScore(0.85);\r\n      expect(() => new PredictionResult('positive', confidence, [], {}, 'invalid' as any))\r\n        .toThrow('Timestamp must be a Date instance');\r\n    });\r\n\r\n    it('should throw error for invalid alternative structure', () => {\r\n      const confidence = new ConfidenceScore(0.85);\r\n      const invalidAlternatives = [{ value: 'negative' }]; // Missing confidence\r\n      \r\n      expect(() => new PredictionResult('positive', confidence, invalidAlternatives as any))\r\n        .toThrow('Each alternative must have value and confidence properties');\r\n    });\r\n\r\n    it('should throw error for unsorted alternatives', () => {\r\n      const confidence = new ConfidenceScore(0.85);\r\n      const unsortedAlternatives = [\r\n        { value: 'negative', confidence: new ConfidenceScore(0.1) },\r\n        { value: 'neutral', confidence: new ConfidenceScore(0.15) }\r\n      ];\r\n      \r\n      expect(() => new PredictionResult('positive', confidence, unsortedAlternatives))\r\n        .toThrow('Alternatives must be sorted by confidence in descending order');\r\n    });\r\n  });\r\n\r\n  describe('Factory Methods', () => {\r\n    it('should create simple prediction result', () => {\r\n      const result = PredictionResult.simple('positive', 0.85);\r\n      \r\n      expect(result.prediction).toBe('positive');\r\n      expect(result.confidence.value).toBe(0.85);\r\n      expect(result.alternatives).toEqual([]);\r\n    });\r\n\r\n    it('should create prediction result with alternatives', () => {\r\n      const result = PredictionResult.withAlternatives('positive', 0.85, [\r\n        { value: 'neutral', confidence: 0.1 },\r\n        { value: 'negative', confidence: 0.05 }\r\n      ]);\r\n      \r\n      expect(result.prediction).toBe('positive');\r\n      expect(result.alternatives).toHaveLength(2);\r\n      expect(result.alternatives[0].value).toBe('neutral'); // Higher confidence first\r\n      expect(result.alternatives[1].value).toBe('negative');\r\n    });\r\n\r\n    it('should create binary classification result', () => {\r\n      const result = PredictionResult.binary(true, 0.85);\r\n      \r\n      expect(result.prediction).toBe('positive');\r\n      expect(result.alternatives).toHaveLength(1);\r\n      expect(result.alternatives[0].value).toBe('negative');\r\n      expect(result.alternatives[0].confidence.value).toBe(0.15);\r\n    });\r\n\r\n    it('should create binary classification result with custom labels', () => {\r\n      const result = PredictionResult.binary(false, 0.7, 'spam', 'ham');\r\n      \r\n      expect(result.prediction).toBe('ham');\r\n      expect(result.alternatives[0].value).toBe('spam');\r\n      expect(result.alternatives[0].confidence.value).toBe(0.3);\r\n    });\r\n\r\n    it('should create multi-class classification result', () => {\r\n      const predictions = [\r\n        { value: 'cat', confidence: 0.6 },\r\n        { value: 'dog', confidence: 0.3 },\r\n        { value: 'bird', confidence: 0.1 }\r\n      ];\r\n      \r\n      const result = PredictionResult.multiClass(predictions);\r\n      \r\n      expect(result.prediction).toBe('cat');\r\n      expect(result.alternatives).toHaveLength(2);\r\n      expect(result.alternatives[0].value).toBe('dog');\r\n      expect(result.alternatives[1].value).toBe('bird');\r\n    });\r\n\r\n    it('should throw error for empty predictions in multi-class', () => {\r\n      expect(() => PredictionResult.multiClass([])).toThrow('At least one prediction is required');\r\n    });\r\n\r\n    it('should create regression result', () => {\r\n      const result = PredictionResult.regression(42.5, 0.9);\r\n      \r\n      expect(result.prediction).toBe(42.5);\r\n      expect(result.confidence.value).toBe(0.9);\r\n    });\r\n\r\n    it('should create regression result with range', () => {\r\n      const result = PredictionResult.regression(42.5, 0.9, { min: 40, max: 50 });\r\n      \r\n      expect(result.prediction).toBe(42.5);\r\n      expect(result.metadata.range).toEqual({ min: 40, max: 50 });\r\n      expect(result.metadata.withinRange).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('Confidence Analysis', () => {\r\n    it('should check high confidence', () => {\r\n      const highConfidence = PredictionResult.simple('positive', 0.9);\r\n      const lowConfidence = PredictionResult.simple('positive', 0.5);\r\n      \r\n      expect(highConfidence.hasHighConfidence()).toBe(true);\r\n      expect(lowConfidence.hasHighConfidence()).toBe(false);\r\n      expect(highConfidence.hasHighConfidence(0.95)).toBe(false);\r\n    });\r\n\r\n    it('should check low confidence', () => {\r\n      const highConfidence = PredictionResult.simple('positive', 0.9);\r\n      const lowConfidence = PredictionResult.simple('positive', 0.2);\r\n      \r\n      expect(lowConfidence.hasLowConfidence()).toBe(true);\r\n      expect(highConfidence.hasLowConfidence()).toBe(false);\r\n      expect(lowConfidence.hasLowConfidence(0.1)).toBe(false);\r\n    });\r\n\r\n    it('should calculate confidence gap', () => {\r\n      const result = PredictionResult.withAlternatives('positive', 0.8, [\r\n        { value: 'negative', confidence: 0.2 }\r\n      ]);\r\n      \r\n      expect(result.getConfidenceGap()).toBe(0.6);\r\n    });\r\n\r\n    it('should handle no alternatives in confidence gap', () => {\r\n      const result = PredictionResult.simple('positive', 0.8);\r\n      expect(result.getConfidenceGap()).toBe(0.8);\r\n    });\r\n\r\n    it('should check if significantly better', () => {\r\n      const significant = PredictionResult.withAlternatives('positive', 0.8, [\r\n        { value: 'negative', confidence: 0.2 }\r\n      ]);\r\n      \r\n      const notSignificant = PredictionResult.withAlternatives('positive', 0.55, [\r\n        { value: 'negative', confidence: 0.45 }\r\n      ]);\r\n      \r\n      expect(significant.isSignificantlyBetter()).toBe(true);\r\n      expect(notSignificant.isSignificantlyBetter()).toBe(false);\r\n    });\r\n\r\n    it('should check if ambiguous', () => {\r\n      const clear = PredictionResult.withAlternatives('positive', 0.8, [\r\n        { value: 'negative', confidence: 0.2 }\r\n      ]);\r\n      \r\n      const ambiguous = PredictionResult.withAlternatives('positive', 0.55, [\r\n        { value: 'negative', confidence: 0.45 }\r\n      ]);\r\n      \r\n      expect(clear.isAmbiguous()).toBe(false);\r\n      expect(ambiguous.isAmbiguous()).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('Alternatives Management', () => {\r\n    it('should check if has alternatives', () => {\r\n      const withAlts = PredictionResult.withAlternatives('positive', 0.8, [\r\n        { value: 'negative', confidence: 0.2 }\r\n      ]);\r\n      const withoutAlts = PredictionResult.simple('positive', 0.8);\r\n      \r\n      expect(withAlts.hasAlternatives()).toBe(true);\r\n      expect(withoutAlts.hasAlternatives()).toBe(false);\r\n    });\r\n\r\n    it('should get alternative count', () => {\r\n      const result = PredictionResult.withAlternatives('positive', 0.8, [\r\n        { value: 'negative', confidence: 0.15 },\r\n        { value: 'neutral', confidence: 0.05 }\r\n      ]);\r\n      \r\n      expect(result.getAlternativeCount()).toBe(2);\r\n    });\r\n\r\n    it('should get top alternatives', () => {\r\n      const result = PredictionResult.withAlternatives('positive', 0.7, [\r\n        { value: 'negative', confidence: 0.2 },\r\n        { value: 'neutral', confidence: 0.1 }\r\n      ]);\r\n      \r\n      const top1 = result.getTopAlternatives(1);\r\n      expect(top1).toHaveLength(1);\r\n      expect(top1[0].value).toBe('negative');\r\n    });\r\n\r\n    it('should get alternatives above threshold', () => {\r\n      const result = PredictionResult.withAlternatives('positive', 0.6, [\r\n        { value: 'negative', confidence: 0.25 },\r\n        { value: 'neutral', confidence: 0.15 }\r\n      ]);\r\n      \r\n      const aboveThreshold = result.getAlternativesAboveThreshold(0.2);\r\n      expect(aboveThreshold).toHaveLength(1);\r\n      expect(aboveThreshold[0].value).toBe('negative');\r\n    });\r\n  });\r\n\r\n  describe('Entropy and Uncertainty', () => {\r\n    it('should calculate entropy', () => {\r\n      const result = PredictionResult.withAlternatives('positive', 0.8, [\r\n        { value: 'negative', confidence: 0.2 }\r\n      ]);\r\n      \r\n      const entropy = result.getEntropy();\r\n      expect(entropy).toBeGreaterThan(0);\r\n      expect(entropy).toBeLessThan(1); // Binary case should be less than 1\r\n    });\r\n  });\r\n\r\n  describe('Time-based Operations', () => {\r\n    it('should get age of prediction', () => {\r\n      const pastTime = new Date(Date.now() - 5000); // 5 seconds ago\r\n      const result = new PredictionResult('positive', new ConfidenceScore(0.8), [], {}, pastTime);\r\n      \r\n      const age = result.getAge();\r\n      expect(age).toBeGreaterThanOrEqual(5000);\r\n    });\r\n\r\n    it('should check if fresh', () => {\r\n      const recentResult = PredictionResult.simple('positive', 0.8);\r\n      const oldTime = new Date(Date.now() - 10000); // 10 seconds ago\r\n      const oldResult = new PredictionResult('positive', new ConfidenceScore(0.8), [], {}, oldTime);\r\n      \r\n      expect(recentResult.isFresh(5000)).toBe(true);\r\n      expect(oldResult.isFresh(5000)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('Immutable Operations', () => {\r\n    it('should add metadata without mutating original', () => {\r\n      const original = PredictionResult.simple('positive', 0.8);\r\n      const withMetadata = original.withMetadata({ model: 'test' });\r\n      \r\n      expect(original.metadata).toEqual({});\r\n      expect(withMetadata.metadata.model).toBe('test');\r\n    });\r\n\r\n    it('should update confidence without mutating original', () => {\r\n      const original = PredictionResult.simple('positive', 0.8);\r\n      const newConfidence = new ConfidenceScore(0.9);\r\n      const updated = original.withConfidence(newConfidence);\r\n      \r\n      expect(original.confidence.value).toBe(0.8);\r\n      expect(updated.confidence.value).toBe(0.9);\r\n    });\r\n\r\n    it('should add alternatives without mutating original', () => {\r\n      const original = PredictionResult.simple('positive', 0.8);\r\n      const alternatives = [{ value: 'negative', confidence: new ConfidenceScore(0.2) }];\r\n      const withAlts = original.withAlternatives(alternatives);\r\n      \r\n      expect(original.alternatives).toHaveLength(0);\r\n      expect(withAlts.alternatives).toHaveLength(1);\r\n    });\r\n  });\r\n\r\n  describe('Comparison', () => {\r\n    it('should compare prediction results', () => {\r\n      const result1 = PredictionResult.simple('positive', 0.9);\r\n      const result2 = PredictionResult.simple('negative', 0.7);\r\n      \r\n      expect(result1.compareTo(result2)).toBeGreaterThan(0);\r\n      expect(result2.compareTo(result1)).toBeLessThan(0);\r\n      expect(result1.isBetterThan(result2)).toBe(true);\r\n      expect(result2.isBetterThan(result1)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('String Representation', () => {\r\n    it('should convert to string', () => {\r\n      const result = PredictionResult.withAlternatives('positive', 0.8, [\r\n        { value: 'negative', confidence: 0.15 },\r\n        { value: 'neutral', confidence: 0.05 }\r\n      ]);\r\n      \r\n      const str = result.toString();\r\n      expect(str).toContain('Prediction: positive');\r\n      expect(str).toContain('80.0%');\r\n      expect(str).toContain('Alternatives:');\r\n      expect(str).toContain('negative');\r\n    });\r\n\r\n    it('should handle object predictions in string', () => {\r\n      const objectPrediction = { class: 'cat', bbox: [10, 20, 30, 40] };\r\n      const result = PredictionResult.simple(objectPrediction, 0.8);\r\n      \r\n      const str = result.toString();\r\n      expect(str).toContain('Prediction:');\r\n      expect(str).toContain('cat');\r\n    });\r\n  });\r\n\r\n  describe('JSON Serialization', () => {\r\n    it('should serialize to JSON', () => {\r\n      const result = PredictionResult.withAlternatives('positive', 0.8, [\r\n        { value: 'negative', confidence: 0.2 }\r\n      ]);\r\n      \r\n      const json = result.toJSON();\r\n      \r\n      expect(json.prediction).toBe('positive');\r\n      expect(json.confidence.value).toBe(0.8);\r\n      expect(json.alternatives).toHaveLength(1);\r\n      expect(json.hasHighConfidence).toBe(true);\r\n      expect(json.confidenceGap).toBe(0.6);\r\n      expect(json.isAmbiguous).toBe(false);\r\n      expect(json.entropy).toBeGreaterThan(0);\r\n      expect(json.age).toBeGreaterThanOrEqual(0);\r\n    });\r\n\r\n    it('should deserialize from JSON', () => {\r\n      const json = {\r\n        prediction: 'positive',\r\n        confidence: { value: 0.8 },\r\n        alternatives: [\r\n          { value: 'negative', confidence: { value: 0.2 } }\r\n        ],\r\n        metadata: { model: 'test' },\r\n        timestamp: new Date().toISOString()\r\n      };\r\n      \r\n      const result = PredictionResult.fromJSON(json);\r\n      \r\n      expect(result.prediction).toBe('positive');\r\n      expect(result.confidence.value).toBe(0.8);\r\n      expect(result.alternatives).toHaveLength(1);\r\n      expect(result.metadata.model).toBe('test');\r\n    });\r\n  });\r\n\r\n  describe('Ensemble Methods', () => {\r\n    it('should create ensemble from multiple results', () => {\r\n      const results = [\r\n        PredictionResult.simple('positive', 0.8),\r\n        PredictionResult.simple('positive', 0.7),\r\n        PredictionResult.simple('negative', 0.6)\r\n      ];\r\n      \r\n      const ensemble = PredictionResult.ensemble(results);\r\n      \r\n      expect(ensemble.prediction).toBe('positive'); // Highest confidence\r\n      expect(ensemble.metadata.ensembleMethod).toBe('average');\r\n      expect(ensemble.metadata.ensembleSize).toBe(3);\r\n    });\r\n\r\n    it('should throw error for empty results in ensemble', () => {\r\n      expect(() => PredictionResult.ensemble([])).toThrow('Cannot create ensemble from empty results');\r\n    });\r\n\r\n    it('should handle weighted ensemble', () => {\r\n      const results = [\r\n        PredictionResult.simple('positive', 0.8),\r\n        PredictionResult.simple('negative', 0.7)\r\n      ];\r\n      const weights = [0.6, 0.4];\r\n      \r\n      const ensemble = PredictionResult.ensemble(results, 'weighted', weights);\r\n      expect(ensemble.metadata.ensembleMethod).toBe('weighted');\r\n    });\r\n\r\n    it('should throw error for mismatched weights in ensemble', () => {\r\n      const results = [PredictionResult.simple('positive', 0.8)];\r\n      const weights = [0.6, 0.4];\r\n      \r\n      expect(() => PredictionResult.ensemble(results, 'weighted', weights))\r\n        .toThrow('Weights array must have the same length as results array');\r\n    });\r\n  });\r\n\r\n  describe('Immutability', () => {\r\n    it('should return copies of arrays and objects', () => {\r\n      const alternatives = [{ value: 'negative', confidence: new ConfidenceScore(0.2) }];\r\n      const metadata = { model: 'test' };\r\n      const result = new PredictionResult('positive', new ConfidenceScore(0.8), alternatives, metadata);\r\n      \r\n      const returnedAlts = result.alternatives;\r\n      const returnedMetadata = result.metadata;\r\n      \r\n      returnedAlts.push({ value: 'neutral', confidence: new ConfidenceScore(0.1) });\r\n      returnedMetadata.modified = true;\r\n      \r\n      expect(result.alternatives).toHaveLength(1);\r\n      expect(result.metadata.modified).toBeUndefined();\r\n    });\r\n  });\r\n});"], "version": 3}