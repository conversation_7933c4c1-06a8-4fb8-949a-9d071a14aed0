f94caf557bd56b49f0d500a2b1413f05
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalysisResultAggregate = void 0;
const analysis_result_entity_1 = require("../entities/analysis-result.entity");
const analysis_result_factory_1 = require("../factories/analysis-result.factory");
class AnalysisResultAggregate {
    constructor(results = []) {
        this.results = new Map();
        results.forEach(result => {
            this.results.set(result.id.toString(), result);
        });
    }
    /**
     * Creates a new analysis result and adds it to the aggregate
     */
    createAnalysisResult(request, id) {
        // Check for duplicate request IDs
        const existingResult = this.findByRequestId(request.requestId);
        if (existingResult) {
            throw new Error(`Analysis result with request ID '${request.requestId}' already exists`);
        }
        const result = analysis_result_factory_1.AnalysisResultFactory.create(request, id);
        this.results.set(result.id.toString(), result);
        return result;
    }
    /**
     * Adds an existing analysis result to the aggregate
     */
    addAnalysisResult(result) {
        if (this.results.has(result.id.toString())) {
            throw new Error(`Analysis result with ID '${result.id.toString()}' already exists in aggregate`);
        }
        this.results.set(result.id.toString(), result);
    }
    /**
     * Removes an analysis result from the aggregate
     */
    removeAnalysisResult(resultId) {
        const result = this.results.get(resultId.toString());
        if (!result) {
            throw new Error(`Analysis result with ID '${resultId.toString()}' not found`);
        }
        // Cancel the analysis if it's still processing
        if (!result.isTerminal()) {
            result.cancel();
        }
        this.results.delete(resultId.toString());
    }
    /**
     * Gets an analysis result by ID
     */
    getAnalysisResult(resultId) {
        return this.results.get(resultId.toString());
    }
    /**
     * Gets all analysis results
     */
    getAllAnalysisResults() {
        return Array.from(this.results.values());
    }
    /**
     * Finds analysis results by query criteria
     */
    findAnalysisResults(query) {
        return Array.from(this.results.values()).filter(result => {
            if (query.requestId && result.requestId !== query.requestId) {
                return false;
            }
            if (query.modelId && !result.modelId.equals(query.modelId)) {
                return false;
            }
            if (query.analysisType && result.analysisType !== query.analysisType) {
                return false;
            }
            if (query.status && result.status !== query.status) {
                return false;
            }
            if (query.minConfidence !== undefined && result.confidence < query.minConfidence) {
                return false;
            }
            if (query.maxConfidence !== undefined && result.confidence > query.maxConfidence) {
                return false;
            }
            if (query.tags && !query.tags.every(tag => result.hasTag(tag))) {
                return false;
            }
            if (query.correlationId && result.correlationId !== query.correlationId) {
                return false;
            }
            if (query.parentAnalysisId) {
                if (!result.parentAnalysisId || !result.parentAnalysisId.equals(query.parentAnalysisId)) {
                    return false;
                }
            }
            if (query.hasChildren !== undefined) {
                const hasChildren = result.childAnalysisIds.length > 0;
                if (query.hasChildren !== hasChildren) {
                    return false;
                }
            }
            if (query.createdAfter && result.createdAt < query.createdAfter) {
                return false;
            }
            if (query.createdBefore && result.createdAt > query.createdBefore) {
                return false;
            }
            if (query.completedAfter && (!result.completedAt || result.completedAt < query.completedAfter)) {
                return false;
            }
            if (query.completedBefore && (!result.completedAt || result.completedAt > query.completedBefore)) {
                return false;
            }
            return true;
        });
    }
    /**
     * Finds an analysis result by request ID
     */
    findByRequestId(requestId) {
        return Array.from(this.results.values()).find(result => result.requestId === requestId);
    }
    /**
     * Gets analysis results by correlation ID
     */
    findByCorrelationId(correlationId) {
        return Array.from(this.results.values()).filter(result => result.correlationId === correlationId);
    }
    /**
     * Gets child analysis results for a parent
     */
    getChildResults(parentId) {
        return Array.from(this.results.values()).filter(result => result.parentAnalysisId && result.parentAnalysisId.equals(parentId));
    }
    /**
     * Gets analysis results by model ID
     */
    getResultsByModel(modelId) {
        return Array.from(this.results.values()).filter(result => result.modelId.equals(modelId));
    }
    /**
     * Gets pending analysis results
     */
    getPendingResults() {
        return Array.from(this.results.values()).filter(result => result.status === analysis_result_entity_1.AnalysisStatus.PENDING);
    }
    /**
     * Gets processing analysis results
     */
    getProcessingResults() {
        return Array.from(this.results.values()).filter(result => result.status === analysis_result_entity_1.AnalysisStatus.PROCESSING);
    }
    /**
     * Gets completed analysis results
     */
    getCompletedResults() {
        return Array.from(this.results.values()).filter(result => result.status === analysis_result_entity_1.AnalysisStatus.COMPLETED);
    }
    /**
     * Gets failed analysis results
     */
    getFailedResults() {
        return Array.from(this.results.values()).filter(result => result.status === analysis_result_entity_1.AnalysisStatus.FAILED);
    }
    /**
     * Gets retryable failed results
     */
    getRetryableFailedResults() {
        return this.getFailedResults().filter(result => result.isRetryable());
    }
    /**
     * Gets high confidence results
     */
    getHighConfidenceResults(threshold = 0.8) {
        return Array.from(this.results.values()).filter(result => result.hasHighConfidence(threshold));
    }
    /**
     * Gets results with low quality scores
     */
    getLowQualityResults(threshold = 0.5) {
        return Array.from(this.results.values()).filter(result => result.getQualityScore() < threshold);
    }
    /**
     * Cancels all pending and processing results
     */
    cancelAllActiveResults() {
        const activeResults = Array.from(this.results.values()).filter(result => !result.isTerminal());
        activeResults.forEach(result => result.cancel());
        return activeResults;
    }
    /**
     * Cancels results by query criteria
     */
    cancelResults(query) {
        const matchingResults = this.findAnalysisResults(query).filter(result => !result.isTerminal());
        matchingResults.forEach(result => result.cancel());
        return matchingResults;
    }
    /**
     * Gets aggregate statistics
     */
    getStatistics() {
        const results = Array.from(this.results.values());
        if (results.length === 0) {
            return this.createEmptyStatistics();
        }
        const statusDistribution = results.reduce((acc, result) => {
            acc[result.status] = (acc[result.status] || 0) + 1;
            return acc;
        }, {});
        const typeDistribution = results.reduce((acc, result) => {
            acc[result.analysisType] = (acc[result.analysisType] || 0) + 1;
            return acc;
        }, {});
        const totalConfidence = results.reduce((sum, result) => sum + result.confidence, 0);
        const totalProcessingTime = results.reduce((sum, result) => sum + result.processingTime, 0);
        const totalQualityScore = results.reduce((sum, result) => sum + result.getQualityScore(), 0);
        const successfulResults = results.filter(result => result.isSuccessful());
        const completedResults = results.filter(result => result.isTerminal());
        const retryableFailures = results.filter(result => result.status === analysis_result_entity_1.AnalysisStatus.FAILED && result.isRetryable());
        return {
            totalResults: results.length,
            statusDistribution,
            typeDistribution,
            averageConfidence: totalConfidence / results.length,
            averageProcessingTime: totalProcessingTime / results.length,
            successRate: successfulResults.length / results.length,
            averageQualityScore: totalQualityScore / results.length,
            retryableFailures: retryableFailures.length,
            completionRate: completedResults.length / results.length,
        };
    }
    /**
     * Gets statistics for a specific time period
     */
    getStatisticsForPeriod(startDate, endDate) {
        const periodResults = Array.from(this.results.values()).filter(result => result.createdAt >= startDate && result.createdAt <= endDate);
        const tempAggregate = new AnalysisResultAggregate(periodResults);
        return tempAggregate.getStatistics();
    }
    /**
     * Creates a batch of analysis results
     */
    createBatch(requests) {
        const results = [];
        for (const request of requests) {
            try {
                const result = this.createAnalysisResult(request);
                results.push(result);
            }
            catch (error) {
                // Log error but continue with other requests
                console.warn(`Failed to create analysis result for request ${request.requestId}:`, error);
            }
        }
        const successCount = results.filter(r => r.isSuccessful()).length;
        const failureCount = results.filter(r => r.status === analysis_result_entity_1.AnalysisStatus.FAILED).length;
        const pendingCount = results.filter(r => r.status === analysis_result_entity_1.AnalysisStatus.PENDING).length;
        const totalConfidence = results.reduce((sum, r) => sum + r.confidence, 0);
        return {
            results,
            totalCount: results.length,
            averageConfidence: results.length > 0 ? totalConfidence / results.length : 0,
            successCount,
            failureCount,
            pendingCount,
        };
    }
    /**
     * Groups results by analysis type
     */
    groupByAnalysisType() {
        const groups = new Map();
        Array.from(this.results.values()).forEach(result => {
            if (!groups.has(result.analysisType)) {
                groups.set(result.analysisType, []);
            }
            groups.get(result.analysisType).push(result);
        });
        return groups;
    }
    /**
     * Groups results by status
     */
    groupByStatus() {
        const groups = new Map();
        Array.from(this.results.values()).forEach(result => {
            if (!groups.has(result.status)) {
                groups.set(result.status, []);
            }
            groups.get(result.status).push(result);
        });
        return groups;
    }
    /**
     * Gets the most recent results
     */
    getMostRecentResults(limit = 10) {
        return Array.from(this.results.values())
            .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
            .slice(0, limit);
    }
    /**
     * Gets results sorted by confidence (highest first)
     */
    getResultsByConfidence(limit) {
        const sorted = Array.from(this.results.values())
            .sort((a, b) => b.confidence - a.confidence);
        return limit ? sorted.slice(0, limit) : sorted;
    }
    /**
     * Gets results sorted by quality score (highest first)
     */
    getResultsByQuality(limit) {
        const sorted = Array.from(this.results.values())
            .sort((a, b) => b.getQualityScore() - a.getQualityScore());
        return limit ? sorted.slice(0, limit) : sorted;
    }
    createEmptyStatistics() {
        return {
            totalResults: 0,
            statusDistribution: {},
            typeDistribution: {},
            averageConfidence: 0,
            averageProcessingTime: 0,
            successRate: 0,
            averageQualityScore: 0,
            retryableFailures: 0,
            completionRate: 0,
        };
    }
}
exports.AnalysisResultAggregate = AnalysisResultAggregate;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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