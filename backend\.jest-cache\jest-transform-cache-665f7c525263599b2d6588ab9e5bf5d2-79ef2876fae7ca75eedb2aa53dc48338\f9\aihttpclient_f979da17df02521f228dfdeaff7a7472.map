{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\communication\\ai-http.client.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAC/C,kDAAgF;AAEhF;;;;;;GAMG;AAEI,IAAM,YAAY,oBAAlB,MAAM,YAAY;IAMvB,YACmB,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAN9B,WAAM,GAAG,IAAI,eAAM,CAAC,cAAY,CAAC,IAAI,CAAC,CAAC;QAQtD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,iBAAiB,EAAE,KAAK,CAAC,CAAC;QAC/E,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,iBAAiB,EAAE,CAAC,CAAC,CAAC;QAE3E,mDAAmD;QACnD,IAAI,CAAC,aAAa,GAAG,eAAK,CAAC,MAAM,CAAC;YAChC,OAAO,EAAE,IAAI,CAAC,cAAc;YAC5B,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;gBAClC,YAAY,EAAE,wBAAwB;aACvC;SACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CACvB,QAAgB,EAChB,OAA0B,EAC1B,UAA0B,EAAE;QAE5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,QAAQ,EAAE,CAAC,CAAC;QAE9D,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YACnE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAqB,MAAM,CAAC,CAAC;YAEvE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,QAAQ,EAAE,CAAC,CAAC;YAC7D,OAAO,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,IAAI,WAAW,CAAC,4BAA4B,KAAK,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QACtF,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CACvB,QAAgB,EAChB,OAA0B,EAC1B,UAA0B,EAAE;QAE5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,QAAQ,EAAE,CAAC,CAAC;QAE9D,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,OAAO,EAAE;gBACxD,GAAG,OAAO;gBACV,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,MAAM,EAAE,yBAAyB;aAC9D,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAqB,MAAM,CAAC,CAAC;YAEvE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,QAAQ,EAAE,CAAC,CAAC;YAC7D,OAAO,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,IAAI,WAAW,CAAC,4BAA4B,KAAK,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QACtF,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACzB,QAAgB,EAChB,OAA4B,EAC5B,UAA0B,EAAE;QAE5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,QAAQ,EAAE,CAAC,CAAC;QAEhE,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,OAAO,EAAE;gBACxD,GAAG,OAAO;gBACV,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,IAAI,EAAE,4BAA4B;aAC/D,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAuB,MAAM,CAAC,CAAC;YAEzE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,QAAQ,EAAE,CAAC,CAAC;YAC/D,OAAO,IAAI,CAAC,2BAA2B,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YACnE,MAAM,IAAI,WAAW,CAAC,8BAA8B,KAAK,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QACxF,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CAC1B,QAAgB,EAChB,UAA0B,EAAE;QAE5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,QAAQ,EAAE,CAAC,CAAC;QAE1D,IAAI,CAAC;YACH,MAAM,MAAM,GAAuB;gBACjC,MAAM,EAAE,KAAK;gBACb,GAAG,EAAE,GAAG,QAAQ,SAAS;gBACzB,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,IAAI;gBAChC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;aACpC,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAmB,MAAM,CAAC,CAAC;YAErE,OAAO;gBACL,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG;gBACzD,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,SAAS;gBACzC,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,YAAY;gBACxC,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO;gBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YAC5D,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,WAAW;gBACnB,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CACpB,QAAgB,EAChB,QAA0B,EAC1B,UAA0B,EAAE;QAE5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,QAAQ,YAAY,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;QAEtF,IAAI,CAAC;YACH,MAAM,YAAY,GAAG;gBACnB,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE;gBAC/B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;YAEF,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,YAAY,EAAE;gBAC7D,GAAG,OAAO;gBACV,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,MAAM,EAAE,sBAAsB;aAC3D,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAkB,MAAM,CAAC,CAAC;YAEpE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,QAAQ,EAAE,CAAC,CAAC;YAC1D,OAAO,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEpD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YAC9D,MAAM,IAAI,WAAW,CAAC,yBAAyB,KAAK,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QACnF,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACd,QAAgB,EAChB,IAAqB,EACrB,QAAgB,EAChB,UAA0B,EAAE;QAE5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,QAAQ,eAAe,QAAQ,EAAE,CAAC,CAAC;QAE3E,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;YAChC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;YAEpD,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACrB,QAAQ,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;YAChE,CAAC;YAED,MAAM,MAAM,GAAuB;gBACjC,MAAM,EAAE,MAAM;gBACd,GAAG,EAAE,QAAQ;gBACb,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,KAAK;gBACjC,OAAO,EAAE;oBACP,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;oBAC7B,cAAc,EAAE,qBAAqB;iBACtC;aACF,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAuB,MAAM,CAAC,CAAC;YAEzE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,QAAQ,EAAE,CAAC,CAAC;YACxD,OAAO,QAAQ,CAAC,IAAI,CAAC;QAEvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,IAAI,WAAW,CAAC,uBAAuB,KAAK,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QACjF,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAChB,QAAgB,EAChB,MAAc,EACd,UAA0B,EAAE;QAE5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,QAAQ,aAAa,MAAM,EAAE,CAAC,CAAC;QAE3E,IAAI,CAAC;YACH,MAAM,MAAM,GAAuB;gBACjC,MAAM,EAAE,KAAK;gBACb,GAAG,EAAE,GAAG,QAAQ,IAAI,MAAM,EAAE;gBAC5B,YAAY,EAAE,aAAa;gBAC3B,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,KAAK;gBACjC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;aACpC,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAc,MAAM,CAAC,CAAC;YAEhE,OAAO;gBACL,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;gBAChC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC;gBAChD,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC;gBAC7C,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,UAAU;aAC/B,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YAC9D,MAAM,IAAI,WAAW,CAAC,yBAAyB,KAAK,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QACnF,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACjB,QAAgB,EAChB,OAAY,EACZ,UAA0B,EAAE;QAE5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,QAAQ,EAAE,CAAC,CAAC;QAE7D,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,OAAO,EAAE;gBACxD,GAAG,OAAO;gBACV,YAAY,EAAE,QAAQ;aACvB,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACnD,OAAO,QAAQ,CAAC,IAAI,CAAC;QAEvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YAC/D,MAAM,IAAI,WAAW,CAAC,0BAA0B,KAAK,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QACpF,CAAC;IACH,CAAC;IAED,yBAAyB;IAEjB,kBAAkB,CACxB,QAAgB,EAChB,OAAY,EACZ,OAAuB;QAEvB,OAAO;YACL,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,MAAM;YAChC,GAAG,EAAE,QAAQ;YACb,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc;YAC/C,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;YACnC,MAAM,EAAE,OAAO,CAAC,WAAW;YAC3B,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,MAAM;SAC7C,CAAC;IACJ,CAAC;IAEO,YAAY,CAAC,OAAuB;QAC1C,MAAM,OAAO,GAA2B;YACtC,cAAc,EAAE,kBAAkB;YAClC,YAAY,EAAE,wBAAwB;YACtC,cAAc,EAAE,IAAI,CAAC,iBAAiB,EAAE;SACzC,CAAC;QAEF,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU,OAAO,CAAC,MAAM,EAAE,CAAC;QACxD,CAAC;QAED,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;YAC1B,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;QAChD,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,cAAc,CAAI,MAA0B;QACxD,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC;QACzD,IAAI,SAAc,CAAC;QAEnB,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,UAAU,EAAE,OAAO,EAAE,EAAE,CAAC;YACvD,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAI,MAAM,CAAC,CAAC;gBAC7D,OAAO,QAAQ,CAAC;YAClB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAK,CAAC;gBAElB,kCAAkC;gBAClC,IAAI,OAAO,KAAK,UAAU,EAAE,CAAC;oBAC3B,MAAM;gBACR,CAAC;gBAED,qCAAqC;gBACrC,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC/B,MAAM;gBACR,CAAC;gBAED,kCAAkC;gBAClC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC;gBAClF,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAExB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,OAAO,GAAG,CAAC,IAAI,UAAU,WAAW,KAAK,IAAI,CAAC,CAAC;YAChG,CAAC;QACH,CAAC;QAED,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;IAC3C,CAAC;IAEO,cAAc,CAAC,KAAU;QAC/B,+DAA+D;QAC/D,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,IAAI,GAAG,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,GAAG,GAAG,EAAE,CAAC;YAClE,mDAAmD;YACnD,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC1D,CAAC;QAED,wCAAwC;QACxC,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;YAChE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAEO,yBAAyB,CAAC,IAAS;QACzC,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACxC,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM;YACnD,UAAU,EAAE,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC;YAC9C,QAAQ,EAAE;gBACR,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,cAAc,EAAE,IAAI,CAAC,cAAc;gBACnC,GAAG,IAAI,CAAC,QAAQ;aACjB;YACD,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;SAClD,CAAC;IACJ,CAAC;IAEO,yBAAyB,CAAC,IAAS;QACzC,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,EAAE;YAC5B,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,WAAW;YAClC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,EAAE;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS,IAAI,EAAE;YAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,EAAE;YAC7B,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;SAClD,CAAC;IACJ,CAAC;IAEO,2BAA2B,CAAC,IAAS;QAC3C,OAAO;YACL,UAAU,EAAE,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM;YACzD,UAAU,EAAE,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC;YAC9C,YAAY,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE;YACrC,QAAQ,EAAE;gBACR,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,GAAG,IAAI,CAAC,QAAQ;aACjB;YACD,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;SAClD,CAAC;IACJ,CAAC;IAEO,sBAAsB,CAAC,IAAS;QACtC,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,IAAI,EAAE;YAC7C,OAAO,EAAE;gBACP,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC;gBAC9C,UAAU,EAAE,IAAI,CAAC,UAAU,IAAI,CAAC;gBAChC,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,CAAC;gBACxB,cAAc,EAAE,IAAI,CAAC,cAAc;aACpC;YACD,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,EAAE;YAC7B,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;SAClD,CAAC;IACJ,CAAC;IAEO,kBAAkB,CAAC,KAAU;QACnC,IAAI,KAAK,EAAE,QAAQ,EAAE,CAAC;YACpB,sBAAsB;YACtB,MAAM,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;YACrC,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,IAAI,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC;YAC1E,OAAO,IAAI,WAAW,CAAC,QAAQ,MAAM,KAAK,OAAO,EAAE,EAAE,KAAK,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QACjF,CAAC;QAED,IAAI,KAAK,EAAE,IAAI,KAAK,cAAc,EAAE,CAAC;YACnC,OAAO,IAAI,WAAW,CAAC,iBAAiB,EAAE,KAAK,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,KAAK,EAAE,IAAI,KAAK,cAAc,EAAE,CAAC;YACnC,OAAO,IAAI,WAAW,CAAC,oBAAoB,EAAE,KAAK,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QACzE,CAAC;QAED,OAAO,IAAI,WAAW,CAAC,KAAK,EAAE,OAAO,IAAI,oBAAoB,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IAC5F,CAAC;IAEO,eAAe,CAAC,OAAY;QAClC,MAAM,kBAAkB,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAC;QAC1D,IAAI,kBAAkB,EAAE,CAAC;YACvB,MAAM,KAAK,GAAG,kBAAkB,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;YAC/D,IAAI,KAAK;gBAAE,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,iBAAiB;QACvB,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACxE,CAAC;IAEO,kBAAkB;QACxB,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACxE,CAAC;IAEO,eAAe;QACrB,OAAO,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAC1E,CAAC;CACF,CAAA;AApcY,oCAAY;uBAAZ,YAAY;IADxB,IAAA,mBAAU,GAAE;yDAQuB,sBAAa,oBAAb,sBAAa;GAPpC,YAAY,CAocxB;AAsGD,MAAM,WAAY,SAAQ,KAAK;IAC7B,YACE,OAAe,EACC,QAAiB,EACjB,aAAmB;QAEnC,KAAK,CAAC,OAAO,CAAC,CAAC;QAHC,aAAQ,GAAR,QAAQ,CAAS;QACjB,kBAAa,GAAb,aAAa,CAAM;QAGnC,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC;IAC5B,CAAC;CACF", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\communication\\ai-http.client.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport axios, { AxiosRequestConfig, AxiosResponse, AxiosInstance } from 'axios';\r\n\r\n/**\r\n * AI HTTP Client\r\n * \r\n * Handles HTTP communication with AI providers and services.\r\n * Implements retry logic, timeout handling, request/response transformation,\r\n * and comprehensive error handling for reliable AI service integration.\r\n */\r\n@Injectable()\r\nexport class AiHttpClient {\r\n  private readonly logger = new Logger(AiHttpClient.name);\r\n  private readonly defaultTimeout: number;\r\n  private readonly defaultRetries: number;\r\n  private readonly axiosInstance: AxiosInstance;\r\n\r\n  constructor(\r\n    private readonly configService: ConfigService,\r\n  ) {\r\n    this.defaultTimeout = this.configService.get<number>('ai.http.timeout', 30000);\r\n    this.defaultRetries = this.configService.get<number>('ai.http.retries', 3);\r\n    \r\n    // Create axios instance with default configuration\r\n    this.axiosInstance = axios.create({\r\n      timeout: this.defaultTimeout,\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'User-Agent': 'Sentinel-AI-Client/1.0',\r\n      },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Sends analysis request to AI provider\r\n   */\r\n  async sendAnalysisRequest(\r\n    endpoint: string,\r\n    payload: AiAnalysisPayload,\r\n    options: RequestOptions = {}\r\n  ): Promise<AiAnalysisResponse> {\r\n    this.logger.debug(`Sending analysis request to: ${endpoint}`);\r\n\r\n    try {\r\n      const config = this.buildRequestConfig(endpoint, payload, options);\r\n      const response = await this.executeRequest<AiAnalysisResponse>(config);\r\n      \r\n      this.logger.debug(`Analysis request completed: ${endpoint}`);\r\n      return this.transformAnalysisResponse(response.data);\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Analysis request failed: ${endpoint}`, error);\r\n      throw new AiHttpError(`Analysis request failed: ${error.message}`, endpoint, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Sends training request to AI provider\r\n   */\r\n  async sendTrainingRequest(\r\n    endpoint: string,\r\n    payload: AiTrainingPayload,\r\n    options: RequestOptions = {}\r\n  ): Promise<AiTrainingResponse> {\r\n    this.logger.debug(`Sending training request to: ${endpoint}`);\r\n\r\n    try {\r\n      const config = this.buildRequestConfig(endpoint, payload, {\r\n        ...options,\r\n        timeout: options.timeout || 300000, // 5 minutes for training\r\n      });\r\n      \r\n      const response = await this.executeRequest<AiTrainingResponse>(config);\r\n      \r\n      this.logger.debug(`Training request completed: ${endpoint}`);\r\n      return this.transformTrainingResponse(response.data);\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Training request failed: ${endpoint}`, error);\r\n      throw new AiHttpError(`Training request failed: ${error.message}`, endpoint, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Sends prediction request to AI provider\r\n   */\r\n  async sendPredictionRequest(\r\n    endpoint: string,\r\n    payload: AiPredictionPayload,\r\n    options: RequestOptions = {}\r\n  ): Promise<AiPredictionResponse> {\r\n    this.logger.debug(`Sending prediction request to: ${endpoint}`);\r\n\r\n    try {\r\n      const config = this.buildRequestConfig(endpoint, payload, {\r\n        ...options,\r\n        timeout: options.timeout || 5000, // 5 seconds for predictions\r\n      });\r\n      \r\n      const response = await this.executeRequest<AiPredictionResponse>(config);\r\n      \r\n      this.logger.debug(`Prediction request completed: ${endpoint}`);\r\n      return this.transformPredictionResponse(response.data);\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Prediction request failed: ${endpoint}`, error);\r\n      throw new AiHttpError(`Prediction request failed: ${error.message}`, endpoint, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Sends health check request to AI provider\r\n   */\r\n  async sendHealthCheckRequest(\r\n    endpoint: string,\r\n    options: RequestOptions = {}\r\n  ): Promise<AiHealthResponse> {\r\n    this.logger.debug(`Sending health check to: ${endpoint}`);\r\n\r\n    try {\r\n      const config: AxiosRequestConfig = {\r\n        method: 'GET',\r\n        url: `${endpoint}/health`,\r\n        timeout: options.timeout || 5000,\r\n        headers: this.buildHeaders(options),\r\n      };\r\n\r\n      const response = await this.executeRequest<AiHealthResponse>(config);\r\n      \r\n      return {\r\n        healthy: response.data.healthy || response.status === 200,\r\n        status: response.data.status || 'unknown',\r\n        responseTime: response.data.responseTime,\r\n        version: response.data.version,\r\n        timestamp: new Date(),\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.warn(`Health check failed: ${endpoint}`, error);\r\n      return {\r\n        healthy: false,\r\n        status: 'unhealthy',\r\n        error: error.message,\r\n        timestamp: new Date(),\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Sends batch request to AI provider\r\n   */\r\n  async sendBatchRequest(\r\n    endpoint: string,\r\n    payloads: AiBatchPayload[],\r\n    options: RequestOptions = {}\r\n  ): Promise<AiBatchResponse> {\r\n    this.logger.debug(`Sending batch request to: ${endpoint}, count: ${payloads.length}`);\r\n\r\n    try {\r\n      const batchPayload = {\r\n        requests: payloads,\r\n        batchId: this.generateBatchId(),\r\n        timestamp: new Date().toISOString(),\r\n      };\r\n\r\n      const config = this.buildRequestConfig(endpoint, batchPayload, {\r\n        ...options,\r\n        timeout: options.timeout || 120000, // 2 minutes for batch\r\n      });\r\n\r\n      const response = await this.executeRequest<AiBatchResponse>(config);\r\n      \r\n      this.logger.debug(`Batch request completed: ${endpoint}`);\r\n      return this.transformBatchResponse(response.data);\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Batch request failed: ${endpoint}`, error);\r\n      throw new AiHttpError(`Batch request failed: ${error.message}`, endpoint, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Uploads file to AI provider\r\n   */\r\n  async uploadFile(\r\n    endpoint: string,\r\n    file: Buffer | string,\r\n    filename: string,\r\n    options: RequestOptions = {}\r\n  ): Promise<AiFileUploadResponse> {\r\n    this.logger.debug(`Uploading file to: ${endpoint}, filename: ${filename}`);\r\n\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append('file', new Blob([file]), filename);\r\n      \r\n      if (options.metadata) {\r\n        formData.append('metadata', JSON.stringify(options.metadata));\r\n      }\r\n\r\n      const config: AxiosRequestConfig = {\r\n        method: 'POST',\r\n        url: endpoint,\r\n        data: formData,\r\n        timeout: options.timeout || 60000,\r\n        headers: {\r\n          ...this.buildHeaders(options),\r\n          'Content-Type': 'multipart/form-data',\r\n        },\r\n      };\r\n\r\n      const response = await this.executeRequest<AiFileUploadResponse>(config);\r\n      \r\n      this.logger.debug(`File upload completed: ${endpoint}`);\r\n      return response.data;\r\n\r\n    } catch (error) {\r\n      this.logger.error(`File upload failed: ${endpoint}`, error);\r\n      throw new AiHttpError(`File upload failed: ${error.message}`, endpoint, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Downloads file from AI provider\r\n   */\r\n  async downloadFile(\r\n    endpoint: string,\r\n    fileId: string,\r\n    options: RequestOptions = {}\r\n  ): Promise<AiFileDownloadResponse> {\r\n    this.logger.debug(`Downloading file from: ${endpoint}, fileId: ${fileId}`);\r\n\r\n    try {\r\n      const config: AxiosRequestConfig = {\r\n        method: 'GET',\r\n        url: `${endpoint}/${fileId}`,\r\n        responseType: 'arraybuffer',\r\n        timeout: options.timeout || 60000,\r\n        headers: this.buildHeaders(options),\r\n      };\r\n\r\n      const response = await this.executeRequest<ArrayBuffer>(config);\r\n      \r\n      return {\r\n        data: Buffer.from(response.data),\r\n        filename: this.extractFilename(response.headers),\r\n        contentType: response.headers['content-type'],\r\n        size: response.data.byteLength,\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error(`File download failed: ${endpoint}`, error);\r\n      throw new AiHttpError(`File download failed: ${error.message}`, endpoint, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Streams data to AI provider\r\n   */\r\n  async streamRequest(\r\n    endpoint: string,\r\n    payload: any,\r\n    options: RequestOptions = {}\r\n  ): Promise<ReadableStream> {\r\n    this.logger.debug(`Starting stream request to: ${endpoint}`);\r\n\r\n    try {\r\n      const config = this.buildRequestConfig(endpoint, payload, {\r\n        ...options,\r\n        responseType: 'stream',\r\n      });\r\n\r\n      const response = await this.executeRequest(config);\r\n      return response.data;\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Stream request failed: ${endpoint}`, error);\r\n      throw new AiHttpError(`Stream request failed: ${error.message}`, endpoint, error);\r\n    }\r\n  }\r\n\r\n  // Private helper methods\r\n\r\n  private buildRequestConfig(\r\n    endpoint: string,\r\n    payload: any,\r\n    options: RequestOptions\r\n  ): AxiosRequestConfig {\r\n    return {\r\n      method: options.method || 'POST',\r\n      url: endpoint,\r\n      data: payload,\r\n      timeout: options.timeout || this.defaultTimeout,\r\n      headers: this.buildHeaders(options),\r\n      params: options.queryParams,\r\n      responseType: options.responseType || 'json',\r\n    };\r\n  }\r\n\r\n  private buildHeaders(options: RequestOptions): Record<string, string> {\r\n    const headers: Record<string, string> = {\r\n      'Content-Type': 'application/json',\r\n      'User-Agent': 'Sentinel-AI-Client/1.0',\r\n      'X-Request-ID': this.generateRequestId(),\r\n    };\r\n\r\n    if (options.apiKey) {\r\n      headers['Authorization'] = `Bearer ${options.apiKey}`;\r\n    }\r\n\r\n    if (options.customHeaders) {\r\n      Object.assign(headers, options.customHeaders);\r\n    }\r\n\r\n    return headers;\r\n  }\r\n\r\n  private async executeRequest<T>(config: AxiosRequestConfig): Promise<AxiosResponse<T>> {\r\n    const maxRetries = config.retries || this.defaultRetries;\r\n    let lastError: any;\r\n\r\n    for (let attempt = 0; attempt <= maxRetries; attempt++) {\r\n      try {\r\n        const response = await this.axiosInstance.request<T>(config);\r\n        return response;\r\n      } catch (error) {\r\n        lastError = error;\r\n        \r\n        // Don't retry on the last attempt\r\n        if (attempt === maxRetries) {\r\n          break;\r\n        }\r\n\r\n        // Don't retry on certain error types\r\n        if (this.shouldNotRetry(error)) {\r\n          break;\r\n        }\r\n\r\n        // Exponential backoff with jitter\r\n        const delay = Math.min(1000 * Math.pow(2, attempt), 10000) + Math.random() * 1000;\r\n        await this.sleep(delay);\r\n        \r\n        this.logger.debug(`Retrying request (attempt ${attempt + 1}/${maxRetries}) after ${delay}ms`);\r\n      }\r\n    }\r\n\r\n    throw this.transformHttpError(lastError);\r\n  }\r\n\r\n  private shouldNotRetry(error: any): boolean {\r\n    // Don't retry on client errors (4xx) except for specific cases\r\n    if (error.response?.status >= 400 && error.response?.status < 500) {\r\n      // Retry on rate limiting and authentication errors\r\n      return ![401, 408, 429].includes(error.response.status);\r\n    }\r\n    \r\n    // Don't retry on certain network errors\r\n    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {\r\n      return true;\r\n    }\r\n    \r\n    return false;\r\n  }\r\n\r\n  private sleep(ms: number): Promise<void> {\r\n    return new Promise(resolve => setTimeout(resolve, ms));\r\n  }\r\n\r\n  private transformAnalysisResponse(data: any): AiAnalysisResponse {\r\n    return {\r\n      id: data.id || this.generateResponseId(),\r\n      result: data.result || data.analysis || data.output,\r\n      confidence: data.confidence || data.score || 0,\r\n      metadata: {\r\n        model: data.model,\r\n        version: data.version,\r\n        processingTime: data.processingTime,\r\n        ...data.metadata,\r\n      },\r\n      timestamp: new Date(data.timestamp || Date.now()),\r\n    };\r\n  }\r\n\r\n  private transformTrainingResponse(data: any): AiTrainingResponse {\r\n    return {\r\n      jobId: data.jobId || data.id,\r\n      status: data.status || 'completed',\r\n      modelId: data.modelId,\r\n      metrics: data.metrics || {},\r\n      artifacts: data.artifacts || [],\r\n      metadata: data.metadata || {},\r\n      timestamp: new Date(data.timestamp || Date.now()),\r\n    };\r\n  }\r\n\r\n  private transformPredictionResponse(data: any): AiPredictionResponse {\r\n    return {\r\n      prediction: data.prediction || data.result || data.output,\r\n      confidence: data.confidence || data.score || 0,\r\n      alternatives: data.alternatives || [],\r\n      metadata: {\r\n        model: data.model,\r\n        latency: data.latency,\r\n        ...data.metadata,\r\n      },\r\n      timestamp: new Date(data.timestamp || Date.now()),\r\n    };\r\n  }\r\n\r\n  private transformBatchResponse(data: any): AiBatchResponse {\r\n    return {\r\n      batchId: data.batchId,\r\n      results: data.results || data.responses || [],\r\n      summary: {\r\n        total: data.total || data.results?.length || 0,\r\n        successful: data.successful || 0,\r\n        failed: data.failed || 0,\r\n        processingTime: data.processingTime,\r\n      },\r\n      metadata: data.metadata || {},\r\n      timestamp: new Date(data.timestamp || Date.now()),\r\n    };\r\n  }\r\n\r\n  private transformHttpError(error: any): Error {\r\n    if (error?.response) {\r\n      // HTTP error response\r\n      const status = error.response.status;\r\n      const message = error.response.data?.message || error.response.statusText;\r\n      return new AiHttpError(`HTTP ${status}: ${message}`, error.config?.url, error);\r\n    }\r\n    \r\n    if (error?.code === 'ECONNABORTED') {\r\n      return new AiHttpError('Request timeout', error.config?.url, error);\r\n    }\r\n    \r\n    if (error?.code === 'ECONNREFUSED') {\r\n      return new AiHttpError('Connection refused', error.config?.url, error);\r\n    }\r\n    \r\n    return new AiHttpError(error?.message || 'Unknown HTTP error', error?.config?.url, error);\r\n  }\r\n\r\n  private extractFilename(headers: any): string {\r\n    const contentDisposition = headers['content-disposition'];\r\n    if (contentDisposition) {\r\n      const match = contentDisposition.match(/filename=\"?([^\"]+)\"?/);\r\n      if (match) return match[1];\r\n    }\r\n    return 'download';\r\n  }\r\n\r\n  private generateRequestId(): string {\r\n    return `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n\r\n  private generateResponseId(): string {\r\n    return `res-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n\r\n  private generateBatchId(): string {\r\n    return `batch-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n}\r\n\r\n// Type definitions\r\ninterface RequestOptions {\r\n  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';\r\n  timeout?: number;\r\n  retries?: number;\r\n  apiKey?: string;\r\n  customHeaders?: Record<string, string>;\r\n  queryParams?: Record<string, any>;\r\n  responseType?: 'json' | 'text' | 'arraybuffer' | 'stream';\r\n  metadata?: any;\r\n}\r\n\r\ninterface AiAnalysisPayload {\r\n  data: any;\r\n  model?: string;\r\n  parameters?: any;\r\n  options?: any;\r\n}\r\n\r\ninterface AiAnalysisResponse {\r\n  id: string;\r\n  result: any;\r\n  confidence: number;\r\n  metadata: any;\r\n  timestamp: Date;\r\n}\r\n\r\ninterface AiTrainingPayload {\r\n  trainingData: any;\r\n  modelConfig: any;\r\n  parameters?: any;\r\n}\r\n\r\ninterface AiTrainingResponse {\r\n  jobId: string;\r\n  status: string;\r\n  modelId?: string;\r\n  metrics: any;\r\n  artifacts: any[];\r\n  metadata: any;\r\n  timestamp: Date;\r\n}\r\n\r\ninterface AiPredictionPayload {\r\n  input: any;\r\n  model?: string;\r\n  parameters?: any;\r\n}\r\n\r\ninterface AiPredictionResponse {\r\n  prediction: any;\r\n  confidence: number;\r\n  alternatives?: any[];\r\n  metadata: any;\r\n  timestamp: Date;\r\n}\r\n\r\ninterface AiHealthResponse {\r\n  healthy: boolean;\r\n  status: string;\r\n  responseTime?: number;\r\n  version?: string;\r\n  error?: string;\r\n  timestamp: Date;\r\n}\r\n\r\ninterface AiBatchPayload {\r\n  id: string;\r\n  data: any;\r\n  type: string;\r\n}\r\n\r\ninterface AiBatchResponse {\r\n  batchId: string;\r\n  results: any[];\r\n  summary: {\r\n    total: number;\r\n    successful: number;\r\n    failed: number;\r\n    processingTime?: number;\r\n  };\r\n  metadata: any;\r\n  timestamp: Date;\r\n}\r\n\r\ninterface AiFileUploadResponse {\r\n  fileId: string;\r\n  filename: string;\r\n  size: number;\r\n  url?: string;\r\n  metadata?: any;\r\n}\r\n\r\ninterface AiFileDownloadResponse {\r\n  data: Buffer;\r\n  filename: string;\r\n  contentType: string;\r\n  size: number;\r\n}\r\n\r\nclass AiHttpError extends Error {\r\n  constructor(\r\n    message: string,\r\n    public readonly endpoint?: string,\r\n    public readonly originalError?: any\r\n  ) {\r\n    super(message);\r\n    this.name = 'AiHttpError';\r\n  }\r\n}\r\n"], "version": 3}