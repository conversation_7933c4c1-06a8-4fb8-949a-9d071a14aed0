{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\services\\prediction-engine.interface.ts", "mappings": ";;;AA2NA,iCAAiC;AACpB,QAAA,iBAAiB,GAAG,MAAM,CAAC,mBAAmB,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\services\\prediction-engine.interface.ts"], "sourcesContent": ["/**\r\n * Prediction Engine Interface\r\n * \r\n * Defines the contract for AI prediction engine operations.\r\n * Supports real-time and batch prediction capabilities with\r\n * advanced features like confidence scoring, explanation, and monitoring.\r\n */\r\nexport interface PredictionEngine {\r\n  /**\r\n   * Gets the engine identifier\r\n   */\r\n  getEngineId(): string;\r\n\r\n  /**\r\n   * Gets the engine name\r\n   */\r\n  getEngineName(): string;\r\n\r\n  /**\r\n   * Gets the engine version\r\n   */\r\n  getEngineVersion(): string;\r\n\r\n  /**\r\n   * Checks if the engine is healthy and ready\r\n   */\r\n  isHealthy(): Promise<boolean>;\r\n\r\n  /**\r\n   * Gets engine health status with details\r\n   */\r\n  getHealthStatus(): Promise<EngineHealthStatus>;\r\n\r\n  /**\r\n   * Performs a single prediction\r\n   */\r\n  predict(request: PredictionRequest): Promise<PredictionResult>;\r\n\r\n  /**\r\n   * Performs batch predictions\r\n   */\r\n  batchPredict(requests: PredictionRequest[]): Promise<PredictionResult[]>;\r\n\r\n  /**\r\n   * Performs streaming predictions for real-time data\r\n   */\r\n  streamPredict(\r\n    inputStream: AsyncIterable<PredictionRequest>\r\n  ): AsyncIterable<PredictionResult>;\r\n\r\n  /**\r\n   * Performs predictions with explanation\r\n   */\r\n  predictWithExplanation(request: PredictionRequest): Promise<ExplainedPredictionResult>;\r\n\r\n  /**\r\n   * Performs ensemble predictions using multiple models\r\n   */\r\n  ensemblePredict(request: EnsemblePredictionRequest): Promise<EnsemblePredictionResult>;\r\n\r\n  /**\r\n   * Performs A/B testing predictions\r\n   */\r\n  abTestPredict(request: ABTestPredictionRequest): Promise<ABTestPredictionResult>;\r\n\r\n  /**\r\n   * Validates prediction input\r\n   */\r\n  validateInput(input: PredictionInput): Promise<ValidationResult>;\r\n\r\n  /**\r\n   * Preprocesses input data for prediction\r\n   */\r\n  preprocessInput(input: PredictionInput): Promise<PreprocessedInput>;\r\n\r\n  /**\r\n   * Postprocesses prediction output\r\n   */\r\n  postprocessOutput(output: RawPredictionOutput): Promise<PredictionOutput>;\r\n\r\n  /**\r\n   * Gets prediction confidence score\r\n   */\r\n  getConfidenceScore(prediction: PredictionOutput): Promise<ConfidenceScore>;\r\n\r\n  /**\r\n   * Calibrates prediction probabilities\r\n   */\r\n  calibratePrediction(prediction: PredictionOutput): Promise<CalibratedPrediction>;\r\n\r\n  /**\r\n   * Explains prediction results\r\n   */\r\n  explainPrediction(\r\n    request: PredictionRequest,\r\n    result: PredictionResult\r\n  ): Promise<PredictionExplanation>;\r\n\r\n  /**\r\n   * Detects prediction anomalies\r\n   */\r\n  detectAnomalies(predictions: PredictionResult[]): Promise<AnomalyDetectionResult>;\r\n\r\n  /**\r\n   * Monitors prediction quality\r\n   */\r\n  monitorPredictionQuality(\r\n    predictions: PredictionResult[],\r\n    actualResults?: any[]\r\n  ): Promise<QualityMonitoringResult>;\r\n\r\n  /**\r\n   * Gets prediction statistics\r\n   */\r\n  getPredictionStatistics(timeRange: TimeRange): Promise<PredictionStatistics>;\r\n\r\n  /**\r\n   * Gets model performance metrics\r\n   */\r\n  getModelPerformance(modelId: string, timeRange: TimeRange): Promise<ModelPerformanceMetrics>;\r\n\r\n  /**\r\n   * Updates model weights for ensemble predictions\r\n   */\r\n  updateEnsembleWeights(modelWeights: ModelWeight[]): Promise<void>;\r\n\r\n  /**\r\n   * Registers a new model for predictions\r\n   */\r\n  registerModel(model: ModelRegistration): Promise<string>;\r\n\r\n  /**\r\n   * Unregisters a model from predictions\r\n   */\r\n  unregisterModel(modelId: string): Promise<void>;\r\n\r\n  /**\r\n   * Lists all registered models\r\n   */\r\n  listModels(filters?: ModelFilters): Promise<RegisteredModel[]>;\r\n\r\n  /**\r\n   * Gets detailed model information\r\n   */\r\n  getModelInfo(modelId: string): Promise<ModelInfo>;\r\n\r\n  /**\r\n   * Loads a model for predictions\r\n   */\r\n  loadModel(modelId: string): Promise<void>;\r\n\r\n  /**\r\n   * Unloads a model to free resources\r\n   */\r\n  unloadModel(modelId: string): Promise<void>;\r\n\r\n  /**\r\n   * Warms up model cache\r\n   */\r\n  warmupModel(modelId: string, sampleInputs: PredictionInput[]): Promise<void>;\r\n\r\n  /**\r\n   * Gets prediction cache statistics\r\n   */\r\n  getCacheStatistics(): Promise<CacheStatistics>;\r\n\r\n  /**\r\n   * Clears prediction cache\r\n   */\r\n  clearCache(modelId?: string): Promise<void>;\r\n\r\n  /**\r\n   * Configures prediction caching\r\n   */\r\n  configureCaching(config: CachingConfiguration): Promise<void>;\r\n\r\n  /**\r\n   * Sets prediction rate limits\r\n   */\r\n  setRateLimits(limits: RateLimitConfiguration): Promise<void>;\r\n\r\n  /**\r\n   * Gets current rate limit status\r\n   */\r\n  getRateLimitStatus(): Promise<RateLimitStatus>;\r\n\r\n  /**\r\n   * Schedules batch prediction job\r\n   */\r\n  scheduleBatchPrediction(job: BatchPredictionJob): Promise<string>;\r\n\r\n  /**\r\n   * Gets batch prediction job status\r\n   */\r\n  getBatchJobStatus(jobId: string): Promise<BatchJobStatus>;\r\n\r\n  /**\r\n   * Cancels batch prediction job\r\n   */\r\n  cancelBatchJob(jobId: string): Promise<void>;\r\n\r\n  /**\r\n   * Gets batch prediction results\r\n   */\r\n  getBatchResults(jobId: string): Promise<BatchPredictionResult>;\r\n\r\n  /**\r\n   * Exports prediction results\r\n   */\r\n  exportPredictions(\r\n    request: ExportRequest\r\n  ): Promise<ExportResult>;\r\n\r\n  /**\r\n   * Handles engine shutdown gracefully\r\n   */\r\n  shutdown(): Promise<void>;\r\n}\r\n\r\n// Token for dependency injection\r\nexport const PREDICTION_ENGINE = Symbol('PREDICTION_ENGINE');\r\n\r\n// Type definitions\r\ninterface EngineHealthStatus {\r\n  isHealthy: boolean;\r\n  status: 'healthy' | 'degraded' | 'unhealthy';\r\n  lastChecked: Date;\r\n  loadedModels: number;\r\n  activeRequests: number;\r\n  queuedRequests: number;\r\n  averageLatency: number;\r\n  errorRate: number;\r\n  resourceUtilization: ResourceUtilization;\r\n  details: Record<string, any>;\r\n}\r\n\r\ninterface ResourceUtilization {\r\n  cpu: number;\r\n  memory: number;\r\n  gpu?: number;\r\n  storage: number;\r\n}\r\n\r\ninterface PredictionRequest {\r\n  requestId?: string;\r\n  modelId: string;\r\n  input: PredictionInput;\r\n  options?: PredictionOptions;\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\ninterface PredictionInput {\r\n  data: any;\r\n  format: string;\r\n  schema?: string;\r\n  preprocessing?: PreprocessingOptions;\r\n}\r\n\r\ninterface PreprocessingOptions {\r\n  normalize: boolean;\r\n  scale: boolean;\r\n  encode: boolean;\r\n  fillMissing: boolean;\r\n  customTransforms: string[];\r\n}\r\n\r\ninterface PredictionOptions {\r\n  timeout?: number;\r\n  priority?: 'low' | 'normal' | 'high';\r\n  caching?: boolean;\r\n  explanation?: boolean;\r\n  confidenceThreshold?: number;\r\n  returnProbabilities?: boolean;\r\n  returnTopK?: number;\r\n  customParameters?: Record<string, any>;\r\n}\r\n\r\ninterface PredictionResult {\r\n  requestId: string;\r\n  modelId: string;\r\n  prediction: PredictionOutput;\r\n  confidence: number;\r\n  probability?: number[];\r\n  metadata: PredictionMetadata;\r\n  processingTime: number;\r\n  timestamp: Date;\r\n}\r\n\r\ninterface PredictionOutput {\r\n  value: any;\r\n  type: string;\r\n  format: string;\r\n  alternatives?: AlternativePrediction[];\r\n}\r\n\r\ninterface AlternativePrediction {\r\n  value: any;\r\n  confidence: number;\r\n  probability: number;\r\n  rank: number;\r\n}\r\n\r\ninterface PredictionMetadata {\r\n  modelVersion: string;\r\n  inputSize: number;\r\n  outputSize: number;\r\n  processingSteps: ProcessingStep[];\r\n  cacheHit: boolean;\r\n  warnings: string[];\r\n}\r\n\r\ninterface ProcessingStep {\r\n  name: string;\r\n  duration: number;\r\n  memoryUsage: number;\r\n  details: Record<string, any>;\r\n}\r\n\r\ninterface ExplainedPredictionResult extends PredictionResult {\r\n  explanation: PredictionExplanation;\r\n}\r\n\r\ninterface PredictionExplanation {\r\n  method: string;\r\n  featureImportance: FeatureImportance[];\r\n  shapValues?: ShapValue[];\r\n  limeExplanation?: LimeExplanation;\r\n  counterfactuals?: Counterfactual[];\r\n  textExplanation: string;\r\n  visualizations?: Visualization[];\r\n}\r\n\r\ninterface FeatureImportance {\r\n  feature: string;\r\n  importance: number;\r\n  direction: 'positive' | 'negative';\r\n  confidence: number;\r\n}\r\n\r\ninterface ShapValue {\r\n  feature: string;\r\n  value: number;\r\n  baseValue: number;\r\n  contribution: number;\r\n}\r\n\r\ninterface LimeExplanation {\r\n  features: LimeFeature[];\r\n  score: number;\r\n  intercept: number;\r\n}\r\n\r\ninterface LimeFeature {\r\n  name: string;\r\n  weight: number;\r\n  value: any;\r\n}\r\n\r\ninterface Counterfactual {\r\n  originalInput: any;\r\n  modifiedInput: any;\r\n  originalPrediction: any;\r\n  counterfactualPrediction: any;\r\n  changes: FeatureChange[];\r\n  distance: number;\r\n}\r\n\r\ninterface FeatureChange {\r\n  feature: string;\r\n  originalValue: any;\r\n  newValue: any;\r\n  changeType: 'increase' | 'decrease' | 'categorical';\r\n}\r\n\r\ninterface Visualization {\r\n  type: string;\r\n  data: any;\r\n  config: Record<string, any>;\r\n}\r\n\r\ninterface EnsemblePredictionRequest {\r\n  requestId?: string;\r\n  modelIds: string[];\r\n  input: PredictionInput;\r\n  ensembleMethod: EnsembleMethod;\r\n  options?: PredictionOptions;\r\n}\r\n\r\ninterface EnsembleMethod {\r\n  type: 'voting' | 'averaging' | 'stacking' | 'blending';\r\n  weights?: number[];\r\n  parameters?: Record<string, any>;\r\n}\r\n\r\ninterface EnsemblePredictionResult {\r\n  requestId: string;\r\n  ensemblePrediction: PredictionOutput;\r\n  individualPredictions: PredictionResult[];\r\n  ensembleConfidence: number;\r\n  consensusScore: number;\r\n  disagreementScore: number;\r\n  metadata: EnsembleMetadata;\r\n}\r\n\r\ninterface EnsembleMetadata {\r\n  method: EnsembleMethod;\r\n  modelCount: number;\r\n  averageConfidence: number;\r\n  predictionVariance: number;\r\n  processingTime: number;\r\n}\r\n\r\ninterface ABTestPredictionRequest {\r\n  requestId?: string;\r\n  controlModelId: string;\r\n  treatmentModelId: string;\r\n  input: PredictionInput;\r\n  trafficSplit: number; // 0.0 to 1.0\r\n  options?: PredictionOptions;\r\n}\r\n\r\ninterface ABTestPredictionResult {\r\n  requestId: string;\r\n  selectedModel: 'control' | 'treatment';\r\n  prediction: PredictionResult;\r\n  alternativePrediction?: PredictionResult;\r\n  testMetadata: ABTestMetadata;\r\n}\r\n\r\ninterface ABTestMetadata {\r\n  trafficSplit: number;\r\n  selectionReason: string;\r\n  testId: string;\r\n  cohort: string;\r\n}\r\n\r\ninterface ValidationResult {\r\n  isValid: boolean;\r\n  errors: ValidationError[];\r\n  warnings: ValidationWarning[];\r\n  suggestions: string[];\r\n}\r\n\r\ninterface ValidationError {\r\n  field: string;\r\n  message: string;\r\n  code: string;\r\n  severity: 'low' | 'medium' | 'high' | 'critical';\r\n}\r\n\r\ninterface ValidationWarning {\r\n  field: string;\r\n  message: string;\r\n  suggestion: string;\r\n}\r\n\r\ninterface PreprocessedInput {\r\n  data: any;\r\n  transformations: AppliedTransformation[];\r\n  metadata: PreprocessingMetadata;\r\n}\r\n\r\ninterface AppliedTransformation {\r\n  name: string;\r\n  parameters: Record<string, any>;\r\n  inputShape: number[];\r\n  outputShape: number[];\r\n}\r\n\r\ninterface PreprocessingMetadata {\r\n  originalFormat: string;\r\n  finalFormat: string;\r\n  processingTime: number;\r\n  dataQuality: DataQualityScore;\r\n}\r\n\r\ninterface DataQualityScore {\r\n  completeness: number;\r\n  consistency: number;\r\n  accuracy: number;\r\n  validity: number;\r\n  overall: number;\r\n}\r\n\r\ninterface RawPredictionOutput {\r\n  data: any;\r\n  format: string;\r\n  metadata: Record<string, any>;\r\n}\r\n\r\ninterface ConfidenceScore {\r\n  value: number;\r\n  method: string;\r\n  calibrated: boolean;\r\n  reliability: number;\r\n  factors: ConfidenceFactor[];\r\n}\r\n\r\ninterface ConfidenceFactor {\r\n  name: string;\r\n  contribution: number;\r\n  description: string;\r\n}\r\n\r\ninterface CalibratedPrediction {\r\n  originalPrediction: PredictionOutput;\r\n  calibratedPrediction: PredictionOutput;\r\n  calibrationMethod: string;\r\n  calibrationScore: number;\r\n  reliability: number;\r\n}\r\n\r\ninterface AnomalyDetectionResult {\r\n  hasAnomalies: boolean;\r\n  anomalies: PredictionAnomaly[];\r\n  anomalyScore: number;\r\n  threshold: number;\r\n  method: string;\r\n}\r\n\r\ninterface PredictionAnomaly {\r\n  requestId: string;\r\n  anomalyType: string;\r\n  severity: 'low' | 'medium' | 'high' | 'critical';\r\n  score: number;\r\n  description: string;\r\n  affectedFeatures: string[];\r\n  recommendations: string[];\r\n}\r\n\r\ninterface QualityMonitoringResult {\r\n  overallQuality: number;\r\n  metrics: QualityMetrics;\r\n  trends: QualityTrend[];\r\n  alerts: QualityAlert[];\r\n  recommendations: string[];\r\n}\r\n\r\ninterface QualityMetrics {\r\n  accuracy: number;\r\n  precision: number;\r\n  recall: number;\r\n  f1Score: number;\r\n  auc?: number;\r\n  calibrationError: number;\r\n  predictionStability: number;\r\n  driftScore: number;\r\n}\r\n\r\ninterface QualityTrend {\r\n  metric: string;\r\n  trend: 'improving' | 'stable' | 'degrading';\r\n  changeRate: number;\r\n  significance: number;\r\n}\r\n\r\ninterface QualityAlert {\r\n  type: string;\r\n  severity: 'low' | 'medium' | 'high' | 'critical';\r\n  message: string;\r\n  triggeredAt: Date;\r\n  threshold: number;\r\n  actualValue: number;\r\n  recommendations: string[];\r\n}\r\n\r\ninterface TimeRange {\r\n  start: Date;\r\n  end: Date;\r\n}\r\n\r\ninterface PredictionStatistics {\r\n  totalPredictions: number;\r\n  successfulPredictions: number;\r\n  failedPredictions: number;\r\n  averageLatency: number;\r\n  p95Latency: number;\r\n  p99Latency: number;\r\n  throughput: number;\r\n  errorRate: number;\r\n  cacheHitRate: number;\r\n  modelUsage: ModelUsageStats[];\r\n  predictionDistribution: PredictionDistribution;\r\n}\r\n\r\ninterface ModelUsageStats {\r\n  modelId: string;\r\n  modelName: string;\r\n  requestCount: number;\r\n  averageLatency: number;\r\n  errorRate: number;\r\n  cacheHitRate: number;\r\n  resourceUsage: ResourceUsage;\r\n}\r\n\r\ninterface ResourceUsage {\r\n  cpu: number;\r\n  memory: number;\r\n  gpu?: number;\r\n  storage: number;\r\n}\r\n\r\ninterface PredictionDistribution {\r\n  byConfidence: ConfidenceDistribution[];\r\n  byPredictionValue: ValueDistribution[];\r\n  byTimeOfDay: TimeDistribution[];\r\n}\r\n\r\ninterface ConfidenceDistribution {\r\n  range: string;\r\n  count: number;\r\n  percentage: number;\r\n}\r\n\r\ninterface ValueDistribution {\r\n  value: any;\r\n  count: number;\r\n  percentage: number;\r\n}\r\n\r\ninterface TimeDistribution {\r\n  hour: number;\r\n  count: number;\r\n  averageLatency: number;\r\n}\r\n\r\ninterface ModelPerformanceMetrics {\r\n  modelId: string;\r\n  timeRange: TimeRange;\r\n  accuracy: number;\r\n  precision: number;\r\n  recall: number;\r\n  f1Score: number;\r\n  auc?: number;\r\n  latencyMetrics: LatencyMetrics;\r\n  throughputMetrics: ThroughputMetrics;\r\n  errorMetrics: ErrorMetrics;\r\n  resourceMetrics: ResourceMetrics;\r\n  driftMetrics: DriftMetrics;\r\n}\r\n\r\ninterface LatencyMetrics {\r\n  average: number;\r\n  median: number;\r\n  p95: number;\r\n  p99: number;\r\n  min: number;\r\n  max: number;\r\n}\r\n\r\ninterface ThroughputMetrics {\r\n  requestsPerSecond: number;\r\n  requestsPerMinute: number;\r\n  requestsPerHour: number;\r\n  peakThroughput: number;\r\n}\r\n\r\ninterface ErrorMetrics {\r\n  errorRate: number;\r\n  errorTypes: Record<string, number>;\r\n  errorTrends: ErrorTrend[];\r\n}\r\n\r\ninterface ErrorTrend {\r\n  timestamp: Date;\r\n  errorRate: number;\r\n  errorCount: number;\r\n}\r\n\r\ninterface ResourceMetrics {\r\n  cpuUtilization: number;\r\n  memoryUtilization: number;\r\n  gpuUtilization?: number;\r\n  storageUtilization: number;\r\n  networkIO: number;\r\n}\r\n\r\ninterface DriftMetrics {\r\n  dataDrift: number;\r\n  conceptDrift: number;\r\n  predictionDrift: number;\r\n  driftTrend: 'stable' | 'increasing' | 'decreasing';\r\n}\r\n\r\ninterface ModelWeight {\r\n  modelId: string;\r\n  weight: number;\r\n  lastUpdated: Date;\r\n}\r\n\r\ninterface ModelRegistration {\r\n  name: string;\r\n  version: string;\r\n  type: string;\r\n  framework: string;\r\n  modelPath: string;\r\n  configPath?: string;\r\n  metadata: Record<string, any>;\r\n  capabilities: ModelCapabilities;\r\n  requirements: ModelRequirements;\r\n}\r\n\r\ninterface ModelCapabilities {\r\n  inputTypes: string[];\r\n  outputTypes: string[];\r\n  supportedTasks: string[];\r\n  maxBatchSize: number;\r\n  supportsStreaming: boolean;\r\n  supportsExplanation: boolean;\r\n}\r\n\r\ninterface ModelRequirements {\r\n  minMemory: number;\r\n  minCpu: number;\r\n  minGpu?: number;\r\n  dependencies: string[];\r\n  environmentVariables: Record<string, string>;\r\n}\r\n\r\ninterface RegisteredModel {\r\n  id: string;\r\n  name: string;\r\n  version: string;\r\n  type: string;\r\n  status: 'loading' | 'ready' | 'error' | 'unloaded';\r\n  registeredAt: Date;\r\n  lastUsed: Date;\r\n  usageCount: number;\r\n  capabilities: ModelCapabilities;\r\n  performance: ModelPerformanceSummary;\r\n}\r\n\r\ninterface ModelPerformanceSummary {\r\n  averageLatency: number;\r\n  throughput: number;\r\n  errorRate: number;\r\n  accuracy?: number;\r\n  lastEvaluated: Date;\r\n}\r\n\r\ninterface ModelFilters {\r\n  type?: string;\r\n  status?: string;\r\n  framework?: string;\r\n  registeredAfter?: Date;\r\n  registeredBefore?: Date;\r\n  lastUsedAfter?: Date;\r\n  tags?: string[];\r\n}\r\n\r\ninterface ModelInfo {\r\n  id: string;\r\n  name: string;\r\n  version: string;\r\n  type: string;\r\n  framework: string;\r\n  status: string;\r\n  capabilities: ModelCapabilities;\r\n  requirements: ModelRequirements;\r\n  performance: ModelPerformanceMetrics;\r\n  metadata: Record<string, any>;\r\n  registeredAt: Date;\r\n  lastUsed: Date;\r\n  usageStatistics: ModelUsageStatistics;\r\n}\r\n\r\ninterface ModelUsageStatistics {\r\n  totalRequests: number;\r\n  successfulRequests: number;\r\n  failedRequests: number;\r\n  averageLatency: number;\r\n  peakLatency: number;\r\n  throughput: number;\r\n  resourceUsage: ResourceUsage;\r\n  costMetrics: CostMetrics;\r\n}\r\n\r\ninterface CostMetrics {\r\n  totalCost: number;\r\n  costPerRequest: number;\r\n  costPerHour: number;\r\n  currency: string;\r\n}\r\n\r\ninterface CacheStatistics {\r\n  hitRate: number;\r\n  missRate: number;\r\n  totalRequests: number;\r\n  cacheSize: number;\r\n  maxCacheSize: number;\r\n  evictionCount: number;\r\n  averageRetrievalTime: number;\r\n  memoryUsage: number;\r\n}\r\n\r\ninterface CachingConfiguration {\r\n  enabled: boolean;\r\n  maxSize: number;\r\n  ttl: number;\r\n  evictionPolicy: 'lru' | 'lfu' | 'fifo';\r\n  compressionEnabled: boolean;\r\n  keyStrategy: 'hash' | 'full';\r\n}\r\n\r\ninterface RateLimitConfiguration {\r\n  requestsPerSecond: number;\r\n  requestsPerMinute: number;\r\n  requestsPerHour: number;\r\n  burstLimit: number;\r\n  queueSize: number;\r\n  timeoutMs: number;\r\n}\r\n\r\ninterface RateLimitStatus {\r\n  currentRequests: number;\r\n  remainingRequests: number;\r\n  resetTime: Date;\r\n  queuedRequests: number;\r\n  rejectedRequests: number;\r\n}\r\n\r\ninterface BatchPredictionJob {\r\n  name: string;\r\n  modelId: string;\r\n  inputSource: DataSource;\r\n  outputDestination: DataDestination;\r\n  batchSize: number;\r\n  priority: 'low' | 'normal' | 'high';\r\n  options: PredictionOptions;\r\n  schedule?: ScheduleConfig;\r\n}\r\n\r\ninterface DataSource {\r\n  type: 'file' | 'database' | 'stream' | 'api';\r\n  location: string;\r\n  format: string;\r\n  credentials?: Record<string, any>;\r\n  configuration?: Record<string, any>;\r\n}\r\n\r\ninterface DataDestination {\r\n  type: 'file' | 'database' | 'stream' | 'api';\r\n  location: string;\r\n  format: string;\r\n  credentials?: Record<string, any>;\r\n  configuration?: Record<string, any>;\r\n}\r\n\r\ninterface ScheduleConfig {\r\n  type: 'once' | 'recurring';\r\n  startTime: Date;\r\n  recurrence?: RecurrencePattern;\r\n}\r\n\r\ninterface RecurrencePattern {\r\n  frequency: 'hourly' | 'daily' | 'weekly' | 'monthly';\r\n  interval: number;\r\n  endDate?: Date;\r\n  maxOccurrences?: number;\r\n}\r\n\r\ninterface BatchJobStatus {\r\n  jobId: string;\r\n  status: 'queued' | 'running' | 'completed' | 'failed' | 'cancelled';\r\n  progress: JobProgress;\r\n  startedAt?: Date;\r\n  completedAt?: Date;\r\n  estimatedCompletion?: Date;\r\n  processedRecords: number;\r\n  totalRecords: number;\r\n  errorCount: number;\r\n  warnings: string[];\r\n  error?: string;\r\n}\r\n\r\ninterface JobProgress {\r\n  percentage: number;\r\n  currentBatch: number;\r\n  totalBatches: number;\r\n  recordsPerSecond: number;\r\n  elapsedTime: number;\r\n  estimatedTimeRemaining: number;\r\n}\r\n\r\ninterface BatchPredictionResult {\r\n  jobId: string;\r\n  totalRecords: number;\r\n  successfulPredictions: number;\r\n  failedPredictions: number;\r\n  results: PredictionResult[];\r\n  summary: BatchSummary;\r\n  downloadUrl?: string;\r\n}\r\n\r\ninterface BatchSummary {\r\n  averageConfidence: number;\r\n  predictionDistribution: Record<string, number>;\r\n  processingTime: number;\r\n  throughput: number;\r\n  qualityMetrics: QualityMetrics;\r\n}\r\n\r\ninterface ExportRequest {\r\n  format: 'csv' | 'json' | 'parquet' | 'excel';\r\n  filters: ExportFilters;\r\n  includeMetadata: boolean;\r\n  compression?: 'gzip' | 'zip';\r\n}\r\n\r\ninterface ExportFilters {\r\n  timeRange?: TimeRange;\r\n  modelIds?: string[];\r\n  confidenceRange?: [number, number];\r\n  predictionTypes?: string[];\r\n  includeErrors?: boolean;\r\n}\r\n\r\ninterface ExportResult {\r\n  exportId: string;\r\n  status: 'processing' | 'completed' | 'failed';\r\n  downloadUrl?: string;\r\n  fileSize?: number;\r\n  recordCount?: number;\r\n  expiresAt?: Date;\r\n  error?: string;\r\n}"], "version": 3}