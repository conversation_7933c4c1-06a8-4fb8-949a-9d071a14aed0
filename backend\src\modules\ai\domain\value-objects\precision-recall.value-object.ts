import { BaseValueObject } from '../../../../shared-kernel/value-objects/base-value-object';

/**
 * Precision Recall Value Object
 * 
 * Represents precision and recall metrics for AI model performance evaluation.
 * Provides utility methods for calculating F1 score, F-beta score, and
 * analyzing the precision-recall trade-off.
 */

export interface PrecisionRecallProps {
  precision: number;
  recall: number;
}

export class PrecisionRecall extends BaseValueObject<PrecisionRecallProps> {
  public static readonly MIN_VALUE = 0.0;
  public static readonly MAX_VALUE = 1.0;

  constructor(precision: number, recall: number) {
    super({ precision, recall });
  }

  protected validate(): void {
    super.validate();
    
    if (typeof this._value.precision !== 'number' || typeof this._value.recall !== 'number') {
      throw new Error('Precision and recall must be numbers');
    }

    if (isNaN(this._value.precision) || isNaN(this._value.recall)) {
      throw new Error('Precision and recall cannot be NaN');
    }

    if (!isFinite(this._value.precision) || !isFinite(this._value.recall)) {
      throw new Error('Precision and recall must be finite');
    }

    if (this._value.precision < PrecisionRecall.MIN_VALUE || this._value.precision > PrecisionRecall.MAX_VALUE) {
      throw new Error(`Precision must be between ${PrecisionRecall.MIN_VALUE} and ${PrecisionRecall.MAX_VALUE}`);
    }

    if (this._value.recall < PrecisionRecall.MIN_VALUE || this._value.recall > PrecisionRecall.MAX_VALUE) {
      throw new Error(`Recall must be between ${PrecisionRecall.MIN_VALUE} and ${PrecisionRecall.MAX_VALUE}`);
    }
  }

  /**
   * Creates precision-recall from confusion matrix values
   */
  public static fromConfusionMatrix(
    truePositives: number,
    falsePositives: number,
    falseNegatives: number
  ): PrecisionRecall {
    if (truePositives < 0 || falsePositives < 0 || falseNegatives < 0) {
      throw new Error('Confusion matrix values cannot be negative');
    }

    const precision = (truePositives + falsePositives) === 0 ? 0 : truePositives / (truePositives + falsePositives);
    const recall = (truePositives + falseNegatives) === 0 ? 0 : truePositives / (truePositives + falseNegatives);

    return new PrecisionRecall(precision, recall);
  }

  /**
   * Creates precision-recall from percentages (0-100)
   */
  public static fromPercentages(precisionPercent: number, recallPercent: number): PrecisionRecall {
    if (precisionPercent < 0 || precisionPercent > 100 || recallPercent < 0 || recallPercent > 100) {
      throw new Error('Percentages must be between 0 and 100');
    }
    return new PrecisionRecall(precisionPercent / 100, recallPercent / 100);
  }

  /**
   * Creates perfect precision-recall (1.0, 1.0)
   */
  public static perfect(): PrecisionRecall {
    return new PrecisionRecall(PrecisionRecall.MAX_VALUE, PrecisionRecall.MAX_VALUE);
  }

  /**
   * Creates zero precision-recall (0.0, 0.0)
   */
  public static zero(): PrecisionRecall {
    return new PrecisionRecall(PrecisionRecall.MIN_VALUE, PrecisionRecall.MIN_VALUE);
  }

  /**
   * Gets the precision value
   */
  get precision(): number {
    return this._value.precision;
  }

  /**
   * Gets the recall value
   */
  get recall(): number {
    return this._value.recall;
  }

  /**
   * Calculates the F1 score (harmonic mean of precision and recall)
   */
  public getF1Score(): number {
    if (this._value.precision + this._value.recall === 0) {
      return 0;
    }
    return (2 * this._value.precision * this._value.recall) / (this._value.precision + this._value.recall);
  }

  /**
   * Calculates the F-beta score with custom beta parameter
   * Beta > 1 gives more weight to recall
   * Beta < 1 gives more weight to precision
   */
  public getFBetaScore(beta: number): number {
    if (beta <= 0) {
      throw new Error('Beta must be greater than 0');
    }

    const betaSquared = beta * beta;
    const denominator = (betaSquared * this._value.precision) + this._value.recall;
    
    if (denominator === 0) {
      return 0;
    }
    
    return ((1 + betaSquared) * this._value.precision * this._value.recall) / denominator;
  }

  /**
   * Calculates the F2 score (gives more weight to recall)
   */
  public getF2Score(): number {
    return this.getFBetaScore(2);
  }

  /**
   * Calculates the F0.5 score (gives more weight to precision)
   */
  public getF05Score(): number {
    return this.getFBetaScore(0.5);
  }

  /**
   * Gets the harmonic mean of precision and recall
   */
  public getHarmonicMean(): number {
    return this.getF1Score();
  }

  /**
   * Gets the arithmetic mean of precision and recall
   */
  public getArithmeticMean(): number {
    return (this._value.precision + this._value.recall) / 2;
  }

  /**
   * Gets the geometric mean of precision and recall
   */
  public getGeometricMean(): number {
    return Math.sqrt(this._value.precision * this._value.recall);
  }

  /**
   * Checks if precision is higher than recall
   */
  public isPrecisionHigher(): boolean {
    return this._value.precision > this._value.recall;
  }

  /**
   * Checks if recall is higher than precision
   */
  public isRecallHigher(): boolean {
    return this._value.recall > this._value.precision;
  }

  /**
   * Checks if precision and recall are balanced (within threshold)
   */
  public isBalanced(threshold: number = 0.05): boolean {
    return Math.abs(this._value.precision - this._value.recall) <= threshold;
  }

  /**
   * Gets the precision-recall trade-off ratio
   */
  public getTradeOffRatio(): number {
    if (this._value.recall === 0) {
      return this._value.precision === 0 ? 1 : Infinity;
    }
    return this._value.precision / this._value.recall;
  }

  /**
   * Checks if both precision and recall meet minimum thresholds
   */
  public meetsBothThresholds(precisionThreshold: number, recallThreshold: number): boolean {
    return this._value.precision >= precisionThreshold && this._value.recall >= recallThreshold;
  }

  /**
   * Checks if either precision or recall meets the threshold
   */
  public meetsEitherThreshold(precisionThreshold: number, recallThreshold: number): boolean {
    return this._value.precision >= precisionThreshold || this._value.recall >= recallThreshold;
  }

  /**
   * Gets precision as a percentage
   */
  public getPrecisionPercentage(): number {
    return this._value.precision * 100;
  }

  /**
   * Gets recall as a percentage
   */
  public getRecallPercentage(): number {
    return this._value.recall * 100;
  }

  /**
   * Gets precision as a percentage string
   */
  public getPrecisionPercentageString(decimals: number = 1): string {
    return `${(this._value.precision * 100).toFixed(decimals)}%`;
  }

  /**
   * Gets recall as a percentage string
   */
  public getRecallPercentageString(decimals: number = 1): string {
    return `${(this._value.recall * 100).toFixed(decimals)}%`;
  }

  /**
   * Combines this precision-recall with another using weighted average
   */
  public combineWith(other: PrecisionRecall, weight: number = 0.5): PrecisionRecall {
    if (weight < 0 || weight > 1) {
      throw new Error('Weight must be between 0 and 1');
    }
    
    const combinedPrecision = this._value.precision * weight + other._value.precision * (1 - weight);
    const combinedRecall = this._value.recall * weight + other._value.recall * (1 - weight);
    
    return new PrecisionRecall(combinedPrecision, combinedRecall);
  }

  /**
   * Gets the Euclidean distance from another precision-recall point
   */
  public distanceFrom(other: PrecisionRecall): number {
    const precisionDiff = this._value.precision - other._value.precision;
    const recallDiff = this._value.recall - other._value.recall;
    return Math.sqrt(precisionDiff * precisionDiff + recallDiff * recallDiff);
  }

  /**
   * Gets the Manhattan distance from another precision-recall point
   */
  public manhattanDistanceFrom(other: PrecisionRecall): number {
    return Math.abs(this._value.precision - other._value.precision) + 
           Math.abs(this._value.recall - other._value.recall);
  }

  /**
   * Checks if this precision-recall is better than another based on F1 score
   */
  public isBetterThan(other: PrecisionRecall): boolean {
    return this.getF1Score() > other.getF1Score();
  }

  /**
   * Rounds precision and recall to specified decimal places
   */
  public round(decimals: number = 3): PrecisionRecall {
    const factor = Math.pow(10, decimals);
    const roundedPrecision = Math.round(this._value.precision * factor) / factor;
    const roundedRecall = Math.round(this._value.recall * factor) / factor;
    return new PrecisionRecall(roundedPrecision, roundedRecall);
  }

  /**
   * Converts to a human-readable string
   */
  public toString(): string {
    return `Precision: ${this.getPrecisionPercentageString()}, Recall: ${this.getRecallPercentageString()}, F1: ${(this.getF1Score() * 100).toFixed(1)}%`;
  }

  /**
   * Converts to JSON representation
   */
  public toJSON(): Record<string, any> {
    return {
      precision: this._value.precision,
      recall: this._value.recall,
      precisionPercentage: this.getPrecisionPercentage(),
      recallPercentage: this.getRecallPercentage(),
      f1Score: this.getF1Score(),
      f2Score: this.getF2Score(),
      f05Score: this.getF05Score(),
      harmonicMean: this.getHarmonicMean(),
      arithmeticMean: this.getArithmeticMean(),
      geometricMean: this.getGeometricMean(),
      isBalanced: this.isBalanced(),
      tradeOffRatio: this.getTradeOffRatio(),
    };
  }

  /**
   * Creates a PrecisionRecall from JSON
   */
  public static fromJSON(json: Record<string, any>): PrecisionRecall {
    return new PrecisionRecall(json.precision, json.recall);
  }

  /**
   * Calculates the average of multiple precision-recall pairs
   */
  public static average(pairs: PrecisionRecall[]): PrecisionRecall {
    if (pairs.length === 0) {
      throw new Error('Cannot calculate average of empty array');
    }
    
    const sumPrecision = pairs.reduce((acc, pair) => acc + pair._value.precision, 0);
    const sumRecall = pairs.reduce((acc, pair) => acc + pair._value.recall, 0);
    
    return new PrecisionRecall(sumPrecision / pairs.length, sumRecall / pairs.length);
  }

  /**
   * Calculates the weighted average of multiple precision-recall pairs
   */
  public static weightedAverage(pairs: PrecisionRecall[], weights: number[]): PrecisionRecall {
    if (pairs.length === 0) {
      throw new Error('Cannot calculate weighted average of empty array');
    }
    
    if (pairs.length !== weights.length) {
      throw new Error('Pairs and weights arrays must have the same length');
    }
    
    const totalWeight = weights.reduce((acc, weight) => acc + weight, 0);
    if (totalWeight === 0) {
      throw new Error('Total weight cannot be zero');
    }
    
    const weightedPrecision = pairs.reduce((acc, pair, index) => acc + pair._value.precision * weights[index], 0);
    const weightedRecall = pairs.reduce((acc, pair, index) => acc + pair._value.recall * weights[index], 0);
    
    return new PrecisionRecall(weightedPrecision / totalWeight, weightedRecall / totalWeight);
  }

  /**
   * Finds the pair with the highest F1 score
   */
  public static bestF1(pairs: PrecisionRecall[]): PrecisionRecall {
    if (pairs.length === 0) {
      throw new Error('Cannot find best F1 of empty array');
    }
    
    return pairs.reduce((best, current) => 
      current.getF1Score() > best.getF1Score() ? current : best
    );
  }

  /**
   * Finds the pair with the highest precision
   */
  public static bestPrecision(pairs: PrecisionRecall[]): PrecisionRecall {
    if (pairs.length === 0) {
      throw new Error('Cannot find best precision of empty array');
    }
    
    return pairs.reduce((best, current) => 
      current._value.precision > best._value.precision ? current : best
    );
  }

  /**
   * Finds the pair with the highest recall
   */
  public static bestRecall(pairs: PrecisionRecall[]): PrecisionRecall {
    if (pairs.length === 0) {
      throw new Error('Cannot find best recall of empty array');
    }
    
    return pairs.reduce((best, current) => 
      current._value.recall > best._value.recall ? current : best
    );
  }
}