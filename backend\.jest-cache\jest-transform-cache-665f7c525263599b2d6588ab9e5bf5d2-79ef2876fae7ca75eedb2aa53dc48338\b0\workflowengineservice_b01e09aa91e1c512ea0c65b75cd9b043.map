{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\services\\workflow-engine.service.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,6CAAmD;AACnD,qCAAqC;AACrC,2CAA+C;AAC/C,yDAAsD;AACtD,qFAA0E;AAC1E,qGAAyF;AAEzF,iFAAoE;AACpE,uFAAiF;AACjF,mGAA6F;AAC7F,yGAAmG;AAGnG;;;;;;;;;;;;;;;;;;GAkBG;AAEI,IAAM,qBAAqB,6BAA3B,MAAM,qBAAqB;IAIhC,YAEE,mBAAmE,EAEnE,iBAAwE,EACvD,aAA4B,EAC5B,YAA2B,EAC3B,UAA8B,EAC9B,oBAAkD,EAClD,sBAA0D,EAC1D,yBAAgE;QARhE,wBAAmB,GAAnB,mBAAmB,CAA+B;QAElD,sBAAiB,GAAjB,iBAAiB,CAAsC;QACvD,kBAAa,GAAb,aAAa,CAAe;QAC5B,iBAAY,GAAZ,YAAY,CAAe;QAC3B,eAAU,GAAV,UAAU,CAAoB;QAC9B,yBAAoB,GAApB,oBAAoB,CAA8B;QAClD,2BAAsB,GAAtB,sBAAsB,CAAoC;QAC1D,8BAAyB,GAAzB,yBAAyB,CAAuC;QAblE,WAAM,GAAG,IAAI,eAAM,CAAC,uBAAqB,CAAC,IAAI,CAAC,CAAC;QAChD,sBAAiB,GAAqB,IAAI,GAAG,EAAE,CAAC;IAa9D,CAAC;IAEJ;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,QAA8B,EAAE,SAA4B,EAAE,UAA8B;QAChH,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;YAElE,4CAA4C;YAC5C,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,EAAE;gBACvC,QAAQ;gBACR,SAAS;gBACT,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,WAAW,EAAE,IAAI;gBACjB,OAAO,EAAE,UAAU,CAAC,OAAO,IAAI,EAAE;aAClC,CAAC,CAAC;YAEH,+BAA+B;YAC/B,MAAM,gBAAgB,GAAG;gBACvB,WAAW,EAAE,SAAS,CAAC,EAAE;gBACzB,UAAU,EAAE,QAAQ,CAAC,EAAE;gBACvB,KAAK,EAAE,UAAU,CAAC,KAAK,IAAI,EAAE;gBAC7B,OAAO,EAAE,UAAU,CAAC,OAAO,IAAI,EAAE;gBACjC,SAAS,EAAE,EAAE;gBACb,WAAW,EAAE,EAAE;gBACf,WAAW,EAAE,QAAQ,CAAC,UAAU,CAAC,SAAS;gBAC1C,MAAM,EAAE,SAAS;aAClB,CAAC;YAEF,sCAAsC;YACtC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,gBAAgB,EAAE,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YAEjG,qCAAqC;YACrC,MAAM,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;YAEhE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;YACnE,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC9E,MAAM,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YAC1E,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,6BAA6B;YAC7B,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,WAAW,CAAC,QAA8B,EAAE,OAAY,EAAE,MAAc;QACpF,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,MAAM,EAAE,CAAC,CAAC;YAE/C,uBAAuB;YACvB,MAAM,cAAc,GAAG,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC;YAClF,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,MAAM,IAAI,KAAK,CAAC,mBAAmB,MAAM,EAAE,CAAC,CAAC;YAC/C,CAAC;YAED,gCAAgC;YAChC,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;gBAChD,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,MAAM;gBACN,MAAM,EAAE,SAAS;gBACjB,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,EAAE;gBACxC,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAE/C,2CAA2C;YAC3C,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC;YAE7B,0BAA0B;YAC1B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBAC9C,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,MAAM;gBACN,QAAQ,EAAE,cAAc,CAAC,IAAI;gBAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,qBAAqB;YACrB,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAE9E,eAAe;YACf,MAAM,UAAU,GAAG,MAAM,SAAS,CAAC,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;YAEpE,kCAAkC;YAClC,WAAW,CAAC,MAAM,GAAG,WAAW,CAAC;YACjC,WAAW,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;YACrC,WAAW,CAAC,MAAM,GAAG,UAAU,CAAC;YAChC,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAE/C,+BAA+B;YAC/B,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,UAAU,CAAC;YAEzC,4BAA4B;YAC5B,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAExD,4BAA4B;YAC5B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,yBAAyB,EAAE;gBAChD,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,MAAM;gBACN,MAAM,EAAE,UAAU;gBAClB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,yBAAyB;YACzB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;YAErF,qBAAqB;YACrB,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC3B,mCAAmC;gBACnC,OAAO,OAAO,CAAC,WAAW,CAAC;YAC7B,CAAC;iBAAM,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAClC,0CAA0C;gBAC1C,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YACjE,CAAC;iBAAM,CAAC;gBACN,2CAA2C;gBAC3C,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;YACvE,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,MAAM,MAAM,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAEtF,iCAAiC;YACjC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;gBACvD,KAAK,EAAE,EAAE,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE,MAAM,EAAE;aACpD,CAAC,CAAC;YAEH,IAAI,WAAW,EAAE,CAAC;gBAChB,WAAW,CAAC,MAAM,GAAG,QAAQ,CAAC;gBAC9B,WAAW,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;gBACrC,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC;gBAClC,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACjD,CAAC;YAED,yBAAyB;YACzB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,sBAAsB,EAAE;gBAC7C,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,MAAM;gBACN,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,4DAA4D;YAC5D,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAAC,QAA8B,EAAE,OAAY,EAAE,OAAiB;QAChG,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAErE,gCAAgC;YAChC,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CACxC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,EAAE,GAAG,OAAO,EAAE,EAAE,MAAM,CAAC,CACnD,CAAC;YAEF,iCAAiC;YACjC,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;YAEvD,kBAAkB;YAClB,MAAM,iBAAiB,GAAG,EAAE,CAAC;YAC7B,MAAM,aAAa,GAAG,EAAE,CAAC;YAEzB,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAChC,IAAI,MAAM,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;oBAClC,iBAAiB,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;gBAC3E,CAAC;qBAAM,CAAC;oBACN,aAAa,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;gBACvE,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,yBAAyB;YACzB,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7B,MAAM,uBAAuB,GAAG,QAAQ,CAAC,UAAU,CAAC,aAAa,EAAE,uBAAuB,IAAI,UAAU,CAAC;gBAEzG,IAAI,uBAAuB,KAAK,UAAU,EAAE,CAAC;oBAC3C,MAAM,IAAI,KAAK,CAAC,mCAAmC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACnG,CAAC;qBAAM,IAAI,uBAAuB,KAAK,6BAA6B,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACrG,mCAAmC;oBACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;gBAClF,CAAC;YACH,CAAC;YAED,6BAA6B;YAC7B,iBAAiB,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE;gBAC/C,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;YACvC,CAAC,CAAC,CAAC;YAEH,OAAO,OAAO,CAAC,WAAW,CAAC;QAE7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACnF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,cAAmB,EAAE,UAAe,EAAE,OAAY;QACjF,IAAI,CAAC;YACH,2CAA2C;YAC3C,IAAI,cAAc,CAAC,SAAS,IAAI,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC;gBACxE,MAAM,SAAS,GAAG,EAAE,CAAC;gBAErB,KAAK,MAAM,cAAc,IAAI,cAAc,CAAC,SAAS,EAAE,CAAC;oBACtD,IAAI,cAAc,CAAC,SAAS,EAAE,CAAC;wBAC7B,uCAAuC;wBACvC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAC7D,cAAc,CAAC,SAAS,EACxB,EAAE,UAAU,EAAE,OAAO,EAAE,CACxB,CAAC;wBAEF,IAAI,eAAe,EAAE,CAAC;4BACpB,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;wBACxC,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,+BAA+B;wBAC/B,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;oBACxC,CAAC;gBACH,CAAC;gBAED,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,6BAA6B;YAC7B,IAAI,cAAc,CAAC,QAAQ,EAAE,CAAC;gBAC5B,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACnC,CAAC;YAED,wBAAwB;YACxB,OAAO,EAAE,CAAC;QAEZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACnF,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,QAA8B,EAAE,OAAY,EAAE,MAAc,EAAE,KAAY;QACxG,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC;YAClF,MAAM,aAAa,GAAG,cAAc,EAAE,aAAa,IAAI,QAAQ,CAAC,UAAU,CAAC,aAAa,IAAI,EAAE,CAAC;YAE/F,4BAA4B;YAC5B,IAAI,aAAa,CAAC,KAAK,EAAE,CAAC;gBACxB,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACrD,MAAM,UAAU,GAAG,aAAa,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC;gBAExD,IAAI,UAAU,GAAG,UAAU,EAAE,CAAC;oBAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,MAAM,aAAa,UAAU,GAAG,CAAC,IAAI,UAAU,EAAE,CAAC,CAAC;oBAEtF,qBAAqB;oBACrB,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;oBAC9C,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC;oBAE5C,uBAAuB;oBACvB,IAAI,aAAa,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;wBAC9B,MAAM,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBAC9C,CAAC;oBAED,uBAAuB;oBACvB,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;gBAC3D,CAAC;YACH,CAAC;YAED,sBAAsB;YACtB,IAAI,aAAa,CAAC,YAAY,EAAE,CAAC;gBAC/B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,aAAa,CAAC,YAAY,EAAE,CAAC,CAAC;gBAC5E,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;YAC/E,CAAC;YAED,6CAA6C;YAC7C,IAAI,aAAa,CAAC,eAAe,EAAE,CAAC;gBAClC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6CAA6C,MAAM,EAAE,CAAC,CAAC;gBACxE,OAAO,OAAO,CAAC,WAAW,CAAC;YAC7B,CAAC;YAED,oCAAoC;YACpC,MAAM,KAAK,CAAC;QAEd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,WAAmB;QACvC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,WAAW,EAAE,CAAC,CAAC;YAE1D,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YACjE,IAAI,gBAAgB,EAAE,CAAC;gBACrB,8BAA8B;gBAC9B,gBAAgB,CAAC,SAAS,GAAG,IAAI,CAAC;gBAElC,2BAA2B;gBAC3B,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;gBAE3C,iCAAiC;gBACjC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YAC7C,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,WAAW,EAAE,CAAC,CAAC;QAE3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC/E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,WAAmB,EAAE,MAAc,EAAE,MAAY,EAAE,KAAc;QAC/F,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,CAAC,CAAC;YACzF,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,OAAO;YACT,CAAC;YAED,SAAS,CAAC,MAAM,GAAG,MAAM,CAAC;YAC1B,SAAS,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;YACnC,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YACrF,SAAS,CAAC,MAAM,GAAG,MAAM,CAAC;YAC1B,SAAS,CAAC,KAAK,GAAG,KAAK,CAAC;YAExB,+BAA+B;YAC/B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;gBACxD,KAAK,EAAE,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE;aAC5C,CAAC,CAAC;YACH,SAAS,CAAC,cAAc,GAAG,cAAc,CAAC;YAE1C,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAE/C,iCAAiC;YACjC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,8BAA8B,EAAE;gBACrD,WAAW;gBACX,MAAM;gBACN,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,MAAM;gBACN,KAAK;gBACL,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QACnF,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB,CAAC,WAAmB;QACvD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,CAAC,CAAC;YACzF,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,OAAO;YACT,CAAC;YAED,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;gBACxD,KAAK,EAAE,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE;aAC5C,CAAC,CAAC;YAEH,SAAS,CAAC,cAAc,GAAG,cAAc,CAAC;YAC1C,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QAC1F,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,WAAmB;QAClD,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;gBACrD,KAAK,EAAE,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE;aAC1C,CAAC,CAAC;YAEH,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;gBAChC,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;gBAC1B,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;gBAC9B,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QACrF,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,uBAAuB,CAAC,WAAmB;QACzC,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,uBAAuB;QACrB,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IACzC,CAAC;CACF,CAAA;AA1bY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;IAMR,WAAA,IAAA,0BAAgB,EAAC,6CAAiB,CAAC,CAAA;IAEnC,WAAA,IAAA,0BAAgB,EAAC,4DAAwB,CAAC,CAAA;yDADL,oBAAU,oBAAV,oBAAU,oDAEZ,oBAAU,oBAAV,oBAAU,oDACd,sBAAa,oBAAb,sBAAa,oDACd,6BAAa,oBAAb,6BAAa,oDACf,iDAAkB,oBAAlB,iDAAkB,oDACR,8DAA4B,oBAA5B,8DAA4B,oDAC1B,0EAAkC,oBAAlC,0EAAkC,oDAC/B,gFAAqC,oBAArC,gFAAqC;GAdxE,qBAAqB,CA0bjC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\services\\workflow-engine.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { EventEmitter2 } from '@nestjs/event-emitter';\r\nimport { WorkflowExecution } from '../entities/workflow-execution.entity';\r\nimport { WorkflowExecutionContext } from '../entities/workflow-execution-context.entity';\r\nimport { NotificationWorkflow } from '../entities/notification-workflow.entity';\r\nimport { WorkflowRuleEngine } from './workflow-rule-engine.service';\r\nimport { WorkflowStepProcessorFactory } from './workflow-step-processor.factory';\r\nimport { NotificationQueueManagementService } from './notification-queue-management.service';\r\nimport { NotificationTemplateManagementService } from './notification-template-management.service';\r\nimport { ExecuteWorkflowDto } from '../dto/execute-workflow.dto';\r\n\r\n/**\r\n * Workflow Engine Service\r\n * \r\n * Core workflow execution engine providing:\r\n * - Step-by-step workflow execution with state management\r\n * - Conditional logic evaluation and branching\r\n * - Parallel and sequential step processing\r\n * - Error handling and recovery mechanisms\r\n * - Integration with notification infrastructure\r\n * - Real-time execution monitoring and tracking\r\n * \r\n * Features:\r\n * - Advanced workflow orchestration with complex logic\r\n * - State persistence and recovery capabilities\r\n * - Performance optimization with parallel execution\r\n * - Comprehensive error handling and retry mechanisms\r\n * - Integration with external systems and APIs\r\n * - Real-time monitoring and execution tracking\r\n */\r\n@Injectable()\r\nexport class WorkflowEngineService {\r\n  private readonly logger = new Logger(WorkflowEngineService.name);\r\n  private readonly runningExecutions: Map<string, any> = new Map();\r\n\r\n  constructor(\r\n    @InjectRepository(WorkflowExecution)\r\n    private readonly executionRepository: Repository<WorkflowExecution>,\r\n    @InjectRepository(WorkflowExecutionContext)\r\n    private readonly contextRepository: Repository<WorkflowExecutionContext>,\r\n    private readonly configService: ConfigService,\r\n    private readonly eventEmitter: EventEmitter2,\r\n    private readonly ruleEngine: WorkflowRuleEngine,\r\n    private readonly stepProcessorFactory: WorkflowStepProcessorFactory,\r\n    private readonly queueManagementService: NotificationQueueManagementService,\r\n    private readonly templateManagementService: NotificationTemplateManagementService\r\n  ) {}\r\n\r\n  /**\r\n   * Execute workflow\r\n   */\r\n  async executeWorkflow(workflow: NotificationWorkflow, execution: WorkflowExecution, executeDto: ExecuteWorkflowDto): Promise<any> {\r\n    try {\r\n      this.logger.debug(`Starting workflow execution: ${execution.id}`);\r\n\r\n      // Store execution in running executions map\r\n      this.runningExecutions.set(execution.id, {\r\n        workflow,\r\n        execution,\r\n        startTime: Date.now(),\r\n        currentStep: null,\r\n        context: executeDto.context || {},\r\n      });\r\n\r\n      // Initialize execution context\r\n      const executionContext = {\r\n        executionId: execution.id,\r\n        workflowId: workflow.id,\r\n        input: executeDto.input || {},\r\n        context: executeDto.context || {},\r\n        variables: {},\r\n        stepResults: {},\r\n        currentStep: workflow.definition.startStep,\r\n        status: 'running',\r\n      };\r\n\r\n      // Start execution from the start step\r\n      const result = await this.executeStep(workflow, executionContext, workflow.definition.startStep);\r\n\r\n      // Update execution with final result\r\n      await this.completeExecution(execution.id, 'completed', result);\r\n\r\n      this.logger.debug(`Workflow execution completed: ${execution.id}`);\r\n      return result;\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Workflow execution failed: ${error.message}`, error.stack);\r\n      await this.completeExecution(execution.id, 'failed', null, error.message);\r\n      throw error;\r\n    } finally {\r\n      // Clean up running execution\r\n      this.runningExecutions.delete(execution.id);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Execute individual step\r\n   */\r\n  private async executeStep(workflow: NotificationWorkflow, context: any, stepId: string): Promise<any> {\r\n    try {\r\n      this.logger.debug(`Executing step: ${stepId}`);\r\n\r\n      // Find step definition\r\n      const stepDefinition = workflow.definition.steps.find(step => step.id === stepId);\r\n      if (!stepDefinition) {\r\n        throw new Error(`Step not found: ${stepId}`);\r\n      }\r\n\r\n      // Create step execution context\r\n      const stepContext = this.contextRepository.create({\r\n        executionId: context.executionId,\r\n        stepId,\r\n        status: 'running',\r\n        input: context.stepResults[stepId] || {},\r\n        startedAt: new Date(),\r\n      });\r\n\r\n      await this.contextRepository.save(stepContext);\r\n\r\n      // Update current step in execution context\r\n      context.currentStep = stepId;\r\n\r\n      // Emit step started event\r\n      this.eventEmitter.emit('workflow.step.started', {\r\n        executionId: context.executionId,\r\n        stepId,\r\n        stepType: stepDefinition.type,\r\n        timestamp: new Date(),\r\n      });\r\n\r\n      // Get step processor\r\n      const processor = this.stepProcessorFactory.getProcessor(stepDefinition.type);\r\n\r\n      // Execute step\r\n      const stepResult = await processor.execute(stepDefinition, context);\r\n\r\n      // Update step context with result\r\n      stepContext.status = 'completed';\r\n      stepContext.completedAt = new Date();\r\n      stepContext.result = stepResult;\r\n      await this.contextRepository.save(stepContext);\r\n\r\n      // Store step result in context\r\n      context.stepResults[stepId] = stepResult;\r\n\r\n      // Update execution progress\r\n      await this.updateExecutionProgress(context.executionId);\r\n\r\n      // Emit step completed event\r\n      this.eventEmitter.emit('workflow.step.completed', {\r\n        executionId: context.executionId,\r\n        stepId,\r\n        result: stepResult,\r\n        timestamp: new Date(),\r\n      });\r\n\r\n      // Determine next step(s)\r\n      const nextSteps = await this.determineNextSteps(stepDefinition, stepResult, context);\r\n\r\n      // Execute next steps\r\n      if (nextSteps.length === 0) {\r\n        // No more steps, workflow complete\r\n        return context.stepResults;\r\n      } else if (nextSteps.length === 1) {\r\n        // Single next step, continue sequentially\r\n        return await this.executeStep(workflow, context, nextSteps[0]);\r\n      } else {\r\n        // Multiple next steps, execute in parallel\r\n        return await this.executeParallelSteps(workflow, context, nextSteps);\r\n      }\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Step execution failed: ${stepId} - ${error.message}`, error.stack);\r\n\r\n      // Update step context with error\r\n      const stepContext = await this.contextRepository.findOne({\r\n        where: { executionId: context.executionId, stepId },\r\n      });\r\n\r\n      if (stepContext) {\r\n        stepContext.status = 'failed';\r\n        stepContext.completedAt = new Date();\r\n        stepContext.error = error.message;\r\n        await this.contextRepository.save(stepContext);\r\n      }\r\n\r\n      // Emit step failed event\r\n      this.eventEmitter.emit('workflow.step.failed', {\r\n        executionId: context.executionId,\r\n        stepId,\r\n        error: error.message,\r\n        timestamp: new Date(),\r\n      });\r\n\r\n      // Handle step failure based on error handling configuration\r\n      return await this.handleStepFailure(workflow, context, stepId, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Execute parallel steps\r\n   */\r\n  private async executeParallelSteps(workflow: NotificationWorkflow, context: any, stepIds: string[]): Promise<any> {\r\n    try {\r\n      this.logger.debug(`Executing parallel steps: ${stepIds.join(', ')}`);\r\n\r\n      // Execute all steps in parallel\r\n      const stepPromises = stepIds.map(stepId => \r\n        this.executeStep(workflow, { ...context }, stepId)\r\n      );\r\n\r\n      // Wait for all steps to complete\r\n      const results = await Promise.allSettled(stepPromises);\r\n\r\n      // Process results\r\n      const successfulResults = [];\r\n      const failedResults = [];\r\n\r\n      results.forEach((result, index) => {\r\n        if (result.status === 'fulfilled') {\r\n          successfulResults.push({ stepId: stepIds[index], result: result.value });\r\n        } else {\r\n          failedResults.push({ stepId: stepIds[index], error: result.reason });\r\n        }\r\n      });\r\n\r\n      // Handle failures if any\r\n      if (failedResults.length > 0) {\r\n        const parallelFailureHandling = workflow.definition.errorHandling?.parallelFailureHandling || 'fail_all';\r\n        \r\n        if (parallelFailureHandling === 'fail_all') {\r\n          throw new Error(`Parallel step execution failed: ${failedResults.map(f => f.error).join(', ')}`);\r\n        } else if (parallelFailureHandling === 'continue_on_partial_success' && successfulResults.length > 0) {\r\n          // Continue with successful results\r\n          this.logger.warn(`Some parallel steps failed, continuing with partial success`);\r\n        }\r\n      }\r\n\r\n      // Merge results into context\r\n      successfulResults.forEach(({ stepId, result }) => {\r\n        context.stepResults[stepId] = result;\r\n      });\r\n\r\n      return context.stepResults;\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Parallel step execution failed: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Determine next steps based on current step result and conditions\r\n   */\r\n  private async determineNextSteps(stepDefinition: any, stepResult: any, context: any): Promise<string[]> {\r\n    try {\r\n      // Check if step has conditional next steps\r\n      if (stepDefinition.nextSteps && Array.isArray(stepDefinition.nextSteps)) {\r\n        const nextSteps = [];\r\n\r\n        for (const nextStepConfig of stepDefinition.nextSteps) {\r\n          if (nextStepConfig.condition) {\r\n            // Evaluate condition using rule engine\r\n            const conditionResult = await this.ruleEngine.evaluateCondition(\r\n              nextStepConfig.condition,\r\n              { stepResult, context }\r\n            );\r\n\r\n            if (conditionResult) {\r\n              nextSteps.push(nextStepConfig.stepId);\r\n            }\r\n          } else {\r\n            // No condition, always include\r\n            nextSteps.push(nextStepConfig.stepId);\r\n          }\r\n        }\r\n\r\n        return nextSteps;\r\n      }\r\n\r\n      // Check for single next step\r\n      if (stepDefinition.nextStep) {\r\n        return [stepDefinition.nextStep];\r\n      }\r\n\r\n      // No next steps defined\r\n      return [];\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to determine next steps: ${error.message}`, error.stack);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handle step failure\r\n   */\r\n  private async handleStepFailure(workflow: NotificationWorkflow, context: any, stepId: string, error: Error): Promise<any> {\r\n    try {\r\n      const stepDefinition = workflow.definition.steps.find(step => step.id === stepId);\r\n      const errorHandling = stepDefinition?.errorHandling || workflow.definition.errorHandling || {};\r\n\r\n      // Check retry configuration\r\n      if (errorHandling.retry) {\r\n        const retryCount = context.retryCount?.[stepId] || 0;\r\n        const maxRetries = errorHandling.retry.maxAttempts || 3;\r\n\r\n        if (retryCount < maxRetries) {\r\n          this.logger.debug(`Retrying step ${stepId}, attempt ${retryCount + 1}/${maxRetries}`);\r\n          \r\n          // Update retry count\r\n          context.retryCount = context.retryCount || {};\r\n          context.retryCount[stepId] = retryCount + 1;\r\n\r\n          // Wait for retry delay\r\n          if (errorHandling.retry.delay) {\r\n            await this.delay(errorHandling.retry.delay);\r\n          }\r\n\r\n          // Retry step execution\r\n          return await this.executeStep(workflow, context, stepId);\r\n        }\r\n      }\r\n\r\n      // Check fallback step\r\n      if (errorHandling.fallbackStep) {\r\n        this.logger.debug(`Executing fallback step: ${errorHandling.fallbackStep}`);\r\n        return await this.executeStep(workflow, context, errorHandling.fallbackStep);\r\n      }\r\n\r\n      // Check if workflow should continue on error\r\n      if (errorHandling.continueOnError) {\r\n        this.logger.warn(`Continuing workflow despite step failure: ${stepId}`);\r\n        return context.stepResults;\r\n      }\r\n\r\n      // Default: fail the entire workflow\r\n      throw error;\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Error handling failed: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cancel execution\r\n   */\r\n  async cancelExecution(executionId: string): Promise<void> {\r\n    try {\r\n      this.logger.debug(`Cancelling execution: ${executionId}`);\r\n\r\n      const runningExecution = this.runningExecutions.get(executionId);\r\n      if (runningExecution) {\r\n        // Mark execution as cancelled\r\n        runningExecution.cancelled = true;\r\n\r\n        // Cancel any running steps\r\n        await this.cancelRunningSteps(executionId);\r\n\r\n        // Remove from running executions\r\n        this.runningExecutions.delete(executionId);\r\n      }\r\n\r\n      this.logger.debug(`Execution cancelled: ${executionId}`);\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to cancel execution: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Complete execution\r\n   */\r\n  private async completeExecution(executionId: string, status: string, result?: any, error?: string): Promise<void> {\r\n    try {\r\n      const execution = await this.executionRepository.findOne({ where: { id: executionId } });\r\n      if (!execution) {\r\n        return;\r\n      }\r\n\r\n      execution.status = status;\r\n      execution.completedAt = new Date();\r\n      execution.duration = execution.completedAt.getTime() - execution.startedAt.getTime();\r\n      execution.result = result;\r\n      execution.error = error;\r\n\r\n      // Update steps completed count\r\n      const completedSteps = await this.contextRepository.count({\r\n        where: { executionId, status: 'completed' },\r\n      });\r\n      execution.stepsCompleted = completedSteps;\r\n\r\n      await this.executionRepository.save(execution);\r\n\r\n      // Emit execution completed event\r\n      this.eventEmitter.emit('workflow.execution.completed', {\r\n        executionId,\r\n        status,\r\n        duration: execution.duration,\r\n        result,\r\n        error,\r\n        timestamp: new Date(),\r\n      });\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to complete execution: ${error.message}`, error.stack);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update execution progress\r\n   */\r\n  private async updateExecutionProgress(executionId: string): Promise<void> {\r\n    try {\r\n      const execution = await this.executionRepository.findOne({ where: { id: executionId } });\r\n      if (!execution) {\r\n        return;\r\n      }\r\n\r\n      const completedSteps = await this.contextRepository.count({\r\n        where: { executionId, status: 'completed' },\r\n      });\r\n\r\n      execution.stepsCompleted = completedSteps;\r\n      await this.executionRepository.save(execution);\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to update execution progress: ${error.message}`, error.stack);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cancel running steps\r\n   */\r\n  private async cancelRunningSteps(executionId: string): Promise<void> {\r\n    try {\r\n      const runningSteps = await this.contextRepository.find({\r\n        where: { executionId, status: 'running' },\r\n      });\r\n\r\n      for (const step of runningSteps) {\r\n        step.status = 'cancelled';\r\n        step.completedAt = new Date();\r\n        await this.contextRepository.save(step);\r\n      }\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to cancel running steps: ${error.message}`, error.stack);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Delay utility\r\n   */\r\n  private delay(ms: number): Promise<void> {\r\n    return new Promise(resolve => setTimeout(resolve, ms));\r\n  }\r\n\r\n  /**\r\n   * Get running execution info\r\n   */\r\n  getRunningExecutionInfo(executionId: string): any {\r\n    return this.runningExecutions.get(executionId);\r\n  }\r\n\r\n  /**\r\n   * Get all running executions\r\n   */\r\n  getAllRunningExecutions(): Map<string, any> {\r\n    return new Map(this.runningExecutions);\r\n  }\r\n}\r\n"], "version": 3}