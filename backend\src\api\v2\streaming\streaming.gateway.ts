import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  OnGatewayInit,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Logger, UseGuards, UsePipes, ValidationPipe } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';

import { StreamingService } from './streaming.service';
import { WsJwtGuard } from '../../../infrastructure/auth/guards/ws-jwt.guard';
import { LoggerService } from '../../../infrastructure/logging/logger.service';

/**
 * Stream subscription interface
 */
export interface StreamSubscription {
  id: string;
  userId: string;
  socketId: string;
  streamType: string;
  filters: Record<string, any>;
  createdAt: Date;
  lastActivity: Date;
}

/**
 * Stream message interface
 */
export interface StreamMessage {
  id: string;
  streamType: string;
  eventType: string;
  data: any;
  timestamp: Date;
  metadata?: Record<string, any>;
}

/**
 * Real-time streaming WebSocket gateway
 * Provides live security monitoring dashboards and real-time data streams
 */
@WebSocketGateway({
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
  },
  namespace: '/api/v2/streaming',
})
export class StreamingGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(StreamingGateway.name);
  private connectedClients = new Map<string, Socket>();
  private userSubscriptions = new Map<string, StreamSubscription[]>();

  constructor(
    private readonly streamingService: StreamingService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly loggerService: LoggerService,
  ) {}

  afterInit(server: Server) {
    this.logger.log('WebSocket Gateway initialized');
    this.streamingService.setGateway(this);
  }

  async handleConnection(client: Socket) {
    try {
      // Authenticate client
      const token = client.handshake.auth?.token || client.handshake.headers?.authorization?.replace('Bearer ', '');
      
      if (!token) {
        client.disconnect();
        return;
      }

      const payload = this.jwtService.verify(token);
      const userId = payload.sub;

      // Store client connection
      this.connectedClients.set(client.id, client);
      client.data.userId = userId;
      client.data.connectedAt = new Date();

      // Join user-specific room
      client.join(`user:${userId}`);

      this.logger.log('Client connected', {
        clientId: client.id,
        userId,
        userAgent: client.handshake.headers['user-agent'],
      });

      // Send connection confirmation
      client.emit('connected', {
        clientId: client.id,
        serverTime: new Date().toISOString(),
        availableStreams: await this.streamingService.getAvailableStreams(userId),
      });

    } catch (error) {
      this.loggerService.error('WebSocket authentication failed', error instanceof Error ? error.message : String(error), {
        clientId: client.id,
      });
      client.disconnect();
    }
  }

  handleDisconnect(client: Socket) {
    const userId = client.data?.userId;
    
    // Remove client connection
    this.connectedClients.delete(client.id);

    // Clean up subscriptions
    if (userId) {
      this.cleanupUserSubscriptions(userId, client.id);
    }

    this.logger.log('Client disconnected', {
      clientId: client.id,
      userId,
      duration: client.data?.connectedAt ? Date.now() - client.data.connectedAt.getTime() : 0,
    });
  }

  /**
   * Subscribe to security events stream
   */
  @SubscribeMessage('subscribe:security-events')
  @UseGuards(WsJwtGuard)
  @UsePipes(new ValidationPipe({ transform: true }))
  async subscribeToSecurityEvents(
    @MessageBody() data: {
      filters?: {
        severity?: string[];
        eventTypes?: string[];
        sources?: string[];
        timeWindow?: number;
      };
      realTime?: boolean;
      includeCorrelations?: boolean;
    },
    @ConnectedSocket() client: Socket,
  ): Promise<void> {
    const userId = client.data.userId;
    const subscriptionId = await this.streamingService.createSubscription({
      userId,
      socketId: client.id,
      streamType: 'security-events',
      filters: data.filters || {},
      options: {
        realTime: data.realTime !== false,
        includeCorrelations: data.includeCorrelations || false,
      },
    });

    // Join stream-specific room
    client.join(`stream:security-events:${subscriptionId}`);

    // Store subscription
    this.addUserSubscription(userId, {
      id: subscriptionId,
      userId,
      socketId: client.id,
      streamType: 'security-events',
      filters: data.filters || {},
      createdAt: new Date(),
      lastActivity: new Date(),
    });

    client.emit('subscription:created', {
      subscriptionId,
      streamType: 'security-events',
      filters: data.filters,
    });

    this.logger.log('Security events subscription created', {
      subscriptionId,
      userId,
      clientId: client.id,
      filters: data.filters,
    });
  }

  /**
   * Subscribe to vulnerability updates stream
   */
  @SubscribeMessage('subscribe:vulnerability-updates')
  @UseGuards(WsJwtGuard)
  @UsePipes(new ValidationPipe({ transform: true }))
  async subscribeToVulnerabilityUpdates(
    @MessageBody() data: {
      filters?: {
        severity?: string[];
        assetTypes?: string[];
        status?: string[];
      };
      includeRemediation?: boolean;
    },
    @ConnectedSocket() client: Socket,
  ): Promise<void> {
    const userId = client.data.userId;
    const subscriptionId = await this.streamingService.createSubscription({
      userId,
      socketId: client.id,
      streamType: 'vulnerability-updates',
      filters: data.filters || {},
      options: {
        includeRemediation: data.includeRemediation || false,
      },
    });

    client.join(`stream:vulnerability-updates:${subscriptionId}`);

    this.addUserSubscription(userId, {
      id: subscriptionId,
      userId,
      socketId: client.id,
      streamType: 'vulnerability-updates',
      filters: data.filters || {},
      createdAt: new Date(),
      lastActivity: new Date(),
    });

    client.emit('subscription:created', {
      subscriptionId,
      streamType: 'vulnerability-updates',
      filters: data.filters,
    });

    this.logger.log('Vulnerability updates subscription created', {
      subscriptionId,
      userId,
      clientId: client.id,
    });
  }

  /**
   * Subscribe to threat intelligence stream
   */
  @SubscribeMessage('subscribe:threat-intelligence')
  @UseGuards(WsJwtGuard)
  @UsePipes(new ValidationPipe({ transform: true }))
  async subscribeToThreatIntelligence(
    @MessageBody() data: {
      filters?: {
        threatTypes?: string[];
        confidence?: number;
        sources?: string[];
      };
      includeAttribution?: boolean;
    },
    @ConnectedSocket() client: Socket,
  ): Promise<void> {
    const userId = client.data.userId;
    const subscriptionId = await this.streamingService.createSubscription({
      userId,
      socketId: client.id,
      streamType: 'threat-intelligence',
      filters: data.filters || {},
      options: {
        includeAttribution: data.includeAttribution || false,
      },
    });

    client.join(`stream:threat-intelligence:${subscriptionId}`);

    this.addUserSubscription(userId, {
      id: subscriptionId,
      userId,
      socketId: client.id,
      streamType: 'threat-intelligence',
      filters: data.filters || {},
      createdAt: new Date(),
      lastActivity: new Date(),
    });

    client.emit('subscription:created', {
      subscriptionId,
      streamType: 'threat-intelligence',
      filters: data.filters,
    });

    this.logger.log('Threat intelligence subscription created', {
      subscriptionId,
      userId,
      clientId: client.id,
    });
  }

  /**
   * Subscribe to system metrics stream
   */
  @SubscribeMessage('subscribe:system-metrics')
  @UseGuards(WsJwtGuard)
  @UsePipes(new ValidationPipe({ transform: true }))
  async subscribeToSystemMetrics(
    @MessageBody() data: {
      metrics?: string[];
      interval?: number;
      aggregation?: string;
    },
    @ConnectedSocket() client: Socket,
  ): Promise<void> {
    const userId = client.data.userId;
    const subscriptionId = await this.streamingService.createSubscription({
      userId,
      socketId: client.id,
      streamType: 'system-metrics',
      filters: {
        metrics: data.metrics || ['cpu', 'memory', 'network', 'events'],
        interval: data.interval || 5000,
        aggregation: data.aggregation || 'average',
      },
    });

    client.join(`stream:system-metrics:${subscriptionId}`);

    this.addUserSubscription(userId, {
      id: subscriptionId,
      userId,
      socketId: client.id,
      streamType: 'system-metrics',
      filters: data,
      createdAt: new Date(),
      lastActivity: new Date(),
    });

    client.emit('subscription:created', {
      subscriptionId,
      streamType: 'system-metrics',
      interval: data.interval || 5000,
    });

    this.logger.log('System metrics subscription created', {
      subscriptionId,
      userId,
      clientId: client.id,
      metrics: data.metrics,
    });
  }

  /**
   * Subscribe to incident updates stream
   */
  @SubscribeMessage('subscribe:incident-updates')
  @UseGuards(WsJwtGuard)
  @UsePipes(new ValidationPipe({ transform: true }))
  async subscribeToIncidentUpdates(
    @MessageBody() data: {
      filters?: {
        severity?: string[];
        status?: string[];
        assignedTo?: string;
      };
      includeTimeline?: boolean;
    },
    @ConnectedSocket() client: Socket,
  ): Promise<void> {
    const userId = client.data.userId;
    const subscriptionId = await this.streamingService.createSubscription({
      userId,
      socketId: client.id,
      streamType: 'incident-updates',
      filters: data.filters || {},
      options: {
        includeTimeline: data.includeTimeline || false,
      },
    });

    client.join(`stream:incident-updates:${subscriptionId}`);

    this.addUserSubscription(userId, {
      id: subscriptionId,
      userId,
      socketId: client.id,
      streamType: 'incident-updates',
      filters: data.filters || {},
      createdAt: new Date(),
      lastActivity: new Date(),
    });

    client.emit('subscription:created', {
      subscriptionId,
      streamType: 'incident-updates',
      filters: data.filters,
    });

    this.logger.log('Incident updates subscription created', {
      subscriptionId,
      userId,
      clientId: client.id,
    });
  }

  /**
   * Unsubscribe from stream
   */
  @SubscribeMessage('unsubscribe')
  @UseGuards(WsJwtGuard)
  async unsubscribe(
    @MessageBody() data: { subscriptionId: string },
    @ConnectedSocket() client: Socket,
  ): Promise<void> {
    const userId = client.data.userId;
    
    await this.streamingService.removeSubscription(data.subscriptionId, userId);
    
    // Leave stream room
    const rooms = Array.from(client.rooms);
    const streamRoom = rooms.find(room => room.includes(data.subscriptionId));
    if (streamRoom) {
      client.leave(streamRoom);
    }

    // Remove from user subscriptions
    this.removeUserSubscription(userId, data.subscriptionId);

    client.emit('subscription:removed', {
      subscriptionId: data.subscriptionId,
    });

    this.logger.log('Subscription removed', {
      subscriptionId: data.subscriptionId,
      userId,
      clientId: client.id,
    });
  }

  /**
   * Get active subscriptions
   */
  @SubscribeMessage('get:subscriptions')
  @UseGuards(WsJwtGuard)
  async getSubscriptions(@ConnectedSocket() client: Socket): Promise<void> {
    const userId = client.data.userId;
    const subscriptions = this.userSubscriptions.get(userId) || [];

    client.emit('subscriptions:list', {
      subscriptions: subscriptions.map(sub => ({
        id: sub.id,
        streamType: sub.streamType,
        filters: sub.filters,
        createdAt: sub.createdAt,
        lastActivity: sub.lastActivity,
      })),
    });
  }

  /**
   * Broadcast message to specific stream
   */
  broadcastToStream(streamType: string, message: StreamMessage, filters?: Record<string, any>): void {
    // Find matching subscriptions
    const matchingSubscriptions = this.findMatchingSubscriptions(streamType, message, filters);

    for (const subscription of matchingSubscriptions) {
      const room = `stream:${streamType}:${subscription.id}`;
      this.server.to(room).emit('stream:data', {
        subscriptionId: subscription.id,
        streamType,
        message,
      });

      // Update last activity
      subscription.lastActivity = new Date();
    }

    if (matchingSubscriptions.length > 0) {
      this.logger.debug('Message broadcasted to stream', {
        streamType,
        messageId: message.id,
        subscriptionCount: matchingSubscriptions.length,
      });
    }
  }

  /**
   * Broadcast message to specific user
   */
  broadcastToUser(userId: string, message: any): void {
    this.server.to(`user:${userId}`).emit('user:notification', message);
  }

  /**
   * Broadcast system-wide message
   */
  broadcastSystemMessage(message: any): void {
    this.server.emit('system:message', message);
  }

  // Private helper methods

  private addUserSubscription(userId: string, subscription: StreamSubscription): void {
    const userSubs = this.userSubscriptions.get(userId) || [];
    userSubs.push(subscription);
    this.userSubscriptions.set(userId, userSubs);
  }

  private removeUserSubscription(userId: string, subscriptionId: string): void {
    const userSubs = this.userSubscriptions.get(userId) || [];
    const filtered = userSubs.filter(sub => sub.id !== subscriptionId);
    this.userSubscriptions.set(userId, filtered);
  }

  private cleanupUserSubscriptions(userId: string, socketId: string): void {
    const userSubs = this.userSubscriptions.get(userId) || [];
    const filtered = userSubs.filter(sub => sub.socketId !== socketId);
    this.userSubscriptions.set(userId, filtered);

    // Cleanup subscriptions in streaming service
    const removedSubs = userSubs.filter(sub => sub.socketId === socketId);
    for (const sub of removedSubs) {
      this.streamingService.removeSubscription(sub.id, userId).catch(error => {
        this.logger.error('Failed to cleanup subscription', { subscriptionId: sub.id, error: error.message });
      });
    }
  }

  private findMatchingSubscriptions(
    streamType: string,
    message: StreamMessage,
    filters?: Record<string, any>,
  ): StreamSubscription[] {
    const allSubscriptions = Array.from(this.userSubscriptions.values()).flat();
    
    return allSubscriptions.filter(subscription => {
      // Match stream type
      if (subscription.streamType !== streamType) {
        return false;
      }

      // Apply filters
      if (subscription.filters && Object.keys(subscription.filters).length > 0) {
        return this.messageMatchesFilters(message, subscription.filters);
      }

      return true;
    });
  }

  private messageMatchesFilters(message: StreamMessage, filters: Record<string, any>): boolean {
    // Implement filter matching logic based on message content
    // This is a simplified version
    
    for (const [filterKey, filterValue] of Object.entries(filters)) {
      if (Array.isArray(filterValue)) {
        // Array filter (e.g., severity: ['high', 'critical'])
        const messageValue = this.getMessageProperty(message, filterKey);
        if (!filterValue.includes(messageValue)) {
          return false;
        }
      } else if (typeof filterValue === 'string' || typeof filterValue === 'number') {
        // Exact match filter
        const messageValue = this.getMessageProperty(message, filterKey);
        if (messageValue !== filterValue) {
          return false;
        }
      }
    }

    return true;
  }

  private getMessageProperty(message: StreamMessage, propertyPath: string): any {
    // Get nested property from message data
    const parts = propertyPath.split('.');
    let value = message.data;
    
    for (const part of parts) {
      if (value && typeof value === 'object' && part in value) {
        value = value[part];
      } else {
        return undefined;
      }
    }
    
    return value;
  }
}
