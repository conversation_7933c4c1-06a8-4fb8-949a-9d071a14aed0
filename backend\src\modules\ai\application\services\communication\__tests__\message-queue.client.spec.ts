import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { getQueueToken } from '@nestjs/bull';
import { MessageQueueClient } from '../message-queue.client';
import { Queue, Job } from 'bull';

describe('MessageQueueClient', () => {
  let client: MessageQueueClient;
  let configService: jest.Mocked<ConfigService>;
  let analysisQueue: jest.Mocked<Queue>;
  let trainingQueue: jest.Mocked<Queue>;
  let predictionQueue: jest.Mocked<Queue>;
  let batchQueue: jest.Mocked<Queue>;

  const createMockQueue = (): jest.Mocked<Queue> => ({
    add: jest.fn(),
    getJob: jest.fn(),
    getWaiting: jest.fn(),
    getActive: jest.fn(),
    getCompleted: jest.fn(),
    getFailed: jest.fn(),
    getDelayed: jest.fn(),
    getPaused: jest.fn(),
    getJobLogs: jest.fn(),
    isPaused: jest.fn(),
    pause: jest.fn(),
    resume: jest.fn(),
    clean: jest.fn(),
  } as any);

  const createMockJob = (id: string, data: any, state = 'waiting'): jest.Mocked<Job> => ({
    id,
    data,
    timestamp: Date.now(),
    opts: { attempts: 3 },
    attemptsMade: 0,
    returnvalue: undefined,
    failedReason: undefined,
    processedOn: undefined,
    finishedOn: undefined,
    getState: jest.fn().mockResolvedValue(state),
    progress: jest.fn().mockReturnValue(0),
    remove: jest.fn(),
    retry: jest.fn(),
  } as any);

  beforeEach(async () => {
    const mockConfigService = {
      get: jest.fn().mockImplementation((key: string, defaultValue?: any) => {
        const config = {
          'ai.queue.attempts': 3,
          'ai.queue.backoffDelay': 2000,
          'ai.queue.removeOnComplete': 100,
          'ai.queue.removeOnFail': 50,
        };
        return config[key] || defaultValue;
      }),
    };

    analysisQueue = createMockQueue();
    trainingQueue = createMockQueue();
    predictionQueue = createMockQueue();
    batchQueue = createMockQueue();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MessageQueueClient,
        { provide: ConfigService, useValue: mockConfigService },
        { provide: getQueueToken('ai-analysis'), useValue: analysisQueue },
        { provide: getQueueToken('ai-training'), useValue: trainingQueue },
        { provide: getQueueToken('ai-prediction'), useValue: predictionQueue },
        { provide: getQueueToken('ai-batch'), useValue: batchQueue },
      ],
    }).compile();

    client = module.get<MessageQueueClient>(MessageQueueClient);
    configService = module.get(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('queueAnalysisRequest', () => {
    it('should queue analysis request successfully', async () => {
      const payload = {
        requestId: 'req-123',
        data: { event: 'suspicious_login' },
        model: 'threat-detector-v1',
      };

      const mockJob = createMockJob('job-123', payload);
      analysisQueue.add.mockResolvedValue(mockJob);

      const result = await client.queueAnalysisRequest(payload);

      expect(result).toMatchObject({
        jobId: 'job-123',
        requestId: 'req-123',
        queueName: 'ai-analysis',
        status: 'queued',
        priority: 0,
        delay: 0,
        timestamp: expect.any(Date),
      });

      expect(analysisQueue.add).toHaveBeenCalledWith(
        'analyze-data',
        payload,
        expect.objectContaining({
          attempts: 3,
          backoff: expect.objectContaining({
            type: 'exponential',
            delay: 2000,
          }),
          removeOnComplete: 100,
          removeOnFail: 50,
        })
      );
    });

    it('should handle analysis request with custom options', async () => {
      const payload = {
        requestId: 'req-456',
        data: { event: 'test' },
      };

      const options = {
        priority: 5,
        delay: 1000,
        timeout: 60000,
        attempts: 5,
      };

      const mockJob = createMockJob('job-456', payload);
      analysisQueue.add.mockResolvedValue(mockJob);

      const result = await client.queueAnalysisRequest(payload, options);

      expect(result.priority).toBe(5);
      expect(result.delay).toBe(1000);

      expect(analysisQueue.add).toHaveBeenCalledWith(
        'analyze-data',
        payload,
        expect.objectContaining({
          priority: 5,
          delay: 1000,
          timeout: 60000,
          attempts: 5,
        })
      );
    });

    it('should handle analysis request failure', async () => {
      const payload = {
        requestId: 'req-error',
        data: { event: 'test' },
      };

      analysisQueue.add.mockRejectedValue(new Error('Queue error'));

      await expect(client.queueAnalysisRequest(payload)).rejects.toThrow('Failed to queue analysis request');
    });
  });

  describe('queueTrainingRequest', () => {
    it('should queue training request successfully', async () => {
      const payload = {
        requestId: 'train-123',
        trainingData: [{ input: 'test', output: 'result' }],
        modelConfig: { type: 'classifier' },
      };

      const mockJob = createMockJob('job-train-123', payload);
      trainingQueue.add.mockResolvedValue(mockJob);

      const result = await client.queueTrainingRequest(payload);

      expect(result).toMatchObject({
        jobId: 'job-train-123',
        requestId: 'train-123',
        queueName: 'ai-training',
        status: 'queued',
        priority: 10, // Default higher priority for training
        timestamp: expect.any(Date),
      });

      expect(trainingQueue.add).toHaveBeenCalledWith(
        'train-model',
        payload,
        expect.objectContaining({
          priority: 10,
          timeout: 3600000, // 1 hour timeout
        })
      );
    });
  });

  describe('queuePredictionRequest', () => {
    it('should queue prediction request successfully', async () => {
      const payload = {
        requestId: 'pred-123',
        input: { features: [1, 2, 3] },
        model: 'classifier-v2',
      };

      const mockJob = createMockJob('job-pred-123', payload);
      predictionQueue.add.mockResolvedValue(mockJob);

      const result = await client.queuePredictionRequest(payload);

      expect(result).toMatchObject({
        jobId: 'job-pred-123',
        requestId: 'pred-123',
        queueName: 'ai-prediction',
        status: 'queued',
        priority: 5, // Default higher priority for predictions
        timestamp: expect.any(Date),
      });

      expect(predictionQueue.add).toHaveBeenCalledWith(
        'predict',
        payload,
        expect.objectContaining({
          priority: 5,
          timeout: 30000, // 30 seconds timeout
        })
      );
    });
  });

  describe('queueBatchRequest', () => {
    it('should queue batch request successfully', async () => {
      const payload = {
        requestId: 'batch-123',
        items: [
          { id: '1', type: 'analysis' as const, data: { event: 'test1' } },
          { id: '2', type: 'prediction' as const, data: { input: 'test2' } },
        ],
      };

      const mockJob = createMockJob('job-batch-123', payload);
      batchQueue.add.mockResolvedValue(mockJob);

      const result = await client.queueBatchRequest(payload);

      expect(result).toMatchObject({
        jobId: 'job-batch-123',
        requestId: 'batch-123',
        queueName: 'ai-batch',
        status: 'queued',
        priority: 1, // Default lower priority for batch
        timestamp: expect.any(Date),
      });

      expect(batchQueue.add).toHaveBeenCalledWith(
        'process-batch',
        payload,
        expect.objectContaining({
          priority: 1,
          timeout: 1800000, // 30 minutes timeout
        })
      );
    });
  });

  describe('getJobStatus', () => {
    it('should get job status successfully', async () => {
      const jobId = 'job-123';
      const queueName = 'ai-analysis';

      const mockJob = createMockJob(jobId, { requestId: 'req-123' }, 'active');
      mockJob.progress.mockReturnValue(50);
      mockJob.processedOn = Date.now() - 1000;

      analysisQueue.getJob.mockResolvedValue(mockJob);
      analysisQueue.getJobLogs.mockResolvedValue({ logs: ['Processing started'] });

      const result = await client.getJobStatus(jobId, queueName);

      expect(result).toMatchObject({
        jobId,
        queueName,
        status: 'active',
        progress: 50,
        data: { requestId: 'req-123' },
        attempts: 0,
        maxAttempts: 3,
        createdAt: expect.any(Date),
        processedAt: expect.any(Date),
        logs: ['Processing started'],
        timestamp: expect.any(Date),
      });
    });

    it('should handle job not found', async () => {
      const jobId = 'nonexistent-job';
      const queueName = 'ai-analysis';

      analysisQueue.getJob.mockResolvedValue(null);

      const result = await client.getJobStatus(jobId, queueName);

      expect(result).toMatchObject({
        jobId,
        queueName,
        status: 'not_found',
        timestamp: expect.any(Date),
      });
    });
  });

  describe('cancelJob', () => {
    it('should cancel job successfully', async () => {
      const jobId = 'job-123';
      const queueName = 'ai-analysis';

      const mockJob = createMockJob(jobId, {}, 'waiting');
      analysisQueue.getJob.mockResolvedValue(mockJob);

      const result = await client.cancelJob(jobId, queueName);

      expect(result).toBe(true);
      expect(mockJob.remove).toHaveBeenCalled();
    });

    it('should handle job not found for cancellation', async () => {
      const jobId = 'nonexistent-job';
      const queueName = 'ai-analysis';

      analysisQueue.getJob.mockResolvedValue(null);

      const result = await client.cancelJob(jobId, queueName);

      expect(result).toBe(false);
    });

    it('should not cancel completed job', async () => {
      const jobId = 'job-123';
      const queueName = 'ai-analysis';

      const mockJob = createMockJob(jobId, {}, 'completed');
      analysisQueue.getJob.mockResolvedValue(mockJob);

      const result = await client.cancelJob(jobId, queueName);

      expect(result).toBe(false);
      expect(mockJob.remove).not.toHaveBeenCalled();
    });
  });

  describe('retryJob', () => {
    it('should retry failed job successfully', async () => {
      const jobId = 'job-123';
      const queueName = 'ai-analysis';

      const mockJob = createMockJob(jobId, {}, 'failed');
      analysisQueue.getJob.mockResolvedValue(mockJob);

      const result = await client.retryJob(jobId, queueName);

      expect(result).toBe(true);
      expect(mockJob.retry).toHaveBeenCalled();
    });

    it('should handle job not found for retry', async () => {
      const jobId = 'nonexistent-job';
      const queueName = 'ai-analysis';

      analysisQueue.getJob.mockResolvedValue(null);

      const result = await client.retryJob(jobId, queueName);

      expect(result).toBe(false);
    });

    it('should not retry non-failed job', async () => {
      const jobId = 'job-123';
      const queueName = 'ai-analysis';

      const mockJob = createMockJob(jobId, {}, 'active');
      analysisQueue.getJob.mockResolvedValue(mockJob);

      const result = await client.retryJob(jobId, queueName);

      expect(result).toBe(false);
      expect(mockJob.retry).not.toHaveBeenCalled();
    });
  });

  describe('getQueueStats', () => {
    it('should get queue statistics successfully', async () => {
      const queueName = 'ai-analysis';

      analysisQueue.getWaiting.mockResolvedValue([{}, {}] as any); // 2 waiting
      analysisQueue.getActive.mockResolvedValue([{}] as any); // 1 active
      analysisQueue.getCompleted.mockResolvedValue([{}, {}, {}] as any); // 3 completed
      analysisQueue.getFailed.mockResolvedValue([{}] as any); // 1 failed
      analysisQueue.getDelayed.mockResolvedValue([] as any); // 0 delayed
      analysisQueue.getPaused.mockResolvedValue([] as any); // 0 paused
      analysisQueue.isPaused.mockResolvedValue(false);

      const result = await client.getQueueStats(queueName);

      expect(result).toMatchObject({
        queueName,
        counts: {
          waiting: 2,
          active: 1,
          completed: 3,
          failed: 1,
          delayed: 0,
          paused: 0,
        },
        isPaused: false,
        timestamp: expect.any(Date),
      });
    });
  });

  describe('getJobsByStatus', () => {
    it('should get waiting jobs successfully', async () => {
      const queueName = 'ai-analysis';
      const status = 'waiting';

      const mockJobs = [
        createMockJob('job-1', { requestId: 'req-1' }, 'waiting'),
        createMockJob('job-2', { requestId: 'req-2' }, 'waiting'),
      ];

      analysisQueue.getWaiting.mockResolvedValue(mockJobs);

      const result = await client.getJobsByStatus(queueName, status, 0, 10);

      expect(result).toHaveLength(2);
      expect(result[0]).toMatchObject({
        jobId: 'job-1',
        queueName,
        status: 'waiting',
        data: { requestId: 'req-1' },
        progress: 0,
        attempts: 0,
        maxAttempts: 3,
        createdAt: expect.any(Date),
      });
    });

    it('should handle invalid job status', async () => {
      const queueName = 'ai-analysis';
      const status = 'invalid' as any;

      await expect(client.getJobsByStatus(queueName, status)).rejects.toThrow('Invalid job status');
    });
  });

  describe('cleanQueue', () => {
    it('should clean completed jobs successfully', async () => {
      const queueName = 'ai-analysis';
      const grace = 24 * 60 * 60 * 1000; // 24 hours

      analysisQueue.clean.mockResolvedValue([{}, {}, {}] as any); // 3 cleaned jobs

      const result = await client.cleanQueue(queueName, grace, 'completed');

      expect(result).toBe(3);
      expect(analysisQueue.clean).toHaveBeenCalledWith(grace, 'completed');
    });
  });

  describe('pauseQueue', () => {
    it('should pause queue successfully', async () => {
      const queueName = 'ai-analysis';

      await client.pauseQueue(queueName);

      expect(analysisQueue.pause).toHaveBeenCalled();
    });
  });

  describe('resumeQueue', () => {
    it('should resume queue successfully', async () => {
      const queueName = 'ai-analysis';

      await client.resumeQueue(queueName);

      expect(analysisQueue.resume).toHaveBeenCalled();
    });
  });

  describe('scheduleJob', () => {
    it('should schedule job successfully', async () => {
      const queueName = 'ai-analysis';
      const jobType = 'analyze-data';
      const payload = { requestId: 'scheduled-req', data: { event: 'test' } };
      const scheduleTime = new Date(Date.now() + 60000); // 1 minute from now

      const mockJob = createMockJob('scheduled-job', payload);
      analysisQueue.add.mockResolvedValue(mockJob);

      const result = await client.scheduleJob(queueName, jobType, payload, scheduleTime);

      expect(result).toMatchObject({
        jobId: 'scheduled-job',
        requestId: 'scheduled-req',
        queueName,
        status: 'delayed',
        delay: expect.any(Number),
        timestamp: expect.any(Date),
      });

      expect(analysisQueue.add).toHaveBeenCalledWith(
        jobType,
        payload,
        expect.objectContaining({
          delay: expect.any(Number),
        })
      );
    });

    it('should handle past schedule time', async () => {
      const queueName = 'ai-analysis';
      const jobType = 'analyze-data';
      const payload = { requestId: 'past-req', data: { event: 'test' } };
      const scheduleTime = new Date(Date.now() - 60000); // 1 minute ago

      await expect(
        client.scheduleJob(queueName, jobType, payload, scheduleTime)
      ).rejects.toThrow('Schedule time must be in the future');
    });
  });

  describe('queue name validation', () => {
    it('should handle unknown queue name', async () => {
      const unknownQueue = 'unknown-queue';

      await expect(client.getQueueStats(unknownQueue)).rejects.toThrow('Unknown queue name');
    });
  });

  describe('configuration', () => {
    it('should use configured queue options', () => {
      configService.get.mockImplementation((key: string, defaultValue?: any) => {
        const config = {
          'ai.queue.attempts': 5,
          'ai.queue.backoffDelay': 3000,
          'ai.queue.removeOnComplete': 200,
          'ai.queue.removeOnFail': 100,
        };
        return config[key] || defaultValue;
      });

      // Create new instance to pick up new config
      const newClient = new MessageQueueClient(
        analysisQueue,
        trainingQueue,
        predictionQueue,
        batchQueue,
        configService
      );

      expect((newClient as any).defaultJobOptions).toMatchObject({
        attempts: 5,
        backoff: expect.objectContaining({
          delay: 3000,
        }),
        removeOnComplete: 200,
        removeOnFail: 100,
      });
    });
  });

  describe('error handling', () => {
    it('should handle queue operation errors', async () => {
      const jobId = 'error-job';
      const queueName = 'ai-analysis';

      analysisQueue.getJob.mockRejectedValue(new Error('Redis connection error'));

      await expect(client.getJobStatus(jobId, queueName)).rejects.toThrow('Failed to get job status');
    });
  });

  describe('ID generation', () => {
    it('should generate unique request IDs', () => {
      const id1 = (client as any).generateRequestId();
      const id2 = (client as any).generateRequestId();

      expect(id1).toBeDefined();
      expect(id2).toBeDefined();
      expect(id1).not.toBe(id2);
      expect(id1).toMatch(/^mq-req-/);
      expect(id2).toMatch(/^mq-req-/);
    });
  });
});