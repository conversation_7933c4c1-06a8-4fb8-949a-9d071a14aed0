import { BaseValueObject } from '../../../../shared-kernel/value-objects/base-value-object';

/**
 * Accuracy Score Value Object
 * 
 * Represents an accuracy score for AI model performance evaluation.
 * Ensures the score is within valid bounds (0.0 to 1.0) and provides
 * utility methods for accuracy level classification and comparison.
 */
export class AccuracyScore extends BaseValueObject<number> {
  public static readonly MIN_ACCURACY = 0.0;
  public static readonly MAX_ACCURACY = 1.0;
  
  // Accuracy level thresholds
  public static readonly POOR_THRESHOLD = 0.5;
  public static readonly FAIR_THRESHOLD = 0.7;
  public static readonly GOOD_THRESHOLD = 0.8;
  public static readonly EXCELLENT_THRESHOLD = 0.95;

  constructor(value: number) {
    super(value);
  }

  protected validate(): void {
    super.validate();
    
    if (typeof this._value !== 'number') {
      throw new Error('Accuracy score must be a number');
    }

    if (isNaN(this._value)) {
      throw new Error('Accuracy score cannot be NaN');
    }

    if (!isFinite(this._value)) {
      throw new Error('Accuracy score must be finite');
    }

    if (this._value < AccuracyScore.MIN_ACCURACY) {
      throw new Error(`Accuracy score cannot be less than ${AccuracyScore.MIN_ACCURACY}`);
    }

    if (this._value > AccuracyScore.MAX_ACCURACY) {
      throw new Error(`Accuracy score cannot be greater than ${AccuracyScore.MAX_ACCURACY}`);
    }
  }

  /**
   * Creates an accuracy score from correct and total predictions
   */
  public static fromPredictions(correct: number, total: number): AccuracyScore {
    if (total <= 0) {
      throw new Error('Total predictions must be greater than zero');
    }
    if (correct < 0) {
      throw new Error('Correct predictions cannot be negative');
    }
    if (correct > total) {
      throw new Error('Correct predictions cannot exceed total predictions');
    }
    
    return new AccuracyScore(correct / total);
  }

  /**
   * Creates an accuracy score from a percentage (0-100)
   */
  public static fromPercentage(percentage: number): AccuracyScore {
    if (percentage < 0 || percentage > 100) {
      throw new Error('Percentage must be between 0 and 100');
    }
    return new AccuracyScore(percentage / 100);
  }

  /**
   * Creates a perfect accuracy score
   */
  public static perfect(): AccuracyScore {
    return new AccuracyScore(AccuracyScore.MAX_ACCURACY);
  }

  /**
   * Creates a zero accuracy score
   */
  public static zero(): AccuracyScore {
    return new AccuracyScore(AccuracyScore.MIN_ACCURACY);
  }

  /**
   * Creates a random accuracy score (for baseline comparison)
   */
  public static random(): AccuracyScore {
    return new AccuracyScore(0.5);
  }

  /**
   * Gets the accuracy level as a string
   */
  public getLevel(): 'poor' | 'fair' | 'good' | 'very_good' | 'excellent' {
    if (this._value < AccuracyScore.POOR_THRESHOLD) {
      return 'poor';
    } else if (this._value < AccuracyScore.FAIR_THRESHOLD) {
      return 'fair';
    } else if (this._value < AccuracyScore.GOOD_THRESHOLD) {
      return 'good';
    } else if (this._value < AccuracyScore.EXCELLENT_THRESHOLD) {
      return 'very_good';
    } else {
      return 'excellent';
    }
  }

  /**
   * Checks if the accuracy is poor
   */
  public isPoor(): boolean {
    return this._value < AccuracyScore.POOR_THRESHOLD;
  }

  /**
   * Checks if the accuracy is fair
   */
  public isFair(): boolean {
    return this._value >= AccuracyScore.POOR_THRESHOLD && this._value < AccuracyScore.FAIR_THRESHOLD;
  }

  /**
   * Checks if the accuracy is good
   */
  public isGood(): boolean {
    return this._value >= AccuracyScore.GOOD_THRESHOLD;
  }

  /**
   * Checks if the accuracy is excellent
   */
  public isExcellent(): boolean {
    return this._value >= AccuracyScore.EXCELLENT_THRESHOLD;
  }

  /**
   * Checks if the accuracy meets a minimum threshold
   */
  public meetsThreshold(threshold: number): boolean {
    return this._value >= threshold;
  }

  /**
   * Gets the accuracy as a percentage
   */
  public toPercentage(): number {
    return this._value * 100;
  }

  /**
   * Gets the accuracy as a percentage string
   */
  public toPercentageString(decimals: number = 1): string {
    return `${(this._value * 100).toFixed(decimals)}%`;
  }

  /**
   * Gets the error rate (1 - accuracy)
   */
  public getErrorRate(): number {
    return 1 - this._value;
  }

  /**
   * Gets the error rate as a percentage
   */
  public getErrorRatePercentage(): number {
    return this.getErrorRate() * 100;
  }

  /**
   * Calculates the improvement over another accuracy score
   */
  public improvementOver(baseline: AccuracyScore): number {
    return this._value - baseline._value;
  }

  /**
   * Calculates the relative improvement over another accuracy score
   */
  public relativeImprovementOver(baseline: AccuracyScore): number {
    if (baseline._value === 0) {
      return this._value === 0 ? 0 : Infinity;
    }
    return (this._value - baseline._value) / baseline._value;
  }

  /**
   * Checks if this accuracy is significantly better than another
   */
  public isSignificantlyBetterThan(other: AccuracyScore, threshold: number = 0.05): boolean {
    return this.improvementOver(other) >= threshold;
  }

  /**
   * Combines this accuracy with another using weighted average
   */
  public combineWith(other: AccuracyScore, weight: number = 0.5): AccuracyScore {
    if (weight < 0 || weight > 1) {
      throw new Error('Weight must be between 0 and 1');
    }
    
    const combinedValue = this._value * weight + other._value * (1 - weight);
    return new AccuracyScore(combinedValue);
  }

  /**
   * Gets the absolute difference between this and another accuracy score
   */
  public differenceFrom(other: AccuracyScore): number {
    return Math.abs(this._value - other._value);
  }

  /**
   * Checks if this accuracy is greater than another
   */
  public isGreaterThan(other: AccuracyScore): boolean {
    return this._value > other._value;
  }

  /**
   * Checks if this accuracy is less than another
   */
  public isLessThan(other: AccuracyScore): boolean {
    return this._value < other._value;
  }

  /**
   * Rounds the accuracy to a specified number of decimal places
   */
  public round(decimals: number = 3): AccuracyScore {
    const factor = Math.pow(10, decimals);
    const rounded = Math.round(this._value * factor) / factor;
    return new AccuracyScore(rounded);
  }

  /**
   * Converts to a human-readable string
   */
  public toString(): string {
    return `${this.toPercentageString()} (${this.getLevel()})`;
  }

  /**
   * Converts to JSON representation
   */
  public toJSON(): Record<string, any> {
    return {
      value: this._value,
      percentage: this.toPercentage(),
      level: this.getLevel(),
      errorRate: this.getErrorRate(),
      errorRatePercentage: this.getErrorRatePercentage(),
      isGood: this.isGood(),
      isExcellent: this.isExcellent(),
    };
  }

  /**
   * Creates an AccuracyScore from JSON
   */
  public static fromJSON(json: Record<string, any>): AccuracyScore {
    return new AccuracyScore(json.value);
  }

  /**
   * Calculates the average of multiple accuracy scores
   */
  public static average(scores: AccuracyScore[]): AccuracyScore {
    if (scores.length === 0) {
      throw new Error('Cannot calculate average of empty array');
    }
    
    const sum = scores.reduce((acc, score) => acc + score._value, 0);
    return new AccuracyScore(sum / scores.length);
  }

  /**
   * Calculates the weighted average of multiple accuracy scores
   */
  public static weightedAverage(scores: AccuracyScore[], weights: number[]): AccuracyScore {
    if (scores.length === 0) {
      throw new Error('Cannot calculate weighted average of empty array');
    }
    
    if (scores.length !== weights.length) {
      throw new Error('Scores and weights arrays must have the same length');
    }
    
    const totalWeight = weights.reduce((acc, weight) => acc + weight, 0);
    if (totalWeight === 0) {
      throw new Error('Total weight cannot be zero');
    }
    
    const weightedSum = scores.reduce((acc, score, index) => acc + score._value * weights[index], 0);
    return new AccuracyScore(weightedSum / totalWeight);
  }

  /**
   * Gets the maximum accuracy from an array
   */
  public static max(scores: AccuracyScore[]): AccuracyScore {
    if (scores.length === 0) {
      throw new Error('Cannot find maximum of empty array');
    }
    
    const maxValue = Math.max(...scores.map(score => score._value));
    return new AccuracyScore(maxValue);
  }

  /**
   * Gets the minimum accuracy from an array
   */
  public static min(scores: AccuracyScore[]): AccuracyScore {
    if (scores.length === 0) {
      throw new Error('Cannot find minimum of empty array');
    }
    
    const minValue = Math.min(...scores.map(score => score._value));
    return new AccuracyScore(minValue);
  }
}