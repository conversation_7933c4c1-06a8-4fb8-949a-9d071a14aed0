import { SetMetadata } from '@nestjs/common';

/**
 * Roles decorator for role-based access control
 * 
 * @param roles - Array of role names required to access the resource
 * @example
 * ```typescript
 * @Roles('admin', 'moderator')
 * @Get('/admin-only')
 * adminOnlyEndpoint() {
 *   // Only users with 'admin' or 'moderator' roles can access this
 * }
 * ```
 */
export const Roles = (...roles: string[]) => SetMetadata('roles', roles);

/**
 * Role constants for commonly used roles
 */
export const ROLES = {
  ADMIN: 'admin',
  USER: 'user',
  MODERATOR: 'moderator',
  ANALYST: 'analyst',
  VIEWER: 'viewer',
  SECURITY_ADMIN: 'security_admin',
  COMPLIANCE_OFFICER: 'compliance_officer',
  INCIDENT_RESPONDER: 'incident_responder',
} as const;

/**
 * Type for role values
 */
export type Role = typeof ROLES[keyof typeof ROLES];
