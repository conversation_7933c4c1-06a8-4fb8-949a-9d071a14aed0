import { UniqueEntityId } from '../../../../../shared-kernel/value-objects/unique-entity-id.value-object';
import { AnalysisType, AnalysisStatus, AnalysisInput } from '../../entities/analysis-result.entity';
import { AnalysisResultFactory, CreateAnalysisResultRequest, ReconstitutionData } from '../analysis-result.factory';

describe('AnalysisResultFactory', () => {
  let validCreateRequest: CreateAnalysisResultRequest;
  let validInputData: AnalysisInput;
  let validReconstitutionData: ReconstitutionData;

  beforeEach(() => {
    validInputData = {
      data: { test: 'input' },
      format: 'json',
      size: 100,
      checksum: 'input-checksum',
      preprocessingApplied: ['normalization'],
      validationRules: ['required'],
    };

    validCreateRequest = {
      requestId: 'test-request-123',
      modelId: UniqueEntityId.generate(),
      analysisType: AnalysisType.CLASSIFICATION,
      inputData: validInputData,
      tags: ['test', 'classification'],
      correlationId: 'correlation-123',
      parentAnalysisId: UniqueEntityId.generate(),
    };

    const now = new Date();
    validReconstitutionData = {
      id: UniqueEntityId.generate().toString(),
      requestId: 'test-request-123',
      modelId: UniqueEntityId.generate(),
      analysisType: AnalysisType.CLASSIFICATION,
      inputData: validInputData,
      outputData: {
        results: { prediction: 'test-result' },
        format: 'json',
        size: 200,
        checksum: 'output-checksum',
        postprocessingApplied: ['formatting'],
        validationStatus: 'passed',
        qualityScore: 0.9,
      },
      confidence: 0.85,
      processingTime: 1500,
      status: AnalysisStatus.COMPLETED,
      metadata: {
        version: '1.0.0',
        algorithm: 'test-algorithm',
        parameters: { param1: 'value1' },
        environment: 'test',
        resourceUsage: {
          cpuTime: 1000,
          memoryUsage: 512,
          gpuTime: 500,
          networkIO: 100,
          diskIO: 50,
        },
        performanceMetrics: {
          throughput: 10,
          latency: 100,
          accuracy: 0.95,
          precision: 0.92,
          recall: 0.88,
          f1Score: 0.90,
        },
        qualityMetrics: {
          dataQuality: 0.9,
          resultReliability: 0.85,
          consistencyScore: 0.8,
          completenessScore: 0.95,
        },
      },
      tags: ['test', 'classification'],
      correlationId: 'correlation-123',
      parentAnalysisId: UniqueEntityId.generate(),
      childAnalysisIds: [UniqueEntityId.generate()],
      errorDetails: undefined,
      createdAt: now,
      updatedAt: now,
      completedAt: now,
    };
  });

  describe('create', () => {
    it('should create a valid analysis result', () => {
      const result = AnalysisResultFactory.create(validCreateRequest);

      expect(result.requestId).toBe(validCreateRequest.requestId);
      expect(result.modelId.equals(validCreateRequest.modelId)).toBe(true);
      expect(result.analysisType).toBe(validCreateRequest.analysisType);
      expect(result.inputData).toEqual(validCreateRequest.inputData);
      expect(result.status).toBe(AnalysisStatus.PENDING);
      expect(result.confidence).toBe(0);
      expect(result.processingTime).toBe(0);
      expect(result.tags).toEqual(['test', 'classification']);
      expect(result.correlationId).toBe(validCreateRequest.correlationId);
      expect(result.parentAnalysisId?.equals(validCreateRequest.parentAnalysisId!)).toBe(true);
      expect(result.childAnalysisIds).toEqual([]);
      expect(result.createdAt).toBeInstanceOf(Date);
      expect(result.updatedAt).toBeInstanceOf(Date);
    });

    it('should create with custom ID', () => {
      const customId = UniqueEntityId.generate();
      const result = AnalysisResultFactory.create(validCreateRequest, customId);

      expect(result.id.equals(customId)).toBe(true);
    });

    it('should create with default values when optional fields are omitted', () => {
      const minimalRequest: CreateAnalysisResultRequest = {
        requestId: 'minimal-request',
        modelId: UniqueEntityId.generate(),
        analysisType: AnalysisType.ANOMALY_DETECTION,
        inputData: validInputData,
      };

      const result = AnalysisResultFactory.create(minimalRequest);

      expect(result.tags).toEqual([]);
      expect(result.correlationId).toBeUndefined();
      expect(result.parentAnalysisId).toBeUndefined();
      expect(result.metadata.version).toBe('1.0.0');
      expect(result.metadata.algorithm).toBe('unknown');
      expect(result.metadata.environment).toBe('production');
    });

    it('should create with partial metadata', () => {
      const requestWithMetadata: CreateAnalysisResultRequest = {
        ...validCreateRequest,
        metadata: {
          version: '2.0.0',
          algorithm: 'custom-algorithm',
        },
      };

      const result = AnalysisResultFactory.create(requestWithMetadata);

      expect(result.metadata.version).toBe('2.0.0');
      expect(result.metadata.algorithm).toBe('custom-algorithm');
      expect(result.metadata.environment).toBe('production'); // Default value
    });

    it('should normalize tags to lowercase', () => {
      const requestWithUppercaseTags: CreateAnalysisResultRequest = {
        ...validCreateRequest,
        tags: ['TEST', 'Classification', 'UPPER'],
      };

      const result = AnalysisResultFactory.create(requestWithUppercaseTags);

      expect(result.tags).toEqual(['test', 'classification', 'upper']);
    });

    it('should trim whitespace from request ID and correlation ID', () => {
      const requestWithWhitespace: CreateAnalysisResultRequest = {
        ...validCreateRequest,
        requestId: '  test-request-123  ',
        correlationId: '  correlation-123  ',
      };

      const result = AnalysisResultFactory.create(requestWithWhitespace);

      expect(result.requestId).toBe('test-request-123');
      expect(result.correlationId).toBe('correlation-123');
    });

    describe('validation', () => {
      it('should throw error for empty request ID', () => {
        const invalidRequest = { ...validCreateRequest, requestId: '' };

        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('Request ID is required');
      });

      it('should throw error for whitespace-only request ID', () => {
        const invalidRequest = { ...validCreateRequest, requestId: '   ' };

        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('Request ID is required');
      });

      it('should throw error for request ID too long', () => {
        const invalidRequest = { ...validCreateRequest, requestId: 'a'.repeat(256) };

        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('Request ID cannot exceed 255 characters');
      });

      it('should throw error for missing model ID', () => {
        const invalidRequest = { ...validCreateRequest, modelId: null as any };

        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('Model ID is required');
      });

      it('should throw error for invalid analysis type', () => {
        const invalidRequest = { ...validCreateRequest, analysisType: 'invalid' as AnalysisType };

        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('Invalid analysis type');
      });

      it('should throw error for missing input data', () => {
        const invalidRequest = { ...validCreateRequest, inputData: null as any };

        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('Input data is required');
      });

      it('should throw error for invalid input data', () => {
        const invalidInputData = { ...validInputData, data: null };
        const invalidRequest = { ...validCreateRequest, inputData: invalidInputData };

        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('Input data content is required');
      });

      it('should throw error for empty input data format', () => {
        const invalidInputData = { ...validInputData, format: '' };
        const invalidRequest = { ...validCreateRequest, inputData: invalidInputData };

        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('Input data format is required');
      });

      it('should throw error for negative input data size', () => {
        const invalidInputData = { ...validInputData, size: -1 };
        const invalidRequest = { ...validCreateRequest, inputData: invalidInputData };

        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('Input data size cannot be negative');
      });

      it('should throw error for empty checksum', () => {
        const invalidInputData = { ...validInputData, checksum: '' };
        const invalidRequest = { ...validCreateRequest, inputData: invalidInputData };

        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('Input data checksum is required');
      });

      it('should throw error for invalid preprocessing applied', () => {
        const invalidInputData = { ...validInputData, preprocessingApplied: 'not-array' as any };
        const invalidRequest = { ...validCreateRequest, inputData: invalidInputData };

        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('Preprocessing applied must be an array');
      });

      it('should throw error for invalid validation rules', () => {
        const invalidInputData = { ...validInputData, validationRules: 'not-array' as any };
        const invalidRequest = { ...validCreateRequest, inputData: invalidInputData };

        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('Validation rules must be an array');
      });

      it('should throw error for empty tag in array', () => {
        const invalidRequest = { ...validCreateRequest, tags: ['valid', ''] };

        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('All tags must be non-empty strings');
      });

      it('should throw error for empty correlation ID', () => {
        const invalidRequest = { ...validCreateRequest, correlationId: '   ' };

        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('Correlation ID cannot be empty if provided');
      });
    });
  });

  describe('reconstitute', () => {
    it('should reconstitute a valid analysis result', () => {
      const result = AnalysisResultFactory.reconstitute(validReconstitutionData);

      expect(result.id.toString()).toBe(validReconstitutionData.id);
      expect(result.requestId).toBe(validReconstitutionData.requestId);
      expect(result.modelId.equals(validReconstitutionData.modelId)).toBe(true);
      expect(result.analysisType).toBe(validReconstitutionData.analysisType);
      expect(result.status).toBe(validReconstitutionData.status);
      expect(result.confidence).toBe(validReconstitutionData.confidence);
      expect(result.processingTime).toBe(validReconstitutionData.processingTime);
      expect(result.createdAt).toBe(validReconstitutionData.createdAt);
      expect(result.updatedAt).toBe(validReconstitutionData.updatedAt);
      expect(result.completedAt).toBe(validReconstitutionData.completedAt);
    });

    describe('validation', () => {
      it('should throw error for invalid ID', () => {
        const invalidData = { ...validReconstitutionData, id: 'invalid-id' };

        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Valid ID is required for reconstitution');
      });

      it('should throw error for empty request ID', () => {
        const invalidData = { ...validReconstitutionData, requestId: '' };

        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Request ID is required');
      });

      it('should throw error for missing model ID', () => {
        const invalidData = { ...validReconstitutionData, modelId: null as any };

        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Model ID is required');
      });

      it('should throw error for invalid analysis type', () => {
        const invalidData = { ...validReconstitutionData, analysisType: 'invalid' as AnalysisType };

        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Invalid analysis type');
      });

      it('should throw error for invalid status', () => {
        const invalidData = { ...validReconstitutionData, status: 'invalid' as AnalysisStatus };

        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Invalid analysis status');
      });

      it('should throw error for missing input data', () => {
        const invalidData = { ...validReconstitutionData, inputData: null as any };

        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Input data is required');
      });

      it('should throw error for missing metadata', () => {
        const invalidData = { ...validReconstitutionData, metadata: null as any };

        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Metadata is required');
      });

      it('should throw error for invalid confidence', () => {
        const invalidData = { ...validReconstitutionData, confidence: 1.5 };

        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Confidence must be between 0 and 1');
      });

      it('should throw error for negative processing time', () => {
        const invalidData = { ...validReconstitutionData, processingTime: -100 };

        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Processing time cannot be negative');
      });

      it('should throw error for invalid tags', () => {
        const invalidData = { ...validReconstitutionData, tags: 'not-array' as any };

        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Tags must be an array');
      });

      it('should throw error for invalid child analysis IDs', () => {
        const invalidData = { ...validReconstitutionData, childAnalysisIds: 'not-array' as any };

        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Child analysis IDs must be an array');
      });

      it('should throw error for invalid creation date', () => {
        const invalidData = { ...validReconstitutionData, createdAt: 'invalid' as any };

        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Valid creation date is required');
      });

      it('should throw error for invalid update date', () => {
        const invalidData = { ...validReconstitutionData, updatedAt: 'invalid' as any };

        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Valid update date is required');
      });

      it('should throw error for invalid completed date', () => {
        const invalidData = { ...validReconstitutionData, completedAt: 'invalid' as any };

        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Completed date must be a valid Date if provided');
      });

      it('should throw error for completed status without output data', () => {
        const invalidData = { 
          ...validReconstitutionData, 
          status: AnalysisStatus.COMPLETED,
          outputData: null as any 
        };

        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Output data is required for completed analysis');
      });

      it('should throw error for failed status without error details', () => {
        const invalidData = { 
          ...validReconstitutionData, 
          status: AnalysisStatus.FAILED,
          errorDetails: undefined 
        };

        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Error details are required for failed analysis');
      });
    });
  });

  describe('createForTesting', () => {
    it('should create a test analysis result with default values', () => {
      const result = AnalysisResultFactory.createForTesting();

      expect(result.requestId).toBe('test-request-123');
      expect(result.analysisType).toBe(AnalysisType.CLASSIFICATION);
      expect(result.status).toBe(AnalysisStatus.PENDING);
      expect(result.tags).toEqual(['test']);
      expect(result.correlationId).toBe('test-correlation-123');
      expect(result.confidence).toBe(0);
      expect(result.processingTime).toBe(0);
    });

    it('should create a test analysis result with overrides', () => {
      const overrides = {
        requestId: 'custom-request',
        analysisType: AnalysisType.ANOMALY_DETECTION,
        tags: ['custom', 'test'],
      };

      const result = AnalysisResultFactory.createForTesting(overrides);

      expect(result.requestId).toBe('custom-request');
      expect(result.analysisType).toBe(AnalysisType.ANOMALY_DETECTION);
      expect(result.tags).toEqual(['custom', 'test']);
      expect(result.correlationId).toBe('test-correlation-123'); // Default value preserved
    });
  });

  describe('createCompletedForTesting', () => {
    it('should create a completed test analysis result', () => {
      const result = AnalysisResultFactory.createCompletedForTesting();

      expect(result.status).toBe(AnalysisStatus.COMPLETED);
      expect(result.confidence).toBe(0.95);
      expect(result.processingTime).toBe(1500);
      expect(result.outputData).toBeDefined();
      expect(result.completedAt).toBeInstanceOf(Date);
      expect(result.isSuccessful()).toBe(true);
    });

    it('should create a completed test analysis result with overrides', () => {
      const overrides = {
        requestId: 'completed-request',
        analysisType: AnalysisType.NLP_ANALYSIS,
      };

      const result = AnalysisResultFactory.createCompletedForTesting(overrides);

      expect(result.requestId).toBe('completed-request');
      expect(result.analysisType).toBe(AnalysisType.NLP_ANALYSIS);
      expect(result.status).toBe(AnalysisStatus.COMPLETED);
    });
  });

  describe('createFailedForTesting', () => {
    it('should create a failed test analysis result', () => {
      const result = AnalysisResultFactory.createFailedForTesting();

      expect(result.status).toBe(AnalysisStatus.FAILED);
      expect(result.errorDetails).toBeDefined();
      expect(result.errorDetails!.code).toBe('TEST_ERROR');
      expect(result.errorDetails!.retryable).toBe(true);
      expect(result.completedAt).toBeInstanceOf(Date);
      expect(result.isSuccessful()).toBe(false);
      expect(result.isRetryable()).toBe(true);
    });

    it('should create a failed test analysis result with overrides', () => {
      const overrides = {
        requestId: 'failed-request',
        analysisType: AnalysisType.THREAT_DETECTION,
      };

      const result = AnalysisResultFactory.createFailedForTesting(overrides);

      expect(result.requestId).toBe('failed-request');
      expect(result.analysisType).toBe(AnalysisType.THREAT_DETECTION);
      expect(result.status).toBe(AnalysisStatus.FAILED);
    });
  });
});