1b3576b740ffae02a6a5875b63ff406c
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const data_serializer_service_1 = require("../data-serializer.service");
describe('DataSerializerService', () => {
    let service;
    let configService;
    beforeEach(async () => {
        const mockConfigService = {
            get: jest.fn().mockImplementation((key, defaultValue) => {
                const config = {
                    'ai.serialization.compression.enabled': true,
                    'ai.serialization.compression.level': 6,
                    'ai.serialization.compression.threshold': 1024,
                };
                return config[key] || defaultValue;
            }),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                data_serializer_service_1.DataSerializerService,
                { provide: config_1.ConfigService, useValue: mockConfigService },
            ],
        }).compile();
        service = module.get(data_serializer_service_1.DataSerializerService);
        configService = module.get(config_1.ConfigService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('JSON Serialization', () => {
        describe('serializeToJson', () => {
            it('should serialize simple object to JSON', async () => {
                const data = { name: 'test', value: 123, active: true };
                const result = await service.serializeToJson(data);
                expect(result).toMatchObject({
                    format: 'json',
                    compressed: false,
                    originalSize: expect.any(Number),
                    compressedSize: expect.any(Number),
                    compressionRatio: 1,
                    processingTime: expect.any(Number),
                    metadata: expect.objectContaining({
                        encoding: 'utf8',
                    }),
                });
                expect(result.data).toBeInstanceOf(Buffer);
                expect(result.originalSize).toBeGreaterThan(0);
            });
            it('should serialize large object with compression', async () => {
                // Create large object that exceeds compression threshold
                const largeData = {
                    items: Array.from({ length: 100 }, (_, i) => ({
                        id: i,
                        name: `Item ${i}`,
                        description: 'This is a long description that will help make the data large enough to trigger compression',
                        metadata: { created: new Date(), updated: new Date(), tags: ['tag1', 'tag2', 'tag3'] },
                    })),
                };
                const result = await service.serializeToJson(largeData);
                expect(result.compressed).toBe(true);
                expect(result.compressionType).toBe('gzip');
                expect(result.compressionRatio).toBeGreaterThan(1);
                expect(result.compressedSize).toBeLessThan(result.originalSize);
            });
            it('should serialize with custom options', async () => {
                const data = { name: 'test', value: 123 };
                const options = {
                    space: 2,
                    forceCompression: true,
                    compressionType: 'deflate',
                    metadata: { source: 'test' },
                };
                const result = await service.serializeToJson(data, options);
                expect(result.compressed).toBe(true);
                expect(result.compressionType).toBe('deflate');
                expect(result.metadata).toMatchObject({
                    encoding: 'utf8',
                    source: 'test',
                });
            });
            it('should handle serialization errors', async () => {
                const circularData = {};
                circularData['self'] = circularData; // Create circular reference
                await expect(service.serializeToJson(circularData)).rejects.toThrow('JSON serialization failed');
            });
        });
        describe('deserializeFromJson', () => {
            it('should deserialize JSON data', async () => {
                const originalData = { name: 'test', value: 123, active: true };
                const serialized = await service.serializeToJson(originalData);
                const result = await service.deserializeFromJson(serialized);
                expect(result).toEqual(originalData);
            });
            it('should deserialize compressed JSON data', async () => {
                const originalData = { name: 'test', value: 123 };
                const serialized = await service.serializeToJson(originalData, { forceCompression: true });
                const result = await service.deserializeFromJson(serialized);
                expect(result).toEqual(originalData);
            });
            it('should deserialize from Buffer', async () => {
                const originalData = { name: 'test', value: 123 };
                const jsonString = JSON.stringify(originalData);
                const buffer = Buffer.from(jsonString, 'utf8');
                const result = await service.deserializeFromJson(buffer);
                expect(result).toEqual(originalData);
            });
            it('should handle deserialization errors', async () => {
                const invalidBuffer = Buffer.from('invalid json', 'utf8');
                await expect(service.deserializeFromJson(invalidBuffer)).rejects.toThrow('JSON deserialization failed');
            });
        });
    });
    describe('Protocol Buffers Serialization', () => {
        describe('serializeToProtobuf', () => {
            it('should serialize data to Protocol Buffers format', async () => {
                const data = { name: 'test', value: 123 };
                const schema = 'TestMessage';
                const result = await service.serializeToProtobuf(data, schema);
                expect(result).toMatchObject({
                    format: 'protobuf',
                    schema: 'TestMessage',
                    compressed: false,
                    originalSize: expect.any(Number),
                    compressedSize: expect.any(Number),
                    processingTime: expect.any(Number),
                    metadata: expect.objectContaining({
                        schema: 'TestMessage',
                    }),
                });
                expect(result.data).toBeInstanceOf(Buffer);
            });
            it('should serialize with compression', async () => {
                const data = { name: 'test', value: 123 };
                const schema = 'TestMessage';
                const options = { forceCompression: true };
                const result = await service.serializeToProtobuf(data, schema, options);
                expect(result.compressed).toBe(true);
                expect(result.compressionType).toBe('gzip');
            });
        });
        describe('deserializeFromProtobuf', () => {
            it('should deserialize Protocol Buffers data', async () => {
                const originalData = { name: 'test', value: 123 };
                const schema = 'TestMessage';
                const serialized = await service.serializeToProtobuf(originalData, schema);
                const result = await service.deserializeFromProtobuf(serialized, schema);
                expect(result).toEqual(originalData);
            });
            it('should handle schema mismatch', async () => {
                const originalData = { name: 'test', value: 123 };
                const schema = 'TestMessage';
                const serialized = await service.serializeToProtobuf(originalData, schema);
                await expect(service.deserializeFromProtobuf(serialized, 'DifferentSchema')).rejects.toThrow('Schema mismatch');
            });
        });
    });
    describe('Binary Serialization', () => {
        describe('serializeToBinary', () => {
            it('should serialize string to binary', async () => {
                const data = 'Hello, World!';
                const encoding = 'base64';
                const result = await service.serializeToBinary(data, encoding);
                expect(result).toMatchObject({
                    format: 'binary',
                    compressed: false,
                    originalSize: expect.any(Number),
                    compressedSize: expect.any(Number),
                    processingTime: expect.any(Number),
                    metadata: expect.objectContaining({
                        encoding: 'base64',
                    }),
                });
                expect(result.data).toBeInstanceOf(Buffer);
            });
            it('should serialize Buffer to binary', async () => {
                const data = Buffer.from('Hello, World!', 'utf8');
                const encoding = 'hex';
                const result = await service.serializeToBinary(data, encoding);
                expect(result.format).toBe('binary');
                expect(result.metadata.encoding).toBe('hex');
            });
            it('should serialize object to binary', async () => {
                const data = { name: 'test', value: 123 };
                const encoding = 'base64';
                const result = await service.serializeToBinary(data, encoding);
                expect(result.format).toBe('binary');
                expect(result.originalSize).toBeGreaterThan(0);
            });
        });
        describe('deserializeFromBinary', () => {
            it('should deserialize binary data', async () => {
                const originalData = 'Hello, World!';
                const encoding = 'base64';
                const serialized = await service.serializeToBinary(originalData, encoding);
                const result = await service.deserializeFromBinary(serialized, encoding);
                expect(result).toBeInstanceOf(Buffer);
                expect(result.toString('utf8')).toBe(originalData);
            });
            it('should deserialize from Buffer', async () => {
                const originalBuffer = Buffer.from('Hello, World!', 'utf8');
                const result = await service.deserializeFromBinary(originalBuffer, 'utf8');
                expect(result).toEqual(originalBuffer);
            });
        });
    });
    describe('Compression', () => {
        describe('compressData', () => {
            it('should compress data using gzip', async () => {
                // Use larger data that will actually compress well
                const data = Buffer.from('Hello, World! '.repeat(100), 'utf8');
                const compressed = await service.compressData(data, 'gzip');
                expect(compressed).toBeInstanceOf(Buffer);
                expect(compressed.length).toBeLessThan(data.length);
            });
            it('should compress data using deflate', async () => {
                // Use larger data that will actually compress well
                const data = Buffer.from('Hello, World! '.repeat(100), 'utf8');
                const compressed = await service.compressData(data, 'deflate');
                expect(compressed).toBeInstanceOf(Buffer);
                expect(compressed.length).toBeLessThan(data.length);
            });
            it('should handle unsupported compression type', async () => {
                const data = Buffer.from('Hello, World!', 'utf8');
                await expect(service.compressData(data, 'unsupported')).rejects.toThrow('Unsupported compression type');
            });
        });
        describe('decompressData', () => {
            it('should decompress gzip data', async () => {
                const originalData = Buffer.from('Hello, World! This is a test string for compression.', 'utf8');
                const compressed = await service.compressData(originalData, 'gzip');
                const decompressed = await service.decompressData(compressed, 'gzip');
                expect(decompressed).toEqual(originalData);
            });
            it('should decompress deflate data', async () => {
                const originalData = Buffer.from('Hello, World! This is a test string for compression.', 'utf8');
                const compressed = await service.compressData(originalData, 'deflate');
                const decompressed = await service.decompressData(compressed, 'deflate');
                expect(decompressed).toEqual(originalData);
            });
            it('should handle unsupported decompression type', async () => {
                const data = Buffer.from('Hello, World!', 'utf8');
                await expect(service.decompressData(data, 'unsupported')).rejects.toThrow('Unsupported compression type');
            });
        });
    });
    describe('Batch Operations', () => {
        describe('batchSerialize', () => {
            it('should batch serialize multiple items', async () => {
                const items = [
                    { data: { name: 'item1', value: 1 }, format: 'json' },
                    { data: { name: 'item2', value: 2 }, format: 'json' },
                    { data: 'binary data', format: 'binary', options: { encoding: 'base64' } },
                ];
                const result = await service.batchSerialize(items);
                expect(result.results).toHaveLength(3);
                expect(result.errors).toHaveLength(0);
                expect(result.summary).toMatchObject({
                    total: 3,
                    successful: 3,
                    failed: 0,
                    totalOriginalSize: expect.any(Number),
                    totalCompressedSize: expect.any(Number),
                    overallCompressionRatio: expect.any(Number),
                    processingTime: expect.any(Number),
                });
                result.results.forEach(item => {
                    expect(item).toMatchObject({
                        data: expect.any(Buffer),
                        format: expect.any(String),
                        originalSize: expect.any(Number),
                        compressedSize: expect.any(Number),
                        processingTime: expect.any(Number),
                    });
                });
            });
            it('should handle batch serialization errors', async () => {
                const items = [
                    { data: { name: 'valid' }, format: 'json' },
                    { data: {}, format: 'unsupported' }, // Invalid format
                    { data: { name: 'valid2' }, format: 'json' },
                ];
                const result = await service.batchSerialize(items);
                expect(result.results).toHaveLength(3);
                expect(result.errors).toHaveLength(1);
                expect(result.summary).toMatchObject({
                    total: 3,
                    successful: 2,
                    failed: 1,
                });
                expect(result.errors[0]).toMatchObject({
                    index: 1,
                    error: expect.any(Error),
                });
            });
        });
        describe('batchDeserialize', () => {
            it('should batch deserialize multiple items', async () => {
                // First serialize some data
                const originalItems = [
                    { data: { name: 'item1', value: 1 }, format: 'json' },
                    { data: { name: 'item2', value: 2 }, format: 'json' },
                ];
                const serialized = await service.batchSerialize(originalItems);
                // Then deserialize
                const deserializeItems = serialized.results.map((item, index) => ({
                    data: item,
                    format: originalItems[index].format,
                }));
                const result = await service.batchDeserialize(deserializeItems);
                expect(result.results).toHaveLength(2);
                expect(result.errors).toHaveLength(0);
                expect(result.summary).toMatchObject({
                    total: 2,
                    successful: 2,
                    failed: 0,
                    processingTime: expect.any(Number),
                });
                expect(result.results[0]).toEqual({ name: 'item1', value: 1 });
                expect(result.results[1]).toEqual({ name: 'item2', value: 2 });
            });
            it('should handle batch deserialization errors', async () => {
                const items = [
                    { data: Buffer.from('{"valid": true}', 'utf8'), format: 'json' },
                    { data: Buffer.from('invalid json', 'utf8'), format: 'json' },
                    { data: Buffer.from('{"valid2": true}', 'utf8'), format: 'json' },
                ];
                const result = await service.batchDeserialize(items);
                expect(result.results).toHaveLength(3);
                expect(result.errors).toHaveLength(1);
                expect(result.summary).toMatchObject({
                    total: 3,
                    successful: 2,
                    failed: 1,
                });
                expect(result.errors[0]).toMatchObject({
                    index: 1,
                    error: expect.any(Error),
                });
            });
        });
    });
    describe('Configuration and Statistics', () => {
        describe('getSerializationStats', () => {
            it('should return serialization statistics', () => {
                const stats = service.getSerializationStats();
                expect(stats).toMatchObject({
                    compressionEnabled: true,
                    compressionLevel: 6,
                    compressionThreshold: 1024,
                    supportedFormats: ['json', 'protobuf', 'binary'],
                    supportedCompressionTypes: ['gzip', 'deflate'],
                    supportedEncodings: ['base64', 'hex', 'utf8'],
                });
            });
        });
        it('should use configured compression settings', async () => {
            const mockConfigService = {
                get: jest.fn().mockImplementation((key, defaultValue) => {
                    const config = {
                        'ai.serialization.compression.enabled': false,
                        'ai.serialization.compression.level': 9,
                        'ai.serialization.compression.threshold': 2048,
                    };
                    return config[key] || defaultValue;
                }),
            };
            // Create new instance to pick up new config
            const newService = new data_serializer_service_1.DataSerializerService(mockConfigService);
            const stats = newService.getSerializationStats();
            expect(stats).toMatchObject({
                compressionEnabled: false,
                compressionLevel: 9,
                compressionThreshold: 2048,
            });
        });
    });
    describe('Edge Cases and Error Handling', () => {
        it('should handle null data', async () => {
            const nullResult = await service.serializeToJson(null);
            expect(nullResult.data).toBeInstanceOf(Buffer);
            const deserializedNull = await service.deserializeFromJson(nullResult);
            expect(deserializedNull).toBeNull();
        });
        it('should handle undefined data by treating it as null', async () => {
            // JSON.stringify(undefined) returns undefined, so we expect this to be handled gracefully
            // In practice, undefined should be converted to null for JSON serialization
            const data = { value: undefined, name: 'test' };
            const result = await service.serializeToJson(data);
            expect(result.data).toBeInstanceOf(Buffer);
            const deserialized = await service.deserializeFromJson(result);
            expect(deserialized).toEqual({ name: 'test' }); // undefined values are omitted in JSON
        });
        it('should handle empty data', async () => {
            const emptyObject = {};
            const emptyArray = [];
            const emptyString = '';
            const objectResult = await service.serializeToJson(emptyObject);
            const arrayResult = await service.serializeToJson(emptyArray);
            const stringResult = await service.serializeToBinary(emptyString);
            expect(objectResult.data).toBeInstanceOf(Buffer);
            expect(arrayResult.data).toBeInstanceOf(Buffer);
            expect(stringResult.data).toBeInstanceOf(Buffer);
            const deserializedObject = await service.deserializeFromJson(objectResult);
            const deserializedArray = await service.deserializeFromJson(arrayResult);
            expect(deserializedObject).toEqual({});
            expect(deserializedArray).toEqual([]);
        });
        it('should handle very large data', async () => {
            // Create a large object
            const largeData = {
                items: Array.from({ length: 10000 }, (_, i) => ({
                    id: i,
                    data: `Item ${i} with some additional data to make it larger`,
                })),
            };
            const result = await service.serializeToJson(largeData);
            expect(result.compressed).toBe(true);
            expect(result.compressionRatio).toBeGreaterThan(1);
            const deserialized = await service.deserializeFromJson(result);
            expect(deserialized.items).toHaveLength(10000);
            expect(deserialized.items[0]).toEqual(largeData.items[0]);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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