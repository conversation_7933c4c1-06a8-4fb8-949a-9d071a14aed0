acdff6d3a13812625931de9c8bef6478
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var WorkflowRuleEngine_1;
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowRuleEngine = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
/**
 * Workflow Rule Engine Service
 *
 * Provides advanced rule evaluation and conditional logic processing for workflows:
 * - Complex condition evaluation with multiple operators and functions
 * - Variable substitution and expression parsing
 * - Custom function registration and execution
 * - Performance optimization with expression caching
 * - Security validation and sandboxed execution
 * - Integration with workflow context and data
 *
 * Features:
 * - Advanced expression language with mathematical and logical operations
 * - Built-in functions for common workflow operations
 * - Custom function registration for domain-specific logic
 * - Variable scoping and context management
 * - Performance optimization with compiled expressions
 * - Security controls with execution limits and validation
 */
let WorkflowRuleEngine = WorkflowRuleEngine_1 = class WorkflowRuleEngine {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(WorkflowRuleEngine_1.name);
        this.expressionCache = new Map();
        this.customFunctions = new Map();
        this.initializeBuiltInFunctions();
    }
    /**
     * Evaluate condition expression
     */
    async evaluateCondition(condition, context) {
        try {
            this.logger.debug(`Evaluating condition: ${JSON.stringify(condition)}`);
            if (typeof condition === 'boolean') {
                return condition;
            }
            if (typeof condition === 'string') {
                return await this.evaluateExpression(condition, context);
            }
            if (typeof condition === 'object') {
                return await this.evaluateObjectCondition(condition, context);
            }
            this.logger.warn(`Unknown condition type: ${typeof condition}`);
            return false;
        }
        catch (error) {
            this.logger.error(`Condition evaluation failed: ${error.message}`, error.stack);
            return false;
        }
    }
    /**
     * Evaluate expression string
     */
    async evaluateExpression(expression, context) {
        try {
            // Check cache first
            const cacheKey = `${expression}_${JSON.stringify(context)}`;
            if (this.expressionCache.has(cacheKey)) {
                return this.expressionCache.get(cacheKey);
            }
            // Parse and evaluate expression
            const result = await this.parseAndEvaluate(expression, context);
            // Cache result
            this.expressionCache.set(cacheKey, result);
            return result;
        }
        catch (error) {
            this.logger.error(`Expression evaluation failed: ${error.message}`, error.stack);
            throw error;
        }
    }
    /**
     * Evaluate object-based condition
     */
    async evaluateObjectCondition(condition, context) {
        try {
            // Handle logical operators
            if (condition.and) {
                const results = await Promise.all(condition.and.map(subCondition => this.evaluateCondition(subCondition, context)));
                return results.every(result => result);
            }
            if (condition.or) {
                const results = await Promise.all(condition.or.map(subCondition => this.evaluateCondition(subCondition, context)));
                return results.some(result => result);
            }
            if (condition.not) {
                const result = await this.evaluateCondition(condition.not, context);
                return !result;
            }
            // Handle comparison operators
            if (condition.field && condition.operator && condition.hasOwnProperty('value')) {
                return await this.evaluateComparison(condition, context);
            }
            // Handle function calls
            if (condition.function && condition.args) {
                return await this.evaluateFunction(condition.function, condition.args, context);
            }
            this.logger.warn(`Unknown condition structure: ${JSON.stringify(condition)}`);
            return false;
        }
        catch (error) {
            this.logger.error(`Object condition evaluation failed: ${error.message}`, error.stack);
            return false;
        }
    }
    /**
     * Evaluate comparison operation
     */
    async evaluateComparison(condition, context) {
        try {
            const fieldValue = this.getFieldValue(condition.field, context);
            const compareValue = this.resolveValue(condition.value, context);
            switch (condition.operator) {
                case 'eq':
                case '==':
                    return fieldValue == compareValue;
                case 'neq':
                case '!=':
                    return fieldValue != compareValue;
                case 'gt':
                case '>':
                    return fieldValue > compareValue;
                case 'gte':
                case '>=':
                    return fieldValue >= compareValue;
                case 'lt':
                case '<':
                    return fieldValue < compareValue;
                case 'lte':
                case '<=':
                    return fieldValue <= compareValue;
                case 'in':
                    return Array.isArray(compareValue) && compareValue.includes(fieldValue);
                case 'not_in':
                    return Array.isArray(compareValue) && !compareValue.includes(fieldValue);
                case 'contains':
                    return String(fieldValue).includes(String(compareValue));
                case 'not_contains':
                    return !String(fieldValue).includes(String(compareValue));
                case 'starts_with':
                    return String(fieldValue).startsWith(String(compareValue));
                case 'ends_with':
                    return String(fieldValue).endsWith(String(compareValue));
                case 'regex':
                    const regex = new RegExp(String(compareValue));
                    return regex.test(String(fieldValue));
                case 'exists':
                    return fieldValue !== undefined && fieldValue !== null;
                case 'not_exists':
                    return fieldValue === undefined || fieldValue === null;
                default:
                    this.logger.warn(`Unknown comparison operator: ${condition.operator}`);
                    return false;
            }
        }
        catch (error) {
            this.logger.error(`Comparison evaluation failed: ${error.message}`, error.stack);
            return false;
        }
    }
    /**
     * Evaluate function call
     */
    async evaluateFunction(functionName, args, context) {
        try {
            // Resolve arguments
            const resolvedArgs = args.map(arg => this.resolveValue(arg, context));
            // Check for custom functions first
            if (this.customFunctions.has(functionName)) {
                const customFunction = this.customFunctions.get(functionName);
                return await customFunction(...resolvedArgs);
            }
            // Built-in functions
            switch (functionName) {
                case 'length':
                    return this.lengthFunction(resolvedArgs[0]);
                case 'sum':
                    return this.sumFunction(resolvedArgs);
                case 'avg':
                    return this.avgFunction(resolvedArgs);
                case 'min':
                    return Math.min(...resolvedArgs);
                case 'max':
                    return Math.max(...resolvedArgs);
                case 'count':
                    return this.countFunction(resolvedArgs[0]);
                case 'unique':
                    return this.uniqueFunction(resolvedArgs[0]);
                case 'filter':
                    return this.filterFunction(resolvedArgs[0], resolvedArgs[1]);
                case 'map':
                    return this.mapFunction(resolvedArgs[0], resolvedArgs[1]);
                case 'reduce':
                    return this.reduceFunction(resolvedArgs[0], resolvedArgs[1], resolvedArgs[2]);
                case 'date_diff':
                    return this.dateDiffFunction(resolvedArgs[0], resolvedArgs[1], resolvedArgs[2]);
                case 'format_date':
                    return this.formatDateFunction(resolvedArgs[0], resolvedArgs[1]);
                case 'now':
                    return new Date();
                case 'today':
                    return new Date().toISOString().split('T')[0];
                case 'random':
                    return Math.random();
                case 'round':
                    return Math.round(resolvedArgs[0]);
                case 'floor':
                    return Math.floor(resolvedArgs[0]);
                case 'ceil':
                    return Math.ceil(resolvedArgs[0]);
                case 'abs':
                    return Math.abs(resolvedArgs[0]);
                case 'upper':
                    return String(resolvedArgs[0]).toUpperCase();
                case 'lower':
                    return String(resolvedArgs[0]).toLowerCase();
                case 'trim':
                    return String(resolvedArgs[0]).trim();
                case 'split':
                    return String(resolvedArgs[0]).split(String(resolvedArgs[1]));
                case 'join':
                    return Array.isArray(resolvedArgs[0]) ? resolvedArgs[0].join(String(resolvedArgs[1])) : '';
                case 'replace':
                    return String(resolvedArgs[0]).replace(String(resolvedArgs[1]), String(resolvedArgs[2]));
                default:
                    this.logger.warn(`Unknown function: ${functionName}`);
                    return null;
            }
        }
        catch (error) {
            this.logger.error(`Function evaluation failed: ${error.message}`, error.stack);
            throw error;
        }
    }
    /**
     * Get field value from context using dot notation
     */
    getFieldValue(field, context) {
        try {
            const parts = field.split('.');
            let value = context;
            for (const part of parts) {
                if (value === null || value === undefined) {
                    return undefined;
                }
                value = value[part];
            }
            return value;
        }
        catch (error) {
            this.logger.error(`Failed to get field value: ${field}`, error.stack);
            return undefined;
        }
    }
    /**
     * Resolve value (could be literal, variable reference, or expression)
     */
    resolveValue(value, context) {
        if (typeof value === 'string' && value.startsWith('$')) {
            // Variable reference
            return this.getFieldValue(value.substring(1), context);
        }
        if (typeof value === 'object' && value.expression) {
            // Nested expression
            return this.evaluateExpression(value.expression, context);
        }
        // Literal value
        return value;
    }
    /**
     * Parse and evaluate expression string
     */
    async parseAndEvaluate(expression, context) {
        try {
            // Simple expression parser for basic operations
            // In a production system, you might want to use a more robust parser like mathjs
            // Replace variables
            let processedExpression = expression;
            const variableRegex = /\$([a-zA-Z_][a-zA-Z0-9_.]*)/g;
            processedExpression = processedExpression.replace(variableRegex, (match, varName) => {
                const value = this.getFieldValue(varName, context);
                return JSON.stringify(value);
            });
            // For security, only allow safe operations
            if (this.isSafeExpression(processedExpression)) {
                // Use Function constructor for evaluation (safer than eval)
                const func = new Function('return ' + processedExpression);
                return func();
            }
            else {
                throw new Error('Unsafe expression detected');
            }
        }
        catch (error) {
            this.logger.error(`Expression parsing failed: ${error.message}`, error.stack);
            throw error;
        }
    }
    /**
     * Check if expression is safe to evaluate
     */
    isSafeExpression(expression) {
        // Basic safety check - in production, use a more comprehensive whitelist
        const dangerousPatterns = [
            /require\s*\(/,
            /import\s*\(/,
            /eval\s*\(/,
            /Function\s*\(/,
            /setTimeout\s*\(/,
            /setInterval\s*\(/,
            /process\./,
            /global\./,
            /window\./,
            /document\./,
        ];
        return !dangerousPatterns.some(pattern => pattern.test(expression));
    }
    /**
     * Register custom function
     */
    registerFunction(name, func) {
        this.customFunctions.set(name, func);
        this.logger.debug(`Registered custom function: ${name}`);
    }
    /**
     * Initialize built-in functions
     */
    initializeBuiltInFunctions() {
        // Additional built-in functions can be registered here
        this.logger.debug('Initialized built-in functions');
    }
    /**
     * Built-in function implementations
     */
    lengthFunction(value) {
        if (Array.isArray(value) || typeof value === 'string') {
            return value.length;
        }
        if (typeof value === 'object' && value !== null) {
            return Object.keys(value).length;
        }
        return 0;
    }
    sumFunction(values) {
        return values.reduce((sum, val) => sum + (Number(val) || 0), 0);
    }
    avgFunction(values) {
        if (values.length === 0)
            return 0;
        return this.sumFunction(values) / values.length;
    }
    countFunction(value) {
        if (Array.isArray(value)) {
            return value.length;
        }
        return value !== null && value !== undefined ? 1 : 0;
    }
    uniqueFunction(values) {
        if (!Array.isArray(values))
            return [];
        return [...new Set(values)];
    }
    filterFunction(values, condition) {
        if (!Array.isArray(values))
            return [];
        // Simple filter implementation
        return values.filter(value => value === condition);
    }
    mapFunction(values, transform) {
        if (!Array.isArray(values))
            return [];
        // Simple map implementation
        return values.map(value => value);
    }
    reduceFunction(values, initialValue, operation) {
        if (!Array.isArray(values))
            return initialValue;
        // Simple reduce implementation
        return values.reduce((acc, val) => {
            switch (operation) {
                case 'sum': return acc + val;
                case 'multiply': return acc * val;
                case 'concat': return acc + String(val);
                default: return acc;
            }
        }, initialValue);
    }
    dateDiffFunction(date1, date2, unit = 'days') {
        const d1 = new Date(date1);
        const d2 = new Date(date2);
        const diffMs = Math.abs(d2.getTime() - d1.getTime());
        switch (unit) {
            case 'milliseconds': return diffMs;
            case 'seconds': return Math.floor(diffMs / 1000);
            case 'minutes': return Math.floor(diffMs / (1000 * 60));
            case 'hours': return Math.floor(diffMs / (1000 * 60 * 60));
            case 'days': return Math.floor(diffMs / (1000 * 60 * 60 * 24));
            default: return diffMs;
        }
    }
    formatDateFunction(date, format) {
        const d = new Date(date);
        // Simple date formatting - in production, use a library like date-fns
        return d.toISOString();
    }
    /**
     * Clear expression cache
     */
    clearCache() {
        this.expressionCache.clear();
        this.logger.debug('Expression cache cleared');
    }
    /**
     * Get cache statistics
     */
    getCacheStats() {
        return {
            size: this.expressionCache.size,
            functions: this.customFunctions.size,
        };
    }
};
exports.WorkflowRuleEngine = WorkflowRuleEngine;
exports.WorkflowRuleEngine = WorkflowRuleEngine = WorkflowRuleEngine_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], WorkflowRuleEngine);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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