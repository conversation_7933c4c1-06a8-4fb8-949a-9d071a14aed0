"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OnGlobalQueueRemoved = exports.OnGlobalQueueDrained = exports.OnGlobalQueueCleaned = exports.OnGlobalQueueResumed = exports.OnGlobalQueuePaused = exports.OnGlobalQueueFailed = exports.OnGlobalQueueCompleted = exports.OnGlobalQueueProgress = exports.OnGlobalQueueStalled = exports.OnGlobalQueueActive = exports.OnGlobalQueueWaiting = exports.OnGlobalQueueError = exports.OnQueueRemoved = exports.OnQueueDrained = exports.OnQueueCleaned = exports.OnQueueResumed = exports.OnQueuePaused = exports.OnQueueFailed = exports.OnQueueCompleted = exports.OnQueueProgress = exports.OnQueueStalled = exports.OnQueueActive = exports.OnQueueWaiting = exports.OnQueueError = exports.OnQueueEvent = void 0;
const common_1 = require("@nestjs/common");
const bull_constants_1 = require("../bull.constants");
const enums_1 = require("../enums");
const OnQueueEvent = (eventNameOrOptions) => (0, common_1.SetMetadata)(bull_constants_1.BULL_MODULE_ON_QUEUE_EVENT, typeof eventNameOrOptions === 'string'
    ? { eventName: eventNameOrOptions }
    : eventNameOrOptions);
exports.OnQueueEvent = OnQueueEvent;
const OnQueueError = (options) => (0, exports.OnQueueEvent)({ ...options, eventName: enums_1.BullQueueEvents.ERROR });
exports.OnQueueError = OnQueueError;
const OnQueueWaiting = (options) => (0, exports.OnQueueEvent)({ ...options, eventName: enums_1.BullQueueEvents.WAITING });
exports.OnQueueWaiting = OnQueueWaiting;
const OnQueueActive = (options) => (0, exports.OnQueueEvent)({ ...options, eventName: enums_1.BullQueueEvents.ACTIVE });
exports.OnQueueActive = OnQueueActive;
const OnQueueStalled = (options) => (0, exports.OnQueueEvent)({ ...options, eventName: enums_1.BullQueueEvents.STALLED });
exports.OnQueueStalled = OnQueueStalled;
const OnQueueProgress = (options) => (0, exports.OnQueueEvent)({ ...options, eventName: enums_1.BullQueueEvents.PROGRESS });
exports.OnQueueProgress = OnQueueProgress;
const OnQueueCompleted = (options) => (0, exports.OnQueueEvent)({ ...options, eventName: enums_1.BullQueueEvents.COMPLETED });
exports.OnQueueCompleted = OnQueueCompleted;
const OnQueueFailed = (options) => (0, exports.OnQueueEvent)({ ...options, eventName: enums_1.BullQueueEvents.FAILED });
exports.OnQueueFailed = OnQueueFailed;
const OnQueuePaused = (options) => (0, exports.OnQueueEvent)({ ...options, eventName: enums_1.BullQueueEvents.PAUSED });
exports.OnQueuePaused = OnQueuePaused;
const OnQueueResumed = (options) => (0, exports.OnQueueEvent)({ ...options, eventName: enums_1.BullQueueEvents.RESUMED });
exports.OnQueueResumed = OnQueueResumed;
const OnQueueCleaned = (options) => (0, exports.OnQueueEvent)({ ...options, eventName: enums_1.BullQueueEvents.CLEANED });
exports.OnQueueCleaned = OnQueueCleaned;
const OnQueueDrained = (options) => (0, exports.OnQueueEvent)({ ...options, eventName: enums_1.BullQueueEvents.DRAINED });
exports.OnQueueDrained = OnQueueDrained;
const OnQueueRemoved = (options) => (0, exports.OnQueueEvent)({ ...options, eventName: enums_1.BullQueueEvents.REMOVED });
exports.OnQueueRemoved = OnQueueRemoved;
const OnGlobalQueueError = (options) => (0, exports.OnQueueEvent)({ ...options, eventName: enums_1.BullQueueGlobalEvents.ERROR });
exports.OnGlobalQueueError = OnGlobalQueueError;
const OnGlobalQueueWaiting = (options) => (0, exports.OnQueueEvent)({ ...options, eventName: enums_1.BullQueueGlobalEvents.WAITING });
exports.OnGlobalQueueWaiting = OnGlobalQueueWaiting;
const OnGlobalQueueActive = (options) => (0, exports.OnQueueEvent)({ ...options, eventName: enums_1.BullQueueGlobalEvents.ACTIVE });
exports.OnGlobalQueueActive = OnGlobalQueueActive;
const OnGlobalQueueStalled = (options) => (0, exports.OnQueueEvent)({ ...options, eventName: enums_1.BullQueueGlobalEvents.STALLED });
exports.OnGlobalQueueStalled = OnGlobalQueueStalled;
const OnGlobalQueueProgress = (options) => (0, exports.OnQueueEvent)({ ...options, eventName: enums_1.BullQueueGlobalEvents.PROGRESS });
exports.OnGlobalQueueProgress = OnGlobalQueueProgress;
const OnGlobalQueueCompleted = (options) => (0, exports.OnQueueEvent)({ ...options, eventName: enums_1.BullQueueGlobalEvents.COMPLETED });
exports.OnGlobalQueueCompleted = OnGlobalQueueCompleted;
const OnGlobalQueueFailed = (options) => (0, exports.OnQueueEvent)({ ...options, eventName: enums_1.BullQueueGlobalEvents.FAILED });
exports.OnGlobalQueueFailed = OnGlobalQueueFailed;
const OnGlobalQueuePaused = (options) => (0, exports.OnQueueEvent)({ ...options, eventName: enums_1.BullQueueGlobalEvents.PAUSED });
exports.OnGlobalQueuePaused = OnGlobalQueuePaused;
const OnGlobalQueueResumed = (options) => (0, exports.OnQueueEvent)({ ...options, eventName: enums_1.BullQueueGlobalEvents.RESUMED });
exports.OnGlobalQueueResumed = OnGlobalQueueResumed;
const OnGlobalQueueCleaned = (options) => (0, exports.OnQueueEvent)({ ...options, eventName: enums_1.BullQueueGlobalEvents.CLEANED });
exports.OnGlobalQueueCleaned = OnGlobalQueueCleaned;
const OnGlobalQueueDrained = (options) => (0, exports.OnQueueEvent)({ ...options, eventName: enums_1.BullQueueGlobalEvents.DRAINED });
exports.OnGlobalQueueDrained = OnGlobalQueueDrained;
const OnGlobalQueueRemoved = (options) => (0, exports.OnQueueEvent)({ ...options, eventName: enums_1.BullQueueGlobalEvents.REMOVED });
exports.OnGlobalQueueRemoved = OnGlobalQueueRemoved;
