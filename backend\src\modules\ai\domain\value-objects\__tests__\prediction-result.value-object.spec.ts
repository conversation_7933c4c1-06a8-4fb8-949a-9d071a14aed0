import { PredictionResult } from '../prediction-result.value-object';
import { ConfidenceScore } from '../confidence-score.value-object';

describe('PredictionResult Value Object', () => {
  describe('Construction', () => {
    it('should create a valid prediction result', () => {
      const confidence = new ConfidenceScore(0.85);
      const result = new PredictionResult('positive', confidence);
      
      expect(result.prediction).toBe('positive');
      expect(result.confidence.equals(confidence)).toBe(true);
      expect(result.alternatives).toEqual([]);
      expect(result.metadata).toEqual({});
      expect(result.timestamp).toBeInstanceOf(Date);
    });

    it('should create with alternatives and metadata', () => {
      const confidence = new ConfidenceScore(0.85);
      const alternatives = [
        { value: 'negative', confidence: new ConfidenceScore(0.15) }
      ];
      const metadata = { model: 'test-model' };
      const timestamp = new Date();
      
      const result = new PredictionResult('positive', confidence, alternatives, metadata, timestamp);
      
      expect(result.prediction).toBe('positive');
      expect(result.alternatives).toHaveLength(1);
      expect(result.metadata.model).toBe('test-model');
      expect(result.timestamp).toBe(timestamp);
    });

    it('should throw error for null prediction', () => {
      const confidence = new ConfidenceScore(0.85);
      expect(() => new PredictionResult(null, confidence)).toThrow('Prediction value cannot be null or undefined');
      expect(() => new PredictionResult(undefined, confidence)).toThrow('Prediction value cannot be null or undefined');
    });

    it('should throw error for invalid confidence', () => {
      expect(() => new PredictionResult('positive', 0.85 as any)).toThrow('Confidence must be a ConfidenceScore instance');
    });

    it('should throw error for invalid alternatives', () => {
      const confidence = new ConfidenceScore(0.85);
      expect(() => new PredictionResult('positive', confidence, 'invalid' as any))
        .toThrow('Alternatives must be an array');
    });

    it('should throw error for invalid timestamp', () => {
      const confidence = new ConfidenceScore(0.85);
      expect(() => new PredictionResult('positive', confidence, [], {}, 'invalid' as any))
        .toThrow('Timestamp must be a Date instance');
    });

    it('should throw error for invalid alternative structure', () => {
      const confidence = new ConfidenceScore(0.85);
      const invalidAlternatives = [{ value: 'negative' }]; // Missing confidence
      
      expect(() => new PredictionResult('positive', confidence, invalidAlternatives as any))
        .toThrow('Each alternative must have value and confidence properties');
    });

    it('should throw error for unsorted alternatives', () => {
      const confidence = new ConfidenceScore(0.85);
      const unsortedAlternatives = [
        { value: 'negative', confidence: new ConfidenceScore(0.1) },
        { value: 'neutral', confidence: new ConfidenceScore(0.15) }
      ];
      
      expect(() => new PredictionResult('positive', confidence, unsortedAlternatives))
        .toThrow('Alternatives must be sorted by confidence in descending order');
    });
  });

  describe('Factory Methods', () => {
    it('should create simple prediction result', () => {
      const result = PredictionResult.simple('positive', 0.85);
      
      expect(result.prediction).toBe('positive');
      expect(result.confidence.value).toBe(0.85);
      expect(result.alternatives).toEqual([]);
    });

    it('should create prediction result with alternatives', () => {
      const result = PredictionResult.withAlternatives('positive', 0.85, [
        { value: 'neutral', confidence: 0.1 },
        { value: 'negative', confidence: 0.05 }
      ]);
      
      expect(result.prediction).toBe('positive');
      expect(result.alternatives).toHaveLength(2);
      expect(result.alternatives[0].value).toBe('neutral'); // Higher confidence first
      expect(result.alternatives[1].value).toBe('negative');
    });

    it('should create binary classification result', () => {
      const result = PredictionResult.binary(true, 0.85);
      
      expect(result.prediction).toBe('positive');
      expect(result.alternatives).toHaveLength(1);
      expect(result.alternatives[0].value).toBe('negative');
      expect(result.alternatives[0].confidence.value).toBe(0.15);
    });

    it('should create binary classification result with custom labels', () => {
      const result = PredictionResult.binary(false, 0.7, 'spam', 'ham');
      
      expect(result.prediction).toBe('ham');
      expect(result.alternatives[0].value).toBe('spam');
      expect(result.alternatives[0].confidence.value).toBe(0.3);
    });

    it('should create multi-class classification result', () => {
      const predictions = [
        { value: 'cat', confidence: 0.6 },
        { value: 'dog', confidence: 0.3 },
        { value: 'bird', confidence: 0.1 }
      ];
      
      const result = PredictionResult.multiClass(predictions);
      
      expect(result.prediction).toBe('cat');
      expect(result.alternatives).toHaveLength(2);
      expect(result.alternatives[0].value).toBe('dog');
      expect(result.alternatives[1].value).toBe('bird');
    });

    it('should throw error for empty predictions in multi-class', () => {
      expect(() => PredictionResult.multiClass([])).toThrow('At least one prediction is required');
    });

    it('should create regression result', () => {
      const result = PredictionResult.regression(42.5, 0.9);
      
      expect(result.prediction).toBe(42.5);
      expect(result.confidence.value).toBe(0.9);
    });

    it('should create regression result with range', () => {
      const result = PredictionResult.regression(42.5, 0.9, { min: 40, max: 50 });
      
      expect(result.prediction).toBe(42.5);
      expect(result.metadata.range).toEqual({ min: 40, max: 50 });
      expect(result.metadata.withinRange).toBe(true);
    });
  });

  describe('Confidence Analysis', () => {
    it('should check high confidence', () => {
      const highConfidence = PredictionResult.simple('positive', 0.9);
      const lowConfidence = PredictionResult.simple('positive', 0.5);
      
      expect(highConfidence.hasHighConfidence()).toBe(true);
      expect(lowConfidence.hasHighConfidence()).toBe(false);
      expect(highConfidence.hasHighConfidence(0.95)).toBe(false);
    });

    it('should check low confidence', () => {
      const highConfidence = PredictionResult.simple('positive', 0.9);
      const lowConfidence = PredictionResult.simple('positive', 0.2);
      
      expect(lowConfidence.hasLowConfidence()).toBe(true);
      expect(highConfidence.hasLowConfidence()).toBe(false);
      expect(lowConfidence.hasLowConfidence(0.1)).toBe(false);
    });

    it('should calculate confidence gap', () => {
      const result = PredictionResult.withAlternatives('positive', 0.8, [
        { value: 'negative', confidence: 0.2 }
      ]);
      
      expect(result.getConfidenceGap()).toBe(0.6);
    });

    it('should handle no alternatives in confidence gap', () => {
      const result = PredictionResult.simple('positive', 0.8);
      expect(result.getConfidenceGap()).toBe(0.8);
    });

    it('should check if significantly better', () => {
      const significant = PredictionResult.withAlternatives('positive', 0.8, [
        { value: 'negative', confidence: 0.2 }
      ]);
      
      const notSignificant = PredictionResult.withAlternatives('positive', 0.55, [
        { value: 'negative', confidence: 0.45 }
      ]);
      
      expect(significant.isSignificantlyBetter()).toBe(true);
      expect(notSignificant.isSignificantlyBetter()).toBe(false);
    });

    it('should check if ambiguous', () => {
      const clear = PredictionResult.withAlternatives('positive', 0.8, [
        { value: 'negative', confidence: 0.2 }
      ]);
      
      const ambiguous = PredictionResult.withAlternatives('positive', 0.55, [
        { value: 'negative', confidence: 0.45 }
      ]);
      
      expect(clear.isAmbiguous()).toBe(false);
      expect(ambiguous.isAmbiguous()).toBe(true);
    });
  });

  describe('Alternatives Management', () => {
    it('should check if has alternatives', () => {
      const withAlts = PredictionResult.withAlternatives('positive', 0.8, [
        { value: 'negative', confidence: 0.2 }
      ]);
      const withoutAlts = PredictionResult.simple('positive', 0.8);
      
      expect(withAlts.hasAlternatives()).toBe(true);
      expect(withoutAlts.hasAlternatives()).toBe(false);
    });

    it('should get alternative count', () => {
      const result = PredictionResult.withAlternatives('positive', 0.8, [
        { value: 'negative', confidence: 0.15 },
        { value: 'neutral', confidence: 0.05 }
      ]);
      
      expect(result.getAlternativeCount()).toBe(2);
    });

    it('should get top alternatives', () => {
      const result = PredictionResult.withAlternatives('positive', 0.7, [
        { value: 'negative', confidence: 0.2 },
        { value: 'neutral', confidence: 0.1 }
      ]);
      
      const top1 = result.getTopAlternatives(1);
      expect(top1).toHaveLength(1);
      expect(top1[0].value).toBe('negative');
    });

    it('should get alternatives above threshold', () => {
      const result = PredictionResult.withAlternatives('positive', 0.6, [
        { value: 'negative', confidence: 0.25 },
        { value: 'neutral', confidence: 0.15 }
      ]);
      
      const aboveThreshold = result.getAlternativesAboveThreshold(0.2);
      expect(aboveThreshold).toHaveLength(1);
      expect(aboveThreshold[0].value).toBe('negative');
    });
  });

  describe('Entropy and Uncertainty', () => {
    it('should calculate entropy', () => {
      const result = PredictionResult.withAlternatives('positive', 0.8, [
        { value: 'negative', confidence: 0.2 }
      ]);
      
      const entropy = result.getEntropy();
      expect(entropy).toBeGreaterThan(0);
      expect(entropy).toBeLessThan(1); // Binary case should be less than 1
    });
  });

  describe('Time-based Operations', () => {
    it('should get age of prediction', () => {
      const pastTime = new Date(Date.now() - 5000); // 5 seconds ago
      const result = new PredictionResult('positive', new ConfidenceScore(0.8), [], {}, pastTime);
      
      const age = result.getAge();
      expect(age).toBeGreaterThanOrEqual(5000);
    });

    it('should check if fresh', () => {
      const recentResult = PredictionResult.simple('positive', 0.8);
      const oldTime = new Date(Date.now() - 10000); // 10 seconds ago
      const oldResult = new PredictionResult('positive', new ConfidenceScore(0.8), [], {}, oldTime);
      
      expect(recentResult.isFresh(5000)).toBe(true);
      expect(oldResult.isFresh(5000)).toBe(false);
    });
  });

  describe('Immutable Operations', () => {
    it('should add metadata without mutating original', () => {
      const original = PredictionResult.simple('positive', 0.8);
      const withMetadata = original.withMetadata({ model: 'test' });
      
      expect(original.metadata).toEqual({});
      expect(withMetadata.metadata.model).toBe('test');
    });

    it('should update confidence without mutating original', () => {
      const original = PredictionResult.simple('positive', 0.8);
      const newConfidence = new ConfidenceScore(0.9);
      const updated = original.withConfidence(newConfidence);
      
      expect(original.confidence.value).toBe(0.8);
      expect(updated.confidence.value).toBe(0.9);
    });

    it('should add alternatives without mutating original', () => {
      const original = PredictionResult.simple('positive', 0.8);
      const alternatives = [{ value: 'negative', confidence: new ConfidenceScore(0.2) }];
      const withAlts = original.withAlternatives(alternatives);
      
      expect(original.alternatives).toHaveLength(0);
      expect(withAlts.alternatives).toHaveLength(1);
    });
  });

  describe('Comparison', () => {
    it('should compare prediction results', () => {
      const result1 = PredictionResult.simple('positive', 0.9);
      const result2 = PredictionResult.simple('negative', 0.7);
      
      expect(result1.compareTo(result2)).toBeGreaterThan(0);
      expect(result2.compareTo(result1)).toBeLessThan(0);
      expect(result1.isBetterThan(result2)).toBe(true);
      expect(result2.isBetterThan(result1)).toBe(false);
    });
  });

  describe('String Representation', () => {
    it('should convert to string', () => {
      const result = PredictionResult.withAlternatives('positive', 0.8, [
        { value: 'negative', confidence: 0.15 },
        { value: 'neutral', confidence: 0.05 }
      ]);
      
      const str = result.toString();
      expect(str).toContain('Prediction: positive');
      expect(str).toContain('80.0%');
      expect(str).toContain('Alternatives:');
      expect(str).toContain('negative');
    });

    it('should handle object predictions in string', () => {
      const objectPrediction = { class: 'cat', bbox: [10, 20, 30, 40] };
      const result = PredictionResult.simple(objectPrediction, 0.8);
      
      const str = result.toString();
      expect(str).toContain('Prediction:');
      expect(str).toContain('cat');
    });
  });

  describe('JSON Serialization', () => {
    it('should serialize to JSON', () => {
      const result = PredictionResult.withAlternatives('positive', 0.8, [
        { value: 'negative', confidence: 0.2 }
      ]);
      
      const json = result.toJSON();
      
      expect(json.prediction).toBe('positive');
      expect(json.confidence.value).toBe(0.8);
      expect(json.alternatives).toHaveLength(1);
      expect(json.hasHighConfidence).toBe(true);
      expect(json.confidenceGap).toBe(0.6);
      expect(json.isAmbiguous).toBe(false);
      expect(json.entropy).toBeGreaterThan(0);
      expect(json.age).toBeGreaterThanOrEqual(0);
    });

    it('should deserialize from JSON', () => {
      const json = {
        prediction: 'positive',
        confidence: { value: 0.8 },
        alternatives: [
          { value: 'negative', confidence: { value: 0.2 } }
        ],
        metadata: { model: 'test' },
        timestamp: new Date().toISOString()
      };
      
      const result = PredictionResult.fromJSON(json);
      
      expect(result.prediction).toBe('positive');
      expect(result.confidence.value).toBe(0.8);
      expect(result.alternatives).toHaveLength(1);
      expect(result.metadata.model).toBe('test');
    });
  });

  describe('Ensemble Methods', () => {
    it('should create ensemble from multiple results', () => {
      const results = [
        PredictionResult.simple('positive', 0.8),
        PredictionResult.simple('positive', 0.7),
        PredictionResult.simple('negative', 0.6)
      ];
      
      const ensemble = PredictionResult.ensemble(results);
      
      expect(ensemble.prediction).toBe('positive'); // Highest confidence
      expect(ensemble.metadata.ensembleMethod).toBe('average');
      expect(ensemble.metadata.ensembleSize).toBe(3);
    });

    it('should throw error for empty results in ensemble', () => {
      expect(() => PredictionResult.ensemble([])).toThrow('Cannot create ensemble from empty results');
    });

    it('should handle weighted ensemble', () => {
      const results = [
        PredictionResult.simple('positive', 0.8),
        PredictionResult.simple('negative', 0.7)
      ];
      const weights = [0.6, 0.4];
      
      const ensemble = PredictionResult.ensemble(results, 'weighted', weights);
      expect(ensemble.metadata.ensembleMethod).toBe('weighted');
    });

    it('should throw error for mismatched weights in ensemble', () => {
      const results = [PredictionResult.simple('positive', 0.8)];
      const weights = [0.6, 0.4];
      
      expect(() => PredictionResult.ensemble(results, 'weighted', weights))
        .toThrow('Weights array must have the same length as results array');
    });
  });

  describe('Immutability', () => {
    it('should return copies of arrays and objects', () => {
      const alternatives = [{ value: 'negative', confidence: new ConfidenceScore(0.2) }];
      const metadata = { model: 'test' };
      const result = new PredictionResult('positive', new ConfidenceScore(0.8), alternatives, metadata);
      
      const returnedAlts = result.alternatives;
      const returnedMetadata = result.metadata;
      
      returnedAlts.push({ value: 'neutral', confidence: new ConfidenceScore(0.1) });
      returnedMetadata.modified = true;
      
      expect(result.alternatives).toHaveLength(1);
      expect(result.metadata.modified).toBeUndefined();
    });
  });
});