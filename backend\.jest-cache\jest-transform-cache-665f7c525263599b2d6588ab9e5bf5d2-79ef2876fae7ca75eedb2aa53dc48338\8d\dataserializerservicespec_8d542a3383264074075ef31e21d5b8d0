d33776fbd018b02ffaff07404624d1ca
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const data_serializer_service_1 = require("../data-serializer.service");
describe('DataSerializerService', () => {
    let service;
    let configService;
    beforeEach(async () => {
        const mockConfigService = {
            get: jest.fn().mockImplementation((key, defaultValue) => {
                const config = {
                    'ai.serialization.compression.enabled': true,
                    'ai.serialization.compression.level': 6,
                    'ai.serialization.compression.threshold': 1024,
                };
                return config[key] || defaultValue;
            }),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                data_serializer_service_1.DataSerializerService,
                { provide: config_1.ConfigService, useValue: mockConfigService },
            ],
        }).compile();
        service = module.get(data_serializer_service_1.DataSerializerService);
        configService = module.get(config_1.ConfigService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('JSON Serialization', () => {
        describe('serializeToJson', () => {
            it('should serialize simple object to JSON', async () => {
                const data = { name: 'test', value: 123, active: true };
                const result = await service.serializeToJson(data);
                expect(result).toMatchObject({
                    format: 'json',
                    compressed: false,
                    originalSize: expect.any(Number),
                    compressedSize: expect.any(Number),
                    compressionRatio: 1,
                    processingTime: expect.any(Number),
                    metadata: expect.objectContaining({
                        encoding: 'utf8',
                    }),
                });
                expect(result.data).toBeInstanceOf(Buffer);
                expect(result.originalSize).toBeGreaterThan(0);
            });
            it('should serialize large object with compression', async () => {
                // Create large object that exceeds compression threshold
                const largeData = {
                    items: Array.from({ length: 100 }, (_, i) => ({
                        id: i,
                        name: `Item ${i}`,
                        description: 'This is a long description that will help make the data large enough to trigger compression',
                        metadata: { created: new Date(), updated: new Date(), tags: ['tag1', 'tag2', 'tag3'] },
                    })),
                };
                const result = await service.serializeToJson(largeData);
                expect(result.compressed).toBe(true);
                expect(result.compressionType).toBe('gzip');
                expect(result.compressionRatio).toBeGreaterThan(1);
                expect(result.compressedSize).toBeLessThan(result.originalSize);
            });
            it('should serialize with custom options', async () => {
                const data = { name: 'test', value: 123 };
                const options = {
                    space: 2,
                    forceCompression: true,
                    compressionType: 'deflate',
                    metadata: { source: 'test' },
                };
                const result = await service.serializeToJson(data, options);
                expect(result.compressed).toBe(true);
                expect(result.compressionType).toBe('deflate');
                expect(result.metadata).toMatchObject({
                    encoding: 'utf8',
                    source: 'test',
                });
            });
            it('should handle serialization errors', async () => {
                const circularData = {};
                circularData['self'] = circularData; // Create circular reference
                await expect(service.serializeToJson(circularData)).rejects.toThrow('JSON serialization failed');
            });
        });
        describe('deserializeFromJson', () => {
            it('should deserialize JSON data', async () => {
                const originalData = { name: 'test', value: 123, active: true };
                const serialized = await service.serializeToJson(originalData);
                const result = await service.deserializeFromJson(serialized);
                expect(result).toEqual(originalData);
            });
            it('should deserialize compressed JSON data', async () => {
                const originalData = { name: 'test', value: 123 };
                const serialized = await service.serializeToJson(originalData, { forceCompression: true });
                const result = await service.deserializeFromJson(serialized);
                expect(result).toEqual(originalData);
            });
            it('should deserialize from Buffer', async () => {
                const originalData = { name: 'test', value: 123 };
                const jsonString = JSON.stringify(originalData);
                const buffer = Buffer.from(jsonString, 'utf8');
                const result = await service.deserializeFromJson(buffer);
                expect(result).toEqual(originalData);
            });
            it('should handle deserialization errors', async () => {
                const invalidBuffer = Buffer.from('invalid json', 'utf8');
                await expect(service.deserializeFromJson(invalidBuffer)).rejects.toThrow('JSON deserialization failed');
            });
        });
    });
    describe('Protocol Buffers Serialization', () => {
        describe('serializeToProtobuf', () => {
            it('should serialize data to Protocol Buffers format', async () => {
                const data = { name: 'test', value: 123 };
                const schema = 'TestMessage';
                const result = await service.serializeToProtobuf(data, schema);
                expect(result).toMatchObject({
                    format: 'protobuf',
                    schema: 'TestMessage',
                    compressed: false,
                    originalSize: expect.any(Number),
                    compressedSize: expect.any(Number),
                    processingTime: expect.any(Number),
                    metadata: expect.objectContaining({
                        schema: 'TestMessage',
                    }),
                });
                expect(result.data).toBeInstanceOf(Buffer);
            });
            it('should serialize with compression', async () => {
                const data = { name: 'test', value: 123 };
                const schema = 'TestMessage';
                const options = { forceCompression: true };
                const result = await service.serializeToProtobuf(data, schema, options);
                expect(result.compressed).toBe(true);
                expect(result.compressionType).toBe('gzip');
            });
        });
        describe('deserializeFromProtobuf', () => {
            it('should deserialize Protocol Buffers data', async () => {
                const originalData = { name: 'test', value: 123 };
                const schema = 'TestMessage';
                const serialized = await service.serializeToProtobuf(originalData, schema);
                const result = await service.deserializeFromProtobuf(serialized, schema);
                expect(result).toEqual(originalData);
            });
            it('should handle schema mismatch', async () => {
                const originalData = { name: 'test', value: 123 };
                const schema = 'TestMessage';
                const serialized = await service.serializeToProtobuf(originalData, schema);
                await expect(service.deserializeFromProtobuf(serialized, 'DifferentSchema')).rejects.toThrow('Schema mismatch');
            });
        });
    });
    describe('Binary Serialization', () => {
        describe('serializeToBinary', () => {
            it('should serialize string to binary', async () => {
                const data = 'Hello, World!';
                const encoding = 'base64';
                const result = await service.serializeToBinary(data, encoding);
                expect(result).toMatchObject({
                    format: 'binary',
                    compressed: false,
                    originalSize: expect.any(Number),
                    compressedSize: expect.any(Number),
                    processingTime: expect.any(Number),
                    metadata: expect.objectContaining({
                        encoding: 'base64',
                    }),
                });
                expect(result.data).toBeInstanceOf(Buffer);
            });
            it('should serialize Buffer to binary', async () => {
                const data = Buffer.from('Hello, World!', 'utf8');
                const encoding = 'hex';
                const result = await service.serializeToBinary(data, encoding);
                expect(result.format).toBe('binary');
                expect(result.metadata.encoding).toBe('hex');
            });
            it('should serialize object to binary', async () => {
                const data = { name: 'test', value: 123 };
                const encoding = 'base64';
                const result = await service.serializeToBinary(data, encoding);
                expect(result.format).toBe('binary');
                expect(result.originalSize).toBeGreaterThan(0);
            });
        });
        describe('deserializeFromBinary', () => {
            it('should deserialize binary data', async () => {
                const originalData = 'Hello, World!';
                const encoding = 'base64';
                const serialized = await service.serializeToBinary(originalData, encoding);
                const result = await service.deserializeFromBinary(serialized, encoding);
                expect(result).toBeInstanceOf(Buffer);
                expect(result.toString('utf8')).toBe(originalData);
            });
            it('should deserialize from Buffer', async () => {
                const originalBuffer = Buffer.from('Hello, World!', 'utf8');
                const result = await service.deserializeFromBinary(originalBuffer, 'utf8');
                expect(result).toEqual(originalBuffer);
            });
        });
    });
    describe('Compression', () => {
        describe('compressData', () => {
            it('should compress data using gzip', async () => {
                // Use larger data that will actually compress well
                const data = Buffer.from('Hello, World! '.repeat(100), 'utf8');
                const compressed = await service.compressData(data, 'gzip');
                expect(compressed).toBeInstanceOf(Buffer);
                expect(compressed.length).toBeLessThan(data.length);
            });
            it('should compress data using deflate', async () => {
                // Use larger data that will actually compress well
                const data = Buffer.from('Hello, World! '.repeat(100), 'utf8');
                const compressed = await service.compressData(data, 'deflate');
                expect(compressed).toBeInstanceOf(Buffer);
                expect(compressed.length).toBeLessThan(data.length);
            });
            it('should handle unsupported compression type', async () => {
                const data = Buffer.from('Hello, World!', 'utf8');
                await expect(service.compressData(data, 'unsupported')).rejects.toThrow('Unsupported compression type');
            });
        });
        describe('decompressData', () => {
            it('should decompress gzip data', async () => {
                const originalData = Buffer.from('Hello, World! This is a test string for compression.', 'utf8');
                const compressed = await service.compressData(originalData, 'gzip');
                const decompressed = await service.decompressData(compressed, 'gzip');
                expect(decompressed).toEqual(originalData);
            });
            it('should decompress deflate data', async () => {
                const originalData = Buffer.from('Hello, World! This is a test string for compression.', 'utf8');
                const compressed = await service.compressData(originalData, 'deflate');
                const decompressed = await service.decompressData(compressed, 'deflate');
                expect(decompressed).toEqual(originalData);
            });
            it('should handle unsupported decompression type', async () => {
                const data = Buffer.from('Hello, World!', 'utf8');
                await expect(service.decompressData(data, 'unsupported')).rejects.toThrow('Unsupported compression type');
            });
        });
    });
    describe('Batch Operations', () => {
        describe('batchSerialize', () => {
            it('should batch serialize multiple items', async () => {
                const items = [
                    { data: { name: 'item1', value: 1 }, format: 'json' },
                    { data: { name: 'item2', value: 2 }, format: 'json' },
                    { data: 'binary data', format: 'binary', options: { encoding: 'base64' } },
                ];
                const result = await service.batchSerialize(items);
                expect(result.results).toHaveLength(3);
                expect(result.errors).toHaveLength(0);
                expect(result.summary).toMatchObject({
                    total: 3,
                    successful: 3,
                    failed: 0,
                    totalOriginalSize: expect.any(Number),
                    totalCompressedSize: expect.any(Number),
                    overallCompressionRatio: expect.any(Number),
                    processingTime: expect.any(Number),
                });
                result.results.forEach(item => {
                    expect(item).toMatchObject({
                        data: expect.any(Buffer),
                        format: expect.any(String),
                        originalSize: expect.any(Number),
                        compressedSize: expect.any(Number),
                        processingTime: expect.any(Number),
                    });
                });
            });
            it('should handle batch serialization errors', async () => {
                const items = [
                    { data: { name: 'valid' }, format: 'json' },
                    { data: {}, format: 'unsupported' }, // Invalid format
                    { data: { name: 'valid2' }, format: 'json' },
                ];
                const result = await service.batchSerialize(items);
                expect(result.results).toHaveLength(3);
                expect(result.errors).toHaveLength(1);
                expect(result.summary).toMatchObject({
                    total: 3,
                    successful: 2,
                    failed: 1,
                });
                expect(result.errors[0]).toMatchObject({
                    index: 1,
                    error: expect.any(Error),
                });
            });
        });
        describe('batchDeserialize', () => {
            it('should batch deserialize multiple items', async () => {
                // First serialize some data
                const originalItems = [
                    { data: { name: 'item1', value: 1 }, format: 'json' },
                    { data: { name: 'item2', value: 2 }, format: 'json' },
                ];
                const serialized = await service.batchSerialize(originalItems);
                // Then deserialize
                const deserializeItems = serialized.results.map((item, index) => ({
                    data: item,
                    format: originalItems[index].format,
                }));
                const result = await service.batchDeserialize(deserializeItems);
                expect(result.results).toHaveLength(2);
                expect(result.errors).toHaveLength(0);
                expect(result.summary).toMatchObject({
                    total: 2,
                    successful: 2,
                    failed: 0,
                    processingTime: expect.any(Number),
                });
                expect(result.results[0]).toEqual({ name: 'item1', value: 1 });
                expect(result.results[1]).toEqual({ name: 'item2', value: 2 });
            });
            it('should handle batch deserialization errors', async () => {
                const items = [
                    { data: Buffer.from('{"valid": true}', 'utf8'), format: 'json' },
                    { data: Buffer.from('invalid json', 'utf8'), format: 'json' },
                    { data: Buffer.from('{"valid2": true}', 'utf8'), format: 'json' },
                ];
                const result = await service.batchDeserialize(items);
                expect(result.results).toHaveLength(3);
                expect(result.errors).toHaveLength(1);
                expect(result.summary).toMatchObject({
                    total: 3,
                    successful: 2,
                    failed: 1,
                });
                expect(result.errors[0]).toMatchObject({
                    index: 1,
                    error: expect.any(Error),
                });
            });
        });
    });
    describe('Configuration and Statistics', () => {
        describe('getSerializationStats', () => {
            it('should return serialization statistics', () => {
                const stats = service.getSerializationStats();
                expect(stats).toMatchObject({
                    compressionEnabled: true,
                    compressionLevel: 6,
                    compressionThreshold: 1024,
                    supportedFormats: ['json', 'protobuf', 'binary'],
                    supportedCompressionTypes: ['gzip', 'deflate'],
                    supportedEncodings: ['base64', 'hex', 'utf8'],
                });
            });
        });
        it('should use configured compression settings', async () => {
            const mockConfigService = {
                get: jest.fn().mockImplementation((key, defaultValue) => {
                    const config = {
                        'ai.serialization.compression.enabled': false,
                        'ai.serialization.compression.level': 9,
                        'ai.serialization.compression.threshold': 2048,
                    };
                    return config[key] || defaultValue;
                }),
            };
            const module = await testing_1.Test.createTestingModule({
                providers: [
                    data_serializer_service_1.DataSerializerService,
                    { provide: config_1.ConfigService, useValue: mockConfigService },
                ],
            }).compile();
            const customService = module.get(data_serializer_service_1.DataSerializerService);
            const stats = customService.getSerializationStats();
            expect(stats).toMatchObject({
                compressionEnabled: false,
                compressionLevel: 9,
                compressionThreshold: 2048,
            });
        });
    });
    describe('Edge Cases and Error Handling', () => {
        it('should handle null data', async () => {
            const nullResult = await service.serializeToJson(null);
            expect(nullResult.data).toBeInstanceOf(Buffer);
            const deserializedNull = await service.deserializeFromJson(nullResult);
            expect(deserializedNull).toBeNull();
        });
        it('should handle undefined data by treating it as null', async () => {
            // JSON.stringify(undefined) returns undefined, so we expect this to be handled gracefully
            // In practice, undefined should be converted to null for JSON serialization
            const data = { value: undefined, name: 'test' };
            const result = await service.serializeToJson(data);
            expect(result.data).toBeInstanceOf(Buffer);
            const deserialized = await service.deserializeFromJson(result);
            expect(deserialized).toEqual({ name: 'test' }); // undefined values are omitted in JSON
        });
        it('should handle empty data', async () => {
            const emptyObject = {};
            const emptyArray = [];
            const emptyString = '';
            const objectResult = await service.serializeToJson(emptyObject);
            const arrayResult = await service.serializeToJson(emptyArray);
            const stringResult = await service.serializeToBinary(emptyString);
            expect(objectResult.data).toBeInstanceOf(Buffer);
            expect(arrayResult.data).toBeInstanceOf(Buffer);
            expect(stringResult.data).toBeInstanceOf(Buffer);
            const deserializedObject = await service.deserializeFromJson(objectResult);
            const deserializedArray = await service.deserializeFromJson(arrayResult);
            expect(deserializedObject).toEqual({});
            expect(deserializedArray).toEqual([]);
        });
        it('should handle very large data', async () => {
            // Create a large object
            const largeData = {
                items: Array.from({ length: 10000 }, (_, i) => ({
                    id: i,
                    data: `Item ${i} with some additional data to make it larger`,
                })),
            };
            const result = await service.serializeToJson(largeData);
            expect(result.compressed).toBe(true);
            expect(result.compressionRatio).toBeGreaterThan(1);
            const deserialized = await service.deserializeFromJson(result);
            expect(deserialized.items).toHaveLength(10000);
            expect(deserialized.items[0]).toEqual(largeData.items[0]);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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