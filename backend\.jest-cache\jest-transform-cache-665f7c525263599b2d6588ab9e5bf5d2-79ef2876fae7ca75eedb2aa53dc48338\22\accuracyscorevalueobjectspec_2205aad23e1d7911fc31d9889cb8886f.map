{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\value-objects\\__tests__\\accuracy-score.value-object.spec.ts", "mappings": ";;AAAA,gFAA+D;AAE/D,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;IAC1C,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,KAAK,GAAG,IAAI,2CAAa,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,KAAK,GAAG,IAAI,2CAAa,CAAC,GAAG,CAAC,CAAC;YACrC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,KAAK,GAAG,IAAI,2CAAa,CAAC,GAAG,CAAC,CAAC;YACrC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,2CAAa,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,2CAAa,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,yCAAyC,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,2CAAa,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,2CAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,2CAAa,CAAC,KAAY,CAAC,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;QAC3F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,KAAK,GAAG,2CAAa,CAAC,eAAe,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;YACrD,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,CAAC,GAAG,EAAE,CAAC,2CAAa,CAAC,eAAe,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,6CAA6C,CAAC,CAAC;YAC1G,MAAM,CAAC,GAAG,EAAE,CAAC,2CAAa,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,wCAAwC,CAAC,CAAC;YACtG,MAAM,CAAC,GAAG,EAAE,CAAC,2CAAa,CAAC,eAAe,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,qDAAqD,CAAC,CAAC;QACrH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,KAAK,GAAG,2CAAa,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;YAC/C,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,CAAC,GAAG,EAAE,CAAC,2CAAa,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC;YAChG,MAAM,CAAC,GAAG,EAAE,CAAC,2CAAa,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC;QAClG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,CAAC,2CAAa,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAChD,MAAM,CAAC,2CAAa,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC7C,MAAM,CAAC,2CAAa,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,CAAC,IAAI,2CAAa,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,2CAAa,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvD,MAAM,CAAC,IAAI,2CAAa,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACxD,MAAM,CAAC,IAAI,2CAAa,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5D,MAAM,CAAC,IAAI,2CAAa,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,SAAS,GAAG,IAAI,2CAAa,CAAC,GAAG,CAAC,CAAC;YACzC,MAAM,SAAS,GAAG,IAAI,2CAAa,CAAC,GAAG,CAAC,CAAC;YACzC,MAAM,SAAS,GAAG,IAAI,2CAAa,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,cAAc,GAAG,IAAI,2CAAa,CAAC,IAAI,CAAC,CAAC;YAE/C,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEhD,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,KAAK,GAAG,IAAI,2CAAa,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,KAAK,GAAG,IAAI,2CAAa,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,KAAK,GAAG,IAAI,2CAAa,CAAC,MAAM,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACjD,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,KAAK,GAAG,IAAI,2CAAa,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,KAAK,GAAG,IAAI,2CAAa,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,OAAO,GAAG,IAAI,2CAAa,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,QAAQ,GAAG,IAAI,2CAAa,CAAC,IAAI,CAAC,CAAC;YAEzC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,OAAO,GAAG,IAAI,2CAAa,CAAC,GAAG,CAAC,CAAC;YACvC,MAAM,QAAQ,GAAG,IAAI,2CAAa,CAAC,IAAI,CAAC,CAAC;YAEzC,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,sBAAsB;QAC5F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,OAAO,GAAG,IAAI,2CAAa,CAAC,GAAG,CAAC,CAAC;YACvC,MAAM,QAAQ,GAAG,IAAI,2CAAa,CAAC,GAAG,CAAC,CAAC;YAExC,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iEAAiE,EAAE,GAAG,EAAE;YACzE,MAAM,OAAO,GAAG,IAAI,2CAAa,CAAC,GAAG,CAAC,CAAC;YACvC,MAAM,QAAQ,GAAG,IAAI,2CAAa,CAAC,GAAG,CAAC,CAAC;YAExC,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,OAAO,GAAG,IAAI,2CAAa,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,QAAQ,GAAG,IAAI,2CAAa,CAAC,IAAI,CAAC,CAAC;YAEzC,MAAM,CAAC,OAAO,CAAC,yBAAyB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrE,MAAM,CAAC,OAAO,CAAC,yBAAyB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,MAAM,GAAG,IAAI,2CAAa,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,MAAM,GAAG,IAAI,2CAAa,CAAC,IAAI,CAAC,CAAC;YAEvC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,MAAM,GAAG,IAAI,2CAAa,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,MAAM,GAAG,IAAI,2CAAa,CAAC,IAAI,CAAC,CAAC;YAEvC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,MAAM,GAAG,IAAI,2CAAa,CAAC,GAAG,CAAC,CAAC;YACtC,MAAM,MAAM,GAAG,IAAI,2CAAa,CAAC,GAAG,CAAC,CAAC;YACtC,MAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;YAEjD,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,wBAAwB;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,MAAM,GAAG,IAAI,2CAAa,CAAC,GAAG,CAAC,CAAC;YACtC,MAAM,MAAM,GAAG,IAAI,2CAAa,CAAC,GAAG,CAAC,CAAC;YAEtC,MAAM,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;YACzF,MAAM,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,KAAK,GAAG,IAAI,2CAAa,CAAC,MAAM,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,MAAM,GAAG;gBACb,IAAI,2CAAa,CAAC,GAAG,CAAC;gBACtB,IAAI,2CAAa,CAAC,GAAG,CAAC;gBACtB,IAAI,2CAAa,CAAC,GAAG,CAAC;aACvB,CAAC;YAEF,MAAM,OAAO,GAAG,2CAAa,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,CAAC,GAAG,EAAE,CAAC,2CAAa,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,yCAAyC,CAAC,CAAC;QAC7F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,MAAM,GAAG;gBACb,IAAI,2CAAa,CAAC,GAAG,CAAC;gBACtB,IAAI,2CAAa,CAAC,GAAG,CAAC;aACvB,CAAC;YACF,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAE3B,MAAM,WAAW,GAAG,2CAAa,CAAC,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YACnE,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,MAAM,MAAM,GAAG,CAAC,IAAI,2CAAa,CAAC,GAAG,CAAC,CAAC,CAAC;YACxC,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAE3B,MAAM,CAAC,GAAG,EAAE,CAAC,2CAAa,CAAC,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;iBACzD,OAAO,CAAC,qDAAqD,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,MAAM,GAAG,CAAC,IAAI,2CAAa,CAAC,GAAG,CAAC,EAAE,IAAI,2CAAa,CAAC,GAAG,CAAC,CAAC,CAAC;YAChE,MAAM,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAEvB,MAAM,CAAC,GAAG,EAAE,CAAC,2CAAa,CAAC,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;iBACzD,OAAO,CAAC,6BAA6B,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,MAAM,GAAG;gBACb,IAAI,2CAAa,CAAC,GAAG,CAAC;gBACtB,IAAI,2CAAa,CAAC,GAAG,CAAC;gBACtB,IAAI,2CAAa,CAAC,GAAG,CAAC;aACvB,CAAC;YAEF,MAAM,CAAC,2CAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClD,MAAM,CAAC,2CAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,CAAC,GAAG,EAAE,CAAC,2CAAa,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;YAClF,MAAM,CAAC,GAAG,EAAE,CAAC,2CAAa,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,KAAK,GAAG,IAAI,2CAAa,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YAE5B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9B,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,IAAI,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;YAC7B,MAAM,KAAK,GAAG,2CAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAE3C,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,MAAM,GAAG,IAAI,2CAAa,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,MAAM,GAAG,IAAI,2CAAa,CAAC,IAAI,CAAC,CAAC;YAEvC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,MAAM,GAAG,IAAI,2CAAa,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,MAAM,GAAG,IAAI,2CAAa,CAAC,IAAI,CAAC,CAAC;YAEvC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\value-objects\\__tests__\\accuracy-score.value-object.spec.ts"], "sourcesContent": ["import { AccuracyScore } from '../accuracy-score.value-object';\r\n\r\ndescribe('AccuracyScore Value Object', () => {\r\n  describe('Construction', () => {\r\n    it('should create a valid accuracy score', () => {\r\n      const score = new AccuracyScore(0.85);\r\n      expect(score.value).toBe(0.85);\r\n    });\r\n\r\n    it('should accept minimum value', () => {\r\n      const score = new AccuracyScore(0.0);\r\n      expect(score.value).toBe(0.0);\r\n    });\r\n\r\n    it('should accept maximum value', () => {\r\n      const score = new AccuracyScore(1.0);\r\n      expect(score.value).toBe(1.0);\r\n    });\r\n\r\n    it('should throw error for negative values', () => {\r\n      expect(() => new AccuracyScore(-0.1)).toThrow('Accuracy score cannot be less than 0');\r\n    });\r\n\r\n    it('should throw error for values greater than 1', () => {\r\n      expect(() => new AccuracyScore(1.1)).toThrow('Accuracy score cannot be greater than 1');\r\n    });\r\n\r\n    it('should throw error for NaN', () => {\r\n      expect(() => new AccuracyScore(NaN)).toThrow('Accuracy score cannot be NaN');\r\n    });\r\n\r\n    it('should throw error for infinite values', () => {\r\n      expect(() => new AccuracyScore(Infinity)).toThrow('Accuracy score must be finite');\r\n    });\r\n\r\n    it('should throw error for non-number values', () => {\r\n      expect(() => new AccuracyScore('0.5' as any)).toThrow('Accuracy score must be a number');\r\n    });\r\n  });\r\n\r\n  describe('Factory Methods', () => {\r\n    it('should create from predictions', () => {\r\n      const score = AccuracyScore.fromPredictions(85, 100);\r\n      expect(score.value).toBe(0.85);\r\n    });\r\n\r\n    it('should throw error for invalid prediction counts', () => {\r\n      expect(() => AccuracyScore.fromPredictions(10, 0)).toThrow('Total predictions must be greater than zero');\r\n      expect(() => AccuracyScore.fromPredictions(-5, 10)).toThrow('Correct predictions cannot be negative');\r\n      expect(() => AccuracyScore.fromPredictions(15, 10)).toThrow('Correct predictions cannot exceed total predictions');\r\n    });\r\n\r\n    it('should create from percentage', () => {\r\n      const score = AccuracyScore.fromPercentage(85);\r\n      expect(score.value).toBe(0.85);\r\n    });\r\n\r\n    it('should throw error for invalid percentage', () => {\r\n      expect(() => AccuracyScore.fromPercentage(-10)).toThrow('Percentage must be between 0 and 100');\r\n      expect(() => AccuracyScore.fromPercentage(110)).toThrow('Percentage must be between 0 and 100');\r\n    });\r\n\r\n    it('should create predefined accuracy levels', () => {\r\n      expect(AccuracyScore.perfect().value).toBe(1.0);\r\n      expect(AccuracyScore.zero().value).toBe(0.0);\r\n      expect(AccuracyScore.random().value).toBe(0.5);\r\n    });\r\n  });\r\n\r\n  describe('Level Classification', () => {\r\n    it('should classify accuracy levels correctly', () => {\r\n      expect(new AccuracyScore(0.3).getLevel()).toBe('poor');\r\n      expect(new AccuracyScore(0.6).getLevel()).toBe('fair');\r\n      expect(new AccuracyScore(0.75).getLevel()).toBe('good');\r\n      expect(new AccuracyScore(0.9).getLevel()).toBe('very_good');\r\n      expect(new AccuracyScore(0.98).getLevel()).toBe('excellent');\r\n    });\r\n\r\n    it('should check level predicates', () => {\r\n      const poorScore = new AccuracyScore(0.3);\r\n      const fairScore = new AccuracyScore(0.6);\r\n      const goodScore = new AccuracyScore(0.85);\r\n      const excellentScore = new AccuracyScore(0.98);\r\n\r\n      expect(poorScore.isPoor()).toBe(true);\r\n      expect(fairScore.isFair()).toBe(true);\r\n      expect(goodScore.isGood()).toBe(true);\r\n      expect(excellentScore.isExcellent()).toBe(true);\r\n\r\n      expect(excellentScore.isPoor()).toBe(false);\r\n      expect(poorScore.isGood()).toBe(false);\r\n    });\r\n\r\n    it('should check threshold compliance', () => {\r\n      const score = new AccuracyScore(0.75);\r\n      expect(score.meetsThreshold(0.7)).toBe(true);\r\n      expect(score.meetsThreshold(0.8)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('Conversions and Metrics', () => {\r\n    it('should convert to percentage', () => {\r\n      const score = new AccuracyScore(0.85);\r\n      expect(score.toPercentage()).toBe(85);\r\n    });\r\n\r\n    it('should convert to percentage string', () => {\r\n      const score = new AccuracyScore(0.8567);\r\n      expect(score.toPercentageString()).toBe('85.7%');\r\n      expect(score.toPercentageString(2)).toBe('85.67%');\r\n    });\r\n\r\n    it('should calculate error rate', () => {\r\n      const score = new AccuracyScore(0.85);\r\n      expect(score.getErrorRate()).toBe(0.15);\r\n      expect(score.getErrorRatePercentage()).toBe(15);\r\n    });\r\n\r\n    it('should convert to string', () => {\r\n      const score = new AccuracyScore(0.85);\r\n      expect(score.toString()).toBe('85.0% (very_good)');\r\n    });\r\n  });\r\n\r\n  describe('Comparison Operations', () => {\r\n    it('should calculate improvement over baseline', () => {\r\n      const current = new AccuracyScore(0.85);\r\n      const baseline = new AccuracyScore(0.75);\r\n      \r\n      expect(current.improvementOver(baseline)).toBe(0.1);\r\n    });\r\n\r\n    it('should calculate relative improvement', () => {\r\n      const current = new AccuracyScore(0.9);\r\n      const baseline = new AccuracyScore(0.75);\r\n      \r\n      expect(current.relativeImprovementOver(baseline)).toBeCloseTo(0.2); // (0.9 - 0.75) / 0.75\r\n    });\r\n\r\n    it('should handle zero baseline in relative improvement', () => {\r\n      const current = new AccuracyScore(0.5);\r\n      const baseline = new AccuracyScore(0.0);\r\n      \r\n      expect(current.relativeImprovementOver(baseline)).toBe(Infinity);\r\n    });\r\n\r\n    it('should handle zero current and baseline in relative improvement', () => {\r\n      const current = new AccuracyScore(0.0);\r\n      const baseline = new AccuracyScore(0.0);\r\n      \r\n      expect(current.relativeImprovementOver(baseline)).toBe(0);\r\n    });\r\n\r\n    it('should check significant improvement', () => {\r\n      const current = new AccuracyScore(0.85);\r\n      const baseline = new AccuracyScore(0.75);\r\n      \r\n      expect(current.isSignificantlyBetterThan(baseline, 0.05)).toBe(true);\r\n      expect(current.isSignificantlyBetterThan(baseline, 0.15)).toBe(false);\r\n    });\r\n\r\n    it('should compare accuracy scores', () => {\r\n      const score1 = new AccuracyScore(0.85);\r\n      const score2 = new AccuracyScore(0.75);\r\n      \r\n      expect(score1.isGreaterThan(score2)).toBe(true);\r\n      expect(score2.isLessThan(score1)).toBe(true);\r\n      expect(score1.isLessThan(score2)).toBe(false);\r\n      expect(score2.isGreaterThan(score1)).toBe(false);\r\n    });\r\n\r\n    it('should calculate difference', () => {\r\n      const score1 = new AccuracyScore(0.85);\r\n      const score2 = new AccuracyScore(0.75);\r\n      \r\n      expect(score1.differenceFrom(score2)).toBe(0.1);\r\n      expect(score2.differenceFrom(score1)).toBe(0.1);\r\n    });\r\n  });\r\n\r\n  describe('Mathematical Operations', () => {\r\n    it('should combine with another accuracy score', () => {\r\n      const score1 = new AccuracyScore(0.8);\r\n      const score2 = new AccuracyScore(0.6);\r\n      const combined = score1.combineWith(score2, 0.7);\r\n      \r\n      expect(combined.value).toBeCloseTo(0.74); // 0.8 * 0.7 + 0.6 * 0.3\r\n    });\r\n\r\n    it('should throw error for invalid weight in combine', () => {\r\n      const score1 = new AccuracyScore(0.8);\r\n      const score2 = new AccuracyScore(0.6);\r\n      \r\n      expect(() => score1.combineWith(score2, -0.1)).toThrow('Weight must be between 0 and 1');\r\n      expect(() => score1.combineWith(score2, 1.1)).toThrow('Weight must be between 0 and 1');\r\n    });\r\n\r\n    it('should round accuracy scores', () => {\r\n      const score = new AccuracyScore(0.8567);\r\n      expect(score.round().value).toBe(0.857);\r\n      expect(score.round(2).value).toBe(0.86);\r\n    });\r\n  });\r\n\r\n  describe('Static Utility Methods', () => {\r\n    it('should calculate average', () => {\r\n      const scores = [\r\n        new AccuracyScore(0.8),\r\n        new AccuracyScore(0.6),\r\n        new AccuracyScore(0.9),\r\n      ];\r\n      \r\n      const average = AccuracyScore.average(scores);\r\n      expect(average.value).toBeCloseTo(0.7667, 3);\r\n    });\r\n\r\n    it('should throw error for empty array in average', () => {\r\n      expect(() => AccuracyScore.average([])).toThrow('Cannot calculate average of empty array');\r\n    });\r\n\r\n    it('should calculate weighted average', () => {\r\n      const scores = [\r\n        new AccuracyScore(0.8),\r\n        new AccuracyScore(0.6),\r\n      ];\r\n      const weights = [0.7, 0.3];\r\n      \r\n      const weightedAvg = AccuracyScore.weightedAverage(scores, weights);\r\n      expect(weightedAvg.value).toBeCloseTo(0.74);\r\n    });\r\n\r\n    it('should throw error for mismatched arrays in weighted average', () => {\r\n      const scores = [new AccuracyScore(0.8)];\r\n      const weights = [0.7, 0.3];\r\n      \r\n      expect(() => AccuracyScore.weightedAverage(scores, weights))\r\n        .toThrow('Scores and weights arrays must have the same length');\r\n    });\r\n\r\n    it('should throw error for zero total weight', () => {\r\n      const scores = [new AccuracyScore(0.8), new AccuracyScore(0.6)];\r\n      const weights = [0, 0];\r\n      \r\n      expect(() => AccuracyScore.weightedAverage(scores, weights))\r\n        .toThrow('Total weight cannot be zero');\r\n    });\r\n\r\n    it('should find maximum and minimum', () => {\r\n      const scores = [\r\n        new AccuracyScore(0.8),\r\n        new AccuracyScore(0.3),\r\n        new AccuracyScore(0.9),\r\n      ];\r\n      \r\n      expect(AccuracyScore.max(scores).value).toBe(0.9);\r\n      expect(AccuracyScore.min(scores).value).toBe(0.3);\r\n    });\r\n\r\n    it('should throw error for empty array in max/min', () => {\r\n      expect(() => AccuracyScore.max([])).toThrow('Cannot find maximum of empty array');\r\n      expect(() => AccuracyScore.min([])).toThrow('Cannot find minimum of empty array');\r\n    });\r\n  });\r\n\r\n  describe('JSON Serialization', () => {\r\n    it('should serialize to JSON', () => {\r\n      const score = new AccuracyScore(0.85);\r\n      const json = score.toJSON();\r\n      \r\n      expect(json.value).toBe(0.85);\r\n      expect(json.percentage).toBe(85);\r\n      expect(json.level).toBe('very_good');\r\n      expect(json.errorRate).toBe(0.15);\r\n      expect(json.isGood).toBe(true);\r\n      expect(json.isExcellent).toBe(false);\r\n    });\r\n\r\n    it('should deserialize from JSON', () => {\r\n      const json = { value: 0.75 };\r\n      const score = AccuracyScore.fromJSON(json);\r\n      \r\n      expect(score.value).toBe(0.75);\r\n    });\r\n  });\r\n\r\n  describe('Equality', () => {\r\n    it('should be equal to another accuracy score with same value', () => {\r\n      const score1 = new AccuracyScore(0.85);\r\n      const score2 = new AccuracyScore(0.85);\r\n      \r\n      expect(score1.equals(score2)).toBe(true);\r\n    });\r\n\r\n    it('should not be equal to accuracy score with different value', () => {\r\n      const score1 = new AccuracyScore(0.85);\r\n      const score2 = new AccuracyScore(0.75);\r\n      \r\n      expect(score1.equals(score2)).toBe(false);\r\n    });\r\n  });\r\n});"], "version": 3}