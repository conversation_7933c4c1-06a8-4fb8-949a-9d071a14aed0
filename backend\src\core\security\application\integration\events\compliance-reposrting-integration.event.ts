import { BaseIntegrationEvent } from '../../../../../shared-kernel/integration/base-integration-event';

/**
 * Compliance Framework Types
 */
export enum ComplianceFramework {
  PCI_DSS = 'pci_dss',
  GDPR = 'gdpr',
  HIPAA = 'hipaa',
  SOX = 'sox',
  ISO_27001 = 'iso_27001',
  NIST_CSF = 'nist_csf',
  SOC2 = 'soc2',
  FISMA = 'fisma',
  CCPA = 'ccpa',
  CUSTOM = 'custom',
}

/**
 * Report Types
 */
export enum ReportType {
  COMPLIANCE_STATUS = 'compliance_status',
  AUDIT_FINDINGS = 'audit_findings',
  RISK_ASSESSMENT = 'risk_assessment',
  INCIDENT_SUMMARY = 'incident_summary',
  VULNERABILITY_REPORT = 'vulnerability_report',
  CONTROL_EFFECTIVENESS = 'control_effectiveness',
  REMEDIATION_STATUS = 'remediation_status',
  EXECUTIVE_SUMMARY = 'executive_summary',
}

/**
 * Compliance Reporting Integration Event Data
 */
export interface ComplianceReportingIntegrationEventData {
  /** Report information */
  report: {
    /** Report ID */
    id: string;
    /** Report type */
    type: ReportType;
    /** Report title */
    title: string;
    /** Report description */
    description: string;
    /** Compliance framework */
    framework: ComplianceFramework;
    /** Reporting period */
    reportingPeriod: {
      from: string;
      to: string;
      frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annually' | 'ad_hoc';
    };
    /** Report status */
    status: 'draft' | 'review' | 'approved' | 'submitted' | 'accepted' | 'rejected';
    /** Report priority */
    priority: 'low' | 'medium' | 'high' | 'critical';
  };

  /** Compliance data */
  complianceData: {
    /** Overall compliance score */
    overallScore: number;
    /** Compliance status */
    status: 'compliant' | 'partially_compliant' | 'non_compliant' | 'not_assessed';
    /** Requirements assessment */
    requirements: Array<{
      requirementId: string;
      requirementName: string;
      category: string;
      status: 'compliant' | 'non_compliant' | 'partially_compliant' | 'not_applicable';
      score: number;
      evidence: Array<{
        type: 'document' | 'screenshot' | 'log' | 'certificate' | 'attestation';
        description: string;
        url?: string;
        hash?: string;
        timestamp: string;
      }>;
      gaps: Array<{
        description: string;
        severity: 'low' | 'medium' | 'high' | 'critical';
        remediation: string;
        dueDate?: string;
        assignee?: string;
      }>;
      lastAssessed: string;
      nextAssessment: string;
    }>;
    /** Control effectiveness */
    controls: Array<{
      controlId: string;
      controlName: string;
      controlType: 'preventive' | 'detective' | 'corrective' | 'compensating';
      effectiveness: 'effective' | 'partially_effective' | 'ineffective' | 'not_tested';
      testResults: Array<{
        testDate: string;
        testType: 'automated' | 'manual' | 'walkthrough' | 'inquiry';
        result: 'pass' | 'fail' | 'exception' | 'not_applicable';
        findings: string[];
        tester: string;
      }>;
      deficiencies: Array<{
        description: string;
        severity: 'low' | 'medium' | 'high' | 'critical';
        remediation: string;
        status: 'open' | 'in_progress' | 'resolved';
      }>;
    }>;
    /** Risk assessment */
    riskAssessment: {
      overallRisk: 'low' | 'medium' | 'high' | 'critical';
      riskScore: number;
      risks: Array<{
        riskId: string;
        description: string;
        category: string;
        likelihood: 'very_low' | 'low' | 'medium' | 'high' | 'very_high';
        impact: 'very_low' | 'low' | 'medium' | 'high' | 'very_high';
        riskScore: number;
        mitigation: string;
        owner: string;
        status: 'identified' | 'assessed' | 'mitigated' | 'accepted' | 'transferred';
      }>;
    };
  };

  /** Incidents and findings */
  incidents: Array<{
    incidentId: string;
    title: string;
    description: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    status: 'open' | 'investigating' | 'resolved' | 'closed';
    complianceImpact: Array<{
      framework: ComplianceFramework;
      requirement: string;
      impact: 'low' | 'medium' | 'high' | 'critical';
      reportingRequired: boolean;
    }>;
    timeline: {
      detected: string;
      reported: string;
      contained?: string;
      resolved?: string;
    };
    rootCause?: string;
    remediation?: string;
    lessonsLearned?: string;
  }>;

  /** Vulnerabilities */
  vulnerabilities: Array<{
    vulnerabilityId: string;
    cveId?: string;
    title: string;
    description: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    cvssScore?: number;
    affectedAssets: string[];
    complianceImpact: Array<{
      framework: ComplianceFramework;
      requirement: string;
      impact: 'low' | 'medium' | 'high' | 'critical';
    }>;
    remediation: {
      status: 'open' | 'in_progress' | 'resolved' | 'accepted';
      dueDate?: string;
      assignee?: string;
      actions: string[];
    };
    discoveryDate: string;
    lastUpdated: string;
  }>;

  /** Metrics and KPIs */
  metrics: {
    /** Compliance metrics */
    compliance: {
      complianceRate: number;
      improvementTrend: 'improving' | 'stable' | 'declining';
      requirementsCovered: number;
      totalRequirements: number;
      controlsEffective: number;
      totalControls: number;
    };
    /** Security metrics */
    security: {
      incidentCount: number;
      vulnerabilityCount: number;
      averageResolutionTime: number;
      slaCompliance: number;
      riskReduction: number;
    };
    /** Operational metrics */
    operational: {
      auditReadiness: number;
      documentationCompleteness: number;
      trainingCompliance: number;
      processMaturity: number;
    };
  };

  /** Regulatory information */
  regulatory: {
    /** Regulatory body */
    regulatoryBody: string;
    /** Reporting requirements */
    reportingRequirements: Array<{
      requirement: string;
      deadline: string;
      status: 'pending' | 'submitted' | 'accepted' | 'rejected';
      submissionMethod: 'portal' | 'email' | 'mail' | 'api';
    }>;
    /** Penalties and fines */
    penalties: Array<{
      type: 'warning' | 'fine' | 'suspension' | 'revocation';
      amount?: number;
      description: string;
      dueDate?: string;
      status: 'pending' | 'paid' | 'appealed' | 'waived';
    }>;
    /** Certifications */
    certifications: Array<{
      certification: string;
      status: 'valid' | 'expired' | 'suspended' | 'revoked';
      issueDate: string;
      expiryDate: string;
      renewalRequired: boolean;
    }>;
  };

  /** Report generation */
  generation: {
    /** Generation timestamp */
    generatedAt: string;
    /** Generated by */
    generatedBy: string;
    /** Generation method */
    method: 'automated' | 'manual' | 'scheduled';
    /** Data sources */
    dataSources: Array<{
      source: string;
      dataRange: {
        from: string;
        to: string;
      };
      recordCount: number;
      quality: number;
    }>;
    /** Report format */
    format: 'pdf' | 'html' | 'excel' | 'json' | 'xml';
    /** Report size */
    size: number;
    /** Report location */
    location: string;
    /** Digital signature */
    signature?: {
      algorithm: string;
      hash: string;
      timestamp: string;
      signer: string;
    };
  };

  /** Integration metadata */
  integration: {
    /** Integration ID */
    integrationId: string;
    /** Target system */
    targetSystem: string;
    /** Submission method */
    submissionMethod: 'api' | 'portal' | 'email' | 'sftp' | 'manual';
    /** Processing status */
    processingStatus: 'pending' | 'processing' | 'completed' | 'failed';
    /** Submission timestamp */
    submittedAt?: string;
    /** Acknowledgment received */
    acknowledgmentReceived?: boolean;
    /** Tracking number */
    trackingNumber?: string;
    /** Errors */
    errors: string[];
    /** Retry attempts */
    retryAttempts: number;
    /** Next retry */
    nextRetry?: string;
  };
}

/**
 * Compliance Reporting Integration Event
 *
 * Published when compliance reports are generated and need to be submitted
 * to regulatory bodies or internal stakeholders.
 *
 * Key use cases:
 * - Submit compliance reports to regulatory authorities
 * - Generate executive compliance dashboards
 * - Automate compliance documentation
 * - Track compliance metrics and trends
 * - Manage audit findings and remediation
 * - Monitor regulatory deadlines and requirements
 */
export class ComplianceReportingIntegrationEvent extends BaseIntegrationEvent<ComplianceReportingIntegrationEventData> {
  constructor(
    eventData: ComplianceReportingIntegrationEventData,
    options?: {
      eventId?: string;
      correlationId?: string;
      causationId?: string;
      timestamp?: Date;
      version?: string;
      metadata?: Record<string, any>;
    }
  ) {
    super(
      'ComplianceReportingIntegration',
      eventData,
      {
        source: 'sentinel-backend',
        version: options?.version || '1.0',
        correlationId: options?.correlationId,
        causationId: options?.causationId,
        metadata: {
          domain: 'Security',
          category: 'Compliance',
          ...options?.metadata,
        },
      }
    );
  }

  /**
   * Get typed event data
   */
  private get data(): ComplianceReportingIntegrationEventData {
    return this.eventData;
  }

  /**
   * Validate event data
   */
  validate(): boolean {
    const data = this.data;
    if (!data?.report?.id) return false;
    if (!data?.report?.type) return false;
    if (!data?.report?.framework) return false;
    if (!data?.complianceData) return false;
    if (typeof data?.complianceData?.overallScore !== 'number') return false;
    if (!data?.generation?.generatedAt) return false;
    if (!data?.integration?.integrationId) return false;
    return true;
  }

  /**
   * Get event priority
   */
  getPriority(): 'low' | 'medium' | 'high' | 'critical' {
    if (this.isHighPriority() || this.hasCriticalGaps()) {
      return 'critical';
    }
    if (this.isComplianceAtRisk()) {
      return 'high';
    }
    if (this.hasOverdueItems()) {
      return 'medium';
    }
    return 'low';
  }

  /**
   * Check if event requires immediate processing
   */
  requiresImmediateProcessing(): boolean {
    return this.isHighPriority() ||
           this.hasCriticalGaps() ||
           this.isComplianceAtRisk() ||
           this.hasOverdueItems();
  }

  /**
   * Get report ID
   */
  get reportId(): string {
    return this.data.report.id;
  }

  /**
   * Get report type
   */
  get reportType(): ReportType {
    return this.data.report.type;
  }

  /**
   * Get compliance framework
   */
  get framework(): ComplianceFramework {
    return this.data.report.framework;
  }

  /**
   * Get overall compliance score
   */
  get complianceScore(): number {
    return this.data.complianceData.overallScore;
  }

  /**
   * Get compliance status
   */
  get complianceStatus(): 'compliant' | 'partially_compliant' | 'non_compliant' | 'not_assessed' {
    return this.data.complianceData.status;
  }

  /**
   * Get report status
   */
  get reportStatus(): 'draft' | 'review' | 'approved' | 'submitted' | 'accepted' | 'rejected' {
    return this.data.report.status;
  }

  /**
   * Get report priority
   */
  get priority(): 'low' | 'medium' | 'high' | 'critical' {
    return this.data.report.priority;
  }

  /**
   * Check if report is high priority
   */
  isHighPriority(): boolean {
    return this.data.report.priority === 'high' ||
           this.data.report.priority === 'critical';
  }

  /**
   * Check if compliance is at risk
   */
  isComplianceAtRisk(): boolean {
    return this.data.complianceData.status === 'non_compliant' ||
           this.data.complianceData.status === 'partially_compliant' ||
           this.data.complianceData.overallScore < 80;
  }

  /**
   * Check if regulatory reporting is required
   */
  requiresRegulatoryReporting(): boolean {
    return this.data.regulatory.reportingRequirements.length > 0;
  }

  /**
   * Check if there are critical gaps
   */
  hasCriticalGaps(): boolean {
    return this.data.complianceData.requirements.some(req =>
      req.gaps.some(gap => gap.severity === 'critical')
    );
  }

  /**
   * Check if there are overdue items
   */
  hasOverdueItems(): boolean {
    const now = new Date();
    return this.data.complianceData.requirements.some(req =>
      req.gaps.some(gap => gap.dueDate && new Date(gap.dueDate) < now)
    ) || this.data.regulatory.reportingRequirements.some(req =>
      new Date(req.deadline) < now && req.status === 'pending'
    );
  }

  /**
   * Get critical findings count
   */
  getCriticalFindingsCount(): number {
    return this.data.complianceData.requirements.reduce((count, req) =>
      count + req.gaps.filter(gap => gap.severity === 'critical').length, 0
    );
  }

  /**
   * Get high-risk vulnerabilities count
   */
  getHighRiskVulnerabilitiesCount(): number {
    return this.data.vulnerabilities.filter(vuln =>
      vuln.severity === 'high' || vuln.severity === 'critical'
    ).length;
  }

  /**
   * Get compliance trend
   */
  getComplianceTrend(): 'improving' | 'stable' | 'declining' {
    return this.data.metrics.compliance.improvementTrend;
  }

  /**
   * Get integration actions to trigger
   */
  getIntegrationActions(): string[] {
    const actions: string[] = [];

    if (this.data.report.status === 'approved') {
      actions.push('submit_report', 'notify_stakeholders');
    }

    if (this.requiresRegulatoryReporting()) {
      actions.push('regulatory_submission', 'track_submission_status');
    }

    if (this.isComplianceAtRisk()) {
      actions.push('compliance_alert', 'escalate_to_management');
    }

    if (this.hasCriticalGaps()) {
      actions.push('critical_gap_notification', 'immediate_remediation');
    }

    if (this.hasOverdueItems()) {
      actions.push('overdue_item_alert', 'deadline_escalation');
    }

    actions.push('update_compliance_dashboard', 'archive_report');

    return actions;
  }

  /**
   * Get notification requirements
   */
  getNotificationRequirements(): {
    urgency: 'low' | 'normal' | 'high' | 'critical';
    channels: string[];
    recipients: string[];
  } {
    let urgency: 'low' | 'normal' | 'high' | 'critical' = 'normal';
    const channels: string[] = ['email', 'dashboard'];
    const recipients: string[] = ['compliance_team'];

    if (this.isHighPriority() || this.hasCriticalGaps()) {
      urgency = 'critical';
      channels.push('sms', 'phone');
      recipients.push('compliance_officer', 'ciso', 'legal_team');
    } else if (this.isComplianceAtRisk()) {
      urgency = 'high';
      channels.push('slack');
      recipients.push('compliance_officer');
    }

    if (this.requiresRegulatoryReporting()) {
      recipients.push('regulatory_affairs', 'external_auditor');
    }

    if (this.hasOverdueItems()) {
      recipients.push('risk_management');
    }

    return { urgency, channels, recipients };
  }

  /**
   * Get remediation priorities
   */
  getRemediationPriorities(): Array<{
    type: 'gap' | 'vulnerability' | 'incident';
    id: string;
    description: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    dueDate?: string;
    assignee?: string;
    framework: ComplianceFramework;
  }> {
    const priorities: Array<{
      type: 'gap' | 'vulnerability' | 'incident';
      id: string;
      description: string;
      severity: 'low' | 'medium' | 'high' | 'critical';
      dueDate?: string;
      assignee?: string;
      framework: ComplianceFramework;
    }> = [];

    // Add compliance gaps
    this.data.complianceData.requirements.forEach(req => {
      req.gaps.forEach(gap => {
        priorities.push({
          type: 'gap',
          id: req.requirementId,
          description: gap.description,
          severity: gap.severity,
          dueDate: gap.dueDate,
          assignee: gap.assignee,
          framework: this.data.report.framework,
        });
      });
    });

    // Add high-risk vulnerabilities
    this.data.vulnerabilities
      .filter(vuln => vuln.severity === 'high' || vuln.severity === 'critical')
      .forEach(vuln => {
        priorities.push({
          type: 'vulnerability',
          id: vuln.vulnerabilityId,
          description: vuln.title,
          severity: vuln.severity,
          dueDate: vuln.remediation.dueDate,
          assignee: vuln.remediation.assignee,
          framework: this.data.report.framework,
        });
      });

    // Add open incidents with compliance impact
    this.data.incidents
      .filter(incident => incident.status === 'open' || incident.status === 'investigating')
      .forEach(incident => {
        priorities.push({
          type: 'incident',
          id: incident.incidentId,
          description: incident.title,
          severity: incident.severity,
          framework: this.data.report.framework,
        });
      });

    // Sort by severity and due date
    return priorities.sort((a, b) => {
      const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      const severityDiff = severityOrder[b.severity] - severityOrder[a.severity];

      if (severityDiff !== 0) return severityDiff;

      if (a.dueDate && b.dueDate) {
        return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
      }

      return 0;
    });
  }

  /**
   * Convert to external reporting format
   */
  toExternalFormat(): {
    report: {
      id: string;
      type: string;
      title: string;
      framework: string;
      period: {
        from: string;
        to: string;
        frequency: string;
      };
      status: string;
      priority: string;
      generatedAt: string;
      generatedBy: string;
    };
    compliance: {
      overallScore: number;
      status: string;
      trend: string;
      requirementsCovered: number;
      totalRequirements: number;
      controlsEffective: number;
      totalControls: number;
    };
    findings: {
      criticalGaps: number;
      highRiskVulnerabilities: number;
      openIncidents: number;
      overdueItems: number;
    };
    metrics: {
      complianceRate: number;
      incidentCount: number;
      vulnerabilityCount: number;
      averageResolutionTime: number;
      slaCompliance: number;
      auditReadiness: number;
    };
    regulatory: {
      reportingRequired: boolean;
      pendingSubmissions: number;
      activePenalties: number;
      certificationStatus: string;
    };
    actions: {
      immediate: string[];
      notifications: {
        urgency: string;
        channels: string[];
        recipients: string[];
      };
      remediationPriorities: Array<{
        type: string;
        description: string;
        severity: string;
        dueDate?: string;
      }>;
    };
  } {
    const notifications = this.getNotificationRequirements();
    const remediationPriorities = this.getRemediationPriorities();

    return {
      report: {
        id: this.data.report.id,
        type: this.data.report.type,
        title: this.data.report.title,
        framework: this.data.report.framework,
        period: this.data.report.reportingPeriod,
        status: this.data.report.status,
        priority: this.data.report.priority,
        generatedAt: this.data.generation.generatedAt,
        generatedBy: this.data.generation.generatedBy,
      },
      compliance: {
        overallScore: this.data.complianceData.overallScore,
        status: this.data.complianceData.status,
        trend: this.data.metrics.compliance.improvementTrend,
        requirementsCovered: this.data.metrics.compliance.requirementsCovered,
        totalRequirements: this.data.metrics.compliance.totalRequirements,
        controlsEffective: this.data.metrics.compliance.controlsEffective,
        totalControls: this.data.metrics.compliance.totalControls,
      },
      findings: {
        criticalGaps: this.getCriticalFindingsCount(),
        highRiskVulnerabilities: this.getHighRiskVulnerabilitiesCount(),
        openIncidents: this.data.incidents.filter(i => i.status === 'open' || i.status === 'investigating').length,
        overdueItems: this.hasOverdueItems() ? 1 : 0,
      },
      metrics: {
        complianceRate: this.data.metrics.compliance.complianceRate,
        incidentCount: this.data.metrics.security.incidentCount,
        vulnerabilityCount: this.data.metrics.security.vulnerabilityCount,
        averageResolutionTime: this.data.metrics.security.averageResolutionTime,
        slaCompliance: this.data.metrics.security.slaCompliance,
        auditReadiness: this.data.metrics.operational.auditReadiness,
      },
      regulatory: {
        reportingRequired: this.requiresRegulatoryReporting(),
        pendingSubmissions: this.data.regulatory.reportingRequirements.filter(r => r.status === 'pending').length,
        activePenalties: this.data.regulatory.penalties.filter(p => p.status === 'pending').length,
        certificationStatus: this.data.regulatory.certifications.length > 0 ?
          this.data.regulatory.certifications[0].status : 'none',
      },
      actions: {
        immediate: this.getIntegrationActions(),
        notifications,
        remediationPriorities: remediationPriorities.slice(0, 10).map(priority => ({
          type: priority.type,
          description: priority.description,
          severity: priority.severity,
          dueDate: priority.dueDate,
        })),
      },
    };
  }

  /**
   * Create compliance status report
   */
  static createComplianceStatusReport(
    framework: ComplianceFramework,
    complianceData: any,
    reportingPeriod: { from: Date; to: Date }
  ): ComplianceReportingIntegrationEvent {
    const now = new Date();
    const reportId = `compliance-${framework}-${Date.now()}`;

    const eventData: ComplianceReportingIntegrationEventData = {
      report: {
        id: reportId,
        type: ReportType.COMPLIANCE_STATUS,
        title: `${framework.toUpperCase()} Compliance Status Report`,
        description: `Comprehensive compliance status report for ${framework}`,
        framework,
        reportingPeriod: {
          from: reportingPeriod.from.toISOString(),
          to: reportingPeriod.to.toISOString(),
          frequency: 'monthly',
        },
        status: 'draft',
        priority: complianceData.overallScore < 80 ? 'high' : 'medium',
      },
      complianceData,
      incidents: [],
      vulnerabilities: [],
      metrics: {
        compliance: {
          complianceRate: complianceData.overallScore / 100,
          improvementTrend: 'stable',
          requirementsCovered: complianceData.requirements?.length || 0,
          totalRequirements: complianceData.requirements?.length || 0,
          controlsEffective: complianceData.controls?.filter((c: any) => c.effectiveness === 'effective').length || 0,
          totalControls: complianceData.controls?.length || 0,
        },
        security: {
          incidentCount: 0,
          vulnerabilityCount: 0,
          averageResolutionTime: 0,
          slaCompliance: 0.95,
          riskReduction: 0.25,
        },
        operational: {
          auditReadiness: 0.85,
          documentationCompleteness: 0.90,
          trainingCompliance: 0.95,
          processMaturity: 0.80,
        },
      },
      regulatory: {
        regulatoryBody: 'Internal Compliance',
        reportingRequirements: [],
        penalties: [],
        certifications: [],
      },
      generation: {
        generatedAt: now.toISOString(),
        generatedBy: 'system',
        method: 'automated',
        dataSources: [],
        format: 'pdf',
        size: 0,
        location: '',
      },
      integration: {
        integrationId: 'compliance-reporting',
        targetSystem: 'internal',
        submissionMethod: 'portal',
        processingStatus: 'pending',
        errors: [],
        retryAttempts: 0,
      },
    };

    return new ComplianceReportingIntegrationEvent(eventData);
  }
}
