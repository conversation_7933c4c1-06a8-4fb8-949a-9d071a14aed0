{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai-ml\\domain\\entities\\prediction.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCASiB;AACjB,+DAAoD;AACpD,6EAAkE;AAElE;;;GAGG;AAOI,IAAM,UAAU,GAAhB,MAAM,UAAU;IAuLrB;;OAEG;IACH,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,UAAU,IAAI,GAAG,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,gBAAgB,CAAC,YAAY,IAAI,EAAE,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,IAAI,qBAAqB;QACvB,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;IACjE,CAAC;IAED;;OAEG;IACH,eAAe,CACb,MAAsC,EACtC,UAAkB,EAClB,cAAuB;QAEvB,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;QAC1B,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC;QAC/B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,cAAc,EAAE,CAAC;YACnB,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACvC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,KAAwE;QACnF,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,QAAQ,CACN,aAAkB,EAClB,WAAmB,EACnB,KAAc;QAEd,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;QACrD,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,aAAa,CAAC;QAEtD,IAAI,CAAC,cAAc,GAAG;YACpB,WAAW;YACX,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACrC,aAAa;YACb,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,KAAK;SACN,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,aAAkB;QACvC,+DAA+D;QAC/D,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE,CAAC;YACxE,8DAA8D;YAC9D,MAAM,SAAS,GAAG,GAAG,CAAC,CAAC,gBAAgB;YACvC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;YACxF,OAAO,IAAI,IAAI,SAAS,CAAC;QAC3B,CAAC;QAED,2CAA2C;QAC3C,OAAO,IAAI,CAAC,KAAK,KAAK,aAAa,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,QAAoE;QACvF,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,WAAsC;QACnD,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,OAAqC;QACrD,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAI,gBAAgB;QAClB,IAAI,IAAI,CAAC,UAAU,IAAI,GAAG;YAAE,OAAO,KAAK,CAAC;QACzC,IAAI,IAAI,CAAC,UAAU,IAAI,GAAG;YAAE,OAAO,QAAQ,CAAC;QAC5C,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,IAAI,IAAI,CAAC,IAAI,KAAK,wBAAwB,EAAE,CAAC;YAC3C,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;YAC5B,IAAI,QAAQ,KAAK,UAAU,IAAI,IAAI,CAAC,UAAU,IAAI,GAAG;gBAAE,OAAO,UAAU,CAAC;YACzE,IAAI,QAAQ,KAAK,MAAM,IAAI,IAAI,CAAC,UAAU,IAAI,GAAG;gBAAE,OAAO,MAAM,CAAC;YACjE,IAAI,QAAQ,KAAK,QAAQ,IAAI,IAAI,CAAC,UAAU,IAAI,GAAG;gBAAE,OAAO,QAAQ,CAAC;YACrE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;YAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YACzB,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,IAAI,GAAG;gBAAE,OAAO,UAAU,CAAC;YAC5D,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,IAAI,GAAG;gBAAE,OAAO,MAAM,CAAC;YACxD,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,IAAI,GAAG;gBAAE,OAAO,QAAQ,CAAC;YAC1D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,8CAA8C;QAC9C,IAAI,IAAI,CAAC,UAAU,IAAI,GAAG;YAAE,OAAO,MAAM,CAAC;QAC1C,IAAI,IAAI,CAAC,UAAU,IAAI,GAAG;YAAE,OAAO,QAAQ,CAAC;QAC5C,OAAO,KAAK,CAAC;IACf,CAAC;CACF,CAAA;AA3VY,gCAAU;AAErB;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;sCACpB;AAkBX;IAbC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE;YACJ,wBAAwB;YACxB,uBAAuB;YACvB,YAAY;YACZ,qBAAqB;YACrB,sBAAsB;YACtB,2BAA2B;YAC3B,0BAA0B;YAC1B,mBAAmB;SACpB;KACF,CAAC;;wCACW;AAMb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;kDACnC,MAAM,oBAAN,MAAM;6CAAc;AAM/B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;oDAcnD;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;;8CACjC;AAUnB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,aAAa,CAAC;QACpE,OAAO,EAAE,SAAS;KACnB,CAAC;;0CACuE;AAMzE;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;gDAC1B;AAMrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDAC7C;AAMxB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDAOjE;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAClD,KAAK,oBAAL,KAAK;qDAItB;AAMH;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;+CASxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDAMjE;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;2CAOxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;6CAK5D;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;wCAC1B;AAMhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAC/B,MAAM,oBAAN,MAAM;4CAAc;AAM/B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;6CAC3B;AAGlB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;6CAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;6CAAC;AAKhB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,iCAAW,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAChD,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,CAAC;kDAC1B,iCAAW,oBAAX,iCAAW;+CAAC;AAG1B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;iDAC3C;AAIvB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,+CAAkB,CAAC;IACnC,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,wBAAwB,EAAE,CAAC;kDAC3B,+CAAkB,oBAAlB,+CAAkB;sDAAC;AAGvC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,wBAAwB,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;wDAC5B;qBArLlB,UAAU;IANtB,IAAA,gBAAM,EAAC,aAAa,CAAC;IACrB,IAAA,eAAK,EAAC,CAAC,MAAM,CAAC,CAAC;IACf,IAAA,eAAK,EAAC,CAAC,YAAY,CAAC,CAAC;IACrB,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,WAAW,CAAC,CAAC;IACpB,IAAA,eAAK,EAAC,CAAC,cAAc,CAAC,CAAC;GACX,UAAU,CA2VtB", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai-ml\\domain\\entities\\prediction.entity.ts"], "sourcesContent": ["import {\r\n  <PERSON><PERSON>ty,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  ManyToOne,\r\n  JoinColumn,\r\n} from 'typeorm';\r\nimport { AnalysisJob } from './analysis-job.entity';\r\nimport { ModelConfiguration } from './model-configuration.entity';\r\n\r\n/**\r\n * Prediction entity\r\n * Represents AI/ML model predictions and their results\r\n */\r\n@Entity('predictions')\r\n@Index(['type'])\r\n@Index(['confidence'])\r\n@Index(['status'])\r\n@Index(['createdAt'])\r\n@Index(['modelVersion'])\r\nexport class Prediction {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Prediction type\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: [\r\n      'vulnerability_severity',\r\n      'threat_classification',\r\n      'risk_score',\r\n      'exploit_probability',\r\n      'remediation_priority',\r\n      'false_positive_likelihood',\r\n      'attack_vector_prediction',\r\n      'impact_assessment',\r\n    ],\r\n  })\r\n  type: string;\r\n\r\n  /**\r\n   * Input data used for prediction\r\n   */\r\n  @Column({ name: 'input_data', type: 'jsonb' })\r\n  inputData: Record<string, any>;\r\n\r\n  /**\r\n   * Prediction result\r\n   */\r\n  @Column({ name: 'prediction_result', type: 'jsonb' })\r\n  predictionResult: {\r\n    value: any;\r\n    probability?: number;\r\n    alternatives?: Array<{\r\n      value: any;\r\n      probability: number;\r\n    }>;\r\n    reasoning?: string;\r\n    factors?: Array<{\r\n      name: string;\r\n      weight: number;\r\n      value: any;\r\n    }>;\r\n  };\r\n\r\n  /**\r\n   * Confidence score (0-1)\r\n   */\r\n  @Column({ type: 'decimal', precision: 5, scale: 4 })\r\n  confidence: number;\r\n\r\n  /**\r\n   * Prediction status\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['pending', 'completed', 'failed', 'validated', 'invalidated'],\r\n    default: 'pending',\r\n  })\r\n  status: 'pending' | 'completed' | 'failed' | 'validated' | 'invalidated';\r\n\r\n  /**\r\n   * Model version used for prediction\r\n   */\r\n  @Column({ name: 'model_version', length: 100 })\r\n  modelVersion: string;\r\n\r\n  /**\r\n   * Processing time in milliseconds\r\n   */\r\n  @Column({ name: 'processing_time', type: 'integer', nullable: true })\r\n  processingTime?: number;\r\n\r\n  /**\r\n   * Validation information\r\n   */\r\n  @Column({ name: 'validation_info', type: 'jsonb', nullable: true })\r\n  validationInfo?: {\r\n    validatedBy?: string;\r\n    validatedAt?: string;\r\n    actualOutcome?: any;\r\n    accuracy?: number;\r\n    notes?: string;\r\n  };\r\n\r\n  /**\r\n   * Feature importance scores\r\n   */\r\n  @Column({ name: 'feature_importance', type: 'jsonb', nullable: true })\r\n  featureImportance?: Array<{\r\n    feature: string;\r\n    importance: number;\r\n    value: any;\r\n  }>;\r\n\r\n  /**\r\n   * Explanation of the prediction\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  explanation?: {\r\n    method?: string;\r\n    summary?: string;\r\n    details?: Record<string, any>;\r\n    visualizations?: Array<{\r\n      type: string;\r\n      data: any;\r\n    }>;\r\n  };\r\n\r\n  /**\r\n   * Quality metrics\r\n   */\r\n  @Column({ name: 'quality_metrics', type: 'jsonb', nullable: true })\r\n  qualityMetrics?: {\r\n    uncertainty?: number;\r\n    stability?: number;\r\n    consistency?: number;\r\n    calibration?: number;\r\n  };\r\n\r\n  /**\r\n   * Context information\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  context?: {\r\n    environment?: string;\r\n    timestamp?: string;\r\n    userAgent?: string;\r\n    sessionId?: string;\r\n    correlationId?: string;\r\n  };\r\n\r\n  /**\r\n   * Error information if prediction failed\r\n   */\r\n  @Column({ name: 'error_info', type: 'jsonb', nullable: true })\r\n  errorInfo?: {\r\n    code?: string;\r\n    message?: string;\r\n    details?: Record<string, any>;\r\n  };\r\n\r\n  /**\r\n   * Tags for categorization\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  tags?: string[];\r\n\r\n  /**\r\n   * Additional metadata\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  metadata?: Record<string, any>;\r\n\r\n  /**\r\n   * User who requested the prediction\r\n   */\r\n  @Column({ name: 'created_by', type: 'uuid' })\r\n  createdBy: string;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @ManyToOne(() => AnalysisJob, { nullable: true })\r\n  @JoinColumn({ name: 'analysis_job_id' })\r\n  analysisJob?: AnalysisJob;\r\n\r\n  @Column({ name: 'analysis_job_id', type: 'uuid', nullable: true })\r\n  analysisJobId?: string;\r\n\r\n  @ManyToOne(() => ModelConfiguration)\r\n  @JoinColumn({ name: 'model_configuration_id' })\r\n  modelConfiguration: ModelConfiguration;\r\n\r\n  @Column({ name: 'model_configuration_id', type: 'uuid' })\r\n  modelConfigurationId: string;\r\n\r\n  /**\r\n   * Check if prediction is high confidence\r\n   */\r\n  get isHighConfidence(): boolean {\r\n    return this.confidence >= 0.8;\r\n  }\r\n\r\n  /**\r\n   * Check if prediction is low confidence\r\n   */\r\n  get isLowConfidence(): boolean {\r\n    return this.confidence < 0.5;\r\n  }\r\n\r\n  /**\r\n   * Check if prediction has been validated\r\n   */\r\n  get isValidated(): boolean {\r\n    return ['validated', 'invalidated'].includes(this.status);\r\n  }\r\n\r\n  /**\r\n   * Get prediction value\r\n   */\r\n  get value(): any {\r\n    return this.predictionResult.value;\r\n  }\r\n\r\n  /**\r\n   * Get prediction probability\r\n   */\r\n  get probability(): number {\r\n    return this.predictionResult.probability || this.confidence;\r\n  }\r\n\r\n  /**\r\n   * Get top alternative predictions\r\n   */\r\n  get alternatives(): Array<{ value: any; probability: number }> {\r\n    return this.predictionResult.alternatives || [];\r\n  }\r\n\r\n  /**\r\n   * Get processing time in seconds\r\n   */\r\n  get processingTimeSeconds(): number | null {\r\n    return this.processingTime ? this.processingTime / 1000 : null;\r\n  }\r\n\r\n  /**\r\n   * Mark prediction as completed\r\n   */\r\n  markAsCompleted(\r\n    result: Prediction['predictionResult'],\r\n    confidence: number,\r\n    processingTime?: number,\r\n  ): void {\r\n    this.status = 'completed';\r\n    this.predictionResult = result;\r\n    this.confidence = confidence;\r\n    if (processingTime) {\r\n      this.processingTime = processingTime;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Mark prediction as failed\r\n   */\r\n  markAsFailed(error: { code?: string; message: string; details?: Record<string, any> }): void {\r\n    this.status = 'failed';\r\n    this.errorInfo = error;\r\n  }\r\n\r\n  /**\r\n   * Validate prediction with actual outcome\r\n   */\r\n  validate(\r\n    actualOutcome: any,\r\n    validatedBy: string,\r\n    notes?: string,\r\n  ): void {\r\n    const isCorrect = this.compareOutcome(actualOutcome);\r\n    this.status = isCorrect ? 'validated' : 'invalidated';\r\n    \r\n    this.validationInfo = {\r\n      validatedBy,\r\n      validatedAt: new Date().toISOString(),\r\n      actualOutcome,\r\n      accuracy: isCorrect ? 1 : 0,\r\n      notes,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Compare predicted outcome with actual outcome\r\n   */\r\n  private compareOutcome(actualOutcome: any): boolean {\r\n    // Simple comparison - can be enhanced based on prediction type\r\n    if (typeof this.value === 'number' && typeof actualOutcome === 'number') {\r\n      // For numerical predictions, check if within reasonable range\r\n      const tolerance = 0.1; // 10% tolerance\r\n      const diff = Math.abs(this.value - actualOutcome) / Math.max(this.value, actualOutcome);\r\n      return diff <= tolerance;\r\n    }\r\n    \r\n    // For categorical predictions, exact match\r\n    return this.value === actualOutcome;\r\n  }\r\n\r\n  /**\r\n   * Add feature importance data\r\n   */\r\n  addFeatureImportance(features: Array<{ feature: string; importance: number; value: any }>): void {\r\n    this.featureImportance = features.sort((a, b) => b.importance - a.importance);\r\n  }\r\n\r\n  /**\r\n   * Add explanation data\r\n   */\r\n  addExplanation(explanation: Prediction['explanation']): void {\r\n    this.explanation = explanation;\r\n  }\r\n\r\n  /**\r\n   * Add quality metrics\r\n   */\r\n  addQualityMetrics(metrics: Prediction['qualityMetrics']): void {\r\n    this.qualityMetrics = metrics;\r\n  }\r\n\r\n  /**\r\n   * Get uncertainty level\r\n   */\r\n  get uncertaintyLevel(): 'low' | 'medium' | 'high' {\r\n    if (this.confidence >= 0.8) return 'low';\r\n    if (this.confidence >= 0.5) return 'medium';\r\n    return 'high';\r\n  }\r\n\r\n  /**\r\n   * Get risk level based on prediction type and confidence\r\n   */\r\n  get riskLevel(): 'low' | 'medium' | 'high' | 'critical' {\r\n    if (this.type === 'vulnerability_severity') {\r\n      const severity = this.value;\r\n      if (severity === 'critical' && this.confidence >= 0.7) return 'critical';\r\n      if (severity === 'high' && this.confidence >= 0.6) return 'high';\r\n      if (severity === 'medium' && this.confidence >= 0.5) return 'medium';\r\n      return 'low';\r\n    }\r\n\r\n    if (this.type === 'risk_score') {\r\n      const score = this.value;\r\n      if (score >= 8 && this.confidence >= 0.7) return 'critical';\r\n      if (score >= 6 && this.confidence >= 0.6) return 'high';\r\n      if (score >= 4 && this.confidence >= 0.5) return 'medium';\r\n      return 'low';\r\n    }\r\n\r\n    // Default risk assessment based on confidence\r\n    if (this.confidence >= 0.8) return 'high';\r\n    if (this.confidence >= 0.6) return 'medium';\r\n    return 'low';\r\n  }\r\n}\r\n"], "version": 3}