{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\events\\analysis-result-updated.domain-event.ts", "mappings": ";;;AAAA,0FAAqF;AAGrF;;;;;;GAMG;AACH,MAAa,0BAA2B,SAAQ,mCAAe;IAC7D,YACkB,gBAAgC,EAChC,SAAiB,EACjB,UAAkB,EAClC,OAAwB,EACxB,UAAiB;QAEjB,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QANX,qBAAgB,GAAhB,gBAAgB,CAAgB;QAChC,cAAS,GAAT,SAAS,CAAQ;QACjB,eAAU,GAAV,UAAU,CAAQ;IAKpC,CAAC;IAEM,YAAY;QACjB,OAAO,uBAAuB,CAAC;IACjC,CAAC;IAEM,eAAe;QACpB,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,YAAY;QACjB,OAAO;YACL,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE;YAClD,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,UAAU,EAAE,IAAI,CAAC,UAAU;SAC5B,CAAC;IACJ,CAAC;CACF;AA1BD,gEA0BC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\events\\analysis-result-updated.domain-event.ts"], "sourcesContent": ["import { BaseDomainEvent } from '../../../../shared-kernel/domain/base-domain-event';\r\nimport { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\n\r\n/**\r\n * Analysis Result Updated Domain Event\r\n * \r\n * Published when an analysis result is updated.\r\n * This event can trigger various downstream processes such as\r\n * cache invalidation, index updates, and change tracking.\r\n */\r\nexport class AnalysisResultUpdatedEvent extends BaseDomainEvent {\r\n  constructor(\r\n    public readonly analysisResultId: UniqueEntityId,\r\n    public readonly requestId: string,\r\n    public readonly updateType: string,\r\n    eventId?: UniqueEntityId,\r\n    occurredOn?: Date\r\n  ) {\r\n    super(eventId, occurredOn);\r\n  }\r\n\r\n  public getEventName(): string {\r\n    return 'AnalysisResultUpdated';\r\n  }\r\n\r\n  public getEventVersion(): string {\r\n    return '1.0';\r\n  }\r\n\r\n  public getEventData(): Record<string, any> {\r\n    return {\r\n      analysisResultId: this.analysisResultId.toString(),\r\n      requestId: this.requestId,\r\n      updateType: this.updateType,\r\n    };\r\n  }\r\n}"], "version": 3}