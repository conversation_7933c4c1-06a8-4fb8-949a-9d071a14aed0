{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\aggregates\\__tests__\\analysis-result.aggregate.spec.ts", "mappings": ";;AAAA,4HAA0G;AAC1G,kFAAqF;AACrF,qFAAgF;AAChF,4EAA4F;AAE5F,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;IACvC,IAAI,SAAkC,CAAC;IAEvC,UAAU,CAAC,GAAG,EAAE;QACd,SAAS,GAAG,IAAI,mDAAuB,EAAE,CAAC;IAC5C,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,OAAO,GAAG;gBACd,SAAS,EAAE,kBAAkB;gBAC7B,OAAO,EAAE,8CAAc,CAAC,QAAQ,EAAE;gBAClC,YAAY,EAAE,qCAAY,CAAC,cAAc;gBACzC,SAAS,EAAE;oBACT,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;oBACvB,MAAM,EAAE,MAAM;oBACd,IAAI,EAAE,GAAG;oBACT,QAAQ,EAAE,UAAU;oBACpB,oBAAoB,EAAE,EAAE;oBACxB,eAAe,EAAE,EAAE;iBACpB;aACF,CAAC;YAEF,MAAM,MAAM,GAAG,SAAS,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAEvD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAClD,MAAM,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1D,MAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;YAC/D,MAAM,OAAO,GAAG;gBACd,SAAS,EAAE,mBAAmB;gBAC9B,OAAO,EAAE,8CAAc,CAAC,QAAQ,EAAE;gBAClC,YAAY,EAAE,qCAAY,CAAC,cAAc;gBACzC,SAAS,EAAE;oBACT,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;oBACvB,MAAM,EAAE,MAAM;oBACd,IAAI,EAAE,GAAG;oBACT,QAAQ,EAAE,UAAU;oBACpB,oBAAoB,EAAE,EAAE;oBACxB,eAAe,EAAE,EAAE;iBACpB;aACF,CAAC;YAEF,SAAS,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAExC,MAAM,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAC3D,oEAAoE,CACrE,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,MAAM,GAAG,+CAAqB,CAAC,gBAAgB,EAAE,CAAC;YAExD,SAAS,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAEpC,MAAM,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1D,MAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,MAAM,GAAG,+CAAqB,CAAC,gBAAgB,EAAE,CAAC;YAExD,SAAS,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAEpC,MAAM,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CACvD,4BAA4B,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,+BAA+B,CAChF,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,MAAM,GAAG,+CAAqB,CAAC,gBAAgB,EAAE,CAAC;YACxD,SAAS,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAEpC,SAAS,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAE1C,MAAM,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,uCAAc,CAAC,SAAS,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,MAAM,aAAa,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YAEhD,MAAM,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CACjE,4BAA4B,aAAa,CAAC,QAAQ,EAAE,aAAa,CAClE,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,UAAU,CAAC,GAAG,EAAE;YACd,+BAA+B;YAC/B,MAAM,OAAO,GAAG;gBACd,+CAAqB,CAAC,gBAAgB,CAAC;oBACrC,SAAS,EAAE,WAAW;oBACtB,YAAY,EAAE,qCAAY,CAAC,cAAc;oBACzC,IAAI,EAAE,CAAC,MAAM,EAAE,gBAAgB,CAAC;iBACjC,CAAC;gBACF,+CAAqB,CAAC,gBAAgB,CAAC;oBACrC,SAAS,EAAE,WAAW;oBACtB,YAAY,EAAE,qCAAY,CAAC,iBAAiB;oBAC5C,IAAI,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;iBAC1B,CAAC;gBACF,+CAAqB,CAAC,yBAAyB,CAAC;oBAC9C,SAAS,EAAE,WAAW;oBACtB,YAAY,EAAE,qCAAY,CAAC,cAAc;oBACzC,IAAI,EAAE,CAAC,WAAW,EAAE,gBAAgB,CAAC;iBACtC,CAAC;aACH,CAAC;YAEF,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,OAAO,GAAG,SAAS,CAAC,qBAAqB,EAAE,CAAC;YAElD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,KAAK,GAAwB;gBACjC,SAAS,EAAE,WAAW;aACvB,CAAC;YAEF,MAAM,OAAO,GAAG,SAAS,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAErD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,KAAK,GAAwB;gBACjC,YAAY,EAAE,qCAAY,CAAC,cAAc;aAC1C,CAAC;YAEF,MAAM,OAAO,GAAG,SAAS,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAErD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,KAAK,qCAAY,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,KAAK,GAAwB;gBACjC,MAAM,EAAE,uCAAc,CAAC,SAAS;aACjC,CAAC;YAEF,MAAM,OAAO,GAAG,SAAS,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAErD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,uCAAc,CAAC,SAAS,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,KAAK,GAAwB;gBACjC,IAAI,EAAE,CAAC,gBAAgB,CAAC;aACzB,CAAC;YAEF,MAAM,OAAO,GAAG,SAAS,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAErD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,KAAK,GAAwB;gBACjC,aAAa,EAAE,GAAG;aACnB,CAAC;YAEF,MAAM,OAAO,GAAG,SAAS,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAErD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,6CAA6C;YAC9E,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,KAAK,GAAwB;gBACjC,YAAY,EAAE,qCAAY,CAAC,cAAc;gBACzC,MAAM,EAAE,uCAAc,CAAC,SAAS;aACjC,CAAC;YAEF,MAAM,OAAO,GAAG,SAAS,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAErD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,qCAAY,CAAC,cAAc,CAAC,CAAC;YAClE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,uCAAc,CAAC,SAAS,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,MAAM,GAAG,SAAS,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAEtD,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7B,MAAM,CAAC,MAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,MAAM,GAAG,SAAS,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;YAEzD,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,CAAC;QACjC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,OAAO,GAAG;gBACd,+CAAqB,CAAC,gBAAgB,CAAC,EAAE,SAAS,EAAE,WAAW,EAAE,CAAC;gBAClE,+CAAqB,CAAC,gBAAgB,CAAC,EAAE,SAAS,EAAE,WAAW,EAAE,CAAC;gBAClE,+CAAqB,CAAC,yBAAyB,CAAC,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC;gBAC7E,+CAAqB,CAAC,sBAAsB,CAAC,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC;aACxE,CAAC;YAEF,6BAA6B;YAC7B,MAAM,gBAAgB,GAAG,+CAAqB,CAAC,gBAAgB,CAAC,EAAE,SAAS,EAAE,cAAc,EAAE,CAAC,CAAC;YAC/F,gBAAgB,CAAC,eAAe,EAAE,CAAC;YACnC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAE/B,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,OAAO,GAAG,SAAS,CAAC,iBAAiB,EAAE,CAAC;YAE9C,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,uCAAc,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,OAAO,GAAG,SAAS,CAAC,oBAAoB,EAAE,CAAC;YAEjD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,uCAAc,CAAC,UAAU,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,OAAO,GAAG,SAAS,CAAC,mBAAmB,EAAE,CAAC;YAEhD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,uCAAc,CAAC,SAAS,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,OAAO,GAAG,SAAS,CAAC,gBAAgB,EAAE,CAAC;YAE7C,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,uCAAc,CAAC,MAAM,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,OAAO,GAAG,SAAS,CAAC,yBAAyB,EAAE,CAAC;YAEtD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,OAAO,GAAG,SAAS,CAAC,wBAAwB,EAAE,CAAC;YAErD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,OAAO,GAAG;gBACd,+CAAqB,CAAC,gBAAgB,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC;gBAC/D,+CAAqB,CAAC,gBAAgB,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC;gBAC/D,+CAAqB,CAAC,yBAAyB,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC;aACzE,CAAC;YAEF,6BAA6B;YAC7B,MAAM,gBAAgB,GAAG,+CAAqB,CAAC,gBAAgB,CAAC,EAAE,SAAS,EAAE,iBAAiB,EAAE,CAAC,CAAC;YAClG,gBAAgB,CAAC,eAAe,EAAE,CAAC;YACnC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAE/B,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,gBAAgB,GAAG,SAAS,CAAC,sBAAsB,EAAE,CAAC;YAE5D,MAAM,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,2BAA2B;YACrE,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,uCAAc,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,KAAK,GAAwB;gBACjC,MAAM,EAAE,uCAAc,CAAC,OAAO;aAC/B,CAAC;YAEF,MAAM,gBAAgB,GAAG,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAExD,MAAM,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,uCAAc,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,OAAO,GAAG;gBACd,+CAAqB,CAAC,gBAAgB,CAAC;oBACrC,SAAS,EAAE,SAAS;oBACpB,YAAY,EAAE,qCAAY,CAAC,cAAc;iBAC1C,CAAC;gBACF,+CAAqB,CAAC,gBAAgB,CAAC;oBACrC,SAAS,EAAE,SAAS;oBACpB,YAAY,EAAE,qCAAY,CAAC,iBAAiB;iBAC7C,CAAC;gBACF,+CAAqB,CAAC,yBAAyB,CAAC;oBAC9C,SAAS,EAAE,SAAS;oBACpB,YAAY,EAAE,qCAAY,CAAC,cAAc;iBAC1C,CAAC;gBACF,+CAAqB,CAAC,sBAAsB,CAAC;oBAC3C,SAAS,EAAE,SAAS;oBACpB,YAAY,EAAE,qCAAY,CAAC,cAAc;iBAC1C,CAAC;aACH,CAAC;YAEF,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,KAAK,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;YAExC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,uCAAc,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACjE,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,uCAAc,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACnE,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,uCAAc,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChE,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,qCAAY,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpE,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,qCAAY,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACvE,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,wBAAwB;YAC9D,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,sBAAsB;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,cAAc,GAAG,IAAI,mDAAuB,EAAE,CAAC;YACrD,MAAM,KAAK,GAAG,cAAc,CAAC,aAAa,EAAE,CAAC;YAE7C,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,QAAQ,GAAG;gBACf;oBACE,SAAS,EAAE,SAAS;oBACpB,OAAO,EAAE,8CAAc,CAAC,QAAQ,EAAE;oBAClC,YAAY,EAAE,qCAAY,CAAC,cAAc;oBACzC,SAAS,EAAE;wBACT,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;wBACvB,MAAM,EAAE,MAAM;wBACd,IAAI,EAAE,GAAG;wBACT,QAAQ,EAAE,UAAU;wBACpB,oBAAoB,EAAE,EAAE;wBACxB,eAAe,EAAE,EAAE;qBACpB;iBACF;gBACD;oBACE,SAAS,EAAE,SAAS;oBACpB,OAAO,EAAE,8CAAc,CAAC,QAAQ,EAAE;oBAClC,YAAY,EAAE,qCAAY,CAAC,iBAAiB;oBAC5C,SAAS,EAAE;wBACT,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;wBACvB,MAAM,EAAE,MAAM;wBACd,IAAI,EAAE,GAAG;wBACT,QAAQ,EAAE,UAAU;wBACpB,oBAAoB,EAAE,EAAE;wBACxB,eAAe,EAAE,EAAE;qBACpB;iBACF;aACF,CAAC;YAEF,MAAM,KAAK,GAAG,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAE9C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACjC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,iDAAiD;YACjD,SAAS,CAAC,oBAAoB,CAAC;gBAC7B,SAAS,EAAE,WAAW;gBACtB,OAAO,EAAE,8CAAc,CAAC,QAAQ,EAAE;gBAClC,YAAY,EAAE,qCAAY,CAAC,cAAc;gBACzC,SAAS,EAAE;oBACT,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;oBACvB,MAAM,EAAE,MAAM;oBACd,IAAI,EAAE,GAAG;oBACT,QAAQ,EAAE,UAAU;oBACpB,oBAAoB,EAAE,EAAE;oBACxB,eAAe,EAAE,EAAE;iBACpB;aACF,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG;gBACf;oBACE,SAAS,EAAE,WAAW,EAAE,iBAAiB;oBACzC,OAAO,EAAE,8CAAc,CAAC,QAAQ,EAAE;oBAClC,YAAY,EAAE,qCAAY,CAAC,cAAc;oBACzC,SAAS,EAAE;wBACT,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;wBACvB,MAAM,EAAE,MAAM;wBACd,IAAI,EAAE,GAAG;wBACT,QAAQ,EAAE,UAAU;wBACpB,oBAAoB,EAAE,EAAE;wBACxB,eAAe,EAAE,EAAE;qBACpB;iBACF;gBACD;oBACE,SAAS,EAAE,OAAO;oBAClB,OAAO,EAAE,8CAAc,CAAC,QAAQ,EAAE;oBAClC,YAAY,EAAE,qCAAY,CAAC,iBAAiB;oBAC5C,SAAS,EAAE;wBACT,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;wBACvB,MAAM,EAAE,MAAM;wBACd,IAAI,EAAE,GAAG;wBACT,QAAQ,EAAE,UAAU;wBACpB,oBAAoB,EAAE,EAAE;wBACxB,eAAe,EAAE,EAAE;qBACpB;iBACF;aACF,CAAC;YAEF,MAAM,KAAK,GAAG,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAE9C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB;YAC5D,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,OAAO,GAAG;gBACd,+CAAqB,CAAC,gBAAgB,CAAC;oBACrC,SAAS,EAAE,SAAS;oBACpB,YAAY,EAAE,qCAAY,CAAC,cAAc;iBAC1C,CAAC;gBACF,+CAAqB,CAAC,gBAAgB,CAAC;oBACrC,SAAS,EAAE,SAAS;oBACpB,YAAY,EAAE,qCAAY,CAAC,cAAc;iBAC1C,CAAC;gBACF,+CAAqB,CAAC,yBAAyB,CAAC;oBAC9C,SAAS,EAAE,SAAS;oBACpB,YAAY,EAAE,qCAAY,CAAC,iBAAiB;iBAC7C,CAAC;aACH,CAAC;YAEF,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,MAAM,GAAG,SAAS,CAAC,mBAAmB,EAAE,CAAC;YAE/C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC5B,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,qCAAY,CAAC,cAAc,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChE,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,qCAAY,CAAC,iBAAiB,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wBAAwB,EAAE,GAAG,EAAE;YAChC,MAAM,MAAM,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;YAEzC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC5B,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,uCAAc,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,uCAAc,CAAC,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,OAAO,GAAG,SAAS,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;YAElD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,gEAAgE;YAChE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;QAChG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,OAAO,GAAG,SAAS,CAAC,sBAAsB,EAAE,CAAC;YAEnD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,iDAAiD;YACjD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC5C,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;YAClF,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,OAAO,GAAG,SAAS,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;YAEjD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,oDAAoD;YACpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC5C,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;YAChG,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,aAAa,GAAG,kBAAkB,CAAC;YACzC,MAAM,OAAO,GAAG;gBACd,+CAAqB,CAAC,gBAAgB,CAAC;oBACrC,SAAS,EAAE,QAAQ;oBACnB,aAAa;iBACd,CAAC;gBACF,+CAAqB,CAAC,gBAAgB,CAAC;oBACrC,SAAS,EAAE,QAAQ;oBACnB,aAAa;iBACd,CAAC;gBACF,+CAAqB,CAAC,gBAAgB,CAAC;oBACrC,SAAS,EAAE,QAAQ;oBACnB,aAAa,EAAE,WAAW;iBAC3B,CAAC;aACH,CAAC;YAEF,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;YAE/D,MAAM,iBAAiB,GAAG,SAAS,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;YAEvE,MAAM,CAAC,iBAAiB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,KAAK,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,QAAQ,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YAC3C,MAAM,MAAM,GAAG,+CAAqB,CAAC,gBAAgB,CAAC;gBACpD,SAAS,EAAE,QAAQ;aACpB,CAAC,CAAC;YACH,MAAM,MAAM,GAAG,+CAAqB,CAAC,gBAAgB,CAAC;gBACpD,SAAS,EAAE,SAAS;gBACpB,gBAAgB,EAAE,QAAQ;aAC3B,CAAC,CAAC;YACH,MAAM,MAAM,GAAG,+CAAqB,CAAC,gBAAgB,CAAC;gBACpD,SAAS,EAAE,SAAS;gBACpB,gBAAgB,EAAE,QAAQ;aAC3B,CAAC,CAAC;YAEH,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;YAEhF,MAAM,YAAY,GAAG,SAAS,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YAEzD,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,gBAAgB,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,OAAO,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YAC1C,MAAM,OAAO,GAAG;gBACd,+CAAqB,CAAC,gBAAgB,CAAC;oBACrC,SAAS,EAAE,SAAS;oBACpB,OAAO;iBACR,CAAC;gBACF,+CAAqB,CAAC,gBAAgB,CAAC;oBACrC,SAAS,EAAE,SAAS;oBACpB,OAAO;iBACR,CAAC;gBACF,+CAAqB,CAAC,gBAAgB,CAAC;oBACrC,SAAS,EAAE,SAAS;iBACrB,CAAC;aACH,CAAC;YAEF,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;YAE/D,MAAM,YAAY,GAAG,SAAS,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAE1D,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\aggregates\\__tests__\\analysis-result.aggregate.spec.ts"], "sourcesContent": ["import { UniqueEntityId } from '../../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { AnalysisType, AnalysisStatus } from '../../entities/analysis-result.entity';\r\nimport { AnalysisResultFactory } from '../../factories/analysis-result.factory';\r\nimport { AnalysisResultAggregate, AnalysisResultQuery } from '../analysis-result.aggregate';\r\n\r\ndescribe('AnalysisResultAggregate', () => {\r\n  let aggregate: AnalysisResultAggregate;\r\n\r\n  beforeEach(() => {\r\n    aggregate = new AnalysisResultAggregate();\r\n  });\r\n\r\n  describe('Analysis Result Management', () => {\r\n    it('should create and add a new analysis result', () => {\r\n      const request = {\r\n        requestId: 'test-request-123',\r\n        modelId: UniqueEntityId.generate(),\r\n        analysisType: AnalysisType.CLASSIFICATION,\r\n        inputData: {\r\n          data: { test: 'input' },\r\n          format: 'json',\r\n          size: 100,\r\n          checksum: 'checksum',\r\n          preprocessingApplied: [],\r\n          validationRules: [],\r\n        },\r\n      };\r\n\r\n      const result = aggregate.createAnalysisResult(request);\r\n\r\n      expect(result.requestId).toBe('test-request-123');\r\n      expect(aggregate.getAllAnalysisResults()).toHaveLength(1);\r\n      expect(aggregate.getAnalysisResult(result.id)).toBe(result);\r\n    });\r\n\r\n    it('should throw error when creating duplicate request ID', () => {\r\n      const request = {\r\n        requestId: 'duplicate-request',\r\n        modelId: UniqueEntityId.generate(),\r\n        analysisType: AnalysisType.CLASSIFICATION,\r\n        inputData: {\r\n          data: { test: 'input' },\r\n          format: 'json',\r\n          size: 100,\r\n          checksum: 'checksum',\r\n          preprocessingApplied: [],\r\n          validationRules: [],\r\n        },\r\n      };\r\n\r\n      aggregate.createAnalysisResult(request);\r\n\r\n      expect(() => aggregate.createAnalysisResult(request)).toThrow(\r\n        \"Analysis result with request ID 'duplicate-request' already exists\"\r\n      );\r\n    });\r\n\r\n    it('should add existing analysis result', () => {\r\n      const result = AnalysisResultFactory.createForTesting();\r\n\r\n      aggregate.addAnalysisResult(result);\r\n\r\n      expect(aggregate.getAllAnalysisResults()).toHaveLength(1);\r\n      expect(aggregate.getAnalysisResult(result.id)).toBe(result);\r\n    });\r\n\r\n    it('should throw error when adding duplicate analysis result', () => {\r\n      const result = AnalysisResultFactory.createForTesting();\r\n\r\n      aggregate.addAnalysisResult(result);\r\n\r\n      expect(() => aggregate.addAnalysisResult(result)).toThrow(\r\n        `Analysis result with ID '${result.id.toString()}' already exists in aggregate`\r\n      );\r\n    });\r\n\r\n    it('should remove analysis result', () => {\r\n      const result = AnalysisResultFactory.createForTesting();\r\n      aggregate.addAnalysisResult(result);\r\n\r\n      aggregate.removeAnalysisResult(result.id);\r\n\r\n      expect(aggregate.getAllAnalysisResults()).toHaveLength(0);\r\n      expect(result.status).toBe(AnalysisStatus.CANCELLED);\r\n    });\r\n\r\n    it('should throw error when removing non-existent analysis result', () => {\r\n      const nonExistentId = UniqueEntityId.generate();\r\n\r\n      expect(() => aggregate.removeAnalysisResult(nonExistentId)).toThrow(\r\n        `Analysis result with ID '${nonExistentId.toString()}' not found`\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('Analysis Result Querying', () => {\r\n    beforeEach(() => {\r\n      // Create test analysis results\r\n      const results = [\r\n        AnalysisResultFactory.createForTesting({\r\n          requestId: 'request-1',\r\n          analysisType: AnalysisType.CLASSIFICATION,\r\n          tags: ['test', 'classification'],\r\n        }),\r\n        AnalysisResultFactory.createForTesting({\r\n          requestId: 'request-2',\r\n          analysisType: AnalysisType.ANOMALY_DETECTION,\r\n          tags: ['test', 'anomaly'],\r\n        }),\r\n        AnalysisResultFactory.createCompletedForTesting({\r\n          requestId: 'request-3',\r\n          analysisType: AnalysisType.CLASSIFICATION,\r\n          tags: ['completed', 'classification'],\r\n        }),\r\n      ];\r\n\r\n      results.forEach(result => aggregate.addAnalysisResult(result));\r\n    });\r\n\r\n    it('should get all analysis results', () => {\r\n      const results = aggregate.getAllAnalysisResults();\r\n\r\n      expect(results).toHaveLength(3);\r\n    });\r\n\r\n    it('should find analysis results by request ID', () => {\r\n      const query: AnalysisResultQuery = {\r\n        requestId: 'request-1',\r\n      };\r\n\r\n      const results = aggregate.findAnalysisResults(query);\r\n\r\n      expect(results).toHaveLength(1);\r\n      expect(results[0].requestId).toBe('request-1');\r\n    });\r\n\r\n    it('should find analysis results by analysis type', () => {\r\n      const query: AnalysisResultQuery = {\r\n        analysisType: AnalysisType.CLASSIFICATION,\r\n      };\r\n\r\n      const results = aggregate.findAnalysisResults(query);\r\n\r\n      expect(results).toHaveLength(2);\r\n      expect(results.every(r => r.analysisType === AnalysisType.CLASSIFICATION)).toBe(true);\r\n    });\r\n\r\n    it('should find analysis results by status', () => {\r\n      const query: AnalysisResultQuery = {\r\n        status: AnalysisStatus.COMPLETED,\r\n      };\r\n\r\n      const results = aggregate.findAnalysisResults(query);\r\n\r\n      expect(results).toHaveLength(1);\r\n      expect(results[0].status).toBe(AnalysisStatus.COMPLETED);\r\n    });\r\n\r\n    it('should find analysis results by tags', () => {\r\n      const query: AnalysisResultQuery = {\r\n        tags: ['classification'],\r\n      };\r\n\r\n      const results = aggregate.findAnalysisResults(query);\r\n\r\n      expect(results).toHaveLength(2);\r\n      expect(results.every(r => r.hasTag('classification'))).toBe(true);\r\n    });\r\n\r\n    it('should find analysis results by confidence range', () => {\r\n      const query: AnalysisResultQuery = {\r\n        minConfidence: 0.9,\r\n      };\r\n\r\n      const results = aggregate.findAnalysisResults(query);\r\n\r\n      expect(results).toHaveLength(1); // Only the completed one has high confidence\r\n      expect(results[0].confidence).toBeGreaterThanOrEqual(0.9);\r\n    });\r\n\r\n    it('should find analysis results by multiple criteria', () => {\r\n      const query: AnalysisResultQuery = {\r\n        analysisType: AnalysisType.CLASSIFICATION,\r\n        status: AnalysisStatus.COMPLETED,\r\n      };\r\n\r\n      const results = aggregate.findAnalysisResults(query);\r\n\r\n      expect(results).toHaveLength(1);\r\n      expect(results[0].analysisType).toBe(AnalysisType.CLASSIFICATION);\r\n      expect(results[0].status).toBe(AnalysisStatus.COMPLETED);\r\n    });\r\n\r\n    it('should find by request ID', () => {\r\n      const result = aggregate.findByRequestId('request-1');\r\n\r\n      expect(result).toBeDefined();\r\n      expect(result!.requestId).toBe('request-1');\r\n    });\r\n\r\n    it('should return undefined for non-existent request ID', () => {\r\n      const result = aggregate.findByRequestId('non-existent');\r\n\r\n      expect(result).toBeUndefined();\r\n    });\r\n  });\r\n\r\n  describe('Status-based Queries', () => {\r\n    beforeEach(() => {\r\n      const results = [\r\n        AnalysisResultFactory.createForTesting({ requestId: 'pending-1' }),\r\n        AnalysisResultFactory.createForTesting({ requestId: 'pending-2' }),\r\n        AnalysisResultFactory.createCompletedForTesting({ requestId: 'completed-1' }),\r\n        AnalysisResultFactory.createFailedForTesting({ requestId: 'failed-1' }),\r\n      ];\r\n\r\n      // Create a processing result\r\n      const processingResult = AnalysisResultFactory.createForTesting({ requestId: 'processing-1' });\r\n      processingResult.startProcessing();\r\n      results.push(processingResult);\r\n\r\n      results.forEach(result => aggregate.addAnalysisResult(result));\r\n    });\r\n\r\n    it('should get pending results', () => {\r\n      const results = aggregate.getPendingResults();\r\n\r\n      expect(results).toHaveLength(2);\r\n      expect(results.every(r => r.status === AnalysisStatus.PENDING)).toBe(true);\r\n    });\r\n\r\n    it('should get processing results', () => {\r\n      const results = aggregate.getProcessingResults();\r\n\r\n      expect(results).toHaveLength(1);\r\n      expect(results[0].status).toBe(AnalysisStatus.PROCESSING);\r\n    });\r\n\r\n    it('should get completed results', () => {\r\n      const results = aggregate.getCompletedResults();\r\n\r\n      expect(results).toHaveLength(1);\r\n      expect(results[0].status).toBe(AnalysisStatus.COMPLETED);\r\n    });\r\n\r\n    it('should get failed results', () => {\r\n      const results = aggregate.getFailedResults();\r\n\r\n      expect(results).toHaveLength(1);\r\n      expect(results[0].status).toBe(AnalysisStatus.FAILED);\r\n    });\r\n\r\n    it('should get retryable failed results', () => {\r\n      const results = aggregate.getRetryableFailedResults();\r\n\r\n      expect(results).toHaveLength(1);\r\n      expect(results[0].isRetryable()).toBe(true);\r\n    });\r\n\r\n    it('should get high confidence results', () => {\r\n      const results = aggregate.getHighConfidenceResults();\r\n\r\n      expect(results).toHaveLength(1);\r\n      expect(results[0].hasHighConfidence()).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('Bulk Operations', () => {\r\n    beforeEach(() => {\r\n      const results = [\r\n        AnalysisResultFactory.createForTesting({ requestId: 'bulk-1' }),\r\n        AnalysisResultFactory.createForTesting({ requestId: 'bulk-2' }),\r\n        AnalysisResultFactory.createCompletedForTesting({ requestId: 'bulk-3' }),\r\n      ];\r\n\r\n      // Create a processing result\r\n      const processingResult = AnalysisResultFactory.createForTesting({ requestId: 'bulk-processing' });\r\n      processingResult.startProcessing();\r\n      results.push(processingResult);\r\n\r\n      results.forEach(result => aggregate.addAnalysisResult(result));\r\n    });\r\n\r\n    it('should cancel all active results', () => {\r\n      const cancelledResults = aggregate.cancelAllActiveResults();\r\n\r\n      expect(cancelledResults).toHaveLength(3); // 2 pending + 1 processing\r\n      expect(cancelledResults.every(r => r.status === AnalysisStatus.CANCELLED)).toBe(true);\r\n    });\r\n\r\n    it('should cancel results by query criteria', () => {\r\n      const query: AnalysisResultQuery = {\r\n        status: AnalysisStatus.PENDING,\r\n      };\r\n\r\n      const cancelledResults = aggregate.cancelResults(query);\r\n\r\n      expect(cancelledResults).toHaveLength(2);\r\n      expect(cancelledResults.every(r => r.status === AnalysisStatus.CANCELLED)).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('Statistics', () => {\r\n    beforeEach(() => {\r\n      const results = [\r\n        AnalysisResultFactory.createForTesting({ \r\n          requestId: 'stats-1',\r\n          analysisType: AnalysisType.CLASSIFICATION \r\n        }),\r\n        AnalysisResultFactory.createForTesting({ \r\n          requestId: 'stats-2',\r\n          analysisType: AnalysisType.ANOMALY_DETECTION \r\n        }),\r\n        AnalysisResultFactory.createCompletedForTesting({ \r\n          requestId: 'stats-3',\r\n          analysisType: AnalysisType.CLASSIFICATION \r\n        }),\r\n        AnalysisResultFactory.createFailedForTesting({ \r\n          requestId: 'stats-4',\r\n          analysisType: AnalysisType.CLASSIFICATION \r\n        }),\r\n      ];\r\n\r\n      results.forEach(result => aggregate.addAnalysisResult(result));\r\n    });\r\n\r\n    it('should calculate aggregate statistics', () => {\r\n      const stats = aggregate.getStatistics();\r\n\r\n      expect(stats.totalResults).toBe(4);\r\n      expect(stats.statusDistribution[AnalysisStatus.PENDING]).toBe(2);\r\n      expect(stats.statusDistribution[AnalysisStatus.COMPLETED]).toBe(1);\r\n      expect(stats.statusDistribution[AnalysisStatus.FAILED]).toBe(1);\r\n      expect(stats.typeDistribution[AnalysisType.CLASSIFICATION]).toBe(3);\r\n      expect(stats.typeDistribution[AnalysisType.ANOMALY_DETECTION]).toBe(1);\r\n      expect(stats.successRate).toBe(0.25); // 1 successful out of 4\r\n      expect(stats.retryableFailures).toBe(1);\r\n      expect(stats.completionRate).toBe(0.5); // 2 terminal out of 4\r\n    });\r\n\r\n    it('should return empty statistics for empty aggregate', () => {\r\n      const emptyAggregate = new AnalysisResultAggregate();\r\n      const stats = emptyAggregate.getStatistics();\r\n\r\n      expect(stats.totalResults).toBe(0);\r\n      expect(stats.averageConfidence).toBe(0);\r\n      expect(stats.successRate).toBe(0);\r\n    });\r\n  });\r\n\r\n  describe('Batch Operations', () => {\r\n    it('should create batch of analysis results', () => {\r\n      const requests = [\r\n        {\r\n          requestId: 'batch-1',\r\n          modelId: UniqueEntityId.generate(),\r\n          analysisType: AnalysisType.CLASSIFICATION,\r\n          inputData: {\r\n            data: { test: 'input' },\r\n            format: 'json',\r\n            size: 100,\r\n            checksum: 'checksum',\r\n            preprocessingApplied: [],\r\n            validationRules: [],\r\n          },\r\n        },\r\n        {\r\n          requestId: 'batch-2',\r\n          modelId: UniqueEntityId.generate(),\r\n          analysisType: AnalysisType.ANOMALY_DETECTION,\r\n          inputData: {\r\n            data: { test: 'input' },\r\n            format: 'json',\r\n            size: 100,\r\n            checksum: 'checksum',\r\n            preprocessingApplied: [],\r\n            validationRules: [],\r\n          },\r\n        },\r\n      ];\r\n\r\n      const batch = aggregate.createBatch(requests);\r\n\r\n      expect(batch.results).toHaveLength(2);\r\n      expect(batch.totalCount).toBe(2);\r\n      expect(batch.pendingCount).toBe(2);\r\n      expect(batch.successCount).toBe(0);\r\n      expect(batch.failureCount).toBe(0);\r\n    });\r\n\r\n    it('should handle errors in batch creation', () => {\r\n      // First create a result to cause duplicate error\r\n      aggregate.createAnalysisResult({\r\n        requestId: 'duplicate',\r\n        modelId: UniqueEntityId.generate(),\r\n        analysisType: AnalysisType.CLASSIFICATION,\r\n        inputData: {\r\n          data: { test: 'input' },\r\n          format: 'json',\r\n          size: 100,\r\n          checksum: 'checksum',\r\n          preprocessingApplied: [],\r\n          validationRules: [],\r\n        },\r\n      });\r\n\r\n      const requests = [\r\n        {\r\n          requestId: 'duplicate', // This will fail\r\n          modelId: UniqueEntityId.generate(),\r\n          analysisType: AnalysisType.CLASSIFICATION,\r\n          inputData: {\r\n            data: { test: 'input' },\r\n            format: 'json',\r\n            size: 100,\r\n            checksum: 'checksum',\r\n            preprocessingApplied: [],\r\n            validationRules: [],\r\n          },\r\n        },\r\n        {\r\n          requestId: 'valid',\r\n          modelId: UniqueEntityId.generate(),\r\n          analysisType: AnalysisType.ANOMALY_DETECTION,\r\n          inputData: {\r\n            data: { test: 'input' },\r\n            format: 'json',\r\n            size: 100,\r\n            checksum: 'checksum',\r\n            preprocessingApplied: [],\r\n            validationRules: [],\r\n          },\r\n        },\r\n      ];\r\n\r\n      const batch = aggregate.createBatch(requests);\r\n\r\n      expect(batch.results).toHaveLength(1); // Only the valid one\r\n      expect(batch.totalCount).toBe(1);\r\n    });\r\n  });\r\n\r\n  describe('Grouping and Sorting', () => {\r\n    beforeEach(() => {\r\n      const results = [\r\n        AnalysisResultFactory.createForTesting({ \r\n          requestId: 'group-1',\r\n          analysisType: AnalysisType.CLASSIFICATION \r\n        }),\r\n        AnalysisResultFactory.createForTesting({ \r\n          requestId: 'group-2',\r\n          analysisType: AnalysisType.CLASSIFICATION \r\n        }),\r\n        AnalysisResultFactory.createCompletedForTesting({ \r\n          requestId: 'group-3',\r\n          analysisType: AnalysisType.ANOMALY_DETECTION \r\n        }),\r\n      ];\r\n\r\n      results.forEach(result => aggregate.addAnalysisResult(result));\r\n    });\r\n\r\n    it('should group by analysis type', () => {\r\n      const groups = aggregate.groupByAnalysisType();\r\n\r\n      expect(groups.size).toBe(2);\r\n      expect(groups.get(AnalysisType.CLASSIFICATION)).toHaveLength(2);\r\n      expect(groups.get(AnalysisType.ANOMALY_DETECTION)).toHaveLength(1);\r\n    });\r\n\r\n    it('should group by status', () => {\r\n      const groups = aggregate.groupByStatus();\r\n\r\n      expect(groups.size).toBe(2);\r\n      expect(groups.get(AnalysisStatus.PENDING)).toHaveLength(2);\r\n      expect(groups.get(AnalysisStatus.COMPLETED)).toHaveLength(1);\r\n    });\r\n\r\n    it('should get most recent results', () => {\r\n      const results = aggregate.getMostRecentResults(2);\r\n\r\n      expect(results).toHaveLength(2);\r\n      // Results should be sorted by creation date (most recent first)\r\n      expect(results[0].createdAt.getTime()).toBeGreaterThanOrEqual(results[1].createdAt.getTime());\r\n    });\r\n\r\n    it('should get results by confidence', () => {\r\n      const results = aggregate.getResultsByConfidence();\r\n\r\n      expect(results).toHaveLength(3);\r\n      // Should be sorted by confidence (highest first)\r\n      for (let i = 0; i < results.length - 1; i++) {\r\n        expect(results[i].confidence).toBeGreaterThanOrEqual(results[i + 1].confidence);\r\n      }\r\n    });\r\n\r\n    it('should get results by quality', () => {\r\n      const results = aggregate.getResultsByQuality(2);\r\n\r\n      expect(results).toHaveLength(2);\r\n      // Should be sorted by quality score (highest first)\r\n      for (let i = 0; i < results.length - 1; i++) {\r\n        expect(results[i].getQualityScore()).toBeGreaterThanOrEqual(results[i + 1].getQualityScore());\r\n      }\r\n    });\r\n  });\r\n\r\n  describe('Relationship Queries', () => {\r\n    it('should find by correlation ID', () => {\r\n      const correlationId = 'test-correlation';\r\n      const results = [\r\n        AnalysisResultFactory.createForTesting({ \r\n          requestId: 'corr-1',\r\n          correlationId \r\n        }),\r\n        AnalysisResultFactory.createForTesting({ \r\n          requestId: 'corr-2',\r\n          correlationId \r\n        }),\r\n        AnalysisResultFactory.createForTesting({ \r\n          requestId: 'corr-3',\r\n          correlationId: 'different' \r\n        }),\r\n      ];\r\n\r\n      results.forEach(result => aggregate.addAnalysisResult(result));\r\n\r\n      const correlatedResults = aggregate.findByCorrelationId(correlationId);\r\n\r\n      expect(correlatedResults).toHaveLength(2);\r\n      expect(correlatedResults.every(r => r.correlationId === correlationId)).toBe(true);\r\n    });\r\n\r\n    it('should get child results', () => {\r\n      const parentId = UniqueEntityId.generate();\r\n      const parent = AnalysisResultFactory.createForTesting({ \r\n        requestId: 'parent' \r\n      });\r\n      const child1 = AnalysisResultFactory.createForTesting({ \r\n        requestId: 'child-1',\r\n        parentAnalysisId: parentId \r\n      });\r\n      const child2 = AnalysisResultFactory.createForTesting({ \r\n        requestId: 'child-2',\r\n        parentAnalysisId: parentId \r\n      });\r\n\r\n      [parent, child1, child2].forEach(result => aggregate.addAnalysisResult(result));\r\n\r\n      const childResults = aggregate.getChildResults(parentId);\r\n\r\n      expect(childResults).toHaveLength(2);\r\n      expect(childResults.every(r => r.parentAnalysisId?.equals(parentId))).toBe(true);\r\n    });\r\n\r\n    it('should get results by model ID', () => {\r\n      const modelId = UniqueEntityId.generate();\r\n      const results = [\r\n        AnalysisResultFactory.createForTesting({ \r\n          requestId: 'model-1',\r\n          modelId \r\n        }),\r\n        AnalysisResultFactory.createForTesting({ \r\n          requestId: 'model-2',\r\n          modelId \r\n        }),\r\n        AnalysisResultFactory.createForTesting({ \r\n          requestId: 'model-3' \r\n        }),\r\n      ];\r\n\r\n      results.forEach(result => aggregate.addAnalysisResult(result));\r\n\r\n      const modelResults = aggregate.getResultsByModel(modelId);\r\n\r\n      expect(modelResults).toHaveLength(2);\r\n      expect(modelResults.every(r => r.modelId.equals(modelId))).toBe(true);\r\n    });\r\n  });\r\n});"], "version": 3}