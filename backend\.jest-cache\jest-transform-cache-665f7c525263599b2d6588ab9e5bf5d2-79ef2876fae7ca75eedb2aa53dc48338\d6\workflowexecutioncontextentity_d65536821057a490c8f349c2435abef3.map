{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\entities\\workflow-execution-context.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCAQiB;AACjB,2EAAgE;AAEhE;;;;;GAKG;AAQI,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;IA6InC,sBAAsB;IACtB,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC;IACnC,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW,CAAC;IACrC,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,CAAC;IAClC,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW,CAAC;IACrC,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC;IACnC,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC/E,CAAC;IAED,IAAI,aAAa;QACf,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO,CAAC,CAAC;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,IAAI,EAAE,CAAC;QAC/C,OAAO,OAAO,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;IACtD,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO;YACL,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,WAAW,EAAE,IAAI,CAAC,OAAO,EAAE,WAAW,IAAI,CAAC;YAC3C,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,QAAQ,IAAI,CAAC;YACrC,YAAY,EAAE,IAAI,CAAC,OAAO,EAAE,YAAY,IAAI,CAAC;YAC7C,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC;YACvC,WAAW,EAAE,IAAI,CAAC,OAAO,EAAE,WAAW,IAAI,CAAC;YAC3C,aAAa,EAAE,IAAI,CAAC,OAAO,EAAE,aAAa,IAAI,EAAE;SACjD,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,aAAa;QACX,OAAO,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,aAAa;QACX,OAAO,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,GAAW,EAAE,KAAU;QACjC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACtB,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,GAAW;QACrB,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAA0C,EAAE,OAAe,EAAE,IAA0B;QAC5F,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YACb,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,KAAK;YACL,OAAO;YACP,IAAI;SACL,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,KAA0C;QACvD,OAAO,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,EAAE,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,QAUf;QACC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QAC1B,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,gBAQnB;QACC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC5B,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAC9B,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,aAKtB;QACC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC9B,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,0BAA0B;QACxB,OAAO,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;IAChE,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,OAAO,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;IACjE,CAAC;IAED;;OAEG;IACH,yBAAyB;QACvB,OAAO,IAAI,CAAC,iBAAiB,EAAE,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,MAAM,KAAK,WAAW,CAAC,IAAI,EAAE,CAAC;IACnG,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,OAAO,IAAI,CAAC,iBAAiB,EAAE,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,MAAM,KAAK,QAAQ,CAAC,IAAI,EAAE,CAAC;IAChG,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,mBAAmB,EAAE,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3C,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO;YACL,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,MAAM,IAAI,CAAC;YAC9C,eAAe,EAAE,IAAI,CAAC,0BAA0B,EAAE,CAAC,MAAM;YACzD,WAAW,EAAE,IAAI,CAAC,sBAAsB,EAAE,CAAC,MAAM;YACjD,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE,MAAM,IAAI,CAAC;YACtD,sBAAsB,EAAE,IAAI,CAAC,yBAAyB,EAAE,CAAC,MAAM;YAC/D,mBAAmB,EAAE,IAAI,CAAC,sBAAsB,EAAE,CAAC,MAAM;YACzD,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,EAAE,MAAM,IAAI,CAAC;YAC1D,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM;YAClE,WAAW,EAAE,IAAI,CAAC,OAAO,EAAE,WAAW,IAAI,CAAC;YAC3C,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,QAAQ,IAAI,CAAC;SACtC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;YACpB,KAAK,SAAS,CAAC,CAAC,OAAO,SAAS,CAAC;YACjC,KAAK,SAAS,CAAC,CAAC,OAAO,SAAS,CAAC;YACjC,KAAK,WAAW,CAAC,CAAC,OAAO,WAAW,CAAC;YACrC,KAAK,QAAQ,CAAC,CAAC,OAAO,QAAQ,CAAC;YAC/B,KAAK,WAAW,CAAC,CAAC,OAAO,WAAW,CAAC;YACrC,KAAK,SAAS,CAAC,CAAC,OAAO,SAAS,CAAC;YACjC,OAAO,CAAC,CAAC,OAAO,SAAS,CAAC;QAC5B,CAAC;IACH,CAAC;CACF,CAAA;AA/aY,4DAAwB;AAEnC;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;oDACpB;AAIX;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC;IAChC,IAAA,eAAK,GAAE;;6DACY;AAIpB;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;IAC3B,IAAA,eAAK,GAAE;;wDACO;AAGf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0DAC7B;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0DAC7B;AAIjB;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;IAC9C,IAAA,eAAK,GAAE;;wDACyE;AAGjF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAClD,MAAM,oBAAN,MAAM;uDAAc;AAG3B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAClD,MAAM,oBAAN,MAAM;wDAAc;AAG5B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAClD,MAAM,oBAAN,MAAM;wDAAc;AAG5B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uDAC1C;AAGd;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;2DAC7B;AAIlB;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACjE,IAAA,eAAK,GAAE;kDACG,IAAI,oBAAJ,IAAI;2DAAC;AAIhB;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACnE,IAAA,eAAK,GAAE;kDACK,IAAI,oBAAJ,IAAI;6DAAC;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0DAC5C;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;4DACzB;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;6DAC7B;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACnD,MAAM,oBAAN,MAAM;4DAAc;AAGhC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACnD,MAAM,oBAAN,MAAM;+DAAc;AAGnC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAClD,MAAM,oBAAN,MAAM;2DAAc;AAG/B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yDASzD;AAGF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAClD,KAAK,oBAAL,KAAK;sDAKR;AAGH;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;2DAQ5D;AAGF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACnD,KAAK,oBAAL,KAAK;+DAUjB;AAGH;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACnD,KAAK,oBAAL,KAAK;mEAQrB;AAGH;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,sBAAsB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACnD,KAAK,oBAAL,KAAK;qEAKvB;AAGH;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0DAO1D;AAGF;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;2DAAC;AAKhB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,6CAAiB,EAAE,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC;IACnE,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC;kDAC1B,6CAAiB,oBAAjB,6CAAiB;2DAAC;mCA3IlB,wBAAwB;IAPpC,IAAA,gBAAM,EAAC,6BAA6B,CAAC;IACrC,IAAA,eAAK,EAAC,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;IAChC,IAAA,eAAK,EAAC,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;IAChC,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,WAAW,CAAC,CAAC;IACpB,IAAA,eAAK,EAAC,CAAC,aAAa,CAAC,CAAC;GACV,wBAAwB,CA+apC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\entities\\workflow-execution-context.entity.ts"], "sourcesContent": ["import {\r\n  <PERSON>tity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  Index,\r\n  ManyToOne,\r\n  JoinColumn,\r\n} from 'typeorm';\r\nimport { WorkflowExecution } from './workflow-execution.entity';\r\n\r\n/**\r\n * Workflow Execution Context Entity\r\n * \r\n * Represents the execution context for individual workflow steps\r\n * with detailed state tracking and result storage.\r\n */\r\n@Entity('workflow_execution_contexts')\r\n@Index(['executionId', 'stepId'])\r\n@Index(['executionId', 'status'])\r\n@Index(['stepId'])\r\n@Index(['status'])\r\n@Index(['startedAt'])\r\n@Index(['completedAt'])\r\nexport class WorkflowExecutionContext {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  @Column({ name: 'execution_id' })\r\n  @Index()\r\n  executionId: string;\r\n\r\n  @Column({ name: 'step_id' })\r\n  @Index()\r\n  stepId: string;\r\n\r\n  @Column({ name: 'step_type', nullable: true })\r\n  stepType: string;\r\n\r\n  @Column({ name: 'step_name', nullable: true })\r\n  stepName: string;\r\n\r\n  @Column({ name: 'status', default: 'pending' })\r\n  @Index()\r\n  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled' | 'skipped';\r\n\r\n  @Column({ name: 'input', type: 'jsonb', nullable: true })\r\n  input: Record<string, any>;\r\n\r\n  @Column({ name: 'output', type: 'jsonb', nullable: true })\r\n  output: Record<string, any>;\r\n\r\n  @Column({ name: 'result', type: 'jsonb', nullable: true })\r\n  result: Record<string, any>;\r\n\r\n  @Column({ name: 'error', type: 'text', nullable: true })\r\n  error: string;\r\n\r\n  @Column({ name: 'error_code', nullable: true })\r\n  errorCode: string;\r\n\r\n  @Column({ name: 'started_at', type: 'timestamp', nullable: true })\r\n  @Index()\r\n  startedAt: Date;\r\n\r\n  @Column({ name: 'completed_at', type: 'timestamp', nullable: true })\r\n  @Index()\r\n  completedAt: Date;\r\n\r\n  @Column({ name: 'duration', type: 'bigint', nullable: true })\r\n  duration: number; // Duration in milliseconds\r\n\r\n  @Column({ name: 'retry_count', default: 0 })\r\n  retryCount: number;\r\n\r\n  @Column({ name: 'retry_reason', nullable: true })\r\n  retryReason: string;\r\n\r\n  @Column({ name: 'step_config', type: 'jsonb', nullable: true })\r\n  stepConfig: Record<string, any>;\r\n\r\n  @Column({ name: 'step_overrides', type: 'jsonb', nullable: true })\r\n  stepOverrides: Record<string, any>;\r\n\r\n  @Column({ name: 'variables', type: 'jsonb', nullable: true })\r\n  variables: Record<string, any>;\r\n\r\n  @Column({ name: 'metrics', type: 'jsonb', nullable: true })\r\n  metrics: {\r\n    executionTime?: number;\r\n    memoryUsage?: number;\r\n    cpuUsage?: number;\r\n    networkCalls?: number;\r\n    cacheHits?: number;\r\n    cacheMisses?: number;\r\n    customMetrics?: Record<string, number>;\r\n  };\r\n\r\n  @Column({ name: 'logs', type: 'jsonb', nullable: true })\r\n  logs: Array<{\r\n    timestamp: string;\r\n    level: 'debug' | 'info' | 'warn' | 'error';\r\n    message: string;\r\n    data?: Record<string, any>;\r\n  }>;\r\n\r\n  @Column({ name: 'trace_data', type: 'jsonb', nullable: true })\r\n  traceData: {\r\n    traceId?: string;\r\n    spanId?: string;\r\n    parentSpanId?: string;\r\n    operationName?: string;\r\n    tags?: Record<string, string>;\r\n    baggage?: Record<string, string>;\r\n  };\r\n\r\n  @Column({ name: 'external_calls', type: 'jsonb', nullable: true })\r\n  externalCalls: Array<{\r\n    service: string;\r\n    endpoint: string;\r\n    method: string;\r\n    requestTime: string;\r\n    responseTime?: string;\r\n    duration?: number;\r\n    status?: number;\r\n    success?: boolean;\r\n    error?: string;\r\n  }>;\r\n\r\n  @Column({ name: 'notifications_sent', type: 'jsonb', nullable: true })\r\n  notificationsSent: Array<{\r\n    id: string;\r\n    channel: string;\r\n    recipient: string;\r\n    status: string;\r\n    sentAt: string;\r\n    deliveredAt?: string;\r\n    error?: string;\r\n  }>;\r\n\r\n  @Column({ name: 'conditions_evaluated', type: 'jsonb', nullable: true })\r\n  conditionsEvaluated: Array<{\r\n    condition: any;\r\n    result: boolean;\r\n    evaluatedAt: string;\r\n    variables: Record<string, any>;\r\n  }>;\r\n\r\n  @Column({ name: 'metadata', type: 'jsonb', nullable: true })\r\n  metadata: {\r\n    stepDefinition?: Record<string, any>;\r\n    executionEnvironment?: Record<string, any>;\r\n    resourceUsage?: Record<string, any>;\r\n    performanceMetrics?: Record<string, any>;\r\n    debugInfo?: Record<string, any>;\r\n  };\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  // Relations\r\n  @ManyToOne(() => WorkflowExecution, execution => execution.contexts)\r\n  @JoinColumn({ name: 'execution_id' })\r\n  execution: WorkflowExecution;\r\n\r\n  // Computed properties\r\n  get isRunning(): boolean {\r\n    return this.status === 'running';\r\n  }\r\n\r\n  get isCompleted(): boolean {\r\n    return this.status === 'completed';\r\n  }\r\n\r\n  get isFailed(): boolean {\r\n    return this.status === 'failed';\r\n  }\r\n\r\n  get isCancelled(): boolean {\r\n    return this.status === 'cancelled';\r\n  }\r\n\r\n  get isSkipped(): boolean {\r\n    return this.status === 'skipped';\r\n  }\r\n\r\n  get isFinished(): boolean {\r\n    return ['completed', 'failed', 'cancelled', 'skipped'].includes(this.status);\r\n  }\r\n\r\n  get executionTime(): number {\r\n    if (!this.startedAt) return 0;\r\n    const endTime = this.completedAt || new Date();\r\n    return endTime.getTime() - this.startedAt.getTime();\r\n  }\r\n\r\n  get hasError(): boolean {\r\n    return !!this.error;\r\n  }\r\n\r\n  get wasRetried(): boolean {\r\n    return this.retryCount > 0;\r\n  }\r\n\r\n  /**\r\n   * Get step context summary\r\n   */\r\n  getStepSummary(): Record<string, any> {\r\n    return {\r\n      id: this.id,\r\n      executionId: this.executionId,\r\n      stepId: this.stepId,\r\n      stepType: this.stepType,\r\n      stepName: this.stepName,\r\n      status: this.status,\r\n      startedAt: this.startedAt,\r\n      completedAt: this.completedAt,\r\n      duration: this.duration,\r\n      retryCount: this.retryCount,\r\n      hasError: this.hasError,\r\n      error: this.error,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get step metrics\r\n   */\r\n  getStepMetrics(): Record<string, any> {\r\n    return {\r\n      executionTime: this.executionTime,\r\n      retryCount: this.retryCount,\r\n      memoryUsage: this.metrics?.memoryUsage || 0,\r\n      cpuUsage: this.metrics?.cpuUsage || 0,\r\n      networkCalls: this.metrics?.networkCalls || 0,\r\n      cacheHits: this.metrics?.cacheHits || 0,\r\n      cacheMisses: this.metrics?.cacheMisses || 0,\r\n      customMetrics: this.metrics?.customMetrics || {},\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get step result\r\n   */\r\n  getStepResult(): Record<string, any> {\r\n    return this.result || {};\r\n  }\r\n\r\n  /**\r\n   * Get step output\r\n   */\r\n  getStepOutput(): Record<string, any> {\r\n    return this.output || {};\r\n  }\r\n\r\n  /**\r\n   * Get step input\r\n   */\r\n  getStepInput(): Record<string, any> {\r\n    return this.input || {};\r\n  }\r\n\r\n  /**\r\n   * Get step variables\r\n   */\r\n  getStepVariables(): Record<string, any> {\r\n    return this.variables || {};\r\n  }\r\n\r\n  /**\r\n   * Set step variable\r\n   */\r\n  setVariable(key: string, value: any): void {\r\n    if (!this.variables) {\r\n      this.variables = {};\r\n    }\r\n    this.variables[key] = value;\r\n  }\r\n\r\n  /**\r\n   * Get step variable\r\n   */\r\n  getVariable(key: string): any {\r\n    return this.variables?.[key];\r\n  }\r\n\r\n  /**\r\n   * Add log entry\r\n   */\r\n  addLog(level: 'debug' | 'info' | 'warn' | 'error', message: string, data?: Record<string, any>): void {\r\n    if (!this.logs) {\r\n      this.logs = [];\r\n    }\r\n\r\n    this.logs.push({\r\n      timestamp: new Date().toISOString(),\r\n      level,\r\n      message,\r\n      data,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Get logs by level\r\n   */\r\n  getLogsByLevel(level: 'debug' | 'info' | 'warn' | 'error'): Array<any> {\r\n    return this.logs?.filter(log => log.level === level) || [];\r\n  }\r\n\r\n  /**\r\n   * Get error logs\r\n   */\r\n  getErrorLogs(): Array<any> {\r\n    return this.getLogsByLevel('error');\r\n  }\r\n\r\n  /**\r\n   * Get warning logs\r\n   */\r\n  getWarningLogs(): Array<any> {\r\n    return this.getLogsByLevel('warn');\r\n  }\r\n\r\n  /**\r\n   * Add external call record\r\n   */\r\n  addExternalCall(callData: {\r\n    service: string;\r\n    endpoint: string;\r\n    method: string;\r\n    requestTime: string;\r\n    responseTime?: string;\r\n    duration?: number;\r\n    status?: number;\r\n    success?: boolean;\r\n    error?: string;\r\n  }): void {\r\n    if (!this.externalCalls) {\r\n      this.externalCalls = [];\r\n    }\r\n\r\n    this.externalCalls.push(callData);\r\n  }\r\n\r\n  /**\r\n   * Add notification sent record\r\n   */\r\n  addNotificationSent(notificationData: {\r\n    id: string;\r\n    channel: string;\r\n    recipient: string;\r\n    status: string;\r\n    sentAt: string;\r\n    deliveredAt?: string;\r\n    error?: string;\r\n  }): void {\r\n    if (!this.notificationsSent) {\r\n      this.notificationsSent = [];\r\n    }\r\n\r\n    this.notificationsSent.push(notificationData);\r\n  }\r\n\r\n  /**\r\n   * Add condition evaluation record\r\n   */\r\n  addConditionEvaluation(conditionData: {\r\n    condition: any;\r\n    result: boolean;\r\n    evaluatedAt: string;\r\n    variables: Record<string, any>;\r\n  }): void {\r\n    if (!this.conditionsEvaluated) {\r\n      this.conditionsEvaluated = [];\r\n    }\r\n\r\n    this.conditionsEvaluated.push(conditionData);\r\n  }\r\n\r\n  /**\r\n   * Get successful external calls\r\n   */\r\n  getSuccessfulExternalCalls(): Array<any> {\r\n    return this.externalCalls?.filter(call => call.success) || [];\r\n  }\r\n\r\n  /**\r\n   * Get failed external calls\r\n   */\r\n  getFailedExternalCalls(): Array<any> {\r\n    return this.externalCalls?.filter(call => !call.success) || [];\r\n  }\r\n\r\n  /**\r\n   * Get delivered notifications\r\n   */\r\n  getDeliveredNotifications(): Array<any> {\r\n    return this.notificationsSent?.filter(notification => notification.status === 'delivered') || [];\r\n  }\r\n\r\n  /**\r\n   * Get failed notifications\r\n   */\r\n  getFailedNotifications(): Array<any> {\r\n    return this.notificationsSent?.filter(notification => notification.status === 'failed') || [];\r\n  }\r\n\r\n  /**\r\n   * Get condition evaluation results\r\n   */\r\n  getConditionResults(): Array<boolean> {\r\n    return this.conditionsEvaluated?.map(evaluation => evaluation.result) || [];\r\n  }\r\n\r\n  /**\r\n   * Check if all conditions passed\r\n   */\r\n  allConditionsPassed(): boolean {\r\n    const results = this.getConditionResults();\r\n    return results.length > 0 && results.every(result => result);\r\n  }\r\n\r\n  /**\r\n   * Get step performance summary\r\n   */\r\n  getPerformanceSummary(): Record<string, any> {\r\n    return {\r\n      executionTime: this.executionTime,\r\n      retryCount: this.retryCount,\r\n      externalCalls: this.externalCalls?.length || 0,\r\n      successfulCalls: this.getSuccessfulExternalCalls().length,\r\n      failedCalls: this.getFailedExternalCalls().length,\r\n      notificationsSent: this.notificationsSent?.length || 0,\r\n      deliveredNotifications: this.getDeliveredNotifications().length,\r\n      failedNotifications: this.getFailedNotifications().length,\r\n      conditionsEvaluated: this.conditionsEvaluated?.length || 0,\r\n      conditionsPassed: this.getConditionResults().filter(r => r).length,\r\n      memoryUsage: this.metrics?.memoryUsage || 0,\r\n      cpuUsage: this.metrics?.cpuUsage || 0,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get step status for display\r\n   */\r\n  getDisplayStatus(): string {\r\n    switch (this.status) {\r\n      case 'pending': return 'Pending';\r\n      case 'running': return 'Running';\r\n      case 'completed': return 'Completed';\r\n      case 'failed': return 'Failed';\r\n      case 'cancelled': return 'Cancelled';\r\n      case 'skipped': return 'Skipped';\r\n      default: return 'Unknown';\r\n    }\r\n  }\r\n}\r\n"], "version": 3}