c2e06c62b9d33f02f989d211ed3e3e3b
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const confidence_score_value_object_1 = require("../confidence-score.value-object");
describe('ConfidenceScore Value Object', () => {
    describe('Construction', () => {
        it('should create a valid confidence score', () => {
            const score = new confidence_score_value_object_1.ConfidenceScore(0.85);
            expect(score.value).toBe(0.85);
        });
        it('should accept minimum value', () => {
            const score = new confidence_score_value_object_1.ConfidenceScore(0.0);
            expect(score.value).toBe(0.0);
        });
        it('should accept maximum value', () => {
            const score = new confidence_score_value_object_1.ConfidenceScore(1.0);
            expect(score.value).toBe(1.0);
        });
        it('should throw error for negative values', () => {
            expect(() => new confidence_score_value_object_1.ConfidenceScore(-0.1)).toThrow('Confidence score cannot be less than 0');
        });
        it('should throw error for values greater than 1', () => {
            expect(() => new confidence_score_value_object_1.ConfidenceScore(1.1)).toThrow('Confidence score cannot be greater than 1');
        });
        it('should throw error for NaN', () => {
            expect(() => new confidence_score_value_object_1.ConfidenceScore(NaN)).toThrow('Confidence score cannot be NaN');
        });
        it('should throw error for infinite values', () => {
            expect(() => new confidence_score_value_object_1.ConfidenceScore(Infinity)).toThrow('Confidence score must be finite');
            expect(() => new confidence_score_value_object_1.ConfidenceScore(-Infinity)).toThrow('Confidence score must be finite');
        });
        it('should throw error for non-number values', () => {
            expect(() => new confidence_score_value_object_1.ConfidenceScore('0.5')).toThrow('Confidence score must be a number');
        });
    });
    describe('Factory Methods', () => {
        it('should create from percentage', () => {
            const score = confidence_score_value_object_1.ConfidenceScore.fromPercentage(85);
            expect(score.value).toBe(0.85);
        });
        it('should throw error for invalid percentage', () => {
            expect(() => confidence_score_value_object_1.ConfidenceScore.fromPercentage(-10)).toThrow('Percentage must be between 0 and 100');
            expect(() => confidence_score_value_object_1.ConfidenceScore.fromPercentage(110)).toThrow('Percentage must be between 0 and 100');
        });
        it('should create from fraction', () => {
            const score = confidence_score_value_object_1.ConfidenceScore.fromFraction(17, 20);
            expect(score.value).toBe(0.85);
        });
        it('should throw error for zero denominator', () => {
            expect(() => confidence_score_value_object_1.ConfidenceScore.fromFraction(1, 0)).toThrow('Denominator cannot be zero');
        });
        it('should throw error for negative values in fraction', () => {
            expect(() => confidence_score_value_object_1.ConfidenceScore.fromFraction(-1, 2)).toThrow('Numerator and denominator must be non-negative');
            expect(() => confidence_score_value_object_1.ConfidenceScore.fromFraction(1, -2)).toThrow('Numerator and denominator must be non-negative');
        });
        it('should create predefined confidence levels', () => {
            expect(confidence_score_value_object_1.ConfidenceScore.low().value).toBe(0.3);
            expect(confidence_score_value_object_1.ConfidenceScore.medium().value).toBe(0.7);
            expect(confidence_score_value_object_1.ConfidenceScore.high().value).toBe(0.9);
            expect(confidence_score_value_object_1.ConfidenceScore.maximum().value).toBe(1.0);
            expect(confidence_score_value_object_1.ConfidenceScore.minimum().value).toBe(0.0);
        });
    });
    describe('Level Classification', () => {
        it('should classify confidence levels correctly', () => {
            expect(new confidence_score_value_object_1.ConfidenceScore(0.1).getLevel()).toBe('very_low');
            expect(new confidence_score_value_object_1.ConfidenceScore(0.25).getLevel()).toBe('low');
            expect(new confidence_score_value_object_1.ConfidenceScore(0.5).getLevel()).toBe('medium');
            expect(new confidence_score_value_object_1.ConfidenceScore(0.8).getLevel()).toBe('high');
            expect(new confidence_score_value_object_1.ConfidenceScore(0.95).getLevel()).toBe('very_high');
        });
        it('should check level predicates', () => {
            const lowScore = new confidence_score_value_object_1.ConfidenceScore(0.2);
            const mediumScore = new confidence_score_value_object_1.ConfidenceScore(0.5);
            const highScore = new confidence_score_value_object_1.ConfidenceScore(0.95);
            expect(lowScore.isLow()).toBe(true);
            expect(mediumScore.isMedium()).toBe(true);
            expect(highScore.isHigh()).toBe(true);
            expect(highScore.isLow()).toBe(false);
            expect(lowScore.isHigh()).toBe(false);
        });
        it('should check threshold compliance', () => {
            const score = new confidence_score_value_object_1.ConfidenceScore(0.75);
            expect(score.meetsThreshold(0.7)).toBe(true);
            expect(score.meetsThreshold(0.8)).toBe(false);
        });
    });
    describe('Conversions', () => {
        it('should convert to percentage', () => {
            const score = new confidence_score_value_object_1.ConfidenceScore(0.85);
            expect(score.toPercentage()).toBe(85);
        });
        it('should convert to percentage string', () => {
            const score = new confidence_score_value_object_1.ConfidenceScore(0.8567);
            expect(score.toPercentageString()).toBe('85.7%');
            expect(score.toPercentageString(2)).toBe('85.67%');
        });
        it('should convert to string', () => {
            const score = new confidence_score_value_object_1.ConfidenceScore(0.85);
            expect(score.toString()).toBe('85.0% (high)');
        });
    });
    describe('Mathematical Operations', () => {
        it('should combine with another confidence score', () => {
            const score1 = new confidence_score_value_object_1.ConfidenceScore(0.8);
            const score2 = new confidence_score_value_object_1.ConfidenceScore(0.6);
            const combined = score1.combineWith(score2, 0.7);
            expect(combined.value).toBeCloseTo(0.74); // 0.8 * 0.7 + 0.6 * 0.3
        });
        it('should throw error for invalid weight in combine', () => {
            const score1 = new confidence_score_value_object_1.ConfidenceScore(0.8);
            const score2 = new confidence_score_value_object_1.ConfidenceScore(0.6);
            expect(() => score1.combineWith(score2, -0.1)).toThrow('Weight must be between 0 and 1');
            expect(() => score1.combineWith(score2, 1.1)).toThrow('Weight must be between 0 and 1');
        });
        it('should calculate inverse', () => {
            const score = new confidence_score_value_object_1.ConfidenceScore(0.3);
            const inverse = score.inverse();
            expect(inverse.value).toBe(0.7);
        });
        it('should multiply by factor', () => {
            const score = new confidence_score_value_object_1.ConfidenceScore(0.5);
            const multiplied = score.multiply(1.5);
            expect(multiplied.value).toBe(0.75);
        });
        it('should cap multiplication at maximum', () => {
            const score = new confidence_score_value_object_1.ConfidenceScore(0.8);
            const multiplied = score.multiply(2);
            expect(multiplied.value).toBe(1.0);
        });
        it('should throw error for negative factor', () => {
            const score = new confidence_score_value_object_1.ConfidenceScore(0.5);
            expect(() => score.multiply(-1)).toThrow('Factor cannot be negative');
        });
        it('should add confidence scores', () => {
            const score1 = new confidence_score_value_object_1.ConfidenceScore(0.3);
            const score2 = new confidence_score_value_object_1.ConfidenceScore(0.4);
            const sum = score1.add(score2);
            expect(sum.value).toBe(0.7);
        });
        it('should cap addition at maximum', () => {
            const score1 = new confidence_score_value_object_1.ConfidenceScore(0.7);
            const score2 = new confidence_score_value_object_1.ConfidenceScore(0.5);
            const sum = score1.add(score2);
            expect(sum.value).toBe(1.0);
        });
        it('should subtract confidence scores', () => {
            const score1 = new confidence_score_value_object_1.ConfidenceScore(0.8);
            const score2 = new confidence_score_value_object_1.ConfidenceScore(0.3);
            const difference = score1.subtract(score2);
            expect(difference.value).toBe(0.5);
        });
        it('should floor subtraction at minimum', () => {
            const score1 = new confidence_score_value_object_1.ConfidenceScore(0.2);
            const score2 = new confidence_score_value_object_1.ConfidenceScore(0.5);
            const difference = score1.subtract(score2);
            expect(difference.value).toBe(0.0);
        });
        it('should calculate difference', () => {
            const score1 = new confidence_score_value_object_1.ConfidenceScore(0.8);
            const score2 = new confidence_score_value_object_1.ConfidenceScore(0.3);
            expect(score1.differenceFrom(score2)).toBe(0.5);
            expect(score2.differenceFrom(score1)).toBe(0.5);
        });
        it('should compare confidence scores', () => {
            const score1 = new confidence_score_value_object_1.ConfidenceScore(0.8);
            const score2 = new confidence_score_value_object_1.ConfidenceScore(0.3);
            expect(score1.isGreaterThan(score2)).toBe(true);
            expect(score2.isLessThan(score1)).toBe(true);
            expect(score1.isLessThan(score2)).toBe(false);
            expect(score2.isGreaterThan(score1)).toBe(false);
        });
        it('should round confidence scores', () => {
            const score = new confidence_score_value_object_1.ConfidenceScore(0.8567);
            expect(score.round().value).toBe(0.86);
            expect(score.round(3).value).toBe(0.857);
        });
    });
    describe('Static Utility Methods', () => {
        it('should calculate average', () => {
            const scores = [
                new confidence_score_value_object_1.ConfidenceScore(0.8),
                new confidence_score_value_object_1.ConfidenceScore(0.6),
                new confidence_score_value_object_1.ConfidenceScore(0.9),
            ];
            const average = confidence_score_value_object_1.ConfidenceScore.average(scores);
            expect(average.value).toBeCloseTo(0.7667, 3);
        });
        it('should throw error for empty array in average', () => {
            expect(() => confidence_score_value_object_1.ConfidenceScore.average([])).toThrow('Cannot calculate average of empty array');
        });
        it('should calculate weighted average', () => {
            const scores = [
                new confidence_score_value_object_1.ConfidenceScore(0.8),
                new confidence_score_value_object_1.ConfidenceScore(0.6),
            ];
            const weights = [0.7, 0.3];
            const weightedAvg = confidence_score_value_object_1.ConfidenceScore.weightedAverage(scores, weights);
            expect(weightedAvg.value).toBeCloseTo(0.74); // (0.8 * 0.7 + 0.6 * 0.3) / 1.0
        });
        it('should throw error for mismatched arrays in weighted average', () => {
            const scores = [new confidence_score_value_object_1.ConfidenceScore(0.8)];
            const weights = [0.7, 0.3];
            expect(() => confidence_score_value_object_1.ConfidenceScore.weightedAverage(scores, weights))
                .toThrow('Scores and weights arrays must have the same length');
        });
        it('should find maximum and minimum', () => {
            const scores = [
                new confidence_score_value_object_1.ConfidenceScore(0.8),
                new confidence_score_value_object_1.ConfidenceScore(0.3),
                new confidence_score_value_object_1.ConfidenceScore(0.9),
            ];
            expect(confidence_score_value_object_1.ConfidenceScore.max(scores).value).toBe(0.9);
            expect(confidence_score_value_object_1.ConfidenceScore.min(scores).value).toBe(0.3);
        });
        it('should throw error for empty array in max/min', () => {
            expect(() => confidence_score_value_object_1.ConfidenceScore.max([])).toThrow('Cannot find maximum of empty array');
            expect(() => confidence_score_value_object_1.ConfidenceScore.min([])).toThrow('Cannot find minimum of empty array');
        });
    });
    describe('JSON Serialization', () => {
        it('should serialize to JSON', () => {
            const score = new confidence_score_value_object_1.ConfidenceScore(0.85);
            const json = score.toJSON();
            expect(json.value).toBe(0.85);
            expect(json.percentage).toBe(85);
            expect(json.level).toBe('high');
            expect(json.isHigh).toBe(true);
        });
        it('should deserialize from JSON', () => {
            const json = { value: 0.75 };
            const score = confidence_score_value_object_1.ConfidenceScore.fromJSON(json);
            expect(score.value).toBe(0.75);
        });
    });
    describe('Equality', () => {
        it('should be equal to another confidence score with same value', () => {
            const score1 = new confidence_score_value_object_1.ConfidenceScore(0.85);
            const score2 = new confidence_score_value_object_1.ConfidenceScore(0.85);
            expect(score1.equals(score2)).toBe(true);
        });
        it('should not be equal to confidence score with different value', () => {
            const score1 = new confidence_score_value_object_1.ConfidenceScore(0.85);
            const score2 = new confidence_score_value_object_1.ConfidenceScore(0.75);
            expect(score1.equals(score2)).toBe(false);
        });
        it('should not be equal to null or undefined', () => {
            const score = new confidence_score_value_object_1.ConfidenceScore(0.85);
            expect(score.equals(null)).toBe(false);
            expect(score.equals(undefined)).toBe(false);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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