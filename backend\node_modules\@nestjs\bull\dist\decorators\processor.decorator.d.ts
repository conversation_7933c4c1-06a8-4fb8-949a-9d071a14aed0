import { Scope } from '@nestjs/common';
export interface ProcessorOptions {
    /**
     * Specifies the name of the queue to subscribe to.
     */
    name?: string;
    /**
     * Specifies the lifetime of an injected Processor.
     */
    scope?: Scope;
}
/**
 * Represents a worker that is able to process jobs from the queue.
 */
export declare function Processor(): ClassDecorator;
/**
 * Represents a worker that is able to process jobs from the queue.
 * @param queueName queue name
 */
export declare function Processor(queueName: string): ClassDecorator;
/**
 * Represents a worker that is able to process jobs from the queue.
 * @param processorOptions processor options
 */
export declare function Processor(processorOptions: ProcessorOptions): ClassDecorator;
//# sourceMappingURL=processor.decorator.d.ts.map