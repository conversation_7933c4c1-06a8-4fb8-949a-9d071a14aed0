import { UniqueEntityId } from '../../../../../shared-kernel/value-objects/unique-entity-id.value-object';
import { 
  AnalysisResult, 
  AnalysisType,
  AnalysisStatus,
  AnalysisInput,
  AnalysisOutput,
  AnalysisMetadata,
  ErrorDetails,
  ErrorCategory
} from '../analysis-result.entity';
import { AnalysisResultCreatedEvent } from '../../events/analysis-result-created.domain-event';
import { AnalysisResultStatusChangedEvent } from '../../events/analysis-result-status-changed.domain-event';
import { AnalysisResultUpdatedEvent } from '../../events/analysis-result-updated.domain-event';

describe('AnalysisResult Entity', () => {
  let validProps: Omit<any, 'createdAt' | 'updatedAt' | 'childAnalysisIds'>;
  let mockInputData: AnalysisInput;
  let mockOutputData: AnalysisOutput;
  let mockMetadata: AnalysisMetadata;

  beforeEach(() => {
    mockInputData = {
      data: { test: 'input' },
      format: 'json',
      size: 100,
      checksum: 'input-checksum',
      preprocessingApplied: ['normalization'],
      validationRules: ['required'],
    };

    mockOutputData = {
      results: { prediction: 'test-result' },
      format: 'json',
      size: 200,
      checksum: 'output-checksum',
      postprocessingApplied: ['formatting'],
      validationStatus: 'passed',
      qualityScore: 0.9,
    };

    mockMetadata = {
      version: '1.0.0',
      algorithm: 'test-algorithm',
      parameters: { param1: 'value1' },
      environment: 'test',
      resourceUsage: {
        cpuTime: 1000,
        memoryUsage: 512,
        gpuTime: 500,
        networkIO: 100,
        diskIO: 50,
      },
      performanceMetrics: {
        throughput: 10,
        latency: 100,
        accuracy: 0.95,
        precision: 0.92,
        recall: 0.88,
        f1Score: 0.90,
      },
      qualityMetrics: {
        dataQuality: 0.9,
        resultReliability: 0.85,
        consistencyScore: 0.8,
        completenessScore: 0.95,
      },
    };

    validProps = {
      requestId: 'test-request-123',
      modelId: UniqueEntityId.generate(),
      analysisType: AnalysisType.CLASSIFICATION,
      inputData: mockInputData,
      outputData: mockOutputData,
      confidence: 0.85,
      processingTime: 1500,
      status: AnalysisStatus.PENDING,
      metadata: mockMetadata,
      tags: ['test', 'classification'],
      correlationId: 'correlation-123',
      parentAnalysisId: UniqueEntityId.generate(),
      errorDetails: undefined,
      completedAt: undefined,
    };
  });

  describe('Creation', () => {
    it('should create a valid analysis result', () => {
      const result = AnalysisResult.create(validProps);

      expect(result).toBeInstanceOf(AnalysisResult);
      expect(result.requestId).toBe(validProps.requestId);
      expect(result.modelId.equals(validProps.modelId)).toBe(true);
      expect(result.analysisType).toBe(validProps.analysisType);
      expect(result.status).toBe(validProps.status);
      expect(result.confidence).toBe(validProps.confidence);
      expect(result.childAnalysisIds).toEqual([]);
      expect(result.createdAt).toBeInstanceOf(Date);
      expect(result.updatedAt).toBeInstanceOf(Date);
    });

    it('should generate a domain event when created', () => {
      const result = AnalysisResult.create(validProps);
      const events = result.domainEvents;

      expect(events).toHaveLength(1);
      expect(events[0]).toBeInstanceOf(AnalysisResultCreatedEvent);
      expect((events[0] as AnalysisResultCreatedEvent).requestId).toBe(validProps.requestId);
    });

    it('should create with custom ID', () => {
      const customId = UniqueEntityId.generate();
      const result = AnalysisResult.create(validProps, customId);

      expect(result.id.equals(customId)).toBe(true);
    });

    it('should throw error for empty request ID', () => {
      const invalidProps = { ...validProps, requestId: '' };

      expect(() => AnalysisResult.create(invalidProps)).toThrow('Request ID is required');
    });

    it('should throw error for missing model ID', () => {
      const invalidProps = { ...validProps, modelId: null as any };

      expect(() => AnalysisResult.create(invalidProps)).toThrow('Model ID is required');
    });

    it('should throw error for invalid analysis type', () => {
      const invalidProps = { ...validProps, analysisType: 'invalid' as AnalysisType };

      expect(() => AnalysisResult.create(invalidProps)).toThrow('Invalid analysis type');
    });

    it('should throw error for invalid confidence', () => {
      const invalidProps = { ...validProps, confidence: 1.5 };

      expect(() => AnalysisResult.create(invalidProps)).toThrow('Confidence must be between 0 and 1');
    });

    it('should throw error for negative processing time', () => {
      const invalidProps = { ...validProps, processingTime: -100 };

      expect(() => AnalysisResult.create(invalidProps)).toThrow('Processing time cannot be negative');
    });

    it('should throw error for missing input data', () => {
      const invalidProps = { ...validProps, inputData: null as any };

      expect(() => AnalysisResult.create(invalidProps)).toThrow('Input data is required');
    });

    it('should throw error for missing metadata', () => {
      const invalidProps = { ...validProps, metadata: null as any };

      expect(() => AnalysisResult.create(invalidProps)).toThrow('Metadata is required');
    });
  });

  describe('Reconstitution', () => {
    it('should reconstitute from valid props', () => {
      const id = UniqueEntityId.generate();
      const now = new Date();
      const props = {
        ...validProps,
        childAnalysisIds: [UniqueEntityId.generate()],
        createdAt: now,
        updatedAt: now,
      };

      const result = AnalysisResult.reconstitute(props, id);

      expect(result.id.equals(id)).toBe(true);
      expect(result.requestId).toBe(props.requestId);
      expect(result.childAnalysisIds).toHaveLength(1);
      expect(result.createdAt).toBe(now);
      expect(result.updatedAt).toBe(now);
    });
  });

  describe('Status Management', () => {
    let result: AnalysisResult;

    beforeEach(() => {
      result = AnalysisResult.create(validProps);
      result.clearEvents(); // Clear creation event
    });

    it('should start processing from pending status', () => {
      result.startProcessing();

      expect(result.status).toBe(AnalysisStatus.PROCESSING);
      expect(result.domainEvents).toHaveLength(1);
      expect(result.domainEvents[0]).toBeInstanceOf(AnalysisResultStatusChangedEvent);
    });

    it('should not start processing from non-pending status', () => {
      // First start processing
      result.startProcessing();
      result.clearEvents();

      expect(() => result.startProcessing()).toThrow('Cannot start processing analysis in status: processing');
    });

    it('should complete analysis from processing status', () => {
      result.startProcessing();
      result.clearEvents();

      result.complete(mockOutputData, 1500, 0.95);

      expect(result.status).toBe(AnalysisStatus.COMPLETED);
      expect(result.outputData).toEqual(mockOutputData);
      expect(result.processingTime).toBe(1500);
      expect(result.confidence).toBe(0.95);
      expect(result.completedAt).toBeInstanceOf(Date);
      expect(result.domainEvents).toHaveLength(1);
      expect(result.domainEvents[0]).toBeInstanceOf(AnalysisResultStatusChangedEvent);
    });

    it('should not complete analysis from non-processing status', () => {
      expect(() => result.complete(mockOutputData, 1500, 0.95)).toThrow(
        'Cannot complete analysis in status: pending'
      );
    });

    it('should fail analysis with error details', () => {
      result.startProcessing();
      result.clearEvents();

      const errorDetails: ErrorDetails = {
        code: 'TEST_ERROR',
        message: 'Test error',
        context: { test: true },
        retryable: true,
        category: ErrorCategory.PROCESSING_ERROR,
      };

      result.fail(errorDetails);

      expect(result.status).toBe(AnalysisStatus.FAILED);
      expect(result.errorDetails).toEqual(errorDetails);
      expect(result.completedAt).toBeInstanceOf(Date);
      expect(result.domainEvents).toHaveLength(1);
      expect(result.domainEvents[0]).toBeInstanceOf(AnalysisResultStatusChangedEvent);
    });

    it('should not fail completed analysis', () => {
      result.startProcessing();
      result.complete(mockOutputData, 1500, 0.95);

      const errorDetails: ErrorDetails = {
        code: 'TEST_ERROR',
        message: 'Test error',
        context: {},
        retryable: false,
        category: ErrorCategory.PROCESSING_ERROR,
      };

      expect(() => result.fail(errorDetails)).toThrow('Cannot fail a completed analysis');
    });

    it('should cancel analysis', () => {
      result.cancel();

      expect(result.status).toBe(AnalysisStatus.CANCELLED);
      expect(result.completedAt).toBeInstanceOf(Date);
      expect(result.domainEvents).toHaveLength(1);
      expect(result.domainEvents[0]).toBeInstanceOf(AnalysisResultStatusChangedEvent);
    });

    it('should not cancel completed or failed analysis', () => {
      result.startProcessing();
      result.complete(mockOutputData, 1500, 0.95);

      expect(() => result.cancel()).toThrow('Cannot cancel analysis in status: completed');
    });
  });

  describe('Metadata Management', () => {
    let result: AnalysisResult;

    beforeEach(() => {
      result = AnalysisResult.create(validProps);
      result.clearEvents();
    });

    it('should update metadata', () => {
      const newMetadata = {
        version: '2.0.0',
        algorithm: 'new-algorithm',
      };

      result.updateMetadata(newMetadata);

      expect(result.metadata.version).toBe('2.0.0');
      expect(result.metadata.algorithm).toBe('new-algorithm');
      expect(result.metadata.parameters).toEqual(mockMetadata.parameters); // Original preserved
      expect(result.domainEvents).toHaveLength(1);
      expect(result.domainEvents[0]).toBeInstanceOf(AnalysisResultUpdatedEvent);
    });
  });

  describe('Tag Management', () => {
    let result: AnalysisResult;

    beforeEach(() => {
      result = AnalysisResult.create(validProps);
      result.clearEvents();
    });

    it('should add tag', () => {
      result.addTag('new-tag');

      expect(result.tags).toContain('new-tag');
      expect(result.domainEvents).toHaveLength(1);
      expect(result.domainEvents[0]).toBeInstanceOf(AnalysisResultUpdatedEvent);
    });

    it('should not add duplicate tag', () => {
      const initialLength = result.tags.length;
      result.addTag('test'); // Already exists

      expect(result.tags).toHaveLength(initialLength);
      expect(result.domainEvents).toHaveLength(0);
    });

    it('should normalize tag case', () => {
      result.addTag('NEW-TAG');

      expect(result.tags).toContain('new-tag');
    });

    it('should throw error for empty tag', () => {
      expect(() => result.addTag('')).toThrow('Tag cannot be empty');
      expect(() => result.addTag('   ')).toThrow('Tag cannot be empty');
    });

    it('should remove tag', () => {
      result.removeTag('test');

      expect(result.tags).not.toContain('test');
      expect(result.domainEvents).toHaveLength(1);
      expect(result.domainEvents[0]).toBeInstanceOf(AnalysisResultUpdatedEvent);
    });

    it('should check if has tag', () => {
      expect(result.hasTag('test')).toBe(true);
      expect(result.hasTag('TEST')).toBe(true); // Case insensitive
      expect(result.hasTag('nonexistent')).toBe(false);
    });
  });

  describe('Child Analysis Management', () => {
    let result: AnalysisResult;

    beforeEach(() => {
      result = AnalysisResult.create(validProps);
      result.clearEvents();
    });

    it('should add child analysis', () => {
      const childId = UniqueEntityId.generate();
      result.addChildAnalysis(childId);

      expect(result.childAnalysisIds).toHaveLength(1);
      expect(result.childAnalysisIds[0].equals(childId)).toBe(true);
      expect(result.domainEvents).toHaveLength(1);
      expect(result.domainEvents[0]).toBeInstanceOf(AnalysisResultUpdatedEvent);
    });

    it('should not add duplicate child analysis', () => {
      const childId = UniqueEntityId.generate();
      result.addChildAnalysis(childId);
      result.clearEvents();

      result.addChildAnalysis(childId);

      expect(result.childAnalysisIds).toHaveLength(1);
      expect(result.domainEvents).toHaveLength(0);
    });

    it('should remove child analysis', () => {
      const childId = UniqueEntityId.generate();
      result.addChildAnalysis(childId);
      result.clearEvents();

      result.removeChildAnalysis(childId);

      expect(result.childAnalysisIds).toHaveLength(0);
      expect(result.domainEvents).toHaveLength(1);
      expect(result.domainEvents[0]).toBeInstanceOf(AnalysisResultUpdatedEvent);
    });
  });

  describe('Status Checks', () => {
    let result: AnalysisResult;

    beforeEach(() => {
      result = AnalysisResult.create(validProps);
    });

    it('should check if terminal', () => {
      expect(result.isTerminal()).toBe(false);

      result.startProcessing();
      expect(result.isTerminal()).toBe(false);

      result.complete(mockOutputData, 1500, 0.95);
      expect(result.isTerminal()).toBe(true);
    });

    it('should check if successful', () => {
      expect(result.isSuccessful()).toBe(false);

      result.startProcessing();
      result.complete(mockOutputData, 1500, 0.95);
      expect(result.isSuccessful()).toBe(true);
    });

    it('should check high confidence', () => {
      result.startProcessing();
      result.complete(mockOutputData, 1500, 0.95);

      expect(result.hasHighConfidence()).toBe(true);
      expect(result.hasHighConfidence(0.98)).toBe(false);
    });

    it('should check if retryable', () => {
      result.startProcessing();
      
      const retryableError: ErrorDetails = {
        code: 'RETRYABLE_ERROR',
        message: 'Retryable error',
        context: {},
        retryable: true,
        category: ErrorCategory.NETWORK_ERROR,
      };

      result.fail(retryableError);
      expect(result.isRetryable()).toBe(true);

      const nonRetryableError: ErrorDetails = {
        code: 'NON_RETRYABLE_ERROR',
        message: 'Non-retryable error',
        context: {},
        retryable: false,
        category: ErrorCategory.INPUT_VALIDATION,
      };

      const result2 = AnalysisResult.create(validProps);
      result2.startProcessing();
      result2.fail(nonRetryableError);
      expect(result2.isRetryable()).toBe(false);
    });
  });

  describe('Utility Methods', () => {
    let result: AnalysisResult;

    beforeEach(() => {
      result = AnalysisResult.create(validProps);
    });

    it('should get duration for completed analysis', () => {
      result.startProcessing();
      
      // Wait a bit to ensure different timestamps
      setTimeout(() => {
        result.complete(mockOutputData, 1500, 0.95);
        const duration = result.getDuration();
        
        expect(duration).toBeGreaterThan(0);
      }, 10);
    });

    it('should return undefined duration for incomplete analysis', () => {
      expect(result.getDuration()).toBeUndefined();
    });

    it('should calculate quality score', () => {
      const qualityScore = result.getQualityScore();

      expect(qualityScore).toBeGreaterThan(0);
      expect(qualityScore).toBeLessThanOrEqual(1);
    });

    it('should get summary', () => {
      result.startProcessing();
      result.complete(mockOutputData, 1500, 0.95);

      const summary = result.getSummary();

      expect(summary.id).toBe(result.id.toString());
      expect(summary.requestId).toBe(result.requestId);
      expect(summary.analysisType).toBe(result.analysisType);
      expect(summary.status).toBe(result.status);
      expect(summary.confidence).toBe(result.confidence);
      expect(summary.processingTime).toBe(result.processingTime);
      expect(summary.isSuccessful).toBe(true);
      expect(summary.qualityScore).toBeGreaterThan(0);
    });
  });

  describe('Immutability', () => {
    let result: AnalysisResult;

    beforeEach(() => {
      result = AnalysisResult.create(validProps);
    });

    it('should return copies of arrays and objects', () => {
      const tags = result.tags;
      const childIds = result.childAnalysisIds;
      const inputData = result.inputData;
      const metadata = result.metadata;

      tags.push('modified');
      childIds.push(UniqueEntityId.generate());
      inputData.data.modified = true;
      metadata.version = 'modified';

      expect(result.tags).not.toContain('modified');
      expect(result.childAnalysisIds).toHaveLength(0);
      expect(result.inputData.data.modified).toBeUndefined();
      expect(result.metadata.version).toBe('1.0.0');
    });
  });
});