import { BaseIntegrationEvent } from '../../../../shared-kernel/integration/base-integration-event';
import { ThreatSeverity } from '../../domain/enums/threat-severity.enum';
import { ConfidenceLevel } from '../../domain/enums/confidence-level.enum';

/**
 * Threat Intelligence Integration Event Data
 */
export interface ThreatIntelligenceIntegrationEventData {
  /** Event type identifier */
  eventType: 'threat_detected' | 'ioc_discovered' | 'campaign_identified' | 'vulnerability_disclosed';
  /** Threat intelligence data */
  threatIntelligence: {
    /** Threat ID */
    threatId: string;
    /** Threat name */
    threatName: string;
    /** Threat type */
    threatType: string;
    /** Severity level */
    severity: ThreatSeverity;
    /** Confidence level */
    confidence: ConfidenceLevel;
    /** First seen timestamp */
    firstSeen: string;
    /** Last seen timestamp */
    lastSeen: string;
    /** Threat description */
    description: string;
    /** Threat actor attribution */
    attribution?: {
      actor: string;
      aliases: string[];
      motivation: string[];
      sophistication: 'low' | 'medium' | 'high' | 'expert';
    };
    /** Campaign information */
    campaign?: {
      name: string;
      startDate: string;
      endDate?: string;
      objectives: string[];
      targetSectors: string[];
      targetGeographies: string[];
    };
    /** Indicators of Compromise */
    indicators: Array<{
      type: string;
      value: string;
      confidence: ConfidenceLevel;
      context: string;
      tags: string[];
    }>;
    /** MITRE ATT&CK techniques */
    mitreAttackTechniques: Array<{
      techniqueId: string;
      techniqueName: string;
      tactic: string;
      description: string;
    }>;
    /** Kill chain phases */
    killChainPhases: string[];
    /** Affected platforms */
    platforms: string[];
    /** Malware families */
    malwareFamilies: string[];
  };
  /** Source information */
  source: {
    /** Source provider */
    provider: string;
    /** Source feed */
    feed: string;
    /** Source reliability */
    reliability: 'A' | 'B' | 'C' | 'D' | 'E' | 'F';
    /** Source URL */
    url?: string;
    /** Source timestamp */
    timestamp: string;
  };
  /** Integration metadata */
  integration: {
    /** Integration ID */
    integrationId: string;
    /** Processing timestamp */
    processedAt: string;
    /** Processing duration (ms) */
    processingDuration: number;
    /** Quality score */
    qualityScore: number;
    /** Enrichment applied */
    enrichmentApplied: string[];
    /** Validation results */
    validationResults: {
      isValid: boolean;
      errors: string[];
      warnings: string[];
    };
  };
}

/**
 * Threat Intelligence Integration Event
 * 
 * Published when threat intelligence data is received from external sources
 * and needs to be integrated into the security platform.
 * 
 * Key use cases:
 * - Integrate threat intelligence feeds (STIX/TAXII, commercial feeds)
 * - Update threat detection rules and signatures
 * - Enrich existing security events with new intelligence
 * - Trigger threat hunting activities
 * - Update risk assessments and vulnerability priorities
 * - Notify security teams of new threats
 */
export class ThreatIntelligenceIntegrationEvent extends BaseIntegrationEvent<ThreatIntelligenceIntegrationEventData> {
  constructor(
    eventData: ThreatIntelligenceIntegrationEventData,
    options?: {
      eventId?: string;
      correlationId?: string;
      causationId?: string;
      timestamp?: Date;
      version?: string;
      metadata?: Record<string, any>;
    }
  ) {
    super('ThreatIntelligenceIntegration', eventData, {
      source: 'sentinel-backend',
      version: options?.version || '1.0',
      correlationId: options?.correlationId,
      causationId: options?.causationId,
      metadata: {
        domain: 'Security',
        category: 'ThreatIntelligence',
        ...options?.metadata,
      },
    });
  }

  /**
   * Validate event data
   */
  validate(): boolean {
    if (!this.eventData?.threatIntelligence?.threatId) return false;
    if (!this.eventData?.threatIntelligence?.threatName) return false;
    if (!this.eventData?.threatIntelligence?.severity) return false;
    if (!this.eventData?.integration?.integrationId) return false;
    return true;
  }

  /**
   * Get event priority
   */
  getPriority(): 'low' | 'medium' | 'high' | 'critical' {
    const severity = this.eventData.threatIntelligence.severity;
    switch (severity) {
      case ThreatSeverity.CRITICAL:
        return 'critical';
      case ThreatSeverity.HIGH:
        return 'high';
      case ThreatSeverity.MEDIUM:
        return 'medium';
      case ThreatSeverity.LOW:
      default:
        return 'low';
    }
  }

  /**
   * Check if event requires immediate processing
   */
  requiresImmediateProcessing(): boolean {
    return this.eventData.threatIntelligence.severity === ThreatSeverity.CRITICAL ||
           this.eventData.threatIntelligence.severity === ThreatSeverity.HIGH;
  }

  /**
   * Get threat ID
   */
  get threatId(): string {
    return this.eventData.threatIntelligence.threatId;
  }

  /**
   * Get threat name
   */
  get threatName(): string {
    return this.eventData.threatIntelligence.threatName;
  }

  /**
   * Get threat severity
   */
  get severity(): ThreatSeverity {
    return this.eventData.threatIntelligence.severity;
  }

  /**
   * Get confidence level
   */
  get confidence(): ConfidenceLevel {
    return this.eventData.threatIntelligence.confidence;
  }

  /**
   * Get source provider
   */
  get sourceProvider(): string {
    return this.eventData.source.provider;
  }

  /**
   * Get indicators count
   */
  get indicatorCount(): number {
    return this.eventData.threatIntelligence.indicators.length;
  }

  /**
   * Get MITRE ATT&CK techniques count
   */
  get mitreAttackTechniqueCount(): number {
    return this.eventData.threatIntelligence.mitreAttackTechniques.length;
  }

  /**
   * Check if threat is high severity
   */
  isHighSeverity(): boolean {
    return this.eventData.threatIntelligence.severity === ThreatSeverity.HIGH ||
           this.eventData.threatIntelligence.severity === ThreatSeverity.CRITICAL;
  }

  /**
   * Check if threat is high confidence
   */
  isHighConfidence(): boolean {
    return this.eventData.threatIntelligence.confidence === ConfidenceLevel.HIGH ||
           this.eventData.threatIntelligence.confidence === ConfidenceLevel.VERY_HIGH ||
           this.eventData.threatIntelligence.confidence === ConfidenceLevel.CONFIRMED;
  }

  /**
   * Check if threat has attribution
   */
  hasAttribution(): boolean {
    return !!this.eventData.threatIntelligence.attribution;
  }

  /**
   * Check if threat is part of campaign
   */
  isPartOfCampaign(): boolean {
    return !!this.eventData.threatIntelligence.campaign;
  }

  /**
   * Check if threat has high-quality indicators
   */
  hasHighQualityIndicators(): boolean {
    return this.eventData.threatIntelligence.indicators.some(indicator =>
      indicator.confidence === ConfidenceLevel.HIGH ||
      indicator.confidence === ConfidenceLevel.VERY_HIGH ||
      indicator.confidence === ConfidenceLevel.CONFIRMED
    );
  }

  /**
   * Check if source is reliable
   */
  isReliableSource(): boolean {
    return ['A', 'B', 'C'].includes(this.eventData.source.reliability);
  }

  /**
   * Check if integration quality is high
   */
  hasHighIntegrationQuality(): boolean {
    return this.eventData.integration.qualityScore >= 80 &&
           this.eventData.integration.validationResults.isValid;
  }

  /**
   * Get threat priority score
   */
  getThreatPriorityScore(): number {
    let score = 0;

    // Base score from severity
    switch (this.eventData.threatIntelligence.severity) {
      case ThreatSeverity.CRITICAL: score += 40; break;
      case ThreatSeverity.HIGH: score += 30; break;
      case ThreatSeverity.MEDIUM: score += 20; break;
      case ThreatSeverity.LOW: score += 10; break;
    }

    // Confidence bonus
    switch (this.eventData.threatIntelligence.confidence) {
      case ConfidenceLevel.CONFIRMED: score += 25; break;
      case ConfidenceLevel.VERY_HIGH: score += 20; break;
      case ConfidenceLevel.HIGH: score += 15; break;
      case ConfidenceLevel.MEDIUM: score += 10; break;
      case ConfidenceLevel.LOW: score += 5; break;
    }

    // Source reliability bonus
    const reliabilityBonus = { A: 15, B: 12, C: 8, D: 4, E: 2, F: 0 };
    score += reliabilityBonus[this.eventData.source.reliability] || 0;

    // Attribution bonus
    if (this.hasAttribution()) score += 10;

    // Campaign bonus
    if (this.isPartOfCampaign()) score += 10;

    return Math.min(100, score);
  }

  /**
   * Get integration actions to trigger
   */
  getIntegrationActions(): string[] {
    const actions: string[] = [];

    if (this.isHighSeverity() && this.isHighConfidence()) {
      actions.push('immediate_threat_alert', 'update_detection_rules', 'trigger_threat_hunting');
    }

    if (this.hasHighQualityIndicators()) {
      actions.push('enrich_existing_events', 'update_ioc_database', 'validate_indicators');
    }

    if (this.hasAttribution()) {
      actions.push('update_threat_actor_profiles', 'correlate_with_campaigns');
    }

    if (this.isPartOfCampaign()) {
      actions.push('campaign_analysis', 'target_assessment', 'defensive_recommendations');
    }

    if (this.eventData.threatIntelligence.mitreAttackTechniques.length > 0) {
      actions.push('update_attack_patterns', 'validate_detection_coverage');
    }

    actions.push('update_threat_landscape', 'generate_intelligence_report');

    return actions;
  }

  /**
   * Get notification requirements
   */
  getNotificationRequirements(): {
    urgency: 'low' | 'normal' | 'high' | 'critical';
    channels: string[];
    recipients: string[];
  } {
    let urgency: 'low' | 'normal' | 'high' | 'critical' = 'normal';
    const channels: string[] = ['email', 'dashboard'];
    const recipients: string[] = ['threat_intelligence_team'];

    if (this.isHighSeverity() && this.isHighConfidence()) {
      urgency = 'critical';
      channels.push('sms', 'slack', 'webhook');
      recipients.push('security_operations_center', 'incident_response_team');
    } else if (this.isHighSeverity() || this.isHighConfidence()) {
      urgency = 'high';
      channels.push('slack');
      recipients.push('security_analysts');
    }

    if (this.hasAttribution() || this.isPartOfCampaign()) {
      recipients.push('threat_hunters', 'intelligence_analysts');
    }

    return { urgency, channels, recipients };
  }

  /**
   * Get enrichment opportunities
   */
  getEnrichmentOpportunities(): Array<{
    type: string;
    description: string;
    priority: 'low' | 'medium' | 'high';
  }> {
    const opportunities: Array<{
      type: string;
      description: string;
      priority: 'low' | 'medium' | 'high';
    }> = [];

    if (this.hasHighQualityIndicators()) {
      opportunities.push({
        type: 'event_enrichment',
        description: 'Enrich existing security events with new IOCs',
        priority: 'high',
      });
    }

    if (this.eventData.threatIntelligence.mitreAttackTechniques.length > 0) {
      opportunities.push({
        type: 'technique_mapping',
        description: 'Map threat techniques to existing detections',
        priority: 'medium',
      });
    }

    if (this.hasAttribution()) {
      opportunities.push({
        type: 'actor_profiling',
        description: 'Update threat actor profiles and TTPs',
        priority: 'medium',
      });
    }

    if (this.isPartOfCampaign()) {
      opportunities.push({
        type: 'campaign_tracking',
        description: 'Track campaign evolution and targets',
        priority: 'high',
      });
    }

    return opportunities;
  }

  /**
   * Get validation summary
   */
  getValidationSummary(): {
    isValid: boolean;
    qualityScore: number;
    issues: string[];
    recommendations: string[];
  } {
    const validation = this.eventData.integration.validationResults;
    const qualityScore = this.eventData.integration.qualityScore;

    const recommendations: string[] = [];

    if (qualityScore < 70) {
      recommendations.push('Review data quality and source reliability');
    }

    if (!this.isReliableSource()) {
      recommendations.push('Verify information with additional sources');
    }

    if (validation.warnings.length > 0) {
      recommendations.push('Address validation warnings');
    }

    if (this.eventData.threatIntelligence.indicators.length === 0) {
      recommendations.push('Obtain additional indicators for better detection');
    }

    return {
      isValid: validation.isValid,
      qualityScore,
      issues: [...validation.errors, ...validation.warnings],
      recommendations,
    };
  }

  /**
   * Convert to external system format
   */
  toExternalFormat(): {
    eventType: string;
    timestamp: string;
    threat: {
      id: string;
      name: string;
      type: string;
      severity: string;
      confidence: string;
      description: string;
      firstSeen: string;
      lastSeen: string;
    };
    attribution?: {
      actor: string;
      aliases: string[];
      motivation: string[];
      sophistication: string;
    };
    campaign?: {
      name: string;
      startDate: string;
      endDate?: string;
      objectives: string[];
      targets: {
        sectors: string[];
        geographies: string[];
      };
    };
    indicators: Array<{
      type: string;
      value: string;
      confidence: string;
      context: string;
      tags: string[];
    }>;
    techniques: Array<{
      id: string;
      name: string;
      tactic: string;
      description: string;
    }>;
    killChain: string[];
    platforms: string[];
    malware: string[];
    source: {
      provider: string;
      feed: string;
      reliability: string;
      url?: string;
      timestamp: string;
    };
    metadata: {
      integrationId: string;
      processedAt: string;
      qualityScore: number;
      priorityScore: number;
      actions: string[];
      notifications: {
        urgency: string;
        channels: string[];
        recipients: string[];
      };
    };
  } {
    const threat = this.eventData.threatIntelligence;
    const notifications = this.getNotificationRequirements();

    return {
      eventType: this.eventData.eventType,
      timestamp: this.timestamp.toISOString(),
      threat: {
        id: threat.threatId,
        name: threat.threatName,
        type: threat.threatType,
        severity: threat.severity,
        confidence: threat.confidence,
        description: threat.description,
        firstSeen: threat.firstSeen,
        lastSeen: threat.lastSeen,
      },
      attribution: threat.attribution,
      campaign: threat.campaign ? {
        ...threat.campaign,
        targets: {
          sectors: threat.campaign.targetSectors,
          geographies: threat.campaign.targetGeographies,
        },
      } : undefined,
      indicators: threat.indicators,
      techniques: threat.mitreAttackTechniques.map(technique => ({
        id: technique.techniqueId,
        name: technique.techniqueName,
        tactic: technique.tactic,
        description: technique.description,
      })),
      killChain: threat.killChainPhases,
      platforms: threat.platforms,
      malware: threat.malwareFamilies,
      source: this.eventData.source,
      metadata: {
        integrationId: this.eventData.integration.integrationId,
        processedAt: this.eventData.integration.processedAt,
        qualityScore: this.eventData.integration.qualityScore,
        priorityScore: this.getThreatPriorityScore(),
        actions: this.getIntegrationActions(),
        notifications,
      },
    };
  }

  /**
   * Create from external threat intelligence data
   */
  static fromExternalData(
    externalData: any,
    integrationId: string,
    sourceProvider: string
  ): ThreatIntelligenceIntegrationEvent {
    const now = new Date();

    const eventData: ThreatIntelligenceIntegrationEventData = {
      eventType: externalData.type || 'threat_detected',
      threatIntelligence: {
        threatId: externalData.id || `threat-${Date.now()}`,
        threatName: externalData.name || 'Unknown Threat',
        threatType: externalData.threat_type || 'unknown',
        severity: externalData.severity || ThreatSeverity.MEDIUM,
        confidence: externalData.confidence || ConfidenceLevel.MEDIUM,
        firstSeen: externalData.first_seen || now.toISOString(),
        lastSeen: externalData.last_seen || now.toISOString(),
        description: externalData.description || '',
        attribution: externalData.attribution,
        campaign: externalData.campaign,
        indicators: externalData.indicators || [],
        mitreAttackTechniques: externalData.mitre_attack_techniques || [],
        killChainPhases: externalData.kill_chain_phases || [],
        platforms: externalData.platforms || [],
        malwareFamilies: externalData.malware_families || [],
      },
      source: {
        provider: sourceProvider,
        feed: externalData.source?.feed || 'unknown',
        reliability: externalData.source?.reliability || 'C',
        url: externalData.source?.url,
        timestamp: externalData.source?.timestamp || now.toISOString(),
      },
      integration: {
        integrationId,
        processedAt: now.toISOString(),
        processingDuration: 0,
        qualityScore: 75, // Default quality score
        enrichmentApplied: [],
        validationResults: {
          isValid: true,
          errors: [],
          warnings: [],
        },
      },
    };

    return new ThreatIntelligenceIntegrationEvent(eventData);
  }
}
