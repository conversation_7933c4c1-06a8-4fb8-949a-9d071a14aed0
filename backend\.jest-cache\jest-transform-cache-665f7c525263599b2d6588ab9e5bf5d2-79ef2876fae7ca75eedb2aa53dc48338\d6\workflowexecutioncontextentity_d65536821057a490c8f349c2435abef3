44a6221df52d2eff389a9b1c4d2428b5
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowExecutionContext = void 0;
const typeorm_1 = require("typeorm");
const workflow_execution_entity_1 = require("./workflow-execution.entity");
/**
 * Workflow Execution Context Entity
 *
 * Represents the execution context for individual workflow steps
 * with detailed state tracking and result storage.
 */
let WorkflowExecutionContext = class WorkflowExecutionContext {
    // Computed properties
    get isRunning() {
        return this.status === 'running';
    }
    get isCompleted() {
        return this.status === 'completed';
    }
    get isFailed() {
        return this.status === 'failed';
    }
    get isCancelled() {
        return this.status === 'cancelled';
    }
    get isSkipped() {
        return this.status === 'skipped';
    }
    get isFinished() {
        return ['completed', 'failed', 'cancelled', 'skipped'].includes(this.status);
    }
    get executionTime() {
        if (!this.startedAt)
            return 0;
        const endTime = this.completedAt || new Date();
        return endTime.getTime() - this.startedAt.getTime();
    }
    get hasError() {
        return !!this.error;
    }
    get wasRetried() {
        return this.retryCount > 0;
    }
    /**
     * Get step context summary
     */
    getStepSummary() {
        return {
            id: this.id,
            executionId: this.executionId,
            stepId: this.stepId,
            stepType: this.stepType,
            stepName: this.stepName,
            status: this.status,
            startedAt: this.startedAt,
            completedAt: this.completedAt,
            duration: this.duration,
            retryCount: this.retryCount,
            hasError: this.hasError,
            error: this.error,
        };
    }
    /**
     * Get step metrics
     */
    getStepMetrics() {
        return {
            executionTime: this.executionTime,
            retryCount: this.retryCount,
            memoryUsage: this.metrics?.memoryUsage || 0,
            cpuUsage: this.metrics?.cpuUsage || 0,
            networkCalls: this.metrics?.networkCalls || 0,
            cacheHits: this.metrics?.cacheHits || 0,
            cacheMisses: this.metrics?.cacheMisses || 0,
            customMetrics: this.metrics?.customMetrics || {},
        };
    }
    /**
     * Get step result
     */
    getStepResult() {
        return this.result || {};
    }
    /**
     * Get step output
     */
    getStepOutput() {
        return this.output || {};
    }
    /**
     * Get step input
     */
    getStepInput() {
        return this.input || {};
    }
    /**
     * Get step variables
     */
    getStepVariables() {
        return this.variables || {};
    }
    /**
     * Set step variable
     */
    setVariable(key, value) {
        if (!this.variables) {
            this.variables = {};
        }
        this.variables[key] = value;
    }
    /**
     * Get step variable
     */
    getVariable(key) {
        return this.variables?.[key];
    }
    /**
     * Add log entry
     */
    addLog(level, message, data) {
        if (!this.logs) {
            this.logs = [];
        }
        this.logs.push({
            timestamp: new Date().toISOString(),
            level,
            message,
            data,
        });
    }
    /**
     * Get logs by level
     */
    getLogsByLevel(level) {
        return this.logs?.filter(log => log.level === level) || [];
    }
    /**
     * Get error logs
     */
    getErrorLogs() {
        return this.getLogsByLevel('error');
    }
    /**
     * Get warning logs
     */
    getWarningLogs() {
        return this.getLogsByLevel('warn');
    }
    /**
     * Add external call record
     */
    addExternalCall(callData) {
        if (!this.externalCalls) {
            this.externalCalls = [];
        }
        this.externalCalls.push(callData);
    }
    /**
     * Add notification sent record
     */
    addNotificationSent(notificationData) {
        if (!this.notificationsSent) {
            this.notificationsSent = [];
        }
        this.notificationsSent.push(notificationData);
    }
    /**
     * Add condition evaluation record
     */
    addConditionEvaluation(conditionData) {
        if (!this.conditionsEvaluated) {
            this.conditionsEvaluated = [];
        }
        this.conditionsEvaluated.push(conditionData);
    }
    /**
     * Get successful external calls
     */
    getSuccessfulExternalCalls() {
        return this.externalCalls?.filter(call => call.success) || [];
    }
    /**
     * Get failed external calls
     */
    getFailedExternalCalls() {
        return this.externalCalls?.filter(call => !call.success) || [];
    }
    /**
     * Get delivered notifications
     */
    getDeliveredNotifications() {
        return this.notificationsSent?.filter(notification => notification.status === 'delivered') || [];
    }
    /**
     * Get failed notifications
     */
    getFailedNotifications() {
        return this.notificationsSent?.filter(notification => notification.status === 'failed') || [];
    }
    /**
     * Get condition evaluation results
     */
    getConditionResults() {
        return this.conditionsEvaluated?.map(evaluation => evaluation.result) || [];
    }
    /**
     * Check if all conditions passed
     */
    allConditionsPassed() {
        const results = this.getConditionResults();
        return results.length > 0 && results.every(result => result);
    }
    /**
     * Get step performance summary
     */
    getPerformanceSummary() {
        return {
            executionTime: this.executionTime,
            retryCount: this.retryCount,
            externalCalls: this.externalCalls?.length || 0,
            successfulCalls: this.getSuccessfulExternalCalls().length,
            failedCalls: this.getFailedExternalCalls().length,
            notificationsSent: this.notificationsSent?.length || 0,
            deliveredNotifications: this.getDeliveredNotifications().length,
            failedNotifications: this.getFailedNotifications().length,
            conditionsEvaluated: this.conditionsEvaluated?.length || 0,
            conditionsPassed: this.getConditionResults().filter(r => r).length,
            memoryUsage: this.metrics?.memoryUsage || 0,
            cpuUsage: this.metrics?.cpuUsage || 0,
        };
    }
    /**
     * Get step status for display
     */
    getDisplayStatus() {
        switch (this.status) {
            case 'pending': return 'Pending';
            case 'running': return 'Running';
            case 'completed': return 'Completed';
            case 'failed': return 'Failed';
            case 'cancelled': return 'Cancelled';
            case 'skipped': return 'Skipped';
            default: return 'Unknown';
        }
    }
};
exports.WorkflowExecutionContext = WorkflowExecutionContext;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], WorkflowExecutionContext.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'execution_id' }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], WorkflowExecutionContext.prototype, "executionId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'step_id' }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], WorkflowExecutionContext.prototype, "stepId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'step_type', nullable: true }),
    __metadata("design:type", String)
], WorkflowExecutionContext.prototype, "stepType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'step_name', nullable: true }),
    __metadata("design:type", String)
], WorkflowExecutionContext.prototype, "stepName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'status', default: 'pending' }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], WorkflowExecutionContext.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'input', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_a = typeof Record !== "undefined" && Record) === "function" ? _a : Object)
], WorkflowExecutionContext.prototype, "input", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'output', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Record !== "undefined" && Record) === "function" ? _b : Object)
], WorkflowExecutionContext.prototype, "output", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'result', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_c = typeof Record !== "undefined" && Record) === "function" ? _c : Object)
], WorkflowExecutionContext.prototype, "result", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'error', type: 'text', nullable: true }),
    __metadata("design:type", String)
], WorkflowExecutionContext.prototype, "error", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'error_code', nullable: true }),
    __metadata("design:type", String)
], WorkflowExecutionContext.prototype, "errorCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'started_at', type: 'timestamp', nullable: true }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], WorkflowExecutionContext.prototype, "startedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'completed_at', type: 'timestamp', nullable: true }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], WorkflowExecutionContext.prototype, "completedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'duration', type: 'bigint', nullable: true }),
    __metadata("design:type", Number)
], WorkflowExecutionContext.prototype, "duration", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'retry_count', default: 0 }),
    __metadata("design:type", Number)
], WorkflowExecutionContext.prototype, "retryCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'retry_reason', nullable: true }),
    __metadata("design:type", String)
], WorkflowExecutionContext.prototype, "retryReason", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'step_config', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_f = typeof Record !== "undefined" && Record) === "function" ? _f : Object)
], WorkflowExecutionContext.prototype, "stepConfig", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'step_overrides', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_g = typeof Record !== "undefined" && Record) === "function" ? _g : Object)
], WorkflowExecutionContext.prototype, "stepOverrides", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'variables', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_h = typeof Record !== "undefined" && Record) === "function" ? _h : Object)
], WorkflowExecutionContext.prototype, "variables", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'metrics', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], WorkflowExecutionContext.prototype, "metrics", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'logs', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_j = typeof Array !== "undefined" && Array) === "function" ? _j : Object)
], WorkflowExecutionContext.prototype, "logs", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'trace_data', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], WorkflowExecutionContext.prototype, "traceData", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'external_calls', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_k = typeof Array !== "undefined" && Array) === "function" ? _k : Object)
], WorkflowExecutionContext.prototype, "externalCalls", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'notifications_sent', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_l = typeof Array !== "undefined" && Array) === "function" ? _l : Object)
], WorkflowExecutionContext.prototype, "notificationsSent", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'conditions_evaluated', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_m = typeof Array !== "undefined" && Array) === "function" ? _m : Object)
], WorkflowExecutionContext.prototype, "conditionsEvaluated", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'metadata', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], WorkflowExecutionContext.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_o = typeof Date !== "undefined" && Date) === "function" ? _o : Object)
], WorkflowExecutionContext.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => workflow_execution_entity_1.WorkflowExecution, execution => execution.contexts),
    (0, typeorm_1.JoinColumn)({ name: 'execution_id' }),
    __metadata("design:type", typeof (_p = typeof workflow_execution_entity_1.WorkflowExecution !== "undefined" && workflow_execution_entity_1.WorkflowExecution) === "function" ? _p : Object)
], WorkflowExecutionContext.prototype, "execution", void 0);
exports.WorkflowExecutionContext = WorkflowExecutionContext = __decorate([
    (0, typeorm_1.Entity)('workflow_execution_contexts'),
    (0, typeorm_1.Index)(['executionId', 'stepId']),
    (0, typeorm_1.Index)(['executionId', 'status']),
    (0, typeorm_1.Index)(['stepId']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['startedAt']),
    (0, typeorm_1.Index)(['completedAt'])
], WorkflowExecutionContext);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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