import {
  Injectable,
  NestMiddleware,
  Logger,
} from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';

/**
 * Enhanced response transformation middleware for API v2
 * Provides advanced response formatting, compression, and analytics
 */
@Injectable()
export class ResponseTransformationV2Middleware implements NestMiddleware {
  private readonly logger = new Logger(ResponseTransformationV2Middleware.name);

  constructor(private readonly configService: ConfigService) {}

  use(req: Request, res: Response, next: NextFunction) {
    const startTime = Date.now();
    const originalSend = res.send;
    const originalJson = res.json;

    // Override res.send
    const self = this;
    res.send = function(data: any) {
      const transformedData = self.transformResponse(req, data, startTime);
      return originalSend.call(this, transformedData);
    };

    // Override res.json
    res.json = function(data: any) {
      const transformedData = self.transformResponse(req, data, startTime);
      return originalJson.call(this, transformedData);
    };

    next();
  }

  private transformResponse(req: Request, data: any, startTime: number): any {
    const processingTime = Date.now() - startTime;
    
    // Enhanced v2 response format
    const transformedResponse: any = {
      success: this.determineSuccess(data),
      data: this.transformData(data),
      metadata: this.createV2Metadata(req, processingTime),
      links: this.generateHATEOASLinks(req, data),
      performance: this.createPerformanceMetrics(processingTime),
    };

    // Add error information if present
    if (data?.error || data?.message?.includes('error')) {
      transformedResponse.error = this.transformError(data);
    }

    // Add pagination if present
    if (data?.pagination || data?.total !== undefined) {
      transformedResponse.pagination = this.transformPagination(data, req);
    }

    // Log response for analytics
    this.logV2Response(req, transformedResponse, processingTime);

    return transformedResponse;
  }

  private determineSuccess(data: any): boolean {
    if (data?.success !== undefined) {
      return data.success;
    }
    
    // Determine success based on data structure
    return !data?.error && !data?.message?.includes('error');
  }

  private transformData(data: any): any {
    if (data?.data !== undefined) {
      return data.data;
    }
    
    // If data doesn't have a 'data' property, return the entire object
    // but exclude metadata fields
    const { success, error, metadata, links, pagination, ...actualData } = data || {};
    return Object.keys(actualData).length > 0 ? actualData : data;
  }

  private createV2Metadata(req: Request, processingTime: number): any {
    return {
      version: '2.0',
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id'] || this.generateRequestId(),
      processingTime: `${processingTime}ms`,
      endpoint: req.originalUrl,
      method: req.method,
      userAgent: req.headers['user-agent'],
      clientVersion: req.headers['x-client-version'],
      features: {
        enhanced: true,
        realTime: req.originalUrl.includes('streaming'),
        analytics: req.originalUrl.includes('analytics'),
        correlation: req.originalUrl.includes('correlation'),
      },
    };
  }

  private generateHATEOASLinks(req: Request, data: any): any {
    const baseUrl = `${req.protocol}://${req.get('host')}`;
    const links: any = {
      self: `${baseUrl}${req.originalUrl}`,
    };

    // Add contextual links based on endpoint
    if (req.originalUrl.includes('analytics')) {
      links.export = `${baseUrl}/api/v2/analytics/export/report`;
      links.insights = `${baseUrl}/api/v2/analytics/insights/recommendations`;
    }

    if (req.originalUrl.includes('correlation')) {
      links.relationships = `${baseUrl}/api/v2/correlation/relationships/map`;
      links.patterns = `${baseUrl}/api/v2/correlation/patterns/analyze`;
    }

    // Add pagination links if applicable
    if (data?.pagination) {
      const { page, limit, total } = data.pagination;
      const totalPages = Math.ceil(total / limit);
      
      if (page > 1) {
        links.prev = this.buildPaginationUrl(req, page - 1, limit);
        links.first = this.buildPaginationUrl(req, 1, limit);
      }
      
      if (page < totalPages) {
        links.next = this.buildPaginationUrl(req, page + 1, limit);
        links.last = this.buildPaginationUrl(req, totalPages, limit);
      }
    }

    return links;
  }

  private createPerformanceMetrics(processingTime: number): any {
    return {
      responseTime: processingTime,
      category: this.categorizePerformance(processingTime),
      optimized: processingTime < 1000, // Under 1 second
      cacheHit: false, // Would be determined by cache middleware
    };
  }

  private transformError(data: any): any {
    return {
      code: data?.error?.code || data?.code || 'UNKNOWN_ERROR',
      message: data?.error?.message || data?.message || 'An error occurred',
      details: data?.error?.details || data?.details || {},
      timestamp: new Date().toISOString(),
      version: '2.0',
    };
  }

  private transformPagination(data: any, req: Request): any {
    const pagination = data?.pagination || {};
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const total = data?.total || pagination?.total || 0;
    
    return {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNext: page * limit < total,
      hasPrev: page > 1,
      offset: (page - 1) * limit,
    };
  }

  private buildPaginationUrl(req: Request, page: number, limit: number): string {
    const url = new URL(`${req.protocol}://${req.get('host')}${req.originalUrl}`);
    url.searchParams.set('page', page.toString());
    url.searchParams.set('limit', limit.toString());
    return url.toString();
  }

  private categorizePerformance(processingTime: number): string {
    if (processingTime < 100) return 'excellent';
    if (processingTime < 500) return 'good';
    if (processingTime < 1000) return 'acceptable';
    if (processingTime < 2000) return 'slow';
    return 'very_slow';
  }

  private generateRequestId(): string {
    return `v2-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  private logV2Response(req: Request, response: any, processingTime: number): void {
    this.logger.log(`V2 Response sent`, {
      method: req.method,
      url: req.url,
      statusCode: response.success ? 200 : 400,
      processingTime,
      responseSize: JSON.stringify(response).length,
      hasError: !!response.error,
      hasPagination: !!response.pagination,
      requestId: response.metadata?.requestId,
    });
  }
}