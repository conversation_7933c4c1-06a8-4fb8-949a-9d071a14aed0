61d4dba7b0fb122e28fe111f90f5d4e8
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const data_serializer_service_1 = require("../data-serializer.service");
describe('DataSerializerService', () => {
    let service;
    let configService;
    beforeEach(async () => {
        const mockConfigService = {
            get: jest.fn().mockImplementation((key, defaultValue) => {
                const config = {
                    'ai.serialization.compression.enabled': true,
                    'ai.serialization.compression.level': 6,
                    'ai.serialization.compression.threshold': 1024,
                };
                return config[key] || defaultValue;
            }),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                data_serializer_service_1.DataSerializerService,
                { provide: config_1.ConfigService, useValue: mockConfigService },
            ],
        }).compile();
        service = module.get(data_serializer_service_1.DataSerializerService);
        configService = module.get(config_1.ConfigService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('JSON Serialization', () => {
        describe('serializeToJson', () => {
            it('should serialize simple object to JSON', async () => {
                const data = { name: 'test', value: 123, active: true };
                const result = await service.serializeToJson(data);
                expect(result).toMatchObject({
                    format: 'json',
                    compressed: false,
                    originalSize: expect.any(Number),
                    compressedSize: expect.any(Number),
                    compressionRatio: 1,
                    processingTime: expect.any(Number),
                    metadata: expect.objectContaining({
                        encoding: 'utf8',
                    }),
                });
                expect(result.data).toBeInstanceOf(Buffer);
                expect(result.originalSize).toBeGreaterThan(0);
            });
            it('should serialize large object with compression', async () => {
                // Create large object that exceeds compression threshold
                const largeData = {
                    items: Array.from({ length: 100 }, (_, i) => ({
                        id: i,
                        name: `Item ${i}`,
                        description: 'This is a long description that will help make the data large enough to trigger compression',
                        metadata: { created: new Date(), updated: new Date(), tags: ['tag1', 'tag2', 'tag3'] },
                    })),
                };
                const result = await service.serializeToJson(largeData);
                expect(result.compressed).toBe(true);
                expect(result.compressionType).toBe('gzip');
                expect(result.compressionRatio).toBeGreaterThan(1);
                expect(result.compressedSize).toBeLessThan(result.originalSize);
            });
            it('should serialize with custom options', async () => {
                const data = { name: 'test', value: 123 };
                const options = {
                    space: 2,
                    forceCompression: true,
                    compressionType: 'deflate',
                    metadata: { source: 'test' },
                };
                const result = await service.serializeToJson(data, options);
                expect(result.compressed).toBe(true);
                expect(result.compressionType).toBe('deflate');
                expect(result.metadata).toMatchObject({
                    encoding: 'utf8',
                    source: 'test',
                });
            });
            it('should handle serialization errors', async () => {
                const circularData = {};
                circularData['self'] = circularData; // Create circular reference
                await expect(service.serializeToJson(circularData)).rejects.toThrow('JSON serialization failed');
            });
        });
        describe('deserializeFromJson', () => {
            it('should deserialize JSON data', async () => {
                const originalData = { name: 'test', value: 123, active: true };
                const serialized = await service.serializeToJson(originalData);
                const result = await service.deserializeFromJson(serialized);
                expect(result).toEqual(originalData);
            });
            it('should deserialize compressed JSON data', async () => {
                const originalData = { name: 'test', value: 123 };
                const serialized = await service.serializeToJson(originalData, { forceCompression: true });
                const result = await service.deserializeFromJson(serialized);
                expect(result).toEqual(originalData);
            });
            it('should deserialize from Buffer', async () => {
                const originalData = { name: 'test', value: 123 };
                const jsonString = JSON.stringify(originalData);
                const buffer = Buffer.from(jsonString, 'utf8');
                const result = await service.deserializeFromJson(buffer);
                expect(result).toEqual(originalData);
            });
            it('should handle deserialization errors', async () => {
                const invalidBuffer = Buffer.from('invalid json', 'utf8');
                await expect(service.deserializeFromJson(invalidBuffer)).rejects.toThrow('JSON deserialization failed');
            });
        });
    });
    describe('Protocol Buffers Serialization', () => {
        describe('serializeToProtobuf', () => {
            it('should serialize data to Protocol Buffers format', async () => {
                const data = { name: 'test', value: 123 };
                const schema = 'TestMessage';
                const result = await service.serializeToProtobuf(data, schema);
                expect(result).toMatchObject({
                    format: 'protobuf',
                    schema: 'TestMessage',
                    compressed: false,
                    originalSize: expect.any(Number),
                    compressedSize: expect.any(Number),
                    processingTime: expect.any(Number),
                    metadata: expect.objectContaining({
                        schema: 'TestMessage',
                    }),
                });
                expect(result.data).toBeInstanceOf(Buffer);
            });
            it('should serialize with compression', async () => {
                const data = { name: 'test', value: 123 };
                const schema = 'TestMessage';
                const options = { forceCompression: true };
                const result = await service.serializeToProtobuf(data, schema, options);
                expect(result.compressed).toBe(true);
                expect(result.compressionType).toBe('gzip');
            });
        });
        describe('deserializeFromProtobuf', () => {
            it('should deserialize Protocol Buffers data', async () => {
                const originalData = { name: 'test', value: 123 };
                const schema = 'TestMessage';
                const serialized = await service.serializeToProtobuf(originalData, schema);
                const result = await service.deserializeFromProtobuf(serialized, schema);
                expect(result).toEqual(originalData);
            });
            it('should handle schema mismatch', async () => {
                const originalData = { name: 'test', value: 123 };
                const schema = 'TestMessage';
                const serialized = await service.serializeToProtobuf(originalData, schema);
                await expect(service.deserializeFromProtobuf(serialized, 'DifferentSchema')).rejects.toThrow('Schema mismatch');
            });
        });
    });
    describe('Binary Serialization', () => {
        describe('serializeToBinary', () => {
            it('should serialize string to binary', async () => {
                const data = 'Hello, World!';
                const encoding = 'base64';
                const result = await service.serializeToBinary(data, encoding);
                expect(result).toMatchObject({
                    format: 'binary',
                    compressed: false,
                    originalSize: expect.any(Number),
                    compressedSize: expect.any(Number),
                    processingTime: expect.any(Number),
                    metadata: expect.objectContaining({
                        encoding: 'base64',
                    }),
                });
                expect(result.data).toBeInstanceOf(Buffer);
            });
            it('should serialize Buffer to binary', async () => {
                const data = Buffer.from('Hello, World!', 'utf8');
                const encoding = 'hex';
                const result = await service.serializeToBinary(data, encoding);
                expect(result.format).toBe('binary');
                expect(result.metadata.encoding).toBe('hex');
            });
            it('should serialize object to binary', async () => {
                const data = { name: 'test', value: 123 };
                const encoding = 'base64';
                const result = await service.serializeToBinary(data, encoding);
                expect(result.format).toBe('binary');
                expect(result.originalSize).toBeGreaterThan(0);
            });
        });
        describe('deserializeFromBinary', () => {
            it('should deserialize binary data', async () => {
                const originalData = 'Hello, World!';
                const encoding = 'base64';
                const serialized = await service.serializeToBinary(originalData, encoding);
                const result = await service.deserializeFromBinary(serialized, encoding);
                expect(result).toBeInstanceOf(Buffer);
                expect(result.toString('utf8')).toBe(originalData);
            });
            it('should deserialize from Buffer', async () => {
                const originalBuffer = Buffer.from('Hello, World!', 'utf8');
                const result = await service.deserializeFromBinary(originalBuffer, 'utf8');
                expect(result).toEqual(originalBuffer);
            });
        });
    });
    describe('Compression', () => {
        describe('compressData', () => {
            it('should compress data using gzip', async () => {
                // Use larger data that will actually compress well
                const data = Buffer.from('Hello, World! '.repeat(100), 'utf8');
                const compressed = await service.compressData(data, 'gzip');
                expect(compressed).toBeInstanceOf(Buffer);
                expect(compressed.length).toBeLessThan(data.length);
            });
            it('should compress data using deflate', async () => {
                // Use larger data that will actually compress well
                const data = Buffer.from('Hello, World! '.repeat(100), 'utf8');
                const compressed = await service.compressData(data, 'deflate');
                expect(compressed).toBeInstanceOf(Buffer);
                expect(compressed.length).toBeLessThan(data.length);
            });
            it('should handle unsupported compression type', async () => {
                const data = Buffer.from('Hello, World!', 'utf8');
                await expect(service.compressData(data, 'unsupported')).rejects.toThrow('Unsupported compression type');
            });
        });
        describe('decompressData', () => {
            it('should decompress gzip data', async () => {
                const originalData = Buffer.from('Hello, World! This is a test string for compression.', 'utf8');
                const compressed = await service.compressData(originalData, 'gzip');
                const decompressed = await service.decompressData(compressed, 'gzip');
                expect(decompressed).toEqual(originalData);
            });
            it('should decompress deflate data', async () => {
                const originalData = Buffer.from('Hello, World! This is a test string for compression.', 'utf8');
                const compressed = await service.compressData(originalData, 'deflate');
                const decompressed = await service.decompressData(compressed, 'deflate');
                expect(decompressed).toEqual(originalData);
            });
            it('should handle unsupported decompression type', async () => {
                const data = Buffer.from('Hello, World!', 'utf8');
                await expect(service.decompressData(data, 'unsupported')).rejects.toThrow('Unsupported compression type');
            });
        });
    });
    describe('Batch Operations', () => {
        describe('batchSerialize', () => {
            it('should batch serialize multiple items', async () => {
                const items = [
                    { data: { name: 'item1', value: 1 }, format: 'json' },
                    { data: { name: 'item2', value: 2 }, format: 'json' },
                    { data: 'binary data', format: 'binary', options: { encoding: 'base64' } },
                ];
                const result = await service.batchSerialize(items);
                expect(result.results).toHaveLength(3);
                expect(result.errors).toHaveLength(0);
                expect(result.summary).toMatchObject({
                    total: 3,
                    successful: 3,
                    failed: 0,
                    totalOriginalSize: expect.any(Number),
                    totalCompressedSize: expect.any(Number),
                    overallCompressionRatio: expect.any(Number),
                    processingTime: expect.any(Number),
                });
                result.results.forEach(item => {
                    expect(item).toMatchObject({
                        data: expect.any(Buffer),
                        format: expect.any(String),
                        originalSize: expect.any(Number),
                        compressedSize: expect.any(Number),
                        processingTime: expect.any(Number),
                    });
                });
            });
            it('should handle batch serialization errors', async () => {
                const items = [
                    { data: { name: 'valid' }, format: 'json' },
                    { data: {}, format: 'unsupported' }, // Invalid format
                    { data: { name: 'valid2' }, format: 'json' },
                ];
                const result = await service.batchSerialize(items);
                expect(result.results).toHaveLength(3);
                expect(result.errors).toHaveLength(1);
                expect(result.summary).toMatchObject({
                    total: 3,
                    successful: 2,
                    failed: 1,
                });
                expect(result.errors[0]).toMatchObject({
                    index: 1,
                    error: expect.any(Error),
                });
            });
        });
        describe('batchDeserialize', () => {
            it('should batch deserialize multiple items', async () => {
                // First serialize some data
                const originalItems = [
                    { data: { name: 'item1', value: 1 }, format: 'json' },
                    { data: { name: 'item2', value: 2 }, format: 'json' },
                ];
                const serialized = await service.batchSerialize(originalItems);
                // Then deserialize
                const deserializeItems = serialized.results.map((item, index) => ({
                    data: item,
                    format: originalItems[index].format,
                }));
                const result = await service.batchDeserialize(deserializeItems);
                expect(result.results).toHaveLength(2);
                expect(result.errors).toHaveLength(0);
                expect(result.summary).toMatchObject({
                    total: 2,
                    successful: 2,
                    failed: 0,
                    processingTime: expect.any(Number),
                });
                expect(result.results[0]).toEqual({ name: 'item1', value: 1 });
                expect(result.results[1]).toEqual({ name: 'item2', value: 2 });
            });
            it('should handle batch deserialization errors', async () => {
                const items = [
                    { data: Buffer.from('{"valid": true}', 'utf8'), format: 'json' },
                    { data: Buffer.from('invalid json', 'utf8'), format: 'json' },
                    { data: Buffer.from('{"valid2": true}', 'utf8'), format: 'json' },
                ];
                const result = await service.batchDeserialize(items);
                expect(result.results).toHaveLength(3);
                expect(result.errors).toHaveLength(1);
                expect(result.summary).toMatchObject({
                    total: 3,
                    successful: 2,
                    failed: 1,
                });
                expect(result.errors[0]).toMatchObject({
                    index: 1,
                    error: expect.any(Error),
                });
            });
        });
    });
    describe('Configuration and Statistics', () => {
        describe('getSerializationStats', () => {
            it('should return serialization statistics', () => {
                const stats = service.getSerializationStats();
                expect(stats).toMatchObject({
                    compressionEnabled: true,
                    compressionLevel: 6,
                    compressionThreshold: 1024,
                    supportedFormats: ['json', 'protobuf', 'binary'],
                    supportedCompressionTypes: ['gzip', 'deflate'],
                    supportedEncodings: ['base64', 'hex', 'utf8'],
                });
            });
        });
        it('should use configured compression settings', () => {
            // Test that the service correctly reads configuration values
            const stats = service.getSerializationStats();
            // Verify default configuration is being used
            expect(stats.compressionEnabled).toBe(true);
            expect(stats.compressionLevel).toBe(6);
            expect(stats.compressionThreshold).toBe(1024);
            expect(stats.supportedFormats).toEqual(['json', 'protobuf', 'binary']);
            expect(stats.supportedCompressionTypes).toEqual(['gzip', 'deflate']);
            expect(stats.supportedEncodings).toEqual(['base64', 'hex', 'utf8']);
        });
    });
    describe('Edge Cases and Error Handling', () => {
        it('should handle null data', async () => {
            const nullResult = await service.serializeToJson(null);
            expect(nullResult.data).toBeInstanceOf(Buffer);
            const deserializedNull = await service.deserializeFromJson(nullResult);
            expect(deserializedNull).toBeNull();
        });
        it('should handle undefined data by treating it as null', async () => {
            // JSON.stringify(undefined) returns undefined, so we expect this to be handled gracefully
            // In practice, undefined should be converted to null for JSON serialization
            const data = { value: undefined, name: 'test' };
            const result = await service.serializeToJson(data);
            expect(result.data).toBeInstanceOf(Buffer);
            const deserialized = await service.deserializeFromJson(result);
            expect(deserialized).toEqual({ name: 'test' }); // undefined values are omitted in JSON
        });
        it('should handle empty data', async () => {
            const emptyObject = {};
            const emptyArray = [];
            const emptyString = '';
            const objectResult = await service.serializeToJson(emptyObject);
            const arrayResult = await service.serializeToJson(emptyArray);
            const stringResult = await service.serializeToBinary(emptyString);
            expect(objectResult.data).toBeInstanceOf(Buffer);
            expect(arrayResult.data).toBeInstanceOf(Buffer);
            expect(stringResult.data).toBeInstanceOf(Buffer);
            const deserializedObject = await service.deserializeFromJson(objectResult);
            const deserializedArray = await service.deserializeFromJson(arrayResult);
            expect(deserializedObject).toEqual({});
            expect(deserializedArray).toEqual([]);
        });
        it('should handle very large data', async () => {
            // Create a large object
            const largeData = {
                items: Array.from({ length: 10000 }, (_, i) => ({
                    id: i,
                    data: `Item ${i} with some additional data to make it larger`,
                })),
            };
            const result = await service.serializeToJson(largeData);
            expect(result.compressed).toBe(true);
            expect(result.compressionRatio).toBeGreaterThan(1);
            const deserialized = await service.deserializeFromJson(result);
            expect(deserialized.items).toHaveLength(10000);
            expect(deserialized.items[0]).toEqual(largeData.items[0]);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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