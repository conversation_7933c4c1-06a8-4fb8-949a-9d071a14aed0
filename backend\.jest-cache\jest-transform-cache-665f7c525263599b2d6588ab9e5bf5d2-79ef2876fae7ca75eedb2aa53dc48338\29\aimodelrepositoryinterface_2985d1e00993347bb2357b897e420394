477bccd93575b7590ce750389133550b
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AI_MODEL_REPOSITORY = void 0;
// Token for dependency injection
exports.AI_MODEL_REPOSITORY = Symbol('AI_MODEL_REPOSITORY');
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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