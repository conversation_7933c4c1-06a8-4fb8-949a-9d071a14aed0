23df3821ce52e75cf8e489c01216258c
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var MetricsAdapter_1;
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MetricsAdapter = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
/**
 * Metrics Adapter
 *
 * Provides abstraction layer for metrics collection and reporting.
 * Supports multiple metrics backends and provides standardized
 * metrics recording interface for AI operations.
 */
let MetricsAdapter = MetricsAdapter_1 = class MetricsAdapter {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(MetricsAdapter_1.name);
        this.metrics = new Map();
        this.counters = new Map();
        this.gauges = new Map();
    }
    /**
     * Records an AI operation metric
     */
    recordAiOperation(type, duration, requestId, metadata) {
        const metric = {
            name: 'ai_operation',
            type: 'histogram',
            value: duration,
            labels: {
                operation_type: type,
                request_id: requestId,
                ...metadata,
            },
            timestamp: new Date(),
        };
        this.recordMetric(metric);
        this.logger.debug(`Recorded AI operation metric: ${type}, duration: ${duration}ms`);
    }
    /**
     * Records model performance metrics
     */
    recordModelPerformance(modelId, metrics) {
        // Record accuracy
        this.recordMetric({
            name: 'model_accuracy',
            type: 'gauge',
            value: metrics.accuracy,
            labels: { model_id: modelId },
            timestamp: new Date(),
        });
        // Record latency
        this.recordMetric({
            name: 'model_latency',
            type: 'histogram',
            value: metrics.latency,
            labels: { model_id: modelId },
            timestamp: new Date(),
        });
        // Record throughput
        this.recordMetric({
            name: 'model_throughput',
            type: 'gauge',
            value: metrics.throughput,
            labels: { model_id: modelId },
            timestamp: new Date(),
        });
        // Record cost
        if (metrics.cost !== undefined) {
            this.recordMetric({
                name: 'model_cost',
                type: 'counter',
                value: metrics.cost,
                labels: { model_id: modelId },
                timestamp: new Date(),
            });
        }
        this.logger.debug(`Recorded model performance metrics for: ${modelId}`);
    }
    /**
     * Records provider health metrics
     */
    recordProviderHealth(providerId, health) {
        // Record availability
        this.recordMetric({
            name: 'provider_availability',
            type: 'gauge',
            value: health.available ? 1 : 0,
            labels: { provider_id: providerId },
            timestamp: new Date(),
        });
        // Record response time
        this.recordMetric({
            name: 'provider_response_time',
            type: 'histogram',
            value: health.responseTime,
            labels: { provider_id: providerId },
            timestamp: new Date(),
        });
        // Record error rate
        this.recordMetric({
            name: 'provider_error_rate',
            type: 'gauge',
            value: health.errorRate,
            labels: { provider_id: providerId },
            timestamp: new Date(),
        });
        this.logger.debug(`Recorded provider health metrics for: ${providerId}`);
    }
    /**
     * Records cache performance metrics
     */
    recordCacheMetrics(cacheType, operation, duration) {
        // Record cache operation counter
        this.incrementCounter(`cache_operations_total`, {
            cache_type: cacheType,
            operation,
        });
        // Record cache operation duration if provided
        if (duration !== undefined) {
            this.recordMetric({
                name: 'cache_operation_duration',
                type: 'histogram',
                value: duration,
                labels: {
                    cache_type: cacheType,
                    operation,
                },
                timestamp: new Date(),
            });
        }
        this.logger.debug(`Recorded cache metric: ${cacheType} ${operation}`);
    }
    /**
     * Records pipeline execution metrics
     */
    recordPipelineExecution(pipelineId, status, duration, stageCount) {
        // Record pipeline status counter
        this.incrementCounter('pipeline_executions_total', {
            pipeline_id: pipelineId,
            status,
        });
        // Record pipeline duration if completed
        if (duration !== undefined) {
            this.recordMetric({
                name: 'pipeline_duration',
                type: 'histogram',
                value: duration,
                labels: { pipeline_id: pipelineId },
                timestamp: new Date(),
            });
        }
        // Record stage count if provided
        if (stageCount !== undefined) {
            this.recordMetric({
                name: 'pipeline_stages',
                type: 'gauge',
                value: stageCount,
                labels: { pipeline_id: pipelineId },
                timestamp: new Date(),
            });
        }
        this.logger.debug(`Recorded pipeline execution metric: ${pipelineId} ${status}`);
    }
    /**
     * Records error metrics
     */
    recordError(errorType, component, severity = 'medium', metadata) {
        this.incrementCounter('errors_total', {
            error_type: errorType,
            component,
            severity,
            ...metadata,
        });
        this.logger.debug(`Recorded error metric: ${errorType} in ${component}`);
    }
    /**
     * Records custom metric
     */
    recordCustomMetric(name, value, type = 'gauge', labels) {
        this.recordMetric({
            name,
            type,
            value,
            labels: labels || {},
            timestamp: new Date(),
        });
        this.logger.debug(`Recorded custom metric: ${name} = ${value}`);
    }
    /**
     * Gets current metric values
     */
    getMetrics() {
        const snapshot = {
            counters: Object.fromEntries(this.counters),
            gauges: Object.fromEntries(this.gauges),
            histograms: {},
            timestamp: new Date(),
        };
        // Calculate histogram statistics
        for (const [name, entries] of this.metrics) {
            if (entries.length > 0 && entries[0].type === 'histogram') {
                const values = entries.map(e => e.value).sort((a, b) => a - b);
                snapshot.histograms[name] = {
                    count: values.length,
                    sum: values.reduce((sum, v) => sum + v, 0),
                    min: values[0],
                    max: values[values.length - 1],
                    avg: values.reduce((sum, v) => sum + v, 0) / values.length,
                    p50: this.percentile(values, 0.5),
                    p95: this.percentile(values, 0.95),
                    p99: this.percentile(values, 0.99),
                };
            }
        }
        return snapshot;
    }
    /**
     * Gets metrics for a specific time range
     */
    getMetricsInRange(startTime, endTime) {
        const result = [];
        for (const entries of this.metrics.values()) {
            const filtered = entries.filter(entry => entry.timestamp >= startTime && entry.timestamp <= endTime);
            result.push(...filtered);
        }
        return result.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    }
    /**
     * Clears old metrics to prevent memory leaks
     */
    cleanup(olderThan = new Date(Date.now() - 24 * 60 * 60 * 1000)) {
        let totalRemoved = 0;
        for (const [name, entries] of this.metrics) {
            const originalLength = entries.length;
            const filtered = entries.filter(entry => entry.timestamp > olderThan);
            if (filtered.length !== originalLength) {
                this.metrics.set(name, filtered);
                totalRemoved += originalLength - filtered.length;
            }
        }
        if (totalRemoved > 0) {
            this.logger.debug(`Cleaned up ${totalRemoved} old metric entries`);
        }
    }
    /**
     * Exports metrics in Prometheus format
     */
    exportPrometheusMetrics() {
        const lines = [];
        const snapshot = this.getMetrics();
        // Export counters
        for (const [name, value] of Object.entries(snapshot.counters)) {
            lines.push(`# TYPE ${name} counter`);
            lines.push(`${name} ${value}`);
        }
        // Export gauges
        for (const [name, value] of Object.entries(snapshot.gauges)) {
            lines.push(`# TYPE ${name} gauge`);
            lines.push(`${name} ${value}`);
        }
        // Export histograms
        for (const [name, stats] of Object.entries(snapshot.histograms)) {
            lines.push(`# TYPE ${name} histogram`);
            lines.push(`${name}_count ${stats.count}`);
            lines.push(`${name}_sum ${stats.sum}`);
            lines.push(`${name}_bucket{le="0.1"} ${this.getBucketCount(name, 0.1)}`);
            lines.push(`${name}_bucket{le="0.5"} ${this.getBucketCount(name, 0.5)}`);
            lines.push(`${name}_bucket{le="1.0"} ${this.getBucketCount(name, 1.0)}`);
            lines.push(`${name}_bucket{le="5.0"} ${this.getBucketCount(name, 5.0)}`);
            lines.push(`${name}_bucket{le="+Inf"} ${stats.count}`);
        }
        return lines.join('\n');
    }
    // Private helper methods
    recordMetric(metric) {
        if (!this.metrics.has(metric.name)) {
            this.metrics.set(metric.name, []);
        }
        const entries = this.metrics.get(metric.name);
        entries.push(metric);
        // Keep only recent entries to prevent memory leaks
        if (entries.length > 10000) {
            entries.splice(0, entries.length - 10000);
        }
        // Update counters and gauges
        if (metric.type === 'counter') {
            const key = this.getMetricKey(metric.name, metric.labels);
            this.counters.set(key, (this.counters.get(key) || 0) + metric.value);
        }
        else if (metric.type === 'gauge') {
            const key = this.getMetricKey(metric.name, metric.labels);
            this.gauges.set(key, metric.value);
        }
    }
    incrementCounter(name, labels = {}) {
        const key = this.getMetricKey(name, labels);
        this.counters.set(key, (this.counters.get(key) || 0) + 1);
    }
    getMetricKey(name, labels) {
        const labelPairs = Object.entries(labels)
            .sort(([a], [b]) => a.localeCompare(b))
            .map(([key, value]) => `${key}="${value}"`)
            .join(',');
        return labelPairs ? `${name}{${labelPairs}}` : name;
    }
    percentile(sortedValues, p) {
        if (sortedValues.length === 0)
            return 0;
        const index = Math.ceil(sortedValues.length * p) - 1;
        return sortedValues[Math.max(0, index)];
    }
    getBucketCount(metricName, threshold) {
        const entries = this.metrics.get(metricName) || [];
        return entries.filter(entry => entry.value <= threshold).length;
    }
};
exports.MetricsAdapter = MetricsAdapter;
exports.MetricsAdapter = MetricsAdapter = MetricsAdapter_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], MetricsAdapter);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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