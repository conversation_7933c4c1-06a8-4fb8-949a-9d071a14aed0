{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\value-objects\\accuracy-score.value-object.ts", "mappings": ";;;AAAA,iGAA4F;AAE5F;;;;;;GAMG;AACH,MAAa,aAAc,SAAQ,mCAAuB;IAUxD,YAAY,KAAa;QACvB,KAAK,CAAC,KAAK,CAAC,CAAC;IACf,CAAC;IAES,QAAQ;QAChB,KAAK,CAAC,QAAQ,EAAE,CAAC;QAEjB,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC,YAAY,EAAE,CAAC;YAC7C,MAAM,IAAI,KAAK,CAAC,sCAAsC,aAAa,CAAC,YAAY,EAAE,CAAC,CAAC;QACtF,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC,YAAY,EAAE,CAAC;YAC7C,MAAM,IAAI,KAAK,CAAC,yCAAyC,aAAa,CAAC,YAAY,EAAE,CAAC,CAAC;QACzF,CAAC;IACH,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,eAAe,CAAC,OAAe,EAAE,KAAa;QAC1D,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,CAAC;QACD,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QACD,IAAI,OAAO,GAAG,KAAK,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;QAED,OAAO,IAAI,aAAa,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,cAAc,CAAC,UAAkB;QAC7C,IAAI,UAAU,GAAG,CAAC,IAAI,UAAU,GAAG,GAAG,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QACD,OAAO,IAAI,aAAa,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,OAAO;QACnB,OAAO,IAAI,aAAa,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,IAAI;QAChB,OAAO,IAAI,aAAa,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,MAAM;QAClB,OAAO,IAAI,aAAa,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,IAAI,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC,cAAc,EAAE,CAAC;YAC/C,OAAO,MAAM,CAAC;QAChB,CAAC;aAAM,IAAI,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC,cAAc,EAAE,CAAC;YACtD,OAAO,MAAM,CAAC;QAChB,CAAC;aAAM,IAAI,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC,cAAc,EAAE,CAAC;YACtD,OAAO,MAAM,CAAC;QAChB,CAAC;aAAM,IAAI,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC,mBAAmB,EAAE,CAAC;YAC3D,OAAO,WAAW,CAAC;QACrB,CAAC;aAAM,CAAC;YACN,OAAO,WAAW,CAAC;QACrB,CAAC;IACH,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC,cAAc,CAAC;IACpD,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO,IAAI,CAAC,MAAM,IAAI,aAAa,CAAC,cAAc,IAAI,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC,cAAc,CAAC;IACnG,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO,IAAI,CAAC,MAAM,IAAI,aAAa,CAAC,cAAc,CAAC;IACrD,CAAC;IAED;;OAEG;IACI,WAAW;QAChB,OAAO,IAAI,CAAC,MAAM,IAAI,aAAa,CAAC,mBAAmB,CAAC;IAC1D,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,SAAiB;QACrC,OAAO,IAAI,CAAC,MAAM,IAAI,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACI,YAAY;QACjB,OAAO,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,WAAmB,CAAC;QAC5C,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC;IACrD,CAAC;IAED;;OAEG;IACI,YAAY;QACjB,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,sBAAsB;QAC3B,OAAO,IAAI,CAAC,YAAY,EAAE,GAAG,GAAG,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,QAAuB;QAC5C,OAAO,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,uBAAuB,CAAC,QAAuB;QACpD,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;QAC1C,CAAC;QACD,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;IAC3D,CAAC;IAED;;OAEG;IACI,yBAAyB,CAAC,KAAoB,EAAE,YAAoB,IAAI;QAC7E,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC;IAClD,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,KAAoB,EAAE,SAAiB,GAAG;QAC3D,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;QACzE,OAAO,IAAI,aAAa,CAAC,aAAa,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,KAAoB;QACxC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,KAAoB;QACvC,OAAO,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;IACpC,CAAC;IAED;;OAEG;IACI,UAAU,CAAC,KAAoB;QACpC,OAAO,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;IACpC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,WAAmB,CAAC;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QACtC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC;QAC1D,OAAO,IAAI,aAAa,CAAC,OAAO,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,OAAO,GAAG,IAAI,CAAC,kBAAkB,EAAE,KAAK,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC;IAC7D,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,UAAU,EAAE,IAAI,CAAC,YAAY,EAAE;YAC/B,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;YACtB,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE;YAC9B,mBAAmB,EAAE,IAAI,CAAC,sBAAsB,EAAE;YAClD,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE;YACrB,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE;SAChC,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,QAAQ,CAAC,IAAyB;QAC9C,OAAO,IAAI,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,OAAO,CAAC,MAAuB;QAC3C,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC;QAED,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjE,OAAO,IAAI,aAAa,CAAC,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,eAAe,CAAC,MAAuB,EAAE,OAAiB;QACtE,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;QAED,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,EAAE,CAAC,CAAC,CAAC;QACrE,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACjD,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;QACjG,OAAO,IAAI,aAAa,CAAC,WAAW,GAAG,WAAW,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,GAAG,CAAC,MAAuB;QACvC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;QAChE,OAAO,IAAI,aAAa,CAAC,QAAQ,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,GAAG,CAAC,MAAuB;QACvC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;QAChE,OAAO,IAAI,aAAa,CAAC,QAAQ,CAAC,CAAC;IACrC,CAAC;;AA5TH,sCA6TC;AA5TwB,0BAAY,GAAG,GAAG,CAAC;AACnB,0BAAY,GAAG,GAAG,CAAC;AAE1C,4BAA4B;AACL,4BAAc,GAAG,GAAG,CAAC;AACrB,4BAAc,GAAG,GAAG,CAAC;AACrB,4BAAc,GAAG,GAAG,CAAC;AACrB,iCAAmB,GAAG,IAAI,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\value-objects\\accuracy-score.value-object.ts"], "sourcesContent": ["import { BaseValueObject } from '../../../../shared-kernel/value-objects/base-value-object';\r\n\r\n/**\r\n * Accuracy Score Value Object\r\n * \r\n * Represents an accuracy score for AI model performance evaluation.\r\n * Ensures the score is within valid bounds (0.0 to 1.0) and provides\r\n * utility methods for accuracy level classification and comparison.\r\n */\r\nexport class AccuracyScore extends BaseValueObject<number> {\r\n  public static readonly MIN_ACCURACY = 0.0;\r\n  public static readonly MAX_ACCURACY = 1.0;\r\n  \r\n  // Accuracy level thresholds\r\n  public static readonly POOR_THRESHOLD = 0.5;\r\n  public static readonly FAIR_THRESHOLD = 0.7;\r\n  public static readonly GOOD_THRESHOLD = 0.8;\r\n  public static readonly EXCELLENT_THRESHOLD = 0.95;\r\n\r\n  constructor(value: number) {\r\n    super(value);\r\n  }\r\n\r\n  protected validate(): void {\r\n    super.validate();\r\n    \r\n    if (typeof this._value !== 'number') {\r\n      throw new Error('Accuracy score must be a number');\r\n    }\r\n\r\n    if (isNaN(this._value)) {\r\n      throw new Error('Accuracy score cannot be NaN');\r\n    }\r\n\r\n    if (!isFinite(this._value)) {\r\n      throw new Error('Accuracy score must be finite');\r\n    }\r\n\r\n    if (this._value < AccuracyScore.MIN_ACCURACY) {\r\n      throw new Error(`Accuracy score cannot be less than ${AccuracyScore.MIN_ACCURACY}`);\r\n    }\r\n\r\n    if (this._value > AccuracyScore.MAX_ACCURACY) {\r\n      throw new Error(`Accuracy score cannot be greater than ${AccuracyScore.MAX_ACCURACY}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Creates an accuracy score from correct and total predictions\r\n   */\r\n  public static fromPredictions(correct: number, total: number): AccuracyScore {\r\n    if (total <= 0) {\r\n      throw new Error('Total predictions must be greater than zero');\r\n    }\r\n    if (correct < 0) {\r\n      throw new Error('Correct predictions cannot be negative');\r\n    }\r\n    if (correct > total) {\r\n      throw new Error('Correct predictions cannot exceed total predictions');\r\n    }\r\n    \r\n    return new AccuracyScore(correct / total);\r\n  }\r\n\r\n  /**\r\n   * Creates an accuracy score from a percentage (0-100)\r\n   */\r\n  public static fromPercentage(percentage: number): AccuracyScore {\r\n    if (percentage < 0 || percentage > 100) {\r\n      throw new Error('Percentage must be between 0 and 100');\r\n    }\r\n    return new AccuracyScore(percentage / 100);\r\n  }\r\n\r\n  /**\r\n   * Creates a perfect accuracy score\r\n   */\r\n  public static perfect(): AccuracyScore {\r\n    return new AccuracyScore(AccuracyScore.MAX_ACCURACY);\r\n  }\r\n\r\n  /**\r\n   * Creates a zero accuracy score\r\n   */\r\n  public static zero(): AccuracyScore {\r\n    return new AccuracyScore(AccuracyScore.MIN_ACCURACY);\r\n  }\r\n\r\n  /**\r\n   * Creates a random accuracy score (for baseline comparison)\r\n   */\r\n  public static random(): AccuracyScore {\r\n    return new AccuracyScore(0.5);\r\n  }\r\n\r\n  /**\r\n   * Gets the accuracy level as a string\r\n   */\r\n  public getLevel(): 'poor' | 'fair' | 'good' | 'very_good' | 'excellent' {\r\n    if (this._value < AccuracyScore.POOR_THRESHOLD) {\r\n      return 'poor';\r\n    } else if (this._value < AccuracyScore.FAIR_THRESHOLD) {\r\n      return 'fair';\r\n    } else if (this._value < AccuracyScore.GOOD_THRESHOLD) {\r\n      return 'good';\r\n    } else if (this._value < AccuracyScore.EXCELLENT_THRESHOLD) {\r\n      return 'very_good';\r\n    } else {\r\n      return 'excellent';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Checks if the accuracy is poor\r\n   */\r\n  public isPoor(): boolean {\r\n    return this._value < AccuracyScore.POOR_THRESHOLD;\r\n  }\r\n\r\n  /**\r\n   * Checks if the accuracy is fair\r\n   */\r\n  public isFair(): boolean {\r\n    return this._value >= AccuracyScore.POOR_THRESHOLD && this._value < AccuracyScore.FAIR_THRESHOLD;\r\n  }\r\n\r\n  /**\r\n   * Checks if the accuracy is good\r\n   */\r\n  public isGood(): boolean {\r\n    return this._value >= AccuracyScore.GOOD_THRESHOLD;\r\n  }\r\n\r\n  /**\r\n   * Checks if the accuracy is excellent\r\n   */\r\n  public isExcellent(): boolean {\r\n    return this._value >= AccuracyScore.EXCELLENT_THRESHOLD;\r\n  }\r\n\r\n  /**\r\n   * Checks if the accuracy meets a minimum threshold\r\n   */\r\n  public meetsThreshold(threshold: number): boolean {\r\n    return this._value >= threshold;\r\n  }\r\n\r\n  /**\r\n   * Gets the accuracy as a percentage\r\n   */\r\n  public toPercentage(): number {\r\n    return this._value * 100;\r\n  }\r\n\r\n  /**\r\n   * Gets the accuracy as a percentage string\r\n   */\r\n  public toPercentageString(decimals: number = 1): string {\r\n    return `${(this._value * 100).toFixed(decimals)}%`;\r\n  }\r\n\r\n  /**\r\n   * Gets the error rate (1 - accuracy)\r\n   */\r\n  public getErrorRate(): number {\r\n    return 1 - this._value;\r\n  }\r\n\r\n  /**\r\n   * Gets the error rate as a percentage\r\n   */\r\n  public getErrorRatePercentage(): number {\r\n    return this.getErrorRate() * 100;\r\n  }\r\n\r\n  /**\r\n   * Calculates the improvement over another accuracy score\r\n   */\r\n  public improvementOver(baseline: AccuracyScore): number {\r\n    return this._value - baseline._value;\r\n  }\r\n\r\n  /**\r\n   * Calculates the relative improvement over another accuracy score\r\n   */\r\n  public relativeImprovementOver(baseline: AccuracyScore): number {\r\n    if (baseline._value === 0) {\r\n      return this._value === 0 ? 0 : Infinity;\r\n    }\r\n    return (this._value - baseline._value) / baseline._value;\r\n  }\r\n\r\n  /**\r\n   * Checks if this accuracy is significantly better than another\r\n   */\r\n  public isSignificantlyBetterThan(other: AccuracyScore, threshold: number = 0.05): boolean {\r\n    return this.improvementOver(other) >= threshold;\r\n  }\r\n\r\n  /**\r\n   * Combines this accuracy with another using weighted average\r\n   */\r\n  public combineWith(other: AccuracyScore, weight: number = 0.5): AccuracyScore {\r\n    if (weight < 0 || weight > 1) {\r\n      throw new Error('Weight must be between 0 and 1');\r\n    }\r\n    \r\n    const combinedValue = this._value * weight + other._value * (1 - weight);\r\n    return new AccuracyScore(combinedValue);\r\n  }\r\n\r\n  /**\r\n   * Gets the absolute difference between this and another accuracy score\r\n   */\r\n  public differenceFrom(other: AccuracyScore): number {\r\n    return Math.abs(this._value - other._value);\r\n  }\r\n\r\n  /**\r\n   * Checks if this accuracy is greater than another\r\n   */\r\n  public isGreaterThan(other: AccuracyScore): boolean {\r\n    return this._value > other._value;\r\n  }\r\n\r\n  /**\r\n   * Checks if this accuracy is less than another\r\n   */\r\n  public isLessThan(other: AccuracyScore): boolean {\r\n    return this._value < other._value;\r\n  }\r\n\r\n  /**\r\n   * Rounds the accuracy to a specified number of decimal places\r\n   */\r\n  public round(decimals: number = 3): AccuracyScore {\r\n    const factor = Math.pow(10, decimals);\r\n    const rounded = Math.round(this._value * factor) / factor;\r\n    return new AccuracyScore(rounded);\r\n  }\r\n\r\n  /**\r\n   * Converts to a human-readable string\r\n   */\r\n  public toString(): string {\r\n    return `${this.toPercentageString()} (${this.getLevel()})`;\r\n  }\r\n\r\n  /**\r\n   * Converts to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      value: this._value,\r\n      percentage: this.toPercentage(),\r\n      level: this.getLevel(),\r\n      errorRate: this.getErrorRate(),\r\n      errorRatePercentage: this.getErrorRatePercentage(),\r\n      isGood: this.isGood(),\r\n      isExcellent: this.isExcellent(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Creates an AccuracyScore from JSON\r\n   */\r\n  public static fromJSON(json: Record<string, any>): AccuracyScore {\r\n    return new AccuracyScore(json.value);\r\n  }\r\n\r\n  /**\r\n   * Calculates the average of multiple accuracy scores\r\n   */\r\n  public static average(scores: AccuracyScore[]): AccuracyScore {\r\n    if (scores.length === 0) {\r\n      throw new Error('Cannot calculate average of empty array');\r\n    }\r\n    \r\n    const sum = scores.reduce((acc, score) => acc + score._value, 0);\r\n    return new AccuracyScore(sum / scores.length);\r\n  }\r\n\r\n  /**\r\n   * Calculates the weighted average of multiple accuracy scores\r\n   */\r\n  public static weightedAverage(scores: AccuracyScore[], weights: number[]): AccuracyScore {\r\n    if (scores.length === 0) {\r\n      throw new Error('Cannot calculate weighted average of empty array');\r\n    }\r\n    \r\n    if (scores.length !== weights.length) {\r\n      throw new Error('Scores and weights arrays must have the same length');\r\n    }\r\n    \r\n    const totalWeight = weights.reduce((acc, weight) => acc + weight, 0);\r\n    if (totalWeight === 0) {\r\n      throw new Error('Total weight cannot be zero');\r\n    }\r\n    \r\n    const weightedSum = scores.reduce((acc, score, index) => acc + score._value * weights[index], 0);\r\n    return new AccuracyScore(weightedSum / totalWeight);\r\n  }\r\n\r\n  /**\r\n   * Gets the maximum accuracy from an array\r\n   */\r\n  public static max(scores: AccuracyScore[]): AccuracyScore {\r\n    if (scores.length === 0) {\r\n      throw new Error('Cannot find maximum of empty array');\r\n    }\r\n    \r\n    const maxValue = Math.max(...scores.map(score => score._value));\r\n    return new AccuracyScore(maxValue);\r\n  }\r\n\r\n  /**\r\n   * Gets the minimum accuracy from an array\r\n   */\r\n  public static min(scores: AccuracyScore[]): AccuracyScore {\r\n    if (scores.length === 0) {\r\n      throw new Error('Cannot find minimum of empty array');\r\n    }\r\n    \r\n    const minValue = Math.min(...scores.map(score => score._value));\r\n    return new AccuracyScore(minValue);\r\n  }\r\n}"], "version": 3}