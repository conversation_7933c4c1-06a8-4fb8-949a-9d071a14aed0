{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\entities\\workflow-execution.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCASiB;AACjB,iFAAsE;AACtE,2FAA+E;AAE/E;;;;;GAKG;AAOI,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IAuJ5B,sBAAsB;IACtB,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC;IACnC,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW,CAAC;IACrC,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,CAAC;IAClC,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW,CAAC;IACrC,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC;IACnC,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC/E,CAAC;IAED,IAAI,QAAQ;QACV,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QACpC,OAAO,CAAC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC;IACvD,CAAC;IAED,IAAI,aAAa;QACf,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO,CAAC,CAAC;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,IAAI,EAAE,CAAC;QAC/C,OAAO,OAAO,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;IACtD,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC;IACvD,CAAC;IAED,IAAI,WAAW;QACb,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QACpC,OAAO,CAAC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO;YACL,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,cAAc,EAAE,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvF,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,UAAU,EAAE,IAAI,CAAC,UAAU;SAC5B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,IAAI,CAAC,KAAK,EAAE,KAAK,IAAI,IAAI,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,KAAK,EAAE,IAAI,IAAI,IAAI,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO,IAAI,CAAC,KAAK,EAAE,YAAY,IAAI,IAAI,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,OAAO,EAAE,eAAe,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,OAAO,EAAE,YAAY,IAAI,KAAK,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,IAAI,CAAC,OAAO,EAAE,cAAc,IAAI,IAAI,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,OAAO,EAAE,gBAAgB,IAAI,EAAE,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,IAAI,CAAC,OAAO,EAAE,SAAS,IAAI,EAAE,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,GAAW,EAAE,KAAU;QACjC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QACpB,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAC5B,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,EAAE,CAAC;QAC9B,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,GAAW;QACrB,OAAO,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,GAAG,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,GAAW;QAChB,OAAO,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,IAAI,KAAK,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,IAAI,KAAK,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,YAAY,IAAI,KAAK,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,MAAc;QAC7B,OAAO,IAAI,CAAC,QAAQ,EAAE,aAAa,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,OAAO,IAAI,CAAC,QAAQ,EAAE,iBAAiB,IAAI,EAAE,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,WAAmB;QACtC,OAAO,IAAI,CAAC,QAAQ,EAAE,iBAAiB,EAAE,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,0BAA0B;QACxB,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACvC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAC1D,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;QAEzC,IAAI,YAAY,KAAK,CAAC,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,kBAAkB,GAAG,WAAW,GAAG,YAAY,CAAC;QACtD,MAAM,aAAa,GAAG,kBAAkB,GAAG,WAAW,CAAC;QAEvD,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,aAAa,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,aAAqB,CAAC;QAChC,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;YACpB,KAAK,SAAS,CAAC,CAAC,OAAO,SAAS,CAAC;YACjC,KAAK,SAAS,CAAC,CAAC,OAAO,SAAS,CAAC;YACjC,KAAK,WAAW,CAAC,CAAC,OAAO,WAAW,CAAC;YACrC,KAAK,QAAQ,CAAC,CAAC,OAAO,QAAQ,CAAC;YAC/B,KAAK,WAAW,CAAC,CAAC,OAAO,WAAW,CAAC;YACrC,KAAK,SAAS,CAAC,CAAC,OAAO,WAAW,CAAC;YACnC,OAAO,CAAC,CAAC,OAAO,SAAS,CAAC;QAC5B,CAAC;IACH,CAAC;CACF,CAAA;AA1YY,8CAAiB;AAE5B;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;6CACpB;AAIX;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;IAC/B,IAAA,eAAK,GAAE;;qDACW;AAInB;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;IAC9C,IAAA,eAAK,GAAE;;iDACyE;AAGjF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gDA8BvD;AAGF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDAkBzD;AAGF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAClD,MAAM,oBAAN,MAAM;iDAAc;AAG5B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gDAC1C;AAId;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC;IAChC,IAAA,eAAK,GAAE;;sDACY;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;;sDACS;AAG7D;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACnD,MAAM,oBAAN,MAAM;sDAAc;AAIjC;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACjE,IAAA,eAAK,GAAE;kDACG,IAAI,oBAAJ,IAAI;oDAAC;AAIhB;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACnE,IAAA,eAAK,GAAE;kDACK,IAAI,oBAAJ,IAAI;sDAAC;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDAC5C;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;yDACzB;AAGvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;qDACzB;AAGnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sDAC7B;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;;mDACC;AAGjD;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACvD,IAAI,oBAAJ,IAAI;oDAAC;AAGhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;qDACzB;AAInB;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAClD,IAAA,eAAK,GAAE;;wDACc;AAItB;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC5C,IAAA,eAAK,GAAE;;kDACQ;AAGhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDAqB1D;AAGF;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;oDAAC;AAKhB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,mDAAoB,EAAE,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;IACtE,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;kDAC1B,mDAAoB,oBAApB,mDAAoB;mDAAC;AAG/B;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,4DAAwB,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC;;mDACnC;4BArJ1B,iBAAiB;IAN7B,IAAA,gBAAM,EAAC,qBAAqB,CAAC;IAC7B,IAAA,eAAK,EAAC,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;IAC/B,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,aAAa,CAAC,CAAC;IACtB,IAAA,eAAK,EAAC,CAAC,WAAW,CAAC,CAAC;IACpB,IAAA,eAAK,EAAC,CAAC,aAAa,CAAC,CAAC;GACV,iBAAiB,CA0Y7B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\entities\\workflow-execution.entity.ts"], "sourcesContent": ["import {\r\n  <PERSON><PERSON><PERSON>,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  Index,\r\n  ManyToOne,\r\n  OneToMany,\r\n  JoinColumn,\r\n} from 'typeorm';\r\nimport { NotificationWorkflow } from './notification-workflow.entity';\r\nimport { WorkflowExecutionContext } from './workflow-execution-context.entity';\r\n\r\n/**\r\n * Workflow Execution Entity\r\n * \r\n * Represents a single execution instance of a notification workflow\r\n * with comprehensive tracking and state management.\r\n */\r\n@Entity('workflow_executions')\r\n@Index(['workflowId', 'status'])\r\n@Index(['status'])\r\n@Index(['triggeredBy'])\r\n@Index(['startedAt'])\r\n@Index(['completedAt'])\r\nexport class WorkflowExecution {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  @Column({ name: 'workflow_id' })\r\n  @Index()\r\n  workflowId: string;\r\n\r\n  @Column({ name: 'status', default: 'pending' })\r\n  @Index()\r\n  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled' | 'timeout';\r\n\r\n  @Column({ name: 'input', type: 'jsonb', nullable: true })\r\n  input: {\r\n    alert?: {\r\n      id?: string;\r\n      severity?: string;\r\n      priority?: number;\r\n      message?: string;\r\n      source?: string;\r\n      timestamp?: string;\r\n      metadata?: Record<string, any>;\r\n    };\r\n    rule?: {\r\n      id?: string;\r\n      name?: string;\r\n      conditions?: Array<{\r\n        metric: string;\r\n        operator: string;\r\n        threshold: number;\r\n      }>;\r\n      metadata?: Record<string, any>;\r\n    };\r\n    user?: {\r\n      id?: string;\r\n      email?: string;\r\n      role?: string;\r\n      team?: string;\r\n      preferences?: Record<string, any>;\r\n    };\r\n    metricValues?: Record<string, number>;\r\n    customData?: Record<string, any>;\r\n  };\r\n\r\n  @Column({ name: 'context', type: 'jsonb', nullable: true })\r\n  context: {\r\n    environment?: string;\r\n    region?: string;\r\n    timezone?: string;\r\n    businessHours?: boolean;\r\n    oncallSchedule?: {\r\n      primary?: string;\r\n      secondary?: string;\r\n      manager?: string;\r\n    };\r\n    escalationLevel?: number;\r\n    acknowledged?: boolean;\r\n    suppressions?: string[];\r\n    correlatedAlerts?: string[];\r\n    parentExecution?: string;\r\n    retryCount?: number;\r\n    variables?: Record<string, any>;\r\n  };\r\n\r\n  @Column({ name: 'result', type: 'jsonb', nullable: true })\r\n  result: Record<string, any>;\r\n\r\n  @Column({ name: 'error', type: 'text', nullable: true })\r\n  error: string;\r\n\r\n  @Column({ name: 'triggered_by' })\r\n  @Index()\r\n  triggeredBy: string;\r\n\r\n  @Column({ name: 'trigger_type', default: 'manual' })\r\n  triggerType: 'manual' | 'cron' | 'event' | 'webhook' | 'api';\r\n\r\n  @Column({ name: 'trigger_data', type: 'jsonb', nullable: true })\r\n  triggerData: Record<string, any>;\r\n\r\n  @Column({ name: 'started_at', type: 'timestamp', nullable: true })\r\n  @Index()\r\n  startedAt: Date;\r\n\r\n  @Column({ name: 'completed_at', type: 'timestamp', nullable: true })\r\n  @Index()\r\n  completedAt: Date;\r\n\r\n  @Column({ name: 'duration', type: 'bigint', nullable: true })\r\n  duration: number; // Duration in milliseconds\r\n\r\n  @Column({ name: 'steps_completed', default: 0 })\r\n  stepsCompleted: number;\r\n\r\n  @Column({ name: 'total_steps', default: 0 })\r\n  totalSteps: number;\r\n\r\n  @Column({ name: 'current_step', nullable: true })\r\n  currentStep: string;\r\n\r\n  @Column({ name: 'priority', default: 'medium' })\r\n  priority: 'low' | 'medium' | 'high' | 'critical';\r\n\r\n  @Column({ name: 'timeout_at', type: 'timestamp', nullable: true })\r\n  timeoutAt: Date;\r\n\r\n  @Column({ name: 'retry_count', default: 0 })\r\n  retryCount: number;\r\n\r\n  @Column({ name: 'correlation_id', nullable: true })\r\n  @Index()\r\n  correlationId: string;\r\n\r\n  @Column({ name: 'trace_id', nullable: true })\r\n  @Index()\r\n  traceId: string;\r\n\r\n  @Column({ name: 'metadata', type: 'jsonb', nullable: true })\r\n  metadata: {\r\n    executionOptions?: Record<string, any>;\r\n    stepOverrides?: Record<string, Record<string, any>>;\r\n    templateVariables?: Record<string, any>;\r\n    integrationConfig?: Record<string, any>;\r\n    callbacks?: {\r\n      webhookUrl?: string;\r\n      events?: string[];\r\n      authentication?: Record<string, any>;\r\n    };\r\n    testing?: {\r\n      testMode?: boolean;\r\n      validateOnly?: boolean;\r\n      mockExternalCalls?: boolean;\r\n    };\r\n    security?: {\r\n      runAsUser?: string;\r\n      permissions?: string[];\r\n      securityContext?: Record<string, any>;\r\n    };\r\n  };\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  // Relations\r\n  @ManyToOne(() => NotificationWorkflow, workflow => workflow.executions)\r\n  @JoinColumn({ name: 'workflow_id' })\r\n  workflow: NotificationWorkflow;\r\n\r\n  @OneToMany(() => WorkflowExecutionContext, context => context.execution)\r\n  contexts: WorkflowExecutionContext[];\r\n\r\n  // Computed properties\r\n  get isRunning(): boolean {\r\n    return this.status === 'running';\r\n  }\r\n\r\n  get isCompleted(): boolean {\r\n    return this.status === 'completed';\r\n  }\r\n\r\n  get isFailed(): boolean {\r\n    return this.status === 'failed';\r\n  }\r\n\r\n  get isCancelled(): boolean {\r\n    return this.status === 'cancelled';\r\n  }\r\n\r\n  get isTimedOut(): boolean {\r\n    return this.status === 'timeout';\r\n  }\r\n\r\n  get isFinished(): boolean {\r\n    return ['completed', 'failed', 'cancelled', 'timeout'].includes(this.status);\r\n  }\r\n\r\n  get progress(): number {\r\n    if (this.totalSteps === 0) return 0;\r\n    return (this.stepsCompleted / this.totalSteps) * 100;\r\n  }\r\n\r\n  get executionTime(): number {\r\n    if (!this.startedAt) return 0;\r\n    const endTime = this.completedAt || new Date();\r\n    return endTime.getTime() - this.startedAt.getTime();\r\n  }\r\n\r\n  get isOverdue(): boolean {\r\n    return this.timeoutAt && new Date() > this.timeoutAt;\r\n  }\r\n\r\n  get successRate(): number {\r\n    if (this.totalSteps === 0) return 0;\r\n    return (this.stepsCompleted / this.totalSteps) * 100;\r\n  }\r\n\r\n  /**\r\n   * Get execution summary\r\n   */\r\n  getExecutionSummary(): Record<string, any> {\r\n    return {\r\n      id: this.id,\r\n      workflowId: this.workflowId,\r\n      status: this.status,\r\n      triggerType: this.triggerType,\r\n      triggeredBy: this.triggeredBy,\r\n      startedAt: this.startedAt,\r\n      completedAt: this.completedAt,\r\n      duration: this.duration,\r\n      progress: this.progress,\r\n      stepsCompleted: this.stepsCompleted,\r\n      totalSteps: this.totalSteps,\r\n      currentStep: this.currentStep,\r\n      priority: this.priority,\r\n      retryCount: this.retryCount,\r\n      correlationId: this.correlationId,\r\n      traceId: this.traceId,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get execution metrics\r\n   */\r\n  getExecutionMetrics(): Record<string, any> {\r\n    return {\r\n      executionTime: this.executionTime,\r\n      successRate: this.successRate,\r\n      progress: this.progress,\r\n      stepsPerMinute: this.duration > 0 ? (this.stepsCompleted / (this.duration / 60000)) : 0,\r\n      isOverdue: this.isOverdue,\r\n      retryCount: this.retryCount,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get alert information from input\r\n   */\r\n  getAlertInfo(): Record<string, any> | null {\r\n    return this.input?.alert || null;\r\n  }\r\n\r\n  /**\r\n   * Get user information from input\r\n   */\r\n  getUserInfo(): Record<string, any> | null {\r\n    return this.input?.user || null;\r\n  }\r\n\r\n  /**\r\n   * Get metric values from input\r\n   */\r\n  getMetricValues(): Record<string, number> | null {\r\n    return this.input?.metricValues || null;\r\n  }\r\n\r\n  /**\r\n   * Get escalation level from context\r\n   */\r\n  getEscalationLevel(): number {\r\n    return this.context?.escalationLevel || 1;\r\n  }\r\n\r\n  /**\r\n   * Check if execution is acknowledged\r\n   */\r\n  isAcknowledged(): boolean {\r\n    return this.context?.acknowledged || false;\r\n  }\r\n\r\n  /**\r\n   * Get oncall schedule from context\r\n   */\r\n  getOncallSchedule(): Record<string, string> | null {\r\n    return this.context?.oncallSchedule || null;\r\n  }\r\n\r\n  /**\r\n   * Get correlated alerts\r\n   */\r\n  getCorrelatedAlerts(): string[] {\r\n    return this.context?.correlatedAlerts || [];\r\n  }\r\n\r\n  /**\r\n   * Get execution variables\r\n   */\r\n  getVariables(): Record<string, any> {\r\n    return this.context?.variables || {};\r\n  }\r\n\r\n  /**\r\n   * Set execution variable\r\n   */\r\n  setVariable(key: string, value: any): void {\r\n    if (!this.context) {\r\n      this.context = {};\r\n    }\r\n    if (!this.context.variables) {\r\n      this.context.variables = {};\r\n    }\r\n    this.context.variables[key] = value;\r\n  }\r\n\r\n  /**\r\n   * Get execution variable\r\n   */\r\n  getVariable(key: string): any {\r\n    return this.context?.variables?.[key];\r\n  }\r\n\r\n  /**\r\n   * Check if execution has specific tag\r\n   */\r\n  hasTag(tag: string): boolean {\r\n    return this.metadata?.testing?.testMode || false;\r\n  }\r\n\r\n  /**\r\n   * Check if execution is in test mode\r\n   */\r\n  isTestMode(): boolean {\r\n    return this.metadata?.testing?.testMode || false;\r\n  }\r\n\r\n  /**\r\n   * Check if execution is validate only\r\n   */\r\n  isValidateOnly(): boolean {\r\n    return this.metadata?.testing?.validateOnly || false;\r\n  }\r\n\r\n  /**\r\n   * Get step overrides for specific step\r\n   */\r\n  getStepOverrides(stepId: string): Record<string, any> {\r\n    return this.metadata?.stepOverrides?.[stepId] || {};\r\n  }\r\n\r\n  /**\r\n   * Get template variables\r\n   */\r\n  getTemplateVariables(): Record<string, any> {\r\n    return this.metadata?.templateVariables || {};\r\n  }\r\n\r\n  /**\r\n   * Get integration config for specific integration\r\n   */\r\n  getIntegrationConfig(integration: string): Record<string, any> {\r\n    return this.metadata?.integrationConfig?.[integration] || {};\r\n  }\r\n\r\n  /**\r\n   * Calculate estimated completion time\r\n   */\r\n  getEstimatedCompletionTime(): Date | null {\r\n    if (!this.startedAt || this.isFinished) {\r\n      return null;\r\n    }\r\n\r\n    const elapsedTime = Date.now() - this.startedAt.getTime();\r\n    const progressRate = this.progress / 100;\r\n    \r\n    if (progressRate === 0) {\r\n      return null;\r\n    }\r\n\r\n    const estimatedTotalTime = elapsedTime / progressRate;\r\n    const remainingTime = estimatedTotalTime - elapsedTime;\r\n    \r\n    return new Date(Date.now() + remainingTime);\r\n  }\r\n\r\n  /**\r\n   * Check if execution should be retried\r\n   */\r\n  shouldRetry(maxRetries: number = 3): boolean {\r\n    return this.isFailed && this.retryCount < maxRetries;\r\n  }\r\n\r\n  /**\r\n   * Get execution status for display\r\n   */\r\n  getDisplayStatus(): string {\r\n    switch (this.status) {\r\n      case 'pending': return 'Pending';\r\n      case 'running': return 'Running';\r\n      case 'completed': return 'Completed';\r\n      case 'failed': return 'Failed';\r\n      case 'cancelled': return 'Cancelled';\r\n      case 'timeout': return 'Timed Out';\r\n      default: return 'Unknown';\r\n    }\r\n  }\r\n}\r\n"], "version": 3}