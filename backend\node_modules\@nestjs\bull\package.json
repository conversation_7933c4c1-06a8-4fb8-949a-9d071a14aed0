{"name": "@nestjs/bull", "version": "11.0.2", "description": "Nest - modern, fast, powerful node.js web framework (@bull)", "homepage": "https://github.com/nestjs/bull", "bugs": {"url": "https://github.com/nestjs/bull/issues"}, "repository": {"type": "git", "url": "git+https://github.com/nestjs/bull.git"}, "license": "MIT", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>"], "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:e2e": "jest --config=e2e/jest-e2e.config.ts --detectOpenHandles --runInBand --forceExit"}, "dependencies": {"@nestjs/bull-shared": "^11.0.2", "tslib": "2.8.1"}, "devDependencies": {"@nestjs/common": "11.0.7", "@nestjs/core": "11.0.7", "@nestjs/platform-express": "11.0.7", "@nestjs/testing": "11.0.7", "@types/bull": "4.10.4", "@types/jest": "29.5.14", "@types/node": "22.13.0", "@types/reflect-metadata": "0.1.0", "bull": "4.16.5", "reflect-metadata": "0.2.2", "rxjs": "7.8.1"}, "peerDependencies": {"@nestjs/common": "^8.0.0 || ^9.0.0 || ^10.0.0 || ^11.0.0", "@nestjs/core": "^8.0.0 || ^9.0.0 || ^10.0.0 || ^11.0.0", "bull": "^3.3 || ^4.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "ca7c3911d2bf6ea0adca0391f22b39252b68fd0b"}