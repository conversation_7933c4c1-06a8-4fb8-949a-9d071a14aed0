import {
  registerDecorator,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
  buildMessage,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';

/**
 * DTO validation options
 */
export interface DtoValidationOptions {
  /** Allow partial validation (skip undefined properties) */
  allowPartial?: boolean;
  /** Transform values during validation */
  transform?: boolean;
  /** Custom error message */
  message?: string;
  /** Validation groups */
  groups?: string[];
}

/**
 * Conditional validation options
 */
export interface ConditionalValidationOptions extends ValidationOptions {
  /** Condition function that determines if validation should run */
  condition: (object: any, value: any) => boolean;
  /** Fallback validation when condition is false */
  fallback?: ValidationOptions;
}

/**
 * DTO validation constraint for complex object validation
 */
@ValidatorConstraint({ name: 'isDtoValid', async: false })
export class IsDtoValidConstraint implements ValidatorConstraintInterface {
  validate(value: any, args: ValidationArguments): boolean {
    const options: DtoValidationOptions = args.constraints[0] || {};
    
    if (!value || typeof value !== 'object') {
      return false;
    }

    // Skip validation for partial updates if allowed
    if (options.allowPartial && Object.keys(value).length === 0) {
      return true;
    }

    return true; // Basic object structure validation
  }

  defaultMessage(args: ValidationArguments): string {
    const options: DtoValidationOptions = args.constraints[0] || {};
    return options.message || `${args.property} must be a valid object`;
  }
}

/**
 * Conditional validation constraint
 */
@ValidatorConstraint({ name: 'isConditionallyValid', async: false })
export class IsConditionallyValidConstraint implements ValidatorConstraintInterface {
  validate(value: any, args: ValidationArguments): boolean {
    const options: ConditionalValidationOptions = args.constraints[0];
    
    if (!options.condition) {
      return true; // No condition means always valid
    }

    const shouldValidate = options.condition(args.object, value);
    
    if (!shouldValidate) {
      return true; // Skip validation when condition is false
    }

    // Perform actual validation here
    // This is a placeholder - in real implementation, you'd call the actual validator
    return value !== null && value !== undefined;
  }

  defaultMessage(args: ValidationArguments): string {
    const options: ConditionalValidationOptions = args.constraints[0];
    if (options.message) {
      return typeof options.message === 'string' ? options.message : options.message(args);
    }
    return buildMessage(eachPrefix => eachPrefix + '$property must meet the specified condition', options)(args);
  }
}

/**
 * DTO validation decorator
 * @param options DTO validation options
 * @param validationOptions Standard validation options
 * @returns Property decorator
 */
export function IsDtoValid(
  options?: DtoValidationOptions,
  validationOptions?: ValidationOptions,
) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [options],
      validator: IsDtoValidConstraint,
    });
  };
}

/**
 * Conditional validation decorator
 * Validates a property only when a condition is met
 * @param condition Function that determines if validation should run
 * @param validationOptions Validation options
 * @returns Property decorator
 */
export function ValidateIf(
  condition: (object: any, value: any) => boolean,
  validationOptions?: ValidationOptions,
) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [{ condition, ...validationOptions }],
      validator: IsConditionallyValidConstraint,
    });
  };
}

/**
 * Validate nested object decorator
 * @param type Class type for nested validation
 * @param options DTO validation options
 * @returns Property decorator
 */
export function ValidateNested(
  type?: () => any,
  options?: DtoValidationOptions,
) {
  return function (target: any, propertyName: string) {
    // Apply class-transformer Type decorator if type is provided
    if (type) {
      Type(type)(target, propertyName);
    }
    
    // Apply DTO validation
    IsDtoValid(options)(target, propertyName);
  };
}

/**
 * Transform and validate decorator
 * Combines transformation and validation in one decorator
 * @param transformFn Transformation function
 * @param validationOptions Validation options
 * @returns Property decorator
 */
export function TransformAndValidate(
  transformFn: (value: any, obj: any, type: any) => any,
  validationOptions?: ValidationOptions,
) {
  return function (target: any, propertyName: string) {
    // Apply transformation
    Transform(({ value, obj, type }) => transformFn(value, obj, type))(target, propertyName);
    
    // Apply validation
    IsDtoValid({ transform: true }, validationOptions)(target, propertyName);
  };
}

/**
 * Sanitize and validate decorator
 * Sanitizes input before validation
 * @param sanitizeFn Sanitization function
 * @param validationOptions Validation options
 * @returns Property decorator
 */
export function SanitizeAndValidate(
  sanitizeFn: (value: any) => any,
  validationOptions?: ValidationOptions,
) {
  return TransformAndValidate(
    (value) => sanitizeFn(value),
    validationOptions,
  );
}

/**
 * Trim and validate string decorator
 * @param validationOptions Validation options
 * @returns Property decorator
 */
export function TrimAndValidate(validationOptions?: ValidationOptions) {
  return SanitizeAndValidate(
    (value: any) => typeof value === 'string' ? value.trim() : value,
    validationOptions,
  );
}

/**
 * Normalize and validate decorator
 * Normalizes input to lowercase before validation
 * @param validationOptions Validation options
 * @returns Property decorator
 */
export function NormalizeAndValidate(validationOptions?: ValidationOptions) {
  return SanitizeAndValidate(
    (value: any) => typeof value === 'string' ? value.toLowerCase().trim() : value,
    validationOptions,
  );
}

/**
 * Array validation decorator with custom element validation
 * @param elementValidator Function to validate each array element
 * @param validationOptions Validation options
 * @returns Property decorator
 */
export function ValidateArrayElements(
  elementValidator: (element: any, index: number, array: any[]) => boolean,
  validationOptions?: ValidationOptions,
) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'validateArrayElements',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [elementValidator],
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (!Array.isArray(value)) {
            return false;
          }

          const validator = args.constraints[0];
          return value.every((element, index) => validator(element, index, value));
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be an array with valid elements`;
        },
      },
    });
  };
}

/**
 * Unique array elements decorator
 * Validates that all array elements are unique
 * @param keySelector Optional function to select comparison key
 * @param validationOptions Validation options
 * @returns Property decorator
 */
export function IsUniqueArray(
  keySelector?: (item: any) => any,
  validationOptions?: ValidationOptions,
) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isUniqueArray',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [keySelector],
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (!Array.isArray(value)) {
            return false;
          }

          const selector = args.constraints[0];
          const keys = value.map(item => selector ? selector(item) : item);
          const uniqueKeys = new Set(keys);
          
          return keys.length === uniqueKeys.size;
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must contain unique elements`;
        },
      },
    });
  };
}

/**
 * Cross-field validation decorator
 * Validates a field based on another field's value
 * @param relatedField Name of the related field
 * @param validator Validation function
 * @param validationOptions Validation options
 * @returns Property decorator
 */
export function ValidateWith(
  relatedField: string,
  validator: (value: any, relatedValue: any, object: any) => boolean,
  validationOptions?: ValidationOptions,
) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'validateWith',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [relatedField, validator],
      validator: {
        validate(value: any, args: ValidationArguments) {
          const [relatedPropertyName, validatorFn] = args.constraints;
          const relatedValue = (args.object as any)[relatedPropertyName];
          
          return validatorFn(value, relatedValue, args.object);
        },
        defaultMessage(args: ValidationArguments) {
          const relatedField = args.constraints[0];
          return `${args.property} must be valid in relation to ${relatedField}`;
        },
      },
    });
  };
}

/**
 * Date range validation decorator
 * Validates that a date is within a specified range
 * @param minDate Minimum date (or function returning minimum date)
 * @param maxDate Maximum date (or function returning maximum date)
 * @param validationOptions Validation options
 * @returns Property decorator
 */
export function IsDateInRange(
  minDate?: Date | (() => Date),
  maxDate?: Date | (() => Date),
  validationOptions?: ValidationOptions,
) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isDateInRange',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [minDate, maxDate],
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (!(value instanceof Date) && typeof value !== 'string') {
            return false;
          }

          const date = value instanceof Date ? value : new Date(value);
          if (isNaN(date.getTime())) {
            return false;
          }

          const [min, max] = args.constraints;
          
          if (min) {
            const minDate = typeof min === 'function' ? min() : min;
            if (date < minDate) {
              return false;
            }
          }
          
          if (max) {
            const maxDate = typeof max === 'function' ? max() : max;
            if (date > maxDate) {
              return false;
            }
          }

          return true;
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be a valid date within the specified range`;
        },
      },
    });
  };
}

/**
 * Password strength validation decorator
 * @param options Password strength options
 * @param validationOptions Validation options
 * @returns Property decorator
 */
export function IsStrongPassword(
  options?: {
    minLength?: number;
    requireUppercase?: boolean;
    requireLowercase?: boolean;
    requireNumbers?: boolean;
    requireSymbols?: boolean;
    forbiddenPatterns?: RegExp[];
  },
  validationOptions?: ValidationOptions,
) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isStrongPassword',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [options],
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (typeof value !== 'string') {
            return false;
          }

          const opts = args.constraints[0] || {};
          const {
            minLength = 8,
            requireUppercase = true,
            requireLowercase = true,
            requireNumbers = true,
            requireSymbols = true,
            forbiddenPatterns = [],
          } = opts;

          // Check minimum length
          if (value.length < minLength) {
            return false;
          }

          // Check character requirements
          if (requireUppercase && !/[A-Z]/.test(value)) {
            return false;
          }

          if (requireLowercase && !/[a-z]/.test(value)) {
            return false;
          }

          if (requireNumbers && !/\d/.test(value)) {
            return false;
          }

          if (requireSymbols && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(value)) {
            return false;
          }

          // Check forbidden patterns
          for (const pattern of forbiddenPatterns) {
            if (pattern.test(value)) {
              return false;
            }
          }

          return true;
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must meet password strength requirements`;
        },
      },
    });
  };
}