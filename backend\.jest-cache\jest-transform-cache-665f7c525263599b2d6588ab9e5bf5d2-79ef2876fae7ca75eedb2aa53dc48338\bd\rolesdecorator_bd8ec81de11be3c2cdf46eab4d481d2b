ec2420fc70840f6a4ca0092fadbd0589
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ROLES = exports.Roles = void 0;
const common_1 = require("@nestjs/common");
/**
 * Roles decorator for role-based access control
 *
 * @param roles - Array of role names required to access the resource
 * @example
 * ```typescript
 * @Roles('admin', 'moderator')
 * @Get('/admin-only')
 * adminOnlyEndpoint() {
 *   // Only users with 'admin' or 'moderator' roles can access this
 * }
 * ```
 */
const Roles = (...roles) => (0, common_1.SetMetadata)('roles', roles);
exports.Roles = Roles;
/**
 * Role constants for commonly used roles
 */
exports.ROLES = {
    ADMIN: 'admin',
    USER: 'user',
    MODERATOR: 'moderator',
    ANALYST: 'analyst',
    VIEWER: 'viewer',
    SECURITY_ADMIN: 'security_admin',
    COMPLIANCE_OFFICER: 'compliance_officer',
    INCIDENT_RESPONDER: 'incident_responder',
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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