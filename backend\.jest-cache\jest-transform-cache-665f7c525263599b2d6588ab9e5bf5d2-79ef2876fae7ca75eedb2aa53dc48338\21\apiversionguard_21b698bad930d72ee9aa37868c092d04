620aae4cb3dcb1294035f6bb4c4be68f
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ApiVersionGuard_1;
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiVersionGuard = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const api_versioning_service_1 = require("./api-versioning.service");
const api_version_decorator_1 = require("./api-version.decorator");
/**
 * Guard to validate and enforce API versioning
 * Checks version compatibility, deprecation status, and version requirements
 */
let ApiVersionGuard = ApiVersionGuard_1 = class ApiVersionGuard {
    constructor(reflector, versioningService) {
        this.reflector = reflector;
        this.versioningService = versioningService;
        this.logger = new common_1.Logger(ApiVersionGuard_1.name);
    }
    async canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const response = context.switchToHttp().getResponse();
        try {
            // Extract version from request
            const requestedVersion = this.versioningService.extractVersion(request);
            // Get version metadata from controller/method
            const versionConfig = this.getVersionConfig(context);
            // Validate version
            await this.validateVersion(requestedVersion, versionConfig, request, response);
            // Store version in request for later use
            request.apiVersion = requestedVersion;
            // Log version usage
            await this.versioningService.logVersionUsage(requestedVersion, request.path, request.user?.id);
            return true;
        }
        catch (error) {
            this.logger.error('API version validation failed', {
                path: request.path,
                method: request.method,
                error: error instanceof Error ? error.message : String(error),
                headers: request.headers,
            });
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.BadRequestException('Invalid API version');
        }
    }
    /**
     * Get version configuration from metadata
     */
    getVersionConfig(context) {
        // Check method-level metadata first
        const methodConfig = this.reflector.get(api_version_decorator_1.API_VERSION_KEY, context.getHandler());
        if (methodConfig) {
            return methodConfig;
        }
        // Check class-level metadata
        const classConfig = this.reflector.get(api_version_decorator_1.API_VERSION_KEY, context.getClass());
        return classConfig || null;
    }
    /**
     * Validate API version against requirements
     */
    async validateVersion(requestedVersion, versionConfig, request, response) {
        // Check if version is supported
        if (!this.versioningService.validateVersion(requestedVersion)) {
            const supportedVersions = this.versioningService.getSupportedVersions()
                .map(v => v.version)
                .join(', ');
            throw new common_1.BadRequestException(`Unsupported API version: ${requestedVersion}. Supported versions: ${supportedVersions}`);
        }
        // Get version information
        const versionInfo = this.versioningService.getVersionInfo(requestedVersion);
        if (!versionInfo) {
            throw new common_1.BadRequestException(`Invalid API version: ${requestedVersion}`);
        }
        // Check if version is sunset
        if (versionInfo.status === 'sunset') {
            throw new common_1.HttpException({
                statusCode: common_1.HttpStatus.GONE,
                message: `API version ${requestedVersion} is no longer supported`,
                error: 'Version Sunset',
                sunsetDate: versionInfo.sunsetDate,
                replacement: this.getReplacementVersion(requestedVersion),
            }, common_1.HttpStatus.GONE);
        }
        // Handle deprecated versions
        if (versionInfo.status === 'deprecated') {
            this.addDeprecationHeaders(response, requestedVersion, versionInfo);
        }
        // Check version-specific requirements
        if (versionConfig) {
            await this.validateVersionConfig(requestedVersion, versionConfig, request, response);
        }
        // Check min/max version requirements
        await this.validateVersionRange(requestedVersion, request, response);
        // Check experimental features
        await this.validateExperimentalFeatures(requestedVersion, request, response);
    }
    /**
     * Validate version against specific configuration
     */
    async validateVersionConfig(requestedVersion, config, request, response) {
        // Check if specific version is required
        if (config.version && config.version !== 'deprecated' && config.version !== requestedVersion) {
            // Allow compatible versions (same major version)
            const compatibility = this.versioningService.checkCompatibility(requestedVersion, config.version);
            if (!compatibility.compatible) {
                throw new common_1.BadRequestException(`This endpoint requires API version ${config.version}. ` +
                    `Version ${requestedVersion} is not compatible.`);
            }
            // Add compatibility warning headers
            if (compatibility.migrationRequired) {
                response.setHeader('X-API-Compatibility-Warning', `Migration required from v${requestedVersion} to v${config.version}`);
                response.setHeader('X-API-Breaking-Changes', compatibility.breakingChanges.join(', '));
            }
        }
        // Handle deprecated endpoint
        if (config.deprecated) {
            this.addDeprecationHeaders(response, requestedVersion, config);
            // Log deprecated endpoint usage
            this.logger.warn('Deprecated endpoint accessed', {
                version: requestedVersion,
                endpoint: request.path,
                deprecationDate: config.deprecationDate,
                sunsetDate: config.sunsetDate,
                replacement: config.replacement,
            });
        }
    }
    /**
     * Validate version range requirements
     */
    async validateVersionRange(requestedVersion, request, response) {
        const context = this.getCurrentExecutionContext(request);
        if (!context)
            return;
        // Check minimum version requirement
        const minVersion = this.reflector.get('min_version', context.getHandler()) ||
            this.reflector.get('min_version', context.getClass());
        if (minVersion && this.compareVersions(requestedVersion, minVersion) < 0) {
            throw new common_1.BadRequestException(`This endpoint requires minimum API version ${minVersion}. ` +
                `Current version: ${requestedVersion}`);
        }
        // Check maximum version requirement
        const maxVersion = this.reflector.get('max_version', context.getHandler()) ||
            this.reflector.get('max_version', context.getClass());
        if (maxVersion && this.compareVersions(requestedVersion, maxVersion) > 0) {
            throw new common_1.BadRequestException(`This endpoint supports maximum API version ${maxVersion}. ` +
                `Current version: ${requestedVersion}`);
        }
    }
    /**
     * Validate experimental features
     */
    async validateExperimentalFeatures(requestedVersion, request, response) {
        const context = this.getCurrentExecutionContext(request);
        if (!context)
            return;
        const experimental = this.reflector.get('experimental', context.getHandler()) ||
            this.reflector.get('experimental', context.getClass());
        if (experimental) {
            // Add experimental headers
            response.setHeader('X-API-Experimental', 'true');
            if (experimental.stabilityLevel) {
                response.setHeader('X-API-Stability-Level', experimental.stabilityLevel);
            }
            if (experimental.warning) {
                response.setHeader('X-API-Experimental-Warning', experimental.warning);
            }
            if (experimental.feedback) {
                response.setHeader('X-API-Feedback', experimental.feedback);
            }
            // Log experimental feature usage
            this.logger.log('Experimental feature accessed', {
                version: requestedVersion,
                endpoint: request.path,
                stabilityLevel: experimental.stabilityLevel || 'unknown',
            });
        }
    }
    /**
     * Add deprecation headers to response
     */
    addDeprecationHeaders(response, version, config) {
        response.setHeader('X-API-Deprecated', 'true');
        response.setHeader('X-API-Deprecated-Version', version);
        if (config.deprecationDate) {
            response.setHeader('X-API-Deprecation-Date', config.deprecationDate.toISOString());
        }
        if (config.sunsetDate) {
            response.setHeader('X-API-Sunset-Date', config.sunsetDate.toISOString());
        }
        if (config.replacement) {
            response.setHeader('X-API-Replacement', config.replacement);
        }
        if (config.migrationGuide) {
            response.setHeader('X-API-Migration-Guide', config.migrationGuide);
        }
        // Add deprecation warnings
        const warnings = this.versioningService.getDeprecationWarnings(version);
        if (warnings.length > 0) {
            const warningMessages = warnings.map(w => `${w.severity}: ${w.feature || 'Version'} deprecated on ${w.deprecationDate.toISOString()}`);
            response.setHeader('X-API-Deprecation-Warnings', warningMessages.join('; '));
        }
    }
    /**
     * Get replacement version for sunset version
     */
    getReplacementVersion(sunsetVersion) {
        // Logic to determine replacement version
        const supportedVersions = this.versioningService.getSupportedVersions()
            .filter(v => v.status === 'active')
            .sort((a, b) => this.compareVersions(b.version, a.version));
        return supportedVersions[0]?.version || 'latest';
    }
    /**
     * Compare two version strings
     */
    compareVersions(a, b) {
        const aParts = a.split('.').map(Number);
        const bParts = b.split('.').map(Number);
        for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {
            const aPart = aParts[i] || 0;
            const bPart = bParts[i] || 0;
            if (aPart > bPart)
                return 1;
            if (aPart < bPart)
                return -1;
        }
        return 0;
    }
    /**
     * Get current execution context (helper method)
     */
    getCurrentExecutionContext(request) {
        // This is a simplified approach - in a real implementation,
        // you might need to store the context in the request or use a different approach
        return request.executionContext || null;
    }
};
exports.ApiVersionGuard = ApiVersionGuard;
exports.ApiVersionGuard = ApiVersionGuard = ApiVersionGuard_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof core_1.Reflector !== "undefined" && core_1.Reflector) === "function" ? _a : Object, typeof (_b = typeof api_versioning_service_1.ApiVersioningService !== "undefined" && api_versioning_service_1.ApiVersioningService) === "function" ? _b : Object])
], ApiVersionGuard);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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