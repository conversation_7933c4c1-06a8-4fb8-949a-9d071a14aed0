import { Injectable, CanActivate, ExecutionContext, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { WsException } from '@nestjs/websockets';
import { Socket } from 'socket.io';
import { AuthenticatedUser } from '../decorators/current-user.decorator';

/**
 * WebSocket JWT Authentication Guard
 * 
 * Validates JWT tokens for WebSocket connections and attaches user information to the socket
 */
@Injectable()
export class WsJwtGuard implements CanActivate {
  private readonly logger = new Logger(WsJwtGuard.name);

  constructor(private readonly jwtService: JwtService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      const client: Socket = context.switchToWs().getClient<Socket>();
      const token = this.extractTokenFromSocket(client);

      if (!token) {
        this.logger.warn('No token provided for WebSocket connection', {
          socketId: client.id,
          handshake: client.handshake.headers,
        });
        throw new WsException('Authentication token required');
      }

      const payload = await this.validateToken(token);
      const user = await this.getUserFromPayload(payload);

      if (!user) {
        this.logger.warn('Invalid user from token payload', {
          socketId: client.id,
          payload,
        });
        throw new WsException('Invalid authentication token');
      }

      // Attach user to socket for later use
      client.data.user = user;
      
      this.logger.debug('WebSocket authentication successful', {
        socketId: client.id,
        userId: user.id,
        userEmail: user.email,
      });

      return true;
    } catch (error) {
      this.logger.error('WebSocket authentication failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        socketId: context.switchToWs().getClient().id,
      });

      if (error instanceof WsException) {
        throw error;
      }

      throw new WsException('Authentication failed');
    }
  }

  /**
   * Extract JWT token from WebSocket connection
   * Supports multiple token sources:
   * 1. Authorization header (Bearer token)
   * 2. Query parameter (token)
   * 3. Handshake auth object
   */
  private extractTokenFromSocket(client: Socket): string | null {
    // Try Authorization header first
    const authHeader = client.handshake.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // Try query parameter
    const queryToken = client.handshake.query.token;
    if (queryToken && typeof queryToken === 'string') {
      return queryToken;
    }

    // Try auth object in handshake
    const authToken = client.handshake.auth?.token;
    if (authToken && typeof authToken === 'string') {
      return authToken;
    }

    return null;
  }

  /**
   * Validate JWT token and return payload
   */
  private async validateToken(token: string): Promise<any> {
    try {
      return await this.jwtService.verifyAsync(token);
    } catch (error) {
      this.logger.warn('JWT token validation failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw new WsException('Invalid token');
    }
  }

  /**
   * Convert JWT payload to user object
   * This should match your JWT payload structure
   */
  private async getUserFromPayload(payload: any): Promise<AuthenticatedUser | null> {
    if (!payload.sub || !payload.email) {
      return null;
    }

    return {
      id: payload.sub,
      email: payload.email,
      username: payload.username,
      roles: payload.roles || [],
      permissions: payload.permissions || [],
      tenantId: payload.tenantId,
      isActive: payload.isActive ?? true,
      lastLoginAt: payload.lastLoginAt ? new Date(payload.lastLoginAt) : undefined,
      createdAt: payload.createdAt ? new Date(payload.createdAt) : new Date(),
      updatedAt: payload.updatedAt ? new Date(payload.updatedAt) : new Date(),
    };
  }
}
