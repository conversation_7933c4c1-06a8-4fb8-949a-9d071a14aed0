{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\services\\notification-queue-management.service.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,6CAAmD;AACnD,qCAAqC;AACrC,uCAA2C;AAC3C,+BAAkC;AAClC,2CAA+C;AAC/C,yDAAsD;AACtD,qFAA0E;AAC1E,iGAAqF;AACrF,+FAAmF;AAEnF;;;;;;;;;;;;;;;;;GAiBG;AAEI,IAAM,kCAAkC,0CAAxC,MAAM,kCAAkC;IAG7C,YAEE,2BAA2E,EAE3E,gCAAqF,EAErF,+BAAmF,EAEnF,iBAAyC,EAEzC,mBAA2C,EAE3C,gBAAwC,EAExC,SAAiC,EAChB,aAA4B,EAC5B,YAA2B;QAd3B,gCAA2B,GAA3B,2BAA2B,CAA+B;QAE1D,qCAAgC,GAAhC,gCAAgC,CAAoC;QAEpE,oCAA+B,GAA/B,+BAA+B,CAAmC;QAElE,sBAAiB,GAAjB,iBAAiB,CAAO;QAExB,wBAAmB,GAAnB,mBAAmB,CAAO;QAE1B,qBAAgB,GAAhB,gBAAgB,CAAO;QAEvB,cAAS,GAAT,SAAS,CAAO;QAChB,kBAAa,GAAb,aAAa,CAAe;QAC5B,iBAAY,GAAZ,YAAY,CAAe;QAlB7B,WAAM,GAAG,IAAI,eAAM,CAAC,oCAAkC,CAAC,IAAI,CAAC,CAAC;IAmB3E,CAAC;IAEJ;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,gBASvB;QACC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,gBAAgB,CAAC,EAAE,kBAAkB,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC;YAE5G,oBAAoB;YACpB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,OAAO,EAAE,gBAAgB,CAAC,SAAS,CAAC,CAAC;YACvG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;gBAC5B,MAAM,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;gBACrE,OAAO;YACT,CAAC;YAED,gDAAgD;YAChD,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAEjE,wDAAwD;YACxD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;YAE/D,qBAAqB;YACrB,MAAM,UAAU,GAAG,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC;gBACzD,cAAc,EAAE,gBAAgB,CAAC,EAAE;gBACnC,OAAO,EAAE,gBAAgB,CAAC,OAAO;gBACjC,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;gBACnC,SAAS,EAAE,gBAAgB,CAAC,SAAS;gBACrC,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE,IAAI,IAAI,EAAE;gBACpB,WAAW,EAAE,gBAAgB,CAAC,WAAW,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC;gBACzE,UAAU,EAAE,gBAAgB,CAAC,UAAU,IAAI,CAAC;gBAC5C,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;aACpC,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAExD,oBAAoB;YACpB,MAAM,UAAU,GAAG;gBACjB,KAAK;gBACL,QAAQ,EAAE,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,QAAQ,CAAC;gBAC7D,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,OAAO,CAAC;gBAC1D,gBAAgB,EAAE,GAAG;gBACrB,YAAY,EAAE,EAAE;aACjB,CAAC;YAEF,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,mBAAmB,EAAE,gBAAgB,EAAE,UAAU,CAAC,CAAC;YAE/E,iCAAiC;YACjC,UAAU,CAAC,KAAK,GAAG,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC;YACrC,UAAU,CAAC,MAAM,GAAG,WAAW,CAAC;YAChC,MAAM,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAExD,6BAA6B;YAC7B,MAAM,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,OAAO,EAAE,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAEzF,oBAAoB;YACpB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,qBAAqB,EAAE;gBAC5C,cAAc,EAAE,gBAAgB,CAAC,EAAE;gBACnC,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,KAAK;gBACL,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;gBACnC,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,gBAAgB,CAAC,EAAE,2BAA2B,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;QAEhG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACjF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,GAAQ;QAChC,MAAM,gBAAgB,GAAG,GAAG,CAAC,IAAI,CAAC;QAElC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,gBAAgB,CAAC,EAAE,aAAa,CAAC,CAAC;YAE/E,sBAAsB;YACtB,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC;YAEhE,6CAA6C;YAC7C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,gBAAgB,CAAC,CAAC;YAC7E,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;gBAC3B,MAAM,IAAI,CAAC,yBAAyB,CAAC,gBAAgB,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;gBAC7E,OAAO;YACT,CAAC;YAED,2BAA2B;YAC3B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAC5E,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACtB,MAAM,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;gBAC/D,OAAO;YACT,CAAC;YAED,oBAAoB;YACpB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;YAElF,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,MAAM,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;YAChE,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;YACjE,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,gBAAgB,CAAC,EAAE,KAAK,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1G,MAAM,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CAAC,gBAAqB,EAAE,MAAW,EAAE,GAAQ;QACrE,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,gBAAgB,CAAC,EAAE,EAAE,CAAC,CAAC;YAEzE,sBAAsB;YACtB,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;YAE5D,gCAAgC;YAChC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;YAEjF,IAAI,WAAW,CAAC,KAAK,IAAI,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC9D,MAAM,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;YAC1D,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;YACjF,CAAC;YAED,oBAAoB;YACpB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,8BAA8B,EAAE;gBACrD,cAAc,EAAE,gBAAgB,CAAC,EAAE;gBACnC,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,YAAY,EAAE,GAAG,CAAC,YAAY;gBAC9B,SAAS,EAAE,WAAW,CAAC,KAAK;gBAC5B,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACtF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACzB,gBAAqB,EACrB,aAAkB,EAClB,MAAc;QAEd,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,gBAAgB,CAAC,EAAE,0BAA0B,MAAM,EAAE,CAAC,CAAC;YAE/F,2BAA2B;YAC3B,MAAM,eAAe,GAAG,IAAI,CAAC,gCAAgC,CAAC,MAAM,CAAC;gBACnE,cAAc,EAAE,gBAAgB,CAAC,EAAE;gBACnC,OAAO,EAAE,gBAAgB,CAAC,OAAO;gBACjC,SAAS,EAAE,gBAAgB,CAAC,SAAS;gBACrC,YAAY,EAAE,gBAAgB;gBAC9B,aAAa,EAAE,MAAM;gBACrB,SAAS,EAAE,aAAa,CAAC,KAAK;gBAC9B,QAAQ,EAAE,IAAI,IAAI,EAAE;gBACpB,UAAU,EAAE,gBAAgB,CAAC,UAAU,IAAI,CAAC;gBAC5C,MAAM,EAAE,aAAa;gBACrB,QAAQ,EAAE;oBACR,GAAG,gBAAgB,CAAC,QAAQ;oBAC5B,cAAc,EAAE,aAAa;iBAC9B;aACF,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAElE,8BAA8B;YAC9B,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC;YAEjE,yBAAyB;YACzB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,0BAA0B,EAAE;gBACjD,cAAc,EAAE,gBAAgB,CAAC,EAAE;gBACnC,MAAM;gBACN,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,gBAAgB,CAAC,EAAE,6BAA6B,CAAC,CAAC;QAErF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qDAAqD,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACrG,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAAC,YAAoB,EAAE,OAIpD;QACC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iDAAiD,YAAY,EAAE,CAAC,CAAC;YAEjF,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,gCAAgC,CAAC,OAAO,CAAC;gBAC1E,KAAK,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE;aAC5B,CAAC,CAAC;YAEH,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,MAAM,IAAI,KAAK,CAAC,gCAAgC,YAAY,EAAE,CAAC,CAAC;YAClE,CAAC;YAED,sCAAsC;YACtC,MAAM,SAAS,GAAG;gBAChB,GAAG,eAAe,CAAC,YAAY;gBAC/B,SAAS,EAAE,OAAO,EAAE,eAAe,IAAI,eAAe,CAAC,SAAS;gBAChE,OAAO,EAAE,OAAO,EAAE,aAAa,IAAI,eAAe,CAAC,OAAO;gBAC1D,UAAU,EAAE,OAAO,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,UAAU,GAAG,CAAC,CAAC;aAC5E,CAAC;YAEF,kBAAkB;YAClB,MAAM,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAExC,4BAA4B;YAC5B,eAAe,CAAC,MAAM,GAAG,UAAU,CAAC;YACpC,eAAe,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YACvC,MAAM,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAElE,mBAAmB;YACnB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,gCAAgC,EAAE;gBACvD,YAAY;gBACZ,cAAc,EAAE,eAAe,CAAC,cAAc;gBAC9C,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,eAAe,CAAC,cAAc,oCAAoC,CAAC,CAAC;QAEtG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC3F,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;YAEjD,MAAM,CACJ,iBAAiB,EACjB,mBAAmB,EACnB,gBAAgB,EAChB,SAAS,EACV,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACpB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC;gBAC1C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC;gBAC5C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC;gBACzC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC;aACnC,CAAC,CAAC;YAEH,gCAAgC;YAChC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAExD,6BAA6B;YAC7B,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAE7D,4BAA4B;YAC5B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAE3D,MAAM,MAAM,GAAG;gBACb,MAAM,EAAE;oBACN,YAAY,EAAE,iBAAiB;oBAC/B,cAAc,EAAE,mBAAmB;oBACnC,WAAW,EAAE,gBAAgB;oBAC7B,IAAI,EAAE,SAAS;iBAChB;gBACD,QAAQ,EAAE,OAAO;gBACjB,UAAU,EAAE,eAAe;gBAC3B,UAAU,EAAE,cAAc;gBAC1B,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;YAC7D,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACxF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,SAAiB;QAChC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,SAAS,EAAE,CAAC,CAAC;YAE/C,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAC7C,MAAM,KAAK,CAAC,KAAK,EAAE,CAAC;YAEpB,mBAAmB;YACnB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,2BAA2B,EAAE;gBAClD,SAAS;gBACT,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8BAA8B,SAAS,EAAE,CAAC,CAAC;QAE7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,SAAiB;QACjC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mBAAmB,SAAS,EAAE,CAAC,CAAC;YAEhD,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAC7C,MAAM,KAAK,CAAC,MAAM,EAAE,CAAC;YAErB,oBAAoB;YACpB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,4BAA4B,EAAE;gBACnD,SAAS;gBACT,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,SAAS,EAAE,CAAC,CAAC;QAE9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC3E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,OAIjB;QACC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YAEnC,MAAM,YAAY,GAAG;gBACnB,KAAK,EAAE,OAAO,CAAC,SAAS,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW;gBAC5D,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,GAAG;aAC5B,CAAC;YAEF,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAChC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,IAAI,WAAW,EAAE,YAAY,CAAC,KAAK,CAAC;gBACnG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,IAAI,WAAW,EAAE,YAAY,CAAC,KAAK,CAAC;gBACrG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,IAAI,WAAW,EAAE,YAAY,CAAC,KAAK,CAAC;gBAClG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,IAAI,WAAW,EAAE,YAAY,CAAC,KAAK,CAAC;aAC5F,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YAEzE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6BAA6B,YAAY,eAAe,CAAC,CAAC;YAC1E,OAAO;gBACL,YAAY;gBACZ,YAAY,EAAE;oBACZ,YAAY,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM;oBAC/B,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM;oBACjC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM;oBAC9B,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM;iBACxB;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC3E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,QAAgB;QACzC,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,QAAQ,CAAC;YACd,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC,iBAAiB,CAAC;YAChC,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,mBAAmB,CAAC;YAClC,KAAK,KAAK;gBACR,OAAO,IAAI,CAAC,gBAAgB,CAAC;YAC/B,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC,SAAS,CAAC;YACxB;gBACE,OAAO,IAAI,CAAC,mBAAmB,CAAC;QACpC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,SAAiB;QACtC,MAAM,QAAQ,GAAG;YACf,4BAA4B,EAAE,IAAI,CAAC,iBAAiB;YACpD,8BAA8B,EAAE,IAAI,CAAC,mBAAmB;YACxD,2BAA2B,EAAE,IAAI,CAAC,gBAAgB;YAClD,mBAAmB,EAAE,IAAI,CAAC,SAAS;SACpC,CAAC;QAEF,OAAO,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,mBAAmB,CAAC;IACzD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc,CAAC,OAAe,EAAE,SAAiB;QAC7D,yCAAyC;QACzC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC;IACvE,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,gBAAqB;QACrD,yDAAyD;QACzD,OAAO,CAAC,CAAC,CAAC,sBAAsB;IAClC,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,QAAgB;QAC1C,MAAM,QAAQ,GAAG;YACf,MAAM,EAAE,CAAC;YACT,IAAI,EAAE,CAAC;YACP,MAAM,EAAE,CAAC;YACT,GAAG,EAAE,CAAC;YACN,IAAI,EAAE,CAAC;SACR,CAAC;QAEF,OAAO,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,OAAe;QACxC,OAAO;YACL,IAAI,EAAE,aAAa;YACnB,KAAK,EAAE,IAAI;SACZ,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,cAAsB,EAAE,MAAc;QACpE,MAAM,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAC3C,EAAE,cAAc,EAAE,EAClB,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,EAAE,CAClC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB,CAAC,gBAAqB,EAAE,cAAmB,IAAkB,CAAC;IAC3F,KAAK,CAAC,uBAAuB,CAAC,OAAe,EAAE,SAAiB,IAAkB,CAAC;IACnF,KAAK,CAAC,yBAAyB,CAAC,gBAAqB,IAAkB,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAClG,KAAK,CAAC,yBAAyB,CAAC,gBAAqB,EAAE,MAAc,IAAkB,CAAC;IACxF,KAAK,CAAC,qBAAqB,CAAC,OAAe,IAAkB,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACxF,KAAK,CAAC,uBAAuB,CAAC,gBAAqB,EAAE,QAAa,IAAkB,CAAC;IACrF,KAAK,CAAC,2BAA2B,CAAC,gBAAqB,EAAE,QAAa,IAAkB,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACnH,KAAK,CAAC,wBAAwB,CAAC,gBAAqB,EAAE,MAAW,IAAkB,CAAC;IACpF,KAAK,CAAC,qBAAqB,CAAC,gBAAqB,EAAE,KAAU,EAAE,GAAQ,IAAkB,CAAC;IAC1F,KAAK,CAAC,uBAAuB,CAAC,gBAAqB,EAAE,MAAW,IAAkB,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;IAC5G,KAAK,CAAC,aAAa,CAAC,gBAAqB,EAAE,MAAW,EAAE,GAAQ,IAAkB,CAAC;IACnF,KAAK,CAAC,aAAa,CAAC,KAAY,IAAkB,OAAO,EAAE,CAAC,CAAC,CAAC;IAC9D,KAAK,CAAC,0BAA0B,KAAmB,OAAO,EAAE,CAAC,CAAC,CAAC;IAC/D,KAAK,CAAC,uBAAuB,KAAmB,OAAO,EAAE,CAAC,CAAC,CAAC;IAC5D,KAAK,CAAC,sBAAsB,KAAmB,OAAO,EAAE,CAAC,CAAC,CAAC;CACpE,CAAA;AArgBY,gFAAkC;6CAAlC,kCAAkC;IAD9C,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,6CAAiB,CAAC,CAAA;IAEnC,WAAA,IAAA,0BAAgB,EAAC,wDAAsB,CAAC,CAAA;IAExC,WAAA,IAAA,0BAAgB,EAAC,sDAAqB,CAAC,CAAA;IAEvC,WAAA,IAAA,kBAAW,EAAC,4BAA4B,CAAC,CAAA;IAEzC,WAAA,IAAA,kBAAW,EAAC,8BAA8B,CAAC,CAAA;IAE3C,WAAA,IAAA,kBAAW,EAAC,2BAA2B,CAAC,CAAA;IAExC,WAAA,IAAA,kBAAW,EAAC,mBAAmB,CAAC,CAAA;yDAXa,oBAAU,oBAAV,oBAAU,oDAEL,oBAAU,oBAAV,oBAAU,oDAEX,oBAAU,oBAAV,oBAAU,oDAExB,YAAK,oBAAL,YAAK,oDAEH,YAAK,oBAAL,YAAK,oDAER,YAAK,oBAAL,YAAK,oDAEZ,YAAK,oBAAL,YAAK,oDACD,sBAAa,oBAAb,sBAAa,oDACd,6BAAa,oBAAb,6BAAa;GAnBnC,kCAAkC,CAqgB9C", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\services\\notification-queue-management.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { InjectQueue } from '@nestjs/bull';\r\nimport { Queue, Job } from 'bull';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { EventEmitter2 } from '@nestjs/event-emitter';\r\nimport { NotificationQueue } from '../entities/notification-queue.entity';\r\nimport { NotificationDeadLetter } from '../entities/notification-dead-letter.entity';\r\nimport { NotificationRateLimit } from '../entities/notification-rate-limit.entity';\r\n\r\n/**\r\n * Notification Queue Management Service\r\n * \r\n * Provides comprehensive notification queue processing and management including:\r\n * - Priority-based notification queuing with intelligent scheduling and batching\r\n * - Rate limiting and throttling with provider-specific limits and burst handling\r\n * - Retry mechanism coordination with exponential backoff and circuit breaker integration\r\n * - Dead letter queue management with failure analysis and manual intervention capabilities\r\n * - Load balancing across notification providers with health-based routing\r\n * \r\n * Features:\r\n * - Multi-priority queue management with intelligent scheduling\r\n * - Advanced rate limiting with burst handling and provider-specific limits\r\n * - Sophisticated retry mechanisms with circuit breaker patterns\r\n * - Comprehensive dead letter queue processing and recovery\r\n * - Dynamic load balancing with health-based provider routing\r\n * - Real-time queue monitoring and performance optimization\r\n */\r\n@Injectable()\r\nexport class NotificationQueueManagementService {\r\n  private readonly logger = new Logger(NotificationQueueManagementService.name);\r\n\r\n  constructor(\r\n    @InjectRepository(NotificationQueue)\r\n    private readonly notificationQueueRepository: Repository<NotificationQueue>,\r\n    @InjectRepository(NotificationDeadLetter)\r\n    private readonly notificationDeadLetterRepository: Repository<NotificationDeadLetter>,\r\n    @InjectRepository(NotificationRateLimit)\r\n    private readonly notificationRateLimitRepository: Repository<NotificationRateLimit>,\r\n    @InjectQueue('notification-high-priority')\r\n    private readonly highPriorityQueue: Queue,\r\n    @InjectQueue('notification-normal-priority')\r\n    private readonly normalPriorityQueue: Queue,\r\n    @InjectQueue('notification-low-priority')\r\n    private readonly lowPriorityQueue: Queue,\r\n    @InjectQueue('notification-bulk')\r\n    private readonly bulkQueue: Queue,\r\n    private readonly configService: ConfigService,\r\n    private readonly eventEmitter: EventEmitter2\r\n  ) {}\r\n\r\n  /**\r\n   * Queue notification for delivery\r\n   */\r\n  async queueNotification(notificationData: {\r\n    id: string;\r\n    channel: string;\r\n    priority: string;\r\n    recipient: string;\r\n    content: any;\r\n    metadata: any;\r\n    scheduledAt?: Date;\r\n    retryCount?: number;\r\n  }): Promise<void> {\r\n    try {\r\n      this.logger.debug(`Queuing notification ${notificationData.id} with priority ${notificationData.priority}`);\r\n\r\n      // Check rate limits\r\n      const rateLimitCheck = await this.checkRateLimit(notificationData.channel, notificationData.recipient);\r\n      if (!rateLimitCheck.allowed) {\r\n        await this.handleRateLimitExceeded(notificationData, rateLimitCheck);\r\n        return;\r\n      }\r\n\r\n      // Determine appropriate queue based on priority\r\n      const queue = this.getQueueByPriority(notificationData.priority);\r\n\r\n      // Calculate delay based on rate limiting and scheduling\r\n      const delay = await this.calculateQueueDelay(notificationData);\r\n\r\n      // Create queue entry\r\n      const queueEntry = this.notificationQueueRepository.create({\r\n        notificationId: notificationData.id,\r\n        channel: notificationData.channel,\r\n        priority: notificationData.priority,\r\n        recipient: notificationData.recipient,\r\n        status: 'queued',\r\n        queuedAt: new Date(),\r\n        scheduledAt: notificationData.scheduledAt || new Date(Date.now() + delay),\r\n        retryCount: notificationData.retryCount || 0,\r\n        metadata: notificationData.metadata,\r\n      });\r\n\r\n      await this.notificationQueueRepository.save(queueEntry);\r\n\r\n      // Add to Bull queue\r\n      const jobOptions = {\r\n        delay,\r\n        attempts: this.getMaxRetryAttempts(notificationData.priority),\r\n        backoff: this.getBackoffStrategy(notificationData.channel),\r\n        removeOnComplete: 100,\r\n        removeOnFail: 50,\r\n      };\r\n\r\n      const job = await queue.add('send-notification', notificationData, jobOptions);\r\n\r\n      // Update queue entry with job ID\r\n      queueEntry.jobId = job.id.toString();\r\n      queueEntry.status = 'scheduled';\r\n      await this.notificationQueueRepository.save(queueEntry);\r\n\r\n      // Update rate limit counters\r\n      await this.updateRateLimitCounters(notificationData.channel, notificationData.recipient);\r\n\r\n      // Emit queued event\r\n      this.eventEmitter.emit('notification.queued', {\r\n        notificationId: notificationData.id,\r\n        queue: queue.name,\r\n        delay,\r\n        priority: notificationData.priority,\r\n        timestamp: new Date(),\r\n      });\r\n\r\n      this.logger.debug(`Notification ${notificationData.id} queued successfully in ${queue.name}`);\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to queue notification: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Process notification from queue\r\n   */\r\n  async processNotification(job: Job): Promise<void> {\r\n    const notificationData = job.data;\r\n    \r\n    try {\r\n      this.logger.debug(`Processing notification ${notificationData.id} from queue`);\r\n\r\n      // Update queue status\r\n      await this.updateQueueStatus(notificationData.id, 'processing');\r\n\r\n      // Check if notification should still be sent\r\n      const shouldProcess = await this.shouldProcessNotification(notificationData);\r\n      if (!shouldProcess.process) {\r\n        await this.handleSkippedNotification(notificationData, shouldProcess.reason);\r\n        return;\r\n      }\r\n\r\n      // Get provider for channel\r\n      const provider = await this.getProviderForChannel(notificationData.channel);\r\n      if (!provider.healthy) {\r\n        await this.handleUnhealthyProvider(notificationData, provider);\r\n        return;\r\n      }\r\n\r\n      // Send notification\r\n      const result = await this.sendNotificationViaProvider(notificationData, provider);\r\n\r\n      if (result.success) {\r\n        await this.handleSuccessfulDelivery(notificationData, result);\r\n      } else {\r\n        await this.handleFailedDelivery(notificationData, result, job);\r\n      }\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to process notification ${notificationData.id}: ${error.message}`, error.stack);\r\n      await this.handleProcessingError(notificationData, error, job);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handle failed notification delivery\r\n   */\r\n  async handleFailedDelivery(notificationData: any, result: any, job: Job): Promise<void> {\r\n    try {\r\n      this.logger.warn(`Notification delivery failed: ${notificationData.id}`);\r\n\r\n      // Update queue status\r\n      await this.updateQueueStatus(notificationData.id, 'failed');\r\n\r\n      // Check if retry is appropriate\r\n      const shouldRetry = await this.shouldRetryNotification(notificationData, result);\r\n\r\n      if (shouldRetry.retry && job.attemptsMade < job.opts.attempts) {\r\n        await this.scheduleRetry(notificationData, result, job);\r\n      } else {\r\n        await this.moveToDeadLetterQueue(notificationData, result, shouldRetry.reason);\r\n      }\r\n\r\n      // Emit failed event\r\n      this.eventEmitter.emit('notification.delivery.failed', {\r\n        notificationId: notificationData.id,\r\n        error: result.error,\r\n        attemptsMade: job.attemptsMade,\r\n        willRetry: shouldRetry.retry,\r\n        timestamp: new Date(),\r\n      });\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to handle delivery failure: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Move notification to dead letter queue\r\n   */\r\n  async moveToDeadLetterQueue(\r\n    notificationData: any,\r\n    failureResult: any,\r\n    reason: string\r\n  ): Promise<void> {\r\n    try {\r\n      this.logger.warn(`Moving notification ${notificationData.id} to dead letter queue: ${reason}`);\r\n\r\n      // Create dead letter entry\r\n      const deadLetterEntry = this.notificationDeadLetterRepository.create({\r\n        notificationId: notificationData.id,\r\n        channel: notificationData.channel,\r\n        recipient: notificationData.recipient,\r\n        originalData: notificationData,\r\n        failureReason: reason,\r\n        lastError: failureResult.error,\r\n        failedAt: new Date(),\r\n        retryCount: notificationData.retryCount || 0,\r\n        status: 'dead_letter',\r\n        metadata: {\r\n          ...notificationData.metadata,\r\n          failureDetails: failureResult,\r\n        },\r\n      });\r\n\r\n      await this.notificationDeadLetterRepository.save(deadLetterEntry);\r\n\r\n      // Update original queue entry\r\n      await this.updateQueueStatus(notificationData.id, 'dead_letter');\r\n\r\n      // Emit dead letter event\r\n      this.eventEmitter.emit('notification.dead_letter', {\r\n        notificationId: notificationData.id,\r\n        reason,\r\n        timestamp: new Date(),\r\n      });\r\n\r\n      this.logger.warn(`Notification ${notificationData.id} moved to dead letter queue`);\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to move notification to dead letter queue: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Retry notification from dead letter queue\r\n   */\r\n  async retryFromDeadLetterQueue(deadLetterId: string, options?: {\r\n    updateRecipient?: string;\r\n    updateChannel?: string;\r\n    resetRetryCount?: boolean;\r\n  }): Promise<void> {\r\n    try {\r\n      this.logger.log(`Retrying notification from dead letter queue: ${deadLetterId}`);\r\n\r\n      const deadLetterEntry = await this.notificationDeadLetterRepository.findOne({\r\n        where: { id: deadLetterId },\r\n      });\r\n\r\n      if (!deadLetterEntry) {\r\n        throw new Error(`Dead letter entry not found: ${deadLetterId}`);\r\n      }\r\n\r\n      // Prepare notification data for retry\r\n      const retryData = {\r\n        ...deadLetterEntry.originalData,\r\n        recipient: options?.updateRecipient || deadLetterEntry.recipient,\r\n        channel: options?.updateChannel || deadLetterEntry.channel,\r\n        retryCount: options?.resetRetryCount ? 0 : (deadLetterEntry.retryCount + 1),\r\n      };\r\n\r\n      // Queue for retry\r\n      await this.queueNotification(retryData);\r\n\r\n      // Update dead letter status\r\n      deadLetterEntry.status = 'retrying';\r\n      deadLetterEntry.retriedAt = new Date();\r\n      await this.notificationDeadLetterRepository.save(deadLetterEntry);\r\n\r\n      // Emit retry event\r\n      this.eventEmitter.emit('notification.dead_letter.retry', {\r\n        deadLetterId,\r\n        notificationId: deadLetterEntry.notificationId,\r\n        timestamp: new Date(),\r\n      });\r\n\r\n      this.logger.log(`Notification ${deadLetterEntry.notificationId} queued for retry from dead letter`);\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to retry from dead letter queue: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get queue statistics\r\n   */\r\n  async getQueueStatistics(): Promise<any> {\r\n    try {\r\n      this.logger.debug('Retrieving queue statistics');\r\n\r\n      const [\r\n        highPriorityStats,\r\n        normalPriorityStats,\r\n        lowPriorityStats,\r\n        bulkStats,\r\n      ] = await Promise.all([\r\n        this.getQueueStats(this.highPriorityQueue),\r\n        this.getQueueStats(this.normalPriorityQueue),\r\n        this.getQueueStats(this.lowPriorityQueue),\r\n        this.getQueueStats(this.bulkQueue),\r\n      ]);\r\n\r\n      // Get database queue statistics\r\n      const dbStats = await this.getDatabaseQueueStatistics();\r\n\r\n      // Get dead letter statistics\r\n      const deadLetterStats = await this.getDeadLetterStatistics();\r\n\r\n      // Get rate limit statistics\r\n      const rateLimitStats = await this.getRateLimitStatistics();\r\n\r\n      const result = {\r\n        queues: {\r\n          highPriority: highPriorityStats,\r\n          normalPriority: normalPriorityStats,\r\n          lowPriority: lowPriorityStats,\r\n          bulk: bulkStats,\r\n        },\r\n        database: dbStats,\r\n        deadLetter: deadLetterStats,\r\n        rateLimits: rateLimitStats,\r\n        timestamp: new Date(),\r\n      };\r\n\r\n      this.logger.debug('Queue statistics retrieved successfully');\r\n      return result;\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to retrieve queue statistics: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Pause queue processing\r\n   */\r\n  async pauseQueue(queueName: string): Promise<void> {\r\n    try {\r\n      this.logger.log(`Pausing queue: ${queueName}`);\r\n\r\n      const queue = this.getQueueByName(queueName);\r\n      await queue.pause();\r\n\r\n      // Emit pause event\r\n      this.eventEmitter.emit('notification.queue.paused', {\r\n        queueName,\r\n        timestamp: new Date(),\r\n      });\r\n\r\n      this.logger.log(`Queue paused successfully: ${queueName}`);\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to pause queue: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Resume queue processing\r\n   */\r\n  async resumeQueue(queueName: string): Promise<void> {\r\n    try {\r\n      this.logger.log(`Resuming queue: ${queueName}`);\r\n\r\n      const queue = this.getQueueByName(queueName);\r\n      await queue.resume();\r\n\r\n      // Emit resume event\r\n      this.eventEmitter.emit('notification.queue.resumed', {\r\n        queueName,\r\n        timestamp: new Date(),\r\n      });\r\n\r\n      this.logger.log(`Queue resumed successfully: ${queueName}`);\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to resume queue: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Clean completed jobs from queues\r\n   */\r\n  async cleanQueues(options: {\r\n    olderThan?: number;\r\n    limit?: number;\r\n    status?: 'completed' | 'failed' | 'active' | 'waiting';\r\n  }): Promise<any> {\r\n    try {\r\n      this.logger.log('Cleaning queues');\r\n\r\n      const cleanOptions = {\r\n        grace: options.olderThan || 24 * 60 * 60 * 1000, // 24 hours\r\n        limit: options.limit || 100,\r\n      };\r\n\r\n      const results = await Promise.all([\r\n        this.highPriorityQueue.clean(cleanOptions.grace, options.status || 'completed', cleanOptions.limit),\r\n        this.normalPriorityQueue.clean(cleanOptions.grace, options.status || 'completed', cleanOptions.limit),\r\n        this.lowPriorityQueue.clean(cleanOptions.grace, options.status || 'completed', cleanOptions.limit),\r\n        this.bulkQueue.clean(cleanOptions.grace, options.status || 'completed', cleanOptions.limit),\r\n      ]);\r\n\r\n      const totalCleaned = results.reduce((sum, jobs) => sum + jobs.length, 0);\r\n\r\n      this.logger.log(`Queue cleaning completed: ${totalCleaned} jobs cleaned`);\r\n      return {\r\n        totalCleaned,\r\n        queueResults: {\r\n          highPriority: results[0].length,\r\n          normalPriority: results[1].length,\r\n          lowPriority: results[2].length,\r\n          bulk: results[3].length,\r\n        },\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to clean queues: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get queue by priority\r\n   */\r\n  private getQueueByPriority(priority: string): Queue {\r\n    switch (priority) {\r\n      case 'urgent':\r\n      case 'high':\r\n        return this.highPriorityQueue;\r\n      case 'normal':\r\n        return this.normalPriorityQueue;\r\n      case 'low':\r\n        return this.lowPriorityQueue;\r\n      case 'bulk':\r\n        return this.bulkQueue;\r\n      default:\r\n        return this.normalPriorityQueue;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get queue by name\r\n   */\r\n  private getQueueByName(queueName: string): Queue {\r\n    const queueMap = {\r\n      'notification-high-priority': this.highPriorityQueue,\r\n      'notification-normal-priority': this.normalPriorityQueue,\r\n      'notification-low-priority': this.lowPriorityQueue,\r\n      'notification-bulk': this.bulkQueue,\r\n    };\r\n\r\n    return queueMap[queueName] || this.normalPriorityQueue;\r\n  }\r\n\r\n  /**\r\n   * Check rate limit for channel and recipient\r\n   */\r\n  private async checkRateLimit(channel: string, recipient: string): Promise<any> {\r\n    // Implementation for rate limit checking\r\n    return { allowed: true, remainingQuota: 100, resetTime: new Date() };\r\n  }\r\n\r\n  /**\r\n   * Calculate queue delay based on rate limiting and scheduling\r\n   */\r\n  private async calculateQueueDelay(notificationData: any): Promise<number> {\r\n    // Implementation for calculating appropriate queue delay\r\n    return 0; // No delay by default\r\n  }\r\n\r\n  /**\r\n   * Get maximum retry attempts based on priority\r\n   */\r\n  private getMaxRetryAttempts(priority: string): number {\r\n    const retryMap = {\r\n      urgent: 5,\r\n      high: 4,\r\n      normal: 3,\r\n      low: 2,\r\n      bulk: 1,\r\n    };\r\n\r\n    return retryMap[priority] || 3;\r\n  }\r\n\r\n  /**\r\n   * Get backoff strategy for channel\r\n   */\r\n  private getBackoffStrategy(channel: string): any {\r\n    return {\r\n      type: 'exponential',\r\n      delay: 2000,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Update queue status in database\r\n   */\r\n  private async updateQueueStatus(notificationId: string, status: string): Promise<void> {\r\n    await this.notificationQueueRepository.update(\r\n      { notificationId },\r\n      { status, updatedAt: new Date() }\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Additional private helper methods\r\n   */\r\n  private async handleRateLimitExceeded(notificationData: any, rateLimitCheck: any): Promise<void> {}\r\n  private async updateRateLimitCounters(channel: string, recipient: string): Promise<void> {}\r\n  private async shouldProcessNotification(notificationData: any): Promise<any> { return { process: true }; }\r\n  private async handleSkippedNotification(notificationData: any, reason: string): Promise<void> {}\r\n  private async getProviderForChannel(channel: string): Promise<any> { return { healthy: true }; }\r\n  private async handleUnhealthyProvider(notificationData: any, provider: any): Promise<void> {}\r\n  private async sendNotificationViaProvider(notificationData: any, provider: any): Promise<any> { return { success: true }; }\r\n  private async handleSuccessfulDelivery(notificationData: any, result: any): Promise<void> {}\r\n  private async handleProcessingError(notificationData: any, error: any, job: Job): Promise<void> {}\r\n  private async shouldRetryNotification(notificationData: any, result: any): Promise<any> { return { retry: false }; }\r\n  private async scheduleRetry(notificationData: any, result: any, job: Job): Promise<void> {}\r\n  private async getQueueStats(queue: Queue): Promise<any> { return {}; }\r\n  private async getDatabaseQueueStatistics(): Promise<any> { return {}; }\r\n  private async getDeadLetterStatistics(): Promise<any> { return {}; }\r\n  private async getRateLimitStatistics(): Promise<any> { return {}; }\r\n}\r\n"], "version": 3}