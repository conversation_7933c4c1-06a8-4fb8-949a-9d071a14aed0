d8653c9b283c643b377fc343140460be
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalysisJob = void 0;
const typeorm_1 = require("typeorm");
const model_configuration_entity_1 = require("./model-configuration.entity");
/**
 * Analysis Job entity
 * Represents AI/ML analysis tasks and their execution status
 */
let AnalysisJob = class AnalysisJob {
    /**
     * Check if job is in a terminal state
     */
    get isTerminal() {
        return ['completed', 'failed', 'cancelled', 'timeout'].includes(this.status);
    }
    /**
     * Check if job is currently running
     */
    get isRunning() {
        return ['queued', 'running'].includes(this.status);
    }
    /**
     * Check if job can be retried
     */
    get canRetry() {
        return this.status === 'failed' && this.retryCount < this.maxRetries;
    }
    /**
     * Get job duration in milliseconds
     */
    get duration() {
        if (!this.startedAt)
            return null;
        const endTime = this.completedAt || new Date();
        return endTime.getTime() - this.startedAt.getTime();
    }
    /**
     * Get job duration in seconds
     */
    get durationSeconds() {
        const duration = this.duration;
        return duration ? Math.round(duration / 1000) : null;
    }
    /**
     * Get estimated time remaining in milliseconds
     */
    get estimatedTimeRemaining() {
        if (!this.estimatedCompletion || this.isTerminal)
            return null;
        const remaining = this.estimatedCompletion.getTime() - Date.now();
        return Math.max(0, remaining);
    }
    /**
     * Update job progress
     */
    updateProgress(progress, stage) {
        this.progress = Math.max(0, Math.min(100, progress));
        if (stage) {
            this.currentStage = stage;
        }
    }
    /**
     * Mark job as started
     */
    markAsStarted() {
        this.status = 'running';
        this.startedAt = new Date();
        this.progress = 0;
    }
    /**
     * Mark job as completed
     */
    markAsCompleted(outputData) {
        this.status = 'completed';
        this.completedAt = new Date();
        this.progress = 100;
        if (outputData) {
            this.outputData = outputData;
        }
    }
    /**
     * Mark job as failed
     */
    markAsFailed(error, details) {
        this.status = 'failed';
        this.completedAt = new Date();
        this.errorMessage = error;
        if (details) {
            this.errorDetails = details;
        }
    }
    /**
     * Mark job as cancelled
     */
    markAsCancelled(reason) {
        this.status = 'cancelled';
        this.completedAt = new Date();
        if (reason) {
            this.errorMessage = reason;
        }
    }
    /**
     * Increment retry count
     */
    incrementRetry() {
        this.retryCount += 1;
        this.status = 'pending';
        this.startedAt = null;
        this.completedAt = null;
        this.errorMessage = null;
        this.errorDetails = null;
        this.progress = 0;
    }
    /**
     * Add resource usage data
     */
    addResourceUsage(usage) {
        if (!this.resourceUsage) {
            this.resourceUsage = {};
        }
        Object.assign(this.resourceUsage, usage);
    }
    /**
     * Add quality metrics
     */
    addQualityMetrics(metrics) {
        if (!this.qualityMetrics) {
            this.qualityMetrics = {};
        }
        Object.assign(this.qualityMetrics, metrics);
    }
    /**
     * Calculate estimated completion time based on progress
     */
    calculateEstimatedCompletion() {
        if (!this.startedAt || this.progress <= 0 || this.isTerminal) {
            this.estimatedCompletion = null;
            return;
        }
        const elapsed = Date.now() - this.startedAt.getTime();
        const estimatedTotal = (elapsed / this.progress) * 100;
        const remaining = estimatedTotal - elapsed;
        this.estimatedCompletion = new Date(Date.now() + remaining);
    }
};
exports.AnalysisJob = AnalysisJob;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], AnalysisJob.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: [
            'vulnerability_analysis',
            'threat_intelligence_processing',
            'risk_assessment',
            'report_generation',
            'ioc_enrichment',
            'malware_analysis',
            'network_analysis',
            'behavioral_analysis',
        ],
    }),
    __metadata("design:type", String)
], AnalysisJob.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], AnalysisJob.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], AnalysisJob.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['pending', 'queued', 'running', 'completed', 'failed', 'cancelled', 'timeout'],
        default: 'pending',
    }),
    __metadata("design:type", String)
], AnalysisJob.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['low', 'normal', 'high', 'urgent'],
        default: 'normal',
    }),
    __metadata("design:type", String)
], AnalysisJob.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'input_data', type: 'jsonb' }),
    __metadata("design:type", typeof (_a = typeof Record !== "undefined" && Record) === "function" ? _a : Object)
], AnalysisJob.prototype, "inputData", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'output_data', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Record !== "undefined" && Record) === "function" ? _b : Object)
], AnalysisJob.prototype, "outputData", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], AnalysisJob.prototype, "configuration", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'integer', default: 0 }),
    __metadata("design:type", Number)
], AnalysisJob.prototype, "progress", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'current_stage', nullable: true }),
    __metadata("design:type", String)
], AnalysisJob.prototype, "currentStage", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'total_stages', type: 'integer', nullable: true }),
    __metadata("design:type", Number)
], AnalysisJob.prototype, "totalStages", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'estimated_completion', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], AnalysisJob.prototype, "estimatedCompletion", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'scheduled_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], AnalysisJob.prototype, "scheduledAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'started_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], AnalysisJob.prototype, "startedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'completed_at', type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", typeof (_f = typeof Date !== "undefined" && Date) === "function" ? _f : Object)
], AnalysisJob.prototype, "completedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'error_message', type: 'text', nullable: true }),
    __metadata("design:type", String)
], AnalysisJob.prototype, "errorMessage", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'error_details', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_g = typeof Record !== "undefined" && Record) === "function" ? _g : Object)
], AnalysisJob.prototype, "errorDetails", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'retry_count', type: 'integer', default: 0 }),
    __metadata("design:type", Number)
], AnalysisJob.prototype, "retryCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'max_retries', type: 'integer', default: 3 }),
    __metadata("design:type", Number)
], AnalysisJob.prototype, "maxRetries", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'resource_usage', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], AnalysisJob.prototype, "resourceUsage", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'quality_metrics', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], AnalysisJob.prototype, "qualityMetrics", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], AnalysisJob.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_h = typeof Record !== "undefined" && Record) === "function" ? _h : Object)
], AnalysisJob.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', type: 'uuid' }),
    __metadata("design:type", String)
], AnalysisJob.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], AnalysisJob.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_j = typeof Date !== "undefined" && Date) === "function" ? _j : Object)
], AnalysisJob.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_k = typeof Date !== "undefined" && Date) === "function" ? _k : Object)
], AnalysisJob.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => model_configuration_entity_1.ModelConfiguration, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'model_configuration_id' }),
    __metadata("design:type", typeof (_l = typeof model_configuration_entity_1.ModelConfiguration !== "undefined" && model_configuration_entity_1.ModelConfiguration) === "function" ? _l : Object)
], AnalysisJob.prototype, "modelConfiguration", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'model_configuration_id', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], AnalysisJob.prototype, "modelConfigurationId", void 0);
exports.AnalysisJob = AnalysisJob = __decorate([
    (0, typeorm_1.Entity)('analysis_jobs'),
    (0, typeorm_1.Index)(['type']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['priority']),
    (0, typeorm_1.Index)(['createdAt']),
    (0, typeorm_1.Index)(['scheduledAt']),
    (0, typeorm_1.Index)(['completedAt'])
], AnalysisJob);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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