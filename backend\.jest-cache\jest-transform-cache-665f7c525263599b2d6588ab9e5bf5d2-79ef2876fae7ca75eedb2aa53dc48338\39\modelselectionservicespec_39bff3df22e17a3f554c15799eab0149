de28e16311b79dcaaecc7df6c837f214
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const model_selection_service_1 = require("../model-selection.service");
const model_performance_service_1 = require("../../optimization/model-performance.service");
const ai_cache_service_1 = require("../../caching/ai-cache.service");
const ai_model_repository_interface_1 = require("../../../../domain/repositories/ai-model.repository.interface");
describe('ModelSelectionService', () => {
    let service;
    let mockModelRepository;
    let mockPerformanceService;
    let mockCacheService;
    let mockConfigService;
    const mockModelInfo = {
        id: 'model-1',
        name: 'Test Model',
        type: 'threat-detection',
        providerType: 'openai',
        version: '1.0.0',
        status: 'active',
        healthStatus: 'healthy',
        defaultParameters: { temperature: 0.7 },
        recommendedUseCases: ['threat-analysis'],
    };
    const mockPerformanceMetrics = {
        accuracy: 0.95,
        precision: 0.92,
        recall: 0.88,
        f1Score: 0.90,
        averageLatency: 1500,
        throughput: 50,
        costPerRequest: 0.005,
        availability: 0.99,
    };
    beforeEach(async () => {
        // Create mocks
        mockModelRepository = {
            findById: jest.fn(),
            findByTaskType: jest.fn(),
            save: jest.fn(),
            delete: jest.fn(),
        };
        mockPerformanceService = {
            getModelMetrics: jest.fn(),
            getCostMetrics: jest.fn(),
            updateModelMetrics: jest.fn(),
        };
        mockCacheService = {
            get: jest.fn(),
            set: jest.fn(),
            delete: jest.fn(),
            checkHealth: jest.fn(),
        };
        mockConfigService = {
            get: jest.fn(),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                model_selection_service_1.ModelSelectionService,
                {
                    provide: ai_model_repository_interface_1.AI_MODEL_REPOSITORY,
                    useValue: mockModelRepository,
                },
                {
                    provide: model_performance_service_1.ModelPerformanceService,
                    useValue: mockPerformanceService,
                },
                {
                    provide: ai_cache_service_1.AiCacheService,
                    useValue: mockCacheService,
                },
                {
                    provide: config_1.ConfigService,
                    useValue: mockConfigService,
                },
            ],
        }).compile();
        service = module.get(model_selection_service_1.ModelSelectionService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('selectOptimalModel', () => {
        const mockRequest = {
            taskType: 'threat-analysis',
            requirements: {
                maxLatency: 2000,
                minAccuracy: 0.9,
                priority: 'accuracy',
            },
        };
        it('should select optimal model based on requirements', async () => {
            // Arrange
            mockModelRepository.findByTaskType.mockResolvedValue([mockModelInfo]);
            mockPerformanceService.getModelMetrics.mockResolvedValue(mockPerformanceMetrics);
            mockConfigService.get.mockReturnValue(300000); // 5 minutes cache TTL
            // Act
            const result = await service.selectOptimalModel(mockRequest);
            // Assert
            expect(result).toEqual({
                modelId: 'model-1',
                providerType: 'openai',
                modelType: 'threat-detection',
                version: '1.0.0',
                parameters: expect.any(Object),
                score: expect.any(Number),
                selectedAt: expect.any(Date),
            });
            expect(mockModelRepository.findByTaskType).toHaveBeenCalledWith('threat-analysis');
            expect(mockPerformanceService.getModelMetrics).toHaveBeenCalledWith('model-1');
        });
        it('should return cached selection when available and valid', async () => {
            // Arrange
            const cachedSelection = {
                configuration: {
                    modelId: 'cached-model',
                    providerType: 'openai',
                    modelType: 'threat-detection',
                    version: '1.0.0',
                    parameters: {},
                    score: 0.95,
                    selectedAt: new Date(),
                },
                timestamp: new Date(),
            };
            // Mock cache hit with valid timestamp
            jest.spyOn(service, 'getCachedSelection').mockResolvedValue(cachedSelection);
            jest.spyOn(service, 'isCacheValid').mockReturnValue(true);
            // Act
            const result = await service.selectOptimalModel(mockRequest);
            // Assert
            expect(result).toEqual(cachedSelection.configuration);
            expect(mockModelRepository.findByTaskType).not.toHaveBeenCalled();
        });
        it('should throw error when no models are available', async () => {
            // Arrange
            mockModelRepository.findByTaskType.mockResolvedValue([]);
            // Act & Assert
            await expect(service.selectOptimalModel(mockRequest))
                .rejects.toThrow('No models available for task type: threat-analysis');
        });
        it('should filter out unhealthy models', async () => {
            // Arrange
            const unhealthyModel = {
                ...mockModelInfo,
                id: 'unhealthy-model',
                status: 'inactive',
            };
            mockModelRepository.findByTaskType.mockResolvedValue([mockModelInfo, unhealthyModel]);
            mockPerformanceService.getModelMetrics.mockResolvedValue(mockPerformanceMetrics);
            // Act
            const result = await service.selectOptimalModel(mockRequest);
            // Assert
            expect(result.modelId).toBe('model-1'); // Only healthy model selected
        });
        it('should handle model selection errors gracefully', async () => {
            // Arrange
            mockModelRepository.findByTaskType.mockRejectedValue(new Error('Database error'));
            // Act & Assert
            await expect(service.selectOptimalModel(mockRequest))
                .rejects.toThrow('Model selection failed: Database error');
        });
    });
    describe('selectRealTimeModel', () => {
        const mockRequest = {
            taskType: 'threat-analysis',
            requirements: {
                priority: 'balanced',
            },
        };
        it('should select model optimized for real-time processing', async () => {
            // Arrange
            mockModelRepository.findByTaskType.mockResolvedValue([mockModelInfo]);
            mockPerformanceService.getModelMetrics.mockResolvedValue({
                ...mockPerformanceMetrics,
                averageLatency: 500, // Low latency model
            });
            // Act
            const result = await service.selectRealTimeModel(mockRequest);
            // Assert
            expect(result.parameters).toEqual({
                temperature: 0.1, // Lower temperature for consistency
                maxTokens: 500, // Reduced tokens for speed
            });
        });
    });
    describe('selectBatchModel', () => {
        const mockRequest = {
            taskType: 'threat-analysis',
        };
        it('should select model optimized for batch processing', async () => {
            // Arrange
            mockModelRepository.findByTaskType.mockResolvedValue([mockModelInfo]);
            mockPerformanceService.getModelMetrics.mockResolvedValue(mockPerformanceMetrics);
            // Act
            const result = await service.selectBatchModel(mockRequest);
            // Assert
            expect(result).toBeDefined();
            // Batch optimization should prioritize throughput over latency
        });
    });
    describe('selectHighAccuracyModel', () => {
        const mockRequest = {
            taskType: 'threat-analysis',
        };
        it('should select model optimized for high accuracy', async () => {
            // Arrange
            mockModelRepository.findByTaskType.mockResolvedValue([mockModelInfo]);
            mockPerformanceService.getModelMetrics.mockResolvedValue(mockPerformanceMetrics);
            // Act
            const result = await service.selectHighAccuracyModel(mockRequest);
            // Assert
            expect(result.parameters).toEqual({
                temperature: 0.0, // Deterministic output
                topP: 0.9,
            });
        });
    });
    describe('getModelRecommendations', () => {
        it('should return top model recommendations', async () => {
            // Arrange
            const models = [
                { ...mockModelInfo, id: 'model-1' },
                { ...mockModelInfo, id: 'model-2' },
                { ...mockModelInfo, id: 'model-3' },
            ];
            mockModelRepository.findByTaskType.mockResolvedValue(models);
            mockPerformanceService.getModelMetrics.mockResolvedValue(mockPerformanceMetrics);
            // Act
            const recommendations = await service.getModelRecommendations('threat-analysis');
            // Assert
            expect(recommendations).toHaveLength(3);
            expect(recommendations[0]).toEqual({
                modelId: expect.any(String),
                modelName: expect.any(String),
                providerType: 'openai',
                accuracy: 0.95,
                latency: 1500,
                costPerRequest: 0.005,
                overallScore: expect.any(Number),
                strengths: expect.any(Array),
                weaknesses: expect.any(Array),
                useCases: ['threat-analysis'],
            });
        });
        it('should limit recommendations to top 5', async () => {
            // Arrange
            const models = Array.from({ length: 10 }, (_, i) => ({
                ...mockModelInfo,
                id: `model-${i}`,
            }));
            mockModelRepository.findByTaskType.mockResolvedValue(models);
            mockPerformanceService.getModelMetrics.mockResolvedValue(mockPerformanceMetrics);
            // Act
            const recommendations = await service.getModelRecommendations('threat-analysis');
            // Assert
            expect(recommendations).toHaveLength(5);
        });
    });
    describe('evaluateModelPerformance', () => {
        it('should evaluate model performance correctly', async () => {
            // Arrange
            mockModelRepository.findById.mockResolvedValue(mockModelInfo);
            mockPerformanceService.getModelMetrics.mockResolvedValue(mockPerformanceMetrics);
            mockPerformanceService.getCostMetrics.mockResolvedValue({
                costPerRequest: 0.005,
            });
            // Act
            const evaluation = await service.evaluateModelPerformance('model-1');
            // Assert
            expect(evaluation).toEqual({
                modelId: 'model-1',
                accuracy: 0.95,
                precision: 0.92,
                recall: 0.88,
                f1Score: 0.90,
                latency: 1500,
                throughput: 50,
                costPerRequest: 0.005,
                availability: 0.99,
                lastUpdated: expect.any(Date),
            });
        });
        it('should throw error for non-existent model', async () => {
            // Arrange
            mockModelRepository.findById.mockResolvedValue(null);
            // Act & Assert
            await expect(service.evaluateModelPerformance('non-existent'))
                .rejects.toThrow('Evaluation failed: Model not found: non-existent');
        });
    });
    describe('updateSelectionStrategy', () => {
        const mockFeedback = {
            modelId: 'model-1',
            actualAccuracy: 0.93,
            actualLatency: 1200,
            userRating: 4.5,
            comments: 'Good performance',
        };
        it('should update selection strategy based on feedback', async () => {
            // Arrange
            mockPerformanceService.updateModelMetrics.mockResolvedValue(undefined);
            // Act
            await service.updateSelectionStrategy(mockFeedback);
            // Assert
            expect(mockPerformanceService.updateModelMetrics).toHaveBeenCalledWith('model-1', {
                accuracy: 0.93,
                latency: 1200,
                userSatisfaction: 4.5,
            });
        });
        it('should handle feedback update errors', async () => {
            // Arrange
            mockPerformanceService.updateModelMetrics.mockRejectedValue(new Error('Update failed'));
            // Act & Assert
            await expect(service.updateSelectionStrategy(mockFeedback))
                .rejects.toThrow('Strategy update failed: Update failed');
        });
    });
    describe('model scoring', () => {
        it('should calculate model scores correctly', async () => {
            // Arrange
            const calculateModelScore = service.calculateModelScore.bind(service);
            mockPerformanceService.getModelMetrics.mockResolvedValue(mockPerformanceMetrics);
            const request = {
                taskType: 'threat-analysis',
                requirements: {
                    priority: 'accuracy',
                    minAccuracy: 0.9,
                    maxLatency: 2000,
                },
            };
            // Act
            const score = await calculateModelScore(mockModelInfo, request);
            // Assert
            expect(score).toEqual({
                accuracy: expect.any(Number),
                latency: expect.any(Number),
                cost: expect.any(Number),
                availability: expect.any(Number),
                throughput: expect.any(Number),
                total: expect.any(Number),
            });
            expect(score.total).toBeGreaterThan(0);
            expect(score.total).toBeLessThanOrEqual(1);
        });
        it('should apply different weights based on priority', async () => {
            // Arrange
            const getSelectionWeights = service.getSelectionWeights.bind(service);
            // Act
            const accuracyWeights = getSelectionWeights({ priority: 'accuracy' });
            const latencyWeights = getSelectionWeights({ priority: 'latency' });
            const balancedWeights = getSelectionWeights({ priority: 'balanced' });
            // Assert
            expect(accuracyWeights.accuracy).toBeGreaterThan(latencyWeights.accuracy);
            expect(latencyWeights.latency).toBeGreaterThan(accuracyWeights.latency);
            expect(balancedWeights.accuracy).toBe(0.25);
            expect(balancedWeights.latency).toBe(0.25);
        });
    });
    describe('cache management', () => {
        it('should generate consistent cache keys', () => {
            // Arrange
            const generateSelectionCacheKey = service.generateSelectionCacheKey.bind(service);
            const request1 = {
                taskType: 'threat-analysis',
                requirements: { priority: 'accuracy' },
            };
            const request2 = {
                taskType: 'threat-analysis',
                requirements: { priority: 'accuracy' },
            };
            const request3 = {
                taskType: 'threat-analysis',
                requirements: { priority: 'latency' },
            };
            // Act
            const key1 = generateSelectionCacheKey(request1);
            const key2 = generateSelectionCacheKey(request2);
            const key3 = generateSelectionCacheKey(request3);
            // Assert
            expect(key1).toBe(key2); // Same requests should generate same key
            expect(key1).not.toBe(key3); // Different requests should generate different keys
        });
        it('should validate cache entries correctly', () => {
            // Arrange
            const isCacheValid = service.isCacheValid.bind(service);
            mockConfigService.get.mockReturnValue(300000); // 5 minutes
            const recentSelection = {
                timestamp: new Date(Date.now() - 60000), // 1 minute ago
            };
            const oldSelection = {
                timestamp: new Date(Date.now() - 600000), // 10 minutes ago
            };
            // Act & Assert
            expect(isCacheValid(recentSelection)).toBe(true);
            expect(isCacheValid(oldSelection)).toBe(false);
        });
    });
    describe('model strengths and weaknesses identification', () => {
        it('should identify model strengths correctly', () => {
            // Arrange
            const identifyModelStrengths = service.identifyModelStrengths.bind(service);
            const highPerformanceMetrics = {
                accuracy: 0.95,
                averageLatency: 500,
                costPerRequest: 0.0005,
                availability: 0.999,
            };
            // Act
            const strengths = identifyModelStrengths(mockModelInfo, highPerformanceMetrics);
            // Assert
            expect(strengths).toContain('High accuracy');
            expect(strengths).toContain('Low latency');
            expect(strengths).toContain('Cost effective');
            expect(strengths).toContain('High availability');
        });
        it('should identify model weaknesses correctly', () => {
            // Arrange
            const identifyModelWeaknesses = service.identifyModelWeaknesses.bind(service);
            const lowPerformanceMetrics = {
                accuracy: 0.75,
                averageLatency: 8000,
                costPerRequest: 0.02,
                availability: 0.90,
            };
            // Act
            const weaknesses = identifyModelWeaknesses(mockModelInfo, lowPerformanceMetrics);
            // Assert
            expect(weaknesses).toContain('Lower accuracy');
            expect(weaknesses).toContain('Higher latency');
            expect(weaknesses).toContain('Higher cost');
            expect(weaknesses).toContain('Availability concerns');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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