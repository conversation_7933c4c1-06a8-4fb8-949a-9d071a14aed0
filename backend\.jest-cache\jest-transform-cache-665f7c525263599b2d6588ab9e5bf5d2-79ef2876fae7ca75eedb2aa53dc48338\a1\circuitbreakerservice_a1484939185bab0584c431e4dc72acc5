764840cf08e82fe5885256088b36247c
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CircuitBreakerService_1;
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CircuitBreakerService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const event_emitter_1 = require("@nestjs/event-emitter");
/**
 * Circuit Breaker Service
 *
 * Implements circuit breaker pattern for AI service calls to prevent
 * cascading failures and provide graceful degradation when services
 * are experiencing issues. Supports multiple circuit breakers with
 * configurable thresholds and recovery strategies.
 */
let CircuitBreakerService = CircuitBreakerService_1 = class CircuitBreakerService {
    constructor(configService, eventEmitter) {
        this.configService = configService;
        this.eventEmitter = eventEmitter;
        this.logger = new common_1.Logger(CircuitBreakerService_1.name);
        this.circuitBreakers = new Map();
        this.defaultConfig = {
            failureThreshold: this.configService.get('ai.circuitBreaker.failureThreshold', 5),
            recoveryTimeout: this.configService.get('ai.circuitBreaker.recoveryTimeout', 60000),
            monitoringPeriod: this.configService.get('ai.circuitBreaker.monitoringPeriod', 10000),
            halfOpenMaxCalls: this.configService.get('ai.circuitBreaker.halfOpenMaxCalls', 3),
            successThreshold: this.configService.get('ai.circuitBreaker.successThreshold', 2),
        };
    }
    /**
     * Executes a function with circuit breaker protection
     */
    async execute(circuitName, operation, config) {
        const circuitBreaker = this.getOrCreateCircuitBreaker(circuitName, config);
        // Check circuit state before execution
        if (circuitBreaker.state === CircuitState.OPEN) {
            if (this.shouldAttemptReset(circuitBreaker)) {
                circuitBreaker.state = CircuitState.HALF_OPEN;
                circuitBreaker.halfOpenCalls = 0;
                this.logger.debug(`Circuit breaker ${circuitName} moved to HALF_OPEN state`);
            }
            else {
                throw new CircuitBreakerOpenError(`Circuit breaker ${circuitName} is OPEN`, circuitName);
            }
        }
        if (circuitBreaker.state === CircuitState.HALF_OPEN) {
            if (circuitBreaker.halfOpenCalls >= circuitBreaker.config.halfOpenMaxCalls) {
                throw new CircuitBreakerOpenError(`Circuit breaker ${circuitName} HALF_OPEN call limit exceeded`, circuitName);
            }
            circuitBreaker.halfOpenCalls++;
        }
        const startTime = Date.now();
        try {
            const result = await operation();
            // Record successful execution
            this.recordSuccess(circuitBreaker, Date.now() - startTime);
            return result;
        }
        catch (error) {
            // Record failed execution
            this.recordFailure(circuitBreaker, error, Date.now() - startTime);
            throw error;
        }
    }
    /**
     * Gets the current state of a circuit breaker
     */
    getCircuitState(circuitName) {
        const circuitBreaker = this.circuitBreakers.get(circuitName);
        return circuitBreaker ? circuitBreaker.state : CircuitState.CLOSED;
    }
    /**
     * Gets circuit breaker statistics
     */
    getCircuitStats(circuitName) {
        const circuitBreaker = this.circuitBreakers.get(circuitName);
        if (!circuitBreaker) {
            return null;
        }
        return {
            name: circuitName,
            state: circuitBreaker.state,
            failureCount: circuitBreaker.failureCount,
            successCount: circuitBreaker.successCount,
            lastFailureTime: circuitBreaker.lastFailureTime,
            lastSuccessTime: circuitBreaker.lastSuccessTime,
            totalCalls: circuitBreaker.totalCalls,
            failureRate: this.calculateFailureRate(circuitBreaker),
            averageResponseTime: this.calculateAverageResponseTime(circuitBreaker),
            config: circuitBreaker.config,
        };
    }
    /**
     * Gets all circuit breaker statuses
     */
    getStatus() {
        const status = {};
        for (const [name] of this.circuitBreakers) {
            const stats = this.getCircuitStats(name);
            if (stats) {
                status[name] = stats;
            }
        }
        return status;
    }
    /**
     * Manually opens a circuit breaker
     */
    openCircuit(circuitName) {
        const circuitBreaker = this.circuitBreakers.get(circuitName);
        if (circuitBreaker) {
            circuitBreaker.state = CircuitState.OPEN;
            circuitBreaker.lastFailureTime = new Date();
            this.logger.warn(`Circuit breaker ${circuitName} manually opened`);
            this.emitStateChange(circuitName, CircuitState.OPEN);
        }
    }
    /**
     * Manually closes a circuit breaker
     */
    closeCircuit(circuitName) {
        const circuitBreaker = this.circuitBreakers.get(circuitName);
        if (circuitBreaker) {
            circuitBreaker.state = CircuitState.CLOSED;
            circuitBreaker.failureCount = 0;
            circuitBreaker.halfOpenCalls = 0;
            this.logger.info(`Circuit breaker ${circuitName} manually closed`);
            this.emitStateChange(circuitName, CircuitState.CLOSED);
        }
    }
    /**
     * Resets circuit breaker statistics
     */
    resetCircuit(circuitName) {
        const circuitBreaker = this.circuitBreakers.get(circuitName);
        if (circuitBreaker) {
            circuitBreaker.failureCount = 0;
            circuitBreaker.successCount = 0;
            circuitBreaker.totalCalls = 0;
            circuitBreaker.halfOpenCalls = 0;
            circuitBreaker.responseTimes = [];
            circuitBreaker.lastFailureTime = null;
            circuitBreaker.lastSuccessTime = null;
            circuitBreaker.state = CircuitState.CLOSED;
            this.logger.info(`Circuit breaker ${circuitName} reset`);
            this.emitStateChange(circuitName, CircuitState.CLOSED);
        }
    }
    /**
     * Removes a circuit breaker
     */
    removeCircuit(circuitName) {
        if (this.circuitBreakers.delete(circuitName)) {
            this.logger.info(`Circuit breaker ${circuitName} removed`);
        }
    }
    // Private helper methods
    getOrCreateCircuitBreaker(name, config) {
        let circuitBreaker = this.circuitBreakers.get(name);
        if (!circuitBreaker) {
            circuitBreaker = {
                name,
                state: CircuitState.CLOSED,
                failureCount: 0,
                successCount: 0,
                totalCalls: 0,
                halfOpenCalls: 0,
                lastFailureTime: null,
                lastSuccessTime: null,
                responseTimes: [],
                config: { ...this.defaultConfig, ...config },
            };
            this.circuitBreakers.set(name, circuitBreaker);
            this.logger.debug(`Created circuit breaker: ${name}`);
        }
        return circuitBreaker;
    }
    recordSuccess(circuitBreaker, responseTime) {
        circuitBreaker.successCount++;
        circuitBreaker.totalCalls++;
        circuitBreaker.lastSuccessTime = new Date();
        circuitBreaker.responseTimes.push(responseTime);
        // Keep only recent response times
        if (circuitBreaker.responseTimes.length > 100) {
            circuitBreaker.responseTimes = circuitBreaker.responseTimes.slice(-50);
        }
        // Handle state transitions
        if (circuitBreaker.state === CircuitState.HALF_OPEN) {
            if (circuitBreaker.halfOpenCalls >= circuitBreaker.config.successThreshold) {
                circuitBreaker.state = CircuitState.CLOSED;
                circuitBreaker.failureCount = 0;
                circuitBreaker.halfOpenCalls = 0;
                this.logger.info(`Circuit breaker ${circuitBreaker.name} closed after successful recovery`);
                this.emitStateChange(circuitBreaker.name, CircuitState.CLOSED);
            }
        }
        this.logger.debug(`Circuit breaker ${circuitBreaker.name} recorded success`);
    }
    recordFailure(circuitBreaker, error, responseTime) {
        circuitBreaker.failureCount++;
        circuitBreaker.totalCalls++;
        circuitBreaker.lastFailureTime = new Date();
        circuitBreaker.responseTimes.push(responseTime);
        // Check if we should open the circuit
        if (circuitBreaker.state === CircuitState.CLOSED) {
            if (circuitBreaker.failureCount >= circuitBreaker.config.failureThreshold) {
                circuitBreaker.state = CircuitState.OPEN;
                this.logger.warn(`Circuit breaker ${circuitBreaker.name} opened due to ${circuitBreaker.failureCount} failures`);
                this.emitStateChange(circuitBreaker.name, CircuitState.OPEN);
            }
        }
        else if (circuitBreaker.state === CircuitState.HALF_OPEN) {
            // Failure in half-open state immediately opens the circuit
            circuitBreaker.state = CircuitState.OPEN;
            circuitBreaker.halfOpenCalls = 0;
            this.logger.warn(`Circuit breaker ${circuitBreaker.name} reopened after failure in HALF_OPEN state`);
            this.emitStateChange(circuitBreaker.name, CircuitState.OPEN);
        }
        this.logger.debug(`Circuit breaker ${circuitBreaker.name} recorded failure: ${error.message}`);
    }
    shouldAttemptReset(circuitBreaker) {
        if (!circuitBreaker.lastFailureTime) {
            return true;
        }
        const timeSinceLastFailure = Date.now() - circuitBreaker.lastFailureTime.getTime();
        return timeSinceLastFailure >= circuitBreaker.config.recoveryTimeout;
    }
    calculateFailureRate(circuitBreaker) {
        if (circuitBreaker.totalCalls === 0) {
            return 0;
        }
        return circuitBreaker.failureCount / circuitBreaker.totalCalls;
    }
    calculateAverageResponseTime(circuitBreaker) {
        if (circuitBreaker.responseTimes.length === 0) {
            return 0;
        }
        const sum = circuitBreaker.responseTimes.reduce((acc, time) => acc + time, 0);
        return sum / circuitBreaker.responseTimes.length;
    }
    emitStateChange(circuitName, newState) {
        this.eventEmitter.emit('circuit-breaker.state-changed', {
            circuitName,
            newState,
            timestamp: new Date(),
        });
    }
};
exports.CircuitBreakerService = CircuitBreakerService;
exports.CircuitBreakerService = CircuitBreakerService = CircuitBreakerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object, typeof (_b = typeof event_emitter_1.EventEmitter2 !== "undefined" && event_emitter_1.EventEmitter2) === "function" ? _b : Object])
], CircuitBreakerService);
// Enums and interfaces
var CircuitState;
(function (CircuitState) {
    CircuitState["CLOSED"] = "CLOSED";
    CircuitState["OPEN"] = "OPEN";
    CircuitState["HALF_OPEN"] = "HALF_OPEN";
})(CircuitState || (CircuitState = {}));
class CircuitBreakerOpenError extends Error {
    constructor(message, circuitName) {
        super(message);
        this.circuitName = circuitName;
        this.name = 'CircuitBreakerOpenError';
    }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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