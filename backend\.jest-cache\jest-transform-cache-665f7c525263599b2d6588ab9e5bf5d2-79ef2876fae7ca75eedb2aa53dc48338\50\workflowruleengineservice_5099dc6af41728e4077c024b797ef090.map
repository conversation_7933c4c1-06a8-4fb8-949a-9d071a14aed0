{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\services\\workflow-rule-engine.service.ts", "mappings": ";;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAE/C;;;;;;;;;;;;;;;;;;GAkBG;AAEI,IAAM,kBAAkB,0BAAxB,MAAM,kBAAkB;IAK7B,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAJxC,WAAM,GAAG,IAAI,eAAM,CAAC,oBAAkB,CAAC,IAAI,CAAC,CAAC;QAC7C,oBAAe,GAAqB,IAAI,GAAG,EAAE,CAAC;QAC9C,oBAAe,GAA0B,IAAI,GAAG,EAAE,CAAC;QAGlE,IAAI,CAAC,0BAA0B,EAAE,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,SAAc,EAAE,OAAY;QAClD,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YAExE,IAAI,OAAO,SAAS,KAAK,SAAS,EAAE,CAAC;gBACnC,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;gBAClC,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAC3D,CAAC;YAED,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;gBAClC,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAChE,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,OAAO,SAAS,EAAE,CAAC,CAAC;YAChE,OAAO,KAAK,CAAC;QAEf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAChF,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,UAAkB,EAAE,OAAY;QACvD,IAAI,CAAC;YACH,oBAAoB;YACpB,MAAM,QAAQ,GAAG,GAAG,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5D,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACvC,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC5C,CAAC;YAED,gCAAgC;YAChC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAEhE,eAAe;YACf,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAE3C,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACjF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB,CAAC,SAAc,EAAE,OAAY;QAChE,IAAI,CAAC;YACH,2BAA2B;YAC3B,IAAI,SAAS,CAAC,GAAG,EAAE,CAAC;gBAClB,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAC/B,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC,CACjF,CAAC;gBACF,OAAO,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;YACzC,CAAC;YAED,IAAI,SAAS,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAC/B,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC,CAChF,CAAC;gBACF,OAAO,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;YACxC,CAAC;YAED,IAAI,SAAS,CAAC,GAAG,EAAE,CAAC;gBAClB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;gBACpE,OAAO,CAAC,MAAM,CAAC;YACjB,CAAC;YAED,8BAA8B;YAC9B,IAAI,SAAS,CAAC,KAAK,IAAI,SAAS,CAAC,QAAQ,IAAI,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC/E,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAC3D,CAAC;YAED,wBAAwB;YACxB,IAAI,SAAS,CAAC,QAAQ,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC;gBACzC,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAClF,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gCAAgC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YAC9E,OAAO,KAAK,CAAC;QAEf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACvF,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,SAAc,EAAE,OAAY;QAC3D,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAChE,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAEjE,QAAQ,SAAS,CAAC,QAAQ,EAAE,CAAC;gBAC3B,KAAK,IAAI,CAAC;gBACV,KAAK,IAAI;oBACP,OAAO,UAAU,IAAI,YAAY,CAAC;gBACpC,KAAK,KAAK,CAAC;gBACX,KAAK,IAAI;oBACP,OAAO,UAAU,IAAI,YAAY,CAAC;gBACpC,KAAK,IAAI,CAAC;gBACV,KAAK,GAAG;oBACN,OAAO,UAAU,GAAG,YAAY,CAAC;gBACnC,KAAK,KAAK,CAAC;gBACX,KAAK,IAAI;oBACP,OAAO,UAAU,IAAI,YAAY,CAAC;gBACpC,KAAK,IAAI,CAAC;gBACV,KAAK,GAAG;oBACN,OAAO,UAAU,GAAG,YAAY,CAAC;gBACnC,KAAK,KAAK,CAAC;gBACX,KAAK,IAAI;oBACP,OAAO,UAAU,IAAI,YAAY,CAAC;gBACpC,KAAK,IAAI;oBACP,OAAO,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;gBAC1E,KAAK,QAAQ;oBACX,OAAO,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;gBAC3E,KAAK,UAAU;oBACb,OAAO,MAAM,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;gBAC3D,KAAK,cAAc;oBACjB,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;gBAC5D,KAAK,aAAa;oBAChB,OAAO,MAAM,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;gBAC7D,KAAK,WAAW;oBACd,OAAO,MAAM,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;gBAC3D,KAAK,OAAO;oBACV,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;oBAC/C,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;gBACxC,KAAK,QAAQ;oBACX,OAAO,UAAU,KAAK,SAAS,IAAI,UAAU,KAAK,IAAI,CAAC;gBACzD,KAAK,YAAY;oBACf,OAAO,UAAU,KAAK,SAAS,IAAI,UAAU,KAAK,IAAI,CAAC;gBACzD;oBACE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gCAAgC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;oBACvE,OAAO,KAAK,CAAC;YACjB,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACjF,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,YAAoB,EAAE,IAAW,EAAE,OAAY;QAC5E,IAAI,CAAC;YACH,oBAAoB;YACpB,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;YAEtE,mCAAmC;YACnC,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC3C,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;gBAC9D,OAAO,MAAM,cAAc,CAAC,GAAG,YAAY,CAAC,CAAC;YAC/C,CAAC;YAED,qBAAqB;YACrB,QAAQ,YAAY,EAAE,CAAC;gBACrB,KAAK,QAAQ;oBACX,OAAO,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9C,KAAK,KAAK;oBACR,OAAO,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;gBACxC,KAAK,KAAK;oBACR,OAAO,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;gBACxC,KAAK,KAAK;oBACR,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC;gBACnC,KAAK,KAAK;oBACR,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC;gBACnC,KAAK,OAAO;oBACV,OAAO,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7C,KAAK,QAAQ;oBACX,OAAO,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9C,KAAK,QAAQ;oBACX,OAAO,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/D,KAAK,KAAK;oBACR,OAAO,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5D,KAAK,QAAQ;oBACX,OAAO,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChF,KAAK,WAAW;oBACd,OAAO,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClF,KAAK,aAAa;oBAChB,OAAO,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnE,KAAK,KAAK;oBACR,OAAO,IAAI,IAAI,EAAE,CAAC;gBACpB,KAAK,OAAO;oBACV,OAAO,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChD,KAAK,QAAQ;oBACX,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;gBACvB,KAAK,OAAO;oBACV,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,KAAK,OAAO;oBACV,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,KAAK,MAAM;oBACT,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpC,KAAK,KAAK;oBACR,OAAO,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnC,KAAK,OAAO;oBACV,OAAO,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC/C,KAAK,OAAO;oBACV,OAAO,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;gBAC/C,KAAK,MAAM;oBACT,OAAO,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gBACxC,KAAK,OAAO;oBACV,OAAO,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChE,KAAK,MAAM;oBACT,OAAO,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC7F,KAAK,SAAS;oBACZ,OAAO,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3F;oBACE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,YAAY,EAAE,CAAC,CAAC;oBACtD,OAAO,IAAI,CAAC;YAChB,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC/E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,KAAa,EAAE,OAAY;QAC/C,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC/B,IAAI,KAAK,GAAG,OAAO,CAAC;YAEpB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;oBAC1C,OAAO,SAAS,CAAC;gBACnB,CAAC;gBACD,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;YACtB,CAAC;YAED,OAAO,KAAK,CAAC;QAEf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,KAAK,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACtE,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,KAAU,EAAE,OAAY;QAC3C,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACvD,qBAAqB;YACrB,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;YAClD,oBAAoB;YACpB,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAC5D,CAAC;QAED,gBAAgB;QAChB,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,UAAkB,EAAE,OAAY;QAC7D,IAAI,CAAC;YACH,gDAAgD;YAChD,iFAAiF;YAEjF,oBAAoB;YACpB,IAAI,mBAAmB,GAAG,UAAU,CAAC;YACrC,MAAM,aAAa,GAAG,8BAA8B,CAAC;YAErD,mBAAmB,GAAG,mBAAmB,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;gBAClF,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;gBACnD,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC;YAEH,2CAA2C;YAC3C,IAAI,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,EAAE,CAAC;gBAC/C,4DAA4D;gBAC5D,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,SAAS,GAAG,mBAAmB,CAAC,CAAC;gBAC3D,OAAO,IAAI,EAAE,CAAC;YAChB,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAChD,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC9E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,UAAkB;QACzC,yEAAyE;QACzE,MAAM,iBAAiB,GAAG;YACxB,cAAc;YACd,aAAa;YACb,WAAW;YACX,eAAe;YACf,iBAAiB;YACjB,kBAAkB;YAClB,WAAW;YACX,UAAU;YACV,UAAU;YACV,YAAY;SACb,CAAC;QAEF,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,IAAY,EAAE,IAAc;QAC3C,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,IAAI,EAAE,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACK,0BAA0B;QAChC,uDAAuD;QACvD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,KAAU;QAC/B,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YACtD,OAAO,KAAK,CAAC,MAAM,CAAC;QACtB,CAAC;QACD,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YAChD,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;QACnC,CAAC;QACD,OAAO,CAAC,CAAC;IACX,CAAC;IAEO,WAAW,CAAC,MAAa;QAC/B,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClE,CAAC;IAEO,WAAW,CAAC,MAAa;QAC/B,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAClC,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;IAClD,CAAC;IAEO,aAAa,CAAC,KAAU;QAC9B,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,KAAK,CAAC,MAAM,CAAC;QACtB,CAAC;QACD,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvD,CAAC;IAEO,cAAc,CAAC,MAAa;QAClC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;YAAE,OAAO,EAAE,CAAC;QACtC,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;IAC9B,CAAC;IAEO,cAAc,CAAC,MAAa,EAAE,SAAc;QAClD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;YAAE,OAAO,EAAE,CAAC;QACtC,+BAA+B;QAC/B,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC;IACrD,CAAC;IAEO,WAAW,CAAC,MAAa,EAAE,SAAc;QAC/C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;YAAE,OAAO,EAAE,CAAC;QACtC,4BAA4B;QAC5B,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;IACpC,CAAC;IAEO,cAAc,CAAC,MAAa,EAAE,YAAiB,EAAE,SAAiB;QACxE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;YAAE,OAAO,YAAY,CAAC;QAChD,+BAA+B;QAC/B,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YAChC,QAAQ,SAAS,EAAE,CAAC;gBAClB,KAAK,KAAK,CAAC,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;gBAC7B,KAAK,UAAU,CAAC,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;gBAClC,KAAK,QAAQ,CAAC,CAAC,OAAO,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;gBACxC,OAAO,CAAC,CAAC,OAAO,GAAG,CAAC;YACtB,CAAC;QACH,CAAC,EAAE,YAAY,CAAC,CAAC;IACnB,CAAC;IAEO,gBAAgB,CAAC,KAAU,EAAE,KAAU,EAAE,OAAe,MAAM;QACpE,MAAM,EAAE,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3B,MAAM,EAAE,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;QAErD,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,cAAc,CAAC,CAAC,OAAO,MAAM,CAAC;YACnC,KAAK,SAAS,CAAC,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;YACjD,KAAK,SAAS,CAAC,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;YACxD,KAAK,OAAO,CAAC,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YAC3D,KAAK,MAAM,CAAC,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YAC/D,OAAO,CAAC,CAAC,OAAO,MAAM,CAAC;QACzB,CAAC;IACH,CAAC;IAEO,kBAAkB,CAAC,IAAS,EAAE,MAAc;QAClD,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;QACzB,sEAAsE;QACtE,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,UAAU;QACR,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAC7B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,aAAa;QACX,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI;YAC/B,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI;SACrC,CAAC;IACJ,CAAC;CACF,CAAA;AA/bY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;yDAMiC,sBAAa,oBAAb,sBAAa;GAL9C,kBAAkB,CA+b9B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\services\\workflow-rule-engine.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\n\r\n/**\r\n * Workflow Rule Engine Service\r\n * \r\n * Provides advanced rule evaluation and conditional logic processing for workflows:\r\n * - Complex condition evaluation with multiple operators and functions\r\n * - Variable substitution and expression parsing\r\n * - Custom function registration and execution\r\n * - Performance optimization with expression caching\r\n * - Security validation and sandboxed execution\r\n * - Integration with workflow context and data\r\n * \r\n * Features:\r\n * - Advanced expression language with mathematical and logical operations\r\n * - Built-in functions for common workflow operations\r\n * - Custom function registration for domain-specific logic\r\n * - Variable scoping and context management\r\n * - Performance optimization with compiled expressions\r\n * - Security controls with execution limits and validation\r\n */\r\n@Injectable()\r\nexport class WorkflowRuleEngine {\r\n  private readonly logger = new Logger(WorkflowRuleEngine.name);\r\n  private readonly expressionCache: Map<string, any> = new Map();\r\n  private readonly customFunctions: Map<string, Function> = new Map();\r\n\r\n  constructor(private readonly configService: ConfigService) {\r\n    this.initializeBuiltInFunctions();\r\n  }\r\n\r\n  /**\r\n   * Evaluate condition expression\r\n   */\r\n  async evaluateCondition(condition: any, context: any): Promise<boolean> {\r\n    try {\r\n      this.logger.debug(`Evaluating condition: ${JSON.stringify(condition)}`);\r\n\r\n      if (typeof condition === 'boolean') {\r\n        return condition;\r\n      }\r\n\r\n      if (typeof condition === 'string') {\r\n        return await this.evaluateExpression(condition, context);\r\n      }\r\n\r\n      if (typeof condition === 'object') {\r\n        return await this.evaluateObjectCondition(condition, context);\r\n      }\r\n\r\n      this.logger.warn(`Unknown condition type: ${typeof condition}`);\r\n      return false;\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Condition evaluation failed: ${error.message}`, error.stack);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Evaluate expression string\r\n   */\r\n  async evaluateExpression(expression: string, context: any): Promise<any> {\r\n    try {\r\n      // Check cache first\r\n      const cacheKey = `${expression}_${JSON.stringify(context)}`;\r\n      if (this.expressionCache.has(cacheKey)) {\r\n        return this.expressionCache.get(cacheKey);\r\n      }\r\n\r\n      // Parse and evaluate expression\r\n      const result = await this.parseAndEvaluate(expression, context);\r\n\r\n      // Cache result\r\n      this.expressionCache.set(cacheKey, result);\r\n\r\n      return result;\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Expression evaluation failed: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Evaluate object-based condition\r\n   */\r\n  private async evaluateObjectCondition(condition: any, context: any): Promise<boolean> {\r\n    try {\r\n      // Handle logical operators\r\n      if (condition.and) {\r\n        const results = await Promise.all(\r\n          condition.and.map(subCondition => this.evaluateCondition(subCondition, context))\r\n        );\r\n        return results.every(result => result);\r\n      }\r\n\r\n      if (condition.or) {\r\n        const results = await Promise.all(\r\n          condition.or.map(subCondition => this.evaluateCondition(subCondition, context))\r\n        );\r\n        return results.some(result => result);\r\n      }\r\n\r\n      if (condition.not) {\r\n        const result = await this.evaluateCondition(condition.not, context);\r\n        return !result;\r\n      }\r\n\r\n      // Handle comparison operators\r\n      if (condition.field && condition.operator && condition.hasOwnProperty('value')) {\r\n        return await this.evaluateComparison(condition, context);\r\n      }\r\n\r\n      // Handle function calls\r\n      if (condition.function && condition.args) {\r\n        return await this.evaluateFunction(condition.function, condition.args, context);\r\n      }\r\n\r\n      this.logger.warn(`Unknown condition structure: ${JSON.stringify(condition)}`);\r\n      return false;\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Object condition evaluation failed: ${error.message}`, error.stack);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Evaluate comparison operation\r\n   */\r\n  private async evaluateComparison(condition: any, context: any): Promise<boolean> {\r\n    try {\r\n      const fieldValue = this.getFieldValue(condition.field, context);\r\n      const compareValue = this.resolveValue(condition.value, context);\r\n\r\n      switch (condition.operator) {\r\n        case 'eq':\r\n        case '==':\r\n          return fieldValue == compareValue;\r\n        case 'neq':\r\n        case '!=':\r\n          return fieldValue != compareValue;\r\n        case 'gt':\r\n        case '>':\r\n          return fieldValue > compareValue;\r\n        case 'gte':\r\n        case '>=':\r\n          return fieldValue >= compareValue;\r\n        case 'lt':\r\n        case '<':\r\n          return fieldValue < compareValue;\r\n        case 'lte':\r\n        case '<=':\r\n          return fieldValue <= compareValue;\r\n        case 'in':\r\n          return Array.isArray(compareValue) && compareValue.includes(fieldValue);\r\n        case 'not_in':\r\n          return Array.isArray(compareValue) && !compareValue.includes(fieldValue);\r\n        case 'contains':\r\n          return String(fieldValue).includes(String(compareValue));\r\n        case 'not_contains':\r\n          return !String(fieldValue).includes(String(compareValue));\r\n        case 'starts_with':\r\n          return String(fieldValue).startsWith(String(compareValue));\r\n        case 'ends_with':\r\n          return String(fieldValue).endsWith(String(compareValue));\r\n        case 'regex':\r\n          const regex = new RegExp(String(compareValue));\r\n          return regex.test(String(fieldValue));\r\n        case 'exists':\r\n          return fieldValue !== undefined && fieldValue !== null;\r\n        case 'not_exists':\r\n          return fieldValue === undefined || fieldValue === null;\r\n        default:\r\n          this.logger.warn(`Unknown comparison operator: ${condition.operator}`);\r\n          return false;\r\n      }\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Comparison evaluation failed: ${error.message}`, error.stack);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Evaluate function call\r\n   */\r\n  private async evaluateFunction(functionName: string, args: any[], context: any): Promise<any> {\r\n    try {\r\n      // Resolve arguments\r\n      const resolvedArgs = args.map(arg => this.resolveValue(arg, context));\r\n\r\n      // Check for custom functions first\r\n      if (this.customFunctions.has(functionName)) {\r\n        const customFunction = this.customFunctions.get(functionName);\r\n        return await customFunction(...resolvedArgs);\r\n      }\r\n\r\n      // Built-in functions\r\n      switch (functionName) {\r\n        case 'length':\r\n          return this.lengthFunction(resolvedArgs[0]);\r\n        case 'sum':\r\n          return this.sumFunction(resolvedArgs);\r\n        case 'avg':\r\n          return this.avgFunction(resolvedArgs);\r\n        case 'min':\r\n          return Math.min(...resolvedArgs);\r\n        case 'max':\r\n          return Math.max(...resolvedArgs);\r\n        case 'count':\r\n          return this.countFunction(resolvedArgs[0]);\r\n        case 'unique':\r\n          return this.uniqueFunction(resolvedArgs[0]);\r\n        case 'filter':\r\n          return this.filterFunction(resolvedArgs[0], resolvedArgs[1]);\r\n        case 'map':\r\n          return this.mapFunction(resolvedArgs[0], resolvedArgs[1]);\r\n        case 'reduce':\r\n          return this.reduceFunction(resolvedArgs[0], resolvedArgs[1], resolvedArgs[2]);\r\n        case 'date_diff':\r\n          return this.dateDiffFunction(resolvedArgs[0], resolvedArgs[1], resolvedArgs[2]);\r\n        case 'format_date':\r\n          return this.formatDateFunction(resolvedArgs[0], resolvedArgs[1]);\r\n        case 'now':\r\n          return new Date();\r\n        case 'today':\r\n          return new Date().toISOString().split('T')[0];\r\n        case 'random':\r\n          return Math.random();\r\n        case 'round':\r\n          return Math.round(resolvedArgs[0]);\r\n        case 'floor':\r\n          return Math.floor(resolvedArgs[0]);\r\n        case 'ceil':\r\n          return Math.ceil(resolvedArgs[0]);\r\n        case 'abs':\r\n          return Math.abs(resolvedArgs[0]);\r\n        case 'upper':\r\n          return String(resolvedArgs[0]).toUpperCase();\r\n        case 'lower':\r\n          return String(resolvedArgs[0]).toLowerCase();\r\n        case 'trim':\r\n          return String(resolvedArgs[0]).trim();\r\n        case 'split':\r\n          return String(resolvedArgs[0]).split(String(resolvedArgs[1]));\r\n        case 'join':\r\n          return Array.isArray(resolvedArgs[0]) ? resolvedArgs[0].join(String(resolvedArgs[1])) : '';\r\n        case 'replace':\r\n          return String(resolvedArgs[0]).replace(String(resolvedArgs[1]), String(resolvedArgs[2]));\r\n        default:\r\n          this.logger.warn(`Unknown function: ${functionName}`);\r\n          return null;\r\n      }\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Function evaluation failed: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get field value from context using dot notation\r\n   */\r\n  private getFieldValue(field: string, context: any): any {\r\n    try {\r\n      const parts = field.split('.');\r\n      let value = context;\r\n\r\n      for (const part of parts) {\r\n        if (value === null || value === undefined) {\r\n          return undefined;\r\n        }\r\n        value = value[part];\r\n      }\r\n\r\n      return value;\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to get field value: ${field}`, error.stack);\r\n      return undefined;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Resolve value (could be literal, variable reference, or expression)\r\n   */\r\n  private resolveValue(value: any, context: any): any {\r\n    if (typeof value === 'string' && value.startsWith('$')) {\r\n      // Variable reference\r\n      return this.getFieldValue(value.substring(1), context);\r\n    }\r\n\r\n    if (typeof value === 'object' && value.expression) {\r\n      // Nested expression\r\n      return this.evaluateExpression(value.expression, context);\r\n    }\r\n\r\n    // Literal value\r\n    return value;\r\n  }\r\n\r\n  /**\r\n   * Parse and evaluate expression string\r\n   */\r\n  private async parseAndEvaluate(expression: string, context: any): Promise<any> {\r\n    try {\r\n      // Simple expression parser for basic operations\r\n      // In a production system, you might want to use a more robust parser like mathjs\r\n\r\n      // Replace variables\r\n      let processedExpression = expression;\r\n      const variableRegex = /\\$([a-zA-Z_][a-zA-Z0-9_.]*)/g;\r\n      \r\n      processedExpression = processedExpression.replace(variableRegex, (match, varName) => {\r\n        const value = this.getFieldValue(varName, context);\r\n        return JSON.stringify(value);\r\n      });\r\n\r\n      // For security, only allow safe operations\r\n      if (this.isSafeExpression(processedExpression)) {\r\n        // Use Function constructor for evaluation (safer than eval)\r\n        const func = new Function('return ' + processedExpression);\r\n        return func();\r\n      } else {\r\n        throw new Error('Unsafe expression detected');\r\n      }\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Expression parsing failed: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if expression is safe to evaluate\r\n   */\r\n  private isSafeExpression(expression: string): boolean {\r\n    // Basic safety check - in production, use a more comprehensive whitelist\r\n    const dangerousPatterns = [\r\n      /require\\s*\\(/,\r\n      /import\\s*\\(/,\r\n      /eval\\s*\\(/,\r\n      /Function\\s*\\(/,\r\n      /setTimeout\\s*\\(/,\r\n      /setInterval\\s*\\(/,\r\n      /process\\./,\r\n      /global\\./,\r\n      /window\\./,\r\n      /document\\./,\r\n    ];\r\n\r\n    return !dangerousPatterns.some(pattern => pattern.test(expression));\r\n  }\r\n\r\n  /**\r\n   * Register custom function\r\n   */\r\n  registerFunction(name: string, func: Function): void {\r\n    this.customFunctions.set(name, func);\r\n    this.logger.debug(`Registered custom function: ${name}`);\r\n  }\r\n\r\n  /**\r\n   * Initialize built-in functions\r\n   */\r\n  private initializeBuiltInFunctions(): void {\r\n    // Additional built-in functions can be registered here\r\n    this.logger.debug('Initialized built-in functions');\r\n  }\r\n\r\n  /**\r\n   * Built-in function implementations\r\n   */\r\n  private lengthFunction(value: any): number {\r\n    if (Array.isArray(value) || typeof value === 'string') {\r\n      return value.length;\r\n    }\r\n    if (typeof value === 'object' && value !== null) {\r\n      return Object.keys(value).length;\r\n    }\r\n    return 0;\r\n  }\r\n\r\n  private sumFunction(values: any[]): number {\r\n    return values.reduce((sum, val) => sum + (Number(val) || 0), 0);\r\n  }\r\n\r\n  private avgFunction(values: any[]): number {\r\n    if (values.length === 0) return 0;\r\n    return this.sumFunction(values) / values.length;\r\n  }\r\n\r\n  private countFunction(value: any): number {\r\n    if (Array.isArray(value)) {\r\n      return value.length;\r\n    }\r\n    return value !== null && value !== undefined ? 1 : 0;\r\n  }\r\n\r\n  private uniqueFunction(values: any[]): any[] {\r\n    if (!Array.isArray(values)) return [];\r\n    return [...new Set(values)];\r\n  }\r\n\r\n  private filterFunction(values: any[], condition: any): any[] {\r\n    if (!Array.isArray(values)) return [];\r\n    // Simple filter implementation\r\n    return values.filter(value => value === condition);\r\n  }\r\n\r\n  private mapFunction(values: any[], transform: any): any[] {\r\n    if (!Array.isArray(values)) return [];\r\n    // Simple map implementation\r\n    return values.map(value => value);\r\n  }\r\n\r\n  private reduceFunction(values: any[], initialValue: any, operation: string): any {\r\n    if (!Array.isArray(values)) return initialValue;\r\n    // Simple reduce implementation\r\n    return values.reduce((acc, val) => {\r\n      switch (operation) {\r\n        case 'sum': return acc + val;\r\n        case 'multiply': return acc * val;\r\n        case 'concat': return acc + String(val);\r\n        default: return acc;\r\n      }\r\n    }, initialValue);\r\n  }\r\n\r\n  private dateDiffFunction(date1: any, date2: any, unit: string = 'days'): number {\r\n    const d1 = new Date(date1);\r\n    const d2 = new Date(date2);\r\n    const diffMs = Math.abs(d2.getTime() - d1.getTime());\r\n\r\n    switch (unit) {\r\n      case 'milliseconds': return diffMs;\r\n      case 'seconds': return Math.floor(diffMs / 1000);\r\n      case 'minutes': return Math.floor(diffMs / (1000 * 60));\r\n      case 'hours': return Math.floor(diffMs / (1000 * 60 * 60));\r\n      case 'days': return Math.floor(diffMs / (1000 * 60 * 60 * 24));\r\n      default: return diffMs;\r\n    }\r\n  }\r\n\r\n  private formatDateFunction(date: any, format: string): string {\r\n    const d = new Date(date);\r\n    // Simple date formatting - in production, use a library like date-fns\r\n    return d.toISOString();\r\n  }\r\n\r\n  /**\r\n   * Clear expression cache\r\n   */\r\n  clearCache(): void {\r\n    this.expressionCache.clear();\r\n    this.logger.debug('Expression cache cleared');\r\n  }\r\n\r\n  /**\r\n   * Get cache statistics\r\n   */\r\n  getCacheStats(): { size: number; functions: number } {\r\n    return {\r\n      size: this.expressionCache.size,\r\n      functions: this.customFunctions.size,\r\n    };\r\n  }\r\n}\r\n"], "version": 3}