import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as zlib from 'zlib';
import { promisify } from 'util';

/**
 * Data Serializer Service
 * 
 * Handles efficient data transformation for AI operations.
 * Supports multiple serialization formats (JSON, Protocol Buffers),
 * data compression, and optimization for high-performance AI workflows.
 */
@Injectable()
export class DataSerializerService {
  private readonly logger = new Logger(DataSerializerService.name);
  private readonly compressionEnabled: boolean;
  private readonly compressionLevel: number;
  private readonly compressionThreshold: number;

  // Promisified compression functions
  private readonly gzip = promisify(zlib.gzip);
  private readonly gunzip = promisify(zlib.gunzip);
  private readonly deflate = promisify(zlib.deflate);
  private readonly inflate = promisify(zlib.inflate);

  constructor(private readonly configService: ConfigService) {
    this.compressionEnabled = this.configService.get<boolean>('ai.serialization.compression.enabled', true);
    this.compressionLevel = this.configService.get<number>('ai.serialization.compression.level', 6);
    this.compressionThreshold = this.configService.get<number>('ai.serialization.compression.threshold', 1024);
  }

  /**
   * Serializes data to JSON format with optional compression
   */
  async serializeToJson(
    data: any,
    options: SerializationOptions = {}
  ): Promise<SerializedData> {
    this.logger.debug('Serializing data to JSON format');

    try {
      const startTime = Date.now();
      
      // Convert to JSON string
      const jsonString = JSON.stringify(data, options.replacer, options.space);
      const originalSize = Buffer.byteLength(jsonString, 'utf8');

      let serializedBuffer: Buffer;
      let compressed = false;

      // Apply compression if enabled and data exceeds threshold
      if (this.shouldCompress(originalSize, options)) {
        serializedBuffer = await this.compressData(Buffer.from(jsonString, 'utf8'), options.compressionType);
        compressed = true;
        this.logger.debug(`JSON data compressed: ${originalSize} -> ${serializedBuffer.length} bytes`);
      } else {
        serializedBuffer = Buffer.from(jsonString, 'utf8');
      }

      const processingTime = Date.now() - startTime;

      return {
        data: serializedBuffer,
        format: 'json',
        compressed,
        compressionType: compressed ? (options.compressionType || 'gzip') : undefined,
        originalSize,
        compressedSize: serializedBuffer.length,
        compressionRatio: compressed ? originalSize / serializedBuffer.length : 1,
        processingTime,
        metadata: {
          encoding: 'utf8',
          ...options.metadata,
        },
      };

    } catch (error) {
      this.logger.error('Failed to serialize data to JSON', error);
      throw new SerializationError(`JSON serialization failed: ${error.message}`, 'json', error);
    }
  }

  /**
   * Deserializes JSON data with optional decompression
   */
  async deserializeFromJson(
    serializedData: SerializedData | Buffer,
    options: DeserializationOptions = {}
  ): Promise<any> {
    this.logger.debug('Deserializing data from JSON format');

    try {
      const startTime = Date.now();
      let buffer: Buffer;
      let compressed = false;
      let compressionType: CompressionType | undefined;

      // Handle different input types
      if (Buffer.isBuffer(serializedData)) {
        buffer = serializedData;
      } else {
        buffer = serializedData.data;
        compressed = serializedData.compressed || false;
        compressionType = serializedData.compressionType;
      }

      // Decompress if needed
      if (compressed && compressionType) {
        buffer = await this.decompressData(buffer, compressionType);
        this.logger.debug(`JSON data decompressed: ${buffer.length} bytes`);
      }

      // Parse JSON
      const jsonString = buffer.toString('utf8');
      const data = JSON.parse(jsonString, options.reviver);

      const processingTime = Date.now() - startTime;
      this.logger.debug(`JSON deserialization completed in ${processingTime}ms`);

      return data;

    } catch (error) {
      this.logger.error('Failed to deserialize JSON data', error);
      throw new SerializationError(`JSON deserialization failed: ${error.message}`, 'json', error);
    }
  }

  /**
   * Serializes data to Protocol Buffers format (mock implementation)
   */
  async serializeToProtobuf(
    data: any,
    schema: string,
    options: SerializationOptions = {}
  ): Promise<SerializedData> {
    this.logger.debug(`Serializing data to Protocol Buffers format with schema: ${schema}`);

    try {
      const startTime = Date.now();

      // Mock Protocol Buffers serialization
      // In real implementation, this would use protobuf.js or similar library
      const mockProtobufData = this.mockProtobufSerialization(data, schema);
      const originalSize = mockProtobufData.length;

      let serializedBuffer: Buffer;
      let compressed = false;

      // Apply compression if enabled and data exceeds threshold
      if (this.shouldCompress(originalSize, options)) {
        serializedBuffer = await this.compressData(mockProtobufData, options.compressionType);
        compressed = true;
        this.logger.debug(`Protobuf data compressed: ${originalSize} -> ${serializedBuffer.length} bytes`);
      } else {
        serializedBuffer = mockProtobufData;
      }

      const processingTime = Date.now() - startTime;

      return {
        data: serializedBuffer,
        format: 'protobuf',
        schema,
        compressed,
        compressionType: compressed ? (options.compressionType || 'gzip') : undefined,
        originalSize,
        compressedSize: serializedBuffer.length,
        compressionRatio: compressed ? originalSize / serializedBuffer.length : 1,
        processingTime,
        metadata: {
          schema,
          ...options.metadata,
        },
      };

    } catch (error) {
      this.logger.error('Failed to serialize data to Protocol Buffers', error);
      throw new SerializationError(`Protobuf serialization failed: ${error.message}`, 'protobuf', error);
    }
  }

  /**
   * Deserializes Protocol Buffers data (mock implementation)
   */
  async deserializeFromProtobuf(
    serializedData: SerializedData | Buffer,
    schema: string,
    options: DeserializationOptions = {}
  ): Promise<any> {
    this.logger.debug(`Deserializing data from Protocol Buffers format with schema: ${schema}`);

    try {
      const startTime = Date.now();
      let buffer: Buffer;
      let compressed = false;
      let compressionType: CompressionType | undefined;

      // Handle different input types
      if (Buffer.isBuffer(serializedData)) {
        buffer = serializedData;
      } else {
        buffer = serializedData.data;
        compressed = serializedData.compressed || false;
        compressionType = serializedData.compressionType;
      }

      // Decompress if needed
      if (compressed && compressionType) {
        buffer = await this.decompressData(buffer, compressionType);
        this.logger.debug(`Protobuf data decompressed: ${buffer.length} bytes`);
      }

      // Mock Protocol Buffers deserialization
      const data = this.mockProtobufDeserialization(buffer, schema);

      const processingTime = Date.now() - startTime;
      this.logger.debug(`Protobuf deserialization completed in ${processingTime}ms`);

      return data;

    } catch (error) {
      this.logger.error('Failed to deserialize Protocol Buffers data', error);
      throw new SerializationError(`Protobuf deserialization failed: ${error.message}`, 'protobuf', error);
    }
  }

  /**
   * Serializes data to binary format with custom encoding
   */
  async serializeToBinary(
    data: any,
    encoding: BinaryEncoding = 'base64',
    options: SerializationOptions = {}
  ): Promise<SerializedData> {
    this.logger.debug(`Serializing data to binary format with encoding: ${encoding}`);

    try {
      const startTime = Date.now();

      // Convert data to buffer
      let buffer: Buffer;
      if (Buffer.isBuffer(data)) {
        buffer = data;
      } else if (typeof data === 'string') {
        buffer = Buffer.from(data, 'utf8');
      } else {
        buffer = Buffer.from(JSON.stringify(data), 'utf8');
      }

      const originalSize = buffer.length;
      let serializedBuffer: Buffer;
      let compressed = false;

      // Apply compression if enabled and data exceeds threshold
      if (this.shouldCompress(originalSize, options)) {
        serializedBuffer = await this.compressData(buffer, options.compressionType);
        compressed = true;
        this.logger.debug(`Binary data compressed: ${originalSize} -> ${serializedBuffer.length} bytes`);
      } else {
        serializedBuffer = buffer;
      }

      const processingTime = Date.now() - startTime;

      return {
        data: serializedBuffer,
        format: 'binary',
        compressed,
        compressionType: compressed ? (options.compressionType || 'gzip') : undefined,
        originalSize,
        compressedSize: serializedBuffer.length,
        compressionRatio: compressed ? originalSize / serializedBuffer.length : 1,
        processingTime,
        metadata: {
          encoding,
          ...options.metadata,
        },
      };

    } catch (error) {
      this.logger.error('Failed to serialize data to binary format', error);
      throw new SerializationError(`Binary serialization failed: ${error.message}`, 'binary', error);
    }
  }

  /**
   * Deserializes binary data with custom encoding
   */
  async deserializeFromBinary(
    serializedData: SerializedData | Buffer,
    encoding: BinaryEncoding = 'base64',
    options: DeserializationOptions = {}
  ): Promise<Buffer> {
    this.logger.debug(`Deserializing data from binary format with encoding: ${encoding}`);

    try {
      const startTime = Date.now();
      let buffer: Buffer;
      let compressed = false;
      let compressionType: CompressionType | undefined;

      // Handle different input types
      if (Buffer.isBuffer(serializedData)) {
        buffer = serializedData;
      } else {
        buffer = serializedData.data;
        compressed = serializedData.compressed || false;
        compressionType = serializedData.compressionType;
      }

      // Decompress if needed
      if (compressed && compressionType) {
        buffer = await this.decompressData(buffer, compressionType);
        this.logger.debug(`Binary data decompressed: ${buffer.length} bytes`);
      }

      const processingTime = Date.now() - startTime;
      this.logger.debug(`Binary deserialization completed in ${processingTime}ms`);

      return buffer;

    } catch (error) {
      this.logger.error('Failed to deserialize binary data', error);
      throw new SerializationError(`Binary deserialization failed: ${error.message}`, 'binary', error);
    }
  }

  /**
   * Compresses data using specified compression algorithm
   */
  async compressData(
    data: Buffer,
    compressionType: CompressionType = 'gzip'
  ): Promise<Buffer> {
    this.logger.debug(`Compressing data using ${compressionType}`);

    try {
      const options = { level: this.compressionLevel };

      switch (compressionType) {
        case 'gzip':
          return await this.gzip(data, options);
        case 'deflate':
          return await this.deflate(data, options);
        default:
          throw new Error(`Unsupported compression type: ${compressionType}`);
      }

    } catch (error) {
      this.logger.error(`Failed to compress data using ${compressionType}`, error);
      throw new SerializationError(`Compression failed: ${error.message}`, compressionType, error);
    }
  }

  /**
   * Decompresses data using specified compression algorithm
   */
  async decompressData(
    data: Buffer,
    compressionType: CompressionType
  ): Promise<Buffer> {
    this.logger.debug(`Decompressing data using ${compressionType}`);

    try {
      switch (compressionType) {
        case 'gzip':
          return await this.gunzip(data);
        case 'deflate':
          return await this.inflate(data);
        default:
          throw new Error(`Unsupported compression type: ${compressionType}`);
      }

    } catch (error) {
      this.logger.error(`Failed to decompress data using ${compressionType}`, error);
      throw new SerializationError(`Decompression failed: ${error.message}`, compressionType, error);
    }
  }

  /**
   * Batch serializes multiple data items
   */
  async batchSerialize(
    items: Array<{ data: any; format: SerializationFormat; options?: SerializationOptions }>,
    globalOptions: SerializationOptions = {}
  ): Promise<BatchSerializationResult> {
    this.logger.debug(`Batch serializing ${items.length} items`);

    try {
      const startTime = Date.now();
      const results: SerializedData[] = [];
      const errors: Array<{ index: number; error: Error }> = [];

      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        const options = { ...globalOptions, ...item.options };

        try {
          let result: SerializedData;

          switch (item.format) {
            case 'json':
              result = await this.serializeToJson(item.data, options);
              break;
            case 'protobuf':
              result = await this.serializeToProtobuf(item.data, options.schema || 'default', options);
              break;
            case 'binary':
              result = await this.serializeToBinary(item.data, options.encoding || 'base64', options);
              break;
            default:
              throw new Error(`Unsupported format: ${item.format}`);
          }

          results.push(result);

        } catch (error) {
          errors.push({ index: i, error });
          results.push(null); // Placeholder for failed serialization
        }
      }

      const processingTime = Date.now() - startTime;
      const totalOriginalSize = results.filter(r => r).reduce((sum, r) => sum + r.originalSize, 0);
      const totalCompressedSize = results.filter(r => r).reduce((sum, r) => sum + r.compressedSize, 0);

      return {
        results,
        errors,
        summary: {
          total: items.length,
          successful: results.filter(r => r).length,
          failed: errors.length,
          totalOriginalSize,
          totalCompressedSize,
          overallCompressionRatio: totalOriginalSize > 0 ? totalOriginalSize / totalCompressedSize : 1,
          processingTime,
        },
      };

    } catch (error) {
      this.logger.error('Failed to batch serialize data', error);
      throw new SerializationError(`Batch serialization failed: ${error.message}`, 'batch', error);
    }
  }

  /**
   * Batch deserializes multiple data items
   */
  async batchDeserialize(
    items: Array<{ data: SerializedData | Buffer; format: SerializationFormat; options?: DeserializationOptions }>,
    globalOptions: DeserializationOptions = {}
  ): Promise<BatchDeserializationResult> {
    this.logger.debug(`Batch deserializing ${items.length} items`);

    try {
      const startTime = Date.now();
      const results: any[] = [];
      const errors: Array<{ index: number; error: Error }> = [];

      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        const options = { ...globalOptions, ...item.options };

        try {
          let result: any;

          switch (item.format) {
            case 'json':
              result = await this.deserializeFromJson(item.data, options);
              break;
            case 'protobuf':
              result = await this.deserializeFromProtobuf(item.data, options.schema || 'default', options);
              break;
            case 'binary':
              result = await this.deserializeFromBinary(item.data, options.encoding || 'base64', options);
              break;
            default:
              throw new Error(`Unsupported format: ${item.format}`);
          }

          results.push(result);

        } catch (error) {
          errors.push({ index: i, error });
          results.push(null); // Placeholder for failed deserialization
        }
      }

      const processingTime = Date.now() - startTime;

      return {
        results,
        errors,
        summary: {
          total: items.length,
          successful: results.filter(r => r !== null).length,
          failed: errors.length,
          processingTime,
        },
      };

    } catch (error) {
      this.logger.error('Failed to batch deserialize data', error);
      throw new SerializationError(`Batch deserialization failed: ${error.message}`, 'batch', error);
    }
  }

  /**
   * Gets serialization statistics
   */
  getSerializationStats(): SerializationStats {
    return {
      compressionEnabled: this.compressionEnabled,
      compressionLevel: this.compressionLevel,
      compressionThreshold: this.compressionThreshold,
      supportedFormats: ['json', 'protobuf', 'binary'],
      supportedCompressionTypes: ['gzip', 'deflate'],
      supportedEncodings: ['base64', 'hex', 'utf8'],
    };
  }

  // Private helper methods

  private shouldCompress(dataSize: number, options: SerializationOptions): boolean {
    if (options.forceCompression !== undefined) {
      return options.forceCompression;
    }

    return this.compressionEnabled && dataSize >= this.compressionThreshold;
  }

  private mockProtobufSerialization(data: any, schema: string): Buffer {
    // Mock Protocol Buffers serialization
    // In real implementation, this would use protobuf.js or similar library
    const mockData = {
      schema,
      data: JSON.stringify(data),
      timestamp: Date.now(),
    };

    return Buffer.from(JSON.stringify(mockData), 'utf8');
  }

  private mockProtobufDeserialization(buffer: Buffer, schema: string): any {
    // Mock Protocol Buffers deserialization
    // In real implementation, this would use protobuf.js or similar library
    const mockData = JSON.parse(buffer.toString('utf8'));

    if (mockData.schema !== schema) {
      throw new Error(`Schema mismatch: expected ${schema}, got ${mockData.schema}`);
    }

    return JSON.parse(mockData.data);
  }
}

// Type definitions
interface SerializationOptions {
  compressionType?: CompressionType;
  forceCompression?: boolean;
  replacer?: (key: string, value: any) => any;
  space?: string | number;
  schema?: string;
  encoding?: BinaryEncoding;
  metadata?: Record<string, any>;
}

interface DeserializationOptions {
  reviver?: (key: string, value: any) => any;
  schema?: string;
  encoding?: BinaryEncoding;
}

interface SerializedData {
  data: Buffer;
  format: SerializationFormat;
  schema?: string;
  compressed: boolean;
  compressionType?: CompressionType;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  processingTime: number;
  metadata?: Record<string, any>;
}

interface BatchSerializationResult {
  results: (SerializedData | null)[];
  errors: Array<{ index: number; error: Error }>;
  summary: {
    total: number;
    successful: number;
    failed: number;
    totalOriginalSize: number;
    totalCompressedSize: number;
    overallCompressionRatio: number;
    processingTime: number;
  };
}

interface BatchDeserializationResult {
  results: any[];
  errors: Array<{ index: number; error: Error }>;
  summary: {
    total: number;
    successful: number;
    failed: number;
    processingTime: number;
  };
}

interface SerializationStats {
  compressionEnabled: boolean;
  compressionLevel: number;
  compressionThreshold: number;
  supportedFormats: SerializationFormat[];
  supportedCompressionTypes: CompressionType[];
  supportedEncodings: BinaryEncoding[];
}

type SerializationFormat = 'json' | 'protobuf' | 'binary';
type CompressionType = 'gzip' | 'deflate';
type BinaryEncoding = 'base64' | 'hex' | 'utf8';

class SerializationError extends Error {
  constructor(
    message: string,
    public readonly format?: string,
    public readonly originalError?: any
  ) {
    super(message);
    this.name = 'SerializationError';
  }
}