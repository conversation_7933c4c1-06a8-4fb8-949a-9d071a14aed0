28f1c909cf6780958481e57c03c5bb04
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalysisResultStatusChangedEvent = void 0;
const base_domain_event_1 = require("../../../../shared-kernel/domain/base-domain-event");
/**
 * Analysis Result Status Changed Domain Event
 *
 * Published when an analysis result's status changes.
 * This event can trigger various downstream processes such as
 * workflow updates, notifications, and status tracking.
 */
class AnalysisResultStatusChangedEvent extends base_domain_event_1.BaseDomainEvent {
    constructor(analysisResultId, requestId, previousStatus, newStatus, eventId, occurredOn) {
        super(eventId, occurredOn);
        this.analysisResultId = analysisResultId;
        this.requestId = requestId;
        this.previousStatus = previousStatus;
        this.newStatus = newStatus;
    }
    getEventName() {
        return 'AnalysisResultStatusChanged';
    }
    getEventVersion() {
        return '1.0';
    }
    getEventData() {
        return {
            analysisResultId: this.analysisResultId.toString(),
            requestId: this.requestId,
            previousStatus: this.previousStatus,
            newStatus: this.newStatus,
        };
    }
}
exports.AnalysisResultStatusChangedEvent = AnalysisResultStatusChangedEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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