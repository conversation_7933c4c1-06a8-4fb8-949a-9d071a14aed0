8dd161a0c497b81f00890635ad095d03
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const unique_entity_id_value_object_1 = require("../../../../../shared-kernel/value-objects/unique-entity-id.value-object");
const analysis_result_entity_1 = require("../../entities/analysis-result.entity");
const analysis_result_factory_1 = require("../analysis-result.factory");
describe('AnalysisResultFactory', () => {
    let validCreateRequest;
    let validInputData;
    let validReconstitutionData;
    beforeEach(() => {
        validInputData = {
            data: { test: 'input' },
            format: 'json',
            size: 100,
            checksum: 'input-checksum',
            preprocessingApplied: ['normalization'],
            validationRules: ['required'],
        };
        validCreateRequest = {
            requestId: 'test-request-123',
            modelId: unique_entity_id_value_object_1.UniqueEntityId.generate(),
            analysisType: analysis_result_entity_1.AnalysisType.CLASSIFICATION,
            inputData: validInputData,
            tags: ['test', 'classification'],
            correlationId: 'correlation-123',
            parentAnalysisId: unique_entity_id_value_object_1.UniqueEntityId.generate(),
        };
        const now = new Date();
        validReconstitutionData = {
            id: unique_entity_id_value_object_1.UniqueEntityId.generate().toString(),
            requestId: 'test-request-123',
            modelId: unique_entity_id_value_object_1.UniqueEntityId.generate(),
            analysisType: analysis_result_entity_1.AnalysisType.CLASSIFICATION,
            inputData: validInputData,
            outputData: {
                results: { prediction: 'test-result' },
                format: 'json',
                size: 200,
                checksum: 'output-checksum',
                postprocessingApplied: ['formatting'],
                validationStatus: 'passed',
                qualityScore: 0.9,
            },
            confidence: 0.85,
            processingTime: 1500,
            status: analysis_result_entity_1.AnalysisStatus.COMPLETED,
            metadata: {
                version: '1.0.0',
                algorithm: 'test-algorithm',
                parameters: { param1: 'value1' },
                environment: 'test',
                resourceUsage: {
                    cpuTime: 1000,
                    memoryUsage: 512,
                    gpuTime: 500,
                    networkIO: 100,
                    diskIO: 50,
                },
                performanceMetrics: {
                    throughput: 10,
                    latency: 100,
                    accuracy: 0.95,
                    precision: 0.92,
                    recall: 0.88,
                    f1Score: 0.90,
                },
                qualityMetrics: {
                    dataQuality: 0.9,
                    resultReliability: 0.85,
                    consistencyScore: 0.8,
                    completenessScore: 0.95,
                },
            },
            tags: ['test', 'classification'],
            correlationId: 'correlation-123',
            parentAnalysisId: unique_entity_id_value_object_1.UniqueEntityId.generate(),
            childAnalysisIds: [unique_entity_id_value_object_1.UniqueEntityId.generate()],
            errorDetails: undefined,
            createdAt: now,
            updatedAt: now,
            completedAt: now,
        };
    });
    describe('create', () => {
        it('should create a valid analysis result', () => {
            const result = analysis_result_factory_1.AnalysisResultFactory.create(validCreateRequest);
            expect(result.requestId).toBe(validCreateRequest.requestId);
            expect(result.modelId.equals(validCreateRequest.modelId)).toBe(true);
            expect(result.analysisType).toBe(validCreateRequest.analysisType);
            expect(result.inputData).toEqual(validCreateRequest.inputData);
            expect(result.status).toBe(analysis_result_entity_1.AnalysisStatus.PENDING);
            expect(result.confidence).toBe(0);
            expect(result.processingTime).toBe(0);
            expect(result.tags).toEqual(['test', 'classification']);
            expect(result.correlationId).toBe(validCreateRequest.correlationId);
            expect(result.parentAnalysisId?.equals(validCreateRequest.parentAnalysisId)).toBe(true);
            expect(result.childAnalysisIds).toEqual([]);
            expect(result.createdAt).toBeInstanceOf(Date);
            expect(result.updatedAt).toBeInstanceOf(Date);
        });
        it('should create with custom ID', () => {
            const customId = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const result = analysis_result_factory_1.AnalysisResultFactory.create(validCreateRequest, customId);
            expect(result.id.equals(customId)).toBe(true);
        });
        it('should create with default values when optional fields are omitted', () => {
            const minimalRequest = {
                requestId: 'minimal-request',
                modelId: unique_entity_id_value_object_1.UniqueEntityId.generate(),
                analysisType: analysis_result_entity_1.AnalysisType.ANOMALY_DETECTION,
                inputData: validInputData,
            };
            const result = analysis_result_factory_1.AnalysisResultFactory.create(minimalRequest);
            expect(result.tags).toEqual([]);
            expect(result.correlationId).toBeUndefined();
            expect(result.parentAnalysisId).toBeUndefined();
            expect(result.metadata.version).toBe('1.0.0');
            expect(result.metadata.algorithm).toBe('unknown');
            expect(result.metadata.environment).toBe('production');
        });
        it('should create with partial metadata', () => {
            const requestWithMetadata = {
                ...validCreateRequest,
                metadata: {
                    version: '2.0.0',
                    algorithm: 'custom-algorithm',
                },
            };
            const result = analysis_result_factory_1.AnalysisResultFactory.create(requestWithMetadata);
            expect(result.metadata.version).toBe('2.0.0');
            expect(result.metadata.algorithm).toBe('custom-algorithm');
            expect(result.metadata.environment).toBe('production'); // Default value
        });
        it('should normalize tags to lowercase', () => {
            const requestWithUppercaseTags = {
                ...validCreateRequest,
                tags: ['TEST', 'Classification', 'UPPER'],
            };
            const result = analysis_result_factory_1.AnalysisResultFactory.create(requestWithUppercaseTags);
            expect(result.tags).toEqual(['test', 'classification', 'upper']);
        });
        it('should trim whitespace from request ID and correlation ID', () => {
            const requestWithWhitespace = {
                ...validCreateRequest,
                requestId: '  test-request-123  ',
                correlationId: '  correlation-123  ',
            };
            const result = analysis_result_factory_1.AnalysisResultFactory.create(requestWithWhitespace);
            expect(result.requestId).toBe('test-request-123');
            expect(result.correlationId).toBe('correlation-123');
        });
        describe('validation', () => {
            it('should throw error for empty request ID', () => {
                const invalidRequest = { ...validCreateRequest, requestId: '' };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.create(invalidRequest)).toThrow('Request ID is required');
            });
            it('should throw error for whitespace-only request ID', () => {
                const invalidRequest = { ...validCreateRequest, requestId: '   ' };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.create(invalidRequest)).toThrow('Request ID is required');
            });
            it('should throw error for request ID too long', () => {
                const invalidRequest = { ...validCreateRequest, requestId: 'a'.repeat(256) };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.create(invalidRequest)).toThrow('Request ID cannot exceed 255 characters');
            });
            it('should throw error for missing model ID', () => {
                const invalidRequest = { ...validCreateRequest, modelId: null };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.create(invalidRequest)).toThrow('Model ID is required');
            });
            it('should throw error for invalid analysis type', () => {
                const invalidRequest = { ...validCreateRequest, analysisType: 'invalid' };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.create(invalidRequest)).toThrow('Invalid analysis type');
            });
            it('should throw error for missing input data', () => {
                const invalidRequest = { ...validCreateRequest, inputData: null };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.create(invalidRequest)).toThrow('Input data is required');
            });
            it('should throw error for invalid input data', () => {
                const invalidInputData = { ...validInputData, data: null };
                const invalidRequest = { ...validCreateRequest, inputData: invalidInputData };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.create(invalidRequest)).toThrow('Input data content is required');
            });
            it('should throw error for empty input data format', () => {
                const invalidInputData = { ...validInputData, format: '' };
                const invalidRequest = { ...validCreateRequest, inputData: invalidInputData };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.create(invalidRequest)).toThrow('Input data format is required');
            });
            it('should throw error for negative input data size', () => {
                const invalidInputData = { ...validInputData, size: -1 };
                const invalidRequest = { ...validCreateRequest, inputData: invalidInputData };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.create(invalidRequest)).toThrow('Input data size cannot be negative');
            });
            it('should throw error for empty checksum', () => {
                const invalidInputData = { ...validInputData, checksum: '' };
                const invalidRequest = { ...validCreateRequest, inputData: invalidInputData };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.create(invalidRequest)).toThrow('Input data checksum is required');
            });
            it('should throw error for invalid preprocessing applied', () => {
                const invalidInputData = { ...validInputData, preprocessingApplied: 'not-array' };
                const invalidRequest = { ...validCreateRequest, inputData: invalidInputData };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.create(invalidRequest)).toThrow('Preprocessing applied must be an array');
            });
            it('should throw error for invalid validation rules', () => {
                const invalidInputData = { ...validInputData, validationRules: 'not-array' };
                const invalidRequest = { ...validCreateRequest, inputData: invalidInputData };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.create(invalidRequest)).toThrow('Validation rules must be an array');
            });
            it('should throw error for empty tag in array', () => {
                const invalidRequest = { ...validCreateRequest, tags: ['valid', ''] };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.create(invalidRequest)).toThrow('All tags must be non-empty strings');
            });
            it('should throw error for empty correlation ID', () => {
                const invalidRequest = { ...validCreateRequest, correlationId: '   ' };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.create(invalidRequest)).toThrow('Correlation ID cannot be empty if provided');
            });
        });
    });
    describe('reconstitute', () => {
        it('should reconstitute a valid analysis result', () => {
            const result = analysis_result_factory_1.AnalysisResultFactory.reconstitute(validReconstitutionData);
            expect(result.id.toString()).toBe(validReconstitutionData.id);
            expect(result.requestId).toBe(validReconstitutionData.requestId);
            expect(result.modelId.equals(validReconstitutionData.modelId)).toBe(true);
            expect(result.analysisType).toBe(validReconstitutionData.analysisType);
            expect(result.status).toBe(validReconstitutionData.status);
            expect(result.confidence).toBe(validReconstitutionData.confidence);
            expect(result.processingTime).toBe(validReconstitutionData.processingTime);
            expect(result.createdAt).toBe(validReconstitutionData.createdAt);
            expect(result.updatedAt).toBe(validReconstitutionData.updatedAt);
            expect(result.completedAt).toBe(validReconstitutionData.completedAt);
        });
        describe('validation', () => {
            it('should throw error for invalid ID', () => {
                const invalidData = { ...validReconstitutionData, id: 'invalid-id' };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.reconstitute(invalidData)).toThrow('Valid ID is required for reconstitution');
            });
            it('should throw error for empty request ID', () => {
                const invalidData = { ...validReconstitutionData, requestId: '' };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.reconstitute(invalidData)).toThrow('Request ID is required');
            });
            it('should throw error for missing model ID', () => {
                const invalidData = { ...validReconstitutionData, modelId: null };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.reconstitute(invalidData)).toThrow('Model ID is required');
            });
            it('should throw error for invalid analysis type', () => {
                const invalidData = { ...validReconstitutionData, analysisType: 'invalid' };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.reconstitute(invalidData)).toThrow('Invalid analysis type');
            });
            it('should throw error for invalid status', () => {
                const invalidData = { ...validReconstitutionData, status: 'invalid' };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.reconstitute(invalidData)).toThrow('Invalid analysis status');
            });
            it('should throw error for missing input data', () => {
                const invalidData = { ...validReconstitutionData, inputData: null };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.reconstitute(invalidData)).toThrow('Input data is required');
            });
            it('should throw error for missing metadata', () => {
                const invalidData = { ...validReconstitutionData, metadata: null };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.reconstitute(invalidData)).toThrow('Metadata is required');
            });
            it('should throw error for invalid confidence', () => {
                const invalidData = { ...validReconstitutionData, confidence: 1.5 };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.reconstitute(invalidData)).toThrow('Confidence must be between 0 and 1');
            });
            it('should throw error for negative processing time', () => {
                const invalidData = { ...validReconstitutionData, processingTime: -100 };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.reconstitute(invalidData)).toThrow('Processing time cannot be negative');
            });
            it('should throw error for invalid tags', () => {
                const invalidData = { ...validReconstitutionData, tags: 'not-array' };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.reconstitute(invalidData)).toThrow('Tags must be an array');
            });
            it('should throw error for invalid child analysis IDs', () => {
                const invalidData = { ...validReconstitutionData, childAnalysisIds: 'not-array' };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.reconstitute(invalidData)).toThrow('Child analysis IDs must be an array');
            });
            it('should throw error for invalid creation date', () => {
                const invalidData = { ...validReconstitutionData, createdAt: 'invalid' };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.reconstitute(invalidData)).toThrow('Valid creation date is required');
            });
            it('should throw error for invalid update date', () => {
                const invalidData = { ...validReconstitutionData, updatedAt: 'invalid' };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.reconstitute(invalidData)).toThrow('Valid update date is required');
            });
            it('should throw error for invalid completed date', () => {
                const invalidData = { ...validReconstitutionData, completedAt: 'invalid' };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.reconstitute(invalidData)).toThrow('Completed date must be a valid Date if provided');
            });
            it('should throw error for completed status without output data', () => {
                const invalidData = {
                    ...validReconstitutionData,
                    status: analysis_result_entity_1.AnalysisStatus.COMPLETED,
                    outputData: null
                };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.reconstitute(invalidData)).toThrow('Output data is required for completed analysis');
            });
            it('should throw error for failed status without error details', () => {
                const invalidData = {
                    ...validReconstitutionData,
                    status: analysis_result_entity_1.AnalysisStatus.FAILED,
                    errorDetails: undefined
                };
                expect(() => analysis_result_factory_1.AnalysisResultFactory.reconstitute(invalidData)).toThrow('Error details are required for failed analysis');
            });
        });
    });
    describe('createForTesting', () => {
        it('should create a test analysis result with default values', () => {
            const result = analysis_result_factory_1.AnalysisResultFactory.createForTesting();
            expect(result.requestId).toBe('test-request-123');
            expect(result.analysisType).toBe(analysis_result_entity_1.AnalysisType.CLASSIFICATION);
            expect(result.status).toBe(analysis_result_entity_1.AnalysisStatus.PENDING);
            expect(result.tags).toEqual(['test']);
            expect(result.correlationId).toBe('test-correlation-123');
            expect(result.confidence).toBe(0);
            expect(result.processingTime).toBe(0);
        });
        it('should create a test analysis result with overrides', () => {
            const overrides = {
                requestId: 'custom-request',
                analysisType: analysis_result_entity_1.AnalysisType.ANOMALY_DETECTION,
                tags: ['custom', 'test'],
            };
            const result = analysis_result_factory_1.AnalysisResultFactory.createForTesting(overrides);
            expect(result.requestId).toBe('custom-request');
            expect(result.analysisType).toBe(analysis_result_entity_1.AnalysisType.ANOMALY_DETECTION);
            expect(result.tags).toEqual(['custom', 'test']);
            expect(result.correlationId).toBe('test-correlation-123'); // Default value preserved
        });
    });
    describe('createCompletedForTesting', () => {
        it('should create a completed test analysis result', () => {
            const result = analysis_result_factory_1.AnalysisResultFactory.createCompletedForTesting();
            expect(result.status).toBe(analysis_result_entity_1.AnalysisStatus.COMPLETED);
            expect(result.confidence).toBe(0.95);
            expect(result.processingTime).toBe(1500);
            expect(result.outputData).toBeDefined();
            expect(result.completedAt).toBeInstanceOf(Date);
            expect(result.isSuccessful()).toBe(true);
        });
        it('should create a completed test analysis result with overrides', () => {
            const overrides = {
                requestId: 'completed-request',
                analysisType: analysis_result_entity_1.AnalysisType.NLP_ANALYSIS,
            };
            const result = analysis_result_factory_1.AnalysisResultFactory.createCompletedForTesting(overrides);
            expect(result.requestId).toBe('completed-request');
            expect(result.analysisType).toBe(analysis_result_entity_1.AnalysisType.NLP_ANALYSIS);
            expect(result.status).toBe(analysis_result_entity_1.AnalysisStatus.COMPLETED);
        });
    });
    describe('createFailedForTesting', () => {
        it('should create a failed test analysis result', () => {
            const result = analysis_result_factory_1.AnalysisResultFactory.createFailedForTesting();
            expect(result.status).toBe(analysis_result_entity_1.AnalysisStatus.FAILED);
            expect(result.errorDetails).toBeDefined();
            expect(result.errorDetails.code).toBe('TEST_ERROR');
            expect(result.errorDetails.retryable).toBe(true);
            expect(result.completedAt).toBeInstanceOf(Date);
            expect(result.isSuccessful()).toBe(false);
            expect(result.isRetryable()).toBe(true);
        });
        it('should create a failed test analysis result with overrides', () => {
            const overrides = {
                requestId: 'failed-request',
                analysisType: analysis_result_entity_1.AnalysisType.THREAT_DETECTION,
            };
            const result = analysis_result_factory_1.AnalysisResultFactory.createFailedForTesting(overrides);
            expect(result.requestId).toBe('failed-request');
            expect(result.analysisType).toBe(analysis_result_entity_1.AnalysisType.THREAT_DETECTION);
            expect(result.status).toBe(analysis_result_entity_1.AnalysisStatus.FAILED);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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