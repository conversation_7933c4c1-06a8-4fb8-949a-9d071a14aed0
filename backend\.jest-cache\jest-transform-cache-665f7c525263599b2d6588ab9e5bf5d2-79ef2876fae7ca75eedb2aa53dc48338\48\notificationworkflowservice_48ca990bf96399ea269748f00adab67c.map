{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\services\\notification-workflow.service.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAA4F;AAC5F,6CAAmD;AACnD,qCAAqC;AACrC,2CAA+C;AAC/C,yDAAsD;AACtD,2FAAgF;AAChF,qFAA0E;AAC1E,qGAAyF;AACzF,uEAAkE;AAClE,iFAAoE;AACpE,6EAAwE;AACxE,qFAAgF;AAOhF;;;;;;;;;;;;;;;;;GAiBG;AAEI,IAAM,2BAA2B,mCAAjC,MAAM,2BAA2B;IAGtC,YAEE,kBAAqE,EAErE,mBAAmE,EAEnE,iBAAwE,EACvD,aAA4B,EAC5B,YAA2B,EAC3B,cAAqC,EACrC,UAA8B,EAC9B,SAAmC,EACnC,gBAA8C;QAV9C,uBAAkB,GAAlB,kBAAkB,CAAkC;QAEpD,wBAAmB,GAAnB,mBAAmB,CAA+B;QAElD,sBAAiB,GAAjB,iBAAiB,CAAsC;QACvD,kBAAa,GAAb,aAAa,CAAe;QAC5B,iBAAY,GAAZ,YAAY,CAAe;QAC3B,mBAAc,GAAd,cAAc,CAAuB;QACrC,eAAU,GAAV,UAAU,CAAoB;QAC9B,cAAS,GAAT,SAAS,CAA0B;QACnC,qBAAgB,GAAhB,gBAAgB,CAA8B;QAdhD,WAAM,GAAG,IAAI,eAAM,CAAC,6BAA2B,CAAC,IAAI,CAAC,CAAC;IAepE,CAAC;IAEJ;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,iBAAoC,EAAE,IAAU;QACnE,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,iBAAiB,CAAC,IAAI,EAAE,CAAC,CAAC;YAElE,+BAA+B;YAC/B,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAC7F,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;gBAC5B,MAAM,IAAI,4BAAmB,CAAC,+BAA+B,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACrG,CAAC;YAED,yBAAyB;YACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;gBAC9C,IAAI,EAAE,iBAAiB,CAAC,IAAI;gBAC5B,WAAW,EAAE,iBAAiB,CAAC,WAAW;gBAC1C,UAAU,EAAE,iBAAiB,CAAC,UAAU;gBACxC,MAAM,EAAE,iBAAiB,CAAC,MAAM,IAAI,OAAO;gBAC3C,QAAQ,EAAE,iBAAiB,CAAC,QAAQ;gBACpC,IAAI,EAAE,iBAAiB,CAAC,IAAI;gBAC5B,OAAO,EAAE,OAAO;gBAChB,UAAU,EAAE,iBAAiB,CAAC,UAAU,IAAI,KAAK;gBACjD,aAAa,EAAE,iBAAiB,CAAC,aAAa,IAAI,EAAE;gBACpD,QAAQ,EAAE,iBAAiB,CAAC,QAAQ,IAAI,EAAE;gBAC1C,SAAS,EAAE,IAAI,CAAC,EAAE;gBAClB,SAAS,EAAE,IAAI,CAAC,EAAE;aACnB,CAAC,CAAC;YAEH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEnE,uCAAuC;YACvC,IAAI,iBAAiB,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtD,MAAM,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;YACvD,CAAC;YAED,8BAA8B;YAC9B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBACzC,UAAU,EAAE,aAAa,CAAC,EAAE;gBAC5B,IAAI,EAAE,aAAa,CAAC,IAAI;gBACxB,SAAS,EAAE,IAAI,CAAC,EAAE;gBAClB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,aAAa,CAAC,EAAE,EAAE,CAAC,CAAC;YACxE,OAAO;gBACL,EAAE,EAAE,aAAa,CAAC,EAAE;gBACpB,IAAI,EAAE,aAAa,CAAC,IAAI;gBACxB,MAAM,EAAE,aAAa,CAAC,MAAM;gBAC5B,OAAO,EAAE,aAAa,CAAC,OAAO;gBAC9B,SAAS,EAAE,aAAa,CAAC,SAAS;aACnC,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC9E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,KAAuB,EAAE,IAAU;QACpD,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;YAE1C,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB;iBACzC,kBAAkB,CAAC,UAAU,CAAC;iBAC9B,iBAAiB,CAAC,qBAAqB,EAAE,WAAW,CAAC;iBACrD,KAAK,CAAC,KAAK,CAAC,CAAC;YAEhB,gBAAgB;YAChB,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;gBACjB,YAAY,CAAC,QAAQ,CAAC,2BAA2B,EAAE,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;YAC/E,CAAC;YAED,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,YAAY,CAAC,QAAQ,CAAC,+BAA+B,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;YACvF,CAAC;YAED,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;gBACjB,YAAY,CAAC,QAAQ,CACnB,qEAAqE,EACrE,EAAE,MAAM,EAAE,IAAI,KAAK,CAAC,MAAM,GAAG,EAAE,CAChC,CAAC;YACJ,CAAC;YAED,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;gBACpB,YAAY,CAAC,QAAQ,CAAC,iCAAiC,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;YAC3F,CAAC;YAED,IAAI,KAAK,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;gBACnC,YAAY,CAAC,QAAQ,CAAC,mCAAmC,EAAE,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC;YAC/F,CAAC;YAED,gBAAgB;YAChB,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,IAAI,WAAW,CAAC;YAC3C,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,IAAI,MAAM,CAAC;YAC5C,YAAY,CAAC,OAAO,CAAC,YAAY,MAAM,EAAE,EAAE,SAAS,CAAC,CAAC;YAEtD,mBAAmB;YACnB,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC;YAC7B,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC;YAChC,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAElC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEtC,gBAAgB;YAChB,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,MAAM,YAAY,CAAC,eAAe,EAAE,CAAC;YAEhE,iCAAiC;YACjC,MAAM,kBAAkB,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBACpD,EAAE,EAAE,QAAQ,CAAC,EAAE;gBACf,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,WAAW,EAAE,QAAQ,CAAC,WAAW;gBACjC,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,cAAc,EAAE,QAAQ,CAAC,UAAU,EAAE,MAAM,IAAI,CAAC;gBAChD,YAAY,EAAE,QAAQ,CAAC,UAAU,EAAE,MAAM,GAAG,CAAC;oBAC3C,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;oBAChG,CAAC,CAAC,IAAI;gBACR,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,SAAS,EAAE,QAAQ,CAAC,SAAS;aAC9B,CAAC,CAAC,CAAC;YAEJ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,SAAS,CAAC,MAAM,YAAY,CAAC,CAAC;YAC7D,OAAO;gBACL,SAAS,EAAE,kBAAkB;gBAC7B,KAAK;gBACL,IAAI;gBACJ,KAAK;gBACL,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;aACrC,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACjF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,EAAU,EAAE,cAAuB,EAAE,gBAAyB,EAAE,IAAU;QAC9F,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,EAAE,CAAC,CAAC;YAEhD,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB;iBACzC,kBAAkB,CAAC,UAAU,CAAC;iBAC9B,KAAK,CAAC,mBAAmB,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YAEtC,IAAI,cAAc,EAAE,CAAC;gBACnB,YAAY,CAAC,iBAAiB,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC;YACrE,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,YAAY,CAAC,MAAM,EAAE,CAAC;YAE7C,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAC;YAC3D,CAAC;YAED,6BAA6B;YAC7B,IAAI,SAAS,GAAG,IAAI,CAAC;YACrB,IAAI,gBAAgB,EAAE,CAAC;gBACrB,SAAS,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YACrE,CAAC;YAED,qCAAqC;YACrC,IAAI,OAAO,GAAG,IAAI,CAAC;YACnB,IAAI,cAAc,EAAE,CAAC;gBACnB,OAAO,GAAG,QAAQ,CAAC,UAAU,EAAE,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;oBAC/C,EAAE,EAAE,SAAS,CAAC,EAAE;oBAChB,MAAM,EAAE,SAAS,CAAC,MAAM;oBACxB,SAAS,EAAE,SAAS,CAAC,SAAS;oBAC9B,WAAW,EAAE,SAAS,CAAC,WAAW;oBAClC,QAAQ,EAAE,SAAS,CAAC,QAAQ;oBAC5B,cAAc,EAAE,SAAS,CAAC,cAAc;oBACxC,UAAU,EAAE,SAAS,CAAC,UAAU;oBAChC,MAAM,EAAE,SAAS,CAAC,MAAM;oBACxB,KAAK,EAAE,SAAS,CAAC,KAAK;iBACvB,CAAC,CAAC,IAAI,EAAE,CAAC;YACZ,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAC;YAC/C,OAAO;gBACL,EAAE,EAAE,QAAQ,CAAC,EAAE;gBACf,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,WAAW,EAAE,QAAQ,CAAC,WAAW;gBACjC,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,aAAa,EAAE,QAAQ,CAAC,aAAa;gBACrC,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,SAAS;gBACT,OAAO;gBACP,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,SAAS,EAAE,QAAQ,CAAC,SAAS;aAC9B,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAChF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,EAAU,EAAE,iBAAoC,EAAE,IAAU;QAC/E,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,EAAE,CAAC,CAAC;YAE9C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YAC1E,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAC;YAC3D,CAAC;YAED,kCAAkC;YAClC,IAAI,iBAAiB,CAAC,UAAU,EAAE,CAAC;gBACjC,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;gBAC7F,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;oBAC5B,MAAM,IAAI,4BAAmB,CAAC,+BAA+B,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACrG,CAAC;YACH,CAAC;YAED,2CAA2C;YAC3C,IAAI,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC;YAClC,IAAI,iBAAiB,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;gBACzH,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACvD,CAAC;YAED,kBAAkB;YAClB,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE;gBACtB,GAAG,iBAAiB;gBACpB,OAAO,EAAE,UAAU;gBACnB,SAAS,EAAE,IAAI,CAAC,EAAE;gBAClB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEnE,uCAAuC;YACvC,IAAI,iBAAiB,CAAC,UAAU,EAAE,QAAQ,EAAE,CAAC;gBAC3C,MAAM,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;YAC7D,CAAC;YAED,8BAA8B;YAC9B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBACzC,UAAU,EAAE,aAAa,CAAC,EAAE;gBAC5B,IAAI,EAAE,aAAa,CAAC,IAAI;gBACxB,OAAO,EAAE,aAAa,CAAC,OAAO;gBAC9B,SAAS,EAAE,IAAI,CAAC,EAAE;gBAClB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,EAAE,CAAC,CAAC;YAC1D,OAAO;gBACL,EAAE,EAAE,aAAa,CAAC,EAAE;gBACpB,IAAI,EAAE,aAAa,CAAC,IAAI;gBACxB,OAAO,EAAE,aAAa,CAAC,OAAO;gBAC9B,SAAS,EAAE,aAAa,CAAC,SAAS;aACnC,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC9E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,EAAU,EAAE,IAAU;QACzC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,EAAE,CAAC,CAAC;YAE9C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YAC1E,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAC;YAC3D,CAAC;YAED,gCAAgC;YAChC,MAAM,IAAI,CAAC,0BAA0B,CAAC,EAAE,CAAC,CAAC;YAE1C,wBAAwB;YACxB,MAAM,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC;YAE5C,mCAAmC;YACnC,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAE/C,8BAA8B;YAC9B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBACzC,UAAU,EAAE,EAAE;gBACd,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,SAAS,EAAE,IAAI,CAAC,EAAE;gBAClB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,EAAE,CAAC,CAAC;QAE5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC9E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,EAAU,EAAE,kBAAsC,EAAE,IAAU;QAClF,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAC;YAE/C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YAC1E,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAC;YAC3D,CAAC;YAED,IAAI,QAAQ,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;gBACjC,MAAM,IAAI,4BAAmB,CAAC,2BAA2B,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;YAC9E,CAAC;YAED,0BAA0B;YAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;gBAChD,UAAU,EAAE,EAAE;gBACd,MAAM,EAAE,SAAS;gBACjB,KAAK,EAAE,kBAAkB,CAAC,KAAK,IAAI,EAAE;gBACrC,OAAO,EAAE,kBAAkB,CAAC,OAAO,IAAI,EAAE;gBACzC,WAAW,EAAE,IAAI,CAAC,EAAE;gBACpB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,UAAU,EAAE,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC;gBACxD,cAAc,EAAE,CAAC;aAClB,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAEtE,2BAA2B;YAC3B,MAAM,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,QAAQ,EAAE,cAAc,EAAE,kBAAkB,CAAC,CAAC;YAE3G,+DAA+D;YAC/D,gBAAgB,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBAC7B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAChF,CAAC,CAAC,CAAC;YAEH,+BAA+B;YAC/B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,4BAA4B,EAAE;gBACnD,WAAW,EAAE,cAAc,CAAC,EAAE;gBAC9B,UAAU,EAAE,EAAE;gBACd,WAAW,EAAE,IAAI,CAAC,EAAE;gBACpB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,cAAc,CAAC,EAAE,EAAE,CAAC,CAAC;YACtE,OAAO;gBACL,WAAW,EAAE,cAAc,CAAC,EAAE;gBAC9B,UAAU,EAAE,EAAE;gBACd,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,cAAc,CAAC,SAAS;gBACnC,iBAAiB,EAAE,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC;aAC5D,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC/E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,UAAkB,EAAE,WAAmB,EAAE,IAAU;QAC1E,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,WAAW,EAAE,CAAC,CAAC;YAE9D,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC;gBACvD,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,UAAU,EAAE;gBACtC,SAAS,EAAE,CAAC,UAAU,CAAC;aACxB,CAAC,CAAC;YAEH,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,IAAI,0BAAiB,CAAC,wBAAwB,WAAW,EAAE,CAAC,CAAC;YACrE,CAAC;YAED,+BAA+B;YAC/B,MAAM,WAAW,GAAG,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,KAAK,SAAS,CAAC,EAAE,MAAM,IAAI,IAAI,CAAC;YAC9F,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBAC5C,MAAM,EAAE,GAAG,CAAC,MAAM;gBAClB,MAAM,EAAE,GAAG,CAAC,MAAM;gBAClB,SAAS,EAAE,GAAG,CAAC,SAAS;gBACxB,WAAW,EAAE,GAAG,CAAC,WAAW;gBAC5B,MAAM,EAAE,GAAG,CAAC,MAAM;gBAClB,KAAK,EAAE,GAAG,CAAC,KAAK;aACjB,CAAC,CAAC,IAAI,EAAE,CAAC;YAEV,qBAAqB;YACrB,MAAM,QAAQ,GAAG,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,GAAG,SAAS,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAExG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,WAAW,EAAE,CAAC,CAAC;YAChE,OAAO;gBACL,WAAW,EAAE,SAAS,CAAC,EAAE;gBACzB,UAAU,EAAE,SAAS,CAAC,UAAU;gBAChC,MAAM,EAAE,SAAS,CAAC,MAAM;gBACxB,QAAQ;gBACR,WAAW;gBACX,KAAK;gBACL,SAAS,EAAE,SAAS,CAAC,SAAS;gBAC9B,WAAW,EAAE,SAAS,CAAC,WAAW;gBAClC,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,MAAM,EAAE,SAAS,CAAC,MAAM;gBACxB,KAAK,EAAE,SAAS,CAAC,KAAK;aACvB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACnF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,UAAkB,EAAE,WAAmB,EAAE,IAAU;QACvE,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,WAAW,EAAE,CAAC,CAAC;YAE1D,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC;gBACvD,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,UAAU,EAAE;aACvC,CAAC,CAAC;YAEH,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,IAAI,0BAAiB,CAAC,wBAAwB,WAAW,EAAE,CAAC,CAAC;YACrE,CAAC;YAED,IAAI,SAAS,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBACnC,MAAM,IAAI,4BAAmB,CAAC,6BAA6B,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;YACjF,CAAC;YAED,mBAAmB;YACnB,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAEvD,0BAA0B;YAC1B,SAAS,CAAC,MAAM,GAAG,WAAW,CAAC;YAC/B,SAAS,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;YACnC,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YACrF,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAE/C,iCAAiC;YACjC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,8BAA8B,EAAE;gBACrD,WAAW;gBACX,UAAU;gBACV,WAAW,EAAE,IAAI,CAAC,EAAE;gBACpB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,WAAW,EAAE,CAAC,CAAC;QAE3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC/E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CAAC,EAAU,EAAE,SAAiB,EAAE,oBAA6B,EAAE,IAAU;QACjG,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,EAAE,CAAC,CAAC;YAEvD,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;YAEtD,2BAA2B;YAC3B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,mBAAmB;iBAC9C,kBAAkB,CAAC,WAAW,CAAC;iBAC/B,KAAK,CAAC,4BAA4B,EAAE,EAAE,EAAE,EAAE,CAAC;iBAC3C,QAAQ,CAAC,qDAAqD,EAAE,UAAU,CAAC;iBAC3E,OAAO,EAAE,CAAC;YAEb,MAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC;YAChC,MAAM,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,MAAM,CAAC;YAC3E,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,MAAM,CAAC;YACpE,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,MAAM,CAAC;YAE1E,gCAAgC;YAChC,MAAM,mBAAmB,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC;YACxE,MAAM,WAAW,GAAG,mBAAmB,CAAC,MAAM,GAAG,CAAC;gBAChD,CAAC,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,GAAG,mBAAmB,CAAC,MAAM;gBAC1F,CAAC,CAAC,CAAC,CAAC;YAEN,MAAM,SAAS,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACjF,MAAM,WAAW,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5D,MAAM,WAAW,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAE/E,MAAM,iBAAiB,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC;gBAC7C,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM;gBAC9E,CAAC,CAAC,CAAC,CAAC;YAEN,aAAa;YACb,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;YAElE,kCAAkC;YAClC,IAAI,aAAa,GAAG,IAAI,CAAC;YACzB,IAAI,oBAAoB,EAAE,CAAC;gBACzB,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;YAC7D,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,EAAE,CAAC,CAAC;YACzD,OAAO;gBACL,UAAU,EAAE,EAAE;gBACd,UAAU,EAAE;oBACV,KAAK;oBACL,UAAU;oBACV,MAAM;oBACN,SAAS;oBACT,WAAW,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;iBACxD;gBACD,WAAW,EAAE;oBACX,WAAW;oBACX,WAAW;oBACX,WAAW;oBACX,iBAAiB;iBAClB;gBACD,MAAM;gBACN,aAAa;gBACb,SAAS;gBACT,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACrF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IAEH;;OAEG;IACK,KAAK,CAAC,0BAA0B,CAAC,UAAe;QACtD,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,6BAA6B;QAC7B,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1D,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QAChD,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;YAC1B,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACtD,CAAC;QAED,iBAAiB;QACjB,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;YACrB,KAAK,MAAM,IAAI,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;gBACpC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;oBACb,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;gBAC3C,CAAC;gBACD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;oBACf,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;gBAC5C,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC1B,MAAM;YACN,QAAQ;SACT,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,OAAe;QACtC,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACjC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACrC,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE,CAAC;IAC5C,CAAC;IAEO,kBAAkB,CAAC,UAAe;QACxC,OAAO,UAAU,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,CAAC;IACvC,CAAC;IAEO,yBAAyB,CAAC,QAAa;QAC7C,wCAAwC;QACxC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,sBAAsB;IACjF,CAAC;IAEO,KAAK,CAAC,0BAA0B,CAAC,UAAkB;QACzD,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;YAC5D,KAAK,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE;SACzC,CAAC,CAAC;QAEH,KAAK,MAAM,SAAS,IAAI,iBAAiB,EAAE,CAAC;YAC1C,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAEO,kBAAkB,CAAC,SAAiB;QAC1C,MAAM,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;QAC3B,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,kBAAkB;QAC5F,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC;IAChC,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,UAAkB,EAAE,SAAiB;QAC1E,4DAA4D;QAC5D,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,UAAkB,EAAE,SAAiB;QAClE,gDAAgD;QAChD,OAAO,EAAE,CAAC;IACZ,CAAC;CACF,CAAA;AAloBY,kEAA2B;sCAA3B,2BAA2B;IADvC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,EAAC,mDAAoB,CAAC,CAAA;IAEtC,WAAA,IAAA,0BAAgB,EAAC,6CAAiB,CAAC,CAAA;IAEnC,WAAA,IAAA,0BAAgB,EAAC,4DAAwB,CAAC,CAAA;yDAHN,oBAAU,oBAAV,oBAAU,oDAET,oBAAU,oBAAV,oBAAU,oDAEZ,oBAAU,oBAAV,oBAAU,oDACd,sBAAa,oBAAb,sBAAa,oDACd,6BAAa,oBAAb,6BAAa,oDACX,+CAAqB,oBAArB,+CAAqB,oDACzB,iDAAkB,oBAAlB,iDAAkB,oDACnB,qDAAwB,oBAAxB,qDAAwB,oDACjB,6DAA4B,oBAA5B,6DAA4B;GAftD,2BAA2B,CAkoBvC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\services\\notification-workflow.service.ts"], "sourcesContent": ["import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { EventEmitter2 } from '@nestjs/event-emitter';\r\nimport { NotificationWorkflow } from '../entities/notification-workflow.entity';\r\nimport { WorkflowExecution } from '../entities/workflow-execution.entity';\r\nimport { WorkflowExecutionContext } from '../entities/workflow-execution-context.entity';\r\nimport { WorkflowEngineService } from './workflow-engine.service';\r\nimport { WorkflowRuleEngine } from './workflow-rule-engine.service';\r\nimport { WorkflowSchedulerService } from './workflow-scheduler.service';\r\nimport { NotificationAnalyticsService } from './notification-analytics.service';\r\nimport { CreateWorkflowDto } from '../dto/create-workflow.dto';\r\nimport { UpdateWorkflowDto } from '../dto/update-workflow.dto';\r\nimport { WorkflowQueryDto } from '../dto/workflow-query.dto';\r\nimport { ExecuteWorkflowDto } from '../dto/execute-workflow.dto';\r\nimport { User } from '../../auth/entities/user.entity';\r\n\r\n/**\r\n * Notification Workflow Service\r\n * \r\n * Provides comprehensive workflow management and execution including:\r\n * - Workflow definition processing and validation with comprehensive rule evaluation\r\n * - Step-by-step execution engine with conditional logic and branching\r\n * - Escalation workflow management with time-based triggers and automatic progression\r\n * - Template workflow integration with dynamic content generation\r\n * - Workflow analytics and performance tracking with execution metrics\r\n * \r\n * Features:\r\n * - Visual workflow designer support with drag-and-drop interface\r\n * - Advanced conditional logic with complex rule evaluation\r\n * - Integration with external systems and APIs\r\n * - Real-time workflow monitoring and execution tracking\r\n * - Workflow versioning and rollback capabilities\r\n * - Performance optimization with intelligent caching and parallel execution\r\n */\r\n@Injectable()\r\nexport class NotificationWorkflowService {\r\n  private readonly logger = new Logger(NotificationWorkflowService.name);\r\n\r\n  constructor(\r\n    @InjectRepository(NotificationWorkflow)\r\n    private readonly workflowRepository: Repository<NotificationWorkflow>,\r\n    @InjectRepository(WorkflowExecution)\r\n    private readonly executionRepository: Repository<WorkflowExecution>,\r\n    @InjectRepository(WorkflowExecutionContext)\r\n    private readonly contextRepository: Repository<WorkflowExecutionContext>,\r\n    private readonly configService: ConfigService,\r\n    private readonly eventEmitter: EventEmitter2,\r\n    private readonly workflowEngine: WorkflowEngineService,\r\n    private readonly ruleEngine: WorkflowRuleEngine,\r\n    private readonly scheduler: WorkflowSchedulerService,\r\n    private readonly analyticsService: NotificationAnalyticsService\r\n  ) {}\r\n\r\n  /**\r\n   * Create new workflow\r\n   */\r\n  async createWorkflow(createWorkflowDto: CreateWorkflowDto, user: User): Promise<any> {\r\n    try {\r\n      this.logger.debug(`Creating workflow: ${createWorkflowDto.name}`);\r\n\r\n      // Validate workflow definition\r\n      const validationResult = await this.validateWorkflowDefinition(createWorkflowDto.definition);\r\n      if (!validationResult.valid) {\r\n        throw new BadRequestException(`Workflow validation failed: ${validationResult.errors.join(', ')}`);\r\n      }\r\n\r\n      // Create workflow entity\r\n      const workflow = this.workflowRepository.create({\r\n        name: createWorkflowDto.name,\r\n        description: createWorkflowDto.description,\r\n        definition: createWorkflowDto.definition,\r\n        status: createWorkflowDto.status || 'draft',\r\n        category: createWorkflowDto.category,\r\n        tags: createWorkflowDto.tags,\r\n        version: '1.0.0',\r\n        isTemplate: createWorkflowDto.isTemplate || false,\r\n        configuration: createWorkflowDto.configuration || {},\r\n        metadata: createWorkflowDto.metadata || {},\r\n        createdBy: user.id,\r\n        updatedBy: user.id,\r\n      });\r\n\r\n      const savedWorkflow = await this.workflowRepository.save(workflow);\r\n\r\n      // Schedule workflow if it has triggers\r\n      if (createWorkflowDto.definition.triggers?.length > 0) {\r\n        await this.scheduler.scheduleWorkflow(savedWorkflow);\r\n      }\r\n\r\n      // Emit workflow created event\r\n      this.eventEmitter.emit('workflow.created', {\r\n        workflowId: savedWorkflow.id,\r\n        name: savedWorkflow.name,\r\n        createdBy: user.id,\r\n        timestamp: new Date(),\r\n      });\r\n\r\n      this.logger.debug(`Workflow created successfully: ${savedWorkflow.id}`);\r\n      return {\r\n        id: savedWorkflow.id,\r\n        name: savedWorkflow.name,\r\n        status: savedWorkflow.status,\r\n        version: savedWorkflow.version,\r\n        createdAt: savedWorkflow.createdAt,\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to create workflow: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get workflows with filtering and pagination\r\n   */\r\n  async getWorkflows(query: WorkflowQueryDto, user: User): Promise<any> {\r\n    try {\r\n      this.logger.debug('Retrieving workflows');\r\n\r\n      const queryBuilder = this.workflowRepository\r\n        .createQueryBuilder('workflow')\r\n        .leftJoinAndSelect('workflow.executions', 'execution')\r\n        .where('1=1');\r\n\r\n      // Apply filters\r\n      if (query.status) {\r\n        queryBuilder.andWhere('workflow.status = :status', { status: query.status });\r\n      }\r\n\r\n      if (query.category) {\r\n        queryBuilder.andWhere('workflow.category = :category', { category: query.category });\r\n      }\r\n\r\n      if (query.search) {\r\n        queryBuilder.andWhere(\r\n          '(workflow.name ILIKE :search OR workflow.description ILIKE :search)',\r\n          { search: `%${query.search}%` }\r\n        );\r\n      }\r\n\r\n      if (query.createdBy) {\r\n        queryBuilder.andWhere('workflow.createdBy = :createdBy', { createdBy: query.createdBy });\r\n      }\r\n\r\n      if (query.isTemplate !== undefined) {\r\n        queryBuilder.andWhere('workflow.isTemplate = :isTemplate', { isTemplate: query.isTemplate });\r\n      }\r\n\r\n      // Apply sorting\r\n      const sortBy = query.sortBy || 'createdAt';\r\n      const sortOrder = query.sortOrder || 'DESC';\r\n      queryBuilder.orderBy(`workflow.${sortBy}`, sortOrder);\r\n\r\n      // Apply pagination\r\n      const page = query.page || 1;\r\n      const limit = query.limit || 20;\r\n      const offset = (page - 1) * limit;\r\n\r\n      queryBuilder.skip(offset).take(limit);\r\n\r\n      // Execute query\r\n      const [workflows, total] = await queryBuilder.getManyAndCount();\r\n\r\n      // Process workflows for response\r\n      const processedWorkflows = workflows.map(workflow => ({\r\n        id: workflow.id,\r\n        name: workflow.name,\r\n        description: workflow.description,\r\n        status: workflow.status,\r\n        category: workflow.category,\r\n        version: workflow.version,\r\n        isTemplate: workflow.isTemplate,\r\n        executionCount: workflow.executions?.length || 0,\r\n        lastExecuted: workflow.executions?.length > 0 \r\n          ? workflow.executions.sort((a, b) => b.startedAt.getTime() - a.startedAt.getTime())[0].startedAt\r\n          : null,\r\n        createdAt: workflow.createdAt,\r\n        updatedAt: workflow.updatedAt,\r\n      }));\r\n\r\n      this.logger.debug(`Retrieved ${workflows.length} workflows`);\r\n      return {\r\n        workflows: processedWorkflows,\r\n        total,\r\n        page,\r\n        limit,\r\n        totalPages: Math.ceil(total / limit),\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to retrieve workflows: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get workflow by ID\r\n   */\r\n  async getWorkflowById(id: string, includeHistory: boolean, includeAnalytics: boolean, user: User): Promise<any> {\r\n    try {\r\n      this.logger.debug(`Retrieving workflow: ${id}`);\r\n\r\n      const queryBuilder = this.workflowRepository\r\n        .createQueryBuilder('workflow')\r\n        .where('workflow.id = :id', { id });\r\n\r\n      if (includeHistory) {\r\n        queryBuilder.leftJoinAndSelect('workflow.executions', 'execution');\r\n      }\r\n\r\n      const workflow = await queryBuilder.getOne();\r\n\r\n      if (!workflow) {\r\n        throw new NotFoundException(`Workflow not found: ${id}`);\r\n      }\r\n\r\n      // Get analytics if requested\r\n      let analytics = null;\r\n      if (includeAnalytics) {\r\n        analytics = await this.getWorkflowAnalytics(id, '30d', true, user);\r\n      }\r\n\r\n      // Get execution history if requested\r\n      let history = null;\r\n      if (includeHistory) {\r\n        history = workflow.executions?.map(execution => ({\r\n          id: execution.id,\r\n          status: execution.status,\r\n          startedAt: execution.startedAt,\r\n          completedAt: execution.completedAt,\r\n          duration: execution.duration,\r\n          stepsCompleted: execution.stepsCompleted,\r\n          totalSteps: execution.totalSteps,\r\n          result: execution.result,\r\n          error: execution.error,\r\n        })) || [];\r\n      }\r\n\r\n      this.logger.debug(`Workflow retrieved: ${id}`);\r\n      return {\r\n        id: workflow.id,\r\n        name: workflow.name,\r\n        description: workflow.description,\r\n        definition: workflow.definition,\r\n        status: workflow.status,\r\n        category: workflow.category,\r\n        version: workflow.version,\r\n        isTemplate: workflow.isTemplate,\r\n        configuration: workflow.configuration,\r\n        metadata: workflow.metadata,\r\n        analytics,\r\n        history,\r\n        createdAt: workflow.createdAt,\r\n        updatedAt: workflow.updatedAt,\r\n        createdBy: workflow.createdBy,\r\n        updatedBy: workflow.updatedBy,\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to retrieve workflow: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update workflow\r\n   */\r\n  async updateWorkflow(id: string, updateWorkflowDto: UpdateWorkflowDto, user: User): Promise<any> {\r\n    try {\r\n      this.logger.debug(`Updating workflow: ${id}`);\r\n\r\n      const workflow = await this.workflowRepository.findOne({ where: { id } });\r\n      if (!workflow) {\r\n        throw new NotFoundException(`Workflow not found: ${id}`);\r\n      }\r\n\r\n      // Validate definition if provided\r\n      if (updateWorkflowDto.definition) {\r\n        const validationResult = await this.validateWorkflowDefinition(updateWorkflowDto.definition);\r\n        if (!validationResult.valid) {\r\n          throw new BadRequestException(`Workflow validation failed: ${validationResult.errors.join(', ')}`);\r\n        }\r\n      }\r\n\r\n      // Create new version if definition changed\r\n      let newVersion = workflow.version;\r\n      if (updateWorkflowDto.definition && JSON.stringify(updateWorkflowDto.definition) !== JSON.stringify(workflow.definition)) {\r\n        newVersion = this.incrementVersion(workflow.version);\r\n      }\r\n\r\n      // Update workflow\r\n      Object.assign(workflow, {\r\n        ...updateWorkflowDto,\r\n        version: newVersion,\r\n        updatedBy: user.id,\r\n        updatedAt: new Date(),\r\n      });\r\n\r\n      const savedWorkflow = await this.workflowRepository.save(workflow);\r\n\r\n      // Update scheduler if triggers changed\r\n      if (updateWorkflowDto.definition?.triggers) {\r\n        await this.scheduler.updateWorkflowSchedule(savedWorkflow);\r\n      }\r\n\r\n      // Emit workflow updated event\r\n      this.eventEmitter.emit('workflow.updated', {\r\n        workflowId: savedWorkflow.id,\r\n        name: savedWorkflow.name,\r\n        version: savedWorkflow.version,\r\n        updatedBy: user.id,\r\n        timestamp: new Date(),\r\n      });\r\n\r\n      this.logger.debug(`Workflow updated successfully: ${id}`);\r\n      return {\r\n        id: savedWorkflow.id,\r\n        name: savedWorkflow.name,\r\n        version: savedWorkflow.version,\r\n        updatedAt: savedWorkflow.updatedAt,\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to update workflow: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Delete workflow\r\n   */\r\n  async deleteWorkflow(id: string, user: User): Promise<void> {\r\n    try {\r\n      this.logger.debug(`Deleting workflow: ${id}`);\r\n\r\n      const workflow = await this.workflowRepository.findOne({ where: { id } });\r\n      if (!workflow) {\r\n        throw new NotFoundException(`Workflow not found: ${id}`);\r\n      }\r\n\r\n      // Cancel any running executions\r\n      await this.cancelAllRunningExecutions(id);\r\n\r\n      // Remove from scheduler\r\n      await this.scheduler.unscheduleWorkflow(id);\r\n\r\n      // Delete workflow and related data\r\n      await this.workflowRepository.remove(workflow);\r\n\r\n      // Emit workflow deleted event\r\n      this.eventEmitter.emit('workflow.deleted', {\r\n        workflowId: id,\r\n        name: workflow.name,\r\n        deletedBy: user.id,\r\n        timestamp: new Date(),\r\n      });\r\n\r\n      this.logger.debug(`Workflow deleted successfully: ${id}`);\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to delete workflow: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Execute workflow\r\n   */\r\n  async executeWorkflow(id: string, executeWorkflowDto: ExecuteWorkflowDto, user: User): Promise<any> {\r\n    try {\r\n      this.logger.debug(`Executing workflow: ${id}`);\r\n\r\n      const workflow = await this.workflowRepository.findOne({ where: { id } });\r\n      if (!workflow) {\r\n        throw new NotFoundException(`Workflow not found: ${id}`);\r\n      }\r\n\r\n      if (workflow.status !== 'active') {\r\n        throw new BadRequestException(`Workflow is not active: ${workflow.status}`);\r\n      }\r\n\r\n      // Create execution record\r\n      const execution = this.executionRepository.create({\r\n        workflowId: id,\r\n        status: 'running',\r\n        input: executeWorkflowDto.input || {},\r\n        context: executeWorkflowDto.context || {},\r\n        triggeredBy: user.id,\r\n        startedAt: new Date(),\r\n        totalSteps: this.countWorkflowSteps(workflow.definition),\r\n        stepsCompleted: 0,\r\n      });\r\n\r\n      const savedExecution = await this.executionRepository.save(execution);\r\n\r\n      // Start workflow execution\r\n      const executionPromise = this.workflowEngine.executeWorkflow(workflow, savedExecution, executeWorkflowDto);\r\n\r\n      // Don't wait for completion, return execution info immediately\r\n      executionPromise.catch(error => {\r\n        this.logger.error(`Workflow execution failed: ${error.message}`, error.stack);\r\n      });\r\n\r\n      // Emit execution started event\r\n      this.eventEmitter.emit('workflow.execution.started', {\r\n        executionId: savedExecution.id,\r\n        workflowId: id,\r\n        triggeredBy: user.id,\r\n        timestamp: new Date(),\r\n      });\r\n\r\n      this.logger.debug(`Workflow execution started: ${savedExecution.id}`);\r\n      return {\r\n        executionId: savedExecution.id,\r\n        workflowId: id,\r\n        status: 'running',\r\n        startedAt: savedExecution.startedAt,\r\n        estimatedDuration: this.estimateExecutionDuration(workflow),\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to execute workflow: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get execution status\r\n   */\r\n  async getExecutionStatus(workflowId: string, executionId: string, user: User): Promise<any> {\r\n    try {\r\n      this.logger.debug(`Getting execution status: ${executionId}`);\r\n\r\n      const execution = await this.executionRepository.findOne({\r\n        where: { id: executionId, workflowId },\r\n        relations: ['contexts'],\r\n      });\r\n\r\n      if (!execution) {\r\n        throw new NotFoundException(`Execution not found: ${executionId}`);\r\n      }\r\n\r\n      // Get current step information\r\n      const currentStep = execution.contexts?.find(ctx => ctx.status === 'running')?.stepId || null;\r\n      const steps = execution.contexts?.map(ctx => ({\r\n        stepId: ctx.stepId,\r\n        status: ctx.status,\r\n        startedAt: ctx.startedAt,\r\n        completedAt: ctx.completedAt,\r\n        result: ctx.result,\r\n        error: ctx.error,\r\n      })) || [];\r\n\r\n      // Calculate progress\r\n      const progress = execution.totalSteps > 0 ? (execution.stepsCompleted / execution.totalSteps) * 100 : 0;\r\n\r\n      this.logger.debug(`Execution status retrieved: ${executionId}`);\r\n      return {\r\n        executionId: execution.id,\r\n        workflowId: execution.workflowId,\r\n        status: execution.status,\r\n        progress,\r\n        currentStep,\r\n        steps,\r\n        startedAt: execution.startedAt,\r\n        completedAt: execution.completedAt,\r\n        duration: execution.duration,\r\n        result: execution.result,\r\n        error: execution.error,\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to get execution status: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cancel execution\r\n   */\r\n  async cancelExecution(workflowId: string, executionId: string, user: User): Promise<void> {\r\n    try {\r\n      this.logger.debug(`Cancelling execution: ${executionId}`);\r\n\r\n      const execution = await this.executionRepository.findOne({\r\n        where: { id: executionId, workflowId },\r\n      });\r\n\r\n      if (!execution) {\r\n        throw new NotFoundException(`Execution not found: ${executionId}`);\r\n      }\r\n\r\n      if (execution.status !== 'running') {\r\n        throw new BadRequestException(`Execution is not running: ${execution.status}`);\r\n      }\r\n\r\n      // Cancel execution\r\n      await this.workflowEngine.cancelExecution(executionId);\r\n\r\n      // Update execution status\r\n      execution.status = 'cancelled';\r\n      execution.completedAt = new Date();\r\n      execution.duration = execution.completedAt.getTime() - execution.startedAt.getTime();\r\n      await this.executionRepository.save(execution);\r\n\r\n      // Emit execution cancelled event\r\n      this.eventEmitter.emit('workflow.execution.cancelled', {\r\n        executionId,\r\n        workflowId,\r\n        cancelledBy: user.id,\r\n        timestamp: new Date(),\r\n      });\r\n\r\n      this.logger.debug(`Execution cancelled: ${executionId}`);\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to cancel execution: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get workflow analytics\r\n   */\r\n  async getWorkflowAnalytics(id: string, timeRange: string, includeStepAnalytics: boolean, user: User): Promise<any> {\r\n    try {\r\n      this.logger.debug(`Getting workflow analytics: ${id}`);\r\n\r\n      const timeFilter = this.getTimeRangeFilter(timeRange);\r\n      \r\n      // Get execution statistics\r\n      const executions = await this.executionRepository\r\n        .createQueryBuilder('execution')\r\n        .where('execution.workflowId = :id', { id })\r\n        .andWhere('execution.startedAt BETWEEN :startDate AND :endDate', timeFilter)\r\n        .getMany();\r\n\r\n      const total = executions.length;\r\n      const successful = executions.filter(e => e.status === 'completed').length;\r\n      const failed = executions.filter(e => e.status === 'failed').length;\r\n      const cancelled = executions.filter(e => e.status === 'cancelled').length;\r\n\r\n      // Calculate performance metrics\r\n      const completedExecutions = executions.filter(e => e.duration !== null);\r\n      const avgDuration = completedExecutions.length > 0\r\n        ? completedExecutions.reduce((sum, e) => sum + e.duration, 0) / completedExecutions.length\r\n        : 0;\r\n\r\n      const durations = completedExecutions.map(e => e.duration).sort((a, b) => a - b);\r\n      const minDuration = durations.length > 0 ? durations[0] : 0;\r\n      const maxDuration = durations.length > 0 ? durations[durations.length - 1] : 0;\r\n\r\n      const avgStepsCompleted = executions.length > 0\r\n        ? executions.reduce((sum, e) => sum + e.stepsCompleted, 0) / executions.length\r\n        : 0;\r\n\r\n      // Get trends\r\n      const trends = await this.calculateExecutionTrends(id, timeRange);\r\n\r\n      // Get step analytics if requested\r\n      let stepAnalytics = null;\r\n      if (includeStepAnalytics) {\r\n        stepAnalytics = await this.getStepAnalytics(id, timeRange);\r\n      }\r\n\r\n      this.logger.debug(`Workflow analytics retrieved: ${id}`);\r\n      return {\r\n        workflowId: id,\r\n        executions: {\r\n          total,\r\n          successful,\r\n          failed,\r\n          cancelled,\r\n          successRate: total > 0 ? (successful / total) * 100 : 0,\r\n        },\r\n        performance: {\r\n          avgDuration,\r\n          minDuration,\r\n          maxDuration,\r\n          avgStepsCompleted,\r\n        },\r\n        trends,\r\n        stepAnalytics,\r\n        timeRange,\r\n        generatedAt: new Date(),\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to get workflow analytics: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Additional methods would be implemented here for:\r\n   * - validateWorkflow, getWorkflowTemplates, cloneWorkflow\r\n   * - Various helper methods for validation, analytics, and processing\r\n   */\r\n\r\n  /**\r\n   * Validate workflow definition\r\n   */\r\n  private async validateWorkflowDefinition(definition: any): Promise<{ valid: boolean; errors: string[]; warnings: string[] }> {\r\n    const errors: string[] = [];\r\n    const warnings: string[] = [];\r\n\r\n    // Basic structure validation\r\n    if (!definition.steps || !Array.isArray(definition.steps)) {\r\n      errors.push('Workflow must have steps array');\r\n    }\r\n\r\n    if (!definition.startStep) {\r\n      errors.push('Workflow must have startStep defined');\r\n    }\r\n\r\n    // Validate steps\r\n    if (definition.steps) {\r\n      for (const step of definition.steps) {\r\n        if (!step.id) {\r\n          errors.push('Each step must have an id');\r\n        }\r\n        if (!step.type) {\r\n          errors.push('Each step must have a type');\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      valid: errors.length === 0,\r\n      errors,\r\n      warnings,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Additional private helper methods would be implemented here\r\n   */\r\n  private incrementVersion(version: string): string {\r\n    const parts = version.split('.');\r\n    const patch = parseInt(parts[2]) + 1;\r\n    return `${parts[0]}.${parts[1]}.${patch}`;\r\n  }\r\n\r\n  private countWorkflowSteps(definition: any): number {\r\n    return definition.steps?.length || 0;\r\n  }\r\n\r\n  private estimateExecutionDuration(workflow: any): number {\r\n    // Simple estimation based on step count\r\n    return (workflow.definition.steps?.length || 1) * 30000; // 30 seconds per step\r\n  }\r\n\r\n  private async cancelAllRunningExecutions(workflowId: string): Promise<void> {\r\n    const runningExecutions = await this.executionRepository.find({\r\n      where: { workflowId, status: 'running' },\r\n    });\r\n\r\n    for (const execution of runningExecutions) {\r\n      await this.workflowEngine.cancelExecution(execution.id);\r\n    }\r\n  }\r\n\r\n  private getTimeRangeFilter(timeRange: string): { startDate: Date; endDate: Date } {\r\n    const endDate = new Date();\r\n    const startDate = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000); // Default 30 days\r\n    return { startDate, endDate };\r\n  }\r\n\r\n  private async calculateExecutionTrends(workflowId: string, timeRange: string): Promise<any[]> {\r\n    // Implementation would calculate execution trends over time\r\n    return [];\r\n  }\r\n\r\n  private async getStepAnalytics(workflowId: string, timeRange: string): Promise<any[]> {\r\n    // Implementation would get step-level analytics\r\n    return [];\r\n  }\r\n}\r\n"], "version": 3}