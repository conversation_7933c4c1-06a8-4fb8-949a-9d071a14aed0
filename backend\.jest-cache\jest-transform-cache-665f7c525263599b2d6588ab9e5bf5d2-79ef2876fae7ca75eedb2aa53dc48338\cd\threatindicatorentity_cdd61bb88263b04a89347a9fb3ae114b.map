{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\domain\\entities\\threat-indicator.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCASiB;AACjB,6EAAkE;AAElE;;GAEG;AACH,IAAY,mBAWX;AAXD,WAAY,mBAAmB;IAC7B,gDAAyB,CAAA;IACzB,wCAAiB,CAAA;IACjB,kCAAW,CAAA;IACX,8CAAuB,CAAA;IACvB,sCAAe,CAAA;IACf,oDAA6B,CAAA;IAC7B,sCAAe,CAAA;IACf,gDAAyB,CAAA;IACzB,kDAA2B,CAAA;IAC3B,kCAAW,CAAA;AACb,CAAC,EAXW,mBAAmB,mCAAnB,mBAAmB,QAW9B;AAED;;GAEG;AACH,IAAY,yBAKX;AALD,WAAY,yBAAyB;IACnC,wCAAW,CAAA;IACX,8CAAiB,CAAA;IACjB,0CAAa,CAAA;IACb,oDAAuB,CAAA;AACzB,CAAC,EALW,yBAAyB,yCAAzB,yBAAyB,QAKpC;AAED;;GAEG;AACH,IAAY,qBAKX;AALD,WAAY,qBAAqB;IAC/B,0CAAiB,CAAA;IACjB,8CAAqB,CAAA;IACrB,4CAAmB,CAAA;IACnB,sDAA6B,CAAA;AAC/B,CAAC,EALW,qBAAqB,qCAArB,qBAAqB,QAKhC;AAED;;;;;GAKG;AAMI,IAAM,eAAe,GAArB,MAAM,eAAe;IAsI1B;;OAEG;IACH,QAAQ;QACN,IAAI,IAAI,CAAC,MAAM,KAAK,qBAAqB,CAAC,MAAM,EAAE,CAAC;YACjD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC;YAClD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,YAAY;QACV,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QAC7D,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;QAC3B,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC;IAC7B,CAAC;CACF,CAAA;AA7KY,0CAAe;AAE1B;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;2CACpB;AAQX;IANC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,mBAAmB;QACzB,OAAO,EAAE,0BAA0B;KACpC,CAAC;IACD,IAAA,eAAK,GAAE;;6CACkB;AAO1B;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,qDAAqD;KAC/D,CAAC;IACD,IAAA,eAAK,GAAE;;8CACM;AAOd;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,6CAA6C;KACvD,CAAC;;oDACmB;AAQrB;IANC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,yBAAyB;QAC/B,OAAO,EAAE,yBAAyB,CAAC,MAAM;QACzC,OAAO,EAAE,mCAAmC;KAC7C,CAAC;;mDACoC;AAStC;IAPC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,qBAAqB;QAC3B,OAAO,EAAE,qBAAqB,CAAC,MAAM;QACrC,OAAO,EAAE,iCAAiC;KAC3C,CAAC;IACD,IAAA,eAAK,GAAE;;+CACsB;AAO9B;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,OAAO;QACb,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,mDAAmD;KAC7D,CAAC;kDACS,MAAM,oBAAN,MAAM;iDAAc;AAQ/B;IANC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,KAAK,EAAE,IAAI;QACX,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,qCAAqC;KAC/C,CAAC;;6CACa;AAOf;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,gCAAgC;KAC1C,CAAC;;+CACc;AAQhB;IANC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,WAAW;QACjB,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,wCAAwC;KAClD,CAAC;IACD,IAAA,eAAK,GAAE;kDACI,IAAI,oBAAJ,IAAI;kDAAC;AAQjB;IANC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,WAAW;QACjB,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,uCAAuC;KACjD,CAAC;IACD,IAAA,eAAK,GAAE;kDACG,IAAI,oBAAJ,IAAI;iDAAC;AAOhB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,WAAW;QACjB,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,uEAAuE;KACjF,CAAC;kDACU,IAAI,oBAAJ,IAAI;kDAAC;AAOjB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,CAAC;QACV,OAAO,EAAE,kDAAkD;KAC5D,CAAC;;yDACuB;AASzB;IAPC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,SAAS;QACf,SAAS,EAAE,CAAC;QACZ,KAAK,EAAE,CAAC;QACR,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,mCAAmC;KAC7C,CAAC;;sDACqB;AAQvB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,uCAAuC;KACjD,CAAC;;6DAC4B;AAM9B;IAJC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,+CAAkB,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,UAAU,EAAE;QAClE,QAAQ,EAAE,SAAS;KACpB,CAAC;IACD,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,sBAAsB,EAAE,CAAC;kDACxB,+CAAkB,oBAAlB,+CAAkB;2DAAC;AAQxC;IALC,IAAA,0BAAgB,EAAC;QAChB,IAAI,EAAE,WAAW;QACjB,OAAO,EAAE,GAAG,EAAE,CAAC,mBAAmB;QAClC,OAAO,EAAE,gCAAgC;KAC1C,CAAC;kDACS,IAAI,oBAAJ,IAAI;kDAAC;AAQhB;IANC,IAAA,0BAAgB,EAAC;QAChB,IAAI,EAAE,WAAW;QACjB,OAAO,EAAE,GAAG,EAAE,CAAC,mBAAmB;QAClC,QAAQ,EAAE,mBAAmB;QAC7B,OAAO,EAAE,qCAAqC;KAC/C,CAAC;kDACS,IAAI,oBAAJ,IAAI;kDAAC;0BApIL,eAAe;IAL3B,IAAA,gBAAM,EAAC,mBAAmB,CAAC;IAC3B,IAAA,eAAK,EAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACxB,IAAA,eAAK,EAAC,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;IAC/B,IAAA,eAAK,EAAC,CAAC,sBAAsB,CAAC,CAAC;IAC/B,IAAA,eAAK,EAAC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;GACpB,eAAe,CA6K3B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\threat-intelligence\\domain\\entities\\threat-indicator.entity.ts"], "sourcesContent": ["import {\n  Entity,\n  PrimaryGeneratedColumn,\n  Column,\n  CreateDateColumn,\n  UpdateDateColumn,\n  Index,\n  ManyToOne,\n  JoinColumn,\n} from 'typeorm';\nimport { ThreatIntelligence } from './threat-intelligence.entity';\n\n/**\n * Threat Indicator Types\n */\nexport enum ThreatIndicatorType {\n  IP_ADDRESS = 'ip_address',\n  DOMAIN = 'domain',\n  URL = 'url',\n  FILE_HASH = 'file_hash',\n  EMAIL = 'email',\n  REGISTRY_KEY = 'registry_key',\n  MUTEX = 'mutex',\n  USER_AGENT = 'user_agent',\n  CERTIFICATE = 'certificate',\n  ASN = 'asn',\n}\n\n/**\n * Threat Indicator Confidence Levels\n */\nexport enum ThreatIndicatorConfidence {\n  LOW = 'low',\n  MEDIUM = 'medium',\n  HIGH = 'high',\n  VERY_HIGH = 'very_high',\n}\n\n/**\n * Threat Indicator Status\n */\nexport enum ThreatIndicatorStatus {\n  ACTIVE = 'active',\n  INACTIVE = 'inactive',\n  EXPIRED = 'expired',\n  UNDER_REVIEW = 'under_review',\n}\n\n/**\n * Threat Indicator Entity\n * \n * Represents individual indicators of compromise (IoCs) and threat indicators\n * that can be used for threat detection and analysis\n */\n@Entity('threat_indicators')\n@Index(['type', 'value'])\n@Index(['confidence', 'status'])\n@Index(['threatIntelligenceId'])\n@Index(['firstSeen', 'lastSeen'])\nexport class ThreatIndicator {\n  @PrimaryGeneratedColumn('uuid')\n  id: string;\n\n  @Column({\n    type: 'enum',\n    enum: ThreatIndicatorType,\n    comment: 'Type of threat indicator',\n  })\n  @Index()\n  type: ThreatIndicatorType;\n\n  @Column({\n    type: 'text',\n    comment: 'The actual indicator value (IP, domain, hash, etc.)',\n  })\n  @Index()\n  value: string;\n\n  @Column({\n    type: 'text',\n    nullable: true,\n    comment: 'Human-readable description of the indicator',\n  })\n  description?: string;\n\n  @Column({\n    type: 'enum',\n    enum: ThreatIndicatorConfidence,\n    default: ThreatIndicatorConfidence.MEDIUM,\n    comment: 'Confidence level of the indicator',\n  })\n  confidence: ThreatIndicatorConfidence;\n\n  @Column({\n    type: 'enum',\n    enum: ThreatIndicatorStatus,\n    default: ThreatIndicatorStatus.ACTIVE,\n    comment: 'Current status of the indicator',\n  })\n  @Index()\n  status: ThreatIndicatorStatus;\n\n  @Column({\n    type: 'jsonb',\n    nullable: true,\n    comment: 'Additional metadata and context for the indicator',\n  })\n  metadata?: Record<string, any>;\n\n  @Column({\n    type: 'text',\n    array: true,\n    default: '{}',\n    comment: 'Tags associated with this indicator',\n  })\n  tags: string[];\n\n  @Column({\n    type: 'text',\n    nullable: true,\n    comment: 'Source of the threat indicator',\n  })\n  source?: string;\n\n  @Column({\n    type: 'timestamp',\n    nullable: true,\n    comment: 'When this indicator was first observed',\n  })\n  @Index()\n  firstSeen?: Date;\n\n  @Column({\n    type: 'timestamp',\n    nullable: true,\n    comment: 'When this indicator was last observed',\n  })\n  @Index()\n  lastSeen?: Date;\n\n  @Column({\n    type: 'timestamp',\n    nullable: true,\n    comment: 'When this indicator expires and should no longer be considered active',\n  })\n  expiresAt?: Date;\n\n  @Column({\n    type: 'integer',\n    default: 0,\n    comment: 'Number of times this indicator has been observed',\n  })\n  observationCount: number;\n\n  @Column({\n    type: 'decimal',\n    precision: 3,\n    scale: 2,\n    nullable: true,\n    comment: 'Severity score from 0.00 to 10.00',\n  })\n  severityScore?: number;\n\n  // Relationships\n  @Column({\n    type: 'uuid',\n    nullable: true,\n    comment: 'Associated threat intelligence record',\n  })\n  threatIntelligenceId?: string;\n\n  @ManyToOne(() => ThreatIntelligence, (threat) => threat.indicators, {\n    onDelete: 'CASCADE',\n  })\n  @JoinColumn({ name: 'threatIntelligenceId' })\n  threatIntelligence?: ThreatIntelligence;\n\n  // Timestamps\n  @CreateDateColumn({\n    type: 'timestamp',\n    default: () => 'CURRENT_TIMESTAMP',\n    comment: 'When the indicator was created',\n  })\n  createdAt: Date;\n\n  @UpdateDateColumn({\n    type: 'timestamp',\n    default: () => 'CURRENT_TIMESTAMP',\n    onUpdate: 'CURRENT_TIMESTAMP',\n    comment: 'When the indicator was last updated',\n  })\n  updatedAt: Date;\n\n  /**\n   * Check if the indicator is currently active\n   */\n  isActive(): boolean {\n    if (this.status !== ThreatIndicatorStatus.ACTIVE) {\n      return false;\n    }\n\n    if (this.expiresAt && this.expiresAt < new Date()) {\n      return false;\n    }\n\n    return true;\n  }\n\n  /**\n   * Check if the indicator has expired\n   */\n  isExpired(): boolean {\n    return this.expiresAt ? this.expiresAt < new Date() : false;\n  }\n\n  /**\n   * Get the age of the indicator in days\n   */\n  getAgeInDays(): number {\n    const now = new Date();\n    const created = new Date(this.createdAt);\n    const diffTime = Math.abs(now.getTime() - created.getTime());\n    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n  }\n\n  /**\n   * Update the last seen timestamp and increment observation count\n   */\n  recordObservation(): void {\n    this.lastSeen = new Date();\n    this.observationCount += 1;\n  }\n}\n"], "version": 3}