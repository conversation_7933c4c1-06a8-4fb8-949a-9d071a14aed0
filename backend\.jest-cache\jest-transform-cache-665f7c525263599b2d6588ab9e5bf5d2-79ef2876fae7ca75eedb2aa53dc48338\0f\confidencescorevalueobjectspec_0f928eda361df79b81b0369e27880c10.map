{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\value-objects\\__tests__\\confidence-score.value-object.spec.ts", "mappings": ";;AAAA,oFAAmE;AAEnE,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;IAC5C,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,KAAK,GAAG,IAAI,+CAAe,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,KAAK,GAAG,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC;YACvC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,KAAK,GAAG,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC;YACvC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,+CAAe,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,wCAAwC,CAAC,CAAC;QAC5F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,2CAA2C,CAAC,CAAC;QAC9F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,+CAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;YACvF,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,+CAAe,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,+CAAe,CAAC,KAAY,CAAC,CAAC,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAC;QAC/F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,KAAK,GAAG,+CAAe,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;YACjD,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAe,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC;YAClG,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAe,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC;QACpG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,KAAK,GAAG,+CAAe,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAe,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,4BAA4B,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAe,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,gDAAgD,CAAC,CAAC;YAC5G,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAe,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,gDAAgD,CAAC,CAAC;QAC9G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,CAAC,+CAAe,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC9C,MAAM,CAAC,+CAAe,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACjD,MAAM,CAAC,+CAAe,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC/C,MAAM,CAAC,+CAAe,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClD,MAAM,CAAC,+CAAe,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,CAAC,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC7D,MAAM,CAAC,IAAI,+CAAe,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzD,MAAM,CAAC,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3D,MAAM,CAAC,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACzD,MAAM,CAAC,IAAI,+CAAe,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,QAAQ,GAAG,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC;YAC1C,MAAM,WAAW,GAAG,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC;YAC7C,MAAM,SAAS,GAAG,IAAI,+CAAe,CAAC,IAAI,CAAC,CAAC;YAE5C,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEtC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,KAAK,GAAG,IAAI,+CAAe,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,KAAK,GAAG,IAAI,+CAAe,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,KAAK,GAAG,IAAI,+CAAe,CAAC,MAAM,CAAC,CAAC;YAC1C,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACjD,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,KAAK,GAAG,IAAI,+CAAe,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,MAAM,GAAG,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC;YACxC,MAAM,MAAM,GAAG,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC;YACxC,MAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;YAEjD,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,wBAAwB;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,MAAM,GAAG,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC;YACxC,MAAM,MAAM,GAAG,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC;YAExC,MAAM,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;YACzF,MAAM,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,KAAK,GAAG,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC;YACvC,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,KAAK,GAAG,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC;YACvC,MAAM,UAAU,GAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACvC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,KAAK,GAAG,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC;YACvC,MAAM,UAAU,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,KAAK,GAAG,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC;YACvC,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,MAAM,GAAG,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC;YACxC,MAAM,MAAM,GAAG,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC;YACxC,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAC/B,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,MAAM,GAAG,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC;YACxC,MAAM,MAAM,GAAG,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC;YACxC,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAC/B,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,MAAM,GAAG,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC;YACxC,MAAM,MAAM,GAAG,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC;YACxC,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAC3C,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,MAAM,GAAG,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC;YACxC,MAAM,MAAM,GAAG,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC;YACxC,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAC3C,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,MAAM,MAAM,GAAG,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC;YACxC,MAAM,MAAM,GAAG,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,MAAM,GAAG,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC;YACxC,MAAM,MAAM,GAAG,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC;YAExC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,KAAK,GAAG,IAAI,+CAAe,CAAC,MAAM,CAAC,CAAC;YAC1C,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,MAAM,GAAG;gBACb,IAAI,+CAAe,CAAC,GAAG,CAAC;gBACxB,IAAI,+CAAe,CAAC,GAAG,CAAC;gBACxB,IAAI,+CAAe,CAAC,GAAG,CAAC;aACzB,CAAC;YAEF,MAAM,OAAO,GAAG,+CAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAe,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,yCAAyC,CAAC,CAAC;QAC/F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,MAAM,GAAG;gBACb,IAAI,+CAAe,CAAC,GAAG,CAAC;gBACxB,IAAI,+CAAe,CAAC,GAAG,CAAC;aACzB,CAAC;YACF,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAE3B,MAAM,WAAW,GAAG,+CAAe,CAAC,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YACrE,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,gCAAgC;QAC/E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,MAAM,MAAM,GAAG,CAAC,IAAI,+CAAe,CAAC,GAAG,CAAC,CAAC,CAAC;YAC1C,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAE3B,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAe,CAAC,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;iBAC3D,OAAO,CAAC,qDAAqD,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,MAAM,GAAG;gBACb,IAAI,+CAAe,CAAC,GAAG,CAAC;gBACxB,IAAI,+CAAe,CAAC,GAAG,CAAC;gBACxB,IAAI,+CAAe,CAAC,GAAG,CAAC;aACzB,CAAC;YAEF,MAAM,CAAC,+CAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpD,MAAM,CAAC,+CAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAe,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;YACpF,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAe,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,KAAK,GAAG,IAAI,+CAAe,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YAE5B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9B,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAChC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,IAAI,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;YAC7B,MAAM,KAAK,GAAG,+CAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAE7C,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;YACrE,MAAM,MAAM,GAAG,IAAI,+CAAe,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,MAAM,GAAG,IAAI,+CAAe,CAAC,IAAI,CAAC,CAAC;YAEzC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,MAAM,MAAM,GAAG,IAAI,+CAAe,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,MAAM,GAAG,IAAI,+CAAe,CAAC,IAAI,CAAC,CAAC;YAEzC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,KAAK,GAAG,IAAI,+CAAe,CAAC,IAAI,CAAC,CAAC;YAExC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,IAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,SAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\value-objects\\__tests__\\confidence-score.value-object.spec.ts"], "sourcesContent": ["import { ConfidenceScore } from '../confidence-score.value-object';\r\n\r\ndescribe('ConfidenceScore Value Object', () => {\r\n  describe('Construction', () => {\r\n    it('should create a valid confidence score', () => {\r\n      const score = new ConfidenceScore(0.85);\r\n      expect(score.value).toBe(0.85);\r\n    });\r\n\r\n    it('should accept minimum value', () => {\r\n      const score = new ConfidenceScore(0.0);\r\n      expect(score.value).toBe(0.0);\r\n    });\r\n\r\n    it('should accept maximum value', () => {\r\n      const score = new ConfidenceScore(1.0);\r\n      expect(score.value).toBe(1.0);\r\n    });\r\n\r\n    it('should throw error for negative values', () => {\r\n      expect(() => new ConfidenceScore(-0.1)).toThrow('Confidence score cannot be less than 0');\r\n    });\r\n\r\n    it('should throw error for values greater than 1', () => {\r\n      expect(() => new ConfidenceScore(1.1)).toThrow('Confidence score cannot be greater than 1');\r\n    });\r\n\r\n    it('should throw error for NaN', () => {\r\n      expect(() => new ConfidenceScore(NaN)).toThrow('Confidence score cannot be NaN');\r\n    });\r\n\r\n    it('should throw error for infinite values', () => {\r\n      expect(() => new ConfidenceScore(Infinity)).toThrow('Confidence score must be finite');\r\n      expect(() => new ConfidenceScore(-Infinity)).toThrow('Confidence score must be finite');\r\n    });\r\n\r\n    it('should throw error for non-number values', () => {\r\n      expect(() => new ConfidenceScore('0.5' as any)).toThrow('Confidence score must be a number');\r\n    });\r\n  });\r\n\r\n  describe('Factory Methods', () => {\r\n    it('should create from percentage', () => {\r\n      const score = ConfidenceScore.fromPercentage(85);\r\n      expect(score.value).toBe(0.85);\r\n    });\r\n\r\n    it('should throw error for invalid percentage', () => {\r\n      expect(() => ConfidenceScore.fromPercentage(-10)).toThrow('Percentage must be between 0 and 100');\r\n      expect(() => ConfidenceScore.fromPercentage(110)).toThrow('Percentage must be between 0 and 100');\r\n    });\r\n\r\n    it('should create from fraction', () => {\r\n      const score = ConfidenceScore.fromFraction(17, 20);\r\n      expect(score.value).toBe(0.85);\r\n    });\r\n\r\n    it('should throw error for zero denominator', () => {\r\n      expect(() => ConfidenceScore.fromFraction(1, 0)).toThrow('Denominator cannot be zero');\r\n    });\r\n\r\n    it('should throw error for negative values in fraction', () => {\r\n      expect(() => ConfidenceScore.fromFraction(-1, 2)).toThrow('Numerator and denominator must be non-negative');\r\n      expect(() => ConfidenceScore.fromFraction(1, -2)).toThrow('Numerator and denominator must be non-negative');\r\n    });\r\n\r\n    it('should create predefined confidence levels', () => {\r\n      expect(ConfidenceScore.low().value).toBe(0.3);\r\n      expect(ConfidenceScore.medium().value).toBe(0.7);\r\n      expect(ConfidenceScore.high().value).toBe(0.9);\r\n      expect(ConfidenceScore.maximum().value).toBe(1.0);\r\n      expect(ConfidenceScore.minimum().value).toBe(0.0);\r\n    });\r\n  });\r\n\r\n  describe('Level Classification', () => {\r\n    it('should classify confidence levels correctly', () => {\r\n      expect(new ConfidenceScore(0.1).getLevel()).toBe('very_low');\r\n      expect(new ConfidenceScore(0.25).getLevel()).toBe('low');\r\n      expect(new ConfidenceScore(0.5).getLevel()).toBe('medium');\r\n      expect(new ConfidenceScore(0.8).getLevel()).toBe('high');\r\n      expect(new ConfidenceScore(0.95).getLevel()).toBe('very_high');\r\n    });\r\n\r\n    it('should check level predicates', () => {\r\n      const lowScore = new ConfidenceScore(0.2);\r\n      const mediumScore = new ConfidenceScore(0.5);\r\n      const highScore = new ConfidenceScore(0.95);\r\n\r\n      expect(lowScore.isLow()).toBe(true);\r\n      expect(mediumScore.isMedium()).toBe(true);\r\n      expect(highScore.isHigh()).toBe(true);\r\n\r\n      expect(highScore.isLow()).toBe(false);\r\n      expect(lowScore.isHigh()).toBe(false);\r\n    });\r\n\r\n    it('should check threshold compliance', () => {\r\n      const score = new ConfidenceScore(0.75);\r\n      expect(score.meetsThreshold(0.7)).toBe(true);\r\n      expect(score.meetsThreshold(0.8)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('Conversions', () => {\r\n    it('should convert to percentage', () => {\r\n      const score = new ConfidenceScore(0.85);\r\n      expect(score.toPercentage()).toBe(85);\r\n    });\r\n\r\n    it('should convert to percentage string', () => {\r\n      const score = new ConfidenceScore(0.8567);\r\n      expect(score.toPercentageString()).toBe('85.7%');\r\n      expect(score.toPercentageString(2)).toBe('85.67%');\r\n    });\r\n\r\n    it('should convert to string', () => {\r\n      const score = new ConfidenceScore(0.85);\r\n      expect(score.toString()).toBe('85.0% (high)');\r\n    });\r\n  });\r\n\r\n  describe('Mathematical Operations', () => {\r\n    it('should combine with another confidence score', () => {\r\n      const score1 = new ConfidenceScore(0.8);\r\n      const score2 = new ConfidenceScore(0.6);\r\n      const combined = score1.combineWith(score2, 0.7);\r\n      \r\n      expect(combined.value).toBeCloseTo(0.74); // 0.8 * 0.7 + 0.6 * 0.3\r\n    });\r\n\r\n    it('should throw error for invalid weight in combine', () => {\r\n      const score1 = new ConfidenceScore(0.8);\r\n      const score2 = new ConfidenceScore(0.6);\r\n      \r\n      expect(() => score1.combineWith(score2, -0.1)).toThrow('Weight must be between 0 and 1');\r\n      expect(() => score1.combineWith(score2, 1.1)).toThrow('Weight must be between 0 and 1');\r\n    });\r\n\r\n    it('should calculate inverse', () => {\r\n      const score = new ConfidenceScore(0.3);\r\n      const inverse = score.inverse();\r\n      expect(inverse.value).toBe(0.7);\r\n    });\r\n\r\n    it('should multiply by factor', () => {\r\n      const score = new ConfidenceScore(0.5);\r\n      const multiplied = score.multiply(1.5);\r\n      expect(multiplied.value).toBe(0.75);\r\n    });\r\n\r\n    it('should cap multiplication at maximum', () => {\r\n      const score = new ConfidenceScore(0.8);\r\n      const multiplied = score.multiply(2);\r\n      expect(multiplied.value).toBe(1.0);\r\n    });\r\n\r\n    it('should throw error for negative factor', () => {\r\n      const score = new ConfidenceScore(0.5);\r\n      expect(() => score.multiply(-1)).toThrow('Factor cannot be negative');\r\n    });\r\n\r\n    it('should add confidence scores', () => {\r\n      const score1 = new ConfidenceScore(0.3);\r\n      const score2 = new ConfidenceScore(0.4);\r\n      const sum = score1.add(score2);\r\n      expect(sum.value).toBe(0.7);\r\n    });\r\n\r\n    it('should cap addition at maximum', () => {\r\n      const score1 = new ConfidenceScore(0.7);\r\n      const score2 = new ConfidenceScore(0.5);\r\n      const sum = score1.add(score2);\r\n      expect(sum.value).toBe(1.0);\r\n    });\r\n\r\n    it('should subtract confidence scores', () => {\r\n      const score1 = new ConfidenceScore(0.8);\r\n      const score2 = new ConfidenceScore(0.3);\r\n      const difference = score1.subtract(score2);\r\n      expect(difference.value).toBe(0.5);\r\n    });\r\n\r\n    it('should floor subtraction at minimum', () => {\r\n      const score1 = new ConfidenceScore(0.2);\r\n      const score2 = new ConfidenceScore(0.5);\r\n      const difference = score1.subtract(score2);\r\n      expect(difference.value).toBe(0.0);\r\n    });\r\n\r\n    it('should calculate difference', () => {\r\n      const score1 = new ConfidenceScore(0.8);\r\n      const score2 = new ConfidenceScore(0.3);\r\n      expect(score1.differenceFrom(score2)).toBe(0.5);\r\n      expect(score2.differenceFrom(score1)).toBe(0.5);\r\n    });\r\n\r\n    it('should compare confidence scores', () => {\r\n      const score1 = new ConfidenceScore(0.8);\r\n      const score2 = new ConfidenceScore(0.3);\r\n      \r\n      expect(score1.isGreaterThan(score2)).toBe(true);\r\n      expect(score2.isLessThan(score1)).toBe(true);\r\n      expect(score1.isLessThan(score2)).toBe(false);\r\n      expect(score2.isGreaterThan(score1)).toBe(false);\r\n    });\r\n\r\n    it('should round confidence scores', () => {\r\n      const score = new ConfidenceScore(0.8567);\r\n      expect(score.round().value).toBe(0.86);\r\n      expect(score.round(3).value).toBe(0.857);\r\n    });\r\n  });\r\n\r\n  describe('Static Utility Methods', () => {\r\n    it('should calculate average', () => {\r\n      const scores = [\r\n        new ConfidenceScore(0.8),\r\n        new ConfidenceScore(0.6),\r\n        new ConfidenceScore(0.9),\r\n      ];\r\n      \r\n      const average = ConfidenceScore.average(scores);\r\n      expect(average.value).toBeCloseTo(0.7667, 3);\r\n    });\r\n\r\n    it('should throw error for empty array in average', () => {\r\n      expect(() => ConfidenceScore.average([])).toThrow('Cannot calculate average of empty array');\r\n    });\r\n\r\n    it('should calculate weighted average', () => {\r\n      const scores = [\r\n        new ConfidenceScore(0.8),\r\n        new ConfidenceScore(0.6),\r\n      ];\r\n      const weights = [0.7, 0.3];\r\n      \r\n      const weightedAvg = ConfidenceScore.weightedAverage(scores, weights);\r\n      expect(weightedAvg.value).toBeCloseTo(0.74); // (0.8 * 0.7 + 0.6 * 0.3) / 1.0\r\n    });\r\n\r\n    it('should throw error for mismatched arrays in weighted average', () => {\r\n      const scores = [new ConfidenceScore(0.8)];\r\n      const weights = [0.7, 0.3];\r\n      \r\n      expect(() => ConfidenceScore.weightedAverage(scores, weights))\r\n        .toThrow('Scores and weights arrays must have the same length');\r\n    });\r\n\r\n    it('should find maximum and minimum', () => {\r\n      const scores = [\r\n        new ConfidenceScore(0.8),\r\n        new ConfidenceScore(0.3),\r\n        new ConfidenceScore(0.9),\r\n      ];\r\n      \r\n      expect(ConfidenceScore.max(scores).value).toBe(0.9);\r\n      expect(ConfidenceScore.min(scores).value).toBe(0.3);\r\n    });\r\n\r\n    it('should throw error for empty array in max/min', () => {\r\n      expect(() => ConfidenceScore.max([])).toThrow('Cannot find maximum of empty array');\r\n      expect(() => ConfidenceScore.min([])).toThrow('Cannot find minimum of empty array');\r\n    });\r\n  });\r\n\r\n  describe('JSON Serialization', () => {\r\n    it('should serialize to JSON', () => {\r\n      const score = new ConfidenceScore(0.85);\r\n      const json = score.toJSON();\r\n      \r\n      expect(json.value).toBe(0.85);\r\n      expect(json.percentage).toBe(85);\r\n      expect(json.level).toBe('high');\r\n      expect(json.isHigh).toBe(true);\r\n    });\r\n\r\n    it('should deserialize from JSON', () => {\r\n      const json = { value: 0.75 };\r\n      const score = ConfidenceScore.fromJSON(json);\r\n      \r\n      expect(score.value).toBe(0.75);\r\n    });\r\n  });\r\n\r\n  describe('Equality', () => {\r\n    it('should be equal to another confidence score with same value', () => {\r\n      const score1 = new ConfidenceScore(0.85);\r\n      const score2 = new ConfidenceScore(0.85);\r\n      \r\n      expect(score1.equals(score2)).toBe(true);\r\n    });\r\n\r\n    it('should not be equal to confidence score with different value', () => {\r\n      const score1 = new ConfidenceScore(0.85);\r\n      const score2 = new ConfidenceScore(0.75);\r\n      \r\n      expect(score1.equals(score2)).toBe(false);\r\n    });\r\n\r\n    it('should not be equal to null or undefined', () => {\r\n      const score = new ConfidenceScore(0.85);\r\n      \r\n      expect(score.equals(null as any)).toBe(false);\r\n      expect(score.equals(undefined as any)).toBe(false);\r\n    });\r\n  });\r\n});"], "version": 3}