import { AccuracyScore } from '../accuracy-score.value-object';

describe('AccuracyScore Value Object', () => {
  describe('Construction', () => {
    it('should create a valid accuracy score', () => {
      const score = new AccuracyScore(0.85);
      expect(score.value).toBe(0.85);
    });

    it('should accept minimum value', () => {
      const score = new AccuracyScore(0.0);
      expect(score.value).toBe(0.0);
    });

    it('should accept maximum value', () => {
      const score = new AccuracyScore(1.0);
      expect(score.value).toBe(1.0);
    });

    it('should throw error for negative values', () => {
      expect(() => new AccuracyScore(-0.1)).toThrow('Accuracy score cannot be less than 0');
    });

    it('should throw error for values greater than 1', () => {
      expect(() => new AccuracyScore(1.1)).toThrow('Accuracy score cannot be greater than 1');
    });

    it('should throw error for NaN', () => {
      expect(() => new AccuracyScore(NaN)).toThrow('Accuracy score cannot be NaN');
    });

    it('should throw error for infinite values', () => {
      expect(() => new AccuracyScore(Infinity)).toThrow('Accuracy score must be finite');
    });

    it('should throw error for non-number values', () => {
      expect(() => new AccuracyScore('0.5' as any)).toThrow('Accuracy score must be a number');
    });
  });

  describe('Factory Methods', () => {
    it('should create from predictions', () => {
      const score = AccuracyScore.fromPredictions(85, 100);
      expect(score.value).toBe(0.85);
    });

    it('should throw error for invalid prediction counts', () => {
      expect(() => AccuracyScore.fromPredictions(10, 0)).toThrow('Total predictions must be greater than zero');
      expect(() => AccuracyScore.fromPredictions(-5, 10)).toThrow('Correct predictions cannot be negative');
      expect(() => AccuracyScore.fromPredictions(15, 10)).toThrow('Correct predictions cannot exceed total predictions');
    });

    it('should create from percentage', () => {
      const score = AccuracyScore.fromPercentage(85);
      expect(score.value).toBe(0.85);
    });

    it('should throw error for invalid percentage', () => {
      expect(() => AccuracyScore.fromPercentage(-10)).toThrow('Percentage must be between 0 and 100');
      expect(() => AccuracyScore.fromPercentage(110)).toThrow('Percentage must be between 0 and 100');
    });

    it('should create predefined accuracy levels', () => {
      expect(AccuracyScore.perfect().value).toBe(1.0);
      expect(AccuracyScore.zero().value).toBe(0.0);
      expect(AccuracyScore.random().value).toBe(0.5);
    });
  });

  describe('Level Classification', () => {
    it('should classify accuracy levels correctly', () => {
      expect(new AccuracyScore(0.3).getLevel()).toBe('poor');
      expect(new AccuracyScore(0.6).getLevel()).toBe('fair');
      expect(new AccuracyScore(0.75).getLevel()).toBe('good');
      expect(new AccuracyScore(0.9).getLevel()).toBe('very_good');
      expect(new AccuracyScore(0.98).getLevel()).toBe('excellent');
    });

    it('should check level predicates', () => {
      const poorScore = new AccuracyScore(0.3);
      const fairScore = new AccuracyScore(0.6);
      const goodScore = new AccuracyScore(0.85);
      const excellentScore = new AccuracyScore(0.98);

      expect(poorScore.isPoor()).toBe(true);
      expect(fairScore.isFair()).toBe(true);
      expect(goodScore.isGood()).toBe(true);
      expect(excellentScore.isExcellent()).toBe(true);

      expect(excellentScore.isPoor()).toBe(false);
      expect(poorScore.isGood()).toBe(false);
    });

    it('should check threshold compliance', () => {
      const score = new AccuracyScore(0.75);
      expect(score.meetsThreshold(0.7)).toBe(true);
      expect(score.meetsThreshold(0.8)).toBe(false);
    });
  });

  describe('Conversions and Metrics', () => {
    it('should convert to percentage', () => {
      const score = new AccuracyScore(0.85);
      expect(score.toPercentage()).toBe(85);
    });

    it('should convert to percentage string', () => {
      const score = new AccuracyScore(0.8567);
      expect(score.toPercentageString()).toBe('85.7%');
      expect(score.toPercentageString(2)).toBe('85.67%');
    });

    it('should calculate error rate', () => {
      const score = new AccuracyScore(0.85);
      expect(score.getErrorRate()).toBe(0.15);
      expect(score.getErrorRatePercentage()).toBe(15);
    });

    it('should convert to string', () => {
      const score = new AccuracyScore(0.85);
      expect(score.toString()).toBe('85.0% (very_good)');
    });
  });

  describe('Comparison Operations', () => {
    it('should calculate improvement over baseline', () => {
      const current = new AccuracyScore(0.85);
      const baseline = new AccuracyScore(0.75);
      
      expect(current.improvementOver(baseline)).toBe(0.1);
    });

    it('should calculate relative improvement', () => {
      const current = new AccuracyScore(0.9);
      const baseline = new AccuracyScore(0.75);
      
      expect(current.relativeImprovementOver(baseline)).toBeCloseTo(0.2); // (0.9 - 0.75) / 0.75
    });

    it('should handle zero baseline in relative improvement', () => {
      const current = new AccuracyScore(0.5);
      const baseline = new AccuracyScore(0.0);
      
      expect(current.relativeImprovementOver(baseline)).toBe(Infinity);
    });

    it('should handle zero current and baseline in relative improvement', () => {
      const current = new AccuracyScore(0.0);
      const baseline = new AccuracyScore(0.0);
      
      expect(current.relativeImprovementOver(baseline)).toBe(0);
    });

    it('should check significant improvement', () => {
      const current = new AccuracyScore(0.85);
      const baseline = new AccuracyScore(0.75);
      
      expect(current.isSignificantlyBetterThan(baseline, 0.05)).toBe(true);
      expect(current.isSignificantlyBetterThan(baseline, 0.15)).toBe(false);
    });

    it('should compare accuracy scores', () => {
      const score1 = new AccuracyScore(0.85);
      const score2 = new AccuracyScore(0.75);
      
      expect(score1.isGreaterThan(score2)).toBe(true);
      expect(score2.isLessThan(score1)).toBe(true);
      expect(score1.isLessThan(score2)).toBe(false);
      expect(score2.isGreaterThan(score1)).toBe(false);
    });

    it('should calculate difference', () => {
      const score1 = new AccuracyScore(0.85);
      const score2 = new AccuracyScore(0.75);
      
      expect(score1.differenceFrom(score2)).toBe(0.1);
      expect(score2.differenceFrom(score1)).toBe(0.1);
    });
  });

  describe('Mathematical Operations', () => {
    it('should combine with another accuracy score', () => {
      const score1 = new AccuracyScore(0.8);
      const score2 = new AccuracyScore(0.6);
      const combined = score1.combineWith(score2, 0.7);
      
      expect(combined.value).toBeCloseTo(0.74); // 0.8 * 0.7 + 0.6 * 0.3
    });

    it('should throw error for invalid weight in combine', () => {
      const score1 = new AccuracyScore(0.8);
      const score2 = new AccuracyScore(0.6);
      
      expect(() => score1.combineWith(score2, -0.1)).toThrow('Weight must be between 0 and 1');
      expect(() => score1.combineWith(score2, 1.1)).toThrow('Weight must be between 0 and 1');
    });

    it('should round accuracy scores', () => {
      const score = new AccuracyScore(0.8567);
      expect(score.round().value).toBe(0.857);
      expect(score.round(2).value).toBe(0.86);
    });
  });

  describe('Static Utility Methods', () => {
    it('should calculate average', () => {
      const scores = [
        new AccuracyScore(0.8),
        new AccuracyScore(0.6),
        new AccuracyScore(0.9),
      ];
      
      const average = AccuracyScore.average(scores);
      expect(average.value).toBeCloseTo(0.7667, 3);
    });

    it('should throw error for empty array in average', () => {
      expect(() => AccuracyScore.average([])).toThrow('Cannot calculate average of empty array');
    });

    it('should calculate weighted average', () => {
      const scores = [
        new AccuracyScore(0.8),
        new AccuracyScore(0.6),
      ];
      const weights = [0.7, 0.3];
      
      const weightedAvg = AccuracyScore.weightedAverage(scores, weights);
      expect(weightedAvg.value).toBeCloseTo(0.74);
    });

    it('should throw error for mismatched arrays in weighted average', () => {
      const scores = [new AccuracyScore(0.8)];
      const weights = [0.7, 0.3];
      
      expect(() => AccuracyScore.weightedAverage(scores, weights))
        .toThrow('Scores and weights arrays must have the same length');
    });

    it('should throw error for zero total weight', () => {
      const scores = [new AccuracyScore(0.8), new AccuracyScore(0.6)];
      const weights = [0, 0];
      
      expect(() => AccuracyScore.weightedAverage(scores, weights))
        .toThrow('Total weight cannot be zero');
    });

    it('should find maximum and minimum', () => {
      const scores = [
        new AccuracyScore(0.8),
        new AccuracyScore(0.3),
        new AccuracyScore(0.9),
      ];
      
      expect(AccuracyScore.max(scores).value).toBe(0.9);
      expect(AccuracyScore.min(scores).value).toBe(0.3);
    });

    it('should throw error for empty array in max/min', () => {
      expect(() => AccuracyScore.max([])).toThrow('Cannot find maximum of empty array');
      expect(() => AccuracyScore.min([])).toThrow('Cannot find minimum of empty array');
    });
  });

  describe('JSON Serialization', () => {
    it('should serialize to JSON', () => {
      const score = new AccuracyScore(0.85);
      const json = score.toJSON();
      
      expect(json.value).toBe(0.85);
      expect(json.percentage).toBe(85);
      expect(json.level).toBe('very_good');
      expect(json.errorRate).toBe(0.15);
      expect(json.isGood).toBe(true);
      expect(json.isExcellent).toBe(false);
    });

    it('should deserialize from JSON', () => {
      const json = { value: 0.75 };
      const score = AccuracyScore.fromJSON(json);
      
      expect(score.value).toBe(0.75);
    });
  });

  describe('Equality', () => {
    it('should be equal to another accuracy score with same value', () => {
      const score1 = new AccuracyScore(0.85);
      const score2 = new AccuracyScore(0.85);
      
      expect(score1.equals(score2)).toBe(true);
    });

    it('should not be equal to accuracy score with different value', () => {
      const score1 = new AccuracyScore(0.85);
      const score2 = new AccuracyScore(0.75);
      
      expect(score1.equals(score2)).toBe(false);
    });
  });
});