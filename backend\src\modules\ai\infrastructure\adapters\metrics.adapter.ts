import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * Metrics Adapter
 * 
 * Provides abstraction layer for metrics collection and reporting.
 * Supports multiple metrics backends and provides standardized
 * metrics recording interface for AI operations.
 */
@Injectable()
export class MetricsAdapter {
  private readonly logger = new Logger(MetricsAdapter.name);
  private readonly metrics = new Map<string, MetricEntry[]>();
  private readonly counters = new Map<string, number>();
  private readonly gauges = new Map<string, number>();

  constructor(private readonly configService: ConfigService) {}

  /**
   * Records an AI operation metric
   */
  recordAiOperation(
    type: string,
    duration: number,
    requestId: string,
    metadata?: Record<string, any>
  ): void {
    const metric: MetricEntry = {
      name: 'ai_operation',
      type: 'histogram',
      value: duration,
      labels: {
        operation_type: type,
        request_id: requestId,
        ...metadata,
      },
      timestamp: new Date(),
    };

    this.recordMetric(metric);
    this.logger.debug(`Recorded AI operation metric: ${type}, duration: ${duration}ms`);
  }

  /**
   * Records model performance metrics
   */
  recordModelPerformance(
    modelId: string,
    metrics: ModelPerformanceMetrics
  ): void {
    // Record accuracy
    this.recordMetric({
      name: 'model_accuracy',
      type: 'gauge',
      value: metrics.accuracy,
      labels: { model_id: modelId },
      timestamp: new Date(),
    });

    // Record latency
    this.recordMetric({
      name: 'model_latency',
      type: 'histogram',
      value: metrics.latency,
      labels: { model_id: modelId },
      timestamp: new Date(),
    });

    // Record throughput
    this.recordMetric({
      name: 'model_throughput',
      type: 'gauge',
      value: metrics.throughput,
      labels: { model_id: modelId },
      timestamp: new Date(),
    });

    // Record cost
    if (metrics.cost !== undefined) {
      this.recordMetric({
        name: 'model_cost',
        type: 'counter',
        value: metrics.cost,
        labels: { model_id: modelId },
        timestamp: new Date(),
      });
    }

    this.logger.debug(`Recorded model performance metrics for: ${modelId}`);
  }

  /**
   * Records provider health metrics
   */
  recordProviderHealth(
    providerId: string,
    health: ProviderHealthMetrics
  ): void {
    // Record availability
    this.recordMetric({
      name: 'provider_availability',
      type: 'gauge',
      value: health.available ? 1 : 0,
      labels: { provider_id: providerId },
      timestamp: new Date(),
    });

    // Record response time
    this.recordMetric({
      name: 'provider_response_time',
      type: 'histogram',
      value: health.responseTime,
      labels: { provider_id: providerId },
      timestamp: new Date(),
    });

    // Record error rate
    this.recordMetric({
      name: 'provider_error_rate',
      type: 'gauge',
      value: health.errorRate,
      labels: { provider_id: providerId },
      timestamp: new Date(),
    });

    this.logger.debug(`Recorded provider health metrics for: ${providerId}`);
  }

  /**
   * Records cache performance metrics
   */
  recordCacheMetrics(
    cacheType: string,
    operation: 'hit' | 'miss' | 'set' | 'delete',
    duration?: number
  ): void {
    // Record cache operation counter
    this.incrementCounter(`cache_operations_total`, {
      cache_type: cacheType,
      operation,
    });

    // Record cache operation duration if provided
    if (duration !== undefined) {
      this.recordMetric({
        name: 'cache_operation_duration',
        type: 'histogram',
        value: duration,
        labels: {
          cache_type: cacheType,
          operation,
        },
        timestamp: new Date(),
      });
    }

    this.logger.debug(`Recorded cache metric: ${cacheType} ${operation}`);
  }

  /**
   * Records pipeline execution metrics
   */
  recordPipelineExecution(
    pipelineId: string,
    status: 'started' | 'completed' | 'failed',
    duration?: number,
    stageCount?: number
  ): void {
    // Record pipeline status counter
    this.incrementCounter('pipeline_executions_total', {
      pipeline_id: pipelineId,
      status,
    });

    // Record pipeline duration if completed
    if (duration !== undefined) {
      this.recordMetric({
        name: 'pipeline_duration',
        type: 'histogram',
        value: duration,
        labels: { pipeline_id: pipelineId },
        timestamp: new Date(),
      });
    }

    // Record stage count if provided
    if (stageCount !== undefined) {
      this.recordMetric({
        name: 'pipeline_stages',
        type: 'gauge',
        value: stageCount,
        labels: { pipeline_id: pipelineId },
        timestamp: new Date(),
      });
    }

    this.logger.debug(`Recorded pipeline execution metric: ${pipelineId} ${status}`);
  }

  /**
   * Records error metrics
   */
  recordError(
    errorType: string,
    component: string,
    severity: 'low' | 'medium' | 'high' = 'medium',
    metadata?: Record<string, any>
  ): void {
    this.incrementCounter('errors_total', {
      error_type: errorType,
      component,
      severity,
      ...metadata,
    });

    this.logger.debug(`Recorded error metric: ${errorType} in ${component}`);
  }

  /**
   * Records custom metric
   */
  recordCustomMetric(
    name: string,
    value: number,
    type: 'counter' | 'gauge' | 'histogram' = 'gauge',
    labels?: Record<string, string>
  ): void {
    this.recordMetric({
      name,
      type,
      value,
      labels: labels || {},
      timestamp: new Date(),
    });

    this.logger.debug(`Recorded custom metric: ${name} = ${value}`);
  }

  /**
   * Gets current metric values
   */
  getMetrics(): MetricsSnapshot {
    const snapshot: MetricsSnapshot = {
      counters: Object.fromEntries(this.counters),
      gauges: Object.fromEntries(this.gauges),
      histograms: {},
      timestamp: new Date(),
    };

    // Calculate histogram statistics
    for (const [name, entries] of this.metrics) {
      if (entries.length > 0 && entries[0].type === 'histogram') {
        const values = entries.map(e => e.value).sort((a, b) => a - b);
        snapshot.histograms[name] = {
          count: values.length,
          sum: values.reduce((sum, v) => sum + v, 0),
          min: values[0],
          max: values[values.length - 1],
          avg: values.reduce((sum, v) => sum + v, 0) / values.length,
          p50: this.percentile(values, 0.5),
          p95: this.percentile(values, 0.95),
          p99: this.percentile(values, 0.99),
        };
      }
    }

    return snapshot;
  }

  /**
   * Gets metrics for a specific time range
   */
  getMetricsInRange(startTime: Date, endTime: Date): MetricEntry[] {
    const result: MetricEntry[] = [];

    for (const entries of this.metrics.values()) {
      const filtered = entries.filter(
        entry => entry.timestamp >= startTime && entry.timestamp <= endTime
      );
      result.push(...filtered);
    }

    return result.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }

  /**
   * Clears old metrics to prevent memory leaks
   */
  cleanup(olderThan: Date = new Date(Date.now() - 24 * 60 * 60 * 1000)): void {
    let totalRemoved = 0;

    for (const [name, entries] of this.metrics) {
      const originalLength = entries.length;
      const filtered = entries.filter(entry => entry.timestamp > olderThan);
      
      if (filtered.length !== originalLength) {
        this.metrics.set(name, filtered);
        totalRemoved += originalLength - filtered.length;
      }
    }

    if (totalRemoved > 0) {
      this.logger.debug(`Cleaned up ${totalRemoved} old metric entries`);
    }
  }

  /**
   * Exports metrics in Prometheus format
   */
  exportPrometheusMetrics(): string {
    const lines: string[] = [];
    const snapshot = this.getMetrics();

    // Export counters
    for (const [name, value] of Object.entries(snapshot.counters)) {
      lines.push(`# TYPE ${name} counter`);
      lines.push(`${name} ${value}`);
    }

    // Export gauges
    for (const [name, value] of Object.entries(snapshot.gauges)) {
      lines.push(`# TYPE ${name} gauge`);
      lines.push(`${name} ${value}`);
    }

    // Export histograms
    for (const [name, stats] of Object.entries(snapshot.histograms)) {
      lines.push(`# TYPE ${name} histogram`);
      lines.push(`${name}_count ${stats.count}`);
      lines.push(`${name}_sum ${stats.sum}`);
      lines.push(`${name}_bucket{le="0.1"} ${this.getBucketCount(name, 0.1)}`);
      lines.push(`${name}_bucket{le="0.5"} ${this.getBucketCount(name, 0.5)}`);
      lines.push(`${name}_bucket{le="1.0"} ${this.getBucketCount(name, 1.0)}`);
      lines.push(`${name}_bucket{le="5.0"} ${this.getBucketCount(name, 5.0)}`);
      lines.push(`${name}_bucket{le="+Inf"} ${stats.count}`);
    }

    return lines.join('\n');
  }

  // Private helper methods

  private recordMetric(metric: MetricEntry): void {
    if (!this.metrics.has(metric.name)) {
      this.metrics.set(metric.name, []);
    }

    const entries = this.metrics.get(metric.name)!;
    entries.push(metric);

    // Keep only recent entries to prevent memory leaks
    if (entries.length > 10000) {
      entries.splice(0, entries.length - 10000);
    }

    // Update counters and gauges
    if (metric.type === 'counter') {
      const key = this.getMetricKey(metric.name, metric.labels);
      this.counters.set(key, (this.counters.get(key) || 0) + metric.value);
    } else if (metric.type === 'gauge') {
      const key = this.getMetricKey(metric.name, metric.labels);
      this.gauges.set(key, metric.value);
    }
  }

  private incrementCounter(name: string, labels: Record<string, string> = {}): void {
    const key = this.getMetricKey(name, labels);
    this.counters.set(key, (this.counters.get(key) || 0) + 1);
  }

  private getMetricKey(name: string, labels: Record<string, string>): string {
    const labelPairs = Object.entries(labels)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([key, value]) => `${key}="${value}"`)
      .join(',');
    
    return labelPairs ? `${name}{${labelPairs}}` : name;
  }

  private percentile(sortedValues: number[], p: number): number {
    if (sortedValues.length === 0) return 0;
    
    const index = Math.ceil(sortedValues.length * p) - 1;
    return sortedValues[Math.max(0, index)];
  }

  private getBucketCount(metricName: string, threshold: number): number {
    const entries = this.metrics.get(metricName) || [];
    return entries.filter(entry => entry.value <= threshold).length;
  }
}

// Type definitions
interface MetricEntry {
  name: string;
  type: 'counter' | 'gauge' | 'histogram';
  value: number;
  labels: Record<string, string>;
  timestamp: Date;
}

interface ModelPerformanceMetrics {
  accuracy: number;
  latency: number;
  throughput: number;
  cost?: number;
}

interface ProviderHealthMetrics {
  available: boolean;
  responseTime: number;
  errorRate: number;
}

interface MetricsSnapshot {
  counters: Record<string, number>;
  gauges: Record<string, number>;
  histograms: Record<string, HistogramStats>;
  timestamp: Date;
}

interface HistogramStats {
  count: number;
  sum: number;
  min: number;
  max: number;
  avg: number;
  p50: number;
  p95: number;
  p99: number;
}