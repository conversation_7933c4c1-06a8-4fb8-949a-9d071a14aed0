import {
  PipeTransform,
  Injectable,
  ArgumentMetadata,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { validate, ValidationError } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { ConfigService } from '@nestjs/config';

/**
 * Global validation pipe that validates and transforms incoming data
 * Provides comprehensive validation with detailed error messages
 */
@Injectable()
export class GlobalValidationPipe implements PipeTransform<any> {
  protected readonly logger = new Logger(GlobalValidationPipe.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * Transform and validate incoming data
   * @param value Input value to validate
   * @param metadata Argument metadata
   * @returns Transformed and validated value
   */
  async transform(value: any, { metatype }: ArgumentMetadata): Promise<any> {
    // Skip validation for primitive types and undefined metatypes
    if (!metatype || !this.toValidate(metatype)) {
      return value;
    }

    // Transform plain object to class instance
    const object = plainToClass(metatype, value);

    // Validate the object
    const errors = await validate(object, {
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      skipMissingProperties: false,
      validationError: {
        target: false,
        value: false,
      },
    });

    if (errors.length > 0) {
      this.logger.warn('Validation failed', {
        errors: this.formatErrors(errors),
        inputData: this.sanitizeInput(value),
      });

      throw new BadRequestException({
        message: 'Validation failed',
        errors: this.formatErrors(errors),
        statusCode: 400,
      });
    }

    return object;
  }

  /**
   * Check if the metatype should be validated
   * @param metatype Type to check
   * @returns boolean indicating if validation is needed
   */
  private toValidate(metatype: Function): boolean {
    const types: Function[] = [String, Boolean, Number, Array, Object];
    return !types.includes(metatype);
  }

  /**
   * Format validation errors into a structured format
   * @param errors Array of validation errors
   * @returns Formatted error object
   */
  private formatErrors(errors: ValidationError[]): any[] {
    return errors.map(error => this.mapChildrenToValidationErrors(error)).flat();
  }

  /**
   * Map validation errors including nested errors
   * @param error Validation error
   * @param parentPath Parent property path
   * @returns Array of formatted errors
   */
  private mapChildrenToValidationErrors(
    error: ValidationError,
    parentPath?: string,
  ): any[] {
    const currentPath = parentPath ? `${parentPath}.${error.property}` : error.property;

    if (!(error.children && error.children.length)) {
      return [
        {
          field: currentPath,
          value: error.value,
          constraints: error.constraints,
          messages: Object.values(error.constraints || {}),
        },
      ];
    }

    const validationErrors = [];

    // Add current level errors if they exist
    if (error.constraints) {
      validationErrors.push({
        field: currentPath,
        value: error.value,
        constraints: error.constraints,
        messages: Object.values(error.constraints),
      });
    }

    // Add nested errors
    for (const child of error.children) {
      validationErrors.push(
        ...this.mapChildrenToValidationErrors(child, currentPath),
      );
    }

    return validationErrors;
  }

  /**
   * Sanitize input data for logging (remove sensitive information)
   * @param input Input data to sanitize
   * @returns Sanitized input data
   */
  private sanitizeInput(input: any): any {
    if (!input || typeof input !== 'object') {
      return input;
    }

    const sensitiveFields = [
      'password',
      'token',
      'secret',
      'key',
      'authorization',
      'credential',
      'apiKey',
      'accessToken',
      'refreshToken',
    ];

    const sanitized = { ...input };

    sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    });

    // Recursively sanitize nested objects
    Object.keys(sanitized).forEach(key => {
      if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
        sanitized[key] = this.sanitizeInput(sanitized[key]);
      }
    });

    return sanitized;
  }
}

/**
 * Custom validation pipe for specific use cases
 * Extends the global validation pipe with additional features
 */
@Injectable()
export class CustomValidationPipe extends GlobalValidationPipe {
  private readonly maxDepth: number;
  private readonly maxArraySize: number;

  constructor(configService: ConfigService) {
    super(configService);
    this.maxDepth = 10; // Maximum nesting depth
    this.maxArraySize = 1000; // Maximum array size
  }

  /**
   * Transform with additional security checks
   * @param value Input value
   * @param metadata Argument metadata
   * @returns Transformed value
   */
  async transform(value: any, metadata: ArgumentMetadata): Promise<any> {
    // Check for potential security issues
    this.validateInputSecurity(value);

    // Call parent transform
    return super.transform(value, metadata);
  }

  /**
   * Validate input for security issues
   * @param input Input to validate
   * @param depth Current nesting depth
   */
  private validateInputSecurity(input: any, depth: number = 0): void {
    if (depth > this.maxDepth) {
      throw new BadRequestException('Input nesting too deep');
    }

    if (Array.isArray(input)) {
      if (input.length > this.maxArraySize) {
        throw new BadRequestException('Array size too large');
      }

      input.forEach(item => this.validateInputSecurity(item, depth + 1));
    } else if (typeof input === 'object' && input !== null) {
      Object.values(input).forEach(value =>
        this.validateInputSecurity(value, depth + 1),
      );
    } else if (typeof input === 'string') {
      // Check for potential injection attacks
      this.validateStringInput(input);
    }
  }

  /**
   * Validate string input for potential security issues
   * @param input String input to validate
   */
  private validateStringInput(input: string): void {
    // Check for SQL injection patterns
    const sqlInjectionPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
      /(--|\/\*|\*\/)/,
      /(\b(OR|AND)\b.*=.*)/i,
    ];

    // Check for XSS patterns
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/i,
      /on\w+\s*=/i,
    ];

    // Check for path traversal
    const pathTraversalPatterns = [
      /\.\.\//,
      /\.\.\\/,
      /%2e%2e%2f/i,
      /%2e%2e%5c/i,
    ];

    const allPatterns = [
      ...sqlInjectionPatterns,
      ...xssPatterns,
      ...pathTraversalPatterns,
    ];

    for (const pattern of allPatterns) {
      if (pattern.test(input)) {
        this.logger.warn('Potential security threat detected in input', {
          pattern: pattern.toString(),
          inputLength: input.length,
        });
        throw new BadRequestException('Invalid input detected');
      }
    }
  }
}
