import { ConfidenceScore } from '../confidence-score.value-object';

describe('ConfidenceScore Value Object', () => {
  describe('Construction', () => {
    it('should create a valid confidence score', () => {
      const score = new ConfidenceScore(0.85);
      expect(score.value).toBe(0.85);
    });

    it('should accept minimum value', () => {
      const score = new ConfidenceScore(0.0);
      expect(score.value).toBe(0.0);
    });

    it('should accept maximum value', () => {
      const score = new ConfidenceScore(1.0);
      expect(score.value).toBe(1.0);
    });

    it('should throw error for negative values', () => {
      expect(() => new ConfidenceScore(-0.1)).toThrow('Confidence score cannot be less than 0');
    });

    it('should throw error for values greater than 1', () => {
      expect(() => new ConfidenceScore(1.1)).toThrow('Confidence score cannot be greater than 1');
    });

    it('should throw error for NaN', () => {
      expect(() => new ConfidenceScore(NaN)).toThrow('Confidence score cannot be NaN');
    });

    it('should throw error for infinite values', () => {
      expect(() => new ConfidenceScore(Infinity)).toThrow('Confidence score must be finite');
      expect(() => new ConfidenceScore(-Infinity)).toThrow('Confidence score must be finite');
    });

    it('should throw error for non-number values', () => {
      expect(() => new ConfidenceScore('0.5' as any)).toThrow('Confidence score must be a number');
    });
  });

  describe('Factory Methods', () => {
    it('should create from percentage', () => {
      const score = ConfidenceScore.fromPercentage(85);
      expect(score.value).toBe(0.85);
    });

    it('should throw error for invalid percentage', () => {
      expect(() => ConfidenceScore.fromPercentage(-10)).toThrow('Percentage must be between 0 and 100');
      expect(() => ConfidenceScore.fromPercentage(110)).toThrow('Percentage must be between 0 and 100');
    });

    it('should create from fraction', () => {
      const score = ConfidenceScore.fromFraction(17, 20);
      expect(score.value).toBe(0.85);
    });

    it('should throw error for zero denominator', () => {
      expect(() => ConfidenceScore.fromFraction(1, 0)).toThrow('Denominator cannot be zero');
    });

    it('should throw error for negative values in fraction', () => {
      expect(() => ConfidenceScore.fromFraction(-1, 2)).toThrow('Numerator and denominator must be non-negative');
      expect(() => ConfidenceScore.fromFraction(1, -2)).toThrow('Numerator and denominator must be non-negative');
    });

    it('should create predefined confidence levels', () => {
      expect(ConfidenceScore.low().value).toBe(0.3);
      expect(ConfidenceScore.medium().value).toBe(0.7);
      expect(ConfidenceScore.high().value).toBe(0.9);
      expect(ConfidenceScore.maximum().value).toBe(1.0);
      expect(ConfidenceScore.minimum().value).toBe(0.0);
    });
  });

  describe('Level Classification', () => {
    it('should classify confidence levels correctly', () => {
      expect(new ConfidenceScore(0.1).getLevel()).toBe('very_low');
      expect(new ConfidenceScore(0.25).getLevel()).toBe('low');
      expect(new ConfidenceScore(0.5).getLevel()).toBe('medium');
      expect(new ConfidenceScore(0.8).getLevel()).toBe('high');
      expect(new ConfidenceScore(0.95).getLevel()).toBe('very_high');
    });

    it('should check level predicates', () => {
      const lowScore = new ConfidenceScore(0.2);
      const mediumScore = new ConfidenceScore(0.5);
      const highScore = new ConfidenceScore(0.95);

      expect(lowScore.isLow()).toBe(true);
      expect(mediumScore.isMedium()).toBe(true);
      expect(highScore.isHigh()).toBe(true);

      expect(highScore.isLow()).toBe(false);
      expect(lowScore.isHigh()).toBe(false);
    });

    it('should check threshold compliance', () => {
      const score = new ConfidenceScore(0.75);
      expect(score.meetsThreshold(0.7)).toBe(true);
      expect(score.meetsThreshold(0.8)).toBe(false);
    });
  });

  describe('Conversions', () => {
    it('should convert to percentage', () => {
      const score = new ConfidenceScore(0.85);
      expect(score.toPercentage()).toBe(85);
    });

    it('should convert to percentage string', () => {
      const score = new ConfidenceScore(0.8567);
      expect(score.toPercentageString()).toBe('85.7%');
      expect(score.toPercentageString(2)).toBe('85.67%');
    });

    it('should convert to string', () => {
      const score = new ConfidenceScore(0.85);
      expect(score.toString()).toBe('85.0% (high)');
    });
  });

  describe('Mathematical Operations', () => {
    it('should combine with another confidence score', () => {
      const score1 = new ConfidenceScore(0.8);
      const score2 = new ConfidenceScore(0.6);
      const combined = score1.combineWith(score2, 0.7);
      
      expect(combined.value).toBeCloseTo(0.74); // 0.8 * 0.7 + 0.6 * 0.3
    });

    it('should throw error for invalid weight in combine', () => {
      const score1 = new ConfidenceScore(0.8);
      const score2 = new ConfidenceScore(0.6);
      
      expect(() => score1.combineWith(score2, -0.1)).toThrow('Weight must be between 0 and 1');
      expect(() => score1.combineWith(score2, 1.1)).toThrow('Weight must be between 0 and 1');
    });

    it('should calculate inverse', () => {
      const score = new ConfidenceScore(0.3);
      const inverse = score.inverse();
      expect(inverse.value).toBe(0.7);
    });

    it('should multiply by factor', () => {
      const score = new ConfidenceScore(0.5);
      const multiplied = score.multiply(1.5);
      expect(multiplied.value).toBe(0.75);
    });

    it('should cap multiplication at maximum', () => {
      const score = new ConfidenceScore(0.8);
      const multiplied = score.multiply(2);
      expect(multiplied.value).toBe(1.0);
    });

    it('should throw error for negative factor', () => {
      const score = new ConfidenceScore(0.5);
      expect(() => score.multiply(-1)).toThrow('Factor cannot be negative');
    });

    it('should add confidence scores', () => {
      const score1 = new ConfidenceScore(0.3);
      const score2 = new ConfidenceScore(0.4);
      const sum = score1.add(score2);
      expect(sum.value).toBe(0.7);
    });

    it('should cap addition at maximum', () => {
      const score1 = new ConfidenceScore(0.7);
      const score2 = new ConfidenceScore(0.5);
      const sum = score1.add(score2);
      expect(sum.value).toBe(1.0);
    });

    it('should subtract confidence scores', () => {
      const score1 = new ConfidenceScore(0.8);
      const score2 = new ConfidenceScore(0.3);
      const difference = score1.subtract(score2);
      expect(difference.value).toBe(0.5);
    });

    it('should floor subtraction at minimum', () => {
      const score1 = new ConfidenceScore(0.2);
      const score2 = new ConfidenceScore(0.5);
      const difference = score1.subtract(score2);
      expect(difference.value).toBe(0.0);
    });

    it('should calculate difference', () => {
      const score1 = new ConfidenceScore(0.8);
      const score2 = new ConfidenceScore(0.3);
      expect(score1.differenceFrom(score2)).toBe(0.5);
      expect(score2.differenceFrom(score1)).toBe(0.5);
    });

    it('should compare confidence scores', () => {
      const score1 = new ConfidenceScore(0.8);
      const score2 = new ConfidenceScore(0.3);
      
      expect(score1.isGreaterThan(score2)).toBe(true);
      expect(score2.isLessThan(score1)).toBe(true);
      expect(score1.isLessThan(score2)).toBe(false);
      expect(score2.isGreaterThan(score1)).toBe(false);
    });

    it('should round confidence scores', () => {
      const score = new ConfidenceScore(0.8567);
      expect(score.round().value).toBe(0.86);
      expect(score.round(3).value).toBe(0.857);
    });
  });

  describe('Static Utility Methods', () => {
    it('should calculate average', () => {
      const scores = [
        new ConfidenceScore(0.8),
        new ConfidenceScore(0.6),
        new ConfidenceScore(0.9),
      ];
      
      const average = ConfidenceScore.average(scores);
      expect(average.value).toBeCloseTo(0.7667, 3);
    });

    it('should throw error for empty array in average', () => {
      expect(() => ConfidenceScore.average([])).toThrow('Cannot calculate average of empty array');
    });

    it('should calculate weighted average', () => {
      const scores = [
        new ConfidenceScore(0.8),
        new ConfidenceScore(0.6),
      ];
      const weights = [0.7, 0.3];
      
      const weightedAvg = ConfidenceScore.weightedAverage(scores, weights);
      expect(weightedAvg.value).toBeCloseTo(0.74); // (0.8 * 0.7 + 0.6 * 0.3) / 1.0
    });

    it('should throw error for mismatched arrays in weighted average', () => {
      const scores = [new ConfidenceScore(0.8)];
      const weights = [0.7, 0.3];
      
      expect(() => ConfidenceScore.weightedAverage(scores, weights))
        .toThrow('Scores and weights arrays must have the same length');
    });

    it('should find maximum and minimum', () => {
      const scores = [
        new ConfidenceScore(0.8),
        new ConfidenceScore(0.3),
        new ConfidenceScore(0.9),
      ];
      
      expect(ConfidenceScore.max(scores).value).toBe(0.9);
      expect(ConfidenceScore.min(scores).value).toBe(0.3);
    });

    it('should throw error for empty array in max/min', () => {
      expect(() => ConfidenceScore.max([])).toThrow('Cannot find maximum of empty array');
      expect(() => ConfidenceScore.min([])).toThrow('Cannot find minimum of empty array');
    });
  });

  describe('JSON Serialization', () => {
    it('should serialize to JSON', () => {
      const score = new ConfidenceScore(0.85);
      const json = score.toJSON();
      
      expect(json.value).toBe(0.85);
      expect(json.percentage).toBe(85);
      expect(json.level).toBe('high');
      expect(json.isHigh).toBe(true);
    });

    it('should deserialize from JSON', () => {
      const json = { value: 0.75 };
      const score = ConfidenceScore.fromJSON(json);
      
      expect(score.value).toBe(0.75);
    });
  });

  describe('Equality', () => {
    it('should be equal to another confidence score with same value', () => {
      const score1 = new ConfidenceScore(0.85);
      const score2 = new ConfidenceScore(0.85);
      
      expect(score1.equals(score2)).toBe(true);
    });

    it('should not be equal to confidence score with different value', () => {
      const score1 = new ConfidenceScore(0.85);
      const score2 = new ConfidenceScore(0.75);
      
      expect(score1.equals(score2)).toBe(false);
    });

    it('should not be equal to null or undefined', () => {
      const score = new ConfidenceScore(0.85);
      
      expect(score.equals(null as any)).toBe(false);
      expect(score.equals(undefined as any)).toBe(false);
    });
  });
});