e6fabf203b97dbfde92a588f83062c91
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var WorkflowEngineService_1;
var _a, _b, _c, _d, _e, _f, _g, _h;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowEngineService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const config_1 = require("@nestjs/config");
const event_emitter_1 = require("@nestjs/event-emitter");
const workflow_execution_entity_1 = require("../entities/workflow-execution.entity");
const workflow_execution_context_entity_1 = require("../entities/workflow-execution-context.entity");
const workflow_rule_engine_service_1 = require("./workflow-rule-engine.service");
const workflow_step_processor_factory_1 = require("./workflow-step-processor.factory");
const notification_queue_management_service_1 = require("./notification-queue-management.service");
const notification_template_management_service_1 = require("./notification-template-management.service");
/**
 * Workflow Engine Service
 *
 * Core workflow execution engine providing:
 * - Step-by-step workflow execution with state management
 * - Conditional logic evaluation and branching
 * - Parallel and sequential step processing
 * - Error handling and recovery mechanisms
 * - Integration with notification infrastructure
 * - Real-time execution monitoring and tracking
 *
 * Features:
 * - Advanced workflow orchestration with complex logic
 * - State persistence and recovery capabilities
 * - Performance optimization with parallel execution
 * - Comprehensive error handling and retry mechanisms
 * - Integration with external systems and APIs
 * - Real-time monitoring and execution tracking
 */
let WorkflowEngineService = WorkflowEngineService_1 = class WorkflowEngineService {
    constructor(executionRepository, contextRepository, configService, eventEmitter, ruleEngine, stepProcessorFactory, queueManagementService, templateManagementService) {
        this.executionRepository = executionRepository;
        this.contextRepository = contextRepository;
        this.configService = configService;
        this.eventEmitter = eventEmitter;
        this.ruleEngine = ruleEngine;
        this.stepProcessorFactory = stepProcessorFactory;
        this.queueManagementService = queueManagementService;
        this.templateManagementService = templateManagementService;
        this.logger = new common_1.Logger(WorkflowEngineService_1.name);
        this.runningExecutions = new Map();
    }
    /**
     * Execute workflow
     */
    async executeWorkflow(workflow, execution, executeDto) {
        try {
            this.logger.debug(`Starting workflow execution: ${execution.id}`);
            // Store execution in running executions map
            this.runningExecutions.set(execution.id, {
                workflow,
                execution,
                startTime: Date.now(),
                currentStep: null,
                context: executeDto.context || {},
            });
            // Initialize execution context
            const executionContext = {
                executionId: execution.id,
                workflowId: workflow.id,
                input: executeDto.input || {},
                context: executeDto.context || {},
                variables: {},
                stepResults: {},
                currentStep: workflow.definition.startStep,
                status: 'running',
            };
            // Start execution from the start step
            const result = await this.executeStep(workflow, executionContext, workflow.definition.startStep);
            // Update execution with final result
            await this.completeExecution(execution.id, 'completed', result);
            this.logger.debug(`Workflow execution completed: ${execution.id}`);
            return result;
        }
        catch (error) {
            this.logger.error(`Workflow execution failed: ${error.message}`, error.stack);
            await this.completeExecution(execution.id, 'failed', null, error.message);
            throw error;
        }
        finally {
            // Clean up running execution
            this.runningExecutions.delete(execution.id);
        }
    }
    /**
     * Execute individual step
     */
    async executeStep(workflow, context, stepId) {
        try {
            this.logger.debug(`Executing step: ${stepId}`);
            // Find step definition
            const stepDefinition = workflow.definition.steps.find(step => step.id === stepId);
            if (!stepDefinition) {
                throw new Error(`Step not found: ${stepId}`);
            }
            // Create step execution context
            const stepContext = this.contextRepository.create({
                executionId: context.executionId,
                stepId,
                status: 'running',
                input: context.stepResults[stepId] || {},
                startedAt: new Date(),
            });
            await this.contextRepository.save(stepContext);
            // Update current step in execution context
            context.currentStep = stepId;
            // Emit step started event
            this.eventEmitter.emit('workflow.step.started', {
                executionId: context.executionId,
                stepId,
                stepType: stepDefinition.type,
                timestamp: new Date(),
            });
            // Get step processor
            const processor = this.stepProcessorFactory.getProcessor(stepDefinition.type);
            // Execute step
            const stepResult = await processor.execute(stepDefinition, context);
            // Update step context with result
            stepContext.status = 'completed';
            stepContext.completedAt = new Date();
            stepContext.result = stepResult;
            await this.contextRepository.save(stepContext);
            // Store step result in context
            context.stepResults[stepId] = stepResult;
            // Update execution progress
            await this.updateExecutionProgress(context.executionId);
            // Emit step completed event
            this.eventEmitter.emit('workflow.step.completed', {
                executionId: context.executionId,
                stepId,
                result: stepResult,
                timestamp: new Date(),
            });
            // Determine next step(s)
            const nextSteps = await this.determineNextSteps(stepDefinition, stepResult, context);
            // Execute next steps
            if (nextSteps.length === 0) {
                // No more steps, workflow complete
                return context.stepResults;
            }
            else if (nextSteps.length === 1) {
                // Single next step, continue sequentially
                return await this.executeStep(workflow, context, nextSteps[0]);
            }
            else {
                // Multiple next steps, execute in parallel
                return await this.executeParallelSteps(workflow, context, nextSteps);
            }
        }
        catch (error) {
            this.logger.error(`Step execution failed: ${stepId} - ${error.message}`, error.stack);
            // Update step context with error
            const stepContext = await this.contextRepository.findOne({
                where: { executionId: context.executionId, stepId },
            });
            if (stepContext) {
                stepContext.status = 'failed';
                stepContext.completedAt = new Date();
                stepContext.error = error.message;
                await this.contextRepository.save(stepContext);
            }
            // Emit step failed event
            this.eventEmitter.emit('workflow.step.failed', {
                executionId: context.executionId,
                stepId,
                error: error.message,
                timestamp: new Date(),
            });
            // Handle step failure based on error handling configuration
            return await this.handleStepFailure(workflow, context, stepId, error);
        }
    }
    /**
     * Execute parallel steps
     */
    async executeParallelSteps(workflow, context, stepIds) {
        try {
            this.logger.debug(`Executing parallel steps: ${stepIds.join(', ')}`);
            // Execute all steps in parallel
            const stepPromises = stepIds.map(stepId => this.executeStep(workflow, { ...context }, stepId));
            // Wait for all steps to complete
            const results = await Promise.allSettled(stepPromises);
            // Process results
            const successfulResults = [];
            const failedResults = [];
            results.forEach((result, index) => {
                if (result.status === 'fulfilled') {
                    successfulResults.push({ stepId: stepIds[index], result: result.value });
                }
                else {
                    failedResults.push({ stepId: stepIds[index], error: result.reason });
                }
            });
            // Handle failures if any
            if (failedResults.length > 0) {
                const parallelFailureHandling = workflow.definition.errorHandling?.parallelFailureHandling || 'fail_all';
                if (parallelFailureHandling === 'fail_all') {
                    throw new Error(`Parallel step execution failed: ${failedResults.map(f => f.error).join(', ')}`);
                }
                else if (parallelFailureHandling === 'continue_on_partial_success' && successfulResults.length > 0) {
                    // Continue with successful results
                    this.logger.warn(`Some parallel steps failed, continuing with partial success`);
                }
            }
            // Merge results into context
            successfulResults.forEach(({ stepId, result }) => {
                context.stepResults[stepId] = result;
            });
            return context.stepResults;
        }
        catch (error) {
            this.logger.error(`Parallel step execution failed: ${error.message}`, error.stack);
            throw error;
        }
    }
    /**
     * Determine next steps based on current step result and conditions
     */
    async determineNextSteps(stepDefinition, stepResult, context) {
        try {
            // Check if step has conditional next steps
            if (stepDefinition.nextSteps && Array.isArray(stepDefinition.nextSteps)) {
                const nextSteps = [];
                for (const nextStepConfig of stepDefinition.nextSteps) {
                    if (nextStepConfig.condition) {
                        // Evaluate condition using rule engine
                        const conditionResult = await this.ruleEngine.evaluateCondition(nextStepConfig.condition, { stepResult, context });
                        if (conditionResult) {
                            nextSteps.push(nextStepConfig.stepId);
                        }
                    }
                    else {
                        // No condition, always include
                        nextSteps.push(nextStepConfig.stepId);
                    }
                }
                return nextSteps;
            }
            // Check for single next step
            if (stepDefinition.nextStep) {
                return [stepDefinition.nextStep];
            }
            // No next steps defined
            return [];
        }
        catch (error) {
            this.logger.error(`Failed to determine next steps: ${error.message}`, error.stack);
            return [];
        }
    }
    /**
     * Handle step failure
     */
    async handleStepFailure(workflow, context, stepId, error) {
        try {
            const stepDefinition = workflow.definition.steps.find(step => step.id === stepId);
            const errorHandling = stepDefinition?.errorHandling || workflow.definition.errorHandling || {};
            // Check retry configuration
            if (errorHandling.retry) {
                const retryCount = context.retryCount?.[stepId] || 0;
                const maxRetries = errorHandling.retry.maxAttempts || 3;
                if (retryCount < maxRetries) {
                    this.logger.debug(`Retrying step ${stepId}, attempt ${retryCount + 1}/${maxRetries}`);
                    // Update retry count
                    context.retryCount = context.retryCount || {};
                    context.retryCount[stepId] = retryCount + 1;
                    // Wait for retry delay
                    if (errorHandling.retry.delay) {
                        await this.delay(errorHandling.retry.delay);
                    }
                    // Retry step execution
                    return await this.executeStep(workflow, context, stepId);
                }
            }
            // Check fallback step
            if (errorHandling.fallbackStep) {
                this.logger.debug(`Executing fallback step: ${errorHandling.fallbackStep}`);
                return await this.executeStep(workflow, context, errorHandling.fallbackStep);
            }
            // Check if workflow should continue on error
            if (errorHandling.continueOnError) {
                this.logger.warn(`Continuing workflow despite step failure: ${stepId}`);
                return context.stepResults;
            }
            // Default: fail the entire workflow
            throw error;
        }
        catch (error) {
            this.logger.error(`Error handling failed: ${error.message}`, error.stack);
            throw error;
        }
    }
    /**
     * Cancel execution
     */
    async cancelExecution(executionId) {
        try {
            this.logger.debug(`Cancelling execution: ${executionId}`);
            const runningExecution = this.runningExecutions.get(executionId);
            if (runningExecution) {
                // Mark execution as cancelled
                runningExecution.cancelled = true;
                // Cancel any running steps
                await this.cancelRunningSteps(executionId);
                // Remove from running executions
                this.runningExecutions.delete(executionId);
            }
            this.logger.debug(`Execution cancelled: ${executionId}`);
        }
        catch (error) {
            this.logger.error(`Failed to cancel execution: ${error.message}`, error.stack);
            throw error;
        }
    }
    /**
     * Complete execution
     */
    async completeExecution(executionId, status, result, error) {
        try {
            const execution = await this.executionRepository.findOne({ where: { id: executionId } });
            if (!execution) {
                return;
            }
            execution.status = status;
            execution.completedAt = new Date();
            execution.duration = execution.completedAt.getTime() - execution.startedAt.getTime();
            execution.result = result;
            execution.error = error;
            // Update steps completed count
            const completedSteps = await this.contextRepository.count({
                where: { executionId, status: 'completed' },
            });
            execution.stepsCompleted = completedSteps;
            await this.executionRepository.save(execution);
            // Emit execution completed event
            this.eventEmitter.emit('workflow.execution.completed', {
                executionId,
                status,
                duration: execution.duration,
                result,
                error,
                timestamp: new Date(),
            });
        }
        catch (error) {
            this.logger.error(`Failed to complete execution: ${error.message}`, error.stack);
        }
    }
    /**
     * Update execution progress
     */
    async updateExecutionProgress(executionId) {
        try {
            const execution = await this.executionRepository.findOne({ where: { id: executionId } });
            if (!execution) {
                return;
            }
            const completedSteps = await this.contextRepository.count({
                where: { executionId, status: 'completed' },
            });
            execution.stepsCompleted = completedSteps;
            await this.executionRepository.save(execution);
        }
        catch (error) {
            this.logger.error(`Failed to update execution progress: ${error.message}`, error.stack);
        }
    }
    /**
     * Cancel running steps
     */
    async cancelRunningSteps(executionId) {
        try {
            const runningSteps = await this.contextRepository.find({
                where: { executionId, status: 'running' },
            });
            for (const step of runningSteps) {
                step.status = 'cancelled';
                step.completedAt = new Date();
                await this.contextRepository.save(step);
            }
        }
        catch (error) {
            this.logger.error(`Failed to cancel running steps: ${error.message}`, error.stack);
        }
    }
    /**
     * Delay utility
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    /**
     * Get running execution info
     */
    getRunningExecutionInfo(executionId) {
        return this.runningExecutions.get(executionId);
    }
    /**
     * Get all running executions
     */
    getAllRunningExecutions() {
        return new Map(this.runningExecutions);
    }
};
exports.WorkflowEngineService = WorkflowEngineService;
exports.WorkflowEngineService = WorkflowEngineService = WorkflowEngineService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(workflow_execution_entity_1.WorkflowExecution)),
    __param(1, (0, typeorm_1.InjectRepository)(workflow_execution_context_entity_1.WorkflowExecutionContext)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _c : Object, typeof (_d = typeof event_emitter_1.EventEmitter2 !== "undefined" && event_emitter_1.EventEmitter2) === "function" ? _d : Object, typeof (_e = typeof workflow_rule_engine_service_1.WorkflowRuleEngine !== "undefined" && workflow_rule_engine_service_1.WorkflowRuleEngine) === "function" ? _e : Object, typeof (_f = typeof workflow_step_processor_factory_1.WorkflowStepProcessorFactory !== "undefined" && workflow_step_processor_factory_1.WorkflowStepProcessorFactory) === "function" ? _f : Object, typeof (_g = typeof notification_queue_management_service_1.NotificationQueueManagementService !== "undefined" && notification_queue_management_service_1.NotificationQueueManagementService) === "function" ? _g : Object, typeof (_h = typeof notification_template_management_service_1.NotificationTemplateManagementService !== "undefined" && notification_template_management_service_1.NotificationTemplateManagementService) === "function" ? _h : Object])
], WorkflowEngineService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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