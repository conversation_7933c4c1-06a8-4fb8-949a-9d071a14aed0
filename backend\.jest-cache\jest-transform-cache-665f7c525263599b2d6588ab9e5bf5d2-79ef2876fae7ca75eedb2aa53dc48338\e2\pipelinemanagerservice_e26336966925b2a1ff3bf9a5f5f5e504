6eae11ccbfeae71b59d291fa4997ef74
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PipelineManagerService_1;
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PipelineManagerService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const cqrs_1 = require("@nestjs/cqrs");
const bull_1 = require("bull");
const bull_2 = require("@nestjs/bull");
/**
 * Pipeline Manager Service
 *
 * Manages AI processing pipelines with support for complex workflows,
 * parallel processing, conditional logic, and error recovery.
 * Orchestrates multi-stage AI operations with monitoring and optimization.
 */
let PipelineManagerService = PipelineManagerService_1 = class PipelineManagerService {
    constructor(aiRequestQueue, aiResponseQueue, trainingQueue, eventBus, configService) {
        this.aiRequestQueue = aiRequestQueue;
        this.aiResponseQueue = aiResponseQueue;
        this.trainingQueue = trainingQueue;
        this.eventBus = eventBus;
        this.configService = configService;
        this.logger = new common_1.Logger(PipelineManagerService_1.name);
        this.activePipelines = new Map();
        this.pipelineTemplates = new Map();
        this.initializePipelineTemplates();
    }
    /**
     * Creates and executes a new AI processing pipeline
     */
    async createPipeline(definition) {
        const pipelineId = this.generatePipelineId();
        this.logger.log(`Creating AI pipeline: ${pipelineId}`);
        try {
            // Validate pipeline definition
            await this.validatePipelineDefinition(definition);
            // Create pipeline execution context
            const execution = {
                id: pipelineId,
                definition,
                status: 'initializing',
                stages: this.initializeStages(definition.stages),
                context: definition.context || {},
                metrics: {
                    startTime: new Date(),
                    totalStages: definition.stages.length,
                    completedStages: 0,
                    failedStages: 0,
                },
                results: {},
            };
            this.activePipelines.set(pipelineId, execution);
            // Start pipeline execution
            await this.executePipeline(execution);
            return execution;
        }
        catch (error) {
            this.logger.error(`Failed to create pipeline: ${pipelineId}`, error);
            throw new PipelineError(`Pipeline creation failed: ${error.message}`, pipelineId);
        }
    }
    /**
     * Executes a pipeline from a predefined template
     */
    async executePipelineFromTemplate(templateName, parameters) {
        const template = this.pipelineTemplates.get(templateName);
        if (!template) {
            throw new PipelineError(`Pipeline template not found: ${templateName}`);
        }
        const definition = this.instantiateTemplate(template, parameters);
        return this.createPipeline(definition);
    }
    /**
     * Gets pipeline execution status and progress
     */
    async getPipelineStatus(pipelineId) {
        const execution = this.activePipelines.get(pipelineId);
        if (!execution) {
            throw new PipelineError(`Pipeline not found: ${pipelineId}`);
        }
        return {
            id: pipelineId,
            status: execution.status,
            progress: this.calculateProgress(execution),
            currentStage: this.getCurrentStage(execution),
            metrics: execution.metrics,
            results: execution.results,
            errors: execution.errors || [],
        };
    }
    /**
     * Cancels a running pipeline
     */
    async cancelPipeline(pipelineId) {
        const execution = this.activePipelines.get(pipelineId);
        if (!execution) {
            throw new PipelineError(`Pipeline not found: ${pipelineId}`);
        }
        this.logger.log(`Cancelling pipeline: ${pipelineId}`);
        try {
            execution.status = 'cancelling';
            // Cancel running stages
            await this.cancelRunningStages(execution);
            // Clean up resources
            await this.cleanupPipelineResources(execution);
            execution.status = 'cancelled';
            execution.metrics.endTime = new Date();
            this.logger.log(`Pipeline cancelled: ${pipelineId}`);
        }
        catch (error) {
            this.logger.error(`Failed to cancel pipeline: ${pipelineId}`, error);
            execution.status = 'error';
            throw new PipelineError(`Pipeline cancellation failed: ${error.message}`, pipelineId);
        }
    }
    /**
     * Retries a failed pipeline stage
     */
    async retryStage(pipelineId, stageId) {
        const execution = this.activePipelines.get(pipelineId);
        if (!execution) {
            throw new PipelineError(`Pipeline not found: ${pipelineId}`);
        }
        const stage = execution.stages.find(s => s.id === stageId);
        if (!stage) {
            throw new PipelineError(`Stage not found: ${stageId}`);
        }
        if (stage.status !== 'failed') {
            throw new PipelineError(`Stage is not in failed state: ${stageId}`);
        }
        this.logger.log(`Retrying pipeline stage: ${pipelineId}/${stageId}`);
        try {
            // Reset stage state
            stage.status = 'pending';
            stage.error = undefined;
            stage.retryCount = (stage.retryCount || 0) + 1;
            // Execute the stage
            await this.executeStage(execution, stage);
        }
        catch (error) {
            this.logger.error(`Stage retry failed: ${pipelineId}/${stageId}`, error);
            throw new PipelineError(`Stage retry failed: ${error.message}`, pipelineId);
        }
    }
    /**
     * Gets all active pipelines
     */
    async getActivePipelines() {
        const statuses = [];
        for (const [pipelineId] of this.activePipelines) {
            try {
                const status = await this.getPipelineStatus(pipelineId);
                statuses.push(status);
            }
            catch (error) {
                this.logger.warn(`Failed to get status for pipeline: ${pipelineId}`, error);
            }
        }
        return statuses;
    }
    /**
     * Registers a new pipeline template
     */
    registerPipelineTemplate(name, template) {
        this.pipelineTemplates.set(name, template);
        this.logger.log(`Registered pipeline template: ${name}`);
    }
    // Private helper methods
    async executePipeline(execution) {
        try {
            execution.status = 'running';
            execution.metrics.startTime = new Date();
            // Execute stages based on execution strategy
            if (execution.definition.executionStrategy === 'parallel') {
                await this.executeStagesInParallel(execution);
            }
            else {
                await this.executeStagesSequentially(execution);
            }
            execution.status = 'completed';
            execution.metrics.endTime = new Date();
            this.logger.log(`Pipeline completed: ${execution.id}`);
        }
        catch (error) {
            execution.status = 'failed';
            execution.metrics.endTime = new Date();
            execution.errors = execution.errors || [];
            execution.errors.push({
                message: error.message,
                timestamp: new Date(),
                stage: 'pipeline',
            });
            this.logger.error(`Pipeline failed: ${execution.id}`, error);
            throw error;
        }
        finally {
            // Emit pipeline completion event
            await this.emitPipelineEvent(execution);
        }
    }
    async executeStagesSequentially(execution) {
        for (const stage of execution.stages) {
            if (execution.status === 'cancelling') {
                break;
            }
            // Check stage dependencies
            if (!this.areDependenciesMet(stage, execution)) {
                stage.status = 'skipped';
                continue;
            }
            await this.executeStage(execution, stage);
            if (stage.status === 'failed' && !stage.continueOnFailure) {
                throw new Error(`Stage failed: ${stage.id}`);
            }
        }
    }
    async executeStagesInParallel(execution) {
        const stagePromises = execution.stages.map(stage => this.executeStageWithDependencies(execution, stage));
        await Promise.allSettled(stagePromises);
        // Check if any critical stages failed
        const failedCriticalStages = execution.stages.filter(stage => stage.status === 'failed' && !stage.continueOnFailure);
        if (failedCriticalStages.length > 0) {
            throw new Error(`Critical stages failed: ${failedCriticalStages.map(s => s.id).join(', ')}`);
        }
    }
    async executeStageWithDependencies(execution, stage) {
        // Wait for dependencies
        await this.waitForDependencies(stage, execution);
        if (execution.status === 'cancelling') {
            return;
        }
        await this.executeStage(execution, stage);
    }
    async executeStage(execution, stage) {
        this.logger.debug(`Executing stage: ${execution.id}/${stage.id}`);
        try {
            stage.status = 'running';
            stage.startTime = new Date();
            // Execute stage based on type
            const result = await this.executeStageByType(execution, stage);
            stage.status = 'completed';
            stage.endTime = new Date();
            stage.result = result;
            execution.metrics.completedStages++;
            execution.results[stage.id] = result;
            this.logger.debug(`Stage completed: ${execution.id}/${stage.id}`);
        }
        catch (error) {
            stage.status = 'failed';
            stage.endTime = new Date();
            stage.error = error.message;
            execution.metrics.failedStages++;
            this.logger.error(`Stage failed: ${execution.id}/${stage.id}`, error);
            if (!stage.continueOnFailure) {
                throw error;
            }
        }
    }
    async executeStageByType(execution, stage) {
        switch (stage.type) {
            case 'ai-analysis':
                return this.executeAiAnalysisStage(execution, stage);
            case 'data-preprocessing':
                return this.executeDataPreprocessingStage(execution, stage);
            case 'model-training':
                return this.executeModelTrainingStage(execution, stage);
            case 'model-evaluation':
                return this.executeModelEvaluationStage(execution, stage);
            case 'data-transformation':
                return this.executeDataTransformationStage(execution, stage);
            case 'conditional':
                return this.executeConditionalStage(execution, stage);
            case 'parallel-batch':
                return this.executeParallelBatchStage(execution, stage);
            default:
                throw new Error(`Unknown stage type: ${stage.type}`);
        }
    }
    async executeAiAnalysisStage(execution, stage) {
        const job = await this.aiRequestQueue.add('analyze', {
            pipelineId: execution.id,
            stageId: stage.id,
            data: this.getStageInput(execution, stage),
            config: stage.config,
        });
        return this.waitForJobCompletion(job);
    }
    async executeModelTrainingStage(execution, stage) {
        const job = await this.trainingQueue.add('train', {
            pipelineId: execution.id,
            stageId: stage.id,
            data: this.getStageInput(execution, stage),
            config: stage.config,
        });
        return this.waitForJobCompletion(job);
    }
    async executeDataPreprocessingStage(execution, stage) {
        // Implementation for data preprocessing
        const input = this.getStageInput(execution, stage);
        // Process data according to stage configuration
        return { processedData: input, metadata: { stage: stage.id } };
    }
    async executeModelEvaluationStage(execution, stage) {
        // Implementation for model evaluation
        const input = this.getStageInput(execution, stage);
        // Evaluate model performance
        return { metrics: {}, evaluation: {} };
    }
    async executeDataTransformationStage(execution, stage) {
        // Implementation for data transformation
        const input = this.getStageInput(execution, stage);
        // Transform data according to configuration
        return { transformedData: input };
    }
    async executeConditionalStage(execution, stage) {
        // Implementation for conditional logic
        const condition = this.evaluateCondition(execution, stage.config.condition);
        return { conditionMet: condition, result: condition ? 'proceed' : 'skip' };
    }
    async executeParallelBatchStage(execution, stage) {
        // Implementation for parallel batch processing
        const input = this.getStageInput(execution, stage);
        const batchSize = stage.config.batchSize || 10;
        // Process in batches
        const results = [];
        for (let i = 0; i < input.length; i += batchSize) {
            const batch = input.slice(i, i + batchSize);
            const batchResult = await this.processBatch(batch, stage.config);
            results.push(...batchResult);
        }
        return results;
    }
    getStageInput(execution, stage) {
        if (stage.inputSource === 'context') {
            return execution.context;
        }
        if (stage.inputSource && execution.results[stage.inputSource]) {
            return execution.results[stage.inputSource];
        }
        return stage.input || {};
    }
    areDependenciesMet(stage, execution) {
        if (!stage.dependencies || stage.dependencies.length === 0) {
            return true;
        }
        return stage.dependencies.every(depId => {
            const depStage = execution.stages.find(s => s.id === depId);
            return depStage && depStage.status === 'completed';
        });
    }
    async waitForDependencies(stage, execution) {
        if (!stage.dependencies || stage.dependencies.length === 0) {
            return;
        }
        const maxWaitTime = 300000; // 5 minutes
        const startTime = Date.now();
        while (!this.areDependenciesMet(stage, execution)) {
            if (Date.now() - startTime > maxWaitTime) {
                throw new Error(`Dependency timeout for stage: ${stage.id}`);
            }
            if (execution.status === 'cancelling') {
                throw new Error('Pipeline cancelled while waiting for dependencies');
            }
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
        }
    }
    async waitForJobCompletion(job) {
        return new Promise((resolve, reject) => {
            job.finished().then(resolve).catch(reject);
        });
    }
    calculateProgress(execution) {
        if (execution.metrics.totalStages === 0)
            return 0;
        return execution.metrics.completedStages / execution.metrics.totalStages;
    }
    getCurrentStage(execution) {
        const runningStage = execution.stages.find(s => s.status === 'running');
        return runningStage ? runningStage.id : null;
    }
    initializeStages(stageDefinitions) {
        return stageDefinitions.map(def => ({
            id: def.id,
            name: def.name,
            type: def.type,
            status: 'pending',
            config: def.config || {},
            dependencies: def.dependencies || [],
            continueOnFailure: def.continueOnFailure || false,
            inputSource: def.inputSource,
            input: def.input,
            retryCount: 0,
        }));
    }
    initializePipelineTemplates() {
        // Register common pipeline templates
        this.registerPipelineTemplate('threat-analysis', {
            name: 'Threat Analysis Pipeline',
            description: 'Comprehensive threat analysis workflow',
            stages: [
                {
                    id: 'preprocess',
                    name: 'Data Preprocessing',
                    type: 'data-preprocessing',
                    config: { normalize: true, validate: true },
                },
                {
                    id: 'analyze',
                    name: 'AI Analysis',
                    type: 'ai-analysis',
                    dependencies: ['preprocess'],
                    config: { model: 'threat-detection', confidence: 0.8 },
                },
                {
                    id: 'evaluate',
                    name: 'Result Evaluation',
                    type: 'model-evaluation',
                    dependencies: ['analyze'],
                    config: { threshold: 0.9 },
                },
            ],
            executionStrategy: 'sequential',
        });
    }
    instantiateTemplate(template, parameters) {
        return {
            name: template.name,
            description: template.description,
            stages: template.stages,
            executionStrategy: template.executionStrategy,
            context: parameters,
        };
    }
    generatePipelineId() {
        return `pipeline-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    evaluateCondition(execution, condition) {
        // Simple condition evaluation - can be extended
        return true;
    }
    async processBatch(batch, config) {
        // Process batch of items
        return batch.map(item => ({ ...item, processed: true }));
    }
    async cancelRunningStages(execution) {
        // Cancel any running stages
        for (const stage of execution.stages) {
            if (stage.status === 'running') {
                stage.status = 'cancelled';
            }
        }
    }
    async cleanupPipelineResources(execution) {
        // Clean up any resources used by the pipeline
        this.activePipelines.delete(execution.id);
    }
    async emitPipelineEvent(execution) {
        // Emit appropriate events based on pipeline status
        // Implementation will be added when event classes are created
    }
    async validatePipelineDefinition(definition) {
        if (!definition.stages || definition.stages.length === 0) {
            throw new Error('Pipeline must have at least one stage');
        }
        // Validate stage dependencies
        for (const stage of definition.stages) {
            if (stage.dependencies) {
                for (const depId of stage.dependencies) {
                    if (!definition.stages.find(s => s.id === depId)) {
                        throw new Error(`Invalid dependency: ${depId} for stage: ${stage.id}`);
                    }
                }
            }
        }
    }
};
exports.PipelineManagerService = PipelineManagerService;
exports.PipelineManagerService = PipelineManagerService = PipelineManagerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, bull_2.InjectQueue)('ai-request')),
    __param(1, (0, bull_2.InjectQueue)('ai-response')),
    __param(2, (0, bull_2.InjectQueue)('training-job')),
    __metadata("design:paramtypes", [typeof (_a = typeof bull_1.Queue !== "undefined" && bull_1.Queue) === "function" ? _a : Object, typeof (_b = typeof bull_1.Queue !== "undefined" && bull_1.Queue) === "function" ? _b : Object, typeof (_c = typeof bull_1.Queue !== "undefined" && bull_1.Queue) === "function" ? _c : Object, typeof (_d = typeof cqrs_1.EventBus !== "undefined" && cqrs_1.EventBus) === "function" ? _d : Object, typeof (_e = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _e : Object])
], PipelineManagerService);
class PipelineError extends Error {
    constructor(message, pipelineId) {
        super(message);
        this.pipelineId = pipelineId;
        this.name = 'PipelineError';
    }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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