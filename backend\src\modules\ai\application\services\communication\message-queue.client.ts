import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectQueue } from '@nestjs/bull';
import { Queue, Job, JobOptions } from 'bull';

/**
 * Message Queue Client for AI Operations
 * 
 * Handles asynchronous AI request processing through message queues.
 * Implements queue-based communication patterns, message persistence,
 * and delivery guarantees for reliable AI operations.
 */
@Injectable()
export class MessageQueueClient {
  private readonly logger = new Logger(MessageQueueClient.name);
  private readonly defaultJobOptions: JobOptions;

  constructor(
    @InjectQueue('ai-analysis') private readonly analysisQueue: Queue,
    @InjectQueue('ai-training') private readonly trainingQueue: Queue,
    @InjectQueue('ai-prediction') private readonly predictionQueue: Queue,
    @InjectQueue('ai-batch') private readonly batchQueue: Queue,
    private readonly configService: ConfigService,
  ) {
    this.defaultJobOptions = {
      attempts: this.configService.get<number>('ai.queue.attempts', 3),
      backoff: {
        type: 'exponential',
        delay: this.configService.get<number>('ai.queue.backoffDelay', 2000),
      },
      removeOnComplete: this.configService.get<number>('ai.queue.removeOnComplete', 100),
      removeOnFail: this.configService.get<number>('ai.queue.removeOnFail', 50),
    };
  }

  /**
   * Queues an analysis request for asynchronous processing
   */
  async queueAnalysisRequest(
    payload: AiAnalysisQueuePayload,
    options: QueueRequestOptions = {}
  ): Promise<AiQueueJobResult> {
    this.logger.debug(`Queuing analysis request: ${payload.requestId}`);

    try {
      const jobOptions = this.buildJobOptions(options);
      const job = await this.analysisQueue.add('analyze-data', payload, jobOptions);

      this.logger.debug(`Analysis request queued: ${payload.requestId}, jobId: ${job.id}`);

      return {
        jobId: job.id.toString(),
        requestId: payload.requestId,
        queueName: 'ai-analysis',
        status: 'queued',
        priority: jobOptions.priority || 0,
        delay: jobOptions.delay || 0,
        timestamp: new Date(),
      };

    } catch (error) {
      this.logger.error(`Failed to queue analysis request: ${payload.requestId}`, error);
      throw new MessageQueueError(`Failed to queue analysis request: ${error.message}`, payload.requestId, error);
    }
  }

  /**
   * Queues a training request for asynchronous processing
   */
  async queueTrainingRequest(
    payload: AiTrainingQueuePayload,
    options: QueueRequestOptions = {}
  ): Promise<AiQueueJobResult> {
    this.logger.debug(`Queuing training request: ${payload.requestId}`);

    try {
      const jobOptions = this.buildJobOptions({
        ...options,
        // Training jobs typically have higher priority and longer timeout
        priority: options.priority || 10,
        timeout: options.timeout || 3600000, // 1 hour
      });

      const job = await this.trainingQueue.add('train-model', payload, jobOptions);

      this.logger.debug(`Training request queued: ${payload.requestId}, jobId: ${job.id}`);

      return {
        jobId: job.id.toString(),
        requestId: payload.requestId,
        queueName: 'ai-training',
        status: 'queued',
        priority: jobOptions.priority || 0,
        delay: jobOptions.delay || 0,
        timestamp: new Date(),
      };

    } catch (error) {
      this.logger.error(`Failed to queue training request: ${payload.requestId}`, error);
      throw new MessageQueueError(`Failed to queue training request: ${error.message}`, payload.requestId, error);
    }
  }

  /**
   * Queues a prediction request for asynchronous processing
   */
  async queuePredictionRequest(
    payload: AiPredictionQueuePayload,
    options: QueueRequestOptions = {}
  ): Promise<AiQueueJobResult> {
    this.logger.debug(`Queuing prediction request: ${payload.requestId}`);

    try {
      const jobOptions = this.buildJobOptions({
        ...options,
        // Prediction jobs typically have high priority and short timeout
        priority: options.priority || 5,
        timeout: options.timeout || 30000, // 30 seconds
      });

      const job = await this.predictionQueue.add('predict', payload, jobOptions);

      this.logger.debug(`Prediction request queued: ${payload.requestId}, jobId: ${job.id}`);

      return {
        jobId: job.id.toString(),
        requestId: payload.requestId,
        queueName: 'ai-prediction',
        status: 'queued',
        priority: jobOptions.priority || 0,
        delay: jobOptions.delay || 0,
        timestamp: new Date(),
      };

    } catch (error) {
      this.logger.error(`Failed to queue prediction request: ${payload.requestId}`, error);
      throw new MessageQueueError(`Failed to queue prediction request: ${error.message}`, payload.requestId, error);
    }
  }

  /**
   * Queues a batch processing request for asynchronous processing
   */
  async queueBatchRequest(
    payload: AiBatchQueuePayload,
    options: QueueRequestOptions = {}
  ): Promise<AiQueueJobResult> {
    this.logger.debug(`Queuing batch request: ${payload.requestId}, items: ${payload.items.length}`);

    try {
      const jobOptions = this.buildJobOptions({
        ...options,
        // Batch jobs typically have lower priority but longer timeout
        priority: options.priority || 1,
        timeout: options.timeout || 1800000, // 30 minutes
      });

      const job = await this.batchQueue.add('process-batch', payload, jobOptions);

      this.logger.debug(`Batch request queued: ${payload.requestId}, jobId: ${job.id}`);

      return {
        jobId: job.id.toString(),
        requestId: payload.requestId,
        queueName: 'ai-batch',
        status: 'queued',
        priority: jobOptions.priority || 0,
        delay: jobOptions.delay || 0,
        timestamp: new Date(),
      };

    } catch (error) {
      this.logger.error(`Failed to queue batch request: ${payload.requestId}`, error);
      throw new MessageQueueError(`Failed to queue batch request: ${error.message}`, payload.requestId, error);
    }
  }

  /**
   * Gets the status of a queued job
   */
  async getJobStatus(jobId: string, queueName: string): Promise<AiQueueJobStatus> {
    this.logger.debug(`Getting job status: ${jobId} from queue: ${queueName}`);

    try {
      const queue = this.getQueueByName(queueName);
      const job = await queue.getJob(jobId);

      if (!job) {
        return {
          jobId,
          queueName,
          status: 'not_found',
          timestamp: new Date(),
        };
      }

      const state = await job.getState();
      const progress = job.progress();
      const logs = await queue.getJobLogs(jobId);

      return {
        jobId,
        queueName,
        status: this.mapBullStateToStatus(state),
        progress: typeof progress === 'number' ? progress : 0,
        data: job.data,
        result: job.returnvalue,
        error: job.failedReason,
        attempts: job.attemptsMade,
        maxAttempts: job.opts.attempts || this.defaultJobOptions.attempts,
        createdAt: new Date(job.timestamp),
        processedAt: job.processedOn ? new Date(job.processedOn) : undefined,
        finishedAt: job.finishedOn ? new Date(job.finishedOn) : undefined,
        logs: logs.logs,
        timestamp: new Date(),
      };

    } catch (error) {
      this.logger.error(`Failed to get job status: ${jobId}`, error);
      throw new MessageQueueError(`Failed to get job status: ${error.message}`, jobId, error);
    }
  }

  /**
   * Cancels a queued job
   */
  async cancelJob(jobId: string, queueName: string): Promise<boolean> {
    this.logger.debug(`Cancelling job: ${jobId} from queue: ${queueName}`);

    try {
      const queue = this.getQueueByName(queueName);
      const job = await queue.getJob(jobId);

      if (!job) {
        this.logger.warn(`Job not found for cancellation: ${jobId}`);
        return false;
      }

      const state = await job.getState();
      
      if (state === 'completed' || state === 'failed') {
        this.logger.warn(`Cannot cancel job in state: ${state}, jobId: ${jobId}`);
        return false;
      }

      await job.remove();
      this.logger.debug(`Job cancelled successfully: ${jobId}`);
      return true;

    } catch (error) {
      this.logger.error(`Failed to cancel job: ${jobId}`, error);
      throw new MessageQueueError(`Failed to cancel job: ${error.message}`, jobId, error);
    }
  }

  /**
   * Retries a failed job
   */
  async retryJob(jobId: string, queueName: string): Promise<boolean> {
    this.logger.debug(`Retrying job: ${jobId} from queue: ${queueName}`);

    try {
      const queue = this.getQueueByName(queueName);
      const job = await queue.getJob(jobId);

      if (!job) {
        this.logger.warn(`Job not found for retry: ${jobId}`);
        return false;
      }

      const state = await job.getState();
      
      if (state !== 'failed') {
        this.logger.warn(`Cannot retry job in state: ${state}, jobId: ${jobId}`);
        return false;
      }

      await job.retry();
      this.logger.debug(`Job retried successfully: ${jobId}`);
      return true;

    } catch (error) {
      this.logger.error(`Failed to retry job: ${jobId}`, error);
      throw new MessageQueueError(`Failed to retry job: ${error.message}`, jobId, error);
    }
  }

  /**
   * Gets queue statistics
   */
  async getQueueStats(queueName: string): Promise<AiQueueStats> {
    this.logger.debug(`Getting queue statistics: ${queueName}`);

    try {
      const queue = this.getQueueByName(queueName);
      
      const [waiting, active, completed, failed, delayed, paused] = await Promise.all([
        queue.getWaiting(),
        queue.getActive(),
        queue.getCompleted(),
        queue.getFailed(),
        queue.getDelayed(),
        queue.getPaused(),
      ]);

      return {
        queueName,
        counts: {
          waiting: waiting.length,
          active: active.length,
          completed: completed.length,
          failed: failed.length,
          delayed: delayed.length,
          paused: paused.length,
        },
        isPaused: await queue.isPaused(),
        timestamp: new Date(),
      };

    } catch (error) {
      this.logger.error(`Failed to get queue statistics: ${queueName}`, error);
      throw new MessageQueueError(`Failed to get queue statistics: ${error.message}`, queueName, error);
    }
  }

  /**
   * Gets jobs by status from a queue
   */
  async getJobsByStatus(
    queueName: string,
    status: JobStatus,
    start = 0,
    end = 10
  ): Promise<AiQueueJob[]> {
    this.logger.debug(`Getting jobs by status: ${status} from queue: ${queueName}`);

    try {
      const queue = this.getQueueByName(queueName);
      let jobs: Job[] = [];

      switch (status) {
        case 'waiting':
          jobs = await queue.getWaiting(start, end);
          break;
        case 'active':
          jobs = await queue.getActive(start, end);
          break;
        case 'completed':
          jobs = await queue.getCompleted(start, end);
          break;
        case 'failed':
          jobs = await queue.getFailed(start, end);
          break;
        case 'delayed':
          jobs = await queue.getDelayed(start, end);
          break;
        case 'paused':
          jobs = await queue.getPaused(start, end);
          break;
        default:
          throw new Error(`Invalid job status: ${status}`);
      }

      return Promise.all(
        jobs.map(async (job) => ({
          jobId: job.id.toString(),
          queueName,
          status: this.mapBullStateToStatus(await job.getState()),
          data: job.data,
          result: job.returnvalue,
          error: job.failedReason,
          progress: typeof job.progress() === 'number' ? job.progress() : 0,
          attempts: job.attemptsMade,
          maxAttempts: job.opts.attempts || this.defaultJobOptions.attempts,
          createdAt: new Date(job.timestamp),
          processedAt: job.processedOn ? new Date(job.processedOn) : undefined,
          finishedAt: job.finishedOn ? new Date(job.finishedOn) : undefined,
        }))
      );

    } catch (error) {
      this.logger.error(`Failed to get jobs by status: ${status} from queue: ${queueName}`, error);
      throw new MessageQueueError(`Failed to get jobs by status: ${error.message}`, queueName, error);
    }
  }

  /**
   * Cleans up old jobs from a queue
   */
  async cleanQueue(
    queueName: string,
    grace: number = 24 * 60 * 60 * 1000, // 24 hours
    status: 'completed' | 'failed' = 'completed'
  ): Promise<number> {
    this.logger.debug(`Cleaning queue: ${queueName}, grace: ${grace}ms, status: ${status}`);

    try {
      const queue = this.getQueueByName(queueName);
      const cleanedJobs = await queue.clean(grace, status);

      this.logger.debug(`Cleaned ${cleanedJobs.length} jobs from queue: ${queueName}`);
      return cleanedJobs.length;

    } catch (error) {
      this.logger.error(`Failed to clean queue: ${queueName}`, error);
      throw new MessageQueueError(`Failed to clean queue: ${error.message}`, queueName, error);
    }
  }

  /**
   * Pauses a queue
   */
  async pauseQueue(queueName: string): Promise<void> {
    this.logger.debug(`Pausing queue: ${queueName}`);

    try {
      const queue = this.getQueueByName(queueName);
      await queue.pause();
      this.logger.debug(`Queue paused: ${queueName}`);

    } catch (error) {
      this.logger.error(`Failed to pause queue: ${queueName}`, error);
      throw new MessageQueueError(`Failed to pause queue: ${error.message}`, queueName, error);
    }
  }

  /**
   * Resumes a paused queue
   */
  async resumeQueue(queueName: string): Promise<void> {
    this.logger.debug(`Resuming queue: ${queueName}`);

    try {
      const queue = this.getQueueByName(queueName);
      await queue.resume();
      this.logger.debug(`Queue resumed: ${queueName}`);

    } catch (error) {
      this.logger.error(`Failed to resume queue: ${queueName}`, error);
      throw new MessageQueueError(`Failed to resume queue: ${error.message}`, queueName, error);
    }
  }

  /**
   * Adds a job with scheduled execution
   */
  async scheduleJob(
    queueName: string,
    jobType: string,
    payload: any,
    scheduleTime: Date,
    options: QueueRequestOptions = {}
  ): Promise<AiQueueJobResult> {
    this.logger.debug(`Scheduling job: ${jobType} in queue: ${queueName} for: ${scheduleTime}`);

    try {
      const queue = this.getQueueByName(queueName);
      const delay = scheduleTime.getTime() - Date.now();

      if (delay < 0) {
        throw new Error('Schedule time must be in the future');
      }

      const jobOptions = this.buildJobOptions({
        ...options,
        delay,
      });

      const job = await queue.add(jobType, payload, jobOptions);

      this.logger.debug(`Job scheduled: ${jobType}, jobId: ${job.id}, delay: ${delay}ms`);

      return {
        jobId: job.id.toString(),
        requestId: payload.requestId || this.generateRequestId(),
        queueName,
        status: 'delayed',
        priority: jobOptions.priority || 0,
        delay: jobOptions.delay || 0,
        timestamp: new Date(),
      };

    } catch (error) {
      this.logger.error(`Failed to schedule job: ${jobType} in queue: ${queueName}`, error);
      throw new MessageQueueError(`Failed to schedule job: ${error.message}`, queueName, error);
    }
  }

  // Private helper methods

  private getQueueByName(queueName: string): Queue {
    switch (queueName) {
      case 'ai-analysis':
        return this.analysisQueue;
      case 'ai-training':
        return this.trainingQueue;
      case 'ai-prediction':
        return this.predictionQueue;
      case 'ai-batch':
        return this.batchQueue;
      default:
        throw new Error(`Unknown queue name: ${queueName}`);
    }
  }

  private buildJobOptions(options: QueueRequestOptions): JobOptions {
    return {
      ...this.defaultJobOptions,
      priority: options.priority ?? this.defaultJobOptions.priority,
      delay: options.delay ?? this.defaultJobOptions.delay,
      timeout: options.timeout ?? this.defaultJobOptions.timeout,
      attempts: options.attempts ?? this.defaultJobOptions.attempts,
      backoff: options.backoff ? {
        type: options.backoff.type || 'exponential',
        delay: options.backoff.delay || 2000,
      } : this.defaultJobOptions.backoff,
      removeOnComplete: options.removeOnComplete ?? this.defaultJobOptions.removeOnComplete,
      removeOnFail: options.removeOnFail ?? this.defaultJobOptions.removeOnFail,
      jobId: options.jobId,
    };
  }

  private mapBullStateToStatus(state: string): JobStatus {
    switch (state) {
      case 'waiting':
        return 'waiting';
      case 'active':
        return 'active';
      case 'completed':
        return 'completed';
      case 'failed':
        return 'failed';
      case 'delayed':
        return 'delayed';
      case 'paused':
        return 'paused';
      default:
        return 'unknown';
    }
  }

  private generateRequestId(): string {
    return `mq-req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Type definitions
interface QueueRequestOptions {
  priority?: number;
  delay?: number;
  timeout?: number;
  attempts?: number;
  backoff?: {
    type: 'fixed' | 'exponential';
    delay: number;
  };
  removeOnComplete?: number;
  removeOnFail?: number;
  jobId?: string;
}

interface AiAnalysisQueuePayload {
  requestId: string;
  data: any;
  model?: string;
  parameters?: any;
  callback?: {
    url: string;
    method: 'POST' | 'PUT';
    headers?: Record<string, string>;
  };
}

interface AiTrainingQueuePayload {
  requestId: string;
  trainingData: any;
  modelConfig: any;
  parameters?: any;
  callback?: {
    url: string;
    method: 'POST' | 'PUT';
    headers?: Record<string, string>;
  };
}

interface AiPredictionQueuePayload {
  requestId: string;
  input: any;
  model?: string;
  parameters?: any;
  callback?: {
    url: string;
    method: 'POST' | 'PUT';
    headers?: Record<string, string>;
  };
}

interface AiBatchQueuePayload {
  requestId: string;
  items: Array<{
    id: string;
    type: 'analysis' | 'prediction' | 'training';
    data: any;
  }>;
  callback?: {
    url: string;
    method: 'POST' | 'PUT';
    headers?: Record<string, string>;
  };
}

interface AiQueueJobResult {
  jobId: string;
  requestId: string;
  queueName: string;
  status: JobStatus;
  priority: number;
  delay: number;
  timestamp: Date;
}

interface AiQueueJobStatus {
  jobId: string;
  queueName: string;
  status: JobStatus;
  progress?: number;
  data?: any;
  result?: any;
  error?: string;
  attempts?: number;
  maxAttempts?: number;
  createdAt?: Date;
  processedAt?: Date;
  finishedAt?: Date;
  logs?: string[];
  timestamp: Date;
}

interface AiQueueJob {
  jobId: string;
  queueName: string;
  status: JobStatus;
  data: any;
  result?: any;
  error?: string;
  progress: number;
  attempts: number;
  maxAttempts: number;
  createdAt: Date;
  processedAt?: Date;
  finishedAt?: Date;
}

interface AiQueueStats {
  queueName: string;
  counts: {
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
    paused: number;
  };
  isPaused: boolean;
  timestamp: Date;
}

type JobStatus = 'waiting' | 'active' | 'completed' | 'failed' | 'delayed' | 'paused' | 'unknown' | 'not_found';

class MessageQueueError extends Error {
  constructor(
    message: string,
    public readonly identifier?: string,
    public readonly originalError?: any
  ) {
    super(message);
    this.name = 'MessageQueueError';
  }
}