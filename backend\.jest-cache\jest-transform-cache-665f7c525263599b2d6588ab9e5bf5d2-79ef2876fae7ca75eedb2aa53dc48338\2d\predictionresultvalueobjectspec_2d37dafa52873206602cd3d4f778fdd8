f800fa74a9c45a848a44741c5c72a427
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const prediction_result_value_object_1 = require("../prediction-result.value-object");
const confidence_score_value_object_1 = require("../confidence-score.value-object");
describe('PredictionResult Value Object', () => {
    describe('Construction', () => {
        it('should create a valid prediction result', () => {
            const confidence = new confidence_score_value_object_1.ConfidenceScore(0.85);
            const result = new prediction_result_value_object_1.PredictionResult('positive', confidence);
            expect(result.prediction).toBe('positive');
            expect(result.confidence.equals(confidence)).toBe(true);
            expect(result.alternatives).toEqual([]);
            expect(result.metadata).toEqual({});
            expect(result.timestamp).toBeInstanceOf(Date);
        });
        it('should create with alternatives and metadata', () => {
            const confidence = new confidence_score_value_object_1.ConfidenceScore(0.85);
            const alternatives = [
                { value: 'negative', confidence: new confidence_score_value_object_1.ConfidenceScore(0.15) }
            ];
            const metadata = { model: 'test-model' };
            const timestamp = new Date();
            const result = new prediction_result_value_object_1.PredictionResult('positive', confidence, alternatives, metadata, timestamp);
            expect(result.prediction).toBe('positive');
            expect(result.alternatives).toHaveLength(1);
            expect(result.metadata.model).toBe('test-model');
            expect(result.timestamp).toBe(timestamp);
        });
        it('should throw error for null prediction', () => {
            const confidence = new confidence_score_value_object_1.ConfidenceScore(0.85);
            expect(() => new prediction_result_value_object_1.PredictionResult(null, confidence)).toThrow('Prediction value cannot be null or undefined');
            expect(() => new prediction_result_value_object_1.PredictionResult(undefined, confidence)).toThrow('Prediction value cannot be null or undefined');
        });
        it('should throw error for invalid confidence', () => {
            expect(() => new prediction_result_value_object_1.PredictionResult('positive', 0.85)).toThrow('Confidence must be a ConfidenceScore instance');
        });
        it('should throw error for invalid alternatives', () => {
            const confidence = new confidence_score_value_object_1.ConfidenceScore(0.85);
            expect(() => new prediction_result_value_object_1.PredictionResult('positive', confidence, 'invalid'))
                .toThrow('Alternatives must be an array');
        });
        it('should throw error for invalid timestamp', () => {
            const confidence = new confidence_score_value_object_1.ConfidenceScore(0.85);
            expect(() => new prediction_result_value_object_1.PredictionResult('positive', confidence, [], {}, 'invalid'))
                .toThrow('Timestamp must be a Date instance');
        });
        it('should throw error for invalid alternative structure', () => {
            const confidence = new confidence_score_value_object_1.ConfidenceScore(0.85);
            const invalidAlternatives = [{ value: 'negative' }]; // Missing confidence
            expect(() => new prediction_result_value_object_1.PredictionResult('positive', confidence, invalidAlternatives))
                .toThrow('Each alternative must have value and confidence properties');
        });
        it('should throw error for unsorted alternatives', () => {
            const confidence = new confidence_score_value_object_1.ConfidenceScore(0.85);
            const unsortedAlternatives = [
                { value: 'negative', confidence: new confidence_score_value_object_1.ConfidenceScore(0.1) },
                { value: 'neutral', confidence: new confidence_score_value_object_1.ConfidenceScore(0.15) }
            ];
            expect(() => new prediction_result_value_object_1.PredictionResult('positive', confidence, unsortedAlternatives))
                .toThrow('Alternatives must be sorted by confidence in descending order');
        });
    });
    describe('Factory Methods', () => {
        it('should create simple prediction result', () => {
            const result = prediction_result_value_object_1.PredictionResult.simple('positive', 0.85);
            expect(result.prediction).toBe('positive');
            expect(result.confidence.value).toBe(0.85);
            expect(result.alternatives).toEqual([]);
        });
        it('should create prediction result with alternatives', () => {
            const result = prediction_result_value_object_1.PredictionResult.withAlternatives('positive', 0.85, [
                { value: 'neutral', confidence: 0.1 },
                { value: 'negative', confidence: 0.05 }
            ]);
            expect(result.prediction).toBe('positive');
            expect(result.alternatives).toHaveLength(2);
            expect(result.alternatives[0].value).toBe('neutral'); // Higher confidence first
            expect(result.alternatives[1].value).toBe('negative');
        });
        it('should create binary classification result', () => {
            const result = prediction_result_value_object_1.PredictionResult.binary(true, 0.85);
            expect(result.prediction).toBe('positive');
            expect(result.alternatives).toHaveLength(1);
            expect(result.alternatives[0].value).toBe('negative');
            expect(result.alternatives[0].confidence.value).toBe(0.15);
        });
        it('should create binary classification result with custom labels', () => {
            const result = prediction_result_value_object_1.PredictionResult.binary(false, 0.7, 'spam', 'ham');
            expect(result.prediction).toBe('ham');
            expect(result.alternatives[0].value).toBe('spam');
            expect(result.alternatives[0].confidence.value).toBe(0.3);
        });
        it('should create multi-class classification result', () => {
            const predictions = [
                { value: 'cat', confidence: 0.6 },
                { value: 'dog', confidence: 0.3 },
                { value: 'bird', confidence: 0.1 }
            ];
            const result = prediction_result_value_object_1.PredictionResult.multiClass(predictions);
            expect(result.prediction).toBe('cat');
            expect(result.alternatives).toHaveLength(2);
            expect(result.alternatives[0].value).toBe('dog');
            expect(result.alternatives[1].value).toBe('bird');
        });
        it('should throw error for empty predictions in multi-class', () => {
            expect(() => prediction_result_value_object_1.PredictionResult.multiClass([])).toThrow('At least one prediction is required');
        });
        it('should create regression result', () => {
            const result = prediction_result_value_object_1.PredictionResult.regression(42.5, 0.9);
            expect(result.prediction).toBe(42.5);
            expect(result.confidence.value).toBe(0.9);
        });
        it('should create regression result with range', () => {
            const result = prediction_result_value_object_1.PredictionResult.regression(42.5, 0.9, { min: 40, max: 50 });
            expect(result.prediction).toBe(42.5);
            expect(result.metadata.range).toEqual({ min: 40, max: 50 });
            expect(result.metadata.withinRange).toBe(true);
        });
    });
    describe('Confidence Analysis', () => {
        it('should check high confidence', () => {
            const highConfidence = prediction_result_value_object_1.PredictionResult.simple('positive', 0.9);
            const lowConfidence = prediction_result_value_object_1.PredictionResult.simple('positive', 0.5);
            expect(highConfidence.hasHighConfidence()).toBe(true);
            expect(lowConfidence.hasHighConfidence()).toBe(false);
            expect(highConfidence.hasHighConfidence(0.95)).toBe(false);
        });
        it('should check low confidence', () => {
            const highConfidence = prediction_result_value_object_1.PredictionResult.simple('positive', 0.9);
            const lowConfidence = prediction_result_value_object_1.PredictionResult.simple('positive', 0.2);
            expect(lowConfidence.hasLowConfidence()).toBe(true);
            expect(highConfidence.hasLowConfidence()).toBe(false);
            expect(lowConfidence.hasLowConfidence(0.1)).toBe(false);
        });
        it('should calculate confidence gap', () => {
            const result = prediction_result_value_object_1.PredictionResult.withAlternatives('positive', 0.8, [
                { value: 'negative', confidence: 0.2 }
            ]);
            expect(result.getConfidenceGap()).toBe(0.6);
        });
        it('should handle no alternatives in confidence gap', () => {
            const result = prediction_result_value_object_1.PredictionResult.simple('positive', 0.8);
            expect(result.getConfidenceGap()).toBe(0.8);
        });
        it('should check if significantly better', () => {
            const significant = prediction_result_value_object_1.PredictionResult.withAlternatives('positive', 0.8, [
                { value: 'negative', confidence: 0.2 }
            ]);
            const notSignificant = prediction_result_value_object_1.PredictionResult.withAlternatives('positive', 0.55, [
                { value: 'negative', confidence: 0.45 }
            ]);
            expect(significant.isSignificantlyBetter()).toBe(true);
            expect(notSignificant.isSignificantlyBetter()).toBe(false);
        });
        it('should check if ambiguous', () => {
            const clear = prediction_result_value_object_1.PredictionResult.withAlternatives('positive', 0.8, [
                { value: 'negative', confidence: 0.2 }
            ]);
            const ambiguous = prediction_result_value_object_1.PredictionResult.withAlternatives('positive', 0.55, [
                { value: 'negative', confidence: 0.45 }
            ]);
            expect(clear.isAmbiguous()).toBe(false);
            expect(ambiguous.isAmbiguous()).toBe(true);
        });
    });
    describe('Alternatives Management', () => {
        it('should check if has alternatives', () => {
            const withAlts = prediction_result_value_object_1.PredictionResult.withAlternatives('positive', 0.8, [
                { value: 'negative', confidence: 0.2 }
            ]);
            const withoutAlts = prediction_result_value_object_1.PredictionResult.simple('positive', 0.8);
            expect(withAlts.hasAlternatives()).toBe(true);
            expect(withoutAlts.hasAlternatives()).toBe(false);
        });
        it('should get alternative count', () => {
            const result = prediction_result_value_object_1.PredictionResult.withAlternatives('positive', 0.8, [
                { value: 'negative', confidence: 0.15 },
                { value: 'neutral', confidence: 0.05 }
            ]);
            expect(result.getAlternativeCount()).toBe(2);
        });
        it('should get top alternatives', () => {
            const result = prediction_result_value_object_1.PredictionResult.withAlternatives('positive', 0.7, [
                { value: 'negative', confidence: 0.2 },
                { value: 'neutral', confidence: 0.1 }
            ]);
            const top1 = result.getTopAlternatives(1);
            expect(top1).toHaveLength(1);
            expect(top1[0].value).toBe('negative');
        });
        it('should get alternatives above threshold', () => {
            const result = prediction_result_value_object_1.PredictionResult.withAlternatives('positive', 0.6, [
                { value: 'negative', confidence: 0.25 },
                { value: 'neutral', confidence: 0.15 }
            ]);
            const aboveThreshold = result.getAlternativesAboveThreshold(0.2);
            expect(aboveThreshold).toHaveLength(1);
            expect(aboveThreshold[0].value).toBe('negative');
        });
    });
    describe('Entropy and Uncertainty', () => {
        it('should calculate entropy', () => {
            const result = prediction_result_value_object_1.PredictionResult.withAlternatives('positive', 0.8, [
                { value: 'negative', confidence: 0.2 }
            ]);
            const entropy = result.getEntropy();
            expect(entropy).toBeGreaterThan(0);
            expect(entropy).toBeLessThan(1); // Binary case should be less than 1
        });
    });
    describe('Time-based Operations', () => {
        it('should get age of prediction', () => {
            const pastTime = new Date(Date.now() - 5000); // 5 seconds ago
            const result = new prediction_result_value_object_1.PredictionResult('positive', new confidence_score_value_object_1.ConfidenceScore(0.8), [], {}, pastTime);
            const age = result.getAge();
            expect(age).toBeGreaterThanOrEqual(5000);
        });
        it('should check if fresh', () => {
            const recentResult = prediction_result_value_object_1.PredictionResult.simple('positive', 0.8);
            const oldTime = new Date(Date.now() - 10000); // 10 seconds ago
            const oldResult = new prediction_result_value_object_1.PredictionResult('positive', new confidence_score_value_object_1.ConfidenceScore(0.8), [], {}, oldTime);
            expect(recentResult.isFresh(5000)).toBe(true);
            expect(oldResult.isFresh(5000)).toBe(false);
        });
    });
    describe('Immutable Operations', () => {
        it('should add metadata without mutating original', () => {
            const original = prediction_result_value_object_1.PredictionResult.simple('positive', 0.8);
            const withMetadata = original.withMetadata({ model: 'test' });
            expect(original.metadata).toEqual({});
            expect(withMetadata.metadata.model).toBe('test');
        });
        it('should update confidence without mutating original', () => {
            const original = prediction_result_value_object_1.PredictionResult.simple('positive', 0.8);
            const newConfidence = new confidence_score_value_object_1.ConfidenceScore(0.9);
            const updated = original.withConfidence(newConfidence);
            expect(original.confidence.value).toBe(0.8);
            expect(updated.confidence.value).toBe(0.9);
        });
        it('should add alternatives without mutating original', () => {
            const original = prediction_result_value_object_1.PredictionResult.simple('positive', 0.8);
            const alternatives = [{ value: 'negative', confidence: new confidence_score_value_object_1.ConfidenceScore(0.2) }];
            const withAlts = original.withAlternatives(alternatives);
            expect(original.alternatives).toHaveLength(0);
            expect(withAlts.alternatives).toHaveLength(1);
        });
    });
    describe('Comparison', () => {
        it('should compare prediction results', () => {
            const result1 = prediction_result_value_object_1.PredictionResult.simple('positive', 0.9);
            const result2 = prediction_result_value_object_1.PredictionResult.simple('negative', 0.7);
            expect(result1.compareTo(result2)).toBeGreaterThan(0);
            expect(result2.compareTo(result1)).toBeLessThan(0);
            expect(result1.isBetterThan(result2)).toBe(true);
            expect(result2.isBetterThan(result1)).toBe(false);
        });
    });
    describe('String Representation', () => {
        it('should convert to string', () => {
            const result = prediction_result_value_object_1.PredictionResult.withAlternatives('positive', 0.8, [
                { value: 'negative', confidence: 0.15 },
                { value: 'neutral', confidence: 0.05 }
            ]);
            const str = result.toString();
            expect(str).toContain('Prediction: positive');
            expect(str).toContain('80.0%');
            expect(str).toContain('Alternatives:');
            expect(str).toContain('negative');
        });
        it('should handle object predictions in string', () => {
            const objectPrediction = { class: 'cat', bbox: [10, 20, 30, 40] };
            const result = prediction_result_value_object_1.PredictionResult.simple(objectPrediction, 0.8);
            const str = result.toString();
            expect(str).toContain('Prediction:');
            expect(str).toContain('cat');
        });
    });
    describe('JSON Serialization', () => {
        it('should serialize to JSON', () => {
            const result = prediction_result_value_object_1.PredictionResult.withAlternatives('positive', 0.8, [
                { value: 'negative', confidence: 0.2 }
            ]);
            const json = result.toJSON();
            expect(json.prediction).toBe('positive');
            expect(json.confidence.value).toBe(0.8);
            expect(json.alternatives).toHaveLength(1);
            expect(json.hasHighConfidence).toBe(true);
            expect(json.confidenceGap).toBe(0.6);
            expect(json.isAmbiguous).toBe(false);
            expect(json.entropy).toBeGreaterThan(0);
            expect(json.age).toBeGreaterThanOrEqual(0);
        });
        it('should deserialize from JSON', () => {
            const json = {
                prediction: 'positive',
                confidence: { value: 0.8 },
                alternatives: [
                    { value: 'negative', confidence: { value: 0.2 } }
                ],
                metadata: { model: 'test' },
                timestamp: new Date().toISOString()
            };
            const result = prediction_result_value_object_1.PredictionResult.fromJSON(json);
            expect(result.prediction).toBe('positive');
            expect(result.confidence.value).toBe(0.8);
            expect(result.alternatives).toHaveLength(1);
            expect(result.metadata.model).toBe('test');
        });
    });
    describe('Ensemble Methods', () => {
        it('should create ensemble from multiple results', () => {
            const results = [
                prediction_result_value_object_1.PredictionResult.simple('positive', 0.8),
                prediction_result_value_object_1.PredictionResult.simple('positive', 0.7),
                prediction_result_value_object_1.PredictionResult.simple('negative', 0.6)
            ];
            const ensemble = prediction_result_value_object_1.PredictionResult.ensemble(results);
            expect(ensemble.prediction).toBe('positive'); // Highest confidence
            expect(ensemble.metadata.ensembleMethod).toBe('average');
            expect(ensemble.metadata.ensembleSize).toBe(3);
        });
        it('should throw error for empty results in ensemble', () => {
            expect(() => prediction_result_value_object_1.PredictionResult.ensemble([])).toThrow('Cannot create ensemble from empty results');
        });
        it('should handle weighted ensemble', () => {
            const results = [
                prediction_result_value_object_1.PredictionResult.simple('positive', 0.8),
                prediction_result_value_object_1.PredictionResult.simple('negative', 0.7)
            ];
            const weights = [0.6, 0.4];
            const ensemble = prediction_result_value_object_1.PredictionResult.ensemble(results, 'weighted', weights);
            expect(ensemble.metadata.ensembleMethod).toBe('weighted');
        });
        it('should throw error for mismatched weights in ensemble', () => {
            const results = [prediction_result_value_object_1.PredictionResult.simple('positive', 0.8)];
            const weights = [0.6, 0.4];
            expect(() => prediction_result_value_object_1.PredictionResult.ensemble(results, 'weighted', weights))
                .toThrow('Weights array must have the same length as results array');
        });
    });
    describe('Immutability', () => {
        it('should return copies of arrays and objects', () => {
            const alternatives = [{ value: 'negative', confidence: new confidence_score_value_object_1.ConfidenceScore(0.2) }];
            const metadata = { model: 'test' };
            const result = new prediction_result_value_object_1.PredictionResult('positive', new confidence_score_value_object_1.ConfidenceScore(0.8), alternatives, metadata);
            const returnedAlts = result.alternatives;
            const returnedMetadata = result.metadata;
            returnedAlts.push({ value: 'neutral', confidence: new confidence_score_value_object_1.ConfidenceScore(0.1) });
            returnedMetadata.modified = true;
            expect(result.alternatives).toHaveLength(1);
            expect(result.metadata.modified).toBeUndefined();
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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