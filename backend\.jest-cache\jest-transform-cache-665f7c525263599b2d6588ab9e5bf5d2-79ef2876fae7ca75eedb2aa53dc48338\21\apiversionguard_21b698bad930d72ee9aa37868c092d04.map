{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\versioning\\api-version.guard.ts", "mappings": ";;;;;;;;;;;;;;AAAA,2CAQwB;AACxB,uCAAyC;AAGzC,qEAAgE;AAChE,mEAA4E;AAE5E;;;GAGG;AAEI,IAAM,eAAe,uBAArB,MAAM,eAAe;IAG1B,YACmB,SAAoB,EACpB,iBAAuC;QADvC,cAAS,GAAT,SAAS,CAAW;QACpB,sBAAiB,GAAjB,iBAAiB,CAAsB;QAJzC,WAAM,GAAG,IAAI,eAAM,CAAC,iBAAe,CAAC,IAAI,CAAC,CAAC;IAKxD,CAAC;IAEJ,KAAK,CAAC,WAAW,CAAC,OAAyB;QACzC,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAW,CAAC;QAC7D,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,WAAW,EAAY,CAAC;QAEhE,IAAI,CAAC;YACH,+BAA+B;YAC/B,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAExE,8CAA8C;YAC9C,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAErD,mBAAmB;YACnB,MAAM,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,aAAa,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;YAE/E,yCAAyC;YACxC,OAAe,CAAC,UAAU,GAAG,gBAAgB,CAAC;YAE/C,oBAAoB;YACpB,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAC1C,gBAAgB,EAChB,OAAO,CAAC,IAAI,EACX,OAAe,CAAC,IAAI,EAAE,EAAE,CAC1B,CAAC;YAEF,OAAO,IAAI,CAAC;QAEd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;gBACjD,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7D,OAAO,EAAE,OAAO,CAAC,OAAO;aACzB,CAAC,CAAC;YAEH,IAAI,KAAK,YAAY,sBAAa,EAAE,CAAC;gBACnC,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,IAAI,4BAAmB,CAAC,qBAAqB,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,OAAyB;QAChD,oCAAoC;QACpC,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CACrC,uCAAe,EACf,OAAO,CAAC,UAAU,EAAE,CACrB,CAAC;QAEF,IAAI,YAAY,EAAE,CAAC;YACjB,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,6BAA6B;QAC7B,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CACpC,uCAAe,EACf,OAAO,CAAC,QAAQ,EAAE,CACnB,CAAC;QAEF,OAAO,WAAW,IAAI,IAAI,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAC3B,gBAAwB,EACxB,aAAsC,EACtC,OAAgB,EAChB,QAAkB;QAElB,gCAAgC;QAChC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC9D,MAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,EAAE;iBACpE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;iBACnB,IAAI,CAAC,IAAI,CAAC,CAAC;YAEd,MAAM,IAAI,4BAAmB,CAC3B,4BAA4B,gBAAgB,yBAAyB,iBAAiB,EAAE,CACzF,CAAC;QACJ,CAAC;QAED,0BAA0B;QAC1B,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;QAE5E,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,4BAAmB,CAAC,wBAAwB,gBAAgB,EAAE,CAAC,CAAC;QAC5E,CAAC;QAED,6BAA6B;QAC7B,IAAI,WAAW,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YACpC,MAAM,IAAI,sBAAa,CACrB;gBACE,UAAU,EAAE,mBAAU,CAAC,IAAI;gBAC3B,OAAO,EAAE,eAAe,gBAAgB,yBAAyB;gBACjE,KAAK,EAAE,gBAAgB;gBACvB,UAAU,EAAE,WAAW,CAAC,UAAU;gBAClC,WAAW,EAAE,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC;aAC1D,EACD,mBAAU,CAAC,IAAI,CAChB,CAAC;QACJ,CAAC;QAED,6BAA6B;QAC7B,IAAI,WAAW,CAAC,MAAM,KAAK,YAAY,EAAE,CAAC;YACxC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,gBAAgB,EAAE,WAAW,CAAC,CAAC;QACtE,CAAC;QAED,sCAAsC;QACtC,IAAI,aAAa,EAAE,CAAC;YAClB,MAAM,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,aAAa,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QACvF,CAAC;QAED,qCAAqC;QACrC,MAAM,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAErE,8BAA8B;QAC9B,MAAM,IAAI,CAAC,4BAA4B,CAAC,gBAAgB,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC/E,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CACjC,gBAAwB,EACxB,MAAwB,EACxB,OAAgB,EAChB,QAAkB;QAElB,wCAAwC;QACxC,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,KAAK,YAAY,IAAI,MAAM,CAAC,OAAO,KAAK,gBAAgB,EAAE,CAAC;YAC7F,iDAAiD;YACjD,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAC7D,gBAAgB,EAChB,MAAM,CAAC,OAAO,CACf,CAAC;YAEF,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;gBAC9B,MAAM,IAAI,4BAAmB,CAC3B,sCAAsC,MAAM,CAAC,OAAO,IAAI;oBACxD,WAAW,gBAAgB,qBAAqB,CACjD,CAAC;YACJ,CAAC;YAED,oCAAoC;YACpC,IAAI,aAAa,CAAC,iBAAiB,EAAE,CAAC;gBACpC,QAAQ,CAAC,SAAS,CAAC,6BAA6B,EAC9C,4BAA4B,gBAAgB,QAAQ,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;gBACxE,QAAQ,CAAC,SAAS,CAAC,wBAAwB,EACzC,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;QAED,6BAA6B;QAC7B,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACtB,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,gBAAgB,EAAE,MAAM,CAAC,CAAC;YAE/D,gCAAgC;YAChC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE;gBAC/C,OAAO,EAAE,gBAAgB;gBACzB,QAAQ,EAAE,OAAO,CAAC,IAAI;gBACtB,eAAe,EAAE,MAAM,CAAC,eAAe;gBACvC,UAAU,EAAE,MAAM,CAAC,UAAU;gBAC7B,WAAW,EAAE,MAAM,CAAC,WAAW;aAChC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAChC,gBAAwB,EACxB,OAAgB,EAChB,QAAkB;QAElB,MAAM,OAAO,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;QAEzD,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,oCAAoC;QACpC,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAS,aAAa,EAAE,OAAO,CAAC,UAAU,EAAE,CAAC;YAChE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAS,aAAa,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QAEhF,IAAI,UAAU,IAAI,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACzE,MAAM,IAAI,4BAAmB,CAC3B,8CAA8C,UAAU,IAAI;gBAC5D,oBAAoB,gBAAgB,EAAE,CACvC,CAAC;QACJ,CAAC;QAED,oCAAoC;QACpC,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAS,aAAa,EAAE,OAAO,CAAC,UAAU,EAAE,CAAC;YAChE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAS,aAAa,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QAEhF,IAAI,UAAU,IAAI,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACzE,MAAM,IAAI,4BAAmB,CAC3B,8CAA8C,UAAU,IAAI;gBAC5D,oBAAoB,gBAAgB,EAAE,CACvC,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,4BAA4B,CACxC,gBAAwB,EACxB,OAAgB,EAChB,QAAkB;QAElB,MAAM,OAAO,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;QAEzD,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAIpC,cAAc,EAAE,OAAO,CAAC,UAAU,EAAE,CAAC;YACpB,IAAI,CAAC,SAAS,CAAC,GAAG,CAInC,cAAc,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QAEvC,IAAI,YAAY,EAAE,CAAC;YACjB,2BAA2B;YAC3B,QAAQ,CAAC,SAAS,CAAC,oBAAoB,EAAE,MAAM,CAAC,CAAC;YACjD,IAAI,YAAY,CAAC,cAAc,EAAE,CAAC;gBAChC,QAAQ,CAAC,SAAS,CAAC,uBAAuB,EAAE,YAAY,CAAC,cAAc,CAAC,CAAC;YAC3E,CAAC;YACD,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;gBACzB,QAAQ,CAAC,SAAS,CAAC,4BAA4B,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;YACzE,CAAC;YAED,IAAI,YAAY,CAAC,QAAQ,EAAE,CAAC;gBAC1B,QAAQ,CAAC,SAAS,CAAC,gBAAgB,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC;YAC9D,CAAC;YAED,iCAAiC;YACjC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,EAAE;gBAC/C,OAAO,EAAE,gBAAgB;gBACzB,QAAQ,EAAE,OAAO,CAAC,IAAI;gBACtB,cAAc,EAAE,YAAY,CAAC,cAAc,IAAI,SAAS;aACzD,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACK,qBAAqB,CAC3B,QAAkB,EAClB,OAAe,EACf,MAAwB;QAExB,QAAQ,CAAC,SAAS,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;QAC/C,QAAQ,CAAC,SAAS,CAAC,0BAA0B,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,MAAM,CAAC,eAAe,EAAE,CAAC;YAC3B,QAAQ,CAAC,SAAS,CAAC,wBAAwB,EAAE,MAAM,CAAC,eAAe,CAAC,WAAW,EAAE,CAAC,CAAC;QACrF,CAAC;QAED,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACtB,QAAQ,CAAC,SAAS,CAAC,mBAAmB,EAAE,MAAM,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;YACvB,QAAQ,CAAC,SAAS,CAAC,mBAAmB,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;YAC1B,QAAQ,CAAC,SAAS,CAAC,uBAAuB,EAAE,MAAM,CAAC,cAAc,CAAC,CAAC;QACrE,CAAC;QAED,2BAA2B;QAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;QACxE,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,MAAM,eAAe,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CACvC,GAAG,CAAC,CAAC,QAAQ,KAAK,CAAC,CAAC,OAAO,IAAI,SAAS,kBAAkB,CAAC,CAAC,eAAe,CAAC,WAAW,EAAE,EAAE,CAC5F,CAAC;YACF,QAAQ,CAAC,SAAS,CAAC,4BAA4B,EAAE,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC/E,CAAC;IACH,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,aAAqB;QACjD,yCAAyC;QACzC,MAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,EAAE;aACpE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,QAAQ,CAAC;aAClC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QAE9D,OAAO,iBAAiB,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI,QAAQ,CAAC;IACnD,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,CAAS,EAAE,CAAS;QAC1C,MAAM,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACxC,MAAM,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAExC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAChE,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAC7B,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAE7B,IAAI,KAAK,GAAG,KAAK;gBAAE,OAAO,CAAC,CAAC;YAC5B,IAAI,KAAK,GAAG,KAAK;gBAAE,OAAO,CAAC,CAAC,CAAC;QAC/B,CAAC;QAED,OAAO,CAAC,CAAC;IACX,CAAC;IAED;;OAEG;IACK,0BAA0B,CAAC,OAAgB;QACjD,4DAA4D;QAC5D,iFAAiF;QACjF,OAAQ,OAAe,CAAC,gBAAgB,IAAI,IAAI,CAAC;IACnD,CAAC;CACF,CAAA;AA/UY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,mBAAU,GAAE;yDAKmB,gBAAS,oBAAT,gBAAS,oDACD,6CAAoB,oBAApB,6CAAoB;GAL/C,eAAe,CA+U3B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\api\\versioning\\api-version.guard.ts"], "sourcesContent": ["import {\r\n  Injectable,\r\n  CanActivate,\r\n  ExecutionContext,\r\n  BadRequestException,\r\n  HttpException,\r\n  HttpStatus,\r\n  Logger,\r\n} from '@nestjs/common';\r\nimport { Reflector } from '@nestjs/core';\r\nimport { Request, Response } from 'express';\r\n\r\nimport { ApiVersioningService } from './api-versioning.service';\r\nimport { API_VERSION_KEY, ApiVersionConfig } from './api-version.decorator';\r\n\r\n/**\r\n * Guard to validate and enforce API versioning\r\n * Checks version compatibility, deprecation status, and version requirements\r\n */\r\n@Injectable()\r\nexport class ApiVersionGuard implements CanActivate {\r\n  private readonly logger = new Logger(ApiVersionGuard.name);\r\n\r\n  constructor(\r\n    private readonly reflector: Reflector,\r\n    private readonly versioningService: ApiVersioningService,\r\n  ) {}\r\n\r\n  async canActivate(context: ExecutionContext): Promise<boolean> {\r\n    const request = context.switchToHttp().getRequest<Request>();\r\n    const response = context.switchToHttp().getResponse<Response>();\r\n\r\n    try {\r\n      // Extract version from request\r\n      const requestedVersion = this.versioningService.extractVersion(request);\r\n      \r\n      // Get version metadata from controller/method\r\n      const versionConfig = this.getVersionConfig(context);\r\n      \r\n      // Validate version\r\n      await this.validateVersion(requestedVersion, versionConfig, request, response);\r\n      \r\n      // Store version in request for later use\r\n      (request as any).apiVersion = requestedVersion;\r\n      \r\n      // Log version usage\r\n      await this.versioningService.logVersionUsage(\r\n        requestedVersion,\r\n        request.path,\r\n        (request as any).user?.id,\r\n      );\r\n\r\n      return true;\r\n\r\n    } catch (error) {\r\n      this.logger.error('API version validation failed', {\r\n        path: request.path,\r\n        method: request.method,\r\n        error: error instanceof Error ? error.message : String(error),\r\n        headers: request.headers,\r\n      });\r\n\r\n      if (error instanceof HttpException) {\r\n        throw error;\r\n      }\r\n\r\n      throw new BadRequestException('Invalid API version');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get version configuration from metadata\r\n   */\r\n  private getVersionConfig(context: ExecutionContext): ApiVersionConfig | null {\r\n    // Check method-level metadata first\r\n    const methodConfig = this.reflector.get<ApiVersionConfig>(\r\n      API_VERSION_KEY,\r\n      context.getHandler(),\r\n    );\r\n\r\n    if (methodConfig) {\r\n      return methodConfig;\r\n    }\r\n\r\n    // Check class-level metadata\r\n    const classConfig = this.reflector.get<ApiVersionConfig>(\r\n      API_VERSION_KEY,\r\n      context.getClass(),\r\n    );\r\n\r\n    return classConfig || null;\r\n  }\r\n\r\n  /**\r\n   * Validate API version against requirements\r\n   */\r\n  private async validateVersion(\r\n    requestedVersion: string,\r\n    versionConfig: ApiVersionConfig | null,\r\n    request: Request,\r\n    response: Response,\r\n  ): Promise<void> {\r\n    // Check if version is supported\r\n    if (!this.versioningService.validateVersion(requestedVersion)) {\r\n      const supportedVersions = this.versioningService.getSupportedVersions()\r\n        .map(v => v.version)\r\n        .join(', ');\r\n\r\n      throw new BadRequestException(\r\n        `Unsupported API version: ${requestedVersion}. Supported versions: ${supportedVersions}`,\r\n      );\r\n    }\r\n\r\n    // Get version information\r\n    const versionInfo = this.versioningService.getVersionInfo(requestedVersion);\r\n    \r\n    if (!versionInfo) {\r\n      throw new BadRequestException(`Invalid API version: ${requestedVersion}`);\r\n    }\r\n\r\n    // Check if version is sunset\r\n    if (versionInfo.status === 'sunset') {\r\n      throw new HttpException(\r\n        {\r\n          statusCode: HttpStatus.GONE,\r\n          message: `API version ${requestedVersion} is no longer supported`,\r\n          error: 'Version Sunset',\r\n          sunsetDate: versionInfo.sunsetDate,\r\n          replacement: this.getReplacementVersion(requestedVersion),\r\n        },\r\n        HttpStatus.GONE,\r\n      );\r\n    }\r\n\r\n    // Handle deprecated versions\r\n    if (versionInfo.status === 'deprecated') {\r\n      this.addDeprecationHeaders(response, requestedVersion, versionInfo);\r\n    }\r\n\r\n    // Check version-specific requirements\r\n    if (versionConfig) {\r\n      await this.validateVersionConfig(requestedVersion, versionConfig, request, response);\r\n    }\r\n\r\n    // Check min/max version requirements\r\n    await this.validateVersionRange(requestedVersion, request, response);\r\n\r\n    // Check experimental features\r\n    await this.validateExperimentalFeatures(requestedVersion, request, response);\r\n  }\r\n\r\n  /**\r\n   * Validate version against specific configuration\r\n   */\r\n  private async validateVersionConfig(\r\n    requestedVersion: string,\r\n    config: ApiVersionConfig,\r\n    request: Request,\r\n    response: Response,\r\n  ): Promise<void> {\r\n    // Check if specific version is required\r\n    if (config.version && config.version !== 'deprecated' && config.version !== requestedVersion) {\r\n      // Allow compatible versions (same major version)\r\n      const compatibility = this.versioningService.checkCompatibility(\r\n        requestedVersion,\r\n        config.version,\r\n      );\r\n\r\n      if (!compatibility.compatible) {\r\n        throw new BadRequestException(\r\n          `This endpoint requires API version ${config.version}. ` +\r\n          `Version ${requestedVersion} is not compatible.`,\r\n        );\r\n      }\r\n\r\n      // Add compatibility warning headers\r\n      if (compatibility.migrationRequired) {\r\n        response.setHeader('X-API-Compatibility-Warning', \r\n          `Migration required from v${requestedVersion} to v${config.version}`);\r\n        response.setHeader('X-API-Breaking-Changes', \r\n          compatibility.breakingChanges.join(', '));\r\n      }\r\n    }\r\n\r\n    // Handle deprecated endpoint\r\n    if (config.deprecated) {\r\n      this.addDeprecationHeaders(response, requestedVersion, config);\r\n      \r\n      // Log deprecated endpoint usage\r\n      this.logger.warn('Deprecated endpoint accessed', {\r\n        version: requestedVersion,\r\n        endpoint: request.path,\r\n        deprecationDate: config.deprecationDate,\r\n        sunsetDate: config.sunsetDate,\r\n        replacement: config.replacement,\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate version range requirements\r\n   */\r\n  private async validateVersionRange(\r\n    requestedVersion: string,\r\n    request: Request,\r\n    response: Response,\r\n  ): Promise<void> {\r\n    const context = this.getCurrentExecutionContext(request);\r\n    \r\n    if (!context) return;\r\n\r\n    // Check minimum version requirement\r\n    const minVersion = this.reflector.get<string>('min_version', context.getHandler()) ||\r\n                      this.reflector.get<string>('min_version', context.getClass());\r\n\r\n    if (minVersion && this.compareVersions(requestedVersion, minVersion) < 0) {\r\n      throw new BadRequestException(\r\n        `This endpoint requires minimum API version ${minVersion}. ` +\r\n        `Current version: ${requestedVersion}`,\r\n      );\r\n    }\r\n\r\n    // Check maximum version requirement\r\n    const maxVersion = this.reflector.get<string>('max_version', context.getHandler()) ||\r\n                      this.reflector.get<string>('max_version', context.getClass());\r\n\r\n    if (maxVersion && this.compareVersions(requestedVersion, maxVersion) > 0) {\r\n      throw new BadRequestException(\r\n        `This endpoint supports maximum API version ${maxVersion}. ` +\r\n        `Current version: ${requestedVersion}`,\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate experimental features\r\n   */\r\n  private async validateExperimentalFeatures(\r\n    requestedVersion: string,\r\n    request: Request,\r\n    response: Response,\r\n  ): Promise<void> {\r\n    const context = this.getCurrentExecutionContext(request);\r\n    \r\n    if (!context) return;\r\n\r\n    const experimental = this.reflector.get<{\r\n      stabilityLevel?: string;\r\n      warning?: string;\r\n      feedback?: string;\r\n    }>('experimental', context.getHandler()) ||\r\n                        this.reflector.get<{\r\n      stabilityLevel?: string;\r\n      warning?: string;\r\n      feedback?: string;\r\n    }>('experimental', context.getClass());\r\n\r\n    if (experimental) {\r\n      // Add experimental headers\r\n      response.setHeader('X-API-Experimental', 'true');\r\n      if (experimental.stabilityLevel) {\r\n        response.setHeader('X-API-Stability-Level', experimental.stabilityLevel);\r\n      }\r\n      if (experimental.warning) {\r\n        response.setHeader('X-API-Experimental-Warning', experimental.warning);\r\n      }\r\n\r\n      if (experimental.feedback) {\r\n        response.setHeader('X-API-Feedback', experimental.feedback);\r\n      }\r\n\r\n      // Log experimental feature usage\r\n      this.logger.log('Experimental feature accessed', {\r\n        version: requestedVersion,\r\n        endpoint: request.path,\r\n        stabilityLevel: experimental.stabilityLevel || 'unknown',\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Add deprecation headers to response\r\n   */\r\n  private addDeprecationHeaders(\r\n    response: Response,\r\n    version: string,\r\n    config: ApiVersionConfig,\r\n  ): void {\r\n    response.setHeader('X-API-Deprecated', 'true');\r\n    response.setHeader('X-API-Deprecated-Version', version);\r\n    \r\n    if (config.deprecationDate) {\r\n      response.setHeader('X-API-Deprecation-Date', config.deprecationDate.toISOString());\r\n    }\r\n    \r\n    if (config.sunsetDate) {\r\n      response.setHeader('X-API-Sunset-Date', config.sunsetDate.toISOString());\r\n    }\r\n    \r\n    if (config.replacement) {\r\n      response.setHeader('X-API-Replacement', config.replacement);\r\n    }\r\n    \r\n    if (config.migrationGuide) {\r\n      response.setHeader('X-API-Migration-Guide', config.migrationGuide);\r\n    }\r\n\r\n    // Add deprecation warnings\r\n    const warnings = this.versioningService.getDeprecationWarnings(version);\r\n    if (warnings.length > 0) {\r\n      const warningMessages = warnings.map(w => \r\n        `${w.severity}: ${w.feature || 'Version'} deprecated on ${w.deprecationDate.toISOString()}`\r\n      );\r\n      response.setHeader('X-API-Deprecation-Warnings', warningMessages.join('; '));\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get replacement version for sunset version\r\n   */\r\n  private getReplacementVersion(sunsetVersion: string): string {\r\n    // Logic to determine replacement version\r\n    const supportedVersions = this.versioningService.getSupportedVersions()\r\n      .filter(v => v.status === 'active')\r\n      .sort((a, b) => this.compareVersions(b.version, a.version));\r\n\r\n    return supportedVersions[0]?.version || 'latest';\r\n  }\r\n\r\n  /**\r\n   * Compare two version strings\r\n   */\r\n  private compareVersions(a: string, b: string): number {\r\n    const aParts = a.split('.').map(Number);\r\n    const bParts = b.split('.').map(Number);\r\n    \r\n    for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {\r\n      const aPart = aParts[i] || 0;\r\n      const bPart = bParts[i] || 0;\r\n      \r\n      if (aPart > bPart) return 1;\r\n      if (aPart < bPart) return -1;\r\n    }\r\n    \r\n    return 0;\r\n  }\r\n\r\n  /**\r\n   * Get current execution context (helper method)\r\n   */\r\n  private getCurrentExecutionContext(request: Request): ExecutionContext | null {\r\n    // This is a simplified approach - in a real implementation,\r\n    // you might need to store the context in the request or use a different approach\r\n    return (request as any).executionContext || null;\r\n  }\r\n}"], "version": 3}