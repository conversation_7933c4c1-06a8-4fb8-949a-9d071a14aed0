import {
  En<PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  OneToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { IsEnum, IsOptional, IsString, IsNumber, IsBoolean, IsArray, IsObject, IsDate } from 'class-validator';

import { IndicatorOfCompromise } from './indicator-of-compromise.entity';
import { ThreatIndicator } from './threat-indicator.entity';
import { ThreatActor } from './threat-actor.entity';
import { ThreatCampaign } from './threat-campaign.entity';

/**
 * Threat intelligence severity levels
 */
export enum ThreatSeverity {
  CRITICAL = 'critical',
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low',
  INFO = 'info',
}

/**
 * Threat intelligence confidence levels
 */
export enum ThreatConfidence {
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low',
  UNKNOWN = 'unknown',
}

/**
 * Threat intelligence types
 */
export enum ThreatType {
  MALWARE = 'malware',
  PHISHING = 'phishing',
  BOTNET = 'botnet',
  APT = 'apt',
  RANSOMWARE = 'ransomware',
  TROJAN = 'trojan',
  BACKDOOR = 'backdoor',
  ROOTKIT = 'rootkit',
  SPYWARE = 'spyware',
  ADWARE = 'adware',
  VULNERABILITY = 'vulnerability',
  EXPLOIT = 'exploit',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  ATTACK_PATTERN = 'attack_pattern',
  TOOL = 'tool',
  INFRASTRUCTURE = 'infrastructure',
  OTHER = 'other',
}

/**
 * Threat intelligence status
 */
export enum ThreatStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  EXPIRED = 'expired',
  REVOKED = 'revoked',
  UNDER_REVIEW = 'under_review',
}

/**
 * Data source information
 */
export interface ThreatDataSource {
  name: string;
  type: 'commercial' | 'open_source' | 'government' | 'internal' | 'community';
  reliability: 'A' | 'B' | 'C' | 'D' | 'E' | 'F'; // Admiralty Code
  url?: string;
  lastUpdated: Date;
  confidence: ThreatConfidence;
}

/**
 * MITRE ATT&CK framework mapping
 */
export interface MitreAttackMapping {
  tactics: string[];
  techniques: string[];
  subTechniques?: string[];
  mitigations?: string[];
}

/**
 * Kill chain phase mapping
 */
export interface KillChainPhase {
  killChainName: string;
  phaseName: string;
  phaseOrder?: number;
}

/**
 * Threat intelligence entity
 * Represents comprehensive threat intelligence information including IOCs, attribution, and analysis
 */
@Entity('threat_intelligence')
@Index(['threatType', 'severity'])
@Index(['status', 'firstSeen'])
@Index(['confidence', 'severity'])
@Index(['tags'], { where: 'tags IS NOT NULL' })
export class ThreatIntelligence {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  @IsString()
  title: string;

  @Column({ type: 'text' })
  @IsString()
  description: string;

  @Column({
    type: 'enum',
    enum: ThreatType,
  })
  @IsEnum(ThreatType)
  threatType: ThreatType;

  @Column({
    type: 'enum',
    enum: ThreatSeverity,
    default: ThreatSeverity.MEDIUM,
  })
  @IsEnum(ThreatSeverity)
  severity: ThreatSeverity;

  @Column({
    type: 'enum',
    enum: ThreatConfidence,
    default: ThreatConfidence.MEDIUM,
  })
  @IsEnum(ThreatConfidence)
  confidence: ThreatConfidence;

  @Column({
    type: 'enum',
    enum: ThreatStatus,
    default: ThreatStatus.ACTIVE,
  })
  @IsEnum(ThreatStatus)
  status: ThreatStatus;

  @Column({ type: 'timestamp with time zone' })
  @IsDate()
  firstSeen: Date;

  @Column({ type: 'timestamp with time zone', nullable: true })
  @IsOptional()
  @IsDate()
  lastSeen?: Date;

  @Column({ type: 'timestamp with time zone', nullable: true })
  @IsOptional()
  @IsDate()
  expiresAt?: Date;

  @Column({ type: 'text', array: true, nullable: true })
  @IsOptional()
  @IsArray()
  tags?: string[];

  @Column({ type: 'jsonb' })
  @IsObject()
  dataSource: ThreatDataSource;

  @Column({ type: 'jsonb', nullable: true })
  @IsOptional()
  @IsObject()
  mitreAttack?: MitreAttackMapping;

  @Column({ type: 'jsonb', nullable: true })
  @IsOptional()
  @IsArray()
  killChain?: KillChainPhase[];

  @Column({ type: 'text', array: true, nullable: true })
  @IsOptional()
  @IsArray()
  targetedSectors?: string[];

  @Column({ type: 'text', array: true, nullable: true })
  @IsOptional()
  @IsArray()
  targetedCountries?: string[];

  @Column({ type: 'text', array: true, nullable: true })
  @IsOptional()
  @IsArray()
  affectedPlatforms?: string[];

  @Column({ type: 'jsonb', nullable: true })
  @IsOptional()
  @IsObject()
  technicalDetails?: any;

  @Column({ type: 'jsonb', nullable: true })
  @IsOptional()
  @IsObject()
  behavioralAnalysis?: any;

  @Column({ type: 'text', array: true, nullable: true })
  @IsOptional()
  @IsArray()
  references?: string[];

  @Column({ type: 'jsonb', nullable: true })
  @IsOptional()
  @IsObject()
  stixData?: any;

  @Column({ type: 'decimal', precision: 3, scale: 1, nullable: true })
  @IsOptional()
  @IsNumber()
  riskScore?: number;

  @Column({ type: 'integer', default: 0 })
  @IsNumber()
  observationCount: number;

  @Column({ type: 'timestamp with time zone', nullable: true })
  @IsOptional()
  @IsDate()
  lastObserved?: Date;

  @Column({ type: 'boolean', default: false })
  @IsBoolean()
  isIoc: boolean;

  @Column({ type: 'boolean', default: false })
  @IsBoolean()
  isAttributed: boolean;

  @Column({ type: 'jsonb', nullable: true })
  @IsOptional()
  @IsObject()
  customAttributes?: Record<string, any>;

  // Relationships
  @OneToMany(() => IndicatorOfCompromise, (ioc) => ioc.threatIntelligence)
  indicators: IndicatorOfCompromise[];

  @OneToMany(() => ThreatIndicator, (indicator) => indicator.threatIntelligence)
  threatIndicators: ThreatIndicator[];

  @ManyToOne(() => ThreatActor, (actor) => actor.threatIntelligence, { nullable: true })
  @JoinColumn({ name: 'threat_actor_id' })
  threatActor?: ThreatActor;

  @Column({ type: 'uuid', nullable: true })
  threatActorId?: string;

  @ManyToOne(() => ThreatCampaign, (campaign) => campaign.threatIntelligence, { nullable: true })
  @JoinColumn({ name: 'threat_campaign_id' })
  threatCampaign?: ThreatCampaign;

  @Column({ type: 'uuid', nullable: true })
  threatCampaignId?: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  /**
   * Check if threat intelligence is currently active
   */
  get isActive(): boolean {
    return this.status === ThreatStatus.ACTIVE && 
           (!this.expiresAt || this.expiresAt > new Date());
  }

  /**
   * Check if threat intelligence is expired
   */
  get isExpired(): boolean {
    return this.expiresAt ? this.expiresAt <= new Date() : false;
  }

  /**
   * Get age in days
   */
  get ageInDays(): number {
    return Math.floor((Date.now() - this.firstSeen.getTime()) / (1000 * 60 * 60 * 24));
  }

  /**
   * Get threat intelligence summary
   */
  getSummary(): any {
    return {
      id: this.id,
      title: this.title,
      threatType: this.threatType,
      severity: this.severity,
      confidence: this.confidence,
      status: this.status,
      isActive: this.isActive,
      isExpired: this.isExpired,
      ageInDays: this.ageInDays,
      observationCount: this.observationCount,
      indicatorCount: this.indicators?.length || 0,
      threatActor: this.threatActor?.name,
      threatCampaign: this.threatCampaign?.name,
      dataSource: this.dataSource.name,
      tags: this.tags,
    };
  }

  /**
   * Update observation count and last observed timestamp
   */
  recordObservation(): void {
    this.observationCount += 1;
    this.lastObserved = new Date();
  }

  /**
   * Calculate risk score based on multiple factors
   */
  calculateRiskScore(): number {
    let score = 0;

    // Base score from severity
    const severityScores = {
      [ThreatSeverity.CRITICAL]: 9.0,
      [ThreatSeverity.HIGH]: 7.0,
      [ThreatSeverity.MEDIUM]: 5.0,
      [ThreatSeverity.LOW]: 3.0,
      [ThreatSeverity.INFO]: 1.0,
    };
    score += severityScores[this.severity];

    // Confidence modifier
    const confidenceModifiers = {
      [ThreatConfidence.HIGH]: 1.0,
      [ThreatConfidence.MEDIUM]: 0.8,
      [ThreatConfidence.LOW]: 0.6,
      [ThreatConfidence.UNKNOWN]: 0.4,
    };
    score *= confidenceModifiers[this.confidence];

    // Observation frequency boost
    if (this.observationCount > 10) {
      score += 1.0;
    } else if (this.observationCount > 5) {
      score += 0.5;
    }

    // Recency factor
    const daysSinceLastSeen = this.lastSeen ? 
      Math.floor((Date.now() - this.lastSeen.getTime()) / (1000 * 60 * 60 * 24)) : 
      this.ageInDays;
    
    if (daysSinceLastSeen <= 7) {
      score += 1.0; // Very recent
    } else if (daysSinceLastSeen <= 30) {
      score += 0.5; // Recent
    }

    // Attribution factor
    if (this.isAttributed && this.threatActor) {
      score += 0.5;
    }

    // Cap at 10.0
    this.riskScore = Math.min(score, 10.0);
    return this.riskScore;
  }

  /**
   * Check if threat intelligence matches given criteria
   */
  matches(criteria: {
    threatTypes?: ThreatType[];
    severities?: ThreatSeverity[];
    tags?: string[];
    sectors?: string[];
    platforms?: string[];
  }): boolean {
    if (criteria.threatTypes && !criteria.threatTypes.includes(this.threatType)) {
      return false;
    }

    if (criteria.severities && !criteria.severities.includes(this.severity)) {
      return false;
    }

    if (criteria.tags && this.tags) {
      const hasMatchingTag = criteria.tags.some(tag => this.tags.includes(tag));
      if (!hasMatchingTag) {
        return false;
      }
    }

    if (criteria.sectors && this.targetedSectors) {
      const hasMatchingSector = criteria.sectors.some(sector => 
        this.targetedSectors.includes(sector)
      );
      if (!hasMatchingSector) {
        return false;
      }
    }

    if (criteria.platforms && this.affectedPlatforms) {
      const hasMatchingPlatform = criteria.platforms.some(platform => 
        this.affectedPlatforms.includes(platform)
      );
      if (!hasMatchingPlatform) {
        return false;
      }
    }

    return true;
  }

  /**
   * Export threat intelligence for reporting
   */
  exportForReporting(): any {
    return {
      id: this.id,
      title: this.title,
      description: this.description,
      threatType: this.threatType,
      severity: this.severity,
      confidence: this.confidence,
      status: this.status,
      firstSeen: this.firstSeen,
      lastSeen: this.lastSeen,
      expiresAt: this.expiresAt,
      tags: this.tags,
      dataSource: this.dataSource,
      mitreAttack: this.mitreAttack,
      killChain: this.killChain,
      targetedSectors: this.targetedSectors,
      targetedCountries: this.targetedCountries,
      affectedPlatforms: this.affectedPlatforms,
      riskScore: this.riskScore,
      observationCount: this.observationCount,
      lastObserved: this.lastObserved,
      isIoc: this.isIoc,
      isAttributed: this.isAttributed,
      threatActor: this.threatActor?.name,
      threatCampaign: this.threatCampaign?.name,
      indicatorCount: this.indicators?.length || 0,
      references: this.references,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }

  /**
   * Activate threat intelligence
   */
  activate(): void {
    this.status = ThreatStatus.ACTIVE;
  }

  /**
   * Deactivate threat intelligence
   */
  deactivate(): void {
    this.status = ThreatStatus.INACTIVE;
  }

  /**
   * Mark as expired
   */
  expire(): void {
    this.status = ThreatStatus.EXPIRED;
    this.expiresAt = new Date();
  }

  /**
   * Revoke threat intelligence
   */
  revoke(reason?: string): void {
    this.status = ThreatStatus.REVOKED;
    if (reason) {
      this.customAttributes = {
        ...this.customAttributes,
        revocationReason: reason,
        revokedAt: new Date(),
      };
    }
  }
}
