import { BullQueueProcessor, BullQueueProcessorCallback, BullQueueSeparateProcessor } from '../bull.types';
import { BullQueueAdvancedProcessor, BullQueueAdvancedSeparateProcessor } from '../interfaces/bull.interfaces';
export declare function isProcessorCallback(processor: BullQueueProcessor): processor is BullQueueProcessorCallback;
export declare function isAdvancedProcessor(processor: BullQueueProcessor): processor is BullQueueAdvancedProcessor;
export declare function isSeparateProcessor(processor: BullQueueProcessor): processor is BullQueueSeparateProcessor;
export declare function isAdvancedSeparateProcessor(processor: BullQueueProcessor): processor is BullQueueAdvancedSeparateProcessor;
//# sourceMappingURL=helpers.d.ts.map