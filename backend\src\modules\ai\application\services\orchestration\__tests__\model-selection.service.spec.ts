import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { ModelSelectionService } from '../model-selection.service';
import { ModelPerformanceService } from '../../optimization/model-performance.service';
import { AiCacheService } from '../../caching/ai-cache.service';
import { 
  AI_MODEL_REPOSITORY,
  AiModelRepository 
} from '../../../../domain/repositories/ai-model.repository.interface';

describe('ModelSelectionService', () => {
  let service: ModelSelectionService;
  let mockModelRepository: jest.Mocked<AiModelRepository>;
  let mockPerformanceService: jest.Mocked<ModelPerformanceService>;
  let mockCacheService: jest.Mocked<AiCacheService>;
  let mockConfigService: jest.Mocked<ConfigService>;

  const mockModelInfo = {
    id: 'model-1',
    name: 'Test Model',
    type: 'threat-detection',
    providerType: 'openai',
    version: '1.0.0',
    status: 'active',
    healthStatus: 'healthy',
    defaultParameters: { temperature: 0.7 },
    recommendedUseCases: ['threat-analysis'],
  };

  const mockPerformanceMetrics = {
    accuracy: 0.95,
    precision: 0.92,
    recall: 0.88,
    f1Score: 0.90,
    averageLatency: 1500,
    throughput: 50,
    costPerRequest: 0.005,
    availability: 0.99,
  };

  beforeEach(async () => {
    // Create mocks
    mockModelRepository = {
      findById: jest.fn(),
      findByTaskType: jest.fn(),
      save: jest.fn(),
      delete: jest.fn(),
    } as any;

    mockPerformanceService = {
      getModelMetrics: jest.fn(),
      getCostMetrics: jest.fn(),
      updateModelMetrics: jest.fn(),
    } as any;

    mockCacheService = {
      get: jest.fn(),
      set: jest.fn(),
      delete: jest.fn(),
      checkHealth: jest.fn(),
    } as any;

    mockConfigService = {
      get: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ModelSelectionService,
        {
          provide: AI_MODEL_REPOSITORY,
          useValue: mockModelRepository,
        },
        {
          provide: ModelPerformanceService,
          useValue: mockPerformanceService,
        },
        {
          provide: AiCacheService,
          useValue: mockCacheService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<ModelSelectionService>(ModelSelectionService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('selectOptimalModel', () => {
    const mockRequest = {
      taskType: 'threat-analysis',
      requirements: {
        maxLatency: 2000,
        minAccuracy: 0.9,
        priority: 'accuracy' as const,
      },
    };

    it('should select optimal model based on requirements', async () => {
      // Arrange
      mockModelRepository.findByTaskType.mockResolvedValue([mockModelInfo]);
      mockPerformanceService.getModelMetrics.mockResolvedValue(mockPerformanceMetrics);
      mockConfigService.get.mockReturnValue(300000); // 5 minutes cache TTL

      // Act
      const result = await service.selectOptimalModel(mockRequest);

      // Assert
      expect(result).toEqual({
        modelId: 'model-1',
        providerType: 'openai',
        modelType: 'threat-detection',
        version: '1.0.0',
        parameters: expect.any(Object),
        score: expect.any(Number),
        selectedAt: expect.any(Date),
      });
      expect(mockModelRepository.findByTaskType).toHaveBeenCalledWith('threat-analysis');
      expect(mockPerformanceService.getModelMetrics).toHaveBeenCalledWith('model-1');
    });

    it('should return cached selection when available and valid', async () => {
      // Arrange
      const cachedSelection = {
        configuration: {
          modelId: 'cached-model',
          providerType: 'openai',
          modelType: 'threat-detection',
          version: '1.0.0',
          parameters: {},
          score: 0.95,
          selectedAt: new Date(),
        },
        timestamp: new Date(),
      };

      // Mock cache hit with valid timestamp
      jest.spyOn(service as any, 'getCachedSelection').mockResolvedValue(cachedSelection);
      jest.spyOn(service as any, 'isCacheValid').mockReturnValue(true);

      // Act
      const result = await service.selectOptimalModel(mockRequest);

      // Assert
      expect(result).toEqual(cachedSelection.configuration);
      expect(mockModelRepository.findByTaskType).not.toHaveBeenCalled();
    });

    it('should throw error when no models are available', async () => {
      // Arrange
      mockModelRepository.findByTaskType.mockResolvedValue([]);

      // Act & Assert
      await expect(service.selectOptimalModel(mockRequest))
        .rejects.toThrow('No models available for task type: threat-analysis');
    });

    it('should filter out unhealthy models', async () => {
      // Arrange
      const unhealthyModel = {
        ...mockModelInfo,
        id: 'unhealthy-model',
        status: 'inactive',
      };
      
      mockModelRepository.findByTaskType.mockResolvedValue([mockModelInfo, unhealthyModel]);
      mockPerformanceService.getModelMetrics.mockResolvedValue(mockPerformanceMetrics);

      // Act
      const result = await service.selectOptimalModel(mockRequest);

      // Assert
      expect(result.modelId).toBe('model-1'); // Only healthy model selected
    });

    it('should handle model selection errors gracefully', async () => {
      // Arrange
      mockModelRepository.findByTaskType.mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.selectOptimalModel(mockRequest))
        .rejects.toThrow('Model selection failed: Database error');
    });
  });

  describe('selectRealTimeModel', () => {
    const mockRequest = {
      taskType: 'threat-analysis',
      requirements: {
        priority: 'balanced' as const,
      },
    };

    it('should select model optimized for real-time processing', async () => {
      // Arrange
      mockModelRepository.findByTaskType.mockResolvedValue([mockModelInfo]);
      mockPerformanceService.getModelMetrics.mockResolvedValue({
        ...mockPerformanceMetrics,
        averageLatency: 500, // Low latency model
      });

      // Act
      const result = await service.selectRealTimeModel(mockRequest);

      // Assert
      expect(result.parameters).toEqual({
        temperature: 0.1, // Lower temperature for consistency
        maxTokens: 500, // Reduced tokens for speed
      });
    });
  });

  describe('selectBatchModel', () => {
    const mockRequest = {
      taskType: 'threat-analysis',
    };

    it('should select model optimized for batch processing', async () => {
      // Arrange
      mockModelRepository.findByTaskType.mockResolvedValue([mockModelInfo]);
      mockPerformanceService.getModelMetrics.mockResolvedValue(mockPerformanceMetrics);

      // Act
      const result = await service.selectBatchModel(mockRequest);

      // Assert
      expect(result).toBeDefined();
      // Batch optimization should prioritize throughput over latency
    });
  });

  describe('selectHighAccuracyModel', () => {
    const mockRequest = {
      taskType: 'threat-analysis',
    };

    it('should select model optimized for high accuracy', async () => {
      // Arrange
      mockModelRepository.findByTaskType.mockResolvedValue([mockModelInfo]);
      mockPerformanceService.getModelMetrics.mockResolvedValue(mockPerformanceMetrics);

      // Act
      const result = await service.selectHighAccuracyModel(mockRequest);

      // Assert
      expect(result.parameters).toEqual({
        temperature: 0.0, // Deterministic output
        topP: 0.9,
      });
    });
  });

  describe('getModelRecommendations', () => {
    it('should return top model recommendations', async () => {
      // Arrange
      const models = [
        { ...mockModelInfo, id: 'model-1' },
        { ...mockModelInfo, id: 'model-2' },
        { ...mockModelInfo, id: 'model-3' },
      ];

      mockModelRepository.findByTaskType.mockResolvedValue(models);
      mockPerformanceService.getModelMetrics.mockResolvedValue(mockPerformanceMetrics);

      // Act
      const recommendations = await service.getModelRecommendations('threat-analysis');

      // Assert
      expect(recommendations).toHaveLength(3);
      expect(recommendations[0]).toEqual({
        modelId: expect.any(String),
        modelName: expect.any(String),
        providerType: 'openai',
        accuracy: 0.95,
        latency: 1500,
        costPerRequest: 0.005,
        overallScore: expect.any(Number),
        strengths: expect.any(Array),
        weaknesses: expect.any(Array),
        useCases: ['threat-analysis'],
      });
    });

    it('should limit recommendations to top 5', async () => {
      // Arrange
      const models = Array.from({ length: 10 }, (_, i) => ({
        ...mockModelInfo,
        id: `model-${i}`,
      }));

      mockModelRepository.findByTaskType.mockResolvedValue(models);
      mockPerformanceService.getModelMetrics.mockResolvedValue(mockPerformanceMetrics);

      // Act
      const recommendations = await service.getModelRecommendations('threat-analysis');

      // Assert
      expect(recommendations).toHaveLength(5);
    });
  });

  describe('evaluateModelPerformance', () => {
    it('should evaluate model performance correctly', async () => {
      // Arrange
      mockModelRepository.findById.mockResolvedValue(mockModelInfo);
      mockPerformanceService.getModelMetrics.mockResolvedValue(mockPerformanceMetrics);
      mockPerformanceService.getCostMetrics.mockResolvedValue({
        costPerRequest: 0.005,
      });

      // Act
      const evaluation = await service.evaluateModelPerformance('model-1');

      // Assert
      expect(evaluation).toEqual({
        modelId: 'model-1',
        accuracy: 0.95,
        precision: 0.92,
        recall: 0.88,
        f1Score: 0.90,
        latency: 1500,
        throughput: 50,
        costPerRequest: 0.005,
        availability: 0.99,
        lastUpdated: expect.any(Date),
      });
    });

    it('should throw error for non-existent model', async () => {
      // Arrange
      mockModelRepository.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(service.evaluateModelPerformance('non-existent'))
        .rejects.toThrow('Evaluation failed: Model not found: non-existent');
    });
  });

  describe('updateSelectionStrategy', () => {
    const mockFeedback = {
      modelId: 'model-1',
      actualAccuracy: 0.93,
      actualLatency: 1200,
      userRating: 4.5,
      comments: 'Good performance',
    };

    it('should update selection strategy based on feedback', async () => {
      // Arrange
      mockPerformanceService.updateModelMetrics.mockResolvedValue(undefined);

      // Act
      await service.updateSelectionStrategy(mockFeedback);

      // Assert
      expect(mockPerformanceService.updateModelMetrics).toHaveBeenCalledWith(
        'model-1',
        {
          accuracy: 0.93,
          latency: 1200,
          userSatisfaction: 4.5,
        }
      );
    });

    it('should handle feedback update errors', async () => {
      // Arrange
      mockPerformanceService.updateModelMetrics.mockRejectedValue(
        new Error('Update failed')
      );

      // Act & Assert
      await expect(service.updateSelectionStrategy(mockFeedback))
        .rejects.toThrow('Strategy update failed: Update failed');
    });
  });

  describe('model scoring', () => {
    it('should calculate model scores correctly', async () => {
      // Arrange
      const calculateModelScore = (service as any).calculateModelScore.bind(service);
      mockPerformanceService.getModelMetrics.mockResolvedValue(mockPerformanceMetrics);

      const request = {
        taskType: 'threat-analysis',
        requirements: {
          priority: 'accuracy',
          minAccuracy: 0.9,
          maxLatency: 2000,
        },
      };

      // Act
      const score = await calculateModelScore(mockModelInfo, request);

      // Assert
      expect(score).toEqual({
        accuracy: expect.any(Number),
        latency: expect.any(Number),
        cost: expect.any(Number),
        availability: expect.any(Number),
        throughput: expect.any(Number),
        total: expect.any(Number),
      });
      expect(score.total).toBeGreaterThan(0);
      expect(score.total).toBeLessThanOrEqual(1);
    });

    it('should apply different weights based on priority', async () => {
      // Arrange
      const getSelectionWeights = (service as any).getSelectionWeights.bind(service);

      // Act
      const accuracyWeights = getSelectionWeights({ priority: 'accuracy' });
      const latencyWeights = getSelectionWeights({ priority: 'latency' });
      const balancedWeights = getSelectionWeights({ priority: 'balanced' });

      // Assert
      expect(accuracyWeights.accuracy).toBeGreaterThan(latencyWeights.accuracy);
      expect(latencyWeights.latency).toBeGreaterThan(accuracyWeights.latency);
      expect(balancedWeights.accuracy).toBe(0.25);
      expect(balancedWeights.latency).toBe(0.25);
    });
  });

  describe('cache management', () => {
    it('should generate consistent cache keys', () => {
      // Arrange
      const generateSelectionCacheKey = (service as any).generateSelectionCacheKey.bind(service);
      
      const request1 = {
        taskType: 'threat-analysis',
        requirements: { priority: 'accuracy' },
      };
      
      const request2 = {
        taskType: 'threat-analysis',
        requirements: { priority: 'accuracy' },
      };
      
      const request3 = {
        taskType: 'threat-analysis',
        requirements: { priority: 'latency' },
      };

      // Act
      const key1 = generateSelectionCacheKey(request1);
      const key2 = generateSelectionCacheKey(request2);
      const key3 = generateSelectionCacheKey(request3);

      // Assert
      expect(key1).toBe(key2); // Same requests should generate same key
      expect(key1).not.toBe(key3); // Different requests should generate different keys
    });

    it('should validate cache entries correctly', () => {
      // Arrange
      const isCacheValid = (service as any).isCacheValid.bind(service);
      mockConfigService.get.mockReturnValue(300000); // 5 minutes

      const recentSelection = {
        timestamp: new Date(Date.now() - 60000), // 1 minute ago
      };
      
      const oldSelection = {
        timestamp: new Date(Date.now() - 600000), // 10 minutes ago
      };

      // Act & Assert
      expect(isCacheValid(recentSelection)).toBe(true);
      expect(isCacheValid(oldSelection)).toBe(false);
    });
  });

  describe('model strengths and weaknesses identification', () => {
    it('should identify model strengths correctly', () => {
      // Arrange
      const identifyModelStrengths = (service as any).identifyModelStrengths.bind(service);
      
      const highPerformanceMetrics = {
        accuracy: 0.95,
        averageLatency: 500,
        costPerRequest: 0.0005,
        availability: 0.999,
      };

      // Act
      const strengths = identifyModelStrengths(mockModelInfo, highPerformanceMetrics);

      // Assert
      expect(strengths).toContain('High accuracy');
      expect(strengths).toContain('Low latency');
      expect(strengths).toContain('Cost effective');
      expect(strengths).toContain('High availability');
    });

    it('should identify model weaknesses correctly', () => {
      // Arrange
      const identifyModelWeaknesses = (service as any).identifyModelWeaknesses.bind(service);
      
      const lowPerformanceMetrics = {
        accuracy: 0.75,
        averageLatency: 8000,
        costPerRequest: 0.02,
        availability: 0.90,
      };

      // Act
      const weaknesses = identifyModelWeaknesses(mockModelInfo, lowPerformanceMetrics);

      // Assert
      expect(weaknesses).toContain('Lower accuracy');
      expect(weaknesses).toContain('Higher latency');
      expect(weaknesses).toContain('Higher cost');
      expect(weaknesses).toContain('Availability concerns');
    });
  });
});