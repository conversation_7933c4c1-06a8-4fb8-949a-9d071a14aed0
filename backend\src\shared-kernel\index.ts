/**
 * Shared Kernel Index
 * 
 * This file exports all the shared kernel components for easy importing
 * across the application. The shared kernel provides foundational domain-driven
 * design patterns and utilities used throughout the system.
 */

// Domain Base Classes
export { BaseEntity } from './domain/base-entity';
export { BaseAggregateRoot } from './domain/base-aggregate-root';
export { BaseDomainEvent } from './domain/base-domain-event';
export { BaseService } from './domain/base-service';

// Integration Base Classes
export { BaseIntegrationEvent } from './integration/base-integration-event';
export type { ServiceContext, ServiceResult } from './domain/base-service';
export { 
  BaseSpecification, 
  AlwaysTrueSpecification, 
  AlwaysFalseSpecification,
  SpecificationUtils
} from './domain/base-specification';

// Repository Interfaces
export type {
  BaseRepository,
  AggregateRepository,
  RepositoryFactory,
  RepositoryConfig,
  RepositoryMetrics,
  PaginationOptions,
  SortOptions,
  QueryOptions,
  PaginatedResult
} from './domain/base-repository.interface';

// Value Objects
export { BaseValueObject } from './value-objects/base-value-object';
export { UniqueEntityId } from './value-objects/unique-entity-id.value-object';
export { Timestamp } from './value-objects/timestamp.value-object';
export { CorrelationId } from './value-objects/correlation-id.value-object';
export { TenantId } from './value-objects/tenant-id.value-object';
export { UserId } from './value-objects/user-id.value-object';
export { Version } from './value-objects/version.value-object';

// Exceptions - Export specific exceptions to avoid conflicts
export { 
  DomainException,
  ValidationException,
  NotFoundException,
  UnauthorizedException,
  ForbiddenException,
  ConflictException,
  RateLimitException,
  ServiceUnavailableException
} from './exceptions';

// Types - Export specific types to avoid conflicts
export type {
  PaginationParams,
  PaginationMeta,
  CursorPaginationParams,
  CursorPaginatedResult
} from './types/pagination.types';

export type {
  FilterCondition,
  FilterExpression,
  FilterParams,
  DateRangeFilter,
  NumericRangeFilter
} from './types/filter.types';

export type {
  SortField,
  SortParams,
  SortDirection
} from './types/sort.types';

export type {
  BaseApiResponse,
  SuccessResponse,
  ErrorResponse,
  PaginatedResponse,
  ErrorDetail,
  ValidationError
} from './types/response.types';

export type {
  AuditEntry,
  AuditUserContext,
  AuditSystemContext,
  AuditResourceContext,
  AuditQueryParams
} from './types/audit.types';

export type {
  SecurityContext
} from './types/security.types';

// Utils - Export all utilities
export * from './utils';

// Decorators - Export specific decorators to avoid conflicts
export {
  Audit,
  SimpleAudit,
  AuditContext,
  AuditLogger,
  AuditLog,
} from './decorators/audit.decorator';

export {
  Cache,
  CacheInvalidate,
  CacheEvictAll,
} from './decorators/cache.decorator';

export {
  Retry,
  ExponentialBackoff,
  LinearBackoff,
  RetryOnError,
} from './decorators/retry.decorator';

export {
  RateLimit,
  UserRateLimit,
  GlobalRateLimit,
  IpRateLimit,
} from './decorators/rate-limit.decorator';

// Patterns - Export specific patterns to avoid conflicts
export {
  CircuitBreaker,
  CircuitBreakerProtection,
  CircuitBreakerState,
  CircuitBreakerMetrics,
} from './patterns/circuit-breaker';

export {
  RetryStrategy,
  RetryStrategies,
  RetryResult,
} from './patterns/retry-strategy';

export {
  CacheStrategy,
  InMemoryCacheStrategy,
  RedisCacheStrategy,
  MultiLevelCacheStrategy,
  CacheEntry,
  CacheMetrics,
} from './patterns/cache-strategy';

export {
  ValueObjectCache,
  ValueObjectCacheManager,
  CacheStatistics,
  CacheConfig,
} from './patterns/value-object-cache';

export {
  DomainEventBatch,
  EventBatchFactory,
  EventPriority,
  BatchProcessingResult,
  BatchMetrics,
  EventBatchConfig,
} from './patterns/domain-event-batch';

export {
  PerformanceMonitor,
  MetricType,
  PerformanceMetric,
  TimingResult,
  AlertConfig,
  PerformanceAlert,
  PerformanceStats,
  MemoryUsage,
  Monitor,
  CountCalls,
} from './patterns/performance-monitor';

export {
  OptimizedSpecification,
  SpecificationBuilder,
  QueryPerformanceAnalyzer,
  QueryHints,
  QueryMetrics,
} from './patterns/optimized-specification';