{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\repositories\\ai-model.repository.interface.ts", "mappings": ";;;AAiKA,iCAAiC;AACpB,QAAA,mBAAmB,GAAG,MAAM,CAAC,qBAAqB,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\repositories\\ai-model.repository.interface.ts"], "sourcesContent": ["import { AiModelEntity } from '../entities/ai-model/ai-model.entity';\r\n\r\n/**\r\n * AI Model Repository Interface\r\n * \r\n * Defines the contract for AI model data access operations.\r\n * Supports complex queries for model selection, performance tracking,\r\n * and operational management with enterprise-grade capabilities.\r\n */\r\nexport interface AiModelRepository {\r\n  /**\r\n   * Finds a model by ID\r\n   */\r\n  findById(id: string): Promise<AiModelEntity | null>;\r\n\r\n  /**\r\n   * Finds models by provider type\r\n   */\r\n  findByProviderType(providerType: string): Promise<AiModelEntity[]>;\r\n\r\n  /**\r\n   * Finds models by task type\r\n   */\r\n  findByTaskType(taskType: string): Promise<AiModelEntity[]>;\r\n\r\n  /**\r\n   * Finds models by status\r\n   */\r\n  findByStatus(status: string): Promise<AiModelEntity[]>;\r\n\r\n  /**\r\n   * Finds healthy and active models\r\n   */\r\n  findHealthyModels(): Promise<AiModelEntity[]>;\r\n\r\n  /**\r\n   * Finds models with available capacity\r\n   */\r\n  findAvailableModels(): Promise<AiModelEntity[]>;\r\n\r\n  /**\r\n   * Finds models by performance criteria\r\n   */\r\n  findByPerformanceCriteria(criteria: PerformanceCriteria): Promise<AiModelEntity[]>;\r\n\r\n  /**\r\n   * Finds models by tags\r\n   */\r\n  findByTags(tags: string[]): Promise<AiModelEntity[]>;\r\n\r\n  /**\r\n   * Finds models with pagination\r\n   */\r\n  findWithPagination(\r\n    offset: number,\r\n    limit: number,\r\n    filters?: ModelFilters\r\n  ): Promise<{ models: AiModelEntity[]; total: number }>;\r\n\r\n  /**\r\n   * Searches models by name or description\r\n   */\r\n  searchModels(query: string): Promise<AiModelEntity[]>;\r\n\r\n  /**\r\n   * Gets model performance rankings\r\n   */\r\n  getPerformanceRankings(taskType?: string): Promise<ModelPerformanceRanking[]>;\r\n\r\n  /**\r\n   * Gets model utilization statistics\r\n   */\r\n  getUtilizationStats(): Promise<ModelUtilizationStats[]>;\r\n\r\n  /**\r\n   * Saves a model\r\n   */\r\n  save(model: AiModelEntity): Promise<AiModelEntity>;\r\n\r\n  /**\r\n   * Saves multiple models\r\n   */\r\n  saveMany(models: AiModelEntity[]): Promise<AiModelEntity[]>;\r\n\r\n  /**\r\n   * Updates model performance metrics\r\n   */\r\n  updatePerformanceMetrics(\r\n    modelId: string,\r\n    metrics: Partial<PerformanceMetrics>\r\n  ): Promise<void>;\r\n\r\n  /**\r\n   * Updates model load\r\n   */\r\n  updateLoad(modelId: string, delta: number): Promise<void>;\r\n\r\n  /**\r\n   * Updates model health status\r\n   */\r\n  updateHealthStatus(modelId: string, status: string): Promise<void>;\r\n\r\n  /**\r\n   * Bulk updates model statuses\r\n   */\r\n  bulkUpdateStatus(modelIds: string[], status: string): Promise<void>;\r\n\r\n  /**\r\n   * Deletes a model\r\n   */\r\n  delete(id: string): Promise<void>;\r\n\r\n  /**\r\n   * Soft deletes a model (archives it)\r\n   */\r\n  softDelete(id: string): Promise<void>;\r\n\r\n  /**\r\n   * Counts models by criteria\r\n   */\r\n  count(filters?: ModelFilters): Promise<number>;\r\n\r\n  /**\r\n   * Checks if model exists\r\n   */\r\n  exists(id: string): Promise<boolean>;\r\n\r\n  /**\r\n   * Gets models that need health checks\r\n   */\r\n  getModelsForHealthCheck(): Promise<AiModelEntity[]>;\r\n\r\n  /**\r\n   * Gets models with stale metrics\r\n   */\r\n  getModelsWithStaleMetrics(olderThan: Date): Promise<AiModelEntity[]>;\r\n\r\n  /**\r\n   * Gets overloaded models\r\n   */\r\n  getOverloadedModels(): Promise<AiModelEntity[]>;\r\n\r\n  /**\r\n   * Gets underutilized models\r\n   */\r\n  getUnderutilizedModels(): Promise<AiModelEntity[]>;\r\n\r\n  /**\r\n   * Gets model deployment history\r\n   */\r\n  getDeploymentHistory(modelId: string): Promise<ModelDeploymentRecord[]>;\r\n\r\n  /**\r\n   * Gets aggregated performance metrics\r\n   */\r\n  getAggregatedMetrics(\r\n    timeRange: TimeRange,\r\n    groupBy?: 'provider' | 'type' | 'day' | 'hour'\r\n  ): Promise<AggregatedMetrics[]>;\r\n}\r\n\r\n// Token for dependency injection\r\nexport const AI_MODEL_REPOSITORY = Symbol('AI_MODEL_REPOSITORY');\r\n\r\n// Type definitions\r\ninterface PerformanceCriteria {\r\n  minAccuracy?: number;\r\n  maxLatency?: number;\r\n  minThroughput?: number;\r\n  minSuccessRate?: number;\r\n  maxCostPerRequest?: number;\r\n}\r\n\r\ninterface ModelFilters {\r\n  providerType?: string;\r\n  modelType?: string;\r\n  status?: string;\r\n  healthStatus?: string;\r\n  taskTypes?: string[];\r\n  tags?: string[];\r\n  minPerformanceScore?: number;\r\n  maxLoad?: number;\r\n  createdAfter?: Date;\r\n  createdBefore?: Date;\r\n  lastUsedAfter?: Date;\r\n  lastUsedBefore?: Date;\r\n}\r\n\r\ninterface ModelPerformanceRanking {\r\n  modelId: string;\r\n  modelName: string;\r\n  providerType: string;\r\n  performanceScore: number;\r\n  accuracy: number;\r\n  averageLatency: number;\r\n  successRate: number;\r\n  rank: number;\r\n}\r\n\r\ninterface ModelUtilizationStats {\r\n  modelId: string;\r\n  modelName: string;\r\n  currentLoad: number;\r\n  maxConcurrentRequests: number;\r\n  utilizationPercentage: number;\r\n  totalRequests: number;\r\n  averageRequestsPerHour: number;\r\n}\r\n\r\ninterface ModelDeploymentRecord {\r\n  id: string;\r\n  modelId: string;\r\n  version: string;\r\n  deployedAt: Date;\r\n  deployedBy: string;\r\n  status: string;\r\n  configuration: any;\r\n  rollbackInfo?: any;\r\n}\r\n\r\ninterface TimeRange {\r\n  start: Date;\r\n  end: Date;\r\n}\r\n\r\ninterface AggregatedMetrics {\r\n  groupKey: string;\r\n  totalRequests: number;\r\n  successfulRequests: number;\r\n  failedRequests: number;\r\n  averageLatency: number;\r\n  averageAccuracy: number;\r\n  totalCost: number;\r\n  period: Date;\r\n}\r\n\r\ninterface PerformanceMetrics {\r\n  totalRequests?: number;\r\n  successfulRequests?: number;\r\n  failedRequests?: number;\r\n  averageLatency?: number;\r\n  p95Latency?: number;\r\n  p99Latency?: number;\r\n  accuracy?: number;\r\n  precision?: number;\r\n  recall?: number;\r\n  f1Score?: number;\r\n  throughput?: number;\r\n  lastUpdated?: Date;\r\n}\r\n"], "version": 3}