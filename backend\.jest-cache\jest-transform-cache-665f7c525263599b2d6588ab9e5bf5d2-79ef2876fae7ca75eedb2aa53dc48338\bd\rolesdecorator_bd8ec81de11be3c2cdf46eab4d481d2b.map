{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\auth\\decorators\\roles.decorator.ts", "mappings": ";;;AAAA,2CAA6C;AAE7C;;;;;;;;;;;;GAYG;AACI,MAAM,KAAK,GAAG,CAAC,GAAG,KAAe,EAAE,EAAE,CAAC,IAAA,oBAAW,EAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAA5D,QAAA,KAAK,SAAuD;AAEzE;;GAEG;AACU,QAAA,KAAK,GAAG;IACnB,KAAK,EAAE,OAAO;IACd,IAAI,EAAE,MAAM;IACZ,SAAS,EAAE,WAAW;IACtB,OAAO,EAAE,SAAS;IAClB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE,gBAAgB;IAChC,kBAAkB,EAAE,oBAAoB;IACxC,kBAAkB,EAAE,oBAAoB;CAChC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\auth\\decorators\\roles.decorator.ts"], "sourcesContent": ["import { SetMetadata } from '@nestjs/common';\n\n/**\n * Roles decorator for role-based access control\n * \n * @param roles - Array of role names required to access the resource\n * @example\n * ```typescript\n * @Roles('admin', 'moderator')\n * @Get('/admin-only')\n * adminOnlyEndpoint() {\n *   // Only users with 'admin' or 'moderator' roles can access this\n * }\n * ```\n */\nexport const Roles = (...roles: string[]) => SetMetadata('roles', roles);\n\n/**\n * Role constants for commonly used roles\n */\nexport const ROLES = {\n  ADMIN: 'admin',\n  USER: 'user',\n  MODERATOR: 'moderator',\n  ANALYST: 'analyst',\n  VIEWER: 'viewer',\n  SECURITY_ADMIN: 'security_admin',\n  COMPLIANCE_OFFICER: 'compliance_officer',\n  INCIDENT_RESPONDER: 'incident_responder',\n} as const;\n\n/**\n * Type for role values\n */\nexport type Role = typeof ROLES[keyof typeof ROLES];\n"], "version": 3}