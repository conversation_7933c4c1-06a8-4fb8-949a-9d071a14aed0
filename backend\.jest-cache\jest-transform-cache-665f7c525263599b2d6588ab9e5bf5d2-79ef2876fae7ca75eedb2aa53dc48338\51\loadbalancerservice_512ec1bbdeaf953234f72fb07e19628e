1b646390b8542dcd26d595cc30ddc086
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var LoadBalancerService_1;
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoadBalancerService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const schedule_1 = require("@nestjs/schedule");
/**
 * Load Balancer Service
 *
 * Manages load balancing across multiple AI providers and models,
 * implementing intelligent routing strategies, health monitoring,
 * and automatic failover capabilities for optimal performance.
 */
let LoadBalancerService = LoadBalancerService_1 = class LoadBalancerService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(LoadBalancerService_1.name);
        this.providers = new Map();
        this.routingStrategies = new Map();
        this.healthChecks = new Map();
        this.initializeProviders();
        this.initializeRoutingStrategies();
    }
    /**
     * Gets available providers for a specific provider type with load balancing
     */
    async getAvailableProviders(providerType) {
        const typeProviders = Array.from(this.providers.values())
            .filter(provider => provider.type === providerType &&
            provider.status === 'healthy' &&
            provider.enabled);
        if (typeProviders.length === 0) {
            this.logger.warn(`No healthy providers available for type: ${providerType}`);
            return [];
        }
        // Sort by load balancing strategy
        const strategy = this.getRoutingStrategy(providerType);
        return this.sortProvidersByStrategy(typeProviders, strategy);
    }
    /**
     * Selects the best provider for a request using load balancing
     */
    async selectProvider(providerType, request) {
        const availableProviders = await this.getAvailableProviders(providerType);
        if (availableProviders.length === 0) {
            return null;
        }
        const strategy = this.getRoutingStrategy(providerType);
        const selectedProvider = this.selectProviderByStrategy(availableProviders, strategy, request);
        // Update provider load metrics
        await this.updateProviderLoad(selectedProvider.id, 1);
        this.logger.debug(`Selected provider: ${selectedProvider.id} for type: ${providerType}`);
        return selectedProvider;
    }
    /**
     * Reports request completion to update load metrics
     */
    async reportRequestCompletion(providerId, duration, success) {
        const provider = this.providers.get(providerId);
        if (!provider) {
            this.logger.warn(`Provider not found for completion report: ${providerId}`);
            return;
        }
        // Update provider metrics
        provider.metrics.totalRequests++;
        provider.metrics.averageResponseTime = this.calculateMovingAverage(provider.metrics.averageResponseTime, duration, provider.metrics.totalRequests);
        if (success) {
            provider.metrics.successfulRequests++;
        }
        else {
            provider.metrics.failedRequests++;
        }
        provider.metrics.successRate =
            provider.metrics.successfulRequests / provider.metrics.totalRequests;
        // Decrease current load
        await this.updateProviderLoad(providerId, -1);
        // Update health status based on recent performance
        await this.updateProviderHealth(providerId);
        this.logger.debug(`Updated metrics for provider: ${providerId}, success: ${success}, duration: ${duration}ms`);
    }
    /**
     * Checks health of all providers
     */
    async checkProviderHealth() {
        const healthResults = {};
        for (const [providerId, provider] of this.providers) {
            try {
                const healthResult = await this.performHealthCheck(provider);
                this.healthChecks.set(providerId, healthResult);
                healthResults[providerId] = healthResult;
                // Update provider status based on health check
                provider.status = healthResult.healthy ? 'healthy' : 'unhealthy';
            }
            catch (error) {
                this.logger.error(`Health check failed for provider: ${providerId}`, error);
                const failedResult = {
                    healthy: false,
                    responseTime: -1,
                    error: error.message,
                    timestamp: new Date(),
                };
                this.healthChecks.set(providerId, failedResult);
                healthResults[providerId] = failedResult;
                provider.status = 'unhealthy';
            }
        }
        return healthResults;
    }
    /**
     * Adds a new provider to the load balancer
     */
    async addProvider(providerConfig) {
        const provider = {
            id: providerConfig.id,
            name: providerConfig.name,
            type: providerConfig.type,
            endpoint: providerConfig.endpoint,
            status: 'unknown',
            enabled: true,
            priority: providerConfig.priority || 1,
            weight: providerConfig.weight || 1,
            maxConcurrentRequests: providerConfig.maxConcurrentRequests || 100,
            currentLoad: 0,
            metrics: {
                totalRequests: 0,
                successfulRequests: 0,
                failedRequests: 0,
                averageResponseTime: 0,
                successRate: 1.0,
            },
            config: providerConfig.config || {},
        };
        this.providers.set(provider.id, provider);
        // Perform initial health check
        await this.performHealthCheck(provider);
        this.logger.log(`Added provider: ${provider.id} (${provider.type})`);
    }
    /**
     * Removes a provider from the load balancer
     */
    async removeProvider(providerId) {
        const provider = this.providers.get(providerId);
        if (!provider) {
            throw new Error(`Provider not found: ${providerId}`);
        }
        // Disable provider first to stop new requests
        provider.enabled = false;
        // Wait for current requests to complete
        await this.waitForProviderDrain(providerId);
        // Remove provider
        this.providers.delete(providerId);
        this.healthChecks.delete(providerId);
        this.logger.log(`Removed provider: ${providerId}`);
    }
    /**
     * Updates provider configuration
     */
    async updateProviderConfig(providerId, updates) {
        const provider = this.providers.get(providerId);
        if (!provider) {
            throw new Error(`Provider not found: ${providerId}`);
        }
        // Update provider properties
        if (updates.name)
            provider.name = updates.name;
        if (updates.endpoint)
            provider.endpoint = updates.endpoint;
        if (updates.priority !== undefined)
            provider.priority = updates.priority;
        if (updates.weight !== undefined)
            provider.weight = updates.weight;
        if (updates.maxConcurrentRequests !== undefined) {
            provider.maxConcurrentRequests = updates.maxConcurrentRequests;
        }
        if (updates.config) {
            provider.config = { ...provider.config, ...updates.config };
        }
        this.logger.log(`Updated provider config: ${providerId}`);
    }
    /**
     * Gets load balancing statistics
     */
    getLoadBalancingStats() {
        const stats = {
            totalProviders: this.providers.size,
            healthyProviders: 0,
            unhealthyProviders: 0,
            totalLoad: 0,
            averageResponseTime: 0,
            overallSuccessRate: 0,
            providerStats: {},
        };
        let totalRequests = 0;
        let totalSuccessful = 0;
        let totalResponseTime = 0;
        let providersWithMetrics = 0;
        for (const [providerId, provider] of this.providers) {
            if (provider.status === 'healthy') {
                stats.healthyProviders++;
            }
            else {
                stats.unhealthyProviders++;
            }
            stats.totalLoad += provider.currentLoad;
            if (provider.metrics.totalRequests > 0) {
                totalRequests += provider.metrics.totalRequests;
                totalSuccessful += provider.metrics.successfulRequests;
                totalResponseTime += provider.metrics.averageResponseTime;
                providersWithMetrics++;
            }
            stats.providerStats[providerId] = {
                status: provider.status,
                currentLoad: provider.currentLoad,
                metrics: provider.metrics,
            };
        }
        if (totalRequests > 0) {
            stats.overallSuccessRate = totalSuccessful / totalRequests;
        }
        if (providersWithMetrics > 0) {
            stats.averageResponseTime = totalResponseTime / providersWithMetrics;
        }
        return stats;
    }
    /**
     * Periodic health check for all providers
     */
    async performPeriodicHealthChecks() {
        this.logger.debug('Performing periodic health checks');
        await this.checkProviderHealth();
    }
    /**
     * Periodic load balancing optimization
     */
    async optimizeLoadBalancing() {
        this.logger.debug('Optimizing load balancing');
        // Adjust provider weights based on performance
        for (const [providerId, provider] of this.providers) {
            if (provider.metrics.totalRequests > 10) {
                const performanceScore = this.calculatePerformanceScore(provider);
                provider.weight = Math.max(0.1, performanceScore);
            }
        }
    }
    // Private helper methods
    initializeProviders() {
        const providerConfigs = this.configService.get('ai.providers', []);
        for (const config of providerConfigs) {
            this.addProvider(config).catch(error => {
                this.logger.error(`Failed to initialize provider: ${config.id}`, error);
            });
        }
    }
    initializeRoutingStrategies() {
        this.routingStrategies.set('round-robin', {
            name: 'Round Robin',
            selector: this.roundRobinSelector.bind(this),
        });
        this.routingStrategies.set('weighted-round-robin', {
            name: 'Weighted Round Robin',
            selector: this.weightedRoundRobinSelector.bind(this),
        });
        this.routingStrategies.set('least-connections', {
            name: 'Least Connections',
            selector: this.leastConnectionsSelector.bind(this),
        });
        this.routingStrategies.set('response-time', {
            name: 'Fastest Response Time',
            selector: this.responseTimeSelector.bind(this),
        });
        this.routingStrategies.set('random', {
            name: 'Random',
            selector: this.randomSelector.bind(this),
        });
    }
    getRoutingStrategy(providerType) {
        const strategyName = this.configService.get(`ai.loadBalancing.${providerType}.strategy`, 'weighted-round-robin');
        return this.routingStrategies.get(strategyName) ||
            this.routingStrategies.get('weighted-round-robin');
    }
    sortProvidersByStrategy(providers, strategy) {
        return strategy.selector(providers, null);
    }
    selectProviderByStrategy(providers, strategy, request) {
        const sorted = strategy.selector(providers, request);
        return sorted[0];
    }
    roundRobinSelector(providers, request) {
        // Simple round-robin based on total requests
        return providers.sort((a, b) => a.metrics.totalRequests - b.metrics.totalRequests);
    }
    weightedRoundRobinSelector(providers, request) {
        return providers.sort((a, b) => {
            const aScore = a.weight / (a.currentLoad + 1);
            const bScore = b.weight / (b.currentLoad + 1);
            return bScore - aScore;
        });
    }
    leastConnectionsSelector(providers, request) {
        return providers.sort((a, b) => a.currentLoad - b.currentLoad);
    }
    responseTimeSelector(providers, request) {
        return providers.sort((a, b) => a.metrics.averageResponseTime - b.metrics.averageResponseTime);
    }
    randomSelector(providers, request) {
        const shuffled = [...providers];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }
    async updateProviderLoad(providerId, delta) {
        const provider = this.providers.get(providerId);
        if (provider) {
            provider.currentLoad = Math.max(0, provider.currentLoad + delta);
        }
    }
    async updateProviderHealth(providerId) {
        const provider = this.providers.get(providerId);
        if (!provider)
            return;
        // Mark as unhealthy if success rate is too low
        if (provider.metrics.totalRequests > 10 && provider.metrics.successRate < 0.8) {
            provider.status = 'unhealthy';
        }
        // Mark as unhealthy if response time is too high
        if (provider.metrics.averageResponseTime > 10000) { // 10 seconds
            provider.status = 'unhealthy';
        }
    }
    async performHealthCheck(provider) {
        const startTime = Date.now();
        try {
            // Perform actual health check based on provider type
            // This is a simplified implementation
            const responseTime = Date.now() - startTime;
            return {
                healthy: true,
                responseTime,
                timestamp: new Date(),
            };
        }
        catch (error) {
            return {
                healthy: false,
                responseTime: Date.now() - startTime,
                error: error.message,
                timestamp: new Date(),
            };
        }
    }
    calculateMovingAverage(currentAverage, newValue, count) {
        return ((currentAverage * (count - 1)) + newValue) / count;
    }
    calculatePerformanceScore(provider) {
        const successWeight = 0.4;
        const responseTimeWeight = 0.3;
        const availabilityWeight = 0.3;
        const successScore = provider.metrics.successRate;
        const responseTimeScore = Math.max(0, 1 - (provider.metrics.averageResponseTime / 10000));
        const availabilityScore = provider.status === 'healthy' ? 1 : 0;
        return (successScore * successWeight +
            responseTimeScore * responseTimeWeight +
            availabilityScore * availabilityWeight);
    }
    async waitForProviderDrain(providerId) {
        const provider = this.providers.get(providerId);
        if (!provider)
            return;
        const maxWaitTime = 30000; // 30 seconds
        const startTime = Date.now();
        while (provider.currentLoad > 0 && Date.now() - startTime < maxWaitTime) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }
};
exports.LoadBalancerService = LoadBalancerService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_30_SECONDS),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_b = typeof Promise !== "undefined" && Promise) === "function" ? _b : Object)
], LoadBalancerService.prototype, "performPeriodicHealthChecks", null);
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_MINUTE),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_c = typeof Promise !== "undefined" && Promise) === "function" ? _c : Object)
], LoadBalancerService.prototype, "optimizeLoadBalancing", null);
exports.LoadBalancerService = LoadBalancerService = LoadBalancerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], LoadBalancerService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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