import { BaseAggregateRoot, UniqueEntityId } from '../../../../shared-kernel';
import { EventMetadata } from '../value-objects/event-metadata/event-metadata.value-object';
import { EventType } from '../enums/event-type.enum';
import { EventSeverity } from '../enums/event-severity.enum';
import { EventStatus } from '../enums/event-status.enum';
import { EventProcessingStatus } from '../enums/event-processing-status.enum';
import { EnrichmentSource } from '../enums/enrichment-source.enum';
import { EnrichedEventCreatedDomainEvent } from '../events/enriched-event-created.domain-event';
import { EnrichedEventStatusChangedDomainEvent } from '../events/enriched-event-status-changed.domain-event';
import { EnrichedEventEnrichmentFailedDomainEvent } from '../events/enriched-event-enrichment-failed.domain-event';

/**
 * Enrichment Status Enum
 */
export enum EnrichmentStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  PARTIAL = 'PARTIAL',
  SKIPPED = 'SKIPPED',
}

/**
 * Enrichment Rule Interface
 */
export interface EnrichmentRule {
  /** Rule identifier */
  id: string;
  /** Rule name */
  name: string;
  /** Rule description */
  description: string;
  /** Rule priority (higher number = higher priority) */
  priority: number;
  /** Whether the rule is required for successful enrichment */
  required: boolean;
  /** Enrichment sources used by this rule */
  sources: EnrichmentSource[];
  /** Rule configuration */
  config?: Record<string, any>;
  /** Timeout for rule execution in milliseconds */
  timeoutMs?: number;
}

/**
 * Enrichment Data Interface
 */
export interface EnrichmentData {
  /** Source of the enrichment data */
  source: EnrichmentSource;
  /** Type of enrichment data */
  type: string;
  /** The enrichment data itself */
  data: Record<string, any>;
  /** Confidence score of the enrichment (0-100) */
  confidence: number;
  /** When the enrichment was obtained */
  timestamp: Date;
  /** TTL for the enrichment data in seconds */
  ttl?: number;
  /** Metadata about the enrichment */
  metadata?: Record<string, any>;
}

/**
 * Enrichment Result Interface
 */
export interface EnrichmentResult {
  /** Whether enrichment was successful */
  success: boolean;
  /** Applied enrichment rules */
  appliedRules: string[];
  /** Failed enrichment rules */
  failedRules: string[];
  /** Enrichment warnings */
  warnings: string[];
  /** Enrichment errors */
  errors: string[];
  /** Processing duration in milliseconds */
  processingDurationMs: number;
  /** Overall confidence score of enrichment (0-100) */
  confidenceScore: number;
  /** Number of enrichment sources used */
  sourcesUsed: number;
  /** Total data points enriched */
  dataPointsEnriched: number;
}

/**
 * EnrichedEvent Entity Properties
 */
export interface EnrichedEventProps {
  /** Original normalized event ID that was enriched */
  normalizedEventId: UniqueEntityId;
  /** Event metadata containing timestamp, source, and processing information */
  metadata: EventMetadata;
  /** Type of the security event */
  type: EventType;
  /** Severity level of the event */
  severity: EventSeverity;
  /** Current status of the event */
  status: EventStatus;
  /** Current processing status in the pipeline */
  processingStatus: EventProcessingStatus;
  /** Current enrichment status */
  enrichmentStatus: EnrichmentStatus;
  /** Original normalized event data */
  normalizedData: Record<string, any>;
  /** Enriched event data with additional context */
  enrichedData: Record<string, any>;
  /** Event title/summary */
  title: string;
  /** Detailed description of the event */
  description?: string;
  /** Tags for categorization and filtering */
  tags?: string[];
  /** Risk score (0-100) */
  riskScore?: number;
  /** Confidence level (0-100) */
  confidenceLevel?: number;
  /** Additional custom attributes */
  attributes?: Record<string, any>;
  /** Event correlation ID for grouping related events */
  correlationId?: string;
  /** Parent event ID if this is a child event */
  parentEventId?: UniqueEntityId;
  /** Applied enrichment rules */
  appliedRules: EnrichmentRule[];
  /** Enrichment data from various sources */
  enrichmentData: EnrichmentData[];
  /** Enrichment result details */
  enrichmentResult?: EnrichmentResult;
  /** When enrichment started */
  enrichmentStartedAt?: Date;
  /** When enrichment completed */
  enrichmentCompletedAt?: Date;
  /** Enrichment processing attempts */
  enrichmentAttempts?: number;
  /** Last enrichment error */
  lastEnrichmentError?: string;
  /** Threat intelligence score (0-100) */
  threatIntelScore?: number;
  /** Asset context information */
  assetContext?: Record<string, any>;
  /** User context information */
  userContext?: Record<string, any>;
  /** Network context information */
  networkContext?: Record<string, any>;
  /** Geolocation context */
  geolocationContext?: Record<string, any>;
  /** Reputation scores from various sources */
  reputationScores?: Record<string, number>;
  /** Whether the enriched event requires manual review */
  requiresManualReview?: boolean;
  /** Manual review notes */
  reviewNotes?: string;
  /** Who reviewed the event */
  reviewedBy?: string;
  /** When the event was reviewed */
  reviewedAt?: Date;
  /** Enrichment quality score (0-100) */
  enrichmentQualityScore?: number;
  /** Validation errors found during enrichment */
  validationErrors?: string[];
}

/**
 * EnrichedEvent Entity
 * 
 * Represents a security event that has been processed through the enrichment pipeline.
 * Enriched events have additional context from threat intelligence, asset management,
 * user directories, and other external sources.
 * 
 * Key responsibilities:
 * - Maintain enriched event state and lifecycle
 * - Enforce enrichment business rules and data quality standards
 * - Track enrichment process and applied rules
 * - Generate domain events for enrichment state changes
 * - Support threat intelligence integration and context building
 * - Manage manual review workflow for complex enrichments
 * 
 * Business Rules:
 * - Enriched events must reference a valid normalized event
 * - Enrichment data must include source attribution and confidence scores
 * - Threat intelligence scores must be calculated based on multiple sources
 * - High-risk enrichments may require manual review before processing
 * - Enrichment attempts are tracked and limited
 * - Failed enrichment must preserve data integrity and provide fallback
 */
export class EnrichedEvent extends BaseAggregateRoot<EnrichedEventProps> {
  private static readonly MAX_ENRICHMENT_ATTEMPTS = 3;
  private static readonly MIN_ENRICHMENT_QUALITY_SCORE = 70;
  private static readonly HIGH_RISK_REVIEW_THRESHOLD = 85;
  private static readonly MAX_VALIDATION_ERRORS = 10;
  private static readonly MAX_ENRICHMENT_DATA_SOURCES = 50;

  constructor(props: EnrichedEventProps, id?: UniqueEntityId) {
    super(props, id);
    this.validateInvariants();
  }

  /**
   * Create a new EnrichedEvent
   */
  static create(props: EnrichedEventProps, id?: UniqueEntityId): EnrichedEvent {
    const enrichedEvent = new EnrichedEvent(props, id);
    
    // Add domain event for enriched event creation
    enrichedEvent.addDomainEvent(new EnrichedEventCreatedDomainEvent(
      enrichedEvent.id,
      {
        normalizedEventId: props.normalizedEventId,
        eventType: props.type,
        severity: props.severity,
        enrichmentStatus: props.enrichmentStatus,
        enrichmentQualityScore: props.enrichmentQualityScore,
        appliedRulesCount: props.appliedRules.length,
        enrichmentDataCount: props.enrichmentData.length,
        threatIntelScore: props.threatIntelScore,
        requiresManualReview: props.requiresManualReview || false,
      }
    ));

    return enrichedEvent;
  }

  protected validateInvariants(): void {
    super.validateInvariants();

    if (!this.props.normalizedEventId) {
      throw new Error('EnrichedEvent must reference a normalized event');
    }

    if (!this.props.metadata) {
      throw new Error('EnrichedEvent must have metadata');
    }

    if (!this.props.type) {
      throw new Error('EnrichedEvent must have a type');
    }

    if (!this.props.severity) {
      throw new Error('EnrichedEvent must have a severity');
    }

    if (!this.props.status) {
      throw new Error('EnrichedEvent must have a status');
    }

    if (!this.props.processingStatus) {
      throw new Error('EnrichedEvent must have a processing status');
    }

    if (!this.props.enrichmentStatus) {
      throw new Error('EnrichedEvent must have an enrichment status');
    }

    if (!this.props.normalizedData) {
      throw new Error('EnrichedEvent must have normalized data');
    }

    if (!this.props.enrichedData) {
      throw new Error('EnrichedEvent must have enriched data');
    }

    if (!this.props.title || this.props.title.trim().length === 0) {
      throw new Error('EnrichedEvent must have a non-empty title');
    }

    if (!Array.isArray(this.props.appliedRules)) {
      throw new Error('EnrichedEvent must have applied rules array');
    }

    if (!Array.isArray(this.props.enrichmentData)) {
      throw new Error('EnrichedEvent must have enrichment data array');
    }

    if (this.props.enrichmentData.length > EnrichedEvent.MAX_ENRICHMENT_DATA_SOURCES) {
      throw new Error(`EnrichedEvent cannot have more than ${EnrichedEvent.MAX_ENRICHMENT_DATA_SOURCES} enrichment data sources`);
    }

    if (this.props.enrichmentQualityScore !== undefined && 
        (this.props.enrichmentQualityScore < 0 || this.props.enrichmentQualityScore > 100)) {
      throw new Error('Enrichment quality score must be between 0 and 100');
    }

    if (this.props.threatIntelScore !== undefined && 
        (this.props.threatIntelScore < 0 || this.props.threatIntelScore > 100)) {
      throw new Error('Threat intelligence score must be between 0 and 100');
    }

    if (this.props.riskScore !== undefined && 
        (this.props.riskScore < 0 || this.props.riskScore > 100)) {
      throw new Error('Risk score must be between 0 and 100');
    }

    if (this.props.confidenceLevel !== undefined && 
        (this.props.confidenceLevel < 0 || this.props.confidenceLevel > 100)) {
      throw new Error('Confidence level must be between 0 and 100');
    }

    if (this.props.enrichmentAttempts !== undefined && this.props.enrichmentAttempts < 0) {
      throw new Error('Enrichment attempts cannot be negative');
    }

    if (this.props.validationErrors && 
        this.props.validationErrors.length > EnrichedEvent.MAX_VALIDATION_ERRORS) {
      throw new Error(`Cannot have more than ${EnrichedEvent.MAX_VALIDATION_ERRORS} validation errors`);
    }

    // Validate enrichment status consistency
    this.validateEnrichmentStatusConsistency();

    // Validate enrichment data integrity
    this.validateEnrichmentDataIntegrity();
  }

  private validateEnrichmentStatusConsistency(): void {
    // If enrichment is completed, it should have completion timestamp
    if (this.props.enrichmentStatus === EnrichmentStatus.COMPLETED) {
      if (!this.props.enrichmentCompletedAt) {
        throw new Error('Completed enrichment must have completion timestamp');
      }
      if (!this.props.enrichmentResult) {
        throw new Error('Completed enrichment must have result');
      }
    }

    // If enrichment failed, it should have error information
    if (this.props.enrichmentStatus === EnrichmentStatus.FAILED) {
      if (!this.props.lastEnrichmentError && 
          (!this.props.enrichmentResult || this.props.enrichmentResult.errors.length === 0)) {
        throw new Error('Failed enrichment must have error information');
      }
    }

    // If enrichment is in progress, it should have started timestamp
    if (this.props.enrichmentStatus === EnrichmentStatus.IN_PROGRESS) {
      if (!this.props.enrichmentStartedAt) {
        throw new Error('In-progress enrichment must have start timestamp');
      }
    }

    // Manual review consistency
    if (this.props.requiresManualReview && this.props.reviewedAt) {
      if (!this.props.reviewedBy) {
        throw new Error('Reviewed events must have reviewer information');
      }
    }
  }

  private validateEnrichmentDataIntegrity(): void {
    // Validate enrichment data structure
    for (const enrichmentData of this.props.enrichmentData) {
      if (!enrichmentData.source) {
        throw new Error('Enrichment data must have a source');
      }
      if (!enrichmentData.type) {
        throw new Error('Enrichment data must have a type');
      }
      if (!enrichmentData.data) {
        throw new Error('Enrichment data must have data');
      }
      if (enrichmentData.confidence < 0 || enrichmentData.confidence > 100) {
        throw new Error('Enrichment data confidence must be between 0 and 100');
      }
      if (!enrichmentData.timestamp) {
        throw new Error('Enrichment data must have a timestamp');
      }
    }

    // Validate reputation scores
    if (this.props.reputationScores) {
      for (const [source, score] of Object.entries(this.props.reputationScores)) {
        if (typeof score !== 'number' || score < 0 || score > 100) {
          throw new Error(`Reputation score for ${source} must be between 0 and 100`);
        }
      }
    }
  }

  // Getters
  get normalizedEventId(): UniqueEntityId {
    return this.props.normalizedEventId;
  }

  get metadata(): EventMetadata {
    return this.props.metadata;
  }

  get type(): EventType {
    return this.props.type;
  }

  get severity(): EventSeverity {
    return this.props.severity;
  }

  get status(): EventStatus {
    return this.props.status;
  }

  get processingStatus(): EventProcessingStatus {
    return this.props.processingStatus;
  }

  get enrichmentStatus(): EnrichmentStatus {
    return this.props.enrichmentStatus;
  }

  get normalizedData(): Record<string, any> {
    return { ...this.props.normalizedData };
  }

  get enrichedData(): Record<string, any> {
    return { ...this.props.enrichedData };
  }

  get title(): string {
    return this.props.title;
  }

  get description(): string | undefined {
    return this.props.description;
  }

  get tags(): string[] {
    return this.props.tags ? [...this.props.tags] : [];
  }

  get riskScore(): number | undefined {
    return this.props.riskScore;
  }

  get confidenceLevel(): number | undefined {
    return this.props.confidenceLevel;
  }

  get attributes(): Record<string, any> {
    return this.props.attributes ? { ...this.props.attributes } : {};
  }

  get correlationId(): string | undefined {
    return this.props.correlationId;
  }

  get parentEventId(): UniqueEntityId | undefined {
    return this.props.parentEventId;
  }

  get appliedRules(): EnrichmentRule[] {
    return [...this.props.appliedRules];
  }

  get enrichmentData(): EnrichmentData[] {
    return [...this.props.enrichmentData];
  }

  get enrichmentResult(): EnrichmentResult | undefined {
    return this.props.enrichmentResult ? { ...this.props.enrichmentResult } : undefined;
  }

  get enrichmentStartedAt(): Date | undefined {
    return this.props.enrichmentStartedAt;
  }

  get enrichmentCompletedAt(): Date | undefined {
    return this.props.enrichmentCompletedAt;
  }

  get enrichmentAttempts(): number {
    return this.props.enrichmentAttempts || 0;
  }

  get lastEnrichmentError(): string | undefined {
    return this.props.lastEnrichmentError;
  }

  get threatIntelScore(): number | undefined {
    return this.props.threatIntelScore;
  }

  get assetContext(): Record<string, any> {
    return this.props.assetContext ? { ...this.props.assetContext } : {};
  }

  get userContext(): Record<string, any> {
    return this.props.userContext ? { ...this.props.userContext } : {};
  }

  get networkContext(): Record<string, any> {
    return this.props.networkContext ? { ...this.props.networkContext } : {};
  }

  get geolocationContext(): Record<string, any> {
    return this.props.geolocationContext ? { ...this.props.geolocationContext } : {};
  }

  get reputationScores(): Record<string, number> {
    return this.props.reputationScores ? { ...this.props.reputationScores } : {};
  }

  get requiresManualReview(): boolean {
    return this.props.requiresManualReview || false;
  }

  get reviewNotes(): string | undefined {
    return this.props.reviewNotes;
  }

  get reviewedBy(): string | undefined {
    return this.props.reviewedBy;
  }

  get reviewedAt(): Date | undefined {
    return this.props.reviewedAt;
  }

  get enrichmentQualityScore(): number | undefined {
    return this.props.enrichmentQualityScore;
  }

  get validationErrors(): string[] {
    return this.props.validationErrors ? [...this.props.validationErrors] : [];
  }

  /**
   * Get the entity ID (inherited from BaseAggregateRoot)
   */
  get id(): UniqueEntityId {
    return this._id;
  }

  /**
   * Get the creation timestamp from metadata
   */
  get createdAt(): Date | undefined {
    return this.props.metadata?.timestamp;
  }

  /**
   * Get threat indicators from enrichment data
   */
  get indicators(): any[] {
    const indicators: any[] = [];

    // Extract indicators from enrichment data
    for (const enrichment of this.props.enrichmentData) {
      if (enrichment.data?.indicators) {
        indicators.push(...enrichment.data.indicators);
      }
      if (enrichment.data?.iocs) {
        indicators.push(...enrichment.data.iocs);
      }
    }

    return indicators;
  }

  /**
   * Get enrichment score for correlation readiness
   */
  get enrichmentScore(): number {
    return this.props.enrichmentQualityScore || this.props.confidenceLevel || 0;
  }

  /**
   * Check if event is ready for correlation
   */
  isReadyForCorrelation(): boolean {
    return this.props.enrichmentStatus === EnrichmentStatus.COMPLETED &&
           this.enrichmentScore >= 50;
  }

  // Business methods

  /**
   * Start enrichment process
   */
  startEnrichment(): void {
    if (this.props.enrichmentStatus !== EnrichmentStatus.PENDING) {
      throw new Error('Can only start enrichment for pending events');
    }

    this.props.enrichmentStatus = EnrichmentStatus.IN_PROGRESS;
    this.props.enrichmentStartedAt = new Date();
    this.props.enrichmentAttempts = (this.props.enrichmentAttempts || 0) + 1;

    this.validateInvariants();
  }

  /**
   * Complete enrichment process
   */
  completeEnrichment(result: EnrichmentResult): void {
    if (this.props.enrichmentStatus !== EnrichmentStatus.IN_PROGRESS) {
      throw new Error('Can only complete enrichment for in-progress events');
    }

    this.props.enrichmentStatus = result.success ? EnrichmentStatus.COMPLETED : EnrichmentStatus.PARTIAL;
    this.props.enrichmentCompletedAt = new Date();
    this.props.enrichmentResult = result;
    this.props.lastEnrichmentError = undefined;

    // Calculate enrichment quality score based on result
    this.calculateEnrichmentQualityScore(result);

    // Determine if manual review is required
    this.determineManualReviewRequirement();

    this.addDomainEvent(new EnrichedEventStatusChangedDomainEvent(
      this.id,
      {
        oldStatus: EnrichmentStatus.IN_PROGRESS,
        newStatus: this.props.enrichmentStatus,
        result,
        enrichmentQualityScore: this.props.enrichmentQualityScore,
        requiresManualReview: this.props.requiresManualReview || false,
      }
    ));

    this.validateInvariants();
  }

  /**
   * Fail enrichment process
   */
  failEnrichment(error: string, result?: Partial<EnrichmentResult>): void {
    if (this.props.enrichmentStatus !== EnrichmentStatus.IN_PROGRESS) {
      throw new Error('Can only fail enrichment for in-progress events');
    }

    this.props.enrichmentStatus = EnrichmentStatus.FAILED;
    this.props.lastEnrichmentError = error;
    
    if (result) {
      this.props.enrichmentResult = {
        success: false,
        appliedRules: result.appliedRules || [],
        failedRules: result.failedRules || [],
        warnings: result.warnings || [],
        errors: result.errors || [error],
        processingDurationMs: result.processingDurationMs || 0,
        confidenceScore: result.confidenceScore || 0,
        sourcesUsed: result.sourcesUsed || 0,
        dataPointsEnriched: result.dataPointsEnriched || 0,
      };
    }

    this.addDomainEvent(new EnrichedEventEnrichmentFailedDomainEvent(
      this.id,
      {
        normalizedEventId: this.props.normalizedEventId,
        error,
        attempt: this.enrichmentAttempts,
        maxAttemptsExceeded: this.hasExceededMaxEnrichmentAttempts(),
      }
    ));

    this.validateInvariants();
  }

  /**
   * Skip enrichment process
   */
  skipEnrichment(reason: string): void {
    if (![EnrichmentStatus.PENDING, EnrichmentStatus.FAILED].includes(this.props.enrichmentStatus)) {
      throw new Error('Can only skip enrichment for pending or failed events');
    }

    this.props.enrichmentStatus = EnrichmentStatus.SKIPPED;
    this.props.lastEnrichmentError = undefined;
    this.props.reviewNotes = reason;

    this.validateInvariants();
  }

  /**
   * Reset enrichment for retry
   */
  resetEnrichment(): void {
    if (this.hasExceededMaxEnrichmentAttempts()) {
      throw new Error('Cannot reset enrichment: maximum attempts exceeded');
    }

    this.props.enrichmentStatus = EnrichmentStatus.PENDING;
    this.props.enrichmentStartedAt = undefined;
    this.props.enrichmentCompletedAt = undefined;
    this.props.lastEnrichmentError = undefined;
    this.props.enrichmentResult = undefined;

    this.validateInvariants();
  }

  /**
   * Add enrichment data
   */
  addEnrichmentData(enrichmentData: EnrichmentData): void {
    if (this.props.enrichmentData.length >= EnrichedEvent.MAX_ENRICHMENT_DATA_SOURCES) {
      throw new Error(`Cannot add more than ${EnrichedEvent.MAX_ENRICHMENT_DATA_SOURCES} enrichment data sources`);
    }

    // Check for duplicate source and type combination
    const existingData = this.props.enrichmentData.find(
      data => data.source === enrichmentData.source && data.type === enrichmentData.type
    );

    if (existingData) {
      // Update existing data
      Object.assign(existingData, enrichmentData);
    } else {
      // Add new data
      this.props.enrichmentData.push(enrichmentData);
    }

    this.validateInvariants();
  }

  /**
   * Update enriched data
   */
  updateEnrichedData(enrichedData: Record<string, any>): void {
    this.props.enrichedData = { ...this.props.enrichedData, ...enrichedData };
  }

  /**
   * Add applied enrichment rule
   */
  addAppliedRule(rule: EnrichmentRule): void {
    const existingRule = this.props.appliedRules.find(r => r.id === rule.id);
    if (!existingRule) {
      this.props.appliedRules.push(rule);
    }
  }

  /**
   * Update threat intelligence score
   */
  updateThreatIntelScore(score: number): void {
    if (score < 0 || score > 100) {
      throw new Error('Threat intelligence score must be between 0 and 100');
    }

    this.props.threatIntelScore = score;
    this.determineManualReviewRequirement();
  }

  /**
   * Update asset context
   */
  updateAssetContext(context: Record<string, any>): void {
    this.props.assetContext = { ...this.props.assetContext, ...context };
  }

  /**
   * Update user context
   */
  updateUserContext(context: Record<string, any>): void {
    this.props.userContext = { ...this.props.userContext, ...context };
  }

  /**
   * Update network context
   */
  updateNetworkContext(context: Record<string, any>): void {
    this.props.networkContext = { ...this.props.networkContext, ...context };
  }

  /**
   * Update geolocation context
   */
  updateGeolocationContext(context: Record<string, any>): void {
    this.props.geolocationContext = { ...this.props.geolocationContext, ...context };
  }

  /**
   * Add reputation score
   */
  addReputationScore(source: string, score: number): void {
    if (score < 0 || score > 100) {
      throw new Error('Reputation score must be between 0 and 100');
    }

    if (!this.props.reputationScores) {
      this.props.reputationScores = {};
    }

    this.props.reputationScores[source] = score;
  }

  /**
   * Update enrichment quality score
   */
  updateEnrichmentQualityScore(score: number): void {
    if (score < 0 || score > 100) {
      throw new Error('Enrichment quality score must be between 0 and 100');
    }

    this.props.enrichmentQualityScore = score;
    this.determineManualReviewRequirement();
  }

  /**
   * Add validation errors
   */
  addValidationErrors(errors: string[]): void {
    const currentErrors = this.props.validationErrors || [];
    const newErrors = [...currentErrors, ...errors];

    if (newErrors.length > EnrichedEvent.MAX_VALIDATION_ERRORS) {
      throw new Error(`Cannot have more than ${EnrichedEvent.MAX_VALIDATION_ERRORS} validation errors`);
    }

    this.props.validationErrors = newErrors;
  }

  /**
   * Clear validation errors
   */
  clearValidationErrors(): void {
    this.props.validationErrors = [];
  }

  /**
   * Mark for manual review
   */
  markForManualReview(reason: string): void {
    this.props.requiresManualReview = true;
    this.props.reviewNotes = reason;
  }

  /**
   * Complete manual review
   */
  completeManualReview(reviewedBy: string, notes?: string): void {
    if (!this.props.requiresManualReview) {
      throw new Error('Event is not marked for manual review');
    }

    this.props.reviewedBy = reviewedBy;
    this.props.reviewedAt = new Date();
    if (notes) {
      this.props.reviewNotes = notes;
    }
  }

  // Query methods

  /**
   * Check if enrichment is completed
   */
  isEnrichmentCompleted(): boolean {
    return this.props.enrichmentStatus === EnrichmentStatus.COMPLETED;
  }

  /**
   * Check if enrichment failed
   */
  isEnrichmentFailed(): boolean {
    return this.props.enrichmentStatus === EnrichmentStatus.FAILED;
  }

  /**
   * Check if enrichment is in progress
   */
  isEnrichmentInProgress(): boolean {
    return this.props.enrichmentStatus === EnrichmentStatus.IN_PROGRESS;
  }

  /**
   * Check if enrichment was skipped
   */
  isEnrichmentSkipped(): boolean {
    return this.props.enrichmentStatus === EnrichmentStatus.SKIPPED;
  }

  /**
   * Check if enrichment is partial
   */
  isEnrichmentPartial(): boolean {
    return this.props.enrichmentStatus === EnrichmentStatus.PARTIAL;
  }

  /**
   * Check if event has high enrichment quality
   */
  hasHighEnrichmentQuality(): boolean {
    return (this.props.enrichmentQualityScore || 0) >= EnrichedEvent.MIN_ENRICHMENT_QUALITY_SCORE;
  }

  /**
   * Check if event has validation errors
   */
  hasValidationErrors(): boolean {
    return (this.props.validationErrors?.length || 0) > 0;
  }

  /**
   * Check if event has exceeded max enrichment attempts
   */
  hasExceededMaxEnrichmentAttempts(): boolean {
    return this.enrichmentAttempts >= EnrichedEvent.MAX_ENRICHMENT_ATTEMPTS;
  }

  /**
   * Check if event is ready for next processing stage
   */
  isReadyForNextStage(): boolean {
    return (this.isEnrichmentCompleted() || this.isEnrichmentPartial()) && 
           this.hasHighEnrichmentQuality() && 
           !this.hasValidationErrors() &&
           (!this.requiresManualReview || this.reviewedAt !== undefined);
  }

  /**
   * Check if event is high risk based on threat intelligence
   */
  isHighThreatRisk(): boolean {
    return (this.props.threatIntelScore || 0) >= EnrichedEvent.HIGH_RISK_REVIEW_THRESHOLD;
  }

  /**
   * Check if event has threat intelligence data
   */
  hasThreatIntelligence(): boolean {
    return this.props.enrichmentData.some(data => 
      [EnrichmentSource.COMMERCIAL_THREAT_INTEL, EnrichmentSource.OSINT, 
       EnrichmentSource.GOVERNMENT_INTEL, EnrichmentSource.TIP].includes(data.source)
    );
  }

  /**
   * Check if event has reputation data
   */
  hasReputationData(): boolean {
    return Object.keys(this.props.reputationScores || {}).length > 0;
  }

  /**
   * Check if event has geolocation data
   */
  hasGeolocationData(): boolean {
    return Object.keys(this.props.geolocationContext || {}).length > 0;
  }

  /**
   * Get enrichment duration
   */
  getEnrichmentDuration(): number | null {
    if (!this.props.enrichmentStartedAt) {
      return null;
    }

    const endTime = this.props.enrichmentCompletedAt || new Date();
    return endTime.getTime() - this.props.enrichmentStartedAt.getTime();
  }

  /**
   * Get applied rule names
   */
  getAppliedRuleNames(): string[] {
    return this.props.appliedRules.map(rule => rule.name);
  }

  /**
   * Check if specific rule was applied
   */
  hasAppliedRule(ruleId: string): boolean {
    return this.props.appliedRules.some(rule => rule.id === ruleId);
  }

  /**
   * Get enrichment data by source
   */
  getEnrichmentDataBySource(source: EnrichmentSource): EnrichmentData[] {
    return this.props.enrichmentData.filter(data => data.source === source);
  }

  /**
   * Get enrichment data by type
   */
  getEnrichmentDataByType(type: string): EnrichmentData[] {
    return this.props.enrichmentData.filter(data => data.type === type);
  }

  /**
   * Get average reputation score
   */
  getAverageReputationScore(): number | null {
    const scores = Object.values(this.props.reputationScores || {});
    if (scores.length === 0) return null;
    
    return scores.reduce((sum, score) => sum + score, 0) / scores.length;
  }

  // Private helper methods

  private calculateEnrichmentQualityScore(result: EnrichmentResult): void {
    let score = 100;

    // Reduce score for failed rules
    const failedRulesPenalty = result.failedRules.length * 15;
    score -= failedRulesPenalty;

    // Reduce score for warnings
    const warningsPenalty = result.warnings.length * 5;
    score -= warningsPenalty;

    // Reduce score for errors
    const errorsPenalty = result.errors.length * 20;
    score -= errorsPenalty;

    // Reduce score for low confidence
    if (result.confidenceScore < 70) {
      score -= (70 - result.confidenceScore);
    }

    // Reduce score for low source usage
    if (result.sourcesUsed < 3) {
      score -= (3 - result.sourcesUsed) * 10;
    }

    // Ensure score is within valid range
    this.props.enrichmentQualityScore = Math.max(0, Math.min(100, score));
  }

  private determineManualReviewRequirement(): void {
    // High threat intelligence risk requires manual review
    if (this.isHighThreatRisk()) {
      this.props.requiresManualReview = true;
      return;
    }

    // Low enrichment quality requires manual review
    if (!this.hasHighEnrichmentQuality()) {
      this.props.requiresManualReview = true;
      return;
    }

    // Events with validation errors require manual review
    if (this.hasValidationErrors()) {
      this.props.requiresManualReview = true;
      return;
    }

    // Critical events require manual review
    if (this.severity === EventSeverity.CRITICAL) {
      this.props.requiresManualReview = true;
      return;
    }

    // High risk events require manual review
    if ((this.props.riskScore || 0) >= 90) {
      this.props.requiresManualReview = true;
      return;
    }

    // Otherwise, no manual review required
    this.props.requiresManualReview = false;
  }

  /**
   * Get event summary for display
   */
  getSummary(): {
    id: string;
    normalizedEventId: string;
    title: string;
    type: EventType;
    severity: EventSeverity;
    status: EventStatus;
    enrichmentStatus: EnrichmentStatus;
    enrichmentQualityScore?: number;
    threatIntelScore?: number;
    riskScore?: number;
    appliedRulesCount: number;
    enrichmentDataCount: number;
    hasValidationErrors: boolean;
    requiresManualReview: boolean;
    isReadyForNextStage: boolean;
    hasThreatIntelligence: boolean;
    hasReputationData: boolean;
  } {
    return {
      id: this.id.toString(),
      normalizedEventId: this.props.normalizedEventId.toString(),
      title: this.props.title,
      type: this.props.type,
      severity: this.props.severity,
      status: this.props.status,
      enrichmentStatus: this.props.enrichmentStatus,
      enrichmentQualityScore: this.props.enrichmentQualityScore,
      threatIntelScore: this.props.threatIntelScore,
      riskScore: this.props.riskScore,
      appliedRulesCount: this.props.appliedRules.length,
      enrichmentDataCount: this.props.enrichmentData.length,
      hasValidationErrors: this.hasValidationErrors(),
      requiresManualReview: this.requiresManualReview,
      isReadyForNextStage: this.isReadyForNextStage(),
      hasThreatIntelligence: this.hasThreatIntelligence(),
      hasReputationData: this.hasReputationData(),
    };
  }

  /**
   * Convert to JSON representation
   */
  public toJSON(): Record<string, any> {
    return {
      ...super.toJSON(),
      normalizedEventId: this.props.normalizedEventId.toString(),
      metadata: this.props.metadata.toJSON(),
      type: this.props.type,
      severity: this.props.severity,
      status: this.props.status,
      processingStatus: this.props.processingStatus,
      enrichmentStatus: this.props.enrichmentStatus,
      normalizedData: this.props.normalizedData,
      enrichedData: this.props.enrichedData,
      title: this.props.title,
      description: this.props.description,
      tags: this.props.tags,
      riskScore: this.props.riskScore,
      confidenceLevel: this.props.confidenceLevel,
      attributes: this.props.attributes,
      correlationId: this.props.correlationId,
      parentEventId: this.props.parentEventId?.toString(),
      appliedRules: this.props.appliedRules,
      enrichmentData: this.props.enrichmentData,
      enrichmentResult: this.props.enrichmentResult,
      enrichmentStartedAt: this.props.enrichmentStartedAt?.toISOString(),
      enrichmentCompletedAt: this.props.enrichmentCompletedAt?.toISOString(),
      enrichmentAttempts: this.props.enrichmentAttempts,
      lastEnrichmentError: this.props.lastEnrichmentError,
      threatIntelScore: this.props.threatIntelScore,
      assetContext: this.props.assetContext,
      userContext: this.props.userContext,
      networkContext: this.props.networkContext,
      geolocationContext: this.props.geolocationContext,
      reputationScores: this.props.reputationScores,
      requiresManualReview: this.props.requiresManualReview,
      reviewNotes: this.props.reviewNotes,
      reviewedBy: this.props.reviewedBy,
      reviewedAt: this.props.reviewedAt?.toISOString(),
      enrichmentQualityScore: this.props.enrichmentQualityScore,
      validationErrors: this.props.validationErrors,
      summary: this.getSummary(),
    };
  }
}