{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\communication\\data-serializer.service.ts", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAC/C,2CAA6B;AAC7B,+BAAiC;AAEjC;;;;;;GAMG;AAEI,IAAM,qBAAqB,6BAA3B,MAAM,qBAAqB;IAYhC,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAXxC,WAAM,GAAG,IAAI,eAAM,CAAC,uBAAqB,CAAC,IAAI,CAAC,CAAC;QAKjE,oCAAoC;QACnB,SAAI,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,WAAM,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAChC,YAAO,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAClC,YAAO,GAAG,IAAA,gBAAS,EAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAGjD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAU,sCAAsC,EAAE,IAAI,CAAC,CAAC;QACxG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,oCAAoC,EAAE,CAAC,CAAC,CAAC;QAChG,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,wCAAwC,EAAE,IAAI,CAAC,CAAC;IAC7G,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CACnB,IAAS,EACT,UAAgC,EAAE;QAElC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;QAErD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,yBAAyB;YACzB,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;YACzE,MAAM,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YAE3D,IAAI,gBAAwB,CAAC;YAC7B,IAAI,UAAU,GAAG,KAAK,CAAC;YAEvB,0DAA0D;YAC1D,IAAI,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,OAAO,CAAC,EAAE,CAAC;gBAC/C,gBAAgB,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC;gBACrG,UAAU,GAAG,IAAI,CAAC;gBAClB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,YAAY,OAAO,gBAAgB,CAAC,MAAM,QAAQ,CAAC,CAAC;YACjG,CAAC;iBAAM,CAAC;gBACN,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YACrD,CAAC;YAED,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE9C,OAAO;gBACL,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,MAAM;gBACd,UAAU;gBACV,eAAe,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;gBAC7E,YAAY;gBACZ,cAAc,EAAE,gBAAgB,CAAC,MAAM;gBACvC,gBAAgB,EAAE,UAAU,CAAC,CAAC,CAAC,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACzE,cAAc;gBACd,QAAQ,EAAE;oBACR,QAAQ,EAAE,MAAM;oBAChB,GAAG,OAAO,CAAC,QAAQ;iBACpB;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YAC7D,MAAM,IAAI,kBAAkB,CAAC,8BAA8B,KAAK,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QAC7F,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CACvB,cAAuC,EACvC,UAAkC,EAAE;QAEpC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,CAAC,CAAC;QAEzD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,IAAI,MAAc,CAAC;YACnB,IAAI,UAAU,GAAG,KAAK,CAAC;YACvB,IAAI,eAA4C,CAAC;YAEjD,+BAA+B;YAC/B,IAAI,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;gBACpC,MAAM,GAAG,cAAc,CAAC;YAC1B,CAAC;iBAAM,CAAC;gBACN,MAAM,GAAG,cAAc,CAAC,IAAI,CAAC;gBAC7B,UAAU,GAAG,cAAc,CAAC,UAAU,IAAI,KAAK,CAAC;gBAChD,eAAe,GAAG,cAAc,CAAC,eAAe,CAAC;YACnD,CAAC;YAED,uBAAuB;YACvB,IAAI,UAAU,IAAI,eAAe,EAAE,CAAC;gBAClC,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;gBAC5D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,MAAM,CAAC,MAAM,QAAQ,CAAC,CAAC;YACtE,CAAC;YAED,aAAa;YACb,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAC3C,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;YAErD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC9C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,cAAc,IAAI,CAAC,CAAC;YAE3E,OAAO,IAAI,CAAC;QAEd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,IAAI,kBAAkB,CAAC,gCAAgC,KAAK,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QAC/F,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CACvB,IAAS,EACT,MAAc,EACd,UAAgC,EAAE;QAElC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4DAA4D,MAAM,EAAE,CAAC,CAAC;QAExF,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,sCAAsC;YACtC,wEAAwE;YACxE,MAAM,gBAAgB,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YACtE,MAAM,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC;YAE7C,IAAI,gBAAwB,CAAC;YAC7B,IAAI,UAAU,GAAG,KAAK,CAAC;YAEvB,0DAA0D;YAC1D,IAAI,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,OAAO,CAAC,EAAE,CAAC;gBAC/C,gBAAgB,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC;gBACtF,UAAU,GAAG,IAAI,CAAC;gBAClB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,YAAY,OAAO,gBAAgB,CAAC,MAAM,QAAQ,CAAC,CAAC;YACrG,CAAC;iBAAM,CAAC;gBACN,gBAAgB,GAAG,gBAAgB,CAAC;YACtC,CAAC;YAED,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE9C,OAAO;gBACL,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,UAAU;gBAClB,MAAM;gBACN,UAAU;gBACV,eAAe,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;gBAC7E,YAAY;gBACZ,cAAc,EAAE,gBAAgB,CAAC,MAAM;gBACvC,gBAAgB,EAAE,UAAU,CAAC,CAAC,CAAC,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACzE,cAAc;gBACd,QAAQ,EAAE;oBACR,MAAM;oBACN,GAAG,OAAO,CAAC,QAAQ;iBACpB;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;YACzE,MAAM,IAAI,kBAAkB,CAAC,kCAAkC,KAAK,CAAC,OAAO,EAAE,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;QACrG,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAC3B,cAAuC,EACvC,MAAc,EACd,UAAkC,EAAE;QAEpC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gEAAgE,MAAM,EAAE,CAAC,CAAC;QAE5F,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,IAAI,MAAc,CAAC;YACnB,IAAI,UAAU,GAAG,KAAK,CAAC;YACvB,IAAI,eAA4C,CAAC;YAEjD,+BAA+B;YAC/B,IAAI,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;gBACpC,MAAM,GAAG,cAAc,CAAC;YAC1B,CAAC;iBAAM,CAAC;gBACN,MAAM,GAAG,cAAc,CAAC,IAAI,CAAC;gBAC7B,UAAU,GAAG,cAAc,CAAC,UAAU,IAAI,KAAK,CAAC;gBAChD,eAAe,GAAG,cAAc,CAAC,eAAe,CAAC;YACnD,CAAC;YAED,uBAAuB;YACvB,IAAI,UAAU,IAAI,eAAe,EAAE,CAAC;gBAClC,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;gBAC5D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,MAAM,CAAC,MAAM,QAAQ,CAAC,CAAC;YAC1E,CAAC;YAED,wCAAwC;YACxC,MAAM,IAAI,GAAG,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAE9D,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC9C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yCAAyC,cAAc,IAAI,CAAC,CAAC;YAE/E,OAAO,IAAI,CAAC;QAEd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAC;YACxE,MAAM,IAAI,kBAAkB,CAAC,oCAAoC,KAAK,CAAC,OAAO,EAAE,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;QACvG,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACrB,IAAS,EACT,WAA2B,QAAQ,EACnC,UAAgC,EAAE;QAElC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oDAAoD,QAAQ,EAAE,CAAC,CAAC;QAElF,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,yBAAyB;YACzB,IAAI,MAAc,CAAC;YACnB,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC1B,MAAM,GAAG,IAAI,CAAC;YAChB,CAAC;iBAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACpC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YACrC,CAAC;iBAAM,CAAC;gBACN,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC;YACrD,CAAC;YAED,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC;YACnC,IAAI,gBAAwB,CAAC;YAC7B,IAAI,UAAU,GAAG,KAAK,CAAC;YAEvB,0DAA0D;YAC1D,IAAI,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,OAAO,CAAC,EAAE,CAAC;gBAC/C,gBAAgB,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC;gBAC5E,UAAU,GAAG,IAAI,CAAC;gBAClB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,YAAY,OAAO,gBAAgB,CAAC,MAAM,QAAQ,CAAC,CAAC;YACnG,CAAC;iBAAM,CAAC;gBACN,gBAAgB,GAAG,MAAM,CAAC;YAC5B,CAAC;YAED,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE9C,OAAO;gBACL,IAAI,EAAE,gBAAgB;gBACtB,MAAM,EAAE,QAAQ;gBAChB,UAAU;gBACV,eAAe,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;gBAC7E,YAAY;gBACZ,cAAc,EAAE,gBAAgB,CAAC,MAAM;gBACvC,gBAAgB,EAAE,UAAU,CAAC,CAAC,CAAC,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACzE,cAAc;gBACd,QAAQ,EAAE;oBACR,QAAQ;oBACR,GAAG,OAAO,CAAC,QAAQ;iBACpB;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;YACtE,MAAM,IAAI,kBAAkB,CAAC,gCAAgC,KAAK,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QACjG,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACzB,cAAuC,EACvC,WAA2B,QAAQ,EACnC,UAAkC,EAAE;QAEpC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wDAAwD,QAAQ,EAAE,CAAC,CAAC;QAEtF,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,IAAI,MAAc,CAAC;YACnB,IAAI,UAAU,GAAG,KAAK,CAAC;YACvB,IAAI,eAA4C,CAAC;YAEjD,+BAA+B;YAC/B,IAAI,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;gBACpC,MAAM,GAAG,cAAc,CAAC;YAC1B,CAAC;iBAAM,CAAC;gBACN,MAAM,GAAG,cAAc,CAAC,IAAI,CAAC;gBAC7B,UAAU,GAAG,cAAc,CAAC,UAAU,IAAI,KAAK,CAAC;gBAChD,eAAe,GAAG,cAAc,CAAC,eAAe,CAAC;YACnD,CAAC;YAED,uBAAuB;YACvB,IAAI,UAAU,IAAI,eAAe,EAAE,CAAC;gBAClC,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;gBAC5D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,MAAM,CAAC,MAAM,QAAQ,CAAC,CAAC;YACxE,CAAC;YAED,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC9C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,cAAc,IAAI,CAAC,CAAC;YAE7E,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC9D,MAAM,IAAI,kBAAkB,CAAC,kCAAkC,KAAK,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QACnG,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAChB,IAAY,EACZ,kBAAmC,MAAM;QAEzC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,eAAe,EAAE,CAAC,CAAC;QAE/D,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAEjD,QAAQ,eAAe,EAAE,CAAC;gBACxB,KAAK,MAAM;oBACT,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBACxC,KAAK,SAAS;oBACZ,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBAC3C;oBACE,MAAM,IAAI,KAAK,CAAC,iCAAiC,eAAe,EAAE,CAAC,CAAC;YACxE,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,eAAe,EAAE,EAAE,KAAK,CAAC,CAAC;YAC7E,MAAM,IAAI,kBAAkB,CAAC,uBAAuB,KAAK,CAAC,OAAO,EAAE,EAAE,eAAe,EAAE,KAAK,CAAC,CAAC;QAC/F,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAClB,IAAY,EACZ,eAAgC;QAEhC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,eAAe,EAAE,CAAC,CAAC;QAEjE,IAAI,CAAC;YACH,QAAQ,eAAe,EAAE,CAAC;gBACxB,KAAK,MAAM;oBACT,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACjC,KAAK,SAAS;oBACZ,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAClC;oBACE,MAAM,IAAI,KAAK,CAAC,iCAAiC,eAAe,EAAE,CAAC,CAAC;YACxE,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,eAAe,EAAE,EAAE,KAAK,CAAC,CAAC;YAC/E,MAAM,IAAI,kBAAkB,CAAC,yBAAyB,KAAK,CAAC,OAAO,EAAE,EAAE,eAAe,EAAE,KAAK,CAAC,CAAC;QACjG,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAClB,KAAwF,EACxF,gBAAsC,EAAE;QAExC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,KAAK,CAAC,MAAM,QAAQ,CAAC,CAAC;QAE7D,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,OAAO,GAAqB,EAAE,CAAC;YACrC,MAAM,MAAM,GAA2C,EAAE,CAAC;YAE1D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACtB,MAAM,OAAO,GAAG,EAAE,GAAG,aAAa,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAEtD,IAAI,CAAC;oBACH,IAAI,MAAsB,CAAC;oBAE3B,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;wBACpB,KAAK,MAAM;4BACT,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;4BACxD,MAAM;wBACR,KAAK,UAAU;4BACb,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,IAAI,SAAS,EAAE,OAAO,CAAC,CAAC;4BACzF,MAAM;wBACR,KAAK,QAAQ;4BACX,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,QAAQ,IAAI,QAAQ,EAAE,OAAO,CAAC,CAAC;4BACxF,MAAM;wBACR;4BACE,MAAM,IAAI,KAAK,CAAC,uBAAuB,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;oBAC1D,CAAC;oBAED,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAEvB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;oBACjC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,uCAAuC;gBAC7D,CAAC;YACH,CAAC;YAED,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC9C,MAAM,iBAAiB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;YAC7F,MAAM,mBAAmB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;YAEjG,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,OAAO,EAAE;oBACP,KAAK,EAAE,KAAK,CAAC,MAAM;oBACnB,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM;oBACzC,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,iBAAiB;oBACjB,mBAAmB;oBACnB,uBAAuB,EAAE,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC;oBAC5F,cAAc;iBACf;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,IAAI,kBAAkB,CAAC,+BAA+B,KAAK,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QAC/F,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CACpB,KAA8G,EAC9G,gBAAwC,EAAE;QAE1C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,KAAK,CAAC,MAAM,QAAQ,CAAC,CAAC;QAE/D,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,MAAM,OAAO,GAAU,EAAE,CAAC;YAC1B,MAAM,MAAM,GAA2C,EAAE,CAAC;YAE1D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACtB,MAAM,OAAO,GAAG,EAAE,GAAG,aAAa,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAEtD,IAAI,CAAC;oBACH,IAAI,MAAW,CAAC;oBAEhB,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;wBACpB,KAAK,MAAM;4BACT,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;4BAC5D,MAAM;wBACR,KAAK,UAAU;4BACb,MAAM,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,IAAI,SAAS,EAAE,OAAO,CAAC,CAAC;4BAC7F,MAAM;wBACR,KAAK,QAAQ;4BACX,MAAM,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,QAAQ,IAAI,QAAQ,EAAE,OAAO,CAAC,CAAC;4BAC5F,MAAM;wBACR;4BACE,MAAM,IAAI,KAAK,CAAC,uBAAuB,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;oBAC1D,CAAC;oBAED,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAEvB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;oBACjC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,yCAAyC;gBAC/D,CAAC;YACH,CAAC;YAED,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE9C,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,OAAO,EAAE;oBACP,KAAK,EAAE,KAAK,CAAC,MAAM;oBACnB,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,MAAM;oBAClD,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,cAAc;iBACf;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YAC7D,MAAM,IAAI,kBAAkB,CAAC,iCAAiC,KAAK,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QACjG,CAAC;IACH,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO;YACL,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,gBAAgB,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC;YAChD,yBAAyB,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;YAC9C,kBAAkB,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC;SAC9C,CAAC;IACJ,CAAC;IAED,yBAAyB;IAEjB,cAAc,CAAC,QAAgB,EAAE,OAA6B;QACpE,IAAI,OAAO,CAAC,gBAAgB,KAAK,SAAS,EAAE,CAAC;YAC3C,OAAO,OAAO,CAAC,gBAAgB,CAAC;QAClC,CAAC;QAED,OAAO,IAAI,CAAC,kBAAkB,IAAI,QAAQ,IAAI,IAAI,CAAC,oBAAoB,CAAC;IAC1E,CAAC;IAEO,yBAAyB,CAAC,IAAS,EAAE,MAAc;QACzD,sCAAsC;QACtC,wEAAwE;QACxE,MAAM,QAAQ,GAAG;YACf,MAAM;YACN,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YAC1B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC;QAEF,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,CAAC;IACvD,CAAC;IAEO,2BAA2B,CAAC,MAAc,EAAE,MAAc;QAChE,wCAAwC;QACxC,wEAAwE;QACxE,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;QAErD,IAAI,QAAQ,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,6BAA6B,MAAM,SAAS,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;QACjF,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;CACF,CAAA;AA9hBY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;yDAaiC,sBAAa,oBAAb,sBAAa;GAZ9C,qBAAqB,CA8hBjC;AAsED,MAAM,kBAAmB,SAAQ,KAAK;IACpC,YACE,OAAe,EACC,MAAe,EACf,aAAmB;QAEnC,KAAK,CAAC,OAAO,CAAC,CAAC;QAHC,WAAM,GAAN,MAAM,CAAS;QACf,kBAAa,GAAb,aAAa,CAAM;QAGnC,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAC;IACnC,CAAC;CACF", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\communication\\data-serializer.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport * as zlib from 'zlib';\r\nimport { promisify } from 'util';\r\n\r\n/**\r\n * Data Serializer Service\r\n * \r\n * Handles efficient data transformation for AI operations.\r\n * Supports multiple serialization formats (JSON, Protocol Buffers),\r\n * data compression, and optimization for high-performance AI workflows.\r\n */\r\n@Injectable()\r\nexport class DataSerializerService {\r\n  private readonly logger = new Logger(DataSerializerService.name);\r\n  private readonly compressionEnabled: boolean;\r\n  private readonly compressionLevel: number;\r\n  private readonly compressionThreshold: number;\r\n\r\n  // Promisified compression functions\r\n  private readonly gzip = promisify(zlib.gzip);\r\n  private readonly gunzip = promisify(zlib.gunzip);\r\n  private readonly deflate = promisify(zlib.deflate);\r\n  private readonly inflate = promisify(zlib.inflate);\r\n\r\n  constructor(private readonly configService: ConfigService) {\r\n    this.compressionEnabled = this.configService.get<boolean>('ai.serialization.compression.enabled', true);\r\n    this.compressionLevel = this.configService.get<number>('ai.serialization.compression.level', 6);\r\n    this.compressionThreshold = this.configService.get<number>('ai.serialization.compression.threshold', 1024);\r\n  }\r\n\r\n  /**\r\n   * Serializes data to JSON format with optional compression\r\n   */\r\n  async serializeToJson(\r\n    data: any,\r\n    options: SerializationOptions = {}\r\n  ): Promise<SerializedData> {\r\n    this.logger.debug('Serializing data to JSON format');\r\n\r\n    try {\r\n      const startTime = Date.now();\r\n      \r\n      // Convert to JSON string\r\n      const jsonString = JSON.stringify(data, options.replacer, options.space);\r\n      const originalSize = Buffer.byteLength(jsonString, 'utf8');\r\n\r\n      let serializedBuffer: Buffer;\r\n      let compressed = false;\r\n\r\n      // Apply compression if enabled and data exceeds threshold\r\n      if (this.shouldCompress(originalSize, options)) {\r\n        serializedBuffer = await this.compressData(Buffer.from(jsonString, 'utf8'), options.compressionType);\r\n        compressed = true;\r\n        this.logger.debug(`JSON data compressed: ${originalSize} -> ${serializedBuffer.length} bytes`);\r\n      } else {\r\n        serializedBuffer = Buffer.from(jsonString, 'utf8');\r\n      }\r\n\r\n      const processingTime = Date.now() - startTime;\r\n\r\n      return {\r\n        data: serializedBuffer,\r\n        format: 'json',\r\n        compressed,\r\n        compressionType: compressed ? (options.compressionType || 'gzip') : undefined,\r\n        originalSize,\r\n        compressedSize: serializedBuffer.length,\r\n        compressionRatio: compressed ? originalSize / serializedBuffer.length : 1,\r\n        processingTime,\r\n        metadata: {\r\n          encoding: 'utf8',\r\n          ...options.metadata,\r\n        },\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error('Failed to serialize data to JSON', error);\r\n      throw new SerializationError(`JSON serialization failed: ${error.message}`, 'json', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Deserializes JSON data with optional decompression\r\n   */\r\n  async deserializeFromJson(\r\n    serializedData: SerializedData | Buffer,\r\n    options: DeserializationOptions = {}\r\n  ): Promise<any> {\r\n    this.logger.debug('Deserializing data from JSON format');\r\n\r\n    try {\r\n      const startTime = Date.now();\r\n      let buffer: Buffer;\r\n      let compressed = false;\r\n      let compressionType: CompressionType | undefined;\r\n\r\n      // Handle different input types\r\n      if (Buffer.isBuffer(serializedData)) {\r\n        buffer = serializedData;\r\n      } else {\r\n        buffer = serializedData.data;\r\n        compressed = serializedData.compressed || false;\r\n        compressionType = serializedData.compressionType;\r\n      }\r\n\r\n      // Decompress if needed\r\n      if (compressed && compressionType) {\r\n        buffer = await this.decompressData(buffer, compressionType);\r\n        this.logger.debug(`JSON data decompressed: ${buffer.length} bytes`);\r\n      }\r\n\r\n      // Parse JSON\r\n      const jsonString = buffer.toString('utf8');\r\n      const data = JSON.parse(jsonString, options.reviver);\r\n\r\n      const processingTime = Date.now() - startTime;\r\n      this.logger.debug(`JSON deserialization completed in ${processingTime}ms`);\r\n\r\n      return data;\r\n\r\n    } catch (error) {\r\n      this.logger.error('Failed to deserialize JSON data', error);\r\n      throw new SerializationError(`JSON deserialization failed: ${error.message}`, 'json', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Serializes data to Protocol Buffers format (mock implementation)\r\n   */\r\n  async serializeToProtobuf(\r\n    data: any,\r\n    schema: string,\r\n    options: SerializationOptions = {}\r\n  ): Promise<SerializedData> {\r\n    this.logger.debug(`Serializing data to Protocol Buffers format with schema: ${schema}`);\r\n\r\n    try {\r\n      const startTime = Date.now();\r\n\r\n      // Mock Protocol Buffers serialization\r\n      // In real implementation, this would use protobuf.js or similar library\r\n      const mockProtobufData = this.mockProtobufSerialization(data, schema);\r\n      const originalSize = mockProtobufData.length;\r\n\r\n      let serializedBuffer: Buffer;\r\n      let compressed = false;\r\n\r\n      // Apply compression if enabled and data exceeds threshold\r\n      if (this.shouldCompress(originalSize, options)) {\r\n        serializedBuffer = await this.compressData(mockProtobufData, options.compressionType);\r\n        compressed = true;\r\n        this.logger.debug(`Protobuf data compressed: ${originalSize} -> ${serializedBuffer.length} bytes`);\r\n      } else {\r\n        serializedBuffer = mockProtobufData;\r\n      }\r\n\r\n      const processingTime = Date.now() - startTime;\r\n\r\n      return {\r\n        data: serializedBuffer,\r\n        format: 'protobuf',\r\n        schema,\r\n        compressed,\r\n        compressionType: compressed ? (options.compressionType || 'gzip') : undefined,\r\n        originalSize,\r\n        compressedSize: serializedBuffer.length,\r\n        compressionRatio: compressed ? originalSize / serializedBuffer.length : 1,\r\n        processingTime,\r\n        metadata: {\r\n          schema,\r\n          ...options.metadata,\r\n        },\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error('Failed to serialize data to Protocol Buffers', error);\r\n      throw new SerializationError(`Protobuf serialization failed: ${error.message}`, 'protobuf', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Deserializes Protocol Buffers data (mock implementation)\r\n   */\r\n  async deserializeFromProtobuf(\r\n    serializedData: SerializedData | Buffer,\r\n    schema: string,\r\n    options: DeserializationOptions = {}\r\n  ): Promise<any> {\r\n    this.logger.debug(`Deserializing data from Protocol Buffers format with schema: ${schema}`);\r\n\r\n    try {\r\n      const startTime = Date.now();\r\n      let buffer: Buffer;\r\n      let compressed = false;\r\n      let compressionType: CompressionType | undefined;\r\n\r\n      // Handle different input types\r\n      if (Buffer.isBuffer(serializedData)) {\r\n        buffer = serializedData;\r\n      } else {\r\n        buffer = serializedData.data;\r\n        compressed = serializedData.compressed || false;\r\n        compressionType = serializedData.compressionType;\r\n      }\r\n\r\n      // Decompress if needed\r\n      if (compressed && compressionType) {\r\n        buffer = await this.decompressData(buffer, compressionType);\r\n        this.logger.debug(`Protobuf data decompressed: ${buffer.length} bytes`);\r\n      }\r\n\r\n      // Mock Protocol Buffers deserialization\r\n      const data = this.mockProtobufDeserialization(buffer, schema);\r\n\r\n      const processingTime = Date.now() - startTime;\r\n      this.logger.debug(`Protobuf deserialization completed in ${processingTime}ms`);\r\n\r\n      return data;\r\n\r\n    } catch (error) {\r\n      this.logger.error('Failed to deserialize Protocol Buffers data', error);\r\n      throw new SerializationError(`Protobuf deserialization failed: ${error.message}`, 'protobuf', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Serializes data to binary format with custom encoding\r\n   */\r\n  async serializeToBinary(\r\n    data: any,\r\n    encoding: BinaryEncoding = 'base64',\r\n    options: SerializationOptions = {}\r\n  ): Promise<SerializedData> {\r\n    this.logger.debug(`Serializing data to binary format with encoding: ${encoding}`);\r\n\r\n    try {\r\n      const startTime = Date.now();\r\n\r\n      // Convert data to buffer\r\n      let buffer: Buffer;\r\n      if (Buffer.isBuffer(data)) {\r\n        buffer = data;\r\n      } else if (typeof data === 'string') {\r\n        buffer = Buffer.from(data, 'utf8');\r\n      } else {\r\n        buffer = Buffer.from(JSON.stringify(data), 'utf8');\r\n      }\r\n\r\n      const originalSize = buffer.length;\r\n      let serializedBuffer: Buffer;\r\n      let compressed = false;\r\n\r\n      // Apply compression if enabled and data exceeds threshold\r\n      if (this.shouldCompress(originalSize, options)) {\r\n        serializedBuffer = await this.compressData(buffer, options.compressionType);\r\n        compressed = true;\r\n        this.logger.debug(`Binary data compressed: ${originalSize} -> ${serializedBuffer.length} bytes`);\r\n      } else {\r\n        serializedBuffer = buffer;\r\n      }\r\n\r\n      const processingTime = Date.now() - startTime;\r\n\r\n      return {\r\n        data: serializedBuffer,\r\n        format: 'binary',\r\n        compressed,\r\n        compressionType: compressed ? (options.compressionType || 'gzip') : undefined,\r\n        originalSize,\r\n        compressedSize: serializedBuffer.length,\r\n        compressionRatio: compressed ? originalSize / serializedBuffer.length : 1,\r\n        processingTime,\r\n        metadata: {\r\n          encoding,\r\n          ...options.metadata,\r\n        },\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error('Failed to serialize data to binary format', error);\r\n      throw new SerializationError(`Binary serialization failed: ${error.message}`, 'binary', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Deserializes binary data with custom encoding\r\n   */\r\n  async deserializeFromBinary(\r\n    serializedData: SerializedData | Buffer,\r\n    encoding: BinaryEncoding = 'base64',\r\n    options: DeserializationOptions = {}\r\n  ): Promise<Buffer> {\r\n    this.logger.debug(`Deserializing data from binary format with encoding: ${encoding}`);\r\n\r\n    try {\r\n      const startTime = Date.now();\r\n      let buffer: Buffer;\r\n      let compressed = false;\r\n      let compressionType: CompressionType | undefined;\r\n\r\n      // Handle different input types\r\n      if (Buffer.isBuffer(serializedData)) {\r\n        buffer = serializedData;\r\n      } else {\r\n        buffer = serializedData.data;\r\n        compressed = serializedData.compressed || false;\r\n        compressionType = serializedData.compressionType;\r\n      }\r\n\r\n      // Decompress if needed\r\n      if (compressed && compressionType) {\r\n        buffer = await this.decompressData(buffer, compressionType);\r\n        this.logger.debug(`Binary data decompressed: ${buffer.length} bytes`);\r\n      }\r\n\r\n      const processingTime = Date.now() - startTime;\r\n      this.logger.debug(`Binary deserialization completed in ${processingTime}ms`);\r\n\r\n      return buffer;\r\n\r\n    } catch (error) {\r\n      this.logger.error('Failed to deserialize binary data', error);\r\n      throw new SerializationError(`Binary deserialization failed: ${error.message}`, 'binary', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Compresses data using specified compression algorithm\r\n   */\r\n  async compressData(\r\n    data: Buffer,\r\n    compressionType: CompressionType = 'gzip'\r\n  ): Promise<Buffer> {\r\n    this.logger.debug(`Compressing data using ${compressionType}`);\r\n\r\n    try {\r\n      const options = { level: this.compressionLevel };\r\n\r\n      switch (compressionType) {\r\n        case 'gzip':\r\n          return await this.gzip(data, options);\r\n        case 'deflate':\r\n          return await this.deflate(data, options);\r\n        default:\r\n          throw new Error(`Unsupported compression type: ${compressionType}`);\r\n      }\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to compress data using ${compressionType}`, error);\r\n      throw new SerializationError(`Compression failed: ${error.message}`, compressionType, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Decompresses data using specified compression algorithm\r\n   */\r\n  async decompressData(\r\n    data: Buffer,\r\n    compressionType: CompressionType\r\n  ): Promise<Buffer> {\r\n    this.logger.debug(`Decompressing data using ${compressionType}`);\r\n\r\n    try {\r\n      switch (compressionType) {\r\n        case 'gzip':\r\n          return await this.gunzip(data);\r\n        case 'deflate':\r\n          return await this.inflate(data);\r\n        default:\r\n          throw new Error(`Unsupported compression type: ${compressionType}`);\r\n      }\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to decompress data using ${compressionType}`, error);\r\n      throw new SerializationError(`Decompression failed: ${error.message}`, compressionType, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Batch serializes multiple data items\r\n   */\r\n  async batchSerialize(\r\n    items: Array<{ data: any; format: SerializationFormat; options?: SerializationOptions }>,\r\n    globalOptions: SerializationOptions = {}\r\n  ): Promise<BatchSerializationResult> {\r\n    this.logger.debug(`Batch serializing ${items.length} items`);\r\n\r\n    try {\r\n      const startTime = Date.now();\r\n      const results: SerializedData[] = [];\r\n      const errors: Array<{ index: number; error: Error }> = [];\r\n\r\n      for (let i = 0; i < items.length; i++) {\r\n        const item = items[i];\r\n        const options = { ...globalOptions, ...item.options };\r\n\r\n        try {\r\n          let result: SerializedData;\r\n\r\n          switch (item.format) {\r\n            case 'json':\r\n              result = await this.serializeToJson(item.data, options);\r\n              break;\r\n            case 'protobuf':\r\n              result = await this.serializeToProtobuf(item.data, options.schema || 'default', options);\r\n              break;\r\n            case 'binary':\r\n              result = await this.serializeToBinary(item.data, options.encoding || 'base64', options);\r\n              break;\r\n            default:\r\n              throw new Error(`Unsupported format: ${item.format}`);\r\n          }\r\n\r\n          results.push(result);\r\n\r\n        } catch (error) {\r\n          errors.push({ index: i, error });\r\n          results.push(null); // Placeholder for failed serialization\r\n        }\r\n      }\r\n\r\n      const processingTime = Date.now() - startTime;\r\n      const totalOriginalSize = results.filter(r => r).reduce((sum, r) => sum + r.originalSize, 0);\r\n      const totalCompressedSize = results.filter(r => r).reduce((sum, r) => sum + r.compressedSize, 0);\r\n\r\n      return {\r\n        results,\r\n        errors,\r\n        summary: {\r\n          total: items.length,\r\n          successful: results.filter(r => r).length,\r\n          failed: errors.length,\r\n          totalOriginalSize,\r\n          totalCompressedSize,\r\n          overallCompressionRatio: totalOriginalSize > 0 ? totalOriginalSize / totalCompressedSize : 1,\r\n          processingTime,\r\n        },\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error('Failed to batch serialize data', error);\r\n      throw new SerializationError(`Batch serialization failed: ${error.message}`, 'batch', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Batch deserializes multiple data items\r\n   */\r\n  async batchDeserialize(\r\n    items: Array<{ data: SerializedData | Buffer; format: SerializationFormat; options?: DeserializationOptions }>,\r\n    globalOptions: DeserializationOptions = {}\r\n  ): Promise<BatchDeserializationResult> {\r\n    this.logger.debug(`Batch deserializing ${items.length} items`);\r\n\r\n    try {\r\n      const startTime = Date.now();\r\n      const results: any[] = [];\r\n      const errors: Array<{ index: number; error: Error }> = [];\r\n\r\n      for (let i = 0; i < items.length; i++) {\r\n        const item = items[i];\r\n        const options = { ...globalOptions, ...item.options };\r\n\r\n        try {\r\n          let result: any;\r\n\r\n          switch (item.format) {\r\n            case 'json':\r\n              result = await this.deserializeFromJson(item.data, options);\r\n              break;\r\n            case 'protobuf':\r\n              result = await this.deserializeFromProtobuf(item.data, options.schema || 'default', options);\r\n              break;\r\n            case 'binary':\r\n              result = await this.deserializeFromBinary(item.data, options.encoding || 'base64', options);\r\n              break;\r\n            default:\r\n              throw new Error(`Unsupported format: ${item.format}`);\r\n          }\r\n\r\n          results.push(result);\r\n\r\n        } catch (error) {\r\n          errors.push({ index: i, error });\r\n          results.push(null); // Placeholder for failed deserialization\r\n        }\r\n      }\r\n\r\n      const processingTime = Date.now() - startTime;\r\n\r\n      return {\r\n        results,\r\n        errors,\r\n        summary: {\r\n          total: items.length,\r\n          successful: results.filter(r => r !== null).length,\r\n          failed: errors.length,\r\n          processingTime,\r\n        },\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error('Failed to batch deserialize data', error);\r\n      throw new SerializationError(`Batch deserialization failed: ${error.message}`, 'batch', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Gets serialization statistics\r\n   */\r\n  getSerializationStats(): SerializationStats {\r\n    return {\r\n      compressionEnabled: this.compressionEnabled,\r\n      compressionLevel: this.compressionLevel,\r\n      compressionThreshold: this.compressionThreshold,\r\n      supportedFormats: ['json', 'protobuf', 'binary'],\r\n      supportedCompressionTypes: ['gzip', 'deflate'],\r\n      supportedEncodings: ['base64', 'hex', 'utf8'],\r\n    };\r\n  }\r\n\r\n  // Private helper methods\r\n\r\n  private shouldCompress(dataSize: number, options: SerializationOptions): boolean {\r\n    if (options.forceCompression !== undefined) {\r\n      return options.forceCompression;\r\n    }\r\n\r\n    return this.compressionEnabled && dataSize >= this.compressionThreshold;\r\n  }\r\n\r\n  private mockProtobufSerialization(data: any, schema: string): Buffer {\r\n    // Mock Protocol Buffers serialization\r\n    // In real implementation, this would use protobuf.js or similar library\r\n    const mockData = {\r\n      schema,\r\n      data: JSON.stringify(data),\r\n      timestamp: Date.now(),\r\n    };\r\n\r\n    return Buffer.from(JSON.stringify(mockData), 'utf8');\r\n  }\r\n\r\n  private mockProtobufDeserialization(buffer: Buffer, schema: string): any {\r\n    // Mock Protocol Buffers deserialization\r\n    // In real implementation, this would use protobuf.js or similar library\r\n    const mockData = JSON.parse(buffer.toString('utf8'));\r\n\r\n    if (mockData.schema !== schema) {\r\n      throw new Error(`Schema mismatch: expected ${schema}, got ${mockData.schema}`);\r\n    }\r\n\r\n    return JSON.parse(mockData.data);\r\n  }\r\n}\r\n\r\n// Type definitions\r\ninterface SerializationOptions {\r\n  compressionType?: CompressionType;\r\n  forceCompression?: boolean;\r\n  replacer?: (key: string, value: any) => any;\r\n  space?: string | number;\r\n  schema?: string;\r\n  encoding?: BinaryEncoding;\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\ninterface DeserializationOptions {\r\n  reviver?: (key: string, value: any) => any;\r\n  schema?: string;\r\n  encoding?: BinaryEncoding;\r\n}\r\n\r\ninterface SerializedData {\r\n  data: Buffer;\r\n  format: SerializationFormat;\r\n  schema?: string;\r\n  compressed: boolean;\r\n  compressionType?: CompressionType;\r\n  originalSize: number;\r\n  compressedSize: number;\r\n  compressionRatio: number;\r\n  processingTime: number;\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\ninterface BatchSerializationResult {\r\n  results: (SerializedData | null)[];\r\n  errors: Array<{ index: number; error: Error }>;\r\n  summary: {\r\n    total: number;\r\n    successful: number;\r\n    failed: number;\r\n    totalOriginalSize: number;\r\n    totalCompressedSize: number;\r\n    overallCompressionRatio: number;\r\n    processingTime: number;\r\n  };\r\n}\r\n\r\ninterface BatchDeserializationResult {\r\n  results: any[];\r\n  errors: Array<{ index: number; error: Error }>;\r\n  summary: {\r\n    total: number;\r\n    successful: number;\r\n    failed: number;\r\n    processingTime: number;\r\n  };\r\n}\r\n\r\ninterface SerializationStats {\r\n  compressionEnabled: boolean;\r\n  compressionLevel: number;\r\n  compressionThreshold: number;\r\n  supportedFormats: SerializationFormat[];\r\n  supportedCompressionTypes: CompressionType[];\r\n  supportedEncodings: BinaryEncoding[];\r\n}\r\n\r\ntype SerializationFormat = 'json' | 'protobuf' | 'binary';\r\ntype CompressionType = 'gzip' | 'deflate';\r\ntype BinaryEncoding = 'base64' | 'hex' | 'utf8';\r\n\r\nclass SerializationError extends Error {\r\n  constructor(\r\n    message: string,\r\n    public readonly format?: string,\r\n    public readonly originalError?: any\r\n  ) {\r\n    super(message);\r\n    this.name = 'SerializationError';\r\n  }\r\n}"], "version": 3}