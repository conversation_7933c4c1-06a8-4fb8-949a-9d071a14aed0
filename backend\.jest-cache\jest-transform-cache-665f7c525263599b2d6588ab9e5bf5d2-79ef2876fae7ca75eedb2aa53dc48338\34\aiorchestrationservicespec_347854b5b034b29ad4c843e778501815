5e83bb6e81bb8c894cd9ff397ac486d3
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const cqrs_1 = require("@nestjs/cqrs");
const ai_orchestration_service_1 = require("../ai-orchestration.service");
const model_selection_service_1 = require("../model-selection.service");
const load_balancer_service_1 = require("../load-balancer.service");
const circuit_breaker_service_1 = require("../../resilience/circuit-breaker.service");
const ai_cache_service_1 = require("../../caching/ai-cache.service");
const metrics_adapter_1 = require("../../../../infrastructure/adapters/metrics.adapter");
const ai_model_provider_interface_1 = require("../../../../domain/services/ai-model-provider.interface");
const prediction_engine_interface_1 = require("../../../../domain/services/prediction-engine.interface");
describe('AiOrchestrationService', () => {
    let service;
    let mockAiModelProvider;
    let mockPredictionEngine;
    let mockModelSelectionService;
    let mockLoadBalancerService;
    let mockCircuitBreakerService;
    let mockCacheService;
    let mockMetricsAdapter;
    let mockEventBus;
    let mockCommandBus;
    let mockQueryBus;
    let mockConfigService;
    beforeEach(async () => {
        // Create mocks
        mockAiModelProvider = {
            analyze: jest.fn(),
        };
        mockPredictionEngine = {
            predict: jest.fn(),
        };
        mockModelSelectionService = {
            selectOptimalModel: jest.fn(),
            selectRealTimeModel: jest.fn(),
        };
        mockLoadBalancerService = {
            getAvailableProviders: jest.fn(),
            checkProviderHealth: jest.fn(),
        };
        mockCircuitBreakerService = {
            execute: jest.fn(),
            getStatus: jest.fn(),
        };
        mockCacheService = {
            get: jest.fn(),
            set: jest.fn().mockResolvedValue(undefined),
            checkHealth: jest.fn(),
        };
        mockMetricsAdapter = {
            recordAiOperation: jest.fn(),
        };
        mockEventBus = {
            publish: jest.fn(),
        };
        mockCommandBus = {
            execute: jest.fn(),
        };
        mockQueryBus = {
            execute: jest.fn(),
        };
        mockConfigService = {
            get: jest.fn(),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                ai_orchestration_service_1.AiOrchestrationService,
                {
                    provide: ai_model_provider_interface_1.AI_MODEL_PROVIDER,
                    useValue: mockAiModelProvider,
                },
                {
                    provide: prediction_engine_interface_1.PREDICTION_ENGINE,
                    useValue: mockPredictionEngine,
                },
                {
                    provide: model_selection_service_1.ModelSelectionService,
                    useValue: mockModelSelectionService,
                },
                {
                    provide: load_balancer_service_1.LoadBalancerService,
                    useValue: mockLoadBalancerService,
                },
                {
                    provide: circuit_breaker_service_1.CircuitBreakerService,
                    useValue: mockCircuitBreakerService,
                },
                {
                    provide: ai_cache_service_1.AiCacheService,
                    useValue: mockCacheService,
                },
                {
                    provide: metrics_adapter_1.MetricsAdapter,
                    useValue: mockMetricsAdapter,
                },
                {
                    provide: cqrs_1.EventBus,
                    useValue: mockEventBus,
                },
                {
                    provide: cqrs_1.CommandBus,
                    useValue: mockCommandBus,
                },
                {
                    provide: cqrs_1.QueryBus,
                    useValue: mockQueryBus,
                },
                {
                    provide: config_1.ConfigService,
                    useValue: mockConfigService,
                },
            ],
        }).compile();
        service = module.get(ai_orchestration_service_1.AiOrchestrationService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('orchestrateAnalysis', () => {
        const mockRequest = {
            id: 'test-request-1',
            type: 'threat-analysis',
            data: { content: 'test data' },
            priority: 'high',
        };
        const mockModelConfig = {
            providerType: 'openai',
            modelId: 'gpt-4',
            parameters: {},
        };
        const mockProviders = [
            {
                id: 'provider-1',
                type: 'openai',
                status: 'healthy',
                load: 0.5,
            },
        ];
        const mockAnalysisResult = {
            id: 'result-1',
            result: { threat_detected: true, confidence: 0.95 },
            confidence: 0.95,
            metadata: { model: 'gpt-4' },
            timestamp: new Date(),
        };
        it('should orchestrate analysis successfully with cache miss', async () => {
            // Arrange
            mockCacheService.get.mockResolvedValue(null);
            mockModelSelectionService.selectOptimalModel.mockResolvedValue(mockModelConfig);
            mockLoadBalancerService.getAvailableProviders.mockResolvedValue(mockProviders);
            mockCircuitBreakerService.execute.mockResolvedValue(mockAnalysisResult);
            mockCacheService.set.mockResolvedValue(undefined);
            // Act
            const result = await service.orchestrateAnalysis(mockRequest);
            // Assert
            expect(result).toEqual(mockAnalysisResult);
            expect(mockCacheService.get).toHaveBeenCalledWith(expect.any(String));
            expect(mockModelSelectionService.selectOptimalModel).toHaveBeenCalledWith(mockRequest);
            expect(mockLoadBalancerService.getAvailableProviders).toHaveBeenCalledWith('openai');
            expect(mockCircuitBreakerService.execute).toHaveBeenCalled();
            expect(mockCacheService.set).toHaveBeenCalledWith(expect.any(String), mockAnalysisResult, expect.any(Number));
            expect(mockMetricsAdapter.recordAiOperation).toHaveBeenCalledWith('success', expect.any(Number), expect.any(String));
        });
        it('should return cached result when available', async () => {
            // Arrange
            mockCacheService.get.mockResolvedValue(mockAnalysisResult);
            // Act
            const result = await service.orchestrateAnalysis(mockRequest);
            // Assert
            expect(result).toEqual(mockAnalysisResult);
            expect(mockCacheService.get).toHaveBeenCalledWith(expect.any(String));
            expect(mockModelSelectionService.selectOptimalModel).not.toHaveBeenCalled();
            expect(mockMetricsAdapter.recordAiOperation).toHaveBeenCalledWith('cache_hit', expect.any(Number), expect.any(String));
        });
        it('should handle provider failures with fallback', async () => {
            // Arrange
            const multipleProviders = [
                { id: 'provider-1', type: 'openai', status: 'healthy', load: 0.5 },
                { id: 'provider-2', type: 'openai', status: 'healthy', load: 0.3 },
            ];
            mockCacheService.get.mockResolvedValue(null);
            mockModelSelectionService.selectOptimalModel.mockResolvedValue(mockModelConfig);
            mockLoadBalancerService.getAvailableProviders.mockResolvedValue(multipleProviders);
            // First provider fails, second succeeds
            mockCircuitBreakerService.execute
                .mockRejectedValueOnce(new Error('Provider 1 failed'))
                .mockResolvedValueOnce(mockAnalysisResult);
            // Act
            const result = await service.orchestrateAnalysis(mockRequest);
            // Assert
            expect(result).toEqual(mockAnalysisResult);
            expect(mockCircuitBreakerService.execute).toHaveBeenCalledTimes(2);
        });
        it('should throw error when all providers fail', async () => {
            // Arrange
            mockCacheService.get.mockResolvedValue(null);
            mockModelSelectionService.selectOptimalModel.mockResolvedValue(mockModelConfig);
            mockLoadBalancerService.getAvailableProviders.mockResolvedValue(mockProviders);
            mockCircuitBreakerService.execute.mockRejectedValue(new Error('All providers failed'));
            // Act & Assert
            await expect(service.orchestrateAnalysis(mockRequest)).rejects.toThrow('Analysis failed');
            expect(mockMetricsAdapter.recordAiOperation).toHaveBeenCalledWith('error', expect.any(Number), expect.any(String));
        });
        it('should handle configuration values correctly', async () => {
            // Arrange
            mockConfigService.get.mockReturnValue(7200); // 2 hours cache TTL
            mockCacheService.get.mockResolvedValue(null);
            mockModelSelectionService.selectOptimalModel.mockResolvedValue(mockModelConfig);
            mockLoadBalancerService.getAvailableProviders.mockResolvedValue(mockProviders);
            mockCircuitBreakerService.execute.mockResolvedValue(mockAnalysisResult);
            // Act
            await service.orchestrateAnalysis(mockRequest);
            // Assert
            expect(mockConfigService.get).toHaveBeenCalledWith('ai.cacheTtl', 3600);
            expect(mockCacheService.set).toHaveBeenCalledWith(expect.any(String), mockAnalysisResult, 7200);
        });
    });
    describe('orchestrateBatchAnalysis', () => {
        const mockRequests = [
            {
                id: 'batch-request-1',
                type: 'threat-analysis',
                data: { content: 'test data 1' },
            },
            {
                id: 'batch-request-2',
                type: 'threat-analysis',
                data: { content: 'test data 2' },
            },
        ];
        it('should process batch requests successfully', async () => {
            // Arrange
            mockConfigService.get.mockReturnValue(2); // Concurrency limit
            // Mock model selection for batch processing
            mockModelSelectionService.selectOptimalModel.mockResolvedValue({
                providerType: 'openai',
                modelId: 'gpt-4',
                parameters: {},
            });
            // Mock individual analysis calls
            jest.spyOn(service, 'orchestrateAnalysis')
                .mockResolvedValueOnce({
                id: 'result-1',
                result: { threat_detected: true },
                confidence: 0.9,
                metadata: {},
                timestamp: new Date(),
            })
                .mockResolvedValueOnce({
                id: 'result-2',
                result: { threat_detected: false },
                confidence: 0.8,
                metadata: {},
                timestamp: new Date(),
            });
            // Act
            const results = await service.orchestrateBatchAnalysis(mockRequests);
            // Assert
            expect(results).toHaveLength(2);
            expect(results[0].id).toBe('result-1');
            expect(results[1].id).toBe('result-2');
            expect(service.orchestrateAnalysis).toHaveBeenCalledTimes(2);
        });
        it('should handle batch processing errors gracefully', async () => {
            // Arrange
            // Mock model selection to fail, which should cause the batch to return empty results
            mockModelSelectionService.selectOptimalModel.mockRejectedValue(new Error('Model selection failed'));
            // Act
            const results = await service.orchestrateBatchAnalysis(mockRequests);
            // Assert
            expect(results).toHaveLength(0); // Should return empty array when all requests fail model selection
        });
    });
    describe('orchestrateRealTimePrediction', () => {
        const mockPredictionRequest = {
            modelId: 'model-1',
            input: { data: 'test input' },
            options: { timeout: 1000 },
        };
        const mockPredictionResult = {
            prediction: { category: 'safe', score: 0.95 },
            confidence: 0.95,
            metadata: { model: 'model-1' },
        };
        it('should orchestrate real-time prediction successfully', async () => {
            // Arrange
            const mockModelConfig = {
                modelId: 'model-1',
                providerType: 'openai',
                parameters: {},
            };
            mockModelSelectionService.selectRealTimeModel.mockResolvedValue(mockModelConfig);
            mockPredictionEngine.predict.mockResolvedValue(mockPredictionResult);
            mockConfigService.get.mockReturnValue(1000);
            // Act
            const result = await service.orchestrateRealTimePrediction(mockPredictionRequest);
            // Assert
            expect(result).toEqual(mockPredictionResult);
            expect(mockModelSelectionService.selectRealTimeModel).toHaveBeenCalledWith(mockPredictionRequest);
            expect(mockPredictionEngine.predict).toHaveBeenCalledWith(mockPredictionRequest, expect.objectContaining({
                modelConfig: mockModelConfig,
                timeout: 1000,
                priority: 'high',
            }));
        });
        it('should handle real-time prediction failures', async () => {
            // Arrange
            mockModelSelectionService.selectRealTimeModel.mockRejectedValue(new Error('Model selection failed'));
            // Act & Assert
            await expect(service.orchestrateRealTimePrediction(mockPredictionRequest))
                .rejects.toThrow('Prediction failed');
        });
    });
    describe('checkHealth', () => {
        it('should return healthy status when all components are healthy', async () => {
            // Arrange
            const mockProviderHealth = { 'provider-1': { healthy: true } };
            const mockCacheHealth = { status: 'healthy' };
            const mockCircuitBreakerStatus = { 'cb-1': { state: 'closed' } };
            mockLoadBalancerService.checkProviderHealth.mockResolvedValue(mockProviderHealth);
            mockCacheService.checkHealth.mockResolvedValue(mockCacheHealth);
            mockCircuitBreakerService.getStatus.mockReturnValue(mockCircuitBreakerStatus);
            // Act
            const health = await service.checkHealth();
            // Assert
            expect(health.status).toBe('healthy');
            expect(health.providers).toEqual(mockProviderHealth);
            expect(health.cache).toEqual(mockCacheHealth);
            expect(health.circuitBreakers).toEqual(mockCircuitBreakerStatus);
            expect(health.timestamp).toBeInstanceOf(Date);
        });
        it('should return unhealthy status when health check fails', async () => {
            // Arrange
            mockLoadBalancerService.checkProviderHealth.mockRejectedValue(new Error('Health check failed'));
            // Act
            const health = await service.checkHealth();
            // Assert
            expect(health.status).toBe('unhealthy');
            expect(health.error).toBe('Health check failed');
            expect(health.timestamp).toBeInstanceOf(Date);
        });
    });
    describe('private helper methods', () => {
        it('should generate unique request IDs', () => {
            // Use reflection to access private method for testing
            const generateRequestId = service.generateRequestId.bind(service);
            const id1 = generateRequestId();
            const id2 = generateRequestId();
            expect(id1).toMatch(/^ai-req-\d+-[a-z0-9]+$/);
            expect(id2).toMatch(/^ai-req-\d+-[a-z0-9]+$/);
            expect(id1).not.toBe(id2);
        });
        it('should generate cache keys consistently', () => {
            const generateCacheKey = service.generateCacheKey.bind(service);
            const request1 = { id: '1', type: 'test', data: { value: 'same' } };
            const request2 = { id: '1', type: 'test', data: { value: 'same' } }; // Same ID for same content
            const request3 = { id: '1', type: 'test', data: { value: 'different' } };
            const key1 = generateCacheKey(request1);
            const key2 = generateCacheKey(request2);
            const key3 = generateCacheKey(request3);
            expect(key1).toBe(key2); // Same content should generate same key
            expect(key1).not.toBe(key3); // Different content should generate different key
        });
    });
    describe('error handling', () => {
        it('should handle orchestration errors with proper error types', async () => {
            // Arrange
            const mockRequest = {
                id: 'error-request',
                type: 'test',
                data: {},
            };
            mockCacheService.get.mockRejectedValue(new Error('Cache error'));
            // Act & Assert
            await expect(service.orchestrateAnalysis(mockRequest)).rejects.toThrow();
            expect(mockMetricsAdapter.recordAiOperation).toHaveBeenCalledWith('error', expect.any(Number), expect.any(String));
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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