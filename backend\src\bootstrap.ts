import { INestApplication, Logger, ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import helmet from 'helmet';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import { GlobalExceptionFilter } from './common/filters/global-exception.filter';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';
import { ResponseTransformInterceptor } from './common/interceptors/response-transform.interceptor';
import { CorrelationIdMiddleware } from './common/middleware/correlation-id.middleware';

/**
 * Bootstrap configuration for the Sentinel application
 * Handles environment-specific startup logic and application configuration
 */
export class ApplicationBootstrap {
  private readonly logger = new Logger(ApplicationBootstrap.name);

  constructor(private readonly app: INestApplication) {}

  /**
   * Configure the application with all necessary middleware, pipes, and settings
   */
  async configure(): Promise<void> {
    const configService = this.app.get(ConfigService);
    
    // Configure security
    await this.configureSecurity(configService);
    
    // Configure global middleware
    this.configureGlobalMiddleware();
    
    // Configure validation and transformation
    this.configureValidation();
    
    // Configure global interceptors and filters
    this.configureGlobalProviders();
    
    // Configure API documentation
    await this.configureSwagger(configService);
    
    // Configure global prefix
    this.configureGlobalPrefix(configService);
    
    this.logger.log('Application bootstrap configuration completed');
  }

  /**
   * Configure security middleware and headers
   */
  private async configureSecurity(configService: ConfigService): Promise<void> {
    const securityConfig = configService.get('security');
    
    // Enable CORS
    if (securityConfig?.cors?.enabled) {
      this.app.enableCors({
        origin: securityConfig.cors.origin,
        methods: securityConfig.cors.methods,
        allowedHeaders: securityConfig.cors.allowedHeaders,
        credentials: securityConfig.cors.credentials,
        maxAge: securityConfig.cors.maxAge,
      });
      this.logger.log('CORS configuration applied');
    }

    // Security headers with Helmet
    this.app.use(helmet({
      contentSecurityPolicy: securityConfig?.headers?.csp?.enabled ? {
        directives: securityConfig.headers.csp.directives,
        reportOnly: securityConfig.headers.csp.reportOnly,
      } : false,
      hsts: securityConfig?.headers?.hsts?.enabled ? {
        maxAge: securityConfig.headers.hsts.maxAge,
        includeSubDomains: securityConfig.headers.hsts.includeSubDomains,
        preload: securityConfig.headers.hsts.preload,
      } : false,
      frameguard: { 
        action: securityConfig?.headers?.frameOptions?.toLowerCase() || 'deny' 
      },
      noSniff: securityConfig?.headers?.contentTypeOptions ?? true,
      xssFilter: securityConfig?.headers?.xssFilter ?? true,
      referrerPolicy: { 
        policy: securityConfig?.headers?.referrerPolicy || 'strict-origin-when-cross-origin' 
      },
    }));
    
    this.logger.log('Security headers configured');
  }

  /**
   * Configure global middleware
   */
  private configureGlobalMiddleware(): void {
    // Compression middleware
    this.app.use(compression());
    
    // Cookie parser middleware
    this.app.use(cookieParser());
    
    // Correlation ID middleware
    this.app.use(new CorrelationIdMiddleware().use);
    
    this.logger.log('Global middleware configured');
  }

  /**
   * Configure global validation pipe
   */
  private configureValidation(): void {
    this.app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        disableErrorMessages: process.env.NODE_ENV === 'production',
        validationError: {
          target: false,
          value: false,
        },
      }),
    );
    
    this.logger.log('Global validation pipe configured');
  }

  /**
   * Configure global interceptors and filters
   */
  private configureGlobalProviders(): void {
    // Global exception filter
    this.app.useGlobalFilters(new GlobalExceptionFilter());
    
    // Global interceptors
    this.app.useGlobalInterceptors(
      new LoggingInterceptor(),
      new ResponseTransformInterceptor(),
    );
    
    this.logger.log('Global providers configured');
  }

  /**
   * Configure Swagger/OpenAPI documentation
   */
  private async configureSwagger(configService: ConfigService): Promise<void> {
    const environment = configService.get('NODE_ENV', 'development');
    const apiPrefix = configService.get('API_PREFIX', 'api');
    
    if (environment !== 'production') {
      const config = new DocumentBuilder()
        .setTitle('Sentinel Vulnerability Assessment Platform API')
        .setDescription(
          'Enterprise-grade vulnerability assessment and threat intelligence platform. ' +
          'Provides comprehensive security analysis, predictive risk assessment, and automated mitigation recommendations.'
        )
        .setVersion('1.0.0')
        .setContact(
          'Sentinel Development Team',
          'https://sentinel-platform.com',
          '<EMAIL>'
        )
        .setLicense('MIT', 'https://opensource.org/licenses/MIT')
        .addServer(`http://localhost:3000`, 'Development Server')
        .addServer(`https://api.sentinel-platform.com`, 'Production Server')
        .addBearerAuth(
          {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
            name: 'JWT',
            description: 'Enter JWT token',
            in: 'header',
          },
          'JWT-auth',
        )
        .addApiKey(
          {
            type: 'apiKey',
            name: 'X-API-Key',
            in: 'header',
            description: 'API Key for service-to-service authentication',
          },
          'API-Key',
        )
        .addTag('Authentication', 'User authentication and authorization endpoints')
        .addTag('Health', 'Application health checks and monitoring')
        .addTag('Metrics', 'Application and business metrics')
        .addTag('Security Events', 'Security event management and analysis')
        .addTag('Vulnerabilities', 'Vulnerability assessment and management')
        .addTag('Threat Intelligence', 'Threat intelligence and analysis')
        .addTag('AI Services', 'AI/ML powered security analysis')
        .addTag('Automation', 'Automated response and mitigation')
        .addTag('Analytics', 'Security analytics and reporting')
        .build();

      const document = SwaggerModule.createDocument(this.app, config);
      SwaggerModule.setup(`${apiPrefix}/docs`, this.app, document, {
        swaggerOptions: {
          persistAuthorization: true,
          displayRequestDuration: true,
          docExpansion: 'none',
          filter: true,
          showRequestHeaders: true,
          tryItOutEnabled: true,
        },
        customSiteTitle: 'Sentinel API Documentation',
        customfavIcon: '/favicon.ico',
        customCss: `
          .swagger-ui .topbar { display: none }
          .swagger-ui .info .title { color: #1f2937; }
        `,
      });

      const port = configService.get('PORT', 3000);
      this.logger.log(`📚 API Documentation available at http://localhost:${port}/${apiPrefix}/docs`);
    }
  }

  /**
   * Configure global API prefix
   */
  private configureGlobalPrefix(configService: ConfigService): void {
    const apiPrefix = configService.get('API_PREFIX', 'api');
    const apiVersion = configService.get('API_VERSION', 'v1');
    
    this.app.setGlobalPrefix(`${apiPrefix}/${apiVersion}`);
    
    this.logger.log(`Global API prefix set to: ${apiPrefix}/${apiVersion}`);
  }

  /**
   * Get application startup information
   */
  getStartupInfo(configService: ConfigService): StartupInfo {
    const port = configService.get('PORT', 3000);
    const apiPrefix = configService.get('API_PREFIX', 'api');
    const apiVersion = configService.get('API_VERSION', 'v1');
    const environment = configService.get('NODE_ENV', 'development');
    
    return {
      port,
      apiPrefix,
      apiVersion,
      environment,
      urls: {
        server: `http://localhost:${port}`,
        api: `http://localhost:${port}/${apiPrefix}/${apiVersion}`,
        docs: environment !== 'production' ? `http://localhost:${port}/${apiPrefix}/docs` : null,
        health: `http://localhost:${port}/${apiPrefix}/${apiVersion}/health`,
        metrics: `http://localhost:${port}/${apiPrefix}/${apiVersion}/metrics`,
      },
    };
  }
}

/**
 * Startup information interface
 */
export interface StartupInfo {
  port: number;
  apiPrefix: string;
  apiVersion: string;
  environment: string;
  urls: {
    server: string;
    api: string;
    docs: string | null;
    health: string;
    metrics: string;
  };
}

/**
 * Environment-specific bootstrap configurations
 */
export class EnvironmentBootstrap {
  private static readonly logger = new Logger(EnvironmentBootstrap.name);

  /**
   * Apply environment-specific configurations
   */
  static async configure(app: INestApplication): Promise<void> {
    const configService = app.get(ConfigService);
    const environment = configService.get('NODE_ENV', 'development');

    switch (environment) {
      case 'development':
        await this.configureDevelopment(app, configService);
        break;
      case 'test':
        await this.configureTest(app, configService);
        break;
      case 'staging':
        await this.configureStaging(app, configService);
        break;
      case 'production':
        await this.configureProduction(app, configService);
        break;
      default:
        this.logger.warn(`Unknown environment: ${environment}, using development configuration`);
        await this.configureDevelopment(app, configService);
    }

    this.logger.log(`Environment-specific configuration applied for: ${environment}`);
  }

  /**
   * Development environment configuration
   */
  private static async configureDevelopment(
    app: INestApplication,
    configService: ConfigService,
  ): Promise<void> {
    // Enable detailed logging
    app.useLogger(['error', 'warn', 'log', 'debug', 'verbose']);
    
    // Development-specific middleware or configurations can be added here
    this.logger.log('Development environment configuration applied');
  }

  /**
   * Test environment configuration
   */
  private static async configureTest(
    app: INestApplication,
    configService: ConfigService,
  ): Promise<void> {
    // Minimal logging for tests
    app.useLogger(['error', 'warn']);
    
    // Test-specific configurations
    this.logger.log('Test environment configuration applied');
  }

  /**
   * Staging environment configuration
   */
  private static async configureStaging(
    app: INestApplication,
    configService: ConfigService,
  ): Promise<void> {
    // Production-like logging
    app.useLogger(['error', 'warn', 'log']);
    
    // Staging-specific configurations
    this.logger.log('Staging environment configuration applied');
  }

  /**
   * Production environment configuration
   */
  private static async configureProduction(
    app: INestApplication,
    configService: ConfigService,
  ): Promise<void> {
    // Production logging (errors and warnings only)
    app.useLogger(['error', 'warn']);
    
    // Production-specific optimizations
    this.logger.log('Production environment configuration applied');
  }
}