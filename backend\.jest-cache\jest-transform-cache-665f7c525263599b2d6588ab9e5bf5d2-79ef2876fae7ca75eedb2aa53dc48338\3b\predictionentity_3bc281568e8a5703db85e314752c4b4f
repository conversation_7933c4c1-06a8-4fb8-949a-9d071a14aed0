1a283d8068906e4b1bfda0b19297c9ab
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g;
Object.defineProperty(exports, "__esModule", { value: true });
exports.Prediction = void 0;
const typeorm_1 = require("typeorm");
const analysis_job_entity_1 = require("./analysis-job.entity");
const model_configuration_entity_1 = require("./model-configuration.entity");
/**
 * Prediction entity
 * Represents AI/ML model predictions and their results
 */
let Prediction = class Prediction {
    /**
     * Check if prediction is high confidence
     */
    get isHighConfidence() {
        return this.confidence >= 0.8;
    }
    /**
     * Check if prediction is low confidence
     */
    get isLowConfidence() {
        return this.confidence < 0.5;
    }
    /**
     * Check if prediction has been validated
     */
    get isValidated() {
        return ['validated', 'invalidated'].includes(this.status);
    }
    /**
     * Get prediction value
     */
    get value() {
        return this.predictionResult.value;
    }
    /**
     * Get prediction probability
     */
    get probability() {
        return this.predictionResult.probability || this.confidence;
    }
    /**
     * Get top alternative predictions
     */
    get alternatives() {
        return this.predictionResult.alternatives || [];
    }
    /**
     * Get processing time in seconds
     */
    get processingTimeSeconds() {
        return this.processingTime ? this.processingTime / 1000 : null;
    }
    /**
     * Mark prediction as completed
     */
    markAsCompleted(result, confidence, processingTime) {
        this.status = 'completed';
        this.predictionResult = result;
        this.confidence = confidence;
        if (processingTime) {
            this.processingTime = processingTime;
        }
    }
    /**
     * Mark prediction as failed
     */
    markAsFailed(error) {
        this.status = 'failed';
        this.errorInfo = error;
    }
    /**
     * Validate prediction with actual outcome
     */
    validate(actualOutcome, validatedBy, notes) {
        const isCorrect = this.compareOutcome(actualOutcome);
        this.status = isCorrect ? 'validated' : 'invalidated';
        this.validationInfo = {
            validatedBy,
            validatedAt: new Date().toISOString(),
            actualOutcome,
            accuracy: isCorrect ? 1 : 0,
            notes,
        };
    }
    /**
     * Compare predicted outcome with actual outcome
     */
    compareOutcome(actualOutcome) {
        // Simple comparison - can be enhanced based on prediction type
        if (typeof this.value === 'number' && typeof actualOutcome === 'number') {
            // For numerical predictions, check if within reasonable range
            const tolerance = 0.1; // 10% tolerance
            const diff = Math.abs(this.value - actualOutcome) / Math.max(this.value, actualOutcome);
            return diff <= tolerance;
        }
        // For categorical predictions, exact match
        return this.value === actualOutcome;
    }
    /**
     * Add feature importance data
     */
    addFeatureImportance(features) {
        this.featureImportance = features.sort((a, b) => b.importance - a.importance);
    }
    /**
     * Add explanation data
     */
    addExplanation(explanation) {
        this.explanation = explanation;
    }
    /**
     * Add quality metrics
     */
    addQualityMetrics(metrics) {
        this.qualityMetrics = metrics;
    }
    /**
     * Get uncertainty level
     */
    get uncertaintyLevel() {
        if (this.confidence >= 0.8)
            return 'low';
        if (this.confidence >= 0.5)
            return 'medium';
        return 'high';
    }
    /**
     * Get risk level based on prediction type and confidence
     */
    get riskLevel() {
        if (this.type === 'vulnerability_severity') {
            const severity = this.value;
            if (severity === 'critical' && this.confidence >= 0.7)
                return 'critical';
            if (severity === 'high' && this.confidence >= 0.6)
                return 'high';
            if (severity === 'medium' && this.confidence >= 0.5)
                return 'medium';
            return 'low';
        }
        if (this.type === 'risk_score') {
            const score = this.value;
            if (score >= 8 && this.confidence >= 0.7)
                return 'critical';
            if (score >= 6 && this.confidence >= 0.6)
                return 'high';
            if (score >= 4 && this.confidence >= 0.5)
                return 'medium';
            return 'low';
        }
        // Default risk assessment based on confidence
        if (this.confidence >= 0.8)
            return 'high';
        if (this.confidence >= 0.6)
            return 'medium';
        return 'low';
    }
};
exports.Prediction = Prediction;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Prediction.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: [
            'vulnerability_severity',
            'threat_classification',
            'risk_score',
            'exploit_probability',
            'remediation_priority',
            'false_positive_likelihood',
            'attack_vector_prediction',
            'impact_assessment',
        ],
    }),
    __metadata("design:type", String)
], Prediction.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'input_data', type: 'jsonb' }),
    __metadata("design:type", typeof (_a = typeof Record !== "undefined" && Record) === "function" ? _a : Object)
], Prediction.prototype, "inputData", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'prediction_result', type: 'jsonb' }),
    __metadata("design:type", Object)
], Prediction.prototype, "predictionResult", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 4 }),
    __metadata("design:type", Number)
], Prediction.prototype, "confidence", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['pending', 'completed', 'failed', 'validated', 'invalidated'],
        default: 'pending',
    }),
    __metadata("design:type", String)
], Prediction.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'model_version', length: 100 }),
    __metadata("design:type", String)
], Prediction.prototype, "modelVersion", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'processing_time', type: 'integer', nullable: true }),
    __metadata("design:type", Number)
], Prediction.prototype, "processingTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'validation_info', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Prediction.prototype, "validationInfo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'feature_importance', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Array !== "undefined" && Array) === "function" ? _b : Object)
], Prediction.prototype, "featureImportance", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Prediction.prototype, "explanation", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'quality_metrics', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Prediction.prototype, "qualityMetrics", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Prediction.prototype, "context", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'error_info', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Prediction.prototype, "errorInfo", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], Prediction.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_c = typeof Record !== "undefined" && Record) === "function" ? _c : Object)
], Prediction.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', type: 'uuid' }),
    __metadata("design:type", String)
], Prediction.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], Prediction.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], Prediction.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => analysis_job_entity_1.AnalysisJob, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'analysis_job_id' }),
    __metadata("design:type", typeof (_f = typeof analysis_job_entity_1.AnalysisJob !== "undefined" && analysis_job_entity_1.AnalysisJob) === "function" ? _f : Object)
], Prediction.prototype, "analysisJob", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'analysis_job_id', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], Prediction.prototype, "analysisJobId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => model_configuration_entity_1.ModelConfiguration),
    (0, typeorm_1.JoinColumn)({ name: 'model_configuration_id' }),
    __metadata("design:type", typeof (_g = typeof model_configuration_entity_1.ModelConfiguration !== "undefined" && model_configuration_entity_1.ModelConfiguration) === "function" ? _g : Object)
], Prediction.prototype, "modelConfiguration", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'model_configuration_id', type: 'uuid' }),
    __metadata("design:type", String)
], Prediction.prototype, "modelConfigurationId", void 0);
exports.Prediction = Prediction = __decorate([
    (0, typeorm_1.Entity)('predictions'),
    (0, typeorm_1.Index)(['type']),
    (0, typeorm_1.Index)(['confidence']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['createdAt']),
    (0, typeorm_1.Index)(['modelVersion'])
], Prediction);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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