{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\value-objects\\__tests__\\precision-recall.value-object.spec.ts", "mappings": ";;AAAA,oFAAmE;AAEnE,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;IAC5C,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,EAAE,GAAG,IAAI,+CAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,EAAE,GAAG,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YACzC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC/B,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,EAAE,GAAG,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YACzC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC/B,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,+CAAe,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAC;YAC1F,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAC;QAC3F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,+CAAe,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;YACvF,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;YAC1F,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;QAC5F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAChD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,+CAAe,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,qCAAqC,CAAC,CAAC;YAChG,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,+CAAe,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,qCAAqC,CAAC,CAAC;QAClG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,+CAAe,CAAC,KAAY,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC;YACrG,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,+CAAe,CAAC,GAAG,EAAE,KAAY,CAAC,CAAC,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC;QACvG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,EAAE,GAAG,+CAAe,CAAC,mBAAmB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,sBAAsB;YAClF,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,iBAAiB;YACjD,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,iBAAiB;QAC7D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,GAAG,GAAG,+CAAe,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,0BAA0B;YACrF,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE3B,MAAM,GAAG,GAAG,+CAAe,CAAC,mBAAmB,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,sBAAsB;YACjF,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAe,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;iBAC1D,OAAO,CAAC,4CAA4C,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,EAAE,GAAG,+CAAe,CAAC,eAAe,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACnD,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAe,CAAC,eAAe,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;iBACnD,OAAO,CAAC,uCAAuC,CAAC,CAAC;YACpD,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAe,CAAC,eAAe,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;iBACnD,OAAO,CAAC,uCAAuC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,OAAO,GAAG,+CAAe,CAAC,OAAO,EAAE,CAAC;YAC1C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAEjC,MAAM,IAAI,GAAG,+CAAe,CAAC,IAAI,EAAE,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,EAAE,GAAG,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YACzC,MAAM,EAAE,GAAG,EAAE,CAAC,UAAU,EAAE,CAAC;YAC3B,MAAM,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,8BAA8B;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,EAAE,GAAG,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YACzC,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,EAAE,GAAG,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAEzC,MAAM,EAAE,GAAG,EAAE,CAAC,UAAU,EAAE,CAAC,CAAC,0BAA0B;YACtD,MAAM,GAAG,GAAG,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC,+BAA+B;YAE7D,MAAM,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,EAAE,GAAG,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YACzC,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;YACzE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,EAAE,GAAG,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YACzC,MAAM,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,EAAE,GAAG,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YACzC,MAAM,CAAC,EAAE,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,EAAE,GAAG,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YACzC,MAAM,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,kBAAkB;QAC1E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,GAAG,GAAG,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAC1C,MAAM,GAAG,GAAG,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAE1C,MAAM,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzC,MAAM,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,QAAQ,GAAG,IAAI,+CAAe,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;YAChD,MAAM,UAAU,GAAG,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAEjD,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,EAAE,GAAG,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YACzC,MAAM,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,YAAY;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,EAAE,GAAG,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YACzC,MAAM,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,EAAE,GAAG,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YACzC,MAAM,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,EAAE,GAAG,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAEzC,MAAM,CAAC,EAAE,CAAC,mBAAmB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,MAAM,CAAC,EAAE,CAAC,mBAAmB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,EAAE,CAAC,oBAAoB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,CAAC,EAAE,CAAC,oBAAoB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;YACvC,MAAM,EAAE,GAAG,IAAI,+CAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAE3C,MAAM,CAAC,EAAE,CAAC,sBAAsB,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC7C,MAAM,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC1C,MAAM,CAAC,EAAE,CAAC,4BAA4B,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACxD,MAAM,CAAC,EAAE,CAAC,yBAAyB,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACrD,MAAM,CAAC,EAAE,CAAC,4BAA4B,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,EAAE,GAAG,IAAI,+CAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC3C,MAAM,GAAG,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;YAE1B,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;YAC1C,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YACvC,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,GAAG,GAAG,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAC1C,MAAM,GAAG,GAAG,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAC1C,MAAM,QAAQ,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAE3C,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,wBAAwB;YACtE,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,wBAAwB;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,GAAG,GAAG,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAC1C,MAAM,GAAG,GAAG,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAE1C,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;YACnF,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,GAAG,GAAG,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAC1C,MAAM,GAAG,GAAG,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAE1C,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;YACxC,MAAM,SAAS,GAAG,GAAG,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC;YAEjD,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,kCAAkC;YAC5E,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,wBAAwB;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,GAAG,GAAG,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,aAAa;YACxD,MAAM,GAAG,GAAG,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,aAAa;YACxD,MAAM,GAAG,GAAG,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,WAAW;YAEtD,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,EAAE,GAAG,IAAI,+CAAe,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAC/C,MAAM,OAAO,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAE5B,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,KAAK,GAAG;gBACZ,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC;gBAC7B,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC;gBAC7B,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC;aAC9B,CAAC;YAEF,MAAM,OAAO,GAAG,+CAAe,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAe,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,yCAAyC,CAAC,CAAC;QAC/F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,KAAK,GAAG;gBACZ,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC;gBAC7B,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC;aAC9B,CAAC;YACF,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAE3B,MAAM,WAAW,GAAG,+CAAe,CAAC,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YACpE,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;YACtE,MAAM,KAAK,GAAG,CAAC,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;YAC9C,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAE3B,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAe,CAAC,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;iBAC1D,OAAO,CAAC,oDAAoD,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YAClD,MAAM,KAAK,GAAG,CAAC,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;YAC7E,MAAM,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAEvB,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAe,CAAC,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;iBAC1D,OAAO,CAAC,6BAA6B,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,KAAK,GAAG;gBACZ,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,aAAa;gBAC5C,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,aAAa;gBAC5C,IAAI,+CAAe,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,aAAa;aAC7C,CAAC;YAEF,MAAM,MAAM,GAAG,+CAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC7C,MAAM,aAAa,GAAG,+CAAe,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,UAAU,GAAG,+CAAe,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAErD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACnC,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC1C,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAe,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;YACvF,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAe,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,2CAA2C,CAAC,CAAC;YACrG,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAe,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,wCAAwC,CAAC,CAAC;QACjG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,EAAE,GAAG,IAAI,+CAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC3C,MAAM,IAAI,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC;YAEzB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACvC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,IAAI,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;YAC/C,MAAM,EAAE,GAAG,+CAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAE1C,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,mEAAmE,EAAE,GAAG,EAAE;YAC3E,MAAM,GAAG,GAAG,IAAI,+CAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC5C,MAAM,GAAG,GAAG,IAAI,+CAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAE5C,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oEAAoE,EAAE,GAAG,EAAE;YAC5E,MAAM,GAAG,GAAG,IAAI,+CAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC5C,MAAM,GAAG,GAAG,IAAI,+CAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAE5C,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\value-objects\\__tests__\\precision-recall.value-object.spec.ts"], "sourcesContent": ["import { PrecisionRecall } from '../precision-recall.value-object';\r\n\r\ndescribe('PrecisionRecall Value Object', () => {\r\n  describe('Construction', () => {\r\n    it('should create a valid precision-recall pair', () => {\r\n      const pr = new PrecisionRecall(0.85, 0.75);\r\n      expect(pr.precision).toBe(0.85);\r\n      expect(pr.recall).toBe(0.75);\r\n    });\r\n\r\n    it('should accept minimum values', () => {\r\n      const pr = new PrecisionRecall(0.0, 0.0);\r\n      expect(pr.precision).toBe(0.0);\r\n      expect(pr.recall).toBe(0.0);\r\n    });\r\n\r\n    it('should accept maximum values', () => {\r\n      const pr = new PrecisionRecall(1.0, 1.0);\r\n      expect(pr.precision).toBe(1.0);\r\n      expect(pr.recall).toBe(1.0);\r\n    });\r\n\r\n    it('should throw error for invalid precision', () => {\r\n      expect(() => new PrecisionRecall(-0.1, 0.5)).toThrow('Precision must be between 0 and 1');\r\n      expect(() => new PrecisionRecall(1.1, 0.5)).toThrow('Precision must be between 0 and 1');\r\n    });\r\n\r\n    it('should throw error for invalid recall', () => {\r\n      expect(() => new PrecisionRecall(0.5, -0.1)).toThrow('Recall must be between 0 and 1');\r\n      expect(() => new PrecisionRecall(0.5, 1.1)).toThrow('Recall must be between 0 and 1');\r\n    });\r\n\r\n    it('should throw error for NaN values', () => {\r\n      expect(() => new PrecisionRecall(NaN, 0.5)).toThrow('Precision and recall cannot be NaN');\r\n      expect(() => new PrecisionRecall(0.5, NaN)).toThrow('Precision and recall cannot be NaN');\r\n    });\r\n\r\n    it('should throw error for infinite values', () => {\r\n      expect(() => new PrecisionRecall(Infinity, 0.5)).toThrow('Precision and recall must be finite');\r\n      expect(() => new PrecisionRecall(0.5, Infinity)).toThrow('Precision and recall must be finite');\r\n    });\r\n\r\n    it('should throw error for non-number values', () => {\r\n      expect(() => new PrecisionRecall('0.5' as any, 0.5)).toThrow('Precision and recall must be numbers');\r\n      expect(() => new PrecisionRecall(0.5, '0.5' as any)).toThrow('Precision and recall must be numbers');\r\n    });\r\n  });\r\n\r\n  describe('Factory Methods', () => {\r\n    it('should create from confusion matrix', () => {\r\n      const pr = PrecisionRecall.fromConfusionMatrix(80, 20, 10); // TP=80, FP=20, FN=10\r\n      expect(pr.precision).toBe(0.8); // 80 / (80 + 20)\r\n      expect(pr.recall).toBeCloseTo(0.8889, 4); // 80 / (80 + 10)\r\n    });\r\n\r\n    it('should handle zero denominators in confusion matrix', () => {\r\n      const pr1 = PrecisionRecall.fromConfusionMatrix(0, 0, 10); // No positive predictions\r\n      expect(pr1.precision).toBe(0);\r\n      expect(pr1.recall).toBe(0);\r\n\r\n      const pr2 = PrecisionRecall.fromConfusionMatrix(0, 20, 0); // No actual positives\r\n      expect(pr2.precision).toBe(0);\r\n      expect(pr2.recall).toBe(0);\r\n    });\r\n\r\n    it('should throw error for negative confusion matrix values', () => {\r\n      expect(() => PrecisionRecall.fromConfusionMatrix(-1, 20, 10))\r\n        .toThrow('Confusion matrix values cannot be negative');\r\n    });\r\n\r\n    it('should create from percentages', () => {\r\n      const pr = PrecisionRecall.fromPercentages(85, 75);\r\n      expect(pr.precision).toBe(0.85);\r\n      expect(pr.recall).toBe(0.75);\r\n    });\r\n\r\n    it('should throw error for invalid percentages', () => {\r\n      expect(() => PrecisionRecall.fromPercentages(-10, 75))\r\n        .toThrow('Percentages must be between 0 and 100');\r\n      expect(() => PrecisionRecall.fromPercentages(85, 110))\r\n        .toThrow('Percentages must be between 0 and 100');\r\n    });\r\n\r\n    it('should create predefined precision-recall pairs', () => {\r\n      const perfect = PrecisionRecall.perfect();\r\n      expect(perfect.precision).toBe(1.0);\r\n      expect(perfect.recall).toBe(1.0);\r\n\r\n      const zero = PrecisionRecall.zero();\r\n      expect(zero.precision).toBe(0.0);\r\n      expect(zero.recall).toBe(0.0);\r\n    });\r\n  });\r\n\r\n  describe('F-Score Calculations', () => {\r\n    it('should calculate F1 score', () => {\r\n      const pr = new PrecisionRecall(0.8, 0.6);\r\n      const f1 = pr.getF1Score();\r\n      expect(f1).toBeCloseTo(0.6857, 4); // 2 * 0.8 * 0.6 / (0.8 + 0.6)\r\n    });\r\n\r\n    it('should handle zero precision and recall in F1', () => {\r\n      const pr = new PrecisionRecall(0.0, 0.0);\r\n      expect(pr.getF1Score()).toBe(0);\r\n    });\r\n\r\n    it('should calculate F-beta scores', () => {\r\n      const pr = new PrecisionRecall(0.8, 0.6);\r\n      \r\n      const f2 = pr.getF2Score(); // Beta = 2, favors recall\r\n      const f05 = pr.getF05Score(); // Beta = 0.5, favors precision\r\n      \r\n      expect(f2).toBeCloseTo(0.6316, 4);\r\n      expect(f05).toBeCloseTo(0.7407, 4);\r\n    });\r\n\r\n    it('should throw error for invalid beta', () => {\r\n      const pr = new PrecisionRecall(0.8, 0.6);\r\n      expect(() => pr.getFBetaScore(0)).toThrow('Beta must be greater than 0');\r\n      expect(() => pr.getFBetaScore(-1)).toThrow('Beta must be greater than 0');\r\n    });\r\n\r\n    it('should calculate harmonic mean', () => {\r\n      const pr = new PrecisionRecall(0.8, 0.6);\r\n      expect(pr.getHarmonicMean()).toBe(pr.getF1Score());\r\n    });\r\n\r\n    it('should calculate arithmetic mean', () => {\r\n      const pr = new PrecisionRecall(0.8, 0.6);\r\n      expect(pr.getArithmeticMean()).toBe(0.7);\r\n    });\r\n\r\n    it('should calculate geometric mean', () => {\r\n      const pr = new PrecisionRecall(0.8, 0.6);\r\n      expect(pr.getGeometricMean()).toBeCloseTo(0.6928, 4); // sqrt(0.8 * 0.6)\r\n    });\r\n  });\r\n\r\n  describe('Comparison and Analysis', () => {\r\n    it('should check which metric is higher', () => {\r\n      const pr1 = new PrecisionRecall(0.8, 0.6);\r\n      const pr2 = new PrecisionRecall(0.6, 0.8);\r\n      \r\n      expect(pr1.isPrecisionHigher()).toBe(true);\r\n      expect(pr1.isRecallHigher()).toBe(false);\r\n      expect(pr2.isPrecisionHigher()).toBe(false);\r\n      expect(pr2.isRecallHigher()).toBe(true);\r\n    });\r\n\r\n    it('should check if balanced', () => {\r\n      const balanced = new PrecisionRecall(0.8, 0.82);\r\n      const unbalanced = new PrecisionRecall(0.8, 0.6);\r\n      \r\n      expect(balanced.isBalanced(0.05)).toBe(true);\r\n      expect(unbalanced.isBalanced(0.05)).toBe(false);\r\n    });\r\n\r\n    it('should calculate trade-off ratio', () => {\r\n      const pr = new PrecisionRecall(0.8, 0.4);\r\n      expect(pr.getTradeOffRatio()).toBe(2.0); // 0.8 / 0.4\r\n    });\r\n\r\n    it('should handle zero recall in trade-off ratio', () => {\r\n      const pr = new PrecisionRecall(0.8, 0.0);\r\n      expect(pr.getTradeOffRatio()).toBe(Infinity);\r\n    });\r\n\r\n    it('should handle zero precision and recall in trade-off ratio', () => {\r\n      const pr = new PrecisionRecall(0.0, 0.0);\r\n      expect(pr.getTradeOffRatio()).toBe(1);\r\n    });\r\n\r\n    it('should check threshold compliance', () => {\r\n      const pr = new PrecisionRecall(0.8, 0.6);\r\n      \r\n      expect(pr.meetsBothThresholds(0.7, 0.5)).toBe(true);\r\n      expect(pr.meetsBothThresholds(0.9, 0.5)).toBe(false);\r\n      expect(pr.meetsEitherThreshold(0.9, 0.5)).toBe(true);\r\n      expect(pr.meetsEitherThreshold(0.9, 0.7)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('Conversions', () => {\r\n    it('should convert to percentages', () => {\r\n      const pr = new PrecisionRecall(0.85, 0.75);\r\n      \r\n      expect(pr.getPrecisionPercentage()).toBe(85);\r\n      expect(pr.getRecallPercentage()).toBe(75);\r\n      expect(pr.getPrecisionPercentageString()).toBe('85.0%');\r\n      expect(pr.getRecallPercentageString()).toBe('75.0%');\r\n      expect(pr.getPrecisionPercentageString(2)).toBe('85.00%');\r\n    });\r\n\r\n    it('should convert to string', () => {\r\n      const pr = new PrecisionRecall(0.85, 0.75);\r\n      const str = pr.toString();\r\n      \r\n      expect(str).toContain('Precision: 85.0%');\r\n      expect(str).toContain('Recall: 75.0%');\r\n      expect(str).toContain('F1:');\r\n    });\r\n  });\r\n\r\n  describe('Mathematical Operations', () => {\r\n    it('should combine with another precision-recall pair', () => {\r\n      const pr1 = new PrecisionRecall(0.8, 0.6);\r\n      const pr2 = new PrecisionRecall(0.6, 0.8);\r\n      const combined = pr1.combineWith(pr2, 0.7);\r\n      \r\n      expect(combined.precision).toBeCloseTo(0.74); // 0.8 * 0.7 + 0.6 * 0.3\r\n      expect(combined.recall).toBeCloseTo(0.66); // 0.6 * 0.7 + 0.8 * 0.3\r\n    });\r\n\r\n    it('should throw error for invalid weight in combine', () => {\r\n      const pr1 = new PrecisionRecall(0.8, 0.6);\r\n      const pr2 = new PrecisionRecall(0.6, 0.8);\r\n      \r\n      expect(() => pr1.combineWith(pr2, -0.1)).toThrow('Weight must be between 0 and 1');\r\n      expect(() => pr1.combineWith(pr2, 1.1)).toThrow('Weight must be between 0 and 1');\r\n    });\r\n\r\n    it('should calculate distances', () => {\r\n      const pr1 = new PrecisionRecall(0.8, 0.6);\r\n      const pr2 = new PrecisionRecall(0.6, 0.8);\r\n      \r\n      const euclidean = pr1.distanceFrom(pr2);\r\n      const manhattan = pr1.manhattanDistanceFrom(pr2);\r\n      \r\n      expect(euclidean).toBeCloseTo(0.2828, 4); // sqrt((0.8-0.6)^2 + (0.6-0.8)^2)\r\n      expect(manhattan).toBe(0.4); // |0.8-0.6| + |0.6-0.8|\r\n    });\r\n\r\n    it('should compare based on F1 score', () => {\r\n      const pr1 = new PrecisionRecall(0.9, 0.7); // F1 ≈ 0.788\r\n      const pr2 = new PrecisionRecall(0.7, 0.9); // F1 ≈ 0.788\r\n      const pr3 = new PrecisionRecall(0.6, 0.6); // F1 = 0.6\r\n      \r\n      expect(pr1.isBetterThan(pr3)).toBe(true);\r\n      expect(pr3.isBetterThan(pr1)).toBe(false);\r\n    });\r\n\r\n    it('should round precision and recall', () => {\r\n      const pr = new PrecisionRecall(0.8567, 0.7234);\r\n      const rounded = pr.round(2);\r\n      \r\n      expect(rounded.precision).toBe(0.86);\r\n      expect(rounded.recall).toBe(0.72);\r\n    });\r\n  });\r\n\r\n  describe('Static Utility Methods', () => {\r\n    it('should calculate average', () => {\r\n      const pairs = [\r\n        new PrecisionRecall(0.8, 0.6),\r\n        new PrecisionRecall(0.6, 0.8),\r\n        new PrecisionRecall(0.9, 0.7),\r\n      ];\r\n      \r\n      const average = PrecisionRecall.average(pairs);\r\n      expect(average.precision).toBeCloseTo(0.7667, 3);\r\n      expect(average.recall).toBeCloseTo(0.7, 3);\r\n    });\r\n\r\n    it('should throw error for empty array in average', () => {\r\n      expect(() => PrecisionRecall.average([])).toThrow('Cannot calculate average of empty array');\r\n    });\r\n\r\n    it('should calculate weighted average', () => {\r\n      const pairs = [\r\n        new PrecisionRecall(0.8, 0.6),\r\n        new PrecisionRecall(0.6, 0.8),\r\n      ];\r\n      const weights = [0.7, 0.3];\r\n      \r\n      const weightedAvg = PrecisionRecall.weightedAverage(pairs, weights);\r\n      expect(weightedAvg.precision).toBeCloseTo(0.74);\r\n      expect(weightedAvg.recall).toBeCloseTo(0.66);\r\n    });\r\n\r\n    it('should throw error for mismatched arrays in weighted average', () => {\r\n      const pairs = [new PrecisionRecall(0.8, 0.6)];\r\n      const weights = [0.7, 0.3];\r\n      \r\n      expect(() => PrecisionRecall.weightedAverage(pairs, weights))\r\n        .toThrow('Pairs and weights arrays must have the same length');\r\n    });\r\n\r\n    it('should throw error for zero total weight', () => {\r\n      const pairs = [new PrecisionRecall(0.8, 0.6), new PrecisionRecall(0.6, 0.8)];\r\n      const weights = [0, 0];\r\n      \r\n      expect(() => PrecisionRecall.weightedAverage(pairs, weights))\r\n        .toThrow('Total weight cannot be zero');\r\n    });\r\n\r\n    it('should find best metrics', () => {\r\n      const pairs = [\r\n        new PrecisionRecall(0.8, 0.6), // F1 ≈ 0.686\r\n        new PrecisionRecall(0.6, 0.8), // F1 ≈ 0.686\r\n        new PrecisionRecall(0.9, 0.7), // F1 ≈ 0.788\r\n      ];\r\n      \r\n      const bestF1 = PrecisionRecall.bestF1(pairs);\r\n      const bestPrecision = PrecisionRecall.bestPrecision(pairs);\r\n      const bestRecall = PrecisionRecall.bestRecall(pairs);\r\n      \r\n      expect(bestF1.precision).toBe(0.9);\r\n      expect(bestPrecision.precision).toBe(0.9);\r\n      expect(bestRecall.recall).toBe(0.8);\r\n    });\r\n\r\n    it('should throw error for empty array in best methods', () => {\r\n      expect(() => PrecisionRecall.bestF1([])).toThrow('Cannot find best F1 of empty array');\r\n      expect(() => PrecisionRecall.bestPrecision([])).toThrow('Cannot find best precision of empty array');\r\n      expect(() => PrecisionRecall.bestRecall([])).toThrow('Cannot find best recall of empty array');\r\n    });\r\n  });\r\n\r\n  describe('JSON Serialization', () => {\r\n    it('should serialize to JSON', () => {\r\n      const pr = new PrecisionRecall(0.85, 0.75);\r\n      const json = pr.toJSON();\r\n      \r\n      expect(json.precision).toBe(0.85);\r\n      expect(json.recall).toBe(0.75);\r\n      expect(json.precisionPercentage).toBe(85);\r\n      expect(json.recallPercentage).toBe(75);\r\n      expect(json.f1Score).toBeCloseTo(0.7969, 4);\r\n      expect(json.isBalanced).toBe(false);\r\n    });\r\n\r\n    it('should deserialize from JSON', () => {\r\n      const json = { precision: 0.85, recall: 0.75 };\r\n      const pr = PrecisionRecall.fromJSON(json);\r\n      \r\n      expect(pr.precision).toBe(0.85);\r\n      expect(pr.recall).toBe(0.75);\r\n    });\r\n  });\r\n\r\n  describe('Equality', () => {\r\n    it('should be equal to another precision-recall pair with same values', () => {\r\n      const pr1 = new PrecisionRecall(0.85, 0.75);\r\n      const pr2 = new PrecisionRecall(0.85, 0.75);\r\n      \r\n      expect(pr1.equals(pr2)).toBe(true);\r\n    });\r\n\r\n    it('should not be equal to precision-recall pair with different values', () => {\r\n      const pr1 = new PrecisionRecall(0.85, 0.75);\r\n      const pr2 = new PrecisionRecall(0.75, 0.85);\r\n      \r\n      expect(pr1.equals(pr2)).toBe(false);\r\n    });\r\n  });\r\n});"], "version": 3}