33ff402b17c97887cbb1f41ac0545650
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowSchedule = void 0;
const typeorm_1 = require("typeorm");
const notification_workflow_entity_1 = require("./notification-workflow.entity");
/**
 * Workflow Schedule Entity
 *
 * Represents scheduled triggers for notification workflows with
 * comprehensive scheduling configuration and execution tracking.
 */
let WorkflowSchedule = class WorkflowSchedule {
    // Computed properties
    get isCronSchedule() {
        return this.triggerType === 'cron';
    }
    get isEventTrigger() {
        return this.triggerType === 'event';
    }
    get isWebhookTrigger() {
        return this.triggerType === 'webhook';
    }
    get successRate() {
        if (this.executionCount === 0)
            return 0;
        return (this.successCount / this.executionCount) * 100;
    }
    get failureRate() {
        if (this.executionCount === 0)
            return 0;
        return (this.failureCount / this.executionCount) * 100;
    }
    get isOverdue() {
        return this.nextExecution && new Date() > this.nextExecution;
    }
    get daysSinceLastExecution() {
        if (!this.lastExecution)
            return -1;
        const now = new Date();
        const diffTime = Math.abs(now.getTime() - this.lastExecution.getTime());
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
    get isHealthy() {
        // Consider schedule healthy if success rate > 90% and no recent failures
        const recentFailureThreshold = 24 * 60 * 60 * 1000; // 24 hours
        const hasRecentFailure = this.lastFailure &&
            (Date.now() - this.lastFailure.getTime()) < recentFailureThreshold;
        return this.successRate >= 90 && !hasRecentFailure;
    }
    /**
     * Get schedule summary
     */
    getScheduleSummary() {
        return {
            id: this.id,
            workflowId: this.workflowId,
            triggerType: this.triggerType,
            cronExpression: this.cronExpression,
            eventType: this.eventType,
            webhookPath: this.webhookPath,
            isActive: this.isActive,
            nextExecution: this.nextExecution,
            lastExecution: this.lastExecution,
            executionCount: this.executionCount,
            successRate: this.successRate,
            failureRate: this.failureRate,
            isHealthy: this.isHealthy,
            isOverdue: this.isOverdue,
        };
    }
    /**
     * Get execution statistics
     */
    getExecutionStats() {
        return {
            totalExecutions: this.executionCount,
            successfulExecutions: this.successCount,
            failedExecutions: this.failureCount,
            successRate: this.successRate,
            failureRate: this.failureRate,
            lastExecution: this.lastExecution,
            lastSuccess: this.lastSuccess,
            lastFailure: this.lastFailure,
            daysSinceLastExecution: this.daysSinceLastExecution,
            isHealthy: this.isHealthy,
        };
    }
    /**
     * Get trigger configuration
     */
    getTriggerConfig() {
        const config = {
            type: this.triggerType,
        };
        switch (this.triggerType) {
            case 'cron':
                config.cronExpression = this.cronExpression;
                config.timezone = this.timezone;
                config.nextExecution = this.nextExecution;
                break;
            case 'event':
                config.eventType = this.eventType;
                config.eventPattern = this.eventPattern;
                config.conditions = this.conditions;
                break;
            case 'webhook':
                config.webhookPath = this.webhookPath;
                config.webhookMethod = this.webhookMethod;
                config.authentication = this.webhookAuthentication;
                break;
        }
        return config;
    }
    /**
     * Check if schedule should execute now
     */
    shouldExecuteNow() {
        if (!this.isActive)
            return false;
        if (this.triggerType !== 'cron')
            return false;
        if (!this.nextExecution)
            return false;
        return new Date() >= this.nextExecution;
    }
    /**
     * Check if event matches this schedule
     */
    matchesEvent(eventType, eventData) {
        if (!this.isActive)
            return false;
        if (this.triggerType !== 'event')
            return false;
        if (this.eventType !== eventType)
            return false;
        // Check event pattern
        if (this.eventPattern) {
            for (const [key, value] of Object.entries(this.eventPattern)) {
                const eventValue = this.getNestedValue(eventData, key);
                if (eventValue !== value)
                    return false;
            }
        }
        // Check conditions
        if (this.conditions && this.conditions.length > 0) {
            for (const condition of this.conditions) {
                const fieldValue = this.getNestedValue(eventData, condition.field);
                if (!this.evaluateCondition(fieldValue, condition.operator, condition.value)) {
                    return false;
                }
            }
        }
        return true;
    }
    /**
     * Check if webhook request matches this schedule
     */
    matchesWebhook(path, method) {
        if (!this.isActive)
            return false;
        if (this.triggerType !== 'webhook')
            return false;
        if (this.webhookPath !== path)
            return false;
        if (this.webhookMethod !== method)
            return false;
        return true;
    }
    /**
     * Record execution attempt
     */
    recordExecution(success, error) {
        this.executionCount++;
        this.lastExecution = new Date();
        if (success) {
            this.successCount++;
            this.lastSuccess = new Date();
            this.lastError = null;
        }
        else {
            this.failureCount++;
            this.lastFailure = new Date();
            this.lastError = error || 'Unknown error';
        }
    }
    /**
     * Calculate next execution time for cron schedules
     */
    calculateNextExecution() {
        if (this.triggerType !== 'cron' || !this.cronExpression) {
            return null;
        }
        // Simple implementation - in production, use a proper cron parser
        const now = new Date();
        return new Date(now.getTime() + 60000); // Next minute for simplicity
    }
    /**
     * Check if schedule is within execution window
     */
    isWithinExecutionWindow() {
        const window = this.configuration?.executionWindow;
        if (!window || !window.start || !window.end) {
            return true; // No window restriction
        }
        const now = new Date();
        const timezone = window.timezone || this.timezone || 'UTC';
        // Simple time window check - in production, use proper timezone handling
        const currentHour = now.getHours();
        const startHour = parseInt(window.start.split(':')[0]);
        const endHour = parseInt(window.end.split(':')[0]);
        return currentHour >= startHour && currentHour <= endHour;
    }
    /**
     * Check if schedule should skip due to business rules
     */
    shouldSkipExecution() {
        const businessRules = this.configuration?.businessRules;
        if (!businessRules)
            return false;
        const now = new Date();
        // Check weekends
        if (businessRules.skipWeekends) {
            const dayOfWeek = now.getDay();
            if (dayOfWeek === 0 || dayOfWeek === 6) { // Sunday or Saturday
                return true;
            }
        }
        // Check business hours
        if (businessRules.businessHoursOnly && !this.isWithinExecutionWindow()) {
            return true;
        }
        // Check holidays (simplified)
        if (businessRules.skipHolidays) {
            // In production, integrate with holiday calendar service
            // For now, just check if it's a major holiday
            const month = now.getMonth() + 1;
            const day = now.getDate();
            // New Year's Day, Christmas
            if ((month === 1 && day === 1) || (month === 12 && day === 25)) {
                return true;
            }
        }
        return false;
    }
    /**
     * Get schedule health status
     */
    getHealthStatus() {
        const issues = [];
        let status = 'healthy';
        if (!this.isActive) {
            status = 'inactive';
            issues.push('Schedule is inactive');
        }
        if (this.isOverdue) {
            status = 'warning';
            issues.push('Schedule is overdue');
        }
        if (this.successRate < 90) {
            status = 'warning';
            issues.push(`Low success rate: ${this.successRate.toFixed(1)}%`);
        }
        if (this.successRate < 50) {
            status = 'critical';
        }
        const recentFailureThreshold = 24 * 60 * 60 * 1000; // 24 hours
        if (this.lastFailure && (Date.now() - this.lastFailure.getTime()) < recentFailureThreshold) {
            status = 'warning';
            issues.push('Recent execution failure');
        }
        return { status, issues };
    }
    /**
     * Private helper methods
     */
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current?.[key], obj);
    }
    evaluateCondition(value, operator, expected) {
        switch (operator) {
            case 'eq': return value == expected;
            case 'neq': return value != expected;
            case 'gt': return value > expected;
            case 'gte': return value >= expected;
            case 'lt': return value < expected;
            case 'lte': return value <= expected;
            case 'in': return Array.isArray(expected) && expected.includes(value);
            case 'contains': return String(value).includes(String(expected));
            case 'regex': return new RegExp(expected).test(String(value));
            default: return false;
        }
    }
};
exports.WorkflowSchedule = WorkflowSchedule;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], WorkflowSchedule.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'workflow_id' }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], WorkflowSchedule.prototype, "workflowId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'trigger_type' }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], WorkflowSchedule.prototype, "triggerType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'cron_expression', nullable: true }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], WorkflowSchedule.prototype, "cronExpression", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'timezone', default: 'UTC' }),
    __metadata("design:type", String)
], WorkflowSchedule.prototype, "timezone", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'event_type', nullable: true }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], WorkflowSchedule.prototype, "eventType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'event_pattern', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_a = typeof Record !== "undefined" && Record) === "function" ? _a : Object)
], WorkflowSchedule.prototype, "eventPattern", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'webhook_path', nullable: true }),
    __metadata("design:type", String)
], WorkflowSchedule.prototype, "webhookPath", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'webhook_method', default: 'POST' }),
    __metadata("design:type", String)
], WorkflowSchedule.prototype, "webhookMethod", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'webhook_authentication', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], WorkflowSchedule.prototype, "webhookAuthentication", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'conditions', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Array !== "undefined" && Array) === "function" ? _b : Object)
], WorkflowSchedule.prototype, "conditions", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_active', default: true }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", Boolean)
], WorkflowSchedule.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'next_execution', type: 'timestamp', nullable: true }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], WorkflowSchedule.prototype, "nextExecution", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_execution', type: 'timestamp', nullable: true }),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], WorkflowSchedule.prototype, "lastExecution", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'execution_count', default: 0 }),
    __metadata("design:type", Number)
], WorkflowSchedule.prototype, "executionCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'success_count', default: 0 }),
    __metadata("design:type", Number)
], WorkflowSchedule.prototype, "successCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'failure_count', default: 0 }),
    __metadata("design:type", Number)
], WorkflowSchedule.prototype, "failureCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_success', type: 'timestamp', nullable: true }),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], WorkflowSchedule.prototype, "lastSuccess", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_failure', type: 'timestamp', nullable: true }),
    __metadata("design:type", typeof (_f = typeof Date !== "undefined" && Date) === "function" ? _f : Object)
], WorkflowSchedule.prototype, "lastFailure", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_error', type: 'text', nullable: true }),
    __metadata("design:type", String)
], WorkflowSchedule.prototype, "lastError", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'configuration', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], WorkflowSchedule.prototype, "configuration", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'metadata', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], WorkflowSchedule.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by' }),
    __metadata("design:type", String)
], WorkflowSchedule.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by' }),
    __metadata("design:type", String)
], WorkflowSchedule.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_g = typeof Date !== "undefined" && Date) === "function" ? _g : Object)
], WorkflowSchedule.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", typeof (_h = typeof Date !== "undefined" && Date) === "function" ? _h : Object)
], WorkflowSchedule.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => notification_workflow_entity_1.NotificationWorkflow, workflow => workflow.schedules),
    (0, typeorm_1.JoinColumn)({ name: 'workflow_id' }),
    __metadata("design:type", typeof (_j = typeof notification_workflow_entity_1.NotificationWorkflow !== "undefined" && notification_workflow_entity_1.NotificationWorkflow) === "function" ? _j : Object)
], WorkflowSchedule.prototype, "workflow", void 0);
exports.WorkflowSchedule = WorkflowSchedule = __decorate([
    (0, typeorm_1.Entity)('workflow_schedules'),
    (0, typeorm_1.Index)(['workflowId']),
    (0, typeorm_1.Index)(['triggerType']),
    (0, typeorm_1.Index)(['isActive']),
    (0, typeorm_1.Index)(['nextExecution']),
    (0, typeorm_1.Index)(['cronExpression']),
    (0, typeorm_1.Index)(['eventType'])
], WorkflowSchedule);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiQzpcXFVzZXJzXFxMdWthXFxzZW50aW5lbFxcYmFja2VuZFxcc3JjXFxtb2R1bGVzXFxyZXBvcnRpbmdcXGFsZXJ0aW5nXFxlbnRpdGllc1xcd29ya2Zsb3ctc2NoZWR1bGUuZW50aXR5LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBQSxxQ0FTaUI7QUFDakIsaUZBQXNFO0FBRXRFOzs7OztHQUtHO0FBUUksSUFBTSxnQkFBZ0IsR0FBdEIsTUFBTSxnQkFBZ0I7SUFxSjNCLHNCQUFzQjtJQUN0QixJQUFJLGNBQWM7UUFDaEIsT0FBTyxJQUFJLENBQUMsV0FBVyxLQUFLLE1BQU0sQ0FBQztJQUNyQyxDQUFDO0lBRUQsSUFBSSxjQUFjO1FBQ2hCLE9BQU8sSUFBSSxDQUFDLFdBQVcsS0FBSyxPQUFPLENBQUM7SUFDdEMsQ0FBQztJQUVELElBQUksZ0JBQWdCO1FBQ2xCLE9BQU8sSUFBSSxDQUFDLFdBQVcsS0FBSyxTQUFTLENBQUM7SUFDeEMsQ0FBQztJQUVELElBQUksV0FBVztRQUNiLElBQUksSUFBSSxDQUFDLGNBQWMsS0FBSyxDQUFDO1lBQUUsT0FBTyxDQUFDLENBQUM7UUFDeEMsT0FBTyxDQUFDLElBQUksQ0FBQyxZQUFZLEdBQUcsSUFBSSxDQUFDLGNBQWMsQ0FBQyxHQUFHLEdBQUcsQ0FBQztJQUN6RCxDQUFDO0lBRUQsSUFBSSxXQUFXO1FBQ2IsSUFBSSxJQUFJLENBQUMsY0FBYyxLQUFLLENBQUM7WUFBRSxPQUFPLENBQUMsQ0FBQztRQUN4QyxPQUFPLENBQUMsSUFBSSxDQUFDLFlBQVksR0FBRyxJQUFJLENBQUMsY0FBYyxDQUFDLEdBQUcsR0FBRyxDQUFDO0lBQ3pELENBQUM7SUFFRCxJQUFJLFNBQVM7UUFDWCxPQUFPLElBQUksQ0FBQyxhQUFhLElBQUksSUFBSSxJQUFJLEVBQUUsR0FBRyxJQUFJLENBQUMsYUFBYSxDQUFDO0lBQy9ELENBQUM7SUFFRCxJQUFJLHNCQUFzQjtRQUN4QixJQUFJLENBQUMsSUFBSSxDQUFDLGFBQWE7WUFBRSxPQUFPLENBQUMsQ0FBQyxDQUFDO1FBQ25DLE1BQU0sR0FBRyxHQUFHLElBQUksSUFBSSxFQUFFLENBQUM7UUFDdkIsTUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsT0FBTyxFQUFFLEdBQUcsSUFBSSxDQUFDLGFBQWEsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxDQUFDO1FBQ3hFLE9BQU8sSUFBSSxDQUFDLElBQUksQ0FBQyxRQUFRLEdBQUcsQ0FBQyxJQUFJLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLENBQUMsQ0FBQyxDQUFDO0lBQ3JELENBQUM7SUFFRCxJQUFJLFNBQVM7UUFDWCx5RUFBeUU7UUFDekUsTUFBTSxzQkFBc0IsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxJQUFJLENBQUMsQ0FBQyxXQUFXO1FBQy9ELE1BQU0sZ0JBQWdCLEdBQUcsSUFBSSxDQUFDLFdBQVc7WUFDdkMsQ0FBQyxJQUFJLENBQUMsR0FBRyxFQUFFLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQyxPQUFPLEVBQUUsQ0FBQyxHQUFHLHNCQUFzQixDQUFDO1FBRXJFLE9BQU8sSUFBSSxDQUFDLFdBQVcsSUFBSSxFQUFFLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQztJQUNyRCxDQUFDO0lBRUQ7O09BRUc7SUFDSCxrQkFBa0I7UUFDaEIsT0FBTztZQUNMLEVBQUUsRUFBRSxJQUFJLENBQUMsRUFBRTtZQUNYLFVBQVUsRUFBRSxJQUFJLENBQUMsVUFBVTtZQUMzQixXQUFXLEVBQUUsSUFBSSxDQUFDLFdBQVc7WUFDN0IsY0FBYyxFQUFFLElBQUksQ0FBQyxjQUFjO1lBQ25DLFNBQVMsRUFBRSxJQUFJLENBQUMsU0FBUztZQUN6QixXQUFXLEVBQUUsSUFBSSxDQUFDLFdBQVc7WUFDN0IsUUFBUSxFQUFFLElBQUksQ0FBQyxRQUFRO1lBQ3ZCLGFBQWEsRUFBRSxJQUFJLENBQUMsYUFBYTtZQUNqQyxhQUFhLEVBQUUsSUFBSSxDQUFDLGFBQWE7WUFDakMsY0FBYyxFQUFFLElBQUksQ0FBQyxjQUFjO1lBQ25DLFdBQVcsRUFBRSxJQUFJLENBQUMsV0FBVztZQUM3QixXQUFXLEVBQUUsSUFBSSxDQUFDLFdBQVc7WUFDN0IsU0FBUyxFQUFFLElBQUksQ0FBQyxTQUFTO1lBQ3pCLFNBQVMsRUFBRSxJQUFJLENBQUMsU0FBUztTQUMxQixDQUFDO0lBQ0osQ0FBQztJQUVEOztPQUVHO0lBQ0gsaUJBQWlCO1FBQ2YsT0FBTztZQUNMLGVBQWUsRUFBRSxJQUFJLENBQUMsY0FBYztZQUNwQyxvQkFBb0IsRUFBRSxJQUFJLENBQUMsWUFBWTtZQUN2QyxnQkFBZ0IsRUFBRSxJQUFJLENBQUMsWUFBWTtZQUNuQyxXQUFXLEVBQUUsSUFBSSxDQUFDLFdBQVc7WUFDN0IsV0FBVyxFQUFFLElBQUksQ0FBQyxXQUFXO1lBQzdCLGFBQWEsRUFBRSxJQUFJLENBQUMsYUFBYTtZQUNqQyxXQUFXLEVBQUUsSUFBSSxDQUFDLFdBQVc7WUFDN0IsV0FBVyxFQUFFLElBQUksQ0FBQyxXQUFXO1lBQzdCLHNCQUFzQixFQUFFLElBQUksQ0FBQyxzQkFBc0I7WUFDbkQsU0FBUyxFQUFFLElBQUksQ0FBQyxTQUFTO1NBQzFCLENBQUM7SUFDSixDQUFDO0lBRUQ7O09BRUc7SUFDSCxnQkFBZ0I7UUFDZCxNQUFNLE1BQU0sR0FBd0I7WUFDbEMsSUFBSSxFQUFFLElBQUksQ0FBQyxXQUFXO1NBQ3ZCLENBQUM7UUFFRixRQUFRLElBQUksQ0FBQyxXQUFXLEVBQUUsQ0FBQztZQUN6QixLQUFLLE1BQU07Z0JBQ1QsTUFBTSxDQUFDLGNBQWMsR0FBRyxJQUFJLENBQUMsY0FBYyxDQUFDO2dCQUM1QyxNQUFNLENBQUMsUUFBUSxHQUFHLElBQUksQ0FBQyxRQUFRLENBQUM7Z0JBQ2hDLE1BQU0sQ0FBQyxhQUFhLEdBQUcsSUFBSSxDQUFDLGFBQWEsQ0FBQztnQkFDMUMsTUFBTTtZQUNSLEtBQUssT0FBTztnQkFDVixNQUFNLENBQUMsU0FBUyxHQUFHLElBQUksQ0FBQyxTQUFTLENBQUM7Z0JBQ2xDLE1BQU0sQ0FBQyxZQUFZLEdBQUcsSUFBSSxDQUFDLFlBQVksQ0FBQztnQkFDeEMsTUFBTSxDQUFDLFVBQVUsR0FBRyxJQUFJLENBQUMsVUFBVSxDQUFDO2dCQUNwQyxNQUFNO1lBQ1IsS0FBSyxTQUFTO2dCQUNaLE1BQU0sQ0FBQyxXQUFXLEdBQUcsSUFBSSxDQUFDLFdBQVcsQ0FBQztnQkFDdEMsTUFBTSxDQUFDLGFBQWEsR0FBRyxJQUFJLENBQUMsYUFBYSxDQUFDO2dCQUMxQyxNQUFNLENBQUMsY0FBYyxHQUFHLElBQUksQ0FBQyxxQkFBcUIsQ0FBQztnQkFDbkQsTUFBTTtRQUNWLENBQUM7UUFFRCxPQUFPLE1BQU0sQ0FBQztJQUNoQixDQUFDO0lBRUQ7O09BRUc7SUFDSCxnQkFBZ0I7UUFDZCxJQUFJLENBQUMsSUFBSSxDQUFDLFFBQVE7WUFBRSxPQUFPLEtBQUssQ0FBQztRQUNqQyxJQUFJLElBQUksQ0FBQyxXQUFXLEtBQUssTUFBTTtZQUFFLE9BQU8sS0FBSyxDQUFDO1FBQzlDLElBQUksQ0FBQyxJQUFJLENBQUMsYUFBYTtZQUFFLE9BQU8sS0FBSyxDQUFDO1FBRXRDLE9BQU8sSUFBSSxJQUFJLEVBQUUsSUFBSSxJQUFJLENBQUMsYUFBYSxDQUFDO0lBQzFDLENBQUM7SUFFRDs7T0FFRztJQUNILFlBQVksQ0FBQyxTQUFpQixFQUFFLFNBQWM7UUFDNUMsSUFBSSxDQUFDLElBQUksQ0FBQyxRQUFRO1lBQUUsT0FBTyxLQUFLLENBQUM7UUFDakMsSUFBSSxJQUFJLENBQUMsV0FBVyxLQUFLLE9BQU87WUFBRSxPQUFPLEtBQUssQ0FBQztRQUMvQyxJQUFJLElBQUksQ0FBQyxTQUFTLEtBQUssU0FBUztZQUFFLE9BQU8sS0FBSyxDQUFDO1FBRS9DLHNCQUFzQjtRQUN0QixJQUFJLElBQUksQ0FBQyxZQUFZLEVBQUUsQ0FBQztZQUN0QixLQUFLLE1BQU0sQ0FBQyxHQUFHLEVBQUUsS0FBSyxDQUFDLElBQUksTUFBTSxDQUFDLE9BQU8sQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLEVBQUUsQ0FBQztnQkFDN0QsTUFBTSxVQUFVLEdBQUcsSUFBSSxDQUFDLGNBQWMsQ0FBQyxTQUFTLEVBQUUsR0FBRyxDQUFDLENBQUM7Z0JBQ3ZELElBQUksVUFBVSxLQUFLLEtBQUs7b0JBQUUsT0FBTyxLQUFLLENBQUM7WUFDekMsQ0FBQztRQUNILENBQUM7UUFFRCxtQkFBbUI7UUFDbkIsSUFBSSxJQUFJLENBQUMsVUFBVSxJQUFJLElBQUksQ0FBQyxVQUFVLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRSxDQUFDO1lBQ2xELEtBQUssTUFBTSxTQUFTLElBQUksSUFBSSxDQUFDLFVBQVUsRUFBRSxDQUFDO2dCQUN4QyxNQUFNLFVBQVUsR0FBRyxJQUFJLENBQUMsY0FBYyxDQUFDLFNBQVMsRUFBRSxTQUFTLENBQUMsS0FBSyxDQUFDLENBQUM7Z0JBQ25FLElBQUksQ0FBQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsVUFBVSxFQUFFLFNBQVMsQ0FBQyxRQUFRLEVBQUUsU0FBUyxDQUFDLEtBQUssQ0FBQyxFQUFFLENBQUM7b0JBQzdFLE9BQU8sS0FBSyxDQUFDO2dCQUNmLENBQUM7WUFDSCxDQUFDO1FBQ0gsQ0FBQztRQUVELE9BQU8sSUFBSSxDQUFDO0lBQ2QsQ0FBQztJQUVEOztPQUVHO0lBQ0gsY0FBYyxDQUFDLElBQVksRUFBRSxNQUFjO1FBQ3pDLElBQUksQ0FBQyxJQUFJLENBQUMsUUFBUTtZQUFFLE9BQU8sS0FBSyxDQUFDO1FBQ2pDLElBQUksSUFBSSxDQUFDLFdBQVcsS0FBSyxTQUFTO1lBQUUsT0FBTyxLQUFLLENBQUM7UUFDakQsSUFBSSxJQUFJLENBQUMsV0FBVyxLQUFLLElBQUk7WUFBRSxPQUFPLEtBQUssQ0FBQztRQUM1QyxJQUFJLElBQUksQ0FBQyxhQUFhLEtBQUssTUFBTTtZQUFFLE9BQU8sS0FBSyxDQUFDO1FBRWhELE9BQU8sSUFBSSxDQUFDO0lBQ2QsQ0FBQztJQUVEOztPQUVHO0lBQ0gsZUFBZSxDQUFDLE9BQWdCLEVBQUUsS0FBYztRQUM5QyxJQUFJLENBQUMsY0FBYyxFQUFFLENBQUM7UUFDdEIsSUFBSSxDQUFDLGFBQWEsR0FBRyxJQUFJLElBQUksRUFBRSxDQUFDO1FBRWhDLElBQUksT0FBTyxFQUFFLENBQUM7WUFDWixJQUFJLENBQUMsWUFBWSxFQUFFLENBQUM7WUFDcEIsSUFBSSxDQUFDLFdBQVcsR0FBRyxJQUFJLElBQUksRUFBRSxDQUFDO1lBQzlCLElBQUksQ0FBQyxTQUFTLEdBQUcsSUFBSSxDQUFDO1FBQ3hCLENBQUM7YUFBTSxDQUFDO1lBQ04sSUFBSSxDQUFDLFlBQVksRUFBRSxDQUFDO1lBQ3BCLElBQUksQ0FBQyxXQUFXLEdBQUcsSUFBSSxJQUFJLEVBQUUsQ0FBQztZQUM5QixJQUFJLENBQUMsU0FBUyxHQUFHLEtBQUssSUFBSSxlQUFlLENBQUM7UUFDNUMsQ0FBQztJQUNILENBQUM7SUFFRDs7T0FFRztJQUNILHNCQUFzQjtRQUNwQixJQUFJLElBQUksQ0FBQyxXQUFXLEtBQUssTUFBTSxJQUFJLENBQUMsSUFBSSxDQUFDLGNBQWMsRUFBRSxDQUFDO1lBQ3hELE9BQU8sSUFBSSxDQUFDO1FBQ2QsQ0FBQztRQUVELGtFQUFrRTtRQUNsRSxNQUFNLEdBQUcsR0FBRyxJQUFJLElBQUksRUFBRSxDQUFDO1FBQ3ZCLE9BQU8sSUFBSSxJQUFJLENBQUMsR0FBRyxDQUFDLE9BQU8sRUFBRSxHQUFHLEtBQUssQ0FBQyxDQUFDLENBQUMsNkJBQTZCO0lBQ3ZFLENBQUM7SUFFRDs7T0FFRztJQUNILHVCQUF1QjtRQUNyQixNQUFNLE1BQU0sR0FBRyxJQUFJLENBQUMsYUFBYSxFQUFFLGVBQWUsQ0FBQztRQUNuRCxJQUFJLENBQUMsTUFBTSxJQUFJLENBQUMsTUFBTSxDQUFDLEtBQUssSUFBSSxDQUFDLE1BQU0sQ0FBQyxHQUFHLEVBQUUsQ0FBQztZQUM1QyxPQUFPLElBQUksQ0FBQyxDQUFDLHdCQUF3QjtRQUN2QyxDQUFDO1FBRUQsTUFBTSxHQUFHLEdBQUcsSUFBSSxJQUFJLEVBQUUsQ0FBQztRQUN2QixNQUFNLFFBQVEsR0FBRyxNQUFNLENBQUMsUUFBUSxJQUFJLElBQUksQ0FBQyxRQUFRLElBQUksS0FBSyxDQUFDO1FBRTNELHlFQUF5RTtRQUN6RSxNQUFNLFdBQVcsR0FBRyxHQUFHLENBQUMsUUFBUSxFQUFFLENBQUM7UUFDbkMsTUFBTSxTQUFTLEdBQUcsUUFBUSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDdkQsTUFBTSxPQUFPLEdBQUcsUUFBUSxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFFbkQsT0FBTyxXQUFXLElBQUksU0FBUyxJQUFJLFdBQVcsSUFBSSxPQUFPLENBQUM7SUFDNUQsQ0FBQztJQUVEOztPQUVHO0lBQ0gsbUJBQW1CO1FBQ2pCLE1BQU0sYUFBYSxHQUFHLElBQUksQ0FBQyxhQUFhLEVBQUUsYUFBYSxDQUFDO1FBQ3hELElBQUksQ0FBQyxhQUFhO1lBQUUsT0FBTyxLQUFLLENBQUM7UUFFakMsTUFBTSxHQUFHLEdBQUcsSUFBSSxJQUFJLEVBQUUsQ0FBQztRQUV2QixpQkFBaUI7UUFDakIsSUFBSSxhQUFhLENBQUMsWUFBWSxFQUFFLENBQUM7WUFDL0IsTUFBTSxTQUFTLEdBQUcsR0FBRyxDQUFDLE1BQU0sRUFBRSxDQUFDO1lBQy9CLElBQUksU0FBUyxLQUFLLENBQUMsSUFBSSxTQUFTLEtBQUssQ0FBQyxFQUFFLENBQUMsQ0FBQyxxQkFBcUI7Z0JBQzdELE9BQU8sSUFBSSxDQUFDO1lBQ2QsQ0FBQztRQUNILENBQUM7UUFFRCx1QkFBdUI7UUFDdkIsSUFBSSxhQUFhLENBQUMsaUJBQWlCLElBQUksQ0FBQyxJQUFJLENBQUMsdUJBQXVCLEVBQUUsRUFBRSxDQUFDO1lBQ3ZFLE9BQU8sSUFBSSxDQUFDO1FBQ2QsQ0FBQztRQUVELDhCQUE4QjtRQUM5QixJQUFJLGFBQWEsQ0FBQyxZQUFZLEVBQUUsQ0FBQztZQUMvQix5REFBeUQ7WUFDekQsOENBQThDO1lBQzlDLE1BQU0sS0FBSyxHQUFHLEdBQUcsQ0FBQyxRQUFRLEVBQUUsR0FBRyxDQUFDLENBQUM7WUFDakMsTUFBTSxHQUFHLEdBQUcsR0FBRyxDQUFDLE9BQU8sRUFBRSxDQUFDO1lBRTFCLDRCQUE0QjtZQUM1QixJQUFJLENBQUMsS0FBSyxLQUFLLENBQUMsSUFBSSxHQUFHLEtBQUssQ0FBQyxDQUFDLElBQUksQ0FBQyxLQUFLLEtBQUssRUFBRSxJQUFJLEdBQUcsS0FBSyxFQUFFLENBQUMsRUFBRSxDQUFDO2dCQUMvRCxPQUFPLElBQUksQ0FBQztZQUNkLENBQUM7UUFDSCxDQUFDO1FBRUQsT0FBTyxLQUFLLENBQUM7SUFDZixDQUFDO0lBRUQ7O09BRUc7SUFDSCxlQUFlO1FBQ2IsTUFBTSxNQUFNLEdBQWEsRUFBRSxDQUFDO1FBQzVCLElBQUksTUFBTSxHQUFHLFNBQVMsQ0FBQztRQUV2QixJQUFJLENBQUMsSUFBSSxDQUFDLFFBQVEsRUFBRSxDQUFDO1lBQ25CLE1BQU0sR0FBRyxVQUFVLENBQUM7WUFDcEIsTUFBTSxDQUFDLElBQUksQ0FBQyxzQkFBc0IsQ0FBQyxDQUFDO1FBQ3RDLENBQUM7UUFFRCxJQUFJLElBQUksQ0FBQyxTQUFTLEVBQUUsQ0FBQztZQUNuQixNQUFNLEdBQUcsU0FBUyxDQUFDO1lBQ25CLE1BQU0sQ0FBQyxJQUFJLENBQUMscUJBQXFCLENBQUMsQ0FBQztRQUNyQyxDQUFDO1FBRUQsSUFBSSxJQUFJLENBQUMsV0FBVyxHQUFHLEVBQUUsRUFBRSxDQUFDO1lBQzFCLE1BQU0sR0FBRyxTQUFTLENBQUM7WUFDbkIsTUFBTSxDQUFDLElBQUksQ0FBQyxxQkFBcUIsSUFBSSxDQUFDLFdBQVcsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1FBQ25FLENBQUM7UUFFRCxJQUFJLElBQUksQ0FBQyxXQUFXLEdBQUcsRUFBRSxFQUFFLENBQUM7WUFDMUIsTUFBTSxHQUFHLFVBQVUsQ0FBQztRQUN0QixDQUFDO1FBRUQsTUFBTSxzQkFBc0IsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxJQUFJLENBQUMsQ0FBQyxXQUFXO1FBQy9ELElBQUksSUFBSSxDQUFDLFdBQVcsSUFBSSxDQUFDLElBQUksQ0FBQyxHQUFHLEVBQUUsR0FBRyxJQUFJLENBQUMsV0FBVyxDQUFDLE9BQU8sRUFBRSxDQUFDLEdBQUcsc0JBQXNCLEVBQUUsQ0FBQztZQUMzRixNQUFNLEdBQUcsU0FBUyxDQUFDO1lBQ25CLE1BQU0sQ0FBQyxJQUFJLENBQUMsMEJBQTBCLENBQUMsQ0FBQztRQUMxQyxDQUFDO1FBRUQsT0FBTyxFQUFFLE1BQU0sRUFBRSxNQUFNLEVBQUUsQ0FBQztJQUM1QixDQUFDO0lBRUQ7O09BRUc7SUFDSyxjQUFjLENBQUMsR0FBUSxFQUFFLElBQVk7UUFDM0MsT0FBTyxJQUFJLENBQUMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLE1BQU0sQ0FBQyxDQUFDLE9BQU8sRUFBRSxHQUFHLEVBQUUsRUFBRSxDQUFDLE9BQU8sRUFBRSxDQUFDLEdBQUcsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxDQUFDO0lBQ3ZFLENBQUM7SUFFTyxpQkFBaUIsQ0FBQyxLQUFVLEVBQUUsUUFBZ0IsRUFBRSxRQUFhO1FBQ25FLFFBQVEsUUFBUSxFQUFFLENBQUM7WUFDakIsS0FBSyxJQUFJLENBQUMsQ0FBQyxPQUFPLEtBQUssSUFBSSxRQUFRLENBQUM7WUFDcEMsS0FBSyxLQUFLLENBQUMsQ0FBQyxPQUFPLEtBQUssSUFBSSxRQUFRLENBQUM7WUFDckMsS0FBSyxJQUFJLENBQUMsQ0FBQyxPQUFPLEtBQUssR0FBRyxRQUFRLENBQUM7WUFDbkMsS0FBSyxLQUFLLENBQUMsQ0FBQyxPQUFPLEtBQUssSUFBSSxRQUFRLENBQUM7WUFDckMsS0FBSyxJQUFJLENBQUMsQ0FBQyxPQUFPLEtBQUssR0FBRyxRQUFRLENBQUM7WUFDbkMsS0FBSyxLQUFLLENBQUMsQ0FBQyxPQUFPLEtBQUssSUFBSSxRQUFRLENBQUM7WUFDckMsS0FBSyxJQUFJLENBQUMsQ0FBQyxPQUFPLEtBQUssQ0FBQyxPQUFPLENBQUMsUUFBUSxDQUFDLElBQUksUUFBUSxDQUFDLFFBQVEsQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUN0RSxLQUFLLFVBQVUsQ0FBQyxDQUFDLE9BQU8sTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQztZQUNqRSxLQUFLLE9BQU8sQ0FBQyxDQUFDLE9BQU8sSUFBSSxNQUFNLENBQUMsUUFBUSxDQUFDLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDO1lBQzlELE9BQU8sQ0FBQyxDQUFDLE9BQU8sS0FBSyxDQUFDO1FBQ3hCLENBQUM7SUFDSCxDQUFDO0NBQ0YsQ0FBQTtBQTFjWSw0Q0FBZ0I7QUFFM0I7SUFEQyxJQUFBLGdDQUFzQixFQUFDLE1BQU0sQ0FBQzs7NENBQ3BCO0FBSVg7SUFGQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsYUFBYSxFQUFFLENBQUM7SUFDL0IsSUFBQSxlQUFLLEdBQUU7O29EQUNXO0FBSW5CO0lBRkMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLGNBQWMsRUFBRSxDQUFDO0lBQ2hDLElBQUEsZUFBSyxHQUFFOztxREFDNkM7QUFJckQ7SUFGQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsaUJBQWlCLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDO0lBQ25ELElBQUEsZUFBSyxHQUFFOzt3REFDZTtBQUd2QjtJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxVQUFVLEVBQUUsT0FBTyxFQUFFLEtBQUssRUFBRSxDQUFDOztrREFDNUI7QUFJakI7SUFGQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsWUFBWSxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQztJQUM5QyxJQUFBLGVBQUssR0FBRTs7bURBQ1U7QUFHbEI7SUFEQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsZUFBZSxFQUFFLElBQUksRUFBRSxPQUFPLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDO2tEQUNuRCxNQUFNLG9CQUFOLE1BQU07c0RBQWM7QUFHbEM7SUFEQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsY0FBYyxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQzs7cURBQzdCO0FBR3BCO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLGdCQUFnQixFQUFFLE9BQU8sRUFBRSxNQUFNLEVBQUUsQ0FBQzs7dURBQ087QUFHM0Q7SUFEQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsd0JBQXdCLEVBQUUsSUFBSSxFQUFFLE9BQU8sRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7OytEQVF4RTtBQUdGO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLFlBQVksRUFBRSxJQUFJLEVBQUUsT0FBTyxFQUFFLFFBQVEsRUFBRSxJQUFJLEVBQUUsQ0FBQztrREFDbEQsS0FBSyxvQkFBTCxLQUFLO29EQUtkO0FBSUg7SUFGQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsV0FBVyxFQUFFLE9BQU8sRUFBRSxJQUFJLEVBQUUsQ0FBQztJQUM1QyxJQUFBLGVBQUssR0FBRTs7a0RBQ1U7QUFJbEI7SUFGQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsZ0JBQWdCLEVBQUUsSUFBSSxFQUFFLFdBQVcsRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7SUFDckUsSUFBQSxlQUFLLEdBQUU7a0RBQ08sSUFBSSxvQkFBSixJQUFJO3VEQUFDO0FBR3BCO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLGdCQUFnQixFQUFFLElBQUksRUFBRSxXQUFXLEVBQUUsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDO2tEQUN2RCxJQUFJLG9CQUFKLElBQUk7dURBQUM7QUFHcEI7SUFEQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsaUJBQWlCLEVBQUUsT0FBTyxFQUFFLENBQUMsRUFBRSxDQUFDOzt3REFDekI7QUFHdkI7SUFEQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsZUFBZSxFQUFFLE9BQU8sRUFBRSxDQUFDLEVBQUUsQ0FBQzs7c0RBQ3pCO0FBR3JCO0lBREMsSUFBQSxnQkFBTSxFQUFDLEVBQUUsSUFBSSxFQUFFLGVBQWUsRUFBRSxPQUFPLEVBQUUsQ0FBQyxFQUFFLENBQUM7O3NEQUN6QjtBQUdyQjtJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxjQUFjLEVBQUUsSUFBSSxFQUFFLFdBQVcsRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7a0RBQ3ZELElBQUksb0JBQUosSUFBSTtxREFBQztBQUdsQjtJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxjQUFjLEVBQUUsSUFBSSxFQUFFLFdBQVcsRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7a0RBQ3ZELElBQUksb0JBQUosSUFBSTtxREFBQztBQUdsQjtJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxZQUFZLEVBQUUsSUFBSSxFQUFFLE1BQU0sRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7O21EQUMzQztBQUdsQjtJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxlQUFlLEVBQUUsSUFBSSxFQUFFLE9BQU8sRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7O3VEQTZCL0Q7QUFHRjtJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxVQUFVLEVBQUUsSUFBSSxFQUFFLE9BQU8sRUFBRSxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUM7O2tEQW9CMUQ7QUFHRjtJQURDLElBQUEsZ0JBQU0sRUFBQyxFQUFFLElBQUksRUFBRSxZQUFZLEVBQUUsQ0FBQzs7bURBQ2I7QUFHbEI7SUFEQyxJQUFBLGdCQUFNLEVBQUMsRUFBRSxJQUFJLEVBQUUsWUFBWSxFQUFFLENBQUM7O21EQUNiO0FBR2xCO0lBREMsSUFBQSwwQkFBZ0IsRUFBQyxFQUFFLElBQUksRUFBRSxZQUFZLEVBQUUsQ0FBQztrREFDOUIsSUFBSSxvQkFBSixJQUFJO21EQUFDO0FBR2hCO0lBREMsSUFBQSwwQkFBZ0IsRUFBQyxFQUFFLElBQUksRUFBRSxZQUFZLEVBQUUsQ0FBQztrREFDOUIsSUFBSSxvQkFBSixJQUFJO21EQUFDO0FBS2hCO0lBRkMsSUFBQSxtQkFBUyxFQUFDLEdBQUcsRUFBRSxDQUFDLG1EQUFvQixFQUFFLFFBQVEsQ0FBQyxFQUFFLENBQUMsUUFBUSxDQUFDLFNBQVMsQ0FBQztJQUNyRSxJQUFBLG9CQUFVLEVBQUMsRUFBRSxJQUFJLEVBQUUsYUFBYSxFQUFFLENBQUM7a0RBQzFCLG1EQUFvQixvQkFBcEIsbURBQW9CO2tEQUFDOzJCQW5KcEIsZ0JBQWdCO0lBUDVCLElBQUEsZ0JBQU0sRUFBQyxvQkFBb0IsQ0FBQztJQUM1QixJQUFBLGVBQUssRUFBQyxDQUFDLFlBQVksQ0FBQyxDQUFDO0lBQ3JCLElBQUEsZUFBSyxFQUFDLENBQUMsYUFBYSxDQUFDLENBQUM7SUFDdEIsSUFBQSxlQUFLLEVBQUMsQ0FBQyxVQUFVLENBQUMsQ0FBQztJQUNuQixJQUFBLGVBQUssRUFBQyxDQUFDLGVBQWUsQ0FBQyxDQUFDO0lBQ3hCLElBQUEsZUFBSyxFQUFDLENBQUMsZ0JBQWdCLENBQUMsQ0FBQztJQUN6QixJQUFBLGVBQUssRUFBQyxDQUFDLFdBQVcsQ0FBQyxDQUFDO0dBQ1IsZ0JBQWdCLENBMGM1QiIsIm5hbWVzIjpbXSwic291cmNlcyI6WyJDOlxcVXNlcnNcXEx1a2FcXHNlbnRpbmVsXFxiYWNrZW5kXFxzcmNcXG1vZHVsZXNcXHJlcG9ydGluZ1xcYWxlcnRpbmdcXGVudGl0aWVzXFx3b3JrZmxvdy1zY2hlZHVsZS5lbnRpdHkudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcclxuICBFbnRpdHksXHJcbiAgUHJpbWFyeUdlbmVyYXRlZENvbHVtbixcclxuICBDb2x1bW4sXHJcbiAgQ3JlYXRlRGF0ZUNvbHVtbixcclxuICBVcGRhdGVEYXRlQ29sdW1uLFxyXG4gIEluZGV4LFxyXG4gIE1hbnlUb09uZSxcclxuICBKb2luQ29sdW1uLFxyXG59IGZyb20gJ3R5cGVvcm0nO1xyXG5pbXBvcnQgeyBOb3RpZmljYXRpb25Xb3JrZmxvdyB9IGZyb20gJy4vbm90aWZpY2F0aW9uLXdvcmtmbG93LmVudGl0eSc7XHJcblxyXG4vKipcclxuICogV29ya2Zsb3cgU2NoZWR1bGUgRW50aXR5XHJcbiAqIFxyXG4gKiBSZXByZXNlbnRzIHNjaGVkdWxlZCB0cmlnZ2VycyBmb3Igbm90aWZpY2F0aW9uIHdvcmtmbG93cyB3aXRoXHJcbiAqIGNvbXByZWhlbnNpdmUgc2NoZWR1bGluZyBjb25maWd1cmF0aW9uIGFuZCBleGVjdXRpb24gdHJhY2tpbmcuXHJcbiAqL1xyXG5ARW50aXR5KCd3b3JrZmxvd19zY2hlZHVsZXMnKVxyXG5ASW5kZXgoWyd3b3JrZmxvd0lkJ10pXHJcbkBJbmRleChbJ3RyaWdnZXJUeXBlJ10pXHJcbkBJbmRleChbJ2lzQWN0aXZlJ10pXHJcbkBJbmRleChbJ25leHRFeGVjdXRpb24nXSlcclxuQEluZGV4KFsnY3JvbkV4cHJlc3Npb24nXSlcclxuQEluZGV4KFsnZXZlbnRUeXBlJ10pXHJcbmV4cG9ydCBjbGFzcyBXb3JrZmxvd1NjaGVkdWxlIHtcclxuICBAUHJpbWFyeUdlbmVyYXRlZENvbHVtbigndXVpZCcpXHJcbiAgaWQ6IHN0cmluZztcclxuXHJcbiAgQENvbHVtbih7IG5hbWU6ICd3b3JrZmxvd19pZCcgfSlcclxuICBASW5kZXgoKVxyXG4gIHdvcmtmbG93SWQ6IHN0cmluZztcclxuXHJcbiAgQENvbHVtbih7IG5hbWU6ICd0cmlnZ2VyX3R5cGUnIH0pXHJcbiAgQEluZGV4KClcclxuICB0cmlnZ2VyVHlwZTogJ2Nyb24nIHwgJ2V2ZW50JyB8ICd3ZWJob29rJyB8ICdtYW51YWwnO1xyXG5cclxuICBAQ29sdW1uKHsgbmFtZTogJ2Nyb25fZXhwcmVzc2lvbicsIG51bGxhYmxlOiB0cnVlIH0pXHJcbiAgQEluZGV4KClcclxuICBjcm9uRXhwcmVzc2lvbjogc3RyaW5nO1xyXG5cclxuICBAQ29sdW1uKHsgbmFtZTogJ3RpbWV6b25lJywgZGVmYXVsdDogJ1VUQycgfSlcclxuICB0aW1lem9uZTogc3RyaW5nO1xyXG5cclxuICBAQ29sdW1uKHsgbmFtZTogJ2V2ZW50X3R5cGUnLCBudWxsYWJsZTogdHJ1ZSB9KVxyXG4gIEBJbmRleCgpXHJcbiAgZXZlbnRUeXBlOiBzdHJpbmc7XHJcblxyXG4gIEBDb2x1bW4oeyBuYW1lOiAnZXZlbnRfcGF0dGVybicsIHR5cGU6ICdqc29uYicsIG51bGxhYmxlOiB0cnVlIH0pXHJcbiAgZXZlbnRQYXR0ZXJuOiBSZWNvcmQ8c3RyaW5nLCBhbnk+O1xyXG5cclxuICBAQ29sdW1uKHsgbmFtZTogJ3dlYmhvb2tfcGF0aCcsIG51bGxhYmxlOiB0cnVlIH0pXHJcbiAgd2ViaG9va1BhdGg6IHN0cmluZztcclxuXHJcbiAgQENvbHVtbih7IG5hbWU6ICd3ZWJob29rX21ldGhvZCcsIGRlZmF1bHQ6ICdQT1NUJyB9KVxyXG4gIHdlYmhvb2tNZXRob2Q6ICdHRVQnIHwgJ1BPU1QnIHwgJ1BVVCcgfCAnUEFUQ0gnIHwgJ0RFTEVURSc7XHJcblxyXG4gIEBDb2x1bW4oeyBuYW1lOiAnd2ViaG9va19hdXRoZW50aWNhdGlvbicsIHR5cGU6ICdqc29uYicsIG51bGxhYmxlOiB0cnVlIH0pXHJcbiAgd2ViaG9va0F1dGhlbnRpY2F0aW9uOiB7XHJcbiAgICB0eXBlPzogJ25vbmUnIHwgJ2Jhc2ljJyB8ICdiZWFyZXInIHwgJ2FwaV9rZXknIHwgJ2N1c3RvbSc7XHJcbiAgICB1c2VybmFtZT86IHN0cmluZztcclxuICAgIHBhc3N3b3JkPzogc3RyaW5nO1xyXG4gICAgdG9rZW4/OiBzdHJpbmc7XHJcbiAgICBhcGlLZXk/OiBzdHJpbmc7XHJcbiAgICBjdXN0b21IZWFkZXJzPzogUmVjb3JkPHN0cmluZywgc3RyaW5nPjtcclxuICB9O1xyXG5cclxuICBAQ29sdW1uKHsgbmFtZTogJ2NvbmRpdGlvbnMnLCB0eXBlOiAnanNvbmInLCBudWxsYWJsZTogdHJ1ZSB9KVxyXG4gIGNvbmRpdGlvbnM6IEFycmF5PHtcclxuICAgIGZpZWxkOiBzdHJpbmc7XHJcbiAgICBvcGVyYXRvcjogc3RyaW5nO1xyXG4gICAgdmFsdWU6IGFueTtcclxuICAgIGRlc2NyaXB0aW9uPzogc3RyaW5nO1xyXG4gIH0+O1xyXG5cclxuICBAQ29sdW1uKHsgbmFtZTogJ2lzX2FjdGl2ZScsIGRlZmF1bHQ6IHRydWUgfSlcclxuICBASW5kZXgoKVxyXG4gIGlzQWN0aXZlOiBib29sZWFuO1xyXG5cclxuICBAQ29sdW1uKHsgbmFtZTogJ25leHRfZXhlY3V0aW9uJywgdHlwZTogJ3RpbWVzdGFtcCcsIG51bGxhYmxlOiB0cnVlIH0pXHJcbiAgQEluZGV4KClcclxuICBuZXh0RXhlY3V0aW9uOiBEYXRlO1xyXG5cclxuICBAQ29sdW1uKHsgbmFtZTogJ2xhc3RfZXhlY3V0aW9uJywgdHlwZTogJ3RpbWVzdGFtcCcsIG51bGxhYmxlOiB0cnVlIH0pXHJcbiAgbGFzdEV4ZWN1dGlvbjogRGF0ZTtcclxuXHJcbiAgQENvbHVtbih7IG5hbWU6ICdleGVjdXRpb25fY291bnQnLCBkZWZhdWx0OiAwIH0pXHJcbiAgZXhlY3V0aW9uQ291bnQ6IG51bWJlcjtcclxuXHJcbiAgQENvbHVtbih7IG5hbWU6ICdzdWNjZXNzX2NvdW50JywgZGVmYXVsdDogMCB9KVxyXG4gIHN1Y2Nlc3NDb3VudDogbnVtYmVyO1xyXG5cclxuICBAQ29sdW1uKHsgbmFtZTogJ2ZhaWx1cmVfY291bnQnLCBkZWZhdWx0OiAwIH0pXHJcbiAgZmFpbHVyZUNvdW50OiBudW1iZXI7XHJcblxyXG4gIEBDb2x1bW4oeyBuYW1lOiAnbGFzdF9zdWNjZXNzJywgdHlwZTogJ3RpbWVzdGFtcCcsIG51bGxhYmxlOiB0cnVlIH0pXHJcbiAgbGFzdFN1Y2Nlc3M6IERhdGU7XHJcblxyXG4gIEBDb2x1bW4oeyBuYW1lOiAnbGFzdF9mYWlsdXJlJywgdHlwZTogJ3RpbWVzdGFtcCcsIG51bGxhYmxlOiB0cnVlIH0pXHJcbiAgbGFzdEZhaWx1cmU6IERhdGU7XHJcblxyXG4gIEBDb2x1bW4oeyBuYW1lOiAnbGFzdF9lcnJvcicsIHR5cGU6ICd0ZXh0JywgbnVsbGFibGU6IHRydWUgfSlcclxuICBsYXN0RXJyb3I6IHN0cmluZztcclxuXHJcbiAgQENvbHVtbih7IG5hbWU6ICdjb25maWd1cmF0aW9uJywgdHlwZTogJ2pzb25iJywgbnVsbGFibGU6IHRydWUgfSlcclxuICBjb25maWd1cmF0aW9uOiB7XHJcbiAgICBtYXhFeGVjdXRpb25zPzogbnVtYmVyO1xyXG4gICAgZXhlY3V0aW9uV2luZG93Pzoge1xyXG4gICAgICBzdGFydD86IHN0cmluZztcclxuICAgICAgZW5kPzogc3RyaW5nO1xyXG4gICAgICB0aW1lem9uZT86IHN0cmluZztcclxuICAgIH07XHJcbiAgICByZXRyeVBvbGljeT86IHtcclxuICAgICAgZW5hYmxlZD86IGJvb2xlYW47XHJcbiAgICAgIG1heFJldHJpZXM/OiBudW1iZXI7XHJcbiAgICAgIGJhY2tvZmZNdWx0aXBsaWVyPzogbnVtYmVyO1xyXG4gICAgICBtYXhCYWNrb2ZmVGltZT86IG51bWJlcjtcclxuICAgIH07XHJcbiAgICBub3RpZmljYXRpb25zPzoge1xyXG4gICAgICBvblN1Y2Nlc3M/OiBzdHJpbmdbXTtcclxuICAgICAgb25GYWlsdXJlPzogc3RyaW5nW107XHJcbiAgICAgIG9uTWlzc2VkPzogc3RyaW5nW107XHJcbiAgICB9O1xyXG4gICAgY29uY3VycmVuY3k/OiB7XHJcbiAgICAgIG1heENvbmN1cnJlbnQ/OiBudW1iZXI7XHJcbiAgICAgIHF1ZXVlT3ZlcmZsb3c/OiAncmVqZWN0JyB8ICdxdWV1ZScgfCAncmVwbGFjZSc7XHJcbiAgICB9O1xyXG4gICAgYnVzaW5lc3NSdWxlcz86IHtcclxuICAgICAgc2tpcFdlZWtlbmRzPzogYm9vbGVhbjtcclxuICAgICAgc2tpcEhvbGlkYXlzPzogYm9vbGVhbjtcclxuICAgICAgYnVzaW5lc3NIb3Vyc09ubHk/OiBib29sZWFuO1xyXG4gICAgICBob2xpZGF5Q2FsZW5kYXI/OiBzdHJpbmc7XHJcbiAgICB9O1xyXG4gIH07XHJcblxyXG4gIEBDb2x1bW4oeyBuYW1lOiAnbWV0YWRhdGEnLCB0eXBlOiAnanNvbmInLCBudWxsYWJsZTogdHJ1ZSB9KVxyXG4gIG1ldGFkYXRhOiB7XHJcbiAgICBkZXNjcmlwdGlvbj86IHN0cmluZztcclxuICAgIHRhZ3M/OiBzdHJpbmdbXTtcclxuICAgIG93bmVyPzogc3RyaW5nO1xyXG4gICAgdGVhbT86IHN0cmluZztcclxuICAgIHByaW9yaXR5PzogJ2xvdycgfCAnbWVkaXVtJyB8ICdoaWdoJyB8ICdjcml0aWNhbCc7XHJcbiAgICBlbnZpcm9ubWVudD86IHN0cmluZztcclxuICAgIHJlZ2lvbj86IHN0cmluZztcclxuICAgIGRlcGVuZGVuY2llcz86IHN0cmluZ1tdO1xyXG4gICAgbW9uaXRvcmluZz86IHtcclxuICAgICAgYWxlcnRPbkZhaWx1cmU/OiBib29sZWFuO1xyXG4gICAgICBhbGVydE9uTWlzc2VkPzogYm9vbGVhbjtcclxuICAgICAgaGVhbHRoQ2hlY2tVcmw/OiBzdHJpbmc7XHJcbiAgICB9O1xyXG4gICAgZG9jdW1lbnRhdGlvbj86IHtcclxuICAgICAgcHVycG9zZT86IHN0cmluZztcclxuICAgICAgcmVxdWlyZW1lbnRzPzogc3RyaW5nW107XHJcbiAgICAgIHRyb3VibGVzaG9vdGluZz86IHN0cmluZztcclxuICAgIH07XHJcbiAgfTtcclxuXHJcbiAgQENvbHVtbih7IG5hbWU6ICdjcmVhdGVkX2J5JyB9KVxyXG4gIGNyZWF0ZWRCeTogc3RyaW5nO1xyXG5cclxuICBAQ29sdW1uKHsgbmFtZTogJ3VwZGF0ZWRfYnknIH0pXHJcbiAgdXBkYXRlZEJ5OiBzdHJpbmc7XHJcblxyXG4gIEBDcmVhdGVEYXRlQ29sdW1uKHsgbmFtZTogJ2NyZWF0ZWRfYXQnIH0pXHJcbiAgY3JlYXRlZEF0OiBEYXRlO1xyXG5cclxuICBAVXBkYXRlRGF0ZUNvbHVtbih7IG5hbWU6ICd1cGRhdGVkX2F0JyB9KVxyXG4gIHVwZGF0ZWRBdDogRGF0ZTtcclxuXHJcbiAgLy8gUmVsYXRpb25zXHJcbiAgQE1hbnlUb09uZSgoKSA9PiBOb3RpZmljYXRpb25Xb3JrZmxvdywgd29ya2Zsb3cgPT4gd29ya2Zsb3cuc2NoZWR1bGVzKVxyXG4gIEBKb2luQ29sdW1uKHsgbmFtZTogJ3dvcmtmbG93X2lkJyB9KVxyXG4gIHdvcmtmbG93OiBOb3RpZmljYXRpb25Xb3JrZmxvdztcclxuXHJcbiAgLy8gQ29tcHV0ZWQgcHJvcGVydGllc1xyXG4gIGdldCBpc0Nyb25TY2hlZHVsZSgpOiBib29sZWFuIHtcclxuICAgIHJldHVybiB0aGlzLnRyaWdnZXJUeXBlID09PSAnY3Jvbic7XHJcbiAgfVxyXG5cclxuICBnZXQgaXNFdmVudFRyaWdnZXIoKTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gdGhpcy50cmlnZ2VyVHlwZSA9PT0gJ2V2ZW50JztcclxuICB9XHJcblxyXG4gIGdldCBpc1dlYmhvb2tUcmlnZ2VyKCk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIHRoaXMudHJpZ2dlclR5cGUgPT09ICd3ZWJob29rJztcclxuICB9XHJcblxyXG4gIGdldCBzdWNjZXNzUmF0ZSgpOiBudW1iZXIge1xyXG4gICAgaWYgKHRoaXMuZXhlY3V0aW9uQ291bnQgPT09IDApIHJldHVybiAwO1xyXG4gICAgcmV0dXJuICh0aGlzLnN1Y2Nlc3NDb3VudCAvIHRoaXMuZXhlY3V0aW9uQ291bnQpICogMTAwO1xyXG4gIH1cclxuXHJcbiAgZ2V0IGZhaWx1cmVSYXRlKCk6IG51bWJlciB7XHJcbiAgICBpZiAodGhpcy5leGVjdXRpb25Db3VudCA9PT0gMCkgcmV0dXJuIDA7XHJcbiAgICByZXR1cm4gKHRoaXMuZmFpbHVyZUNvdW50IC8gdGhpcy5leGVjdXRpb25Db3VudCkgKiAxMDA7XHJcbiAgfVxyXG5cclxuICBnZXQgaXNPdmVyZHVlKCk6IGJvb2xlYW4ge1xyXG4gICAgcmV0dXJuIHRoaXMubmV4dEV4ZWN1dGlvbiAmJiBuZXcgRGF0ZSgpID4gdGhpcy5uZXh0RXhlY3V0aW9uO1xyXG4gIH1cclxuXHJcbiAgZ2V0IGRheXNTaW5jZUxhc3RFeGVjdXRpb24oKTogbnVtYmVyIHtcclxuICAgIGlmICghdGhpcy5sYXN0RXhlY3V0aW9uKSByZXR1cm4gLTE7XHJcbiAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpO1xyXG4gICAgY29uc3QgZGlmZlRpbWUgPSBNYXRoLmFicyhub3cuZ2V0VGltZSgpIC0gdGhpcy5sYXN0RXhlY3V0aW9uLmdldFRpbWUoKSk7XHJcbiAgICByZXR1cm4gTWF0aC5jZWlsKGRpZmZUaW1lIC8gKDEwMDAgKiA2MCAqIDYwICogMjQpKTtcclxuICB9XHJcblxyXG4gIGdldCBpc0hlYWx0aHkoKTogYm9vbGVhbiB7XHJcbiAgICAvLyBDb25zaWRlciBzY2hlZHVsZSBoZWFsdGh5IGlmIHN1Y2Nlc3MgcmF0ZSA+IDkwJSBhbmQgbm8gcmVjZW50IGZhaWx1cmVzXHJcbiAgICBjb25zdCByZWNlbnRGYWlsdXJlVGhyZXNob2xkID0gMjQgKiA2MCAqIDYwICogMTAwMDsgLy8gMjQgaG91cnNcclxuICAgIGNvbnN0IGhhc1JlY2VudEZhaWx1cmUgPSB0aGlzLmxhc3RGYWlsdXJlICYmIFxyXG4gICAgICAoRGF0ZS5ub3coKSAtIHRoaXMubGFzdEZhaWx1cmUuZ2V0VGltZSgpKSA8IHJlY2VudEZhaWx1cmVUaHJlc2hvbGQ7XHJcbiAgICBcclxuICAgIHJldHVybiB0aGlzLnN1Y2Nlc3NSYXRlID49IDkwICYmICFoYXNSZWNlbnRGYWlsdXJlO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IHNjaGVkdWxlIHN1bW1hcnlcclxuICAgKi9cclxuICBnZXRTY2hlZHVsZVN1bW1hcnkoKTogUmVjb3JkPHN0cmluZywgYW55PiB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBpZDogdGhpcy5pZCxcclxuICAgICAgd29ya2Zsb3dJZDogdGhpcy53b3JrZmxvd0lkLFxyXG4gICAgICB0cmlnZ2VyVHlwZTogdGhpcy50cmlnZ2VyVHlwZSxcclxuICAgICAgY3JvbkV4cHJlc3Npb246IHRoaXMuY3JvbkV4cHJlc3Npb24sXHJcbiAgICAgIGV2ZW50VHlwZTogdGhpcy5ldmVudFR5cGUsXHJcbiAgICAgIHdlYmhvb2tQYXRoOiB0aGlzLndlYmhvb2tQYXRoLFxyXG4gICAgICBpc0FjdGl2ZTogdGhpcy5pc0FjdGl2ZSxcclxuICAgICAgbmV4dEV4ZWN1dGlvbjogdGhpcy5uZXh0RXhlY3V0aW9uLFxyXG4gICAgICBsYXN0RXhlY3V0aW9uOiB0aGlzLmxhc3RFeGVjdXRpb24sXHJcbiAgICAgIGV4ZWN1dGlvbkNvdW50OiB0aGlzLmV4ZWN1dGlvbkNvdW50LFxyXG4gICAgICBzdWNjZXNzUmF0ZTogdGhpcy5zdWNjZXNzUmF0ZSxcclxuICAgICAgZmFpbHVyZVJhdGU6IHRoaXMuZmFpbHVyZVJhdGUsXHJcbiAgICAgIGlzSGVhbHRoeTogdGhpcy5pc0hlYWx0aHksXHJcbiAgICAgIGlzT3ZlcmR1ZTogdGhpcy5pc092ZXJkdWUsXHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogR2V0IGV4ZWN1dGlvbiBzdGF0aXN0aWNzXHJcbiAgICovXHJcbiAgZ2V0RXhlY3V0aW9uU3RhdHMoKTogUmVjb3JkPHN0cmluZywgYW55PiB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICB0b3RhbEV4ZWN1dGlvbnM6IHRoaXMuZXhlY3V0aW9uQ291bnQsXHJcbiAgICAgIHN1Y2Nlc3NmdWxFeGVjdXRpb25zOiB0aGlzLnN1Y2Nlc3NDb3VudCxcclxuICAgICAgZmFpbGVkRXhlY3V0aW9uczogdGhpcy5mYWlsdXJlQ291bnQsXHJcbiAgICAgIHN1Y2Nlc3NSYXRlOiB0aGlzLnN1Y2Nlc3NSYXRlLFxyXG4gICAgICBmYWlsdXJlUmF0ZTogdGhpcy5mYWlsdXJlUmF0ZSxcclxuICAgICAgbGFzdEV4ZWN1dGlvbjogdGhpcy5sYXN0RXhlY3V0aW9uLFxyXG4gICAgICBsYXN0U3VjY2VzczogdGhpcy5sYXN0U3VjY2VzcyxcclxuICAgICAgbGFzdEZhaWx1cmU6IHRoaXMubGFzdEZhaWx1cmUsXHJcbiAgICAgIGRheXNTaW5jZUxhc3RFeGVjdXRpb246IHRoaXMuZGF5c1NpbmNlTGFzdEV4ZWN1dGlvbixcclxuICAgICAgaXNIZWFsdGh5OiB0aGlzLmlzSGVhbHRoeSxcclxuICAgIH07XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgdHJpZ2dlciBjb25maWd1cmF0aW9uXHJcbiAgICovXHJcbiAgZ2V0VHJpZ2dlckNvbmZpZygpOiBSZWNvcmQ8c3RyaW5nLCBhbnk+IHtcclxuICAgIGNvbnN0IGNvbmZpZzogUmVjb3JkPHN0cmluZywgYW55PiA9IHtcclxuICAgICAgdHlwZTogdGhpcy50cmlnZ2VyVHlwZSxcclxuICAgIH07XHJcblxyXG4gICAgc3dpdGNoICh0aGlzLnRyaWdnZXJUeXBlKSB7XHJcbiAgICAgIGNhc2UgJ2Nyb24nOlxyXG4gICAgICAgIGNvbmZpZy5jcm9uRXhwcmVzc2lvbiA9IHRoaXMuY3JvbkV4cHJlc3Npb247XHJcbiAgICAgICAgY29uZmlnLnRpbWV6b25lID0gdGhpcy50aW1lem9uZTtcclxuICAgICAgICBjb25maWcubmV4dEV4ZWN1dGlvbiA9IHRoaXMubmV4dEV4ZWN1dGlvbjtcclxuICAgICAgICBicmVhaztcclxuICAgICAgY2FzZSAnZXZlbnQnOlxyXG4gICAgICAgIGNvbmZpZy5ldmVudFR5cGUgPSB0aGlzLmV2ZW50VHlwZTtcclxuICAgICAgICBjb25maWcuZXZlbnRQYXR0ZXJuID0gdGhpcy5ldmVudFBhdHRlcm47XHJcbiAgICAgICAgY29uZmlnLmNvbmRpdGlvbnMgPSB0aGlzLmNvbmRpdGlvbnM7XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICAgIGNhc2UgJ3dlYmhvb2snOlxyXG4gICAgICAgIGNvbmZpZy53ZWJob29rUGF0aCA9IHRoaXMud2ViaG9va1BhdGg7XHJcbiAgICAgICAgY29uZmlnLndlYmhvb2tNZXRob2QgPSB0aGlzLndlYmhvb2tNZXRob2Q7XHJcbiAgICAgICAgY29uZmlnLmF1dGhlbnRpY2F0aW9uID0gdGhpcy53ZWJob29rQXV0aGVudGljYXRpb247XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIGNvbmZpZztcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENoZWNrIGlmIHNjaGVkdWxlIHNob3VsZCBleGVjdXRlIG5vd1xyXG4gICAqL1xyXG4gIHNob3VsZEV4ZWN1dGVOb3coKTogYm9vbGVhbiB7XHJcbiAgICBpZiAoIXRoaXMuaXNBY3RpdmUpIHJldHVybiBmYWxzZTtcclxuICAgIGlmICh0aGlzLnRyaWdnZXJUeXBlICE9PSAnY3JvbicpIHJldHVybiBmYWxzZTtcclxuICAgIGlmICghdGhpcy5uZXh0RXhlY3V0aW9uKSByZXR1cm4gZmFsc2U7XHJcbiAgICBcclxuICAgIHJldHVybiBuZXcgRGF0ZSgpID49IHRoaXMubmV4dEV4ZWN1dGlvbjtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENoZWNrIGlmIGV2ZW50IG1hdGNoZXMgdGhpcyBzY2hlZHVsZVxyXG4gICAqL1xyXG4gIG1hdGNoZXNFdmVudChldmVudFR5cGU6IHN0cmluZywgZXZlbnREYXRhOiBhbnkpOiBib29sZWFuIHtcclxuICAgIGlmICghdGhpcy5pc0FjdGl2ZSkgcmV0dXJuIGZhbHNlO1xyXG4gICAgaWYgKHRoaXMudHJpZ2dlclR5cGUgIT09ICdldmVudCcpIHJldHVybiBmYWxzZTtcclxuICAgIGlmICh0aGlzLmV2ZW50VHlwZSAhPT0gZXZlbnRUeXBlKSByZXR1cm4gZmFsc2U7XHJcblxyXG4gICAgLy8gQ2hlY2sgZXZlbnQgcGF0dGVyblxyXG4gICAgaWYgKHRoaXMuZXZlbnRQYXR0ZXJuKSB7XHJcbiAgICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKHRoaXMuZXZlbnRQYXR0ZXJuKSkge1xyXG4gICAgICAgIGNvbnN0IGV2ZW50VmFsdWUgPSB0aGlzLmdldE5lc3RlZFZhbHVlKGV2ZW50RGF0YSwga2V5KTtcclxuICAgICAgICBpZiAoZXZlbnRWYWx1ZSAhPT0gdmFsdWUpIHJldHVybiBmYWxzZTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIENoZWNrIGNvbmRpdGlvbnNcclxuICAgIGlmICh0aGlzLmNvbmRpdGlvbnMgJiYgdGhpcy5jb25kaXRpb25zLmxlbmd0aCA+IDApIHtcclxuICAgICAgZm9yIChjb25zdCBjb25kaXRpb24gb2YgdGhpcy5jb25kaXRpb25zKSB7XHJcbiAgICAgICAgY29uc3QgZmllbGRWYWx1ZSA9IHRoaXMuZ2V0TmVzdGVkVmFsdWUoZXZlbnREYXRhLCBjb25kaXRpb24uZmllbGQpO1xyXG4gICAgICAgIGlmICghdGhpcy5ldmFsdWF0ZUNvbmRpdGlvbihmaWVsZFZhbHVlLCBjb25kaXRpb24ub3BlcmF0b3IsIGNvbmRpdGlvbi52YWx1ZSkpIHtcclxuICAgICAgICAgIHJldHVybiBmYWxzZTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gdHJ1ZTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIENoZWNrIGlmIHdlYmhvb2sgcmVxdWVzdCBtYXRjaGVzIHRoaXMgc2NoZWR1bGVcclxuICAgKi9cclxuICBtYXRjaGVzV2ViaG9vayhwYXRoOiBzdHJpbmcsIG1ldGhvZDogc3RyaW5nKTogYm9vbGVhbiB7XHJcbiAgICBpZiAoIXRoaXMuaXNBY3RpdmUpIHJldHVybiBmYWxzZTtcclxuICAgIGlmICh0aGlzLnRyaWdnZXJUeXBlICE9PSAnd2ViaG9vaycpIHJldHVybiBmYWxzZTtcclxuICAgIGlmICh0aGlzLndlYmhvb2tQYXRoICE9PSBwYXRoKSByZXR1cm4gZmFsc2U7XHJcbiAgICBpZiAodGhpcy53ZWJob29rTWV0aG9kICE9PSBtZXRob2QpIHJldHVybiBmYWxzZTtcclxuXHJcbiAgICByZXR1cm4gdHJ1ZTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIFJlY29yZCBleGVjdXRpb24gYXR0ZW1wdFxyXG4gICAqL1xyXG4gIHJlY29yZEV4ZWN1dGlvbihzdWNjZXNzOiBib29sZWFuLCBlcnJvcj86IHN0cmluZyk6IHZvaWQge1xyXG4gICAgdGhpcy5leGVjdXRpb25Db3VudCsrO1xyXG4gICAgdGhpcy5sYXN0RXhlY3V0aW9uID0gbmV3IERhdGUoKTtcclxuXHJcbiAgICBpZiAoc3VjY2Vzcykge1xyXG4gICAgICB0aGlzLnN1Y2Nlc3NDb3VudCsrO1xyXG4gICAgICB0aGlzLmxhc3RTdWNjZXNzID0gbmV3IERhdGUoKTtcclxuICAgICAgdGhpcy5sYXN0RXJyb3IgPSBudWxsO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgdGhpcy5mYWlsdXJlQ291bnQrKztcclxuICAgICAgdGhpcy5sYXN0RmFpbHVyZSA9IG5ldyBEYXRlKCk7XHJcbiAgICAgIHRoaXMubGFzdEVycm9yID0gZXJyb3IgfHwgJ1Vua25vd24gZXJyb3InO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2FsY3VsYXRlIG5leHQgZXhlY3V0aW9uIHRpbWUgZm9yIGNyb24gc2NoZWR1bGVzXHJcbiAgICovXHJcbiAgY2FsY3VsYXRlTmV4dEV4ZWN1dGlvbigpOiBEYXRlIHwgbnVsbCB7XHJcbiAgICBpZiAodGhpcy50cmlnZ2VyVHlwZSAhPT0gJ2Nyb24nIHx8ICF0aGlzLmNyb25FeHByZXNzaW9uKSB7XHJcbiAgICAgIHJldHVybiBudWxsO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFNpbXBsZSBpbXBsZW1lbnRhdGlvbiAtIGluIHByb2R1Y3Rpb24sIHVzZSBhIHByb3BlciBjcm9uIHBhcnNlclxyXG4gICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTtcclxuICAgIHJldHVybiBuZXcgRGF0ZShub3cuZ2V0VGltZSgpICsgNjAwMDApOyAvLyBOZXh0IG1pbnV0ZSBmb3Igc2ltcGxpY2l0eVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogQ2hlY2sgaWYgc2NoZWR1bGUgaXMgd2l0aGluIGV4ZWN1dGlvbiB3aW5kb3dcclxuICAgKi9cclxuICBpc1dpdGhpbkV4ZWN1dGlvbldpbmRvdygpOiBib29sZWFuIHtcclxuICAgIGNvbnN0IHdpbmRvdyA9IHRoaXMuY29uZmlndXJhdGlvbj8uZXhlY3V0aW9uV2luZG93O1xyXG4gICAgaWYgKCF3aW5kb3cgfHwgIXdpbmRvdy5zdGFydCB8fCAhd2luZG93LmVuZCkge1xyXG4gICAgICByZXR1cm4gdHJ1ZTsgLy8gTm8gd2luZG93IHJlc3RyaWN0aW9uXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTtcclxuICAgIGNvbnN0IHRpbWV6b25lID0gd2luZG93LnRpbWV6b25lIHx8IHRoaXMudGltZXpvbmUgfHwgJ1VUQyc7XHJcbiAgICBcclxuICAgIC8vIFNpbXBsZSB0aW1lIHdpbmRvdyBjaGVjayAtIGluIHByb2R1Y3Rpb24sIHVzZSBwcm9wZXIgdGltZXpvbmUgaGFuZGxpbmdcclxuICAgIGNvbnN0IGN1cnJlbnRIb3VyID0gbm93LmdldEhvdXJzKCk7XHJcbiAgICBjb25zdCBzdGFydEhvdXIgPSBwYXJzZUludCh3aW5kb3cuc3RhcnQuc3BsaXQoJzonKVswXSk7XHJcbiAgICBjb25zdCBlbmRIb3VyID0gcGFyc2VJbnQod2luZG93LmVuZC5zcGxpdCgnOicpWzBdKTtcclxuXHJcbiAgICByZXR1cm4gY3VycmVudEhvdXIgPj0gc3RhcnRIb3VyICYmIGN1cnJlbnRIb3VyIDw9IGVuZEhvdXI7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBDaGVjayBpZiBzY2hlZHVsZSBzaG91bGQgc2tpcCBkdWUgdG8gYnVzaW5lc3MgcnVsZXNcclxuICAgKi9cclxuICBzaG91bGRTa2lwRXhlY3V0aW9uKCk6IGJvb2xlYW4ge1xyXG4gICAgY29uc3QgYnVzaW5lc3NSdWxlcyA9IHRoaXMuY29uZmlndXJhdGlvbj8uYnVzaW5lc3NSdWxlcztcclxuICAgIGlmICghYnVzaW5lc3NSdWxlcykgcmV0dXJuIGZhbHNlO1xyXG5cclxuICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7XHJcblxyXG4gICAgLy8gQ2hlY2sgd2Vla2VuZHNcclxuICAgIGlmIChidXNpbmVzc1J1bGVzLnNraXBXZWVrZW5kcykge1xyXG4gICAgICBjb25zdCBkYXlPZldlZWsgPSBub3cuZ2V0RGF5KCk7XHJcbiAgICAgIGlmIChkYXlPZldlZWsgPT09IDAgfHwgZGF5T2ZXZWVrID09PSA2KSB7IC8vIFN1bmRheSBvciBTYXR1cmRheVxyXG4gICAgICAgIHJldHVybiB0cnVlO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLy8gQ2hlY2sgYnVzaW5lc3MgaG91cnNcclxuICAgIGlmIChidXNpbmVzc1J1bGVzLmJ1c2luZXNzSG91cnNPbmx5ICYmICF0aGlzLmlzV2l0aGluRXhlY3V0aW9uV2luZG93KCkpIHtcclxuICAgICAgcmV0dXJuIHRydWU7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gQ2hlY2sgaG9saWRheXMgKHNpbXBsaWZpZWQpXHJcbiAgICBpZiAoYnVzaW5lc3NSdWxlcy5za2lwSG9saWRheXMpIHtcclxuICAgICAgLy8gSW4gcHJvZHVjdGlvbiwgaW50ZWdyYXRlIHdpdGggaG9saWRheSBjYWxlbmRhciBzZXJ2aWNlXHJcbiAgICAgIC8vIEZvciBub3csIGp1c3QgY2hlY2sgaWYgaXQncyBhIG1ham9yIGhvbGlkYXlcclxuICAgICAgY29uc3QgbW9udGggPSBub3cuZ2V0TW9udGgoKSArIDE7XHJcbiAgICAgIGNvbnN0IGRheSA9IG5vdy5nZXREYXRlKCk7XHJcbiAgICAgIFxyXG4gICAgICAvLyBOZXcgWWVhcidzIERheSwgQ2hyaXN0bWFzXHJcbiAgICAgIGlmICgobW9udGggPT09IDEgJiYgZGF5ID09PSAxKSB8fCAobW9udGggPT09IDEyICYmIGRheSA9PT0gMjUpKSB7XHJcbiAgICAgICAgcmV0dXJuIHRydWU7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gZmFsc2U7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBHZXQgc2NoZWR1bGUgaGVhbHRoIHN0YXR1c1xyXG4gICAqL1xyXG4gIGdldEhlYWx0aFN0YXR1cygpOiB7IHN0YXR1czogc3RyaW5nOyBpc3N1ZXM6IHN0cmluZ1tdIH0ge1xyXG4gICAgY29uc3QgaXNzdWVzOiBzdHJpbmdbXSA9IFtdO1xyXG4gICAgbGV0IHN0YXR1cyA9ICdoZWFsdGh5JztcclxuXHJcbiAgICBpZiAoIXRoaXMuaXNBY3RpdmUpIHtcclxuICAgICAgc3RhdHVzID0gJ2luYWN0aXZlJztcclxuICAgICAgaXNzdWVzLnB1c2goJ1NjaGVkdWxlIGlzIGluYWN0aXZlJyk7XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKHRoaXMuaXNPdmVyZHVlKSB7XHJcbiAgICAgIHN0YXR1cyA9ICd3YXJuaW5nJztcclxuICAgICAgaXNzdWVzLnB1c2goJ1NjaGVkdWxlIGlzIG92ZXJkdWUnKTtcclxuICAgIH1cclxuXHJcbiAgICBpZiAodGhpcy5zdWNjZXNzUmF0ZSA8IDkwKSB7XHJcbiAgICAgIHN0YXR1cyA9ICd3YXJuaW5nJztcclxuICAgICAgaXNzdWVzLnB1c2goYExvdyBzdWNjZXNzIHJhdGU6ICR7dGhpcy5zdWNjZXNzUmF0ZS50b0ZpeGVkKDEpfSVgKTtcclxuICAgIH1cclxuXHJcbiAgICBpZiAodGhpcy5zdWNjZXNzUmF0ZSA8IDUwKSB7XHJcbiAgICAgIHN0YXR1cyA9ICdjcml0aWNhbCc7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgcmVjZW50RmFpbHVyZVRocmVzaG9sZCA9IDI0ICogNjAgKiA2MCAqIDEwMDA7IC8vIDI0IGhvdXJzXHJcbiAgICBpZiAodGhpcy5sYXN0RmFpbHVyZSAmJiAoRGF0ZS5ub3coKSAtIHRoaXMubGFzdEZhaWx1cmUuZ2V0VGltZSgpKSA8IHJlY2VudEZhaWx1cmVUaHJlc2hvbGQpIHtcclxuICAgICAgc3RhdHVzID0gJ3dhcm5pbmcnO1xyXG4gICAgICBpc3N1ZXMucHVzaCgnUmVjZW50IGV4ZWN1dGlvbiBmYWlsdXJlJyk7XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIHsgc3RhdHVzLCBpc3N1ZXMgfTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIFByaXZhdGUgaGVscGVyIG1ldGhvZHNcclxuICAgKi9cclxuICBwcml2YXRlIGdldE5lc3RlZFZhbHVlKG9iajogYW55LCBwYXRoOiBzdHJpbmcpOiBhbnkge1xyXG4gICAgcmV0dXJuIHBhdGguc3BsaXQoJy4nKS5yZWR1Y2UoKGN1cnJlbnQsIGtleSkgPT4gY3VycmVudD8uW2tleV0sIG9iaik7XHJcbiAgfVxyXG5cclxuICBwcml2YXRlIGV2YWx1YXRlQ29uZGl0aW9uKHZhbHVlOiBhbnksIG9wZXJhdG9yOiBzdHJpbmcsIGV4cGVjdGVkOiBhbnkpOiBib29sZWFuIHtcclxuICAgIHN3aXRjaCAob3BlcmF0b3IpIHtcclxuICAgICAgY2FzZSAnZXEnOiByZXR1cm4gdmFsdWUgPT0gZXhwZWN0ZWQ7XHJcbiAgICAgIGNhc2UgJ25lcSc6IHJldHVybiB2YWx1ZSAhPSBleHBlY3RlZDtcclxuICAgICAgY2FzZSAnZ3QnOiByZXR1cm4gdmFsdWUgPiBleHBlY3RlZDtcclxuICAgICAgY2FzZSAnZ3RlJzogcmV0dXJuIHZhbHVlID49IGV4cGVjdGVkO1xyXG4gICAgICBjYXNlICdsdCc6IHJldHVybiB2YWx1ZSA8IGV4cGVjdGVkO1xyXG4gICAgICBjYXNlICdsdGUnOiByZXR1cm4gdmFsdWUgPD0gZXhwZWN0ZWQ7XHJcbiAgICAgIGNhc2UgJ2luJzogcmV0dXJuIEFycmF5LmlzQXJyYXkoZXhwZWN0ZWQpICYmIGV4cGVjdGVkLmluY2x1ZGVzKHZhbHVlKTtcclxuICAgICAgY2FzZSAnY29udGFpbnMnOiByZXR1cm4gU3RyaW5nKHZhbHVlKS5pbmNsdWRlcyhTdHJpbmcoZXhwZWN0ZWQpKTtcclxuICAgICAgY2FzZSAncmVnZXgnOiByZXR1cm4gbmV3IFJlZ0V4cChleHBlY3RlZCkudGVzdChTdHJpbmcodmFsdWUpKTtcclxuICAgICAgZGVmYXVsdDogcmV0dXJuIGZhbHNlO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG4iXSwidmVyc2lvbiI6M30=