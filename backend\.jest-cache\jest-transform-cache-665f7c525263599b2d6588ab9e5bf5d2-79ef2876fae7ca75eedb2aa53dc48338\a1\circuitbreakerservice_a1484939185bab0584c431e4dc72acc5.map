{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\resilience\\circuit-breaker.service.ts", "mappings": ";;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAC/C,yDAAsD;AAEtD;;;;;;;GAOG;AAEI,IAAM,qBAAqB,6BAA3B,MAAM,qBAAqB;IAKhC,YACmB,aAA4B,EAC5B,YAA2B;QAD3B,kBAAa,GAAb,aAAa,CAAe;QAC5B,iBAAY,GAAZ,YAAY,CAAe;QAN7B,WAAM,GAAG,IAAI,eAAM,CAAC,uBAAqB,CAAC,IAAI,CAAC,CAAC;QAChD,oBAAe,GAAG,IAAI,GAAG,EAA0B,CAAC;QAOnE,IAAI,CAAC,aAAa,GAAG;YACnB,gBAAgB,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,oCAAoC,EAAE,CAAC,CAAC;YACzF,eAAe,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,mCAAmC,EAAE,KAAK,CAAC;YAC3F,gBAAgB,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,oCAAoC,EAAE,KAAK,CAAC;YAC7F,gBAAgB,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,oCAAoC,EAAE,CAAC,CAAC;YACzF,gBAAgB,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,oCAAoC,EAAE,CAAC,CAAC;SAC1F,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CACX,WAAmB,EACnB,SAA2B,EAC3B,MAAsC;QAEtC,MAAM,cAAc,GAAG,IAAI,CAAC,yBAAyB,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QAE3E,uCAAuC;QACvC,IAAI,cAAc,CAAC,KAAK,KAAK,YAAY,CAAC,IAAI,EAAE,CAAC;YAC/C,IAAI,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,EAAE,CAAC;gBAC5C,cAAc,CAAC,KAAK,GAAG,YAAY,CAAC,SAAS,CAAC;gBAC9C,cAAc,CAAC,aAAa,GAAG,CAAC,CAAC;gBACjC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,WAAW,2BAA2B,CAAC,CAAC;YAC/E,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,uBAAuB,CAC/B,mBAAmB,WAAW,UAAU,EACxC,WAAW,CACZ,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,cAAc,CAAC,KAAK,KAAK,YAAY,CAAC,SAAS,EAAE,CAAC;YACpD,IAAI,cAAc,CAAC,aAAa,IAAI,cAAc,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;gBAC3E,MAAM,IAAI,uBAAuB,CAC/B,mBAAmB,WAAW,gCAAgC,EAC9D,WAAW,CACZ,CAAC;YACJ,CAAC;YACD,cAAc,CAAC,aAAa,EAAE,CAAC;QACjC,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;YAEjC,8BAA8B;YAC9B,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;YAE3D,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,0BAA0B;YAC1B,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;YAElE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,WAAmB;QACjC,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAC7D,OAAO,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,WAAmB;QACjC,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAC7D,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO;YACL,IAAI,EAAE,WAAW;YACjB,KAAK,EAAE,cAAc,CAAC,KAAK;YAC3B,YAAY,EAAE,cAAc,CAAC,YAAY;YACzC,YAAY,EAAE,cAAc,CAAC,YAAY;YACzC,eAAe,EAAE,cAAc,CAAC,eAAe;YAC/C,eAAe,EAAE,cAAc,CAAC,eAAe;YAC/C,UAAU,EAAE,cAAc,CAAC,UAAU;YACrC,WAAW,EAAE,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC;YACtD,mBAAmB,EAAE,IAAI,CAAC,4BAA4B,CAAC,cAAc,CAAC;YACtE,MAAM,EAAE,cAAc,CAAC,MAAM;SAC9B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,SAAS;QACP,MAAM,MAAM,GAAwC,EAAE,CAAC;QAEvD,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1C,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACzC,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;YACvB,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,WAAmB;QAC7B,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAC7D,IAAI,cAAc,EAAE,CAAC;YACnB,cAAc,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC;YACzC,cAAc,CAAC,eAAe,GAAG,IAAI,IAAI,EAAE,CAAC;YAE5C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,WAAW,kBAAkB,CAAC,CAAC;YACnE,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,WAAmB;QAC9B,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAC7D,IAAI,cAAc,EAAE,CAAC;YACnB,cAAc,CAAC,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC;YAC3C,cAAc,CAAC,YAAY,GAAG,CAAC,CAAC;YAChC,cAAc,CAAC,aAAa,GAAG,CAAC,CAAC;YAEjC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,WAAW,kBAAkB,CAAC,CAAC;YACnE,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,WAAmB;QAC9B,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAC7D,IAAI,cAAc,EAAE,CAAC;YACnB,cAAc,CAAC,YAAY,GAAG,CAAC,CAAC;YAChC,cAAc,CAAC,YAAY,GAAG,CAAC,CAAC;YAChC,cAAc,CAAC,UAAU,GAAG,CAAC,CAAC;YAC9B,cAAc,CAAC,aAAa,GAAG,CAAC,CAAC;YACjC,cAAc,CAAC,aAAa,GAAG,EAAE,CAAC;YAClC,cAAc,CAAC,eAAe,GAAG,IAAI,CAAC;YACtC,cAAc,CAAC,eAAe,GAAG,IAAI,CAAC;YACtC,cAAc,CAAC,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC;YAE3C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,WAAW,QAAQ,CAAC,CAAC;YACzD,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,WAAmB;QAC/B,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC;YAC7C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,WAAW,UAAU,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED,yBAAyB;IAEjB,yBAAyB,CAC/B,IAAY,EACZ,MAAsC;QAEtC,IAAI,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAEpD,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,cAAc,GAAG;gBACf,IAAI;gBACJ,KAAK,EAAE,YAAY,CAAC,MAAM;gBAC1B,YAAY,EAAE,CAAC;gBACf,YAAY,EAAE,CAAC;gBACf,UAAU,EAAE,CAAC;gBACb,aAAa,EAAE,CAAC;gBAChB,eAAe,EAAE,IAAI;gBACrB,eAAe,EAAE,IAAI;gBACrB,aAAa,EAAE,EAAE;gBACjB,MAAM,EAAE,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE,GAAG,MAAM,EAAE;aAC7C,CAAC;YAEF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;YAC/C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,IAAI,EAAE,CAAC,CAAC;QACxD,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAEO,aAAa,CAAC,cAA8B,EAAE,YAAoB;QACxE,cAAc,CAAC,YAAY,EAAE,CAAC;QAC9B,cAAc,CAAC,UAAU,EAAE,CAAC;QAC5B,cAAc,CAAC,eAAe,GAAG,IAAI,IAAI,EAAE,CAAC;QAC5C,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAEhD,kCAAkC;QAClC,IAAI,cAAc,CAAC,aAAa,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YAC9C,cAAc,CAAC,aAAa,GAAG,cAAc,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QACzE,CAAC;QAED,2BAA2B;QAC3B,IAAI,cAAc,CAAC,KAAK,KAAK,YAAY,CAAC,SAAS,EAAE,CAAC;YACpD,IAAI,cAAc,CAAC,aAAa,IAAI,cAAc,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;gBAC3E,cAAc,CAAC,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC;gBAC3C,cAAc,CAAC,YAAY,GAAG,CAAC,CAAC;gBAChC,cAAc,CAAC,aAAa,GAAG,CAAC,CAAC;gBAEjC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,cAAc,CAAC,IAAI,mCAAmC,CAAC,CAAC;gBAC5F,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,IAAI,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,cAAc,CAAC,IAAI,mBAAmB,CAAC,CAAC;IAC/E,CAAC;IAEO,aAAa,CAAC,cAA8B,EAAE,KAAU,EAAE,YAAoB;QACpF,cAAc,CAAC,YAAY,EAAE,CAAC;QAC9B,cAAc,CAAC,UAAU,EAAE,CAAC;QAC5B,cAAc,CAAC,eAAe,GAAG,IAAI,IAAI,EAAE,CAAC;QAC5C,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAEhD,sCAAsC;QACtC,IAAI,cAAc,CAAC,KAAK,KAAK,YAAY,CAAC,MAAM,EAAE,CAAC;YACjD,IAAI,cAAc,CAAC,YAAY,IAAI,cAAc,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;gBAC1E,cAAc,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC;gBAEzC,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,mBAAmB,cAAc,CAAC,IAAI,kBAAkB,cAAc,CAAC,YAAY,WAAW,CAC/F,CAAC;gBACF,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;aAAM,IAAI,cAAc,CAAC,KAAK,KAAK,YAAY,CAAC,SAAS,EAAE,CAAC;YAC3D,2DAA2D;YAC3D,cAAc,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC;YACzC,cAAc,CAAC,aAAa,GAAG,CAAC,CAAC;YAEjC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,cAAc,CAAC,IAAI,4CAA4C,CAAC,CAAC;YACrG,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,cAAc,CAAC,IAAI,sBAAsB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;IACjG,CAAC;IAEO,kBAAkB,CAAC,cAA8B;QACvD,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,oBAAoB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,cAAc,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;QACnF,OAAO,oBAAoB,IAAI,cAAc,CAAC,MAAM,CAAC,eAAe,CAAC;IACvE,CAAC;IAEO,oBAAoB,CAAC,cAA8B;QACzD,IAAI,cAAc,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;YACpC,OAAO,CAAC,CAAC;QACX,CAAC;QAED,OAAO,cAAc,CAAC,YAAY,GAAG,cAAc,CAAC,UAAU,CAAC;IACjE,CAAC;IAEO,4BAA4B,CAAC,cAA8B;QACjE,IAAI,cAAc,CAAC,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9C,OAAO,CAAC,CAAC;QACX,CAAC;QAED,MAAM,GAAG,GAAG,cAAc,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;QAC9E,OAAO,GAAG,GAAG,cAAc,CAAC,aAAa,CAAC,MAAM,CAAC;IACnD,CAAC;IAEO,eAAe,CAAC,WAAmB,EAAE,QAAsB;QACjE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,+BAA+B,EAAE;YACtD,WAAW;YACX,QAAQ;YACR,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;IACL,CAAC;CACF,CAAA;AAnSY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;yDAOuB,sBAAa,oBAAb,sBAAa,oDACd,6BAAa,oBAAb,6BAAa;GAPnC,qBAAqB,CAmSjC;AAED,uBAAuB;AACvB,IAAK,YAIJ;AAJD,WAAK,YAAY;IACf,iCAAiB,CAAA;IACjB,6BAAa,CAAA;IACb,uCAAuB,CAAA;AACzB,CAAC,EAJI,YAAY,KAAZ,YAAY,QAIhB;AAoCD,MAAM,uBAAwB,SAAQ,KAAK;IACzC,YAAY,OAAe,EAAkB,WAAmB;QAC9D,KAAK,CAAC,OAAO,CAAC,CAAC;QAD4B,gBAAW,GAAX,WAAW,CAAQ;QAE9D,IAAI,CAAC,IAAI,GAAG,yBAAyB,CAAC;IACxC,CAAC;CACF", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\resilience\\circuit-breaker.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { EventEmitter2 } from '@nestjs/event-emitter';\r\n\r\n/**\r\n * Circuit Breaker Service\r\n * \r\n * Implements circuit breaker pattern for AI service calls to prevent\r\n * cascading failures and provide graceful degradation when services\r\n * are experiencing issues. Supports multiple circuit breakers with\r\n * configurable thresholds and recovery strategies.\r\n */\r\n@Injectable()\r\nexport class CircuitBreakerService {\r\n  private readonly logger = new Logger(CircuitBreakerService.name);\r\n  private readonly circuitBreakers = new Map<string, CircuitBreaker>();\r\n  private readonly defaultConfig: CircuitBreakerConfig;\r\n\r\n  constructor(\r\n    private readonly configService: ConfigService,\r\n    private readonly eventEmitter: EventEmitter2,\r\n  ) {\r\n    this.defaultConfig = {\r\n      failureThreshold: this.configService.get<number>('ai.circuitBreaker.failureThreshold', 5),\r\n      recoveryTimeout: this.configService.get<number>('ai.circuitBreaker.recoveryTimeout', 60000),\r\n      monitoringPeriod: this.configService.get<number>('ai.circuitBreaker.monitoringPeriod', 10000),\r\n      halfOpenMaxCalls: this.configService.get<number>('ai.circuitBreaker.halfOpenMaxCalls', 3),\r\n      successThreshold: this.configService.get<number>('ai.circuitBreaker.successThreshold', 2),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Executes a function with circuit breaker protection\r\n   */\r\n  async execute<T>(\r\n    circuitName: string,\r\n    operation: () => Promise<T>,\r\n    config?: Partial<CircuitBreakerConfig>\r\n  ): Promise<T> {\r\n    const circuitBreaker = this.getOrCreateCircuitBreaker(circuitName, config);\r\n    \r\n    // Check circuit state before execution\r\n    if (circuitBreaker.state === CircuitState.OPEN) {\r\n      if (this.shouldAttemptReset(circuitBreaker)) {\r\n        circuitBreaker.state = CircuitState.HALF_OPEN;\r\n        circuitBreaker.halfOpenCalls = 0;\r\n        this.logger.debug(`Circuit breaker ${circuitName} moved to HALF_OPEN state`);\r\n      } else {\r\n        throw new CircuitBreakerOpenError(\r\n          `Circuit breaker ${circuitName} is OPEN`,\r\n          circuitName\r\n        );\r\n      }\r\n    }\r\n\r\n    if (circuitBreaker.state === CircuitState.HALF_OPEN) {\r\n      if (circuitBreaker.halfOpenCalls >= circuitBreaker.config.halfOpenMaxCalls) {\r\n        throw new CircuitBreakerOpenError(\r\n          `Circuit breaker ${circuitName} HALF_OPEN call limit exceeded`,\r\n          circuitName\r\n        );\r\n      }\r\n      circuitBreaker.halfOpenCalls++;\r\n    }\r\n\r\n    const startTime = Date.now();\r\n    \r\n    try {\r\n      const result = await operation();\r\n      \r\n      // Record successful execution\r\n      this.recordSuccess(circuitBreaker, Date.now() - startTime);\r\n      \r\n      return result;\r\n      \r\n    } catch (error) {\r\n      // Record failed execution\r\n      this.recordFailure(circuitBreaker, error, Date.now() - startTime);\r\n      \r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Gets the current state of a circuit breaker\r\n   */\r\n  getCircuitState(circuitName: string): CircuitState {\r\n    const circuitBreaker = this.circuitBreakers.get(circuitName);\r\n    return circuitBreaker ? circuitBreaker.state : CircuitState.CLOSED;\r\n  }\r\n\r\n  /**\r\n   * Gets circuit breaker statistics\r\n   */\r\n  getCircuitStats(circuitName: string): CircuitBreakerStats | null {\r\n    const circuitBreaker = this.circuitBreakers.get(circuitName);\r\n    if (!circuitBreaker) {\r\n      return null;\r\n    }\r\n\r\n    return {\r\n      name: circuitName,\r\n      state: circuitBreaker.state,\r\n      failureCount: circuitBreaker.failureCount,\r\n      successCount: circuitBreaker.successCount,\r\n      lastFailureTime: circuitBreaker.lastFailureTime,\r\n      lastSuccessTime: circuitBreaker.lastSuccessTime,\r\n      totalCalls: circuitBreaker.totalCalls,\r\n      failureRate: this.calculateFailureRate(circuitBreaker),\r\n      averageResponseTime: this.calculateAverageResponseTime(circuitBreaker),\r\n      config: circuitBreaker.config,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Gets all circuit breaker statuses\r\n   */\r\n  getStatus(): Record<string, CircuitBreakerStats> {\r\n    const status: Record<string, CircuitBreakerStats> = {};\r\n    \r\n    for (const [name] of this.circuitBreakers) {\r\n      const stats = this.getCircuitStats(name);\r\n      if (stats) {\r\n        status[name] = stats;\r\n      }\r\n    }\r\n    \r\n    return status;\r\n  }\r\n\r\n  /**\r\n   * Manually opens a circuit breaker\r\n   */\r\n  openCircuit(circuitName: string): void {\r\n    const circuitBreaker = this.circuitBreakers.get(circuitName);\r\n    if (circuitBreaker) {\r\n      circuitBreaker.state = CircuitState.OPEN;\r\n      circuitBreaker.lastFailureTime = new Date();\r\n      \r\n      this.logger.warn(`Circuit breaker ${circuitName} manually opened`);\r\n      this.emitStateChange(circuitName, CircuitState.OPEN);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Manually closes a circuit breaker\r\n   */\r\n  closeCircuit(circuitName: string): void {\r\n    const circuitBreaker = this.circuitBreakers.get(circuitName);\r\n    if (circuitBreaker) {\r\n      circuitBreaker.state = CircuitState.CLOSED;\r\n      circuitBreaker.failureCount = 0;\r\n      circuitBreaker.halfOpenCalls = 0;\r\n      \r\n      this.logger.info(`Circuit breaker ${circuitName} manually closed`);\r\n      this.emitStateChange(circuitName, CircuitState.CLOSED);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Resets circuit breaker statistics\r\n   */\r\n  resetCircuit(circuitName: string): void {\r\n    const circuitBreaker = this.circuitBreakers.get(circuitName);\r\n    if (circuitBreaker) {\r\n      circuitBreaker.failureCount = 0;\r\n      circuitBreaker.successCount = 0;\r\n      circuitBreaker.totalCalls = 0;\r\n      circuitBreaker.halfOpenCalls = 0;\r\n      circuitBreaker.responseTimes = [];\r\n      circuitBreaker.lastFailureTime = null;\r\n      circuitBreaker.lastSuccessTime = null;\r\n      circuitBreaker.state = CircuitState.CLOSED;\r\n      \r\n      this.logger.info(`Circuit breaker ${circuitName} reset`);\r\n      this.emitStateChange(circuitName, CircuitState.CLOSED);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Removes a circuit breaker\r\n   */\r\n  removeCircuit(circuitName: string): void {\r\n    if (this.circuitBreakers.delete(circuitName)) {\r\n      this.logger.info(`Circuit breaker ${circuitName} removed`);\r\n    }\r\n  }\r\n\r\n  // Private helper methods\r\n\r\n  private getOrCreateCircuitBreaker(\r\n    name: string,\r\n    config?: Partial<CircuitBreakerConfig>\r\n  ): CircuitBreaker {\r\n    let circuitBreaker = this.circuitBreakers.get(name);\r\n    \r\n    if (!circuitBreaker) {\r\n      circuitBreaker = {\r\n        name,\r\n        state: CircuitState.CLOSED,\r\n        failureCount: 0,\r\n        successCount: 0,\r\n        totalCalls: 0,\r\n        halfOpenCalls: 0,\r\n        lastFailureTime: null,\r\n        lastSuccessTime: null,\r\n        responseTimes: [],\r\n        config: { ...this.defaultConfig, ...config },\r\n      };\r\n      \r\n      this.circuitBreakers.set(name, circuitBreaker);\r\n      this.logger.debug(`Created circuit breaker: ${name}`);\r\n    }\r\n    \r\n    return circuitBreaker;\r\n  }\r\n\r\n  private recordSuccess(circuitBreaker: CircuitBreaker, responseTime: number): void {\r\n    circuitBreaker.successCount++;\r\n    circuitBreaker.totalCalls++;\r\n    circuitBreaker.lastSuccessTime = new Date();\r\n    circuitBreaker.responseTimes.push(responseTime);\r\n    \r\n    // Keep only recent response times\r\n    if (circuitBreaker.responseTimes.length > 100) {\r\n      circuitBreaker.responseTimes = circuitBreaker.responseTimes.slice(-50);\r\n    }\r\n\r\n    // Handle state transitions\r\n    if (circuitBreaker.state === CircuitState.HALF_OPEN) {\r\n      if (circuitBreaker.halfOpenCalls >= circuitBreaker.config.successThreshold) {\r\n        circuitBreaker.state = CircuitState.CLOSED;\r\n        circuitBreaker.failureCount = 0;\r\n        circuitBreaker.halfOpenCalls = 0;\r\n        \r\n        this.logger.info(`Circuit breaker ${circuitBreaker.name} closed after successful recovery`);\r\n        this.emitStateChange(circuitBreaker.name, CircuitState.CLOSED);\r\n      }\r\n    }\r\n\r\n    this.logger.debug(`Circuit breaker ${circuitBreaker.name} recorded success`);\r\n  }\r\n\r\n  private recordFailure(circuitBreaker: CircuitBreaker, error: any, responseTime: number): void {\r\n    circuitBreaker.failureCount++;\r\n    circuitBreaker.totalCalls++;\r\n    circuitBreaker.lastFailureTime = new Date();\r\n    circuitBreaker.responseTimes.push(responseTime);\r\n\r\n    // Check if we should open the circuit\r\n    if (circuitBreaker.state === CircuitState.CLOSED) {\r\n      if (circuitBreaker.failureCount >= circuitBreaker.config.failureThreshold) {\r\n        circuitBreaker.state = CircuitState.OPEN;\r\n        \r\n        this.logger.warn(\r\n          `Circuit breaker ${circuitBreaker.name} opened due to ${circuitBreaker.failureCount} failures`\r\n        );\r\n        this.emitStateChange(circuitBreaker.name, CircuitState.OPEN);\r\n      }\r\n    } else if (circuitBreaker.state === CircuitState.HALF_OPEN) {\r\n      // Failure in half-open state immediately opens the circuit\r\n      circuitBreaker.state = CircuitState.OPEN;\r\n      circuitBreaker.halfOpenCalls = 0;\r\n      \r\n      this.logger.warn(`Circuit breaker ${circuitBreaker.name} reopened after failure in HALF_OPEN state`);\r\n      this.emitStateChange(circuitBreaker.name, CircuitState.OPEN);\r\n    }\r\n\r\n    this.logger.debug(`Circuit breaker ${circuitBreaker.name} recorded failure: ${error.message}`);\r\n  }\r\n\r\n  private shouldAttemptReset(circuitBreaker: CircuitBreaker): boolean {\r\n    if (!circuitBreaker.lastFailureTime) {\r\n      return true;\r\n    }\r\n    \r\n    const timeSinceLastFailure = Date.now() - circuitBreaker.lastFailureTime.getTime();\r\n    return timeSinceLastFailure >= circuitBreaker.config.recoveryTimeout;\r\n  }\r\n\r\n  private calculateFailureRate(circuitBreaker: CircuitBreaker): number {\r\n    if (circuitBreaker.totalCalls === 0) {\r\n      return 0;\r\n    }\r\n    \r\n    return circuitBreaker.failureCount / circuitBreaker.totalCalls;\r\n  }\r\n\r\n  private calculateAverageResponseTime(circuitBreaker: CircuitBreaker): number {\r\n    if (circuitBreaker.responseTimes.length === 0) {\r\n      return 0;\r\n    }\r\n    \r\n    const sum = circuitBreaker.responseTimes.reduce((acc, time) => acc + time, 0);\r\n    return sum / circuitBreaker.responseTimes.length;\r\n  }\r\n\r\n  private emitStateChange(circuitName: string, newState: CircuitState): void {\r\n    this.eventEmitter.emit('circuit-breaker.state-changed', {\r\n      circuitName,\r\n      newState,\r\n      timestamp: new Date(),\r\n    });\r\n  }\r\n}\r\n\r\n// Enums and interfaces\r\nenum CircuitState {\r\n  CLOSED = 'CLOSED',\r\n  OPEN = 'OPEN',\r\n  HALF_OPEN = 'HALF_OPEN',\r\n}\r\n\r\ninterface CircuitBreakerConfig {\r\n  failureThreshold: number;\r\n  recoveryTimeout: number;\r\n  monitoringPeriod: number;\r\n  halfOpenMaxCalls: number;\r\n  successThreshold: number;\r\n}\r\n\r\ninterface CircuitBreaker {\r\n  name: string;\r\n  state: CircuitState;\r\n  failureCount: number;\r\n  successCount: number;\r\n  totalCalls: number;\r\n  halfOpenCalls: number;\r\n  lastFailureTime: Date | null;\r\n  lastSuccessTime: Date | null;\r\n  responseTimes: number[];\r\n  config: CircuitBreakerConfig;\r\n}\r\n\r\ninterface CircuitBreakerStats {\r\n  name: string;\r\n  state: CircuitState;\r\n  failureCount: number;\r\n  successCount: number;\r\n  lastFailureTime: Date | null;\r\n  lastSuccessTime: Date | null;\r\n  totalCalls: number;\r\n  failureRate: number;\r\n  averageResponseTime: number;\r\n  config: CircuitBreakerConfig;\r\n}\r\n\r\nclass CircuitBreakerOpenError extends Error {\r\n  constructor(message: string, public readonly circuitName: string) {\r\n    super(message);\r\n    this.name = 'CircuitBreakerOpenError';\r\n  }\r\n}\r\n"], "version": 3}