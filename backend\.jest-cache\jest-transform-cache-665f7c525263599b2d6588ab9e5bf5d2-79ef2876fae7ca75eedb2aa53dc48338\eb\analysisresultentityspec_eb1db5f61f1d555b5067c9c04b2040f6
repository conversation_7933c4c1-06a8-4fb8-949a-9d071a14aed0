7f8c31cd25c38c4b7c2b70c7e05f5d6a
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const unique_entity_id_value_object_1 = require("../../../../../shared-kernel/value-objects/unique-entity-id.value-object");
const analysis_result_entity_1 = require("../analysis-result.entity");
const analysis_result_created_domain_event_1 = require("../../events/analysis-result-created.domain-event");
const analysis_result_status_changed_domain_event_1 = require("../../events/analysis-result-status-changed.domain-event");
const analysis_result_updated_domain_event_1 = require("../../events/analysis-result-updated.domain-event");
describe('AnalysisResult Entity', () => {
    let validProps;
    let mockInputData;
    let mockOutputData;
    let mockMetadata;
    beforeEach(() => {
        mockInputData = {
            data: { test: 'input' },
            format: 'json',
            size: 100,
            checksum: 'input-checksum',
            preprocessingApplied: ['normalization'],
            validationRules: ['required'],
        };
        mockOutputData = {
            results: { prediction: 'test-result' },
            format: 'json',
            size: 200,
            checksum: 'output-checksum',
            postprocessingApplied: ['formatting'],
            validationStatus: 'passed',
            qualityScore: 0.9,
        };
        mockMetadata = {
            version: '1.0.0',
            algorithm: 'test-algorithm',
            parameters: { param1: 'value1' },
            environment: 'test',
            resourceUsage: {
                cpuTime: 1000,
                memoryUsage: 512,
                gpuTime: 500,
                networkIO: 100,
                diskIO: 50,
            },
            performanceMetrics: {
                throughput: 10,
                latency: 100,
                accuracy: 0.95,
                precision: 0.92,
                recall: 0.88,
                f1Score: 0.90,
            },
            qualityMetrics: {
                dataQuality: 0.9,
                resultReliability: 0.85,
                consistencyScore: 0.8,
                completenessScore: 0.95,
            },
        };
        validProps = {
            requestId: 'test-request-123',
            modelId: unique_entity_id_value_object_1.UniqueEntityId.generate(),
            analysisType: analysis_result_entity_1.AnalysisType.CLASSIFICATION,
            inputData: mockInputData,
            outputData: mockOutputData,
            confidence: 0.85,
            processingTime: 1500,
            status: analysis_result_entity_1.AnalysisStatus.PENDING,
            metadata: mockMetadata,
            tags: ['test', 'classification'],
            correlationId: 'correlation-123',
            parentAnalysisId: unique_entity_id_value_object_1.UniqueEntityId.generate(),
            errorDetails: undefined,
            completedAt: undefined,
        };
    });
    describe('Creation', () => {
        it('should create a valid analysis result', () => {
            const result = analysis_result_entity_1.AnalysisResult.create(validProps);
            expect(result).toBeInstanceOf(analysis_result_entity_1.AnalysisResult);
            expect(result.requestId).toBe(validProps.requestId);
            expect(result.modelId.equals(validProps.modelId)).toBe(true);
            expect(result.analysisType).toBe(validProps.analysisType);
            expect(result.status).toBe(validProps.status);
            expect(result.confidence).toBe(validProps.confidence);
            expect(result.childAnalysisIds).toEqual([]);
            expect(result.createdAt).toBeInstanceOf(Date);
            expect(result.updatedAt).toBeInstanceOf(Date);
        });
        it('should generate a domain event when created', () => {
            const result = analysis_result_entity_1.AnalysisResult.create(validProps);
            const events = result.domainEvents;
            expect(events).toHaveLength(1);
            expect(events[0]).toBeInstanceOf(analysis_result_created_domain_event_1.AnalysisResultCreatedEvent);
            expect(events[0].requestId).toBe(validProps.requestId);
        });
        it('should create with custom ID', () => {
            const customId = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const result = analysis_result_entity_1.AnalysisResult.create(validProps, customId);
            expect(result.id.equals(customId)).toBe(true);
        });
        it('should throw error for empty request ID', () => {
            const invalidProps = { ...validProps, requestId: '' };
            expect(() => analysis_result_entity_1.AnalysisResult.create(invalidProps)).toThrow('Request ID is required');
        });
        it('should throw error for missing model ID', () => {
            const invalidProps = { ...validProps, modelId: null };
            expect(() => analysis_result_entity_1.AnalysisResult.create(invalidProps)).toThrow('Model ID is required');
        });
        it('should throw error for invalid analysis type', () => {
            const invalidProps = { ...validProps, analysisType: 'invalid' };
            expect(() => analysis_result_entity_1.AnalysisResult.create(invalidProps)).toThrow('Invalid analysis type');
        });
        it('should throw error for invalid confidence', () => {
            const invalidProps = { ...validProps, confidence: 1.5 };
            expect(() => analysis_result_entity_1.AnalysisResult.create(invalidProps)).toThrow('Confidence must be between 0 and 1');
        });
        it('should throw error for negative processing time', () => {
            const invalidProps = { ...validProps, processingTime: -100 };
            expect(() => analysis_result_entity_1.AnalysisResult.create(invalidProps)).toThrow('Processing time cannot be negative');
        });
        it('should throw error for missing input data', () => {
            const invalidProps = { ...validProps, inputData: null };
            expect(() => analysis_result_entity_1.AnalysisResult.create(invalidProps)).toThrow('Input data is required');
        });
        it('should throw error for missing metadata', () => {
            const invalidProps = { ...validProps, metadata: null };
            expect(() => analysis_result_entity_1.AnalysisResult.create(invalidProps)).toThrow('Metadata is required');
        });
    });
    describe('Reconstitution', () => {
        it('should reconstitute from valid props', () => {
            const id = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const now = new Date();
            const props = {
                ...validProps,
                childAnalysisIds: [unique_entity_id_value_object_1.UniqueEntityId.generate()],
                createdAt: now,
                updatedAt: now,
            };
            const result = analysis_result_entity_1.AnalysisResult.reconstitute(props, id);
            expect(result.id.equals(id)).toBe(true);
            expect(result.requestId).toBe(props.requestId);
            expect(result.childAnalysisIds).toHaveLength(1);
            expect(result.createdAt).toBe(now);
            expect(result.updatedAt).toBe(now);
        });
    });
    describe('Status Management', () => {
        let result;
        beforeEach(() => {
            result = analysis_result_entity_1.AnalysisResult.create(validProps);
            result.clearEvents(); // Clear creation event
        });
        it('should start processing from pending status', () => {
            result.startProcessing();
            expect(result.status).toBe(analysis_result_entity_1.AnalysisStatus.PROCESSING);
            expect(result.domainEvents).toHaveLength(1);
            expect(result.domainEvents[0]).toBeInstanceOf(analysis_result_status_changed_domain_event_1.AnalysisResultStatusChangedEvent);
        });
        it('should not start processing from non-pending status', () => {
            // First start processing
            result.startProcessing();
            result.clearEvents();
            expect(() => result.startProcessing()).toThrow('Cannot start processing analysis in status: processing');
        });
        it('should complete analysis from processing status', () => {
            result.startProcessing();
            result.clearEvents();
            result.complete(mockOutputData, 1500, 0.95);
            expect(result.status).toBe(analysis_result_entity_1.AnalysisStatus.COMPLETED);
            expect(result.outputData).toEqual(mockOutputData);
            expect(result.processingTime).toBe(1500);
            expect(result.confidence).toBe(0.95);
            expect(result.completedAt).toBeInstanceOf(Date);
            expect(result.domainEvents).toHaveLength(1);
            expect(result.domainEvents[0]).toBeInstanceOf(analysis_result_status_changed_domain_event_1.AnalysisResultStatusChangedEvent);
        });
        it('should not complete analysis from non-processing status', () => {
            expect(() => result.complete(mockOutputData, 1500, 0.95)).toThrow('Cannot complete analysis in status: pending');
        });
        it('should fail analysis with error details', () => {
            result.startProcessing();
            result.clearEvents();
            const errorDetails = {
                code: 'TEST_ERROR',
                message: 'Test error',
                context: { test: true },
                retryable: true,
                category: analysis_result_entity_1.ErrorCategory.PROCESSING_ERROR,
            };
            result.fail(errorDetails);
            expect(result.status).toBe(analysis_result_entity_1.AnalysisStatus.FAILED);
            expect(result.errorDetails).toEqual(errorDetails);
            expect(result.completedAt).toBeInstanceOf(Date);
            expect(result.domainEvents).toHaveLength(1);
            expect(result.domainEvents[0]).toBeInstanceOf(analysis_result_status_changed_domain_event_1.AnalysisResultStatusChangedEvent);
        });
        it('should not fail completed analysis', () => {
            result.startProcessing();
            result.complete(mockOutputData, 1500, 0.95);
            const errorDetails = {
                code: 'TEST_ERROR',
                message: 'Test error',
                context: {},
                retryable: false,
                category: analysis_result_entity_1.ErrorCategory.PROCESSING_ERROR,
            };
            expect(() => result.fail(errorDetails)).toThrow('Cannot fail a completed analysis');
        });
        it('should cancel analysis', () => {
            result.cancel();
            expect(result.status).toBe(analysis_result_entity_1.AnalysisStatus.CANCELLED);
            expect(result.completedAt).toBeInstanceOf(Date);
            expect(result.domainEvents).toHaveLength(1);
            expect(result.domainEvents[0]).toBeInstanceOf(analysis_result_status_changed_domain_event_1.AnalysisResultStatusChangedEvent);
        });
        it('should not cancel completed or failed analysis', () => {
            result.startProcessing();
            result.complete(mockOutputData, 1500, 0.95);
            expect(() => result.cancel()).toThrow('Cannot cancel analysis in status: completed');
        });
    });
    describe('Metadata Management', () => {
        let result;
        beforeEach(() => {
            result = analysis_result_entity_1.AnalysisResult.create(validProps);
            result.clearEvents();
        });
        it('should update metadata', () => {
            const newMetadata = {
                version: '2.0.0',
                algorithm: 'new-algorithm',
            };
            result.updateMetadata(newMetadata);
            expect(result.metadata.version).toBe('2.0.0');
            expect(result.metadata.algorithm).toBe('new-algorithm');
            expect(result.metadata.parameters).toEqual(mockMetadata.parameters); // Original preserved
            expect(result.domainEvents).toHaveLength(1);
            expect(result.domainEvents[0]).toBeInstanceOf(analysis_result_updated_domain_event_1.AnalysisResultUpdatedEvent);
        });
    });
    describe('Tag Management', () => {
        let result;
        beforeEach(() => {
            result = analysis_result_entity_1.AnalysisResult.create(validProps);
            result.clearEvents();
        });
        it('should add tag', () => {
            result.addTag('new-tag');
            expect(result.tags).toContain('new-tag');
            expect(result.domainEvents).toHaveLength(1);
            expect(result.domainEvents[0]).toBeInstanceOf(analysis_result_updated_domain_event_1.AnalysisResultUpdatedEvent);
        });
        it('should not add duplicate tag', () => {
            const initialLength = result.tags.length;
            result.addTag('test'); // Already exists
            expect(result.tags).toHaveLength(initialLength);
            expect(result.domainEvents).toHaveLength(0);
        });
        it('should normalize tag case', () => {
            result.addTag('NEW-TAG');
            expect(result.tags).toContain('new-tag');
        });
        it('should throw error for empty tag', () => {
            expect(() => result.addTag('')).toThrow('Tag cannot be empty');
            expect(() => result.addTag('   ')).toThrow('Tag cannot be empty');
        });
        it('should remove tag', () => {
            result.removeTag('test');
            expect(result.tags).not.toContain('test');
            expect(result.domainEvents).toHaveLength(1);
            expect(result.domainEvents[0]).toBeInstanceOf(analysis_result_updated_domain_event_1.AnalysisResultUpdatedEvent);
        });
        it('should check if has tag', () => {
            expect(result.hasTag('test')).toBe(true);
            expect(result.hasTag('TEST')).toBe(true); // Case insensitive
            expect(result.hasTag('nonexistent')).toBe(false);
        });
    });
    describe('Child Analysis Management', () => {
        let result;
        beforeEach(() => {
            result = analysis_result_entity_1.AnalysisResult.create(validProps);
            result.clearEvents();
        });
        it('should add child analysis', () => {
            const childId = unique_entity_id_value_object_1.UniqueEntityId.generate();
            result.addChildAnalysis(childId);
            expect(result.childAnalysisIds).toHaveLength(1);
            expect(result.childAnalysisIds[0].equals(childId)).toBe(true);
            expect(result.domainEvents).toHaveLength(1);
            expect(result.domainEvents[0]).toBeInstanceOf(analysis_result_updated_domain_event_1.AnalysisResultUpdatedEvent);
        });
        it('should not add duplicate child analysis', () => {
            const childId = unique_entity_id_value_object_1.UniqueEntityId.generate();
            result.addChildAnalysis(childId);
            result.clearEvents();
            result.addChildAnalysis(childId);
            expect(result.childAnalysisIds).toHaveLength(1);
            expect(result.domainEvents).toHaveLength(0);
        });
        it('should remove child analysis', () => {
            const childId = unique_entity_id_value_object_1.UniqueEntityId.generate();
            result.addChildAnalysis(childId);
            result.clearEvents();
            result.removeChildAnalysis(childId);
            expect(result.childAnalysisIds).toHaveLength(0);
            expect(result.domainEvents).toHaveLength(1);
            expect(result.domainEvents[0]).toBeInstanceOf(analysis_result_updated_domain_event_1.AnalysisResultUpdatedEvent);
        });
    });
    describe('Status Checks', () => {
        let result;
        beforeEach(() => {
            result = analysis_result_entity_1.AnalysisResult.create(validProps);
        });
        it('should check if terminal', () => {
            expect(result.isTerminal()).toBe(false);
            result.startProcessing();
            expect(result.isTerminal()).toBe(false);
            result.complete(mockOutputData, 1500, 0.95);
            expect(result.isTerminal()).toBe(true);
        });
        it('should check if successful', () => {
            expect(result.isSuccessful()).toBe(false);
            result.startProcessing();
            result.complete(mockOutputData, 1500, 0.95);
            expect(result.isSuccessful()).toBe(true);
        });
        it('should check high confidence', () => {
            result.startProcessing();
            result.complete(mockOutputData, 1500, 0.95);
            expect(result.hasHighConfidence()).toBe(true);
            expect(result.hasHighConfidence(0.98)).toBe(false);
        });
        it('should check if retryable', () => {
            result.startProcessing();
            const retryableError = {
                code: 'RETRYABLE_ERROR',
                message: 'Retryable error',
                context: {},
                retryable: true,
                category: analysis_result_entity_1.ErrorCategory.NETWORK_ERROR,
            };
            result.fail(retryableError);
            expect(result.isRetryable()).toBe(true);
            const nonRetryableError = {
                code: 'NON_RETRYABLE_ERROR',
                message: 'Non-retryable error',
                context: {},
                retryable: false,
                category: analysis_result_entity_1.ErrorCategory.INPUT_VALIDATION,
            };
            const result2 = analysis_result_entity_1.AnalysisResult.create(validProps);
            result2.startProcessing();
            result2.fail(nonRetryableError);
            expect(result2.isRetryable()).toBe(false);
        });
    });
    describe('Utility Methods', () => {
        let result;
        beforeEach(() => {
            result = analysis_result_entity_1.AnalysisResult.create(validProps);
        });
        it('should get duration for completed analysis', () => {
            result.startProcessing();
            // Wait a bit to ensure different timestamps
            setTimeout(() => {
                result.complete(mockOutputData, 1500, 0.95);
                const duration = result.getDuration();
                expect(duration).toBeGreaterThan(0);
            }, 10);
        });
        it('should return undefined duration for incomplete analysis', () => {
            expect(result.getDuration()).toBeUndefined();
        });
        it('should calculate quality score', () => {
            const qualityScore = result.getQualityScore();
            expect(qualityScore).toBeGreaterThan(0);
            expect(qualityScore).toBeLessThanOrEqual(1);
        });
        it('should get summary', () => {
            result.startProcessing();
            result.complete(mockOutputData, 1500, 0.95);
            const summary = result.getSummary();
            expect(summary.id).toBe(result.id.toString());
            expect(summary.requestId).toBe(result.requestId);
            expect(summary.analysisType).toBe(result.analysisType);
            expect(summary.status).toBe(result.status);
            expect(summary.confidence).toBe(result.confidence);
            expect(summary.processingTime).toBe(result.processingTime);
            expect(summary.isSuccessful).toBe(true);
            expect(summary.qualityScore).toBeGreaterThan(0);
        });
    });
    describe('Immutability', () => {
        let result;
        beforeEach(() => {
            result = analysis_result_entity_1.AnalysisResult.create(validProps);
        });
        it('should return copies of arrays and objects', () => {
            const tags = result.tags;
            const childIds = result.childAnalysisIds;
            const inputData = result.inputData;
            const metadata = result.metadata;
            tags.push('modified');
            childIds.push(unique_entity_id_value_object_1.UniqueEntityId.generate());
            inputData.data.modified = true;
            metadata.version = 'modified';
            expect(result.tags).not.toContain('modified');
            expect(result.childAnalysisIds).toHaveLength(0);
            expect(result.inputData.data.modified).toBeUndefined();
            expect(result.metadata.version).toBe('1.0.0');
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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