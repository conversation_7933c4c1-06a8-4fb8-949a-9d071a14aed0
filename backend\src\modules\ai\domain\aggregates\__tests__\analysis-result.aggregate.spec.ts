import { UniqueEntityId } from '../../../../../shared-kernel/value-objects/unique-entity-id.value-object';
import { AnalysisType, AnalysisStatus } from '../../entities/analysis-result.entity';
import { AnalysisResultFactory } from '../../factories/analysis-result.factory';
import { AnalysisResultAggregate, AnalysisResultQuery } from '../analysis-result.aggregate';

describe('AnalysisResultAggregate', () => {
  let aggregate: AnalysisResultAggregate;

  beforeEach(() => {
    aggregate = new AnalysisResultAggregate();
  });

  describe('Analysis Result Management', () => {
    it('should create and add a new analysis result', () => {
      const request = {
        requestId: 'test-request-123',
        modelId: UniqueEntityId.generate(),
        analysisType: AnalysisType.CLASSIFICATION,
        inputData: {
          data: { test: 'input' },
          format: 'json',
          size: 100,
          checksum: 'checksum',
          preprocessingApplied: [],
          validationRules: [],
        },
      };

      const result = aggregate.createAnalysisResult(request);

      expect(result.requestId).toBe('test-request-123');
      expect(aggregate.getAllAnalysisResults()).toHaveLength(1);
      expect(aggregate.getAnalysisResult(result.id)).toBe(result);
    });

    it('should throw error when creating duplicate request ID', () => {
      const request = {
        requestId: 'duplicate-request',
        modelId: UniqueEntityId.generate(),
        analysisType: AnalysisType.CLASSIFICATION,
        inputData: {
          data: { test: 'input' },
          format: 'json',
          size: 100,
          checksum: 'checksum',
          preprocessingApplied: [],
          validationRules: [],
        },
      };

      aggregate.createAnalysisResult(request);

      expect(() => aggregate.createAnalysisResult(request)).toThrow(
        "Analysis result with request ID 'duplicate-request' already exists"
      );
    });

    it('should add existing analysis result', () => {
      const result = AnalysisResultFactory.createForTesting();

      aggregate.addAnalysisResult(result);

      expect(aggregate.getAllAnalysisResults()).toHaveLength(1);
      expect(aggregate.getAnalysisResult(result.id)).toBe(result);
    });

    it('should throw error when adding duplicate analysis result', () => {
      const result = AnalysisResultFactory.createForTesting();

      aggregate.addAnalysisResult(result);

      expect(() => aggregate.addAnalysisResult(result)).toThrow(
        `Analysis result with ID '${result.id.toString()}' already exists in aggregate`
      );
    });

    it('should remove analysis result', () => {
      const result = AnalysisResultFactory.createForTesting();
      aggregate.addAnalysisResult(result);

      aggregate.removeAnalysisResult(result.id);

      expect(aggregate.getAllAnalysisResults()).toHaveLength(0);
      expect(result.status).toBe(AnalysisStatus.CANCELLED);
    });

    it('should throw error when removing non-existent analysis result', () => {
      const nonExistentId = UniqueEntityId.generate();

      expect(() => aggregate.removeAnalysisResult(nonExistentId)).toThrow(
        `Analysis result with ID '${nonExistentId.toString()}' not found`
      );
    });
  });

  describe('Analysis Result Querying', () => {
    beforeEach(() => {
      // Create test analysis results
      const results = [
        AnalysisResultFactory.createForTesting({
          requestId: 'request-1',
          analysisType: AnalysisType.CLASSIFICATION,
          tags: ['test', 'classification'],
        }),
        AnalysisResultFactory.createForTesting({
          requestId: 'request-2',
          analysisType: AnalysisType.ANOMALY_DETECTION,
          tags: ['test', 'anomaly'],
        }),
        AnalysisResultFactory.createCompletedForTesting({
          requestId: 'request-3',
          analysisType: AnalysisType.CLASSIFICATION,
          tags: ['completed', 'classification'],
        }),
      ];

      results.forEach(result => aggregate.addAnalysisResult(result));
    });

    it('should get all analysis results', () => {
      const results = aggregate.getAllAnalysisResults();

      expect(results).toHaveLength(3);
    });

    it('should find analysis results by request ID', () => {
      const query: AnalysisResultQuery = {
        requestId: 'request-1',
      };

      const results = aggregate.findAnalysisResults(query);

      expect(results).toHaveLength(1);
      expect(results[0].requestId).toBe('request-1');
    });

    it('should find analysis results by analysis type', () => {
      const query: AnalysisResultQuery = {
        analysisType: AnalysisType.CLASSIFICATION,
      };

      const results = aggregate.findAnalysisResults(query);

      expect(results).toHaveLength(2);
      expect(results.every(r => r.analysisType === AnalysisType.CLASSIFICATION)).toBe(true);
    });

    it('should find analysis results by status', () => {
      const query: AnalysisResultQuery = {
        status: AnalysisStatus.COMPLETED,
      };

      const results = aggregate.findAnalysisResults(query);

      expect(results).toHaveLength(1);
      expect(results[0].status).toBe(AnalysisStatus.COMPLETED);
    });

    it('should find analysis results by tags', () => {
      const query: AnalysisResultQuery = {
        tags: ['classification'],
      };

      const results = aggregate.findAnalysisResults(query);

      expect(results).toHaveLength(2);
      expect(results.every(r => r.hasTag('classification'))).toBe(true);
    });

    it('should find analysis results by confidence range', () => {
      const query: AnalysisResultQuery = {
        minConfidence: 0.9,
      };

      const results = aggregate.findAnalysisResults(query);

      expect(results).toHaveLength(1); // Only the completed one has high confidence
      expect(results[0].confidence).toBeGreaterThanOrEqual(0.9);
    });

    it('should find analysis results by multiple criteria', () => {
      const query: AnalysisResultQuery = {
        analysisType: AnalysisType.CLASSIFICATION,
        status: AnalysisStatus.COMPLETED,
      };

      const results = aggregate.findAnalysisResults(query);

      expect(results).toHaveLength(1);
      expect(results[0].analysisType).toBe(AnalysisType.CLASSIFICATION);
      expect(results[0].status).toBe(AnalysisStatus.COMPLETED);
    });

    it('should find by request ID', () => {
      const result = aggregate.findByRequestId('request-1');

      expect(result).toBeDefined();
      expect(result!.requestId).toBe('request-1');
    });

    it('should return undefined for non-existent request ID', () => {
      const result = aggregate.findByRequestId('non-existent');

      expect(result).toBeUndefined();
    });
  });

  describe('Status-based Queries', () => {
    beforeEach(() => {
      const results = [
        AnalysisResultFactory.createForTesting({ requestId: 'pending-1' }),
        AnalysisResultFactory.createForTesting({ requestId: 'pending-2' }),
        AnalysisResultFactory.createCompletedForTesting({ requestId: 'completed-1' }),
        AnalysisResultFactory.createFailedForTesting({ requestId: 'failed-1' }),
      ];

      // Create a processing result
      const processingResult = AnalysisResultFactory.createForTesting({ requestId: 'processing-1' });
      processingResult.startProcessing();
      results.push(processingResult);

      results.forEach(result => aggregate.addAnalysisResult(result));
    });

    it('should get pending results', () => {
      const results = aggregate.getPendingResults();

      expect(results).toHaveLength(2);
      expect(results.every(r => r.status === AnalysisStatus.PENDING)).toBe(true);
    });

    it('should get processing results', () => {
      const results = aggregate.getProcessingResults();

      expect(results).toHaveLength(1);
      expect(results[0].status).toBe(AnalysisStatus.PROCESSING);
    });

    it('should get completed results', () => {
      const results = aggregate.getCompletedResults();

      expect(results).toHaveLength(1);
      expect(results[0].status).toBe(AnalysisStatus.COMPLETED);
    });

    it('should get failed results', () => {
      const results = aggregate.getFailedResults();

      expect(results).toHaveLength(1);
      expect(results[0].status).toBe(AnalysisStatus.FAILED);
    });

    it('should get retryable failed results', () => {
      const results = aggregate.getRetryableFailedResults();

      expect(results).toHaveLength(1);
      expect(results[0].isRetryable()).toBe(true);
    });

    it('should get high confidence results', () => {
      const results = aggregate.getHighConfidenceResults();

      expect(results).toHaveLength(1);
      expect(results[0].hasHighConfidence()).toBe(true);
    });
  });

  describe('Bulk Operations', () => {
    beforeEach(() => {
      const results = [
        AnalysisResultFactory.createForTesting({ requestId: 'bulk-1' }),
        AnalysisResultFactory.createForTesting({ requestId: 'bulk-2' }),
        AnalysisResultFactory.createCompletedForTesting({ requestId: 'bulk-3' }),
      ];

      // Create a processing result
      const processingResult = AnalysisResultFactory.createForTesting({ requestId: 'bulk-processing' });
      processingResult.startProcessing();
      results.push(processingResult);

      results.forEach(result => aggregate.addAnalysisResult(result));
    });

    it('should cancel all active results', () => {
      const cancelledResults = aggregate.cancelAllActiveResults();

      expect(cancelledResults).toHaveLength(3); // 2 pending + 1 processing
      expect(cancelledResults.every(r => r.status === AnalysisStatus.CANCELLED)).toBe(true);
    });

    it('should cancel results by query criteria', () => {
      const query: AnalysisResultQuery = {
        status: AnalysisStatus.PENDING,
      };

      const cancelledResults = aggregate.cancelResults(query);

      expect(cancelledResults).toHaveLength(2);
      expect(cancelledResults.every(r => r.status === AnalysisStatus.CANCELLED)).toBe(true);
    });
  });

  describe('Statistics', () => {
    beforeEach(() => {
      const results = [
        AnalysisResultFactory.createForTesting({ 
          requestId: 'stats-1',
          analysisType: AnalysisType.CLASSIFICATION 
        }),
        AnalysisResultFactory.createForTesting({ 
          requestId: 'stats-2',
          analysisType: AnalysisType.ANOMALY_DETECTION 
        }),
        AnalysisResultFactory.createCompletedForTesting({ 
          requestId: 'stats-3',
          analysisType: AnalysisType.CLASSIFICATION 
        }),
        AnalysisResultFactory.createFailedForTesting({ 
          requestId: 'stats-4',
          analysisType: AnalysisType.CLASSIFICATION 
        }),
      ];

      results.forEach(result => aggregate.addAnalysisResult(result));
    });

    it('should calculate aggregate statistics', () => {
      const stats = aggregate.getStatistics();

      expect(stats.totalResults).toBe(4);
      expect(stats.statusDistribution[AnalysisStatus.PENDING]).toBe(2);
      expect(stats.statusDistribution[AnalysisStatus.COMPLETED]).toBe(1);
      expect(stats.statusDistribution[AnalysisStatus.FAILED]).toBe(1);
      expect(stats.typeDistribution[AnalysisType.CLASSIFICATION]).toBe(3);
      expect(stats.typeDistribution[AnalysisType.ANOMALY_DETECTION]).toBe(1);
      expect(stats.successRate).toBe(0.25); // 1 successful out of 4
      expect(stats.retryableFailures).toBe(1);
      expect(stats.completionRate).toBe(0.5); // 2 terminal out of 4
    });

    it('should return empty statistics for empty aggregate', () => {
      const emptyAggregate = new AnalysisResultAggregate();
      const stats = emptyAggregate.getStatistics();

      expect(stats.totalResults).toBe(0);
      expect(stats.averageConfidence).toBe(0);
      expect(stats.successRate).toBe(0);
    });
  });

  describe('Batch Operations', () => {
    it('should create batch of analysis results', () => {
      const requests = [
        {
          requestId: 'batch-1',
          modelId: UniqueEntityId.generate(),
          analysisType: AnalysisType.CLASSIFICATION,
          inputData: {
            data: { test: 'input' },
            format: 'json',
            size: 100,
            checksum: 'checksum',
            preprocessingApplied: [],
            validationRules: [],
          },
        },
        {
          requestId: 'batch-2',
          modelId: UniqueEntityId.generate(),
          analysisType: AnalysisType.ANOMALY_DETECTION,
          inputData: {
            data: { test: 'input' },
            format: 'json',
            size: 100,
            checksum: 'checksum',
            preprocessingApplied: [],
            validationRules: [],
          },
        },
      ];

      const batch = aggregate.createBatch(requests);

      expect(batch.results).toHaveLength(2);
      expect(batch.totalCount).toBe(2);
      expect(batch.pendingCount).toBe(2);
      expect(batch.successCount).toBe(0);
      expect(batch.failureCount).toBe(0);
    });

    it('should handle errors in batch creation', () => {
      // First create a result to cause duplicate error
      aggregate.createAnalysisResult({
        requestId: 'duplicate',
        modelId: UniqueEntityId.generate(),
        analysisType: AnalysisType.CLASSIFICATION,
        inputData: {
          data: { test: 'input' },
          format: 'json',
          size: 100,
          checksum: 'checksum',
          preprocessingApplied: [],
          validationRules: [],
        },
      });

      const requests = [
        {
          requestId: 'duplicate', // This will fail
          modelId: UniqueEntityId.generate(),
          analysisType: AnalysisType.CLASSIFICATION,
          inputData: {
            data: { test: 'input' },
            format: 'json',
            size: 100,
            checksum: 'checksum',
            preprocessingApplied: [],
            validationRules: [],
          },
        },
        {
          requestId: 'valid',
          modelId: UniqueEntityId.generate(),
          analysisType: AnalysisType.ANOMALY_DETECTION,
          inputData: {
            data: { test: 'input' },
            format: 'json',
            size: 100,
            checksum: 'checksum',
            preprocessingApplied: [],
            validationRules: [],
          },
        },
      ];

      const batch = aggregate.createBatch(requests);

      expect(batch.results).toHaveLength(1); // Only the valid one
      expect(batch.totalCount).toBe(1);
    });
  });

  describe('Grouping and Sorting', () => {
    beforeEach(() => {
      const results = [
        AnalysisResultFactory.createForTesting({ 
          requestId: 'group-1',
          analysisType: AnalysisType.CLASSIFICATION 
        }),
        AnalysisResultFactory.createForTesting({ 
          requestId: 'group-2',
          analysisType: AnalysisType.CLASSIFICATION 
        }),
        AnalysisResultFactory.createCompletedForTesting({ 
          requestId: 'group-3',
          analysisType: AnalysisType.ANOMALY_DETECTION 
        }),
      ];

      results.forEach(result => aggregate.addAnalysisResult(result));
    });

    it('should group by analysis type', () => {
      const groups = aggregate.groupByAnalysisType();

      expect(groups.size).toBe(2);
      expect(groups.get(AnalysisType.CLASSIFICATION)).toHaveLength(2);
      expect(groups.get(AnalysisType.ANOMALY_DETECTION)).toHaveLength(1);
    });

    it('should group by status', () => {
      const groups = aggregate.groupByStatus();

      expect(groups.size).toBe(2);
      expect(groups.get(AnalysisStatus.PENDING)).toHaveLength(2);
      expect(groups.get(AnalysisStatus.COMPLETED)).toHaveLength(1);
    });

    it('should get most recent results', () => {
      const results = aggregate.getMostRecentResults(2);

      expect(results).toHaveLength(2);
      // Results should be sorted by creation date (most recent first)
      expect(results[0].createdAt.getTime()).toBeGreaterThanOrEqual(results[1].createdAt.getTime());
    });

    it('should get results by confidence', () => {
      const results = aggregate.getResultsByConfidence();

      expect(results).toHaveLength(3);
      // Should be sorted by confidence (highest first)
      for (let i = 0; i < results.length - 1; i++) {
        expect(results[i].confidence).toBeGreaterThanOrEqual(results[i + 1].confidence);
      }
    });

    it('should get results by quality', () => {
      const results = aggregate.getResultsByQuality(2);

      expect(results).toHaveLength(2);
      // Should be sorted by quality score (highest first)
      for (let i = 0; i < results.length - 1; i++) {
        expect(results[i].getQualityScore()).toBeGreaterThanOrEqual(results[i + 1].getQualityScore());
      }
    });
  });

  describe('Relationship Queries', () => {
    it('should find by correlation ID', () => {
      const correlationId = 'test-correlation';
      const results = [
        AnalysisResultFactory.createForTesting({ 
          requestId: 'corr-1',
          correlationId 
        }),
        AnalysisResultFactory.createForTesting({ 
          requestId: 'corr-2',
          correlationId 
        }),
        AnalysisResultFactory.createForTesting({ 
          requestId: 'corr-3',
          correlationId: 'different' 
        }),
      ];

      results.forEach(result => aggregate.addAnalysisResult(result));

      const correlatedResults = aggregate.findByCorrelationId(correlationId);

      expect(correlatedResults).toHaveLength(2);
      expect(correlatedResults.every(r => r.correlationId === correlationId)).toBe(true);
    });

    it('should get child results', () => {
      const parentId = UniqueEntityId.generate();
      const parent = AnalysisResultFactory.createForTesting({ 
        requestId: 'parent' 
      });
      const child1 = AnalysisResultFactory.createForTesting({ 
        requestId: 'child-1',
        parentAnalysisId: parentId 
      });
      const child2 = AnalysisResultFactory.createForTesting({ 
        requestId: 'child-2',
        parentAnalysisId: parentId 
      });

      [parent, child1, child2].forEach(result => aggregate.addAnalysisResult(result));

      const childResults = aggregate.getChildResults(parentId);

      expect(childResults).toHaveLength(2);
      expect(childResults.every(r => r.parentAnalysisId?.equals(parentId))).toBe(true);
    });

    it('should get results by model ID', () => {
      const modelId = UniqueEntityId.generate();
      const results = [
        AnalysisResultFactory.createForTesting({ 
          requestId: 'model-1',
          modelId 
        }),
        AnalysisResultFactory.createForTesting({ 
          requestId: 'model-2',
          modelId 
        }),
        AnalysisResultFactory.createForTesting({ 
          requestId: 'model-3' 
        }),
      ];

      results.forEach(result => aggregate.addAnalysisResult(result));

      const modelResults = aggregate.getResultsByModel(modelId);

      expect(modelResults).toHaveLength(2);
      expect(modelResults.every(r => r.modelId.equals(modelId))).toBe(true);
    });
  });
});