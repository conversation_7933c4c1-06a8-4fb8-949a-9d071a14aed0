1853a7d42b70059a8d6d2406639af9b4
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AiOrchestrationService_1;
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AiOrchestrationService = void 0;
const common_1 = require("@nestjs/common");
const cqrs_1 = require("@nestjs/cqrs");
const config_1 = require("@nestjs/config");
const ai_model_provider_interface_1 = require("../../../domain/services/ai-model-provider.interface");
const prediction_engine_interface_1 = require("../../../domain/services/prediction-engine.interface");
const model_selection_service_1 = require("./model-selection.service");
const load_balancer_service_1 = require("./load-balancer.service");
const circuit_breaker_service_1 = require("../resilience/circuit-breaker.service");
const ai_cache_service_1 = require("../caching/ai-cache.service");
const metrics_adapter_1 = require("../../../infrastructure/adapters/metrics.adapter");
/**
 * AI Orchestration Service
 *
 * Coordinates AI operations across multiple providers and models,
 * managing request routing, load balancing, and result aggregation.
 * Implements enterprise patterns for reliability and performance.
 */
let AiOrchestrationService = AiOrchestrationService_1 = class AiOrchestrationService {
    constructor(aiModelProvider, predictionEngine, modelSelectionService, loadBalancerService, circuitBreakerService, cacheService, metricsAdapter, eventBus, commandBus, queryBus, configService) {
        this.aiModelProvider = aiModelProvider;
        this.predictionEngine = predictionEngine;
        this.modelSelectionService = modelSelectionService;
        this.loadBalancerService = loadBalancerService;
        this.circuitBreakerService = circuitBreakerService;
        this.cacheService = cacheService;
        this.metricsAdapter = metricsAdapter;
        this.eventBus = eventBus;
        this.commandBus = commandBus;
        this.queryBus = queryBus;
        this.configService = configService;
        this.logger = new common_1.Logger(AiOrchestrationService_1.name);
    }
    /**
     * Orchestrates AI analysis request with intelligent routing and fallback
     */
    async orchestrateAnalysis(request) {
        const startTime = Date.now();
        const requestId = this.generateRequestId();
        this.logger.log(`Starting AI analysis orchestration: ${requestId}`);
        try {
            // Check cache first
            const cachedResult = await this.checkCache(request);
            if (cachedResult) {
                this.recordMetrics('cache_hit', startTime, requestId);
                return cachedResult;
            }
            // Select optimal model and provider
            const modelConfig = await this.modelSelectionService.selectOptimalModel(request);
            // Get available providers with load balancing
            const providers = await this.loadBalancerService.getAvailableProviders(modelConfig.providerType);
            // Execute analysis with circuit breaker protection
            const result = await this.executeWithFallback(request, providers, modelConfig);
            // Cache successful results
            await this.cacheResult(request, result);
            // Record metrics and emit events
            this.recordMetrics('success', startTime, requestId);
            await this.emitAnalysisCompleted(requestId, result);
            return result;
        }
        catch (error) {
            this.logger.error(`AI analysis orchestration failed: ${requestId}`, error);
            this.recordMetrics('error', startTime, requestId);
            throw new AiOrchestrationError(`Analysis failed: ${error.message}`, requestId);
        }
    }
    /**
     * Orchestrates batch AI processing with parallel execution
     */
    async orchestrateBatchAnalysis(requests) {
        const batchId = this.generateBatchId();
        this.logger.log(`Starting batch AI analysis: ${batchId}, count: ${requests.length}`);
        try {
            // Group requests by optimal model configuration
            const requestGroups = await this.groupRequestsByModel(requests);
            // Process groups in parallel with concurrency limits
            const concurrencyLimit = this.configService.get('ai.batchConcurrency', 5);
            const results = [];
            for (const group of requestGroups) {
                const groupResults = await this.processBatchGroup(group, concurrencyLimit);
                results.push(...groupResults);
            }
            this.logger.log(`Completed batch AI analysis: ${batchId}`);
            return results;
        }
        catch (error) {
            this.logger.error(`Batch AI analysis failed: ${batchId}`, error);
            throw new AiOrchestrationError(`Batch analysis failed: ${error.message}`, batchId);
        }
    }
    /**
     * Orchestrates model training workflow
     */
    async orchestrateTraining(trainingRequest) {
        const trainingId = this.generateTrainingId();
        this.logger.log(`Starting AI training orchestration: ${trainingId}`);
        try {
            // Validate training data and configuration
            await this.validateTrainingRequest(trainingRequest);
            // Select training infrastructure
            const trainingConfig = await this.selectTrainingInfrastructure(trainingRequest);
            // Initialize training pipeline
            const pipeline = await this.initializeTrainingPipeline(trainingConfig);
            // Execute training with monitoring
            const result = await this.executeTrainingWithMonitoring(pipeline, trainingRequest);
            // Validate and deploy model
            await this.validateAndDeployModel(result);
            this.logger.log(`Completed AI training orchestration: ${trainingId}`);
            return result;
        }
        catch (error) {
            this.logger.error(`AI training orchestration failed: ${trainingId}`, error);
            throw new AiOrchestrationError(`Training failed: ${error.message}`, trainingId);
        }
    }
    /**
     * Orchestrates real-time prediction with streaming support
     */
    async orchestrateRealTimePrediction(predictionRequest) {
        const predictionId = this.generatePredictionId();
        try {
            // Select real-time optimized model
            const modelConfig = await this.modelSelectionService.selectRealTimeModel(predictionRequest);
            // Execute prediction with low latency requirements
            const result = await this.predictionEngine.predict(predictionRequest, {
                modelConfig,
                timeout: this.configService.get('ai.realTimeTimeout', 1000),
                priority: 'high',
            });
            // Emit real-time events
            await this.emitPredictionGenerated(predictionId, result);
            return result;
        }
        catch (error) {
            this.logger.error(`Real-time prediction failed: ${predictionId}`, error);
            throw new AiOrchestrationError(`Prediction failed: ${error.message}`, predictionId);
        }
    }
    /**
     * Health check for AI orchestration system
     */
    async checkHealth() {
        try {
            const providerHealth = await this.loadBalancerService.checkProviderHealth();
            const cacheHealth = await this.cacheService.checkHealth();
            const circuitBreakerStatus = this.circuitBreakerService.getStatus();
            return {
                status: 'healthy',
                providers: providerHealth,
                cache: cacheHealth,
                circuitBreakers: circuitBreakerStatus,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error('AI orchestration health check failed', error);
            return {
                status: 'unhealthy',
                error: error.message,
                timestamp: new Date(),
            };
        }
    }
    // Private helper methods
    async checkCache(request) {
        const cacheKey = this.generateCacheKey(request);
        return await this.cacheService.get(cacheKey);
    }
    async cacheResult(request, result) {
        const cacheKey = this.generateCacheKey(request);
        const ttl = this.configService.get('ai.cacheTtl', 3600);
        await this.cacheService.set(cacheKey, result, ttl);
    }
    async executeWithFallback(request, providers, modelConfig) {
        for (const provider of providers) {
            try {
                return await this.circuitBreakerService.execute(`ai-provider-${provider.id}`, () => this.aiModelProvider.analyze(request, { ...modelConfig, provider }));
            }
            catch (error) {
                this.logger.warn(`Provider ${provider.id} failed, trying next`, error);
                continue;
            }
        }
        throw new Error('All AI providers failed');
    }
    async groupRequestsByModel(requests) {
        const groups = new Map();
        for (const request of requests) {
            const modelConfig = await this.modelSelectionService.selectOptimalModel(request);
            const groupKey = `${modelConfig.providerType}-${modelConfig.modelId}`;
            if (!groups.has(groupKey)) {
                groups.set(groupKey, []);
            }
            groups.get(groupKey).push(request);
        }
        return Array.from(groups.entries()).map(([key, requests]) => ({
            key,
            requests,
            modelConfig: null, // Will be set during processing
        }));
    }
    async processBatchGroup(group, concurrencyLimit) {
        const chunks = this.chunkArray(group.requests, concurrencyLimit);
        const results = [];
        for (const chunk of chunks) {
            const chunkResults = await Promise.all(chunk.map(request => this.orchestrateAnalysis(request)));
            results.push(...chunkResults);
        }
        return results;
    }
    chunkArray(array, size) {
        const chunks = [];
        for (let i = 0; i < array.length; i += size) {
            chunks.push(array.slice(i, i + size));
        }
        return chunks;
    }
    generateRequestId() {
        return `ai-req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    generateBatchId() {
        return `ai-batch-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    generateTrainingId() {
        return `ai-train-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    generatePredictionId() {
        return `ai-pred-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    generateCacheKey(request) {
        const hash = require('crypto')
            .createHash('sha256')
            .update(JSON.stringify(request))
            .digest('hex');
        return `ai-analysis:${hash}`;
    }
    recordMetrics(type, startTime, requestId) {
        const duration = Date.now() - startTime;
        this.metricsAdapter.recordAiOperation(type, duration, requestId);
    }
    async emitAnalysisCompleted(requestId, result) {
        // Implementation will be added when event classes are created
    }
    async emitPredictionGenerated(predictionId, result) {
        // Implementation will be added when event classes are created
    }
    async validateTrainingRequest(request) {
        // Implementation for training request validation
    }
    async selectTrainingInfrastructure(request) {
        // Implementation for training infrastructure selection
        return {};
    }
    async initializeTrainingPipeline(config) {
        // Implementation for training pipeline initialization
        return {};
    }
    async executeTrainingWithMonitoring(pipeline, request) {
        // Implementation for training execution with monitoring
        return {};
    }
    async validateAndDeployModel(result) {
        // Implementation for model validation and deployment
    }
};
exports.AiOrchestrationService = AiOrchestrationService;
exports.AiOrchestrationService = AiOrchestrationService = AiOrchestrationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(ai_model_provider_interface_1.AI_MODEL_PROVIDER)),
    __param(1, (0, common_1.Inject)(prediction_engine_interface_1.PREDICTION_ENGINE)),
    __metadata("design:paramtypes", [typeof (_a = typeof ai_model_provider_interface_1.AiModelProvider !== "undefined" && ai_model_provider_interface_1.AiModelProvider) === "function" ? _a : Object, typeof (_b = typeof prediction_engine_interface_1.PredictionEngine !== "undefined" && prediction_engine_interface_1.PredictionEngine) === "function" ? _b : Object, typeof (_c = typeof model_selection_service_1.ModelSelectionService !== "undefined" && model_selection_service_1.ModelSelectionService) === "function" ? _c : Object, typeof (_d = typeof load_balancer_service_1.LoadBalancerService !== "undefined" && load_balancer_service_1.LoadBalancerService) === "function" ? _d : Object, typeof (_e = typeof circuit_breaker_service_1.CircuitBreakerService !== "undefined" && circuit_breaker_service_1.CircuitBreakerService) === "function" ? _e : Object, typeof (_f = typeof ai_cache_service_1.AiCacheService !== "undefined" && ai_cache_service_1.AiCacheService) === "function" ? _f : Object, typeof (_g = typeof metrics_adapter_1.MetricsAdapter !== "undefined" && metrics_adapter_1.MetricsAdapter) === "function" ? _g : Object, typeof (_h = typeof cqrs_1.EventBus !== "undefined" && cqrs_1.EventBus) === "function" ? _h : Object, typeof (_j = typeof cqrs_1.CommandBus !== "undefined" && cqrs_1.CommandBus) === "function" ? _j : Object, typeof (_k = typeof cqrs_1.QueryBus !== "undefined" && cqrs_1.QueryBus) === "function" ? _k : Object, typeof (_l = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _l : Object])
], AiOrchestrationService);
class AiOrchestrationError extends Error {
    constructor(message, requestId) {
        super(message);
        this.requestId = requestId;
        this.name = 'AiOrchestrationError';
    }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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