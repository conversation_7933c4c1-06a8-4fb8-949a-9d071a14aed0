import { FactoryProvider, ModuleMetadata, Provider, Type } from '@nestjs/common';
import { BullRootModuleOptions } from './bull-module-options.interface';
export interface SharedBullConfigurationFactory {
    createSharedConfiguration(): Promise<BullRootModuleOptions> | BullRootModuleOptions;
}
export interface SharedBullAsyncConfiguration extends Pick<ModuleMetadata, 'imports'> {
    /**
     * Existing Provider to be used.
     */
    useExisting?: Type<SharedBullConfigurationFactory>;
    /**
     * Type (class name) of provider (instance to be registered and injected).
     */
    useClass?: Type<SharedBullConfigurationFactory>;
    /**
     * Factory function that returns an instance of the provider to be injected.
     */
    useFactory?: (...args: any[]) => Promise<BullRootModuleOptions> | BullRootModuleOptions;
    /**
     * Optional list of providers to be injected into the context of the Factory function.
     */
    inject?: FactoryProvider['inject'];
    /**
     * Extra providers to be registered in the module context.
     */
    extraProviders?: Provider[];
}
//# sourceMappingURL=shared-bull-config.interface.d.ts.map