43e669ce46ba90eb124dfb15f766c070
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrecisionRecall = void 0;
const base_value_object_1 = require("../../../../shared-kernel/value-objects/base-value-object");
class PrecisionRecall extends base_value_object_1.BaseValueObject {
    constructor(precision, recall) {
        super({ precision, recall });
    }
    validate() {
        super.validate();
        if (typeof this._value.precision !== 'number' || typeof this._value.recall !== 'number') {
            throw new Error('Precision and recall must be numbers');
        }
        if (isNaN(this._value.precision) || isNaN(this._value.recall)) {
            throw new Error('Precision and recall cannot be NaN');
        }
        if (!isFinite(this._value.precision) || !isFinite(this._value.recall)) {
            throw new Error('Precision and recall must be finite');
        }
        if (this._value.precision < PrecisionRecall.MIN_VALUE || this._value.precision > PrecisionRecall.MAX_VALUE) {
            throw new Error(`Precision must be between ${PrecisionRecall.MIN_VALUE} and ${PrecisionRecall.MAX_VALUE}`);
        }
        if (this._value.recall < PrecisionRecall.MIN_VALUE || this._value.recall > PrecisionRecall.MAX_VALUE) {
            throw new Error(`Recall must be between ${PrecisionRecall.MIN_VALUE} and ${PrecisionRecall.MAX_VALUE}`);
        }
    }
    /**
     * Creates precision-recall from confusion matrix values
     */
    static fromConfusionMatrix(truePositives, falsePositives, falseNegatives) {
        if (truePositives < 0 || falsePositives < 0 || falseNegatives < 0) {
            throw new Error('Confusion matrix values cannot be negative');
        }
        const precision = (truePositives + falsePositives) === 0 ? 0 : truePositives / (truePositives + falsePositives);
        const recall = (truePositives + falseNegatives) === 0 ? 0 : truePositives / (truePositives + falseNegatives);
        return new PrecisionRecall(precision, recall);
    }
    /**
     * Creates precision-recall from percentages (0-100)
     */
    static fromPercentages(precisionPercent, recallPercent) {
        if (precisionPercent < 0 || precisionPercent > 100 || recallPercent < 0 || recallPercent > 100) {
            throw new Error('Percentages must be between 0 and 100');
        }
        return new PrecisionRecall(precisionPercent / 100, recallPercent / 100);
    }
    /**
     * Creates perfect precision-recall (1.0, 1.0)
     */
    static perfect() {
        return new PrecisionRecall(PrecisionRecall.MAX_VALUE, PrecisionRecall.MAX_VALUE);
    }
    /**
     * Creates zero precision-recall (0.0, 0.0)
     */
    static zero() {
        return new PrecisionRecall(PrecisionRecall.MIN_VALUE, PrecisionRecall.MIN_VALUE);
    }
    /**
     * Gets the precision value
     */
    get precision() {
        return this._value.precision;
    }
    /**
     * Gets the recall value
     */
    get recall() {
        return this._value.recall;
    }
    /**
     * Calculates the F1 score (harmonic mean of precision and recall)
     */
    getF1Score() {
        if (this._value.precision + this._value.recall === 0) {
            return 0;
        }
        return (2 * this._value.precision * this._value.recall) / (this._value.precision + this._value.recall);
    }
    /**
     * Calculates the F-beta score with custom beta parameter
     * Beta > 1 gives more weight to recall
     * Beta < 1 gives more weight to precision
     */
    getFBetaScore(beta) {
        if (beta <= 0) {
            throw new Error('Beta must be greater than 0');
        }
        const betaSquared = beta * beta;
        const denominator = (betaSquared * this._value.precision) + this._value.recall;
        if (denominator === 0) {
            return 0;
        }
        return ((1 + betaSquared) * this._value.precision * this._value.recall) / denominator;
    }
    /**
     * Calculates the F2 score (gives more weight to recall)
     */
    getF2Score() {
        return this.getFBetaScore(2);
    }
    /**
     * Calculates the F0.5 score (gives more weight to precision)
     */
    getF05Score() {
        return this.getFBetaScore(0.5);
    }
    /**
     * Gets the harmonic mean of precision and recall
     */
    getHarmonicMean() {
        return this.getF1Score();
    }
    /**
     * Gets the arithmetic mean of precision and recall
     */
    getArithmeticMean() {
        return (this._value.precision + this._value.recall) / 2;
    }
    /**
     * Gets the geometric mean of precision and recall
     */
    getGeometricMean() {
        return Math.sqrt(this._value.precision * this._value.recall);
    }
    /**
     * Checks if precision is higher than recall
     */
    isPrecisionHigher() {
        return this._value.precision > this._value.recall;
    }
    /**
     * Checks if recall is higher than precision
     */
    isRecallHigher() {
        return this._value.recall > this._value.precision;
    }
    /**
     * Checks if precision and recall are balanced (within threshold)
     */
    isBalanced(threshold = 0.05) {
        return Math.abs(this._value.precision - this._value.recall) <= threshold;
    }
    /**
     * Gets the precision-recall trade-off ratio
     */
    getTradeOffRatio() {
        if (this._value.recall === 0) {
            return this._value.precision === 0 ? 1 : Infinity;
        }
        return this._value.precision / this._value.recall;
    }
    /**
     * Checks if both precision and recall meet minimum thresholds
     */
    meetsBothThresholds(precisionThreshold, recallThreshold) {
        return this._value.precision >= precisionThreshold && this._value.recall >= recallThreshold;
    }
    /**
     * Checks if either precision or recall meets the threshold
     */
    meetsEitherThreshold(precisionThreshold, recallThreshold) {
        return this._value.precision >= precisionThreshold || this._value.recall >= recallThreshold;
    }
    /**
     * Gets precision as a percentage
     */
    getPrecisionPercentage() {
        return this._value.precision * 100;
    }
    /**
     * Gets recall as a percentage
     */
    getRecallPercentage() {
        return this._value.recall * 100;
    }
    /**
     * Gets precision as a percentage string
     */
    getPrecisionPercentageString(decimals = 1) {
        return `${(this._value.precision * 100).toFixed(decimals)}%`;
    }
    /**
     * Gets recall as a percentage string
     */
    getRecallPercentageString(decimals = 1) {
        return `${(this._value.recall * 100).toFixed(decimals)}%`;
    }
    /**
     * Combines this precision-recall with another using weighted average
     */
    combineWith(other, weight = 0.5) {
        if (weight < 0 || weight > 1) {
            throw new Error('Weight must be between 0 and 1');
        }
        const combinedPrecision = this._value.precision * weight + other._value.precision * (1 - weight);
        const combinedRecall = this._value.recall * weight + other._value.recall * (1 - weight);
        return new PrecisionRecall(combinedPrecision, combinedRecall);
    }
    /**
     * Gets the Euclidean distance from another precision-recall point
     */
    distanceFrom(other) {
        const precisionDiff = this._value.precision - other._value.precision;
        const recallDiff = this._value.recall - other._value.recall;
        return Math.sqrt(precisionDiff * precisionDiff + recallDiff * recallDiff);
    }
    /**
     * Gets the Manhattan distance from another precision-recall point
     */
    manhattanDistanceFrom(other) {
        return Math.abs(this._value.precision - other._value.precision) +
            Math.abs(this._value.recall - other._value.recall);
    }
    /**
     * Checks if this precision-recall is better than another based on F1 score
     */
    isBetterThan(other) {
        return this.getF1Score() > other.getF1Score();
    }
    /**
     * Rounds precision and recall to specified decimal places
     */
    round(decimals = 3) {
        const factor = Math.pow(10, decimals);
        const roundedPrecision = Math.round(this._value.precision * factor) / factor;
        const roundedRecall = Math.round(this._value.recall * factor) / factor;
        return new PrecisionRecall(roundedPrecision, roundedRecall);
    }
    /**
     * Converts to a human-readable string
     */
    toString() {
        return `Precision: ${this.getPrecisionPercentageString()}, Recall: ${this.getRecallPercentageString()}, F1: ${(this.getF1Score() * 100).toFixed(1)}%`;
    }
    /**
     * Converts to JSON representation
     */
    toJSON() {
        return {
            precision: this._value.precision,
            recall: this._value.recall,
            precisionPercentage: this.getPrecisionPercentage(),
            recallPercentage: this.getRecallPercentage(),
            f1Score: this.getF1Score(),
            f2Score: this.getF2Score(),
            f05Score: this.getF05Score(),
            harmonicMean: this.getHarmonicMean(),
            arithmeticMean: this.getArithmeticMean(),
            geometricMean: this.getGeometricMean(),
            isBalanced: this.isBalanced(),
            tradeOffRatio: this.getTradeOffRatio(),
        };
    }
    /**
     * Creates a PrecisionRecall from JSON
     */
    static fromJSON(json) {
        return new PrecisionRecall(json.precision, json.recall);
    }
    /**
     * Calculates the average of multiple precision-recall pairs
     */
    static average(pairs) {
        if (pairs.length === 0) {
            throw new Error('Cannot calculate average of empty array');
        }
        const sumPrecision = pairs.reduce((acc, pair) => acc + pair._value.precision, 0);
        const sumRecall = pairs.reduce((acc, pair) => acc + pair._value.recall, 0);
        return new PrecisionRecall(sumPrecision / pairs.length, sumRecall / pairs.length);
    }
    /**
     * Calculates the weighted average of multiple precision-recall pairs
     */
    static weightedAverage(pairs, weights) {
        if (pairs.length === 0) {
            throw new Error('Cannot calculate weighted average of empty array');
        }
        if (pairs.length !== weights.length) {
            throw new Error('Pairs and weights arrays must have the same length');
        }
        const totalWeight = weights.reduce((acc, weight) => acc + weight, 0);
        if (totalWeight === 0) {
            throw new Error('Total weight cannot be zero');
        }
        const weightedPrecision = pairs.reduce((acc, pair, index) => acc + pair._value.precision * weights[index], 0);
        const weightedRecall = pairs.reduce((acc, pair, index) => acc + pair._value.recall * weights[index], 0);
        return new PrecisionRecall(weightedPrecision / totalWeight, weightedRecall / totalWeight);
    }
    /**
     * Finds the pair with the highest F1 score
     */
    static bestF1(pairs) {
        if (pairs.length === 0) {
            throw new Error('Cannot find best F1 of empty array');
        }
        return pairs.reduce((best, current) => current.getF1Score() > best.getF1Score() ? current : best);
    }
    /**
     * Finds the pair with the highest precision
     */
    static bestPrecision(pairs) {
        if (pairs.length === 0) {
            throw new Error('Cannot find best precision of empty array');
        }
        return pairs.reduce((best, current) => current._value.precision > best._value.precision ? current : best);
    }
    /**
     * Finds the pair with the highest recall
     */
    static bestRecall(pairs) {
        if (pairs.length === 0) {
            throw new Error('Cannot find best recall of empty array');
        }
        return pairs.reduce((best, current) => current._value.recall > best._value.recall ? current : best);
    }
}
exports.PrecisionRecall = PrecisionRecall;
PrecisionRecall.MIN_VALUE = 0.0;
PrecisionRecall.MAX_VALUE = 1.0;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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