602bece80eaaf684dbb6696262c3f108
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const bull_1 = require("@nestjs/bull");
const message_queue_client_1 = require("../message-queue.client");
describe('MessageQueueClient', () => {
    let client;
    let configService;
    let analysisQueue;
    let trainingQueue;
    let predictionQueue;
    let batchQueue;
    const createMockQueue = () => ({
        add: jest.fn(),
        getJob: jest.fn(),
        getWaiting: jest.fn(),
        getActive: jest.fn(),
        getCompleted: jest.fn(),
        getFailed: jest.fn(),
        getDelayed: jest.fn(),
        getPaused: jest.fn(),
        getJobLogs: jest.fn(),
        isPaused: jest.fn(),
        pause: jest.fn(),
        resume: jest.fn(),
        clean: jest.fn(),
    });
    const createMockJob = (id, data, state = 'waiting') => ({
        id,
        data,
        timestamp: Date.now(),
        opts: { attempts: 3 },
        attemptsMade: 0,
        returnvalue: undefined,
        failedReason: undefined,
        processedOn: undefined,
        finishedOn: undefined,
        getState: jest.fn().mockResolvedValue(state),
        progress: jest.fn().mockReturnValue(0),
        remove: jest.fn(),
        retry: jest.fn(),
    });
    beforeEach(async () => {
        const mockConfigService = {
            get: jest.fn().mockImplementation((key, defaultValue) => {
                const config = {
                    'ai.queue.attempts': 3,
                    'ai.queue.backoffDelay': 2000,
                    'ai.queue.removeOnComplete': 100,
                    'ai.queue.removeOnFail': 50,
                };
                return config[key] || defaultValue;
            }),
        };
        analysisQueue = createMockQueue();
        trainingQueue = createMockQueue();
        predictionQueue = createMockQueue();
        batchQueue = createMockQueue();
        const module = await testing_1.Test.createTestingModule({
            providers: [
                message_queue_client_1.MessageQueueClient,
                { provide: config_1.ConfigService, useValue: mockConfigService },
                { provide: (0, bull_1.getQueueToken)('ai-analysis'), useValue: analysisQueue },
                { provide: (0, bull_1.getQueueToken)('ai-training'), useValue: trainingQueue },
                { provide: (0, bull_1.getQueueToken)('ai-prediction'), useValue: predictionQueue },
                { provide: (0, bull_1.getQueueToken)('ai-batch'), useValue: batchQueue },
            ],
        }).compile();
        client = module.get(message_queue_client_1.MessageQueueClient);
        configService = module.get(config_1.ConfigService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('queueAnalysisRequest', () => {
        it('should queue analysis request successfully', async () => {
            const payload = {
                requestId: 'req-123',
                data: { event: 'suspicious_login' },
                model: 'threat-detector-v1',
            };
            const mockJob = createMockJob('job-123', payload);
            analysisQueue.add.mockResolvedValue(mockJob);
            const result = await client.queueAnalysisRequest(payload);
            expect(result).toMatchObject({
                jobId: 'job-123',
                requestId: 'req-123',
                queueName: 'ai-analysis',
                status: 'queued',
                priority: 0,
                delay: 0,
                timestamp: expect.any(Date),
            });
            expect(analysisQueue.add).toHaveBeenCalledWith('analyze-data', payload, expect.objectContaining({
                attempts: 3,
                backoff: expect.objectContaining({
                    type: 'exponential',
                    delay: 2000,
                }),
                removeOnComplete: 100,
                removeOnFail: 50,
            }));
        });
        it('should handle analysis request with custom options', async () => {
            const payload = {
                requestId: 'req-456',
                data: { event: 'test' },
            };
            const options = {
                priority: 5,
                delay: 1000,
                timeout: 60000,
                attempts: 5,
            };
            const mockJob = createMockJob('job-456', payload);
            analysisQueue.add.mockResolvedValue(mockJob);
            const result = await client.queueAnalysisRequest(payload, options);
            expect(result.priority).toBe(5);
            expect(result.delay).toBe(1000);
            expect(analysisQueue.add).toHaveBeenCalledWith('analyze-data', payload, expect.objectContaining({
                priority: 5,
                delay: 1000,
                timeout: 60000,
                attempts: 5,
            }));
        });
        it('should handle analysis request failure', async () => {
            const payload = {
                requestId: 'req-error',
                data: { event: 'test' },
            };
            analysisQueue.add.mockRejectedValue(new Error('Queue error'));
            await expect(client.queueAnalysisRequest(payload)).rejects.toThrow('Failed to queue analysis request');
        });
    });
    describe('queueTrainingRequest', () => {
        it('should queue training request successfully', async () => {
            const payload = {
                requestId: 'train-123',
                trainingData: [{ input: 'test', output: 'result' }],
                modelConfig: { type: 'classifier' },
            };
            const mockJob = createMockJob('job-train-123', payload);
            trainingQueue.add.mockResolvedValue(mockJob);
            const result = await client.queueTrainingRequest(payload);
            expect(result).toMatchObject({
                jobId: 'job-train-123',
                requestId: 'train-123',
                queueName: 'ai-training',
                status: 'queued',
                priority: 10, // Default higher priority for training
                timestamp: expect.any(Date),
            });
            expect(trainingQueue.add).toHaveBeenCalledWith('train-model', payload, expect.objectContaining({
                priority: 10,
                timeout: 3600000, // 1 hour timeout
            }));
        });
    });
    describe('queuePredictionRequest', () => {
        it('should queue prediction request successfully', async () => {
            const payload = {
                requestId: 'pred-123',
                input: { features: [1, 2, 3] },
                model: 'classifier-v2',
            };
            const mockJob = createMockJob('job-pred-123', payload);
            predictionQueue.add.mockResolvedValue(mockJob);
            const result = await client.queuePredictionRequest(payload);
            expect(result).toMatchObject({
                jobId: 'job-pred-123',
                requestId: 'pred-123',
                queueName: 'ai-prediction',
                status: 'queued',
                priority: 5, // Default higher priority for predictions
                timestamp: expect.any(Date),
            });
            expect(predictionQueue.add).toHaveBeenCalledWith('predict', payload, expect.objectContaining({
                priority: 5,
                timeout: 30000, // 30 seconds timeout
            }));
        });
    });
    describe('queueBatchRequest', () => {
        it('should queue batch request successfully', async () => {
            const payload = {
                requestId: 'batch-123',
                items: [
                    { id: '1', type: 'analysis', data: { event: 'test1' } },
                    { id: '2', type: 'prediction', data: { input: 'test2' } },
                ],
            };
            const mockJob = createMockJob('job-batch-123', payload);
            batchQueue.add.mockResolvedValue(mockJob);
            const result = await client.queueBatchRequest(payload);
            expect(result).toMatchObject({
                jobId: 'job-batch-123',
                requestId: 'batch-123',
                queueName: 'ai-batch',
                status: 'queued',
                priority: 1, // Default lower priority for batch
                timestamp: expect.any(Date),
            });
            expect(batchQueue.add).toHaveBeenCalledWith('process-batch', payload, expect.objectContaining({
                priority: 1,
                timeout: 1800000, // 30 minutes timeout
            }));
        });
    });
    describe('getJobStatus', () => {
        it('should get job status successfully', async () => {
            const jobId = 'job-123';
            const queueName = 'ai-analysis';
            const mockJob = createMockJob(jobId, { requestId: 'req-123' }, 'active');
            mockJob.progress.mockReturnValue(50);
            mockJob.processedOn = Date.now() - 1000;
            analysisQueue.getJob.mockResolvedValue(mockJob);
            analysisQueue.getJobLogs.mockResolvedValue({ logs: ['Processing started'] });
            const result = await client.getJobStatus(jobId, queueName);
            expect(result).toMatchObject({
                jobId,
                queueName,
                status: 'active',
                progress: 50,
                data: { requestId: 'req-123' },
                attempts: 0,
                maxAttempts: 3,
                createdAt: expect.any(Date),
                processedAt: expect.any(Date),
                logs: ['Processing started'],
                timestamp: expect.any(Date),
            });
        });
        it('should handle job not found', async () => {
            const jobId = 'nonexistent-job';
            const queueName = 'ai-analysis';
            analysisQueue.getJob.mockResolvedValue(null);
            const result = await client.getJobStatus(jobId, queueName);
            expect(result).toMatchObject({
                jobId,
                queueName,
                status: 'not_found',
                timestamp: expect.any(Date),
            });
        });
    });
    describe('cancelJob', () => {
        it('should cancel job successfully', async () => {
            const jobId = 'job-123';
            const queueName = 'ai-analysis';
            const mockJob = createMockJob(jobId, {}, 'waiting');
            analysisQueue.getJob.mockResolvedValue(mockJob);
            const result = await client.cancelJob(jobId, queueName);
            expect(result).toBe(true);
            expect(mockJob.remove).toHaveBeenCalled();
        });
        it('should handle job not found for cancellation', async () => {
            const jobId = 'nonexistent-job';
            const queueName = 'ai-analysis';
            analysisQueue.getJob.mockResolvedValue(null);
            const result = await client.cancelJob(jobId, queueName);
            expect(result).toBe(false);
        });
        it('should not cancel completed job', async () => {
            const jobId = 'job-123';
            const queueName = 'ai-analysis';
            const mockJob = createMockJob(jobId, {}, 'completed');
            analysisQueue.getJob.mockResolvedValue(mockJob);
            const result = await client.cancelJob(jobId, queueName);
            expect(result).toBe(false);
            expect(mockJob.remove).not.toHaveBeenCalled();
        });
    });
    describe('retryJob', () => {
        it('should retry failed job successfully', async () => {
            const jobId = 'job-123';
            const queueName = 'ai-analysis';
            const mockJob = createMockJob(jobId, {}, 'failed');
            analysisQueue.getJob.mockResolvedValue(mockJob);
            const result = await client.retryJob(jobId, queueName);
            expect(result).toBe(true);
            expect(mockJob.retry).toHaveBeenCalled();
        });
        it('should handle job not found for retry', async () => {
            const jobId = 'nonexistent-job';
            const queueName = 'ai-analysis';
            analysisQueue.getJob.mockResolvedValue(null);
            const result = await client.retryJob(jobId, queueName);
            expect(result).toBe(false);
        });
        it('should not retry non-failed job', async () => {
            const jobId = 'job-123';
            const queueName = 'ai-analysis';
            const mockJob = createMockJob(jobId, {}, 'active');
            analysisQueue.getJob.mockResolvedValue(mockJob);
            const result = await client.retryJob(jobId, queueName);
            expect(result).toBe(false);
            expect(mockJob.retry).not.toHaveBeenCalled();
        });
    });
    describe('getQueueStats', () => {
        it('should get queue statistics successfully', async () => {
            const queueName = 'ai-analysis';
            analysisQueue.getWaiting.mockResolvedValue([{}, {}]); // 2 waiting
            analysisQueue.getActive.mockResolvedValue([{}]); // 1 active
            analysisQueue.getCompleted.mockResolvedValue([{}, {}, {}]); // 3 completed
            analysisQueue.getFailed.mockResolvedValue([{}]); // 1 failed
            analysisQueue.getDelayed.mockResolvedValue([]); // 0 delayed
            analysisQueue.getPaused.mockResolvedValue([]); // 0 paused
            analysisQueue.isPaused.mockResolvedValue(false);
            const result = await client.getQueueStats(queueName);
            expect(result).toMatchObject({
                queueName,
                counts: {
                    waiting: 2,
                    active: 1,
                    completed: 3,
                    failed: 1,
                    delayed: 0,
                    paused: 0,
                },
                isPaused: false,
                timestamp: expect.any(Date),
            });
        });
    });
    describe('getJobsByStatus', () => {
        it('should get waiting jobs successfully', async () => {
            const queueName = 'ai-analysis';
            const status = 'waiting';
            const mockJobs = [
                createMockJob('job-1', { requestId: 'req-1' }, 'waiting'),
                createMockJob('job-2', { requestId: 'req-2' }, 'waiting'),
            ];
            analysisQueue.getWaiting.mockResolvedValue(mockJobs);
            const result = await client.getJobsByStatus(queueName, status, 0, 10);
            expect(result).toHaveLength(2);
            expect(result[0]).toMatchObject({
                jobId: 'job-1',
                queueName,
                status: 'waiting',
                data: { requestId: 'req-1' },
                progress: 0,
                attempts: 0,
                maxAttempts: 3,
                createdAt: expect.any(Date),
            });
        });
        it('should handle invalid job status', async () => {
            const queueName = 'ai-analysis';
            const status = 'invalid';
            await expect(client.getJobsByStatus(queueName, status)).rejects.toThrow('Invalid job status');
        });
    });
    describe('cleanQueue', () => {
        it('should clean completed jobs successfully', async () => {
            const queueName = 'ai-analysis';
            const grace = 24 * 60 * 60 * 1000; // 24 hours
            analysisQueue.clean.mockResolvedValue([{}, {}, {}]); // 3 cleaned jobs
            const result = await client.cleanQueue(queueName, grace, 'completed');
            expect(result).toBe(3);
            expect(analysisQueue.clean).toHaveBeenCalledWith(grace, 'completed');
        });
    });
    describe('pauseQueue', () => {
        it('should pause queue successfully', async () => {
            const queueName = 'ai-analysis';
            await client.pauseQueue(queueName);
            expect(analysisQueue.pause).toHaveBeenCalled();
        });
    });
    describe('resumeQueue', () => {
        it('should resume queue successfully', async () => {
            const queueName = 'ai-analysis';
            await client.resumeQueue(queueName);
            expect(analysisQueue.resume).toHaveBeenCalled();
        });
    });
    describe('scheduleJob', () => {
        it('should schedule job successfully', async () => {
            const queueName = 'ai-analysis';
            const jobType = 'analyze-data';
            const payload = { requestId: 'scheduled-req', data: { event: 'test' } };
            const scheduleTime = new Date(Date.now() + 60000); // 1 minute from now
            const mockJob = createMockJob('scheduled-job', payload);
            analysisQueue.add.mockResolvedValue(mockJob);
            const result = await client.scheduleJob(queueName, jobType, payload, scheduleTime);
            expect(result).toMatchObject({
                jobId: 'scheduled-job',
                requestId: 'scheduled-req',
                queueName,
                status: 'delayed',
                delay: expect.any(Number),
                timestamp: expect.any(Date),
            });
            expect(analysisQueue.add).toHaveBeenCalledWith(jobType, payload, expect.objectContaining({
                delay: expect.any(Number),
            }));
        });
        it('should handle past schedule time', async () => {
            const queueName = 'ai-analysis';
            const jobType = 'analyze-data';
            const payload = { requestId: 'past-req', data: { event: 'test' } };
            const scheduleTime = new Date(Date.now() - 60000); // 1 minute ago
            await expect(client.scheduleJob(queueName, jobType, payload, scheduleTime)).rejects.toThrow('Schedule time must be in the future');
        });
    });
    describe('queue name validation', () => {
        it('should handle unknown queue name', async () => {
            const unknownQueue = 'unknown-queue';
            await expect(client.getQueueStats(unknownQueue)).rejects.toThrow('Unknown queue name');
        });
    });
    describe('configuration', () => {
        it('should use configured queue options', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                const config = {
                    'ai.queue.attempts': 5,
                    'ai.queue.backoffDelay': 3000,
                    'ai.queue.removeOnComplete': 200,
                    'ai.queue.removeOnFail': 100,
                };
                return config[key] || defaultValue;
            });
            // Create new instance to pick up new config
            const newClient = new message_queue_client_1.MessageQueueClient(analysisQueue, trainingQueue, predictionQueue, batchQueue, configService);
            expect(newClient.defaultJobOptions).toMatchObject({
                attempts: 5,
                backoff: expect.objectContaining({
                    delay: 3000,
                }),
                removeOnComplete: 200,
                removeOnFail: 100,
            });
        });
    });
    describe('error handling', () => {
        it('should handle queue operation errors', async () => {
            const jobId = 'error-job';
            const queueName = 'ai-analysis';
            analysisQueue.getJob.mockRejectedValue(new Error('Redis connection error'));
            await expect(client.getJobStatus(jobId, queueName)).rejects.toThrow('Failed to get job status');
        });
    });
    describe('ID generation', () => {
        it('should generate unique request IDs', () => {
            const id1 = client.generateRequestId();
            const id2 = client.generateRequestId();
            expect(id1).toBeDefined();
            expect(id2).toBeDefined();
            expect(id1).not.toBe(id2);
            expect(id1).toMatch(/^mq-req-/);
            expect(id2).toMatch(/^mq-req-/);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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