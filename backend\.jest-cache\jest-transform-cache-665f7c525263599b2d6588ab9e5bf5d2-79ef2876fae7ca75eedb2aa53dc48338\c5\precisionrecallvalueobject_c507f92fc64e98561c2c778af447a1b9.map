{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\value-objects\\precision-recall.value-object.ts", "mappings": ";;;AAAA,iGAA4F;AAe5F,MAAa,eAAgB,SAAQ,mCAAqC;IAIxE,YAAY,SAAiB,EAAE,MAAc;QAC3C,KAAK,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAC;IAC/B,CAAC;IAES,QAAQ;QAChB,KAAK,CAAC,QAAQ,EAAE,CAAC;QAEjB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,KAAK,QAAQ,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YACxF,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;YAC9D,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;YACtE,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,eAAe,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,eAAe,CAAC,SAAS,EAAE,CAAC;YAC3G,MAAM,IAAI,KAAK,CAAC,6BAA6B,eAAe,CAAC,SAAS,QAAQ,eAAe,CAAC,SAAS,EAAE,CAAC,CAAC;QAC7G,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,eAAe,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,eAAe,CAAC,SAAS,EAAE,CAAC;YACrG,MAAM,IAAI,KAAK,CAAC,0BAA0B,eAAe,CAAC,SAAS,QAAQ,eAAe,CAAC,SAAS,EAAE,CAAC,CAAC;QAC1G,CAAC;IACH,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,mBAAmB,CAC/B,aAAqB,EACrB,cAAsB,EACtB,cAAsB;QAEtB,IAAI,aAAa,GAAG,CAAC,IAAI,cAAc,GAAG,CAAC,IAAI,cAAc,GAAG,CAAC,EAAE,CAAC;YAClE,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;QAED,MAAM,SAAS,GAAG,CAAC,aAAa,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,CAAC,aAAa,GAAG,cAAc,CAAC,CAAC;QAChH,MAAM,MAAM,GAAG,CAAC,aAAa,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,CAAC,aAAa,GAAG,cAAc,CAAC,CAAC;QAE7G,OAAO,IAAI,eAAe,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,eAAe,CAAC,gBAAwB,EAAE,aAAqB;QAC3E,IAAI,gBAAgB,GAAG,CAAC,IAAI,gBAAgB,GAAG,GAAG,IAAI,aAAa,GAAG,CAAC,IAAI,aAAa,GAAG,GAAG,EAAE,CAAC;YAC/F,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC3D,CAAC;QACD,OAAO,IAAI,eAAe,CAAC,gBAAgB,GAAG,GAAG,EAAE,aAAa,GAAG,GAAG,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,OAAO;QACnB,OAAO,IAAI,eAAe,CAAC,eAAe,CAAC,SAAS,EAAE,eAAe,CAAC,SAAS,CAAC,CAAC;IACnF,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,IAAI;QAChB,OAAO,IAAI,eAAe,CAAC,eAAe,CAAC,SAAS,EAAE,eAAe,CAAC,SAAS,CAAC,CAAC;IACnF,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC5B,CAAC;IAED;;OAEG;IACI,UAAU;QACf,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrD,OAAO,CAAC,CAAC;QACX,CAAC;QACD,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACzG,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,IAAY;QAC/B,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACjD,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,GAAG,IAAI,CAAC;QAChC,MAAM,WAAW,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAE/E,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;YACtB,OAAO,CAAC,CAAC;QACX,CAAC;QAED,OAAO,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,CAAC;IACxF,CAAC;IAED;;OAEG;IACI,UAAU;QACf,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,WAAW;QAChB,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,eAAe;QACpB,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,iBAAiB;QACtB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACI,gBAAgB;QACrB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACI,iBAAiB;QACtB,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IACpD,CAAC;IAED;;OAEG;IACI,cAAc;QACnB,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;IACpD,CAAC;IAED;;OAEG;IACI,UAAU,CAAC,YAAoB,IAAI;QACxC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC;IAC3E,CAAC;IAED;;OAEG;IACI,gBAAgB;QACrB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;QACpD,CAAC;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IACpD,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,kBAA0B,EAAE,eAAuB;QAC5E,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,eAAe,CAAC;IAC9F,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,kBAA0B,EAAE,eAAuB;QAC7E,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,eAAe,CAAC;IAC9F,CAAC;IAED;;OAEG;IACI,sBAAsB;QAC3B,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,mBAAmB;QACxB,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC;IAClC,CAAC;IAED;;OAEG;IACI,4BAA4B,CAAC,WAAmB,CAAC;QACtD,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC;IAC/D,CAAC;IAED;;OAEG;IACI,yBAAyB,CAAC,WAAmB,CAAC;QACnD,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC;IAC5D,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,KAAsB,EAAE,SAAiB,GAAG;QAC7D,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;QACjG,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;QAExF,OAAO,IAAI,eAAe,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,KAAsB;QACxC,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC;QACrE,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;QAC5D,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,aAAa,GAAG,UAAU,GAAG,UAAU,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACI,qBAAqB,CAAC,KAAsB;QACjD,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC;YACxD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,KAAsB;QACxC,OAAO,IAAI,CAAC,UAAU,EAAE,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;IAChD,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,WAAmB,CAAC;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QACtC,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC;QAC7E,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC;QACvE,OAAO,IAAI,eAAe,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,OAAO,cAAc,IAAI,CAAC,4BAA4B,EAAE,aAAa,IAAI,CAAC,yBAAyB,EAAE,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACxJ,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;YAChC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;YAC1B,mBAAmB,EAAE,IAAI,CAAC,sBAAsB,EAAE;YAClD,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC5C,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;YAC1B,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;YAC1B,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE;YAC5B,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE;YACpC,cAAc,EAAE,IAAI,CAAC,iBAAiB,EAAE;YACxC,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE;YACtC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE;YAC7B,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE;SACvC,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,QAAQ,CAAC,IAAyB;QAC9C,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,OAAO,CAAC,KAAwB;QAC5C,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC;QAED,MAAM,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;QACjF,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAE3E,OAAO,IAAI,eAAe,CAAC,YAAY,GAAG,KAAK,CAAC,MAAM,EAAE,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;IACpF,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,eAAe,CAAC,KAAwB,EAAE,OAAiB;QACvE,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QACxE,CAAC;QAED,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,EAAE,CAAC,CAAC,CAAC;QACrE,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACjD,CAAC;QAED,MAAM,iBAAiB,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9G,MAAM,cAAc,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;QAExG,OAAO,IAAI,eAAe,CAAC,iBAAiB,GAAG,WAAW,EAAE,cAAc,GAAG,WAAW,CAAC,CAAC;IAC5F,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,MAAM,CAAC,KAAwB;QAC3C,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAED,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE,CACpC,OAAO,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAC1D,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,aAAa,CAAC,KAAwB;QAClD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE,CACpC,OAAO,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAClE,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,UAAU,CAAC,KAAwB;QAC/C,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QAED,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE,CACpC,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAC5D,CAAC;IACJ,CAAC;;AA9XH,0CA+XC;AA9XwB,yBAAS,GAAG,GAAG,CAAC;AAChB,yBAAS,GAAG,GAAG,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\value-objects\\precision-recall.value-object.ts"], "sourcesContent": ["import { BaseValueObject } from '../../../../shared-kernel/value-objects/base-value-object';\r\n\r\n/**\r\n * Precision Recall Value Object\r\n * \r\n * Represents precision and recall metrics for AI model performance evaluation.\r\n * Provides utility methods for calculating F1 score, F-beta score, and\r\n * analyzing the precision-recall trade-off.\r\n */\r\n\r\nexport interface PrecisionRecallProps {\r\n  precision: number;\r\n  recall: number;\r\n}\r\n\r\nexport class PrecisionRecall extends BaseValueObject<PrecisionRecallProps> {\r\n  public static readonly MIN_VALUE = 0.0;\r\n  public static readonly MAX_VALUE = 1.0;\r\n\r\n  constructor(precision: number, recall: number) {\r\n    super({ precision, recall });\r\n  }\r\n\r\n  protected validate(): void {\r\n    super.validate();\r\n    \r\n    if (typeof this._value.precision !== 'number' || typeof this._value.recall !== 'number') {\r\n      throw new Error('Precision and recall must be numbers');\r\n    }\r\n\r\n    if (isNaN(this._value.precision) || isNaN(this._value.recall)) {\r\n      throw new Error('Precision and recall cannot be NaN');\r\n    }\r\n\r\n    if (!isFinite(this._value.precision) || !isFinite(this._value.recall)) {\r\n      throw new Error('Precision and recall must be finite');\r\n    }\r\n\r\n    if (this._value.precision < PrecisionRecall.MIN_VALUE || this._value.precision > PrecisionRecall.MAX_VALUE) {\r\n      throw new Error(`Precision must be between ${PrecisionRecall.MIN_VALUE} and ${PrecisionRecall.MAX_VALUE}`);\r\n    }\r\n\r\n    if (this._value.recall < PrecisionRecall.MIN_VALUE || this._value.recall > PrecisionRecall.MAX_VALUE) {\r\n      throw new Error(`Recall must be between ${PrecisionRecall.MIN_VALUE} and ${PrecisionRecall.MAX_VALUE}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Creates precision-recall from confusion matrix values\r\n   */\r\n  public static fromConfusionMatrix(\r\n    truePositives: number,\r\n    falsePositives: number,\r\n    falseNegatives: number\r\n  ): PrecisionRecall {\r\n    if (truePositives < 0 || falsePositives < 0 || falseNegatives < 0) {\r\n      throw new Error('Confusion matrix values cannot be negative');\r\n    }\r\n\r\n    const precision = (truePositives + falsePositives) === 0 ? 0 : truePositives / (truePositives + falsePositives);\r\n    const recall = (truePositives + falseNegatives) === 0 ? 0 : truePositives / (truePositives + falseNegatives);\r\n\r\n    return new PrecisionRecall(precision, recall);\r\n  }\r\n\r\n  /**\r\n   * Creates precision-recall from percentages (0-100)\r\n   */\r\n  public static fromPercentages(precisionPercent: number, recallPercent: number): PrecisionRecall {\r\n    if (precisionPercent < 0 || precisionPercent > 100 || recallPercent < 0 || recallPercent > 100) {\r\n      throw new Error('Percentages must be between 0 and 100');\r\n    }\r\n    return new PrecisionRecall(precisionPercent / 100, recallPercent / 100);\r\n  }\r\n\r\n  /**\r\n   * Creates perfect precision-recall (1.0, 1.0)\r\n   */\r\n  public static perfect(): PrecisionRecall {\r\n    return new PrecisionRecall(PrecisionRecall.MAX_VALUE, PrecisionRecall.MAX_VALUE);\r\n  }\r\n\r\n  /**\r\n   * Creates zero precision-recall (0.0, 0.0)\r\n   */\r\n  public static zero(): PrecisionRecall {\r\n    return new PrecisionRecall(PrecisionRecall.MIN_VALUE, PrecisionRecall.MIN_VALUE);\r\n  }\r\n\r\n  /**\r\n   * Gets the precision value\r\n   */\r\n  get precision(): number {\r\n    return this._value.precision;\r\n  }\r\n\r\n  /**\r\n   * Gets the recall value\r\n   */\r\n  get recall(): number {\r\n    return this._value.recall;\r\n  }\r\n\r\n  /**\r\n   * Calculates the F1 score (harmonic mean of precision and recall)\r\n   */\r\n  public getF1Score(): number {\r\n    if (this._value.precision + this._value.recall === 0) {\r\n      return 0;\r\n    }\r\n    return (2 * this._value.precision * this._value.recall) / (this._value.precision + this._value.recall);\r\n  }\r\n\r\n  /**\r\n   * Calculates the F-beta score with custom beta parameter\r\n   * Beta > 1 gives more weight to recall\r\n   * Beta < 1 gives more weight to precision\r\n   */\r\n  public getFBetaScore(beta: number): number {\r\n    if (beta <= 0) {\r\n      throw new Error('Beta must be greater than 0');\r\n    }\r\n\r\n    const betaSquared = beta * beta;\r\n    const denominator = (betaSquared * this._value.precision) + this._value.recall;\r\n    \r\n    if (denominator === 0) {\r\n      return 0;\r\n    }\r\n    \r\n    return ((1 + betaSquared) * this._value.precision * this._value.recall) / denominator;\r\n  }\r\n\r\n  /**\r\n   * Calculates the F2 score (gives more weight to recall)\r\n   */\r\n  public getF2Score(): number {\r\n    return this.getFBetaScore(2);\r\n  }\r\n\r\n  /**\r\n   * Calculates the F0.5 score (gives more weight to precision)\r\n   */\r\n  public getF05Score(): number {\r\n    return this.getFBetaScore(0.5);\r\n  }\r\n\r\n  /**\r\n   * Gets the harmonic mean of precision and recall\r\n   */\r\n  public getHarmonicMean(): number {\r\n    return this.getF1Score();\r\n  }\r\n\r\n  /**\r\n   * Gets the arithmetic mean of precision and recall\r\n   */\r\n  public getArithmeticMean(): number {\r\n    return (this._value.precision + this._value.recall) / 2;\r\n  }\r\n\r\n  /**\r\n   * Gets the geometric mean of precision and recall\r\n   */\r\n  public getGeometricMean(): number {\r\n    return Math.sqrt(this._value.precision * this._value.recall);\r\n  }\r\n\r\n  /**\r\n   * Checks if precision is higher than recall\r\n   */\r\n  public isPrecisionHigher(): boolean {\r\n    return this._value.precision > this._value.recall;\r\n  }\r\n\r\n  /**\r\n   * Checks if recall is higher than precision\r\n   */\r\n  public isRecallHigher(): boolean {\r\n    return this._value.recall > this._value.precision;\r\n  }\r\n\r\n  /**\r\n   * Checks if precision and recall are balanced (within threshold)\r\n   */\r\n  public isBalanced(threshold: number = 0.05): boolean {\r\n    return Math.abs(this._value.precision - this._value.recall) <= threshold;\r\n  }\r\n\r\n  /**\r\n   * Gets the precision-recall trade-off ratio\r\n   */\r\n  public getTradeOffRatio(): number {\r\n    if (this._value.recall === 0) {\r\n      return this._value.precision === 0 ? 1 : Infinity;\r\n    }\r\n    return this._value.precision / this._value.recall;\r\n  }\r\n\r\n  /**\r\n   * Checks if both precision and recall meet minimum thresholds\r\n   */\r\n  public meetsBothThresholds(precisionThreshold: number, recallThreshold: number): boolean {\r\n    return this._value.precision >= precisionThreshold && this._value.recall >= recallThreshold;\r\n  }\r\n\r\n  /**\r\n   * Checks if either precision or recall meets the threshold\r\n   */\r\n  public meetsEitherThreshold(precisionThreshold: number, recallThreshold: number): boolean {\r\n    return this._value.precision >= precisionThreshold || this._value.recall >= recallThreshold;\r\n  }\r\n\r\n  /**\r\n   * Gets precision as a percentage\r\n   */\r\n  public getPrecisionPercentage(): number {\r\n    return this._value.precision * 100;\r\n  }\r\n\r\n  /**\r\n   * Gets recall as a percentage\r\n   */\r\n  public getRecallPercentage(): number {\r\n    return this._value.recall * 100;\r\n  }\r\n\r\n  /**\r\n   * Gets precision as a percentage string\r\n   */\r\n  public getPrecisionPercentageString(decimals: number = 1): string {\r\n    return `${(this._value.precision * 100).toFixed(decimals)}%`;\r\n  }\r\n\r\n  /**\r\n   * Gets recall as a percentage string\r\n   */\r\n  public getRecallPercentageString(decimals: number = 1): string {\r\n    return `${(this._value.recall * 100).toFixed(decimals)}%`;\r\n  }\r\n\r\n  /**\r\n   * Combines this precision-recall with another using weighted average\r\n   */\r\n  public combineWith(other: PrecisionRecall, weight: number = 0.5): PrecisionRecall {\r\n    if (weight < 0 || weight > 1) {\r\n      throw new Error('Weight must be between 0 and 1');\r\n    }\r\n    \r\n    const combinedPrecision = this._value.precision * weight + other._value.precision * (1 - weight);\r\n    const combinedRecall = this._value.recall * weight + other._value.recall * (1 - weight);\r\n    \r\n    return new PrecisionRecall(combinedPrecision, combinedRecall);\r\n  }\r\n\r\n  /**\r\n   * Gets the Euclidean distance from another precision-recall point\r\n   */\r\n  public distanceFrom(other: PrecisionRecall): number {\r\n    const precisionDiff = this._value.precision - other._value.precision;\r\n    const recallDiff = this._value.recall - other._value.recall;\r\n    return Math.sqrt(precisionDiff * precisionDiff + recallDiff * recallDiff);\r\n  }\r\n\r\n  /**\r\n   * Gets the Manhattan distance from another precision-recall point\r\n   */\r\n  public manhattanDistanceFrom(other: PrecisionRecall): number {\r\n    return Math.abs(this._value.precision - other._value.precision) + \r\n           Math.abs(this._value.recall - other._value.recall);\r\n  }\r\n\r\n  /**\r\n   * Checks if this precision-recall is better than another based on F1 score\r\n   */\r\n  public isBetterThan(other: PrecisionRecall): boolean {\r\n    return this.getF1Score() > other.getF1Score();\r\n  }\r\n\r\n  /**\r\n   * Rounds precision and recall to specified decimal places\r\n   */\r\n  public round(decimals: number = 3): PrecisionRecall {\r\n    const factor = Math.pow(10, decimals);\r\n    const roundedPrecision = Math.round(this._value.precision * factor) / factor;\r\n    const roundedRecall = Math.round(this._value.recall * factor) / factor;\r\n    return new PrecisionRecall(roundedPrecision, roundedRecall);\r\n  }\r\n\r\n  /**\r\n   * Converts to a human-readable string\r\n   */\r\n  public toString(): string {\r\n    return `Precision: ${this.getPrecisionPercentageString()}, Recall: ${this.getRecallPercentageString()}, F1: ${(this.getF1Score() * 100).toFixed(1)}%`;\r\n  }\r\n\r\n  /**\r\n   * Converts to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      precision: this._value.precision,\r\n      recall: this._value.recall,\r\n      precisionPercentage: this.getPrecisionPercentage(),\r\n      recallPercentage: this.getRecallPercentage(),\r\n      f1Score: this.getF1Score(),\r\n      f2Score: this.getF2Score(),\r\n      f05Score: this.getF05Score(),\r\n      harmonicMean: this.getHarmonicMean(),\r\n      arithmeticMean: this.getArithmeticMean(),\r\n      geometricMean: this.getGeometricMean(),\r\n      isBalanced: this.isBalanced(),\r\n      tradeOffRatio: this.getTradeOffRatio(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Creates a PrecisionRecall from JSON\r\n   */\r\n  public static fromJSON(json: Record<string, any>): PrecisionRecall {\r\n    return new PrecisionRecall(json.precision, json.recall);\r\n  }\r\n\r\n  /**\r\n   * Calculates the average of multiple precision-recall pairs\r\n   */\r\n  public static average(pairs: PrecisionRecall[]): PrecisionRecall {\r\n    if (pairs.length === 0) {\r\n      throw new Error('Cannot calculate average of empty array');\r\n    }\r\n    \r\n    const sumPrecision = pairs.reduce((acc, pair) => acc + pair._value.precision, 0);\r\n    const sumRecall = pairs.reduce((acc, pair) => acc + pair._value.recall, 0);\r\n    \r\n    return new PrecisionRecall(sumPrecision / pairs.length, sumRecall / pairs.length);\r\n  }\r\n\r\n  /**\r\n   * Calculates the weighted average of multiple precision-recall pairs\r\n   */\r\n  public static weightedAverage(pairs: PrecisionRecall[], weights: number[]): PrecisionRecall {\r\n    if (pairs.length === 0) {\r\n      throw new Error('Cannot calculate weighted average of empty array');\r\n    }\r\n    \r\n    if (pairs.length !== weights.length) {\r\n      throw new Error('Pairs and weights arrays must have the same length');\r\n    }\r\n    \r\n    const totalWeight = weights.reduce((acc, weight) => acc + weight, 0);\r\n    if (totalWeight === 0) {\r\n      throw new Error('Total weight cannot be zero');\r\n    }\r\n    \r\n    const weightedPrecision = pairs.reduce((acc, pair, index) => acc + pair._value.precision * weights[index], 0);\r\n    const weightedRecall = pairs.reduce((acc, pair, index) => acc + pair._value.recall * weights[index], 0);\r\n    \r\n    return new PrecisionRecall(weightedPrecision / totalWeight, weightedRecall / totalWeight);\r\n  }\r\n\r\n  /**\r\n   * Finds the pair with the highest F1 score\r\n   */\r\n  public static bestF1(pairs: PrecisionRecall[]): PrecisionRecall {\r\n    if (pairs.length === 0) {\r\n      throw new Error('Cannot find best F1 of empty array');\r\n    }\r\n    \r\n    return pairs.reduce((best, current) => \r\n      current.getF1Score() > best.getF1Score() ? current : best\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Finds the pair with the highest precision\r\n   */\r\n  public static bestPrecision(pairs: PrecisionRecall[]): PrecisionRecall {\r\n    if (pairs.length === 0) {\r\n      throw new Error('Cannot find best precision of empty array');\r\n    }\r\n    \r\n    return pairs.reduce((best, current) => \r\n      current._value.precision > best._value.precision ? current : best\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Finds the pair with the highest recall\r\n   */\r\n  public static bestRecall(pairs: PrecisionRecall[]): PrecisionRecall {\r\n    if (pairs.length === 0) {\r\n      throw new Error('Cannot find best recall of empty array');\r\n    }\r\n    \r\n    return pairs.reduce((best, current) => \r\n      current._value.recall > best._value.recall ? current : best\r\n    );\r\n  }\r\n}"], "version": 3}