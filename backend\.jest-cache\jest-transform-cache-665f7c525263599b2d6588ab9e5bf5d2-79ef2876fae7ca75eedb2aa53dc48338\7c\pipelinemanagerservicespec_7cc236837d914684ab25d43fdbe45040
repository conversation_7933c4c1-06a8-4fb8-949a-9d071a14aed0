6c916704b73d57712d133999e2ebba08
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const cqrs_1 = require("@nestjs/cqrs");
const bull_1 = require("@nestjs/bull");
const pipeline_manager_service_1 = require("../pipeline-manager.service");
describe('PipelineManagerService', () => {
    let service;
    let mockAiRequestQueue;
    let mockAiResponseQueue;
    let mockTrainingQueue;
    let mockEventBus;
    let mockConfigService;
    const mockPipelineDefinition = {
        name: 'Test Pipeline',
        description: 'Test pipeline for unit tests',
        stages: [
            {
                id: 'stage-1',
                name: 'Data Preprocessing',
                type: 'data-preprocessing',
                config: { normalize: true },
            },
            {
                id: 'stage-2',
                name: 'AI Analysis',
                type: 'ai-analysis',
                dependencies: ['stage-1'],
                config: { model: 'test-model' },
            },
        ],
        executionStrategy: 'sequential',
        context: { testData: 'test' },
    };
    beforeEach(async () => {
        // Create mocks
        mockAiRequestQueue = {
            add: jest.fn(),
            process: jest.fn(),
        };
        mockAiResponseQueue = {
            add: jest.fn(),
            process: jest.fn(),
        };
        mockTrainingQueue = {
            add: jest.fn(),
            process: jest.fn(),
        };
        mockEventBus = {
            publish: jest.fn(),
        };
        mockConfigService = {
            get: jest.fn(),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                pipeline_manager_service_1.PipelineManagerService,
                {
                    provide: (0, bull_1.getQueueToken)('ai-request'),
                    useValue: mockAiRequestQueue,
                },
                {
                    provide: (0, bull_1.getQueueToken)('ai-response'),
                    useValue: mockAiResponseQueue,
                },
                {
                    provide: (0, bull_1.getQueueToken)('training-job'),
                    useValue: mockTrainingQueue,
                },
                {
                    provide: cqrs_1.EventBus,
                    useValue: mockEventBus,
                },
                {
                    provide: config_1.ConfigService,
                    useValue: mockConfigService,
                },
            ],
        }).compile();
        service = module.get(pipeline_manager_service_1.PipelineManagerService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('createPipeline', () => {
        it('should create and execute pipeline successfully', async () => {
            // Arrange
            const mockJob = {
                finished: jest.fn().mockResolvedValue({ result: 'success' }),
            };
            mockAiRequestQueue.add.mockResolvedValue(mockJob);
            // Act
            const execution = await service.createPipeline(mockPipelineDefinition);
            // Assert
            expect(execution).toBeDefined();
            expect(execution.id).toMatch(/^pipeline-\d+-[a-z0-9]+$/);
            expect(execution.definition).toEqual(mockPipelineDefinition);
            expect(execution.stages).toHaveLength(2);
            expect(execution.status).toBe('completed');
        });
        it('should validate pipeline definition', async () => {
            // Arrange
            const invalidDefinition = {
                ...mockPipelineDefinition,
                stages: [], // Empty stages
            };
            // Act & Assert
            await expect(service.createPipeline(invalidDefinition))
                .rejects.toThrow('Pipeline must have at least one stage');
        });
        it('should validate stage dependencies', async () => {
            // Arrange
            const invalidDefinition = {
                ...mockPipelineDefinition,
                stages: [
                    {
                        id: 'stage-1',
                        name: 'Test Stage',
                        type: 'ai-analysis',
                        dependencies: ['non-existent-stage'], // Invalid dependency
                    },
                ],
            };
            // Act & Assert
            await expect(service.createPipeline(invalidDefinition))
                .rejects.toThrow('Invalid dependency: non-existent-stage for stage: stage-1');
        });
        it('should handle pipeline execution failures', async () => {
            // Arrange
            const mockJob = {
                finished: jest.fn().mockRejectedValue(new Error('Job failed')),
            };
            mockAiRequestQueue.add.mockResolvedValue(mockJob);
            // Act & Assert
            await expect(service.createPipeline(mockPipelineDefinition))
                .rejects.toThrow('Pipeline creation failed');
        });
    });
    describe('executePipelineFromTemplate', () => {
        beforeEach(() => {
            // Register a test template
            service.registerPipelineTemplate('test-template', {
                name: 'Test Template',
                description: 'Template for testing',
                stages: [
                    {
                        id: 'template-stage',
                        name: 'Template Stage',
                        type: 'ai-analysis',
                        config: { model: 'template-model' },
                    },
                ],
                executionStrategy: 'sequential',
            });
        });
        it('should execute pipeline from template successfully', async () => {
            // Arrange
            const parameters = { testParam: 'value' };
            const mockJob = {
                finished: jest.fn().mockResolvedValue({ result: 'success' }),
            };
            mockAiRequestQueue.add.mockResolvedValue(mockJob);
            // Act
            const execution = await service.executePipelineFromTemplate('test-template', parameters);
            // Assert
            expect(execution).toBeDefined();
            expect(execution.definition.name).toBe('Test Template');
            expect(execution.context).toEqual(parameters);
        });
        it('should throw error for non-existent template', async () => {
            // Act & Assert
            await expect(service.executePipelineFromTemplate('non-existent', {}))
                .rejects.toThrow('Pipeline template not found: non-existent');
        });
    });
    describe('getPipelineStatus', () => {
        it('should return pipeline status correctly', async () => {
            // Arrange
            const mockJob = {
                finished: jest.fn().mockResolvedValue({ result: 'success' }),
            };
            mockAiRequestQueue.add.mockResolvedValue(mockJob);
            const execution = await service.createPipeline(mockPipelineDefinition);
            // Act
            const status = await service.getPipelineStatus(execution.id);
            // Assert
            expect(status).toEqual({
                id: execution.id,
                status: 'completed',
                progress: 1, // 100% complete
                currentStage: null, // No stage currently running
                metrics: expect.objectContaining({
                    startTime: expect.any(Date),
                    endTime: expect.any(Date),
                    totalStages: 2,
                    completedStages: 2,
                    failedStages: 0,
                }),
                results: expect.any(Object),
                errors: [],
            });
        });
        it('should throw error for non-existent pipeline', async () => {
            // Act & Assert
            await expect(service.getPipelineStatus('non-existent'))
                .rejects.toThrow('Pipeline not found: non-existent');
        });
    });
    describe('cancelPipeline', () => {
        it('should cancel running pipeline successfully', async () => {
            // Arrange
            const mockJob = {
                finished: jest.fn().mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000)) // Long running job
                ),
            };
            mockAiRequestQueue.add.mockResolvedValue(mockJob);
            // Start pipeline but don't wait for completion
            const executionPromise = service.createPipeline(mockPipelineDefinition);
            // Wait a bit to ensure pipeline starts
            await new Promise(resolve => setTimeout(resolve, 100));
            // Act
            const execution = await executionPromise;
            await service.cancelPipeline(execution.id);
            // Assert
            const status = await service.getPipelineStatus(execution.id);
            expect(status.status).toBe('cancelled');
        });
        it('should throw error for non-existent pipeline', async () => {
            // Act & Assert
            await expect(service.cancelPipeline('non-existent'))
                .rejects.toThrow('Pipeline not found: non-existent');
        });
    });
    describe('retryStage', () => {
        it('should retry failed stage successfully', async () => {
            // Arrange
            const failingJob = {
                finished: jest.fn().mockRejectedValue(new Error('Stage failed')),
            };
            const successJob = {
                finished: jest.fn().mockResolvedValue({ result: 'success' }),
            };
            mockAiRequestQueue.add
                .mockResolvedValueOnce(failingJob)
                .mockResolvedValueOnce(successJob);
            // Create pipeline that will fail
            let execution;
            try {
                execution = await service.createPipeline(mockPipelineDefinition);
            }
            catch (error) {
                // Pipeline should fail, but we can still get the execution
                const activePipelines = await service.getActivePipelines();
                execution = activePipelines[0];
            }
            // Act
            await service.retryStage(execution.id, 'stage-2');
            // Assert
            const status = await service.getPipelineStatus(execution.id);
            expect(status.status).toBe('completed');
        });
        it('should throw error for non-failed stage', async () => {
            // Arrange
            const mockJob = {
                finished: jest.fn().mockResolvedValue({ result: 'success' }),
            };
            mockAiRequestQueue.add.mockResolvedValue(mockJob);
            const execution = await service.createPipeline(mockPipelineDefinition);
            // Act & Assert
            await expect(service.retryStage(execution.id, 'stage-1'))
                .rejects.toThrow('Stage is not in failed state: stage-1');
        });
    });
    describe('getActivePipelines', () => {
        it('should return all active pipelines', async () => {
            // Arrange
            const mockJob = {
                finished: jest.fn().mockResolvedValue({ result: 'success' }),
            };
            mockAiRequestQueue.add.mockResolvedValue(mockJob);
            const execution1 = await service.createPipeline(mockPipelineDefinition);
            const execution2 = await service.createPipeline({
                ...mockPipelineDefinition,
                name: 'Second Pipeline',
            });
            // Act
            const activePipelines = await service.getActivePipelines();
            // Assert
            expect(activePipelines).toHaveLength(2);
            expect(activePipelines.map(p => p.id)).toContain(execution1.id);
            expect(activePipelines.map(p => p.id)).toContain(execution2.id);
        });
    });
    describe('stage execution', () => {
        it('should execute AI analysis stage', async () => {
            // Arrange
            const mockJob = {
                finished: jest.fn().mockResolvedValue({
                    analysis: 'threat detected',
                    confidence: 0.95
                }),
            };
            mockAiRequestQueue.add.mockResolvedValue(mockJob);
            const aiAnalysisDefinition = {
                ...mockPipelineDefinition,
                stages: [
                    {
                        id: 'ai-stage',
                        name: 'AI Analysis',
                        type: 'ai-analysis',
                        config: { model: 'threat-detection' },
                    },
                ],
            };
            // Act
            const execution = await service.createPipeline(aiAnalysisDefinition);
            // Assert
            expect(mockAiRequestQueue.add).toHaveBeenCalledWith('analyze', {
                pipelineId: execution.id,
                stageId: 'ai-stage',
                data: expect.any(Object),
                config: { model: 'threat-detection' },
            });
            expect(execution.results['ai-stage']).toEqual({
                analysis: 'threat detected',
                confidence: 0.95,
            });
        });
        it('should execute model training stage', async () => {
            // Arrange
            const mockJob = {
                finished: jest.fn().mockResolvedValue({
                    modelId: 'trained-model-123',
                    metrics: { accuracy: 0.95 }
                }),
            };
            mockTrainingQueue.add.mockResolvedValue(mockJob);
            const trainingDefinition = {
                ...mockPipelineDefinition,
                stages: [
                    {
                        id: 'training-stage',
                        name: 'Model Training',
                        type: 'model-training',
                        config: { epochs: 10 },
                    },
                ],
            };
            // Act
            const execution = await service.createPipeline(trainingDefinition);
            // Assert
            expect(mockTrainingQueue.add).toHaveBeenCalledWith('train', {
                pipelineId: execution.id,
                stageId: 'training-stage',
                data: expect.any(Object),
                config: { epochs: 10 },
            });
            expect(execution.results['training-stage']).toEqual({
                modelId: 'trained-model-123',
                metrics: { accuracy: 0.95 },
            });
        });
        it('should execute data preprocessing stage', async () => {
            // Arrange
            const preprocessingDefinition = {
                ...mockPipelineDefinition,
                stages: [
                    {
                        id: 'preprocess-stage',
                        name: 'Data Preprocessing',
                        type: 'data-preprocessing',
                        config: { normalize: true, validate: true },
                    },
                ],
            };
            // Act
            const execution = await service.createPipeline(preprocessingDefinition);
            // Assert
            expect(execution.results['preprocess-stage']).toEqual({
                processedData: expect.any(Object),
                metadata: { stage: 'preprocess-stage' },
            });
        });
        it('should execute conditional stage', async () => {
            // Arrange
            const conditionalDefinition = {
                ...mockPipelineDefinition,
                stages: [
                    {
                        id: 'conditional-stage',
                        name: 'Conditional Logic',
                        type: 'conditional',
                        config: { condition: 'always_true' },
                    },
                ],
            };
            // Act
            const execution = await service.createPipeline(conditionalDefinition);
            // Assert
            expect(execution.results['conditional-stage']).toEqual({
                conditionMet: true,
                result: 'proceed',
            });
        });
        it('should execute parallel batch stage', async () => {
            // Arrange
            const batchDefinition = {
                ...mockPipelineDefinition,
                stages: [
                    {
                        id: 'batch-stage',
                        name: 'Parallel Batch',
                        type: 'parallel-batch',
                        config: { batchSize: 5 },
                        input: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], // 10 items
                    },
                ],
            };
            // Act
            const execution = await service.createPipeline(batchDefinition);
            // Assert
            expect(execution.results['batch-stage']).toHaveLength(10);
            expect(execution.results['batch-stage'][0]).toEqual({
                ...1,
                processed: true,
            });
        });
    });
    describe('parallel execution', () => {
        it('should execute stages in parallel when specified', async () => {
            // Arrange
            const mockJob = {
                finished: jest.fn().mockResolvedValue({ result: 'success' }),
            };
            mockAiRequestQueue.add.mockResolvedValue(mockJob);
            const parallelDefinition = {
                ...mockPipelineDefinition,
                executionStrategy: 'parallel',
                stages: [
                    {
                        id: 'parallel-stage-1',
                        name: 'Parallel Stage 1',
                        type: 'data-preprocessing',
                        config: {},
                    },
                    {
                        id: 'parallel-stage-2',
                        name: 'Parallel Stage 2',
                        type: 'data-preprocessing',
                        config: {},
                    },
                ],
            };
            // Act
            const execution = await service.createPipeline(parallelDefinition);
            // Assert
            expect(execution.status).toBe('completed');
            expect(execution.metrics.completedStages).toBe(2);
        });
        it('should handle stage dependencies in parallel execution', async () => {
            // Arrange
            const mockJob = {
                finished: jest.fn().mockResolvedValue({ result: 'success' }),
            };
            mockAiRequestQueue.add.mockResolvedValue(mockJob);
            const dependentParallelDefinition = {
                ...mockPipelineDefinition,
                executionStrategy: 'parallel',
                stages: [
                    {
                        id: 'independent-stage',
                        name: 'Independent Stage',
                        type: 'data-preprocessing',
                        config: {},
                    },
                    {
                        id: 'dependent-stage',
                        name: 'Dependent Stage',
                        type: 'ai-analysis',
                        dependencies: ['independent-stage'],
                        config: {},
                    },
                ],
            };
            // Act
            const execution = await service.createPipeline(dependentParallelDefinition);
            // Assert
            expect(execution.status).toBe('completed');
            expect(execution.metrics.completedStages).toBe(2);
        });
    });
    describe('error handling', () => {
        it('should handle stage failures with continueOnFailure', async () => {
            // Arrange
            const failingJob = {
                finished: jest.fn().mockRejectedValue(new Error('Stage failed')),
            };
            const successJob = {
                finished: jest.fn().mockResolvedValue({ result: 'success' }),
            };
            mockAiRequestQueue.add
                .mockResolvedValueOnce(failingJob)
                .mockResolvedValueOnce(successJob);
            const resilientDefinition = {
                ...mockPipelineDefinition,
                stages: [
                    {
                        id: 'failing-stage',
                        name: 'Failing Stage',
                        type: 'ai-analysis',
                        continueOnFailure: true,
                        config: {},
                    },
                    {
                        id: 'success-stage',
                        name: 'Success Stage',
                        type: 'data-preprocessing',
                        config: {},
                    },
                ],
            };
            // Act
            const execution = await service.createPipeline(resilientDefinition);
            // Assert
            expect(execution.status).toBe('completed');
            expect(execution.metrics.failedStages).toBe(1);
            expect(execution.metrics.completedStages).toBe(1);
        });
        it('should fail pipeline when critical stage fails', async () => {
            // Arrange
            const failingJob = {
                finished: jest.fn().mockRejectedValue(new Error('Critical stage failed')),
            };
            mockAiRequestQueue.add.mockResolvedValue(failingJob);
            const criticalDefinition = {
                ...mockPipelineDefinition,
                stages: [
                    {
                        id: 'critical-stage',
                        name: 'Critical Stage',
                        type: 'ai-analysis',
                        continueOnFailure: false, // Critical stage
                        config: {},
                    },
                ],
            };
            // Act & Assert
            await expect(service.createPipeline(criticalDefinition))
                .rejects.toThrow('Pipeline creation failed');
        });
    });
    describe('template management', () => {
        it('should register pipeline template successfully', () => {
            // Arrange
            const template = {
                name: 'Custom Template',
                description: 'Custom pipeline template',
                stages: [
                    {
                        id: 'custom-stage',
                        name: 'Custom Stage',
                        type: 'ai-analysis',
                        config: { model: 'custom-model' },
                    },
                ],
                executionStrategy: 'sequential',
            };
            // Act
            service.registerPipelineTemplate('custom-template', template);
            // Assert - Should not throw error and template should be usable
            expect(() => service.registerPipelineTemplate('custom-template', template))
                .not.toThrow();
        });
    });
    describe('pipeline metrics and monitoring', () => {
        it('should track pipeline metrics correctly', async () => {
            // Arrange
            const mockJob = {
                finished: jest.fn().mockResolvedValue({ result: 'success' }),
            };
            mockAiRequestQueue.add.mockResolvedValue(mockJob);
            // Act
            const execution = await service.createPipeline(mockPipelineDefinition);
            // Assert
            expect(execution.metrics).toEqual({
                startTime: expect.any(Date),
                endTime: expect.any(Date),
                totalStages: 2,
                completedStages: 2,
                failedStages: 0,
            });
            expect(execution.metrics.endTime.getTime()).toBeGreaterThan(execution.metrics.startTime.getTime());
        });
        it('should calculate progress correctly', async () => {
            // Arrange
            const mockJob = {
                finished: jest.fn().mockResolvedValue({ result: 'success' }),
            };
            mockAiRequestQueue.add.mockResolvedValue(mockJob);
            const execution = await service.createPipeline(mockPipelineDefinition);
            // Act
            const status = await service.getPipelineStatus(execution.id);
            // Assert
            expect(status.progress).toBe(1); // 100% complete (2/2 stages)
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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