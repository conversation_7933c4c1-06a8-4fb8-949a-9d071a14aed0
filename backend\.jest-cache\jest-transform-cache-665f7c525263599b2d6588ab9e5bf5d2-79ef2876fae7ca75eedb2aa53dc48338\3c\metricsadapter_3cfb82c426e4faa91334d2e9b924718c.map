{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\infrastructure\\adapters\\metrics.adapter.ts", "mappings": ";;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAE/C;;;;;;GAMG;AAEI,IAAM,cAAc,sBAApB,MAAM,cAAc;IAMzB,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QALxC,WAAM,GAAG,IAAI,eAAM,CAAC,gBAAc,CAAC,IAAI,CAAC,CAAC;QACzC,YAAO,GAAG,IAAI,GAAG,EAAyB,CAAC;QAC3C,aAAQ,GAAG,IAAI,GAAG,EAAkB,CAAC;QACrC,WAAM,GAAG,IAAI,GAAG,EAAkB,CAAC;IAEQ,CAAC;IAE7D;;OAEG;IACH,iBAAiB,CACf,IAAY,EACZ,QAAgB,EAChB,SAAiB,EACjB,QAA8B;QAE9B,MAAM,MAAM,GAAgB;YAC1B,IAAI,EAAE,cAAc;YACpB,IAAI,EAAE,WAAW;YACjB,KAAK,EAAE,QAAQ;YACf,MAAM,EAAE;gBACN,cAAc,EAAE,IAAI;gBACpB,UAAU,EAAE,SAAS;gBACrB,GAAG,QAAQ;aACZ;YACD,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,IAAI,eAAe,QAAQ,IAAI,CAAC,CAAC;IACtF,CAAC;IAED;;OAEG;IACH,sBAAsB,CACpB,OAAe,EACf,OAAgC;QAEhC,kBAAkB;QAClB,IAAI,CAAC,YAAY,CAAC;YAChB,IAAI,EAAE,gBAAgB;YACtB,IAAI,EAAE,OAAO;YACb,KAAK,EAAE,OAAO,CAAC,QAAQ;YACvB,MAAM,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE;YAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;QAEH,iBAAiB;QACjB,IAAI,CAAC,YAAY,CAAC;YAChB,IAAI,EAAE,eAAe;YACrB,IAAI,EAAE,WAAW;YACjB,KAAK,EAAE,OAAO,CAAC,OAAO;YACtB,MAAM,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE;YAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;QAEH,oBAAoB;QACpB,IAAI,CAAC,YAAY,CAAC;YAChB,IAAI,EAAE,kBAAkB;YACxB,IAAI,EAAE,OAAO;YACb,KAAK,EAAE,OAAO,CAAC,UAAU;YACzB,MAAM,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE;YAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;QAEH,cAAc;QACd,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC/B,IAAI,CAAC,YAAY,CAAC;gBAChB,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,SAAS;gBACf,KAAK,EAAE,OAAO,CAAC,IAAI;gBACnB,MAAM,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE;gBAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,OAAO,EAAE,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACH,oBAAoB,CAClB,UAAkB,EAClB,MAA6B;QAE7B,sBAAsB;QACtB,IAAI,CAAC,YAAY,CAAC;YAChB,IAAI,EAAE,uBAAuB;YAC7B,IAAI,EAAE,OAAO;YACb,KAAK,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,EAAE,EAAE,WAAW,EAAE,UAAU,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;QAEH,uBAAuB;QACvB,IAAI,CAAC,YAAY,CAAC;YAChB,IAAI,EAAE,wBAAwB;YAC9B,IAAI,EAAE,WAAW;YACjB,KAAK,EAAE,MAAM,CAAC,YAAY;YAC1B,MAAM,EAAE,EAAE,WAAW,EAAE,UAAU,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;QAEH,oBAAoB;QACpB,IAAI,CAAC,YAAY,CAAC;YAChB,IAAI,EAAE,qBAAqB;YAC3B,IAAI,EAAE,OAAO;YACb,KAAK,EAAE,MAAM,CAAC,SAAS;YACvB,MAAM,EAAE,EAAE,WAAW,EAAE,UAAU,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yCAAyC,UAAU,EAAE,CAAC,CAAC;IAC3E,CAAC;IAED;;OAEG;IACH,kBAAkB,CAChB,SAAiB,EACjB,SAA4C,EAC5C,QAAiB;QAEjB,iCAAiC;QACjC,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,EAAE;YAC9C,UAAU,EAAE,SAAS;YACrB,SAAS;SACV,CAAC,CAAC;QAEH,8CAA8C;QAC9C,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC3B,IAAI,CAAC,YAAY,CAAC;gBAChB,IAAI,EAAE,0BAA0B;gBAChC,IAAI,EAAE,WAAW;gBACjB,KAAK,EAAE,QAAQ;gBACf,MAAM,EAAE;oBACN,UAAU,EAAE,SAAS;oBACrB,SAAS;iBACV;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,SAAS,IAAI,SAAS,EAAE,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,uBAAuB,CACrB,UAAkB,EAClB,MAA0C,EAC1C,QAAiB,EACjB,UAAmB;QAEnB,iCAAiC;QACjC,IAAI,CAAC,gBAAgB,CAAC,2BAA2B,EAAE;YACjD,WAAW,EAAE,UAAU;YACvB,MAAM;SACP,CAAC,CAAC;QAEH,wCAAwC;QACxC,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC3B,IAAI,CAAC,YAAY,CAAC;gBAChB,IAAI,EAAE,mBAAmB;gBACzB,IAAI,EAAE,WAAW;gBACjB,KAAK,EAAE,QAAQ;gBACf,MAAM,EAAE,EAAE,WAAW,EAAE,UAAU,EAAE;gBACnC,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;QACL,CAAC;QAED,iCAAiC;QACjC,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;YAC7B,IAAI,CAAC,YAAY,CAAC;gBAChB,IAAI,EAAE,iBAAiB;gBACvB,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,UAAU;gBACjB,MAAM,EAAE,EAAE,WAAW,EAAE,UAAU,EAAE;gBACnC,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,UAAU,IAAI,MAAM,EAAE,CAAC,CAAC;IACnF,CAAC;IAED;;OAEG;IACH,WAAW,CACT,SAAiB,EACjB,SAAiB,EACjB,WAAsC,QAAQ,EAC9C,QAA8B;QAE9B,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE;YACpC,UAAU,EAAE,SAAS;YACrB,SAAS;YACT,QAAQ;YACR,GAAG,QAAQ;SACZ,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,SAAS,OAAO,SAAS,EAAE,CAAC,CAAC;IAC3E,CAAC;IAED;;OAEG;IACH,kBAAkB,CAChB,IAAY,EACZ,KAAa,EACb,OAA0C,OAAO,EACjD,MAA+B;QAE/B,IAAI,CAAC,YAAY,CAAC;YAChB,IAAI;YACJ,IAAI;YACJ,KAAK;YACL,MAAM,EAAE,MAAM,IAAI,EAAE;YACpB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,IAAI,MAAM,KAAK,EAAE,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,UAAU;QACR,MAAM,QAAQ,GAAoB;YAChC,QAAQ,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC3C,MAAM,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC;YACvC,UAAU,EAAE,EAAE;YACd,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,iCAAiC;QACjC,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAC3C,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;gBAC1D,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC/D,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG;oBAC1B,KAAK,EAAE,MAAM,CAAC,MAAM;oBACpB,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;oBAC1C,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC;oBACd,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;oBAC9B,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM;oBAC1D,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC;oBACjC,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC;oBAClC,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC;iBACnC,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,SAAe,EAAE,OAAa;QAC9C,MAAM,MAAM,GAAkB,EAAE,CAAC;QAEjC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;YAC5C,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAC7B,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,IAAI,SAAS,IAAI,KAAK,CAAC,SAAS,IAAI,OAAO,CACpE,CAAC;YACF,MAAM,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;QAC3B,CAAC;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,YAAkB,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAClE,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAC3C,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC;YACtC,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC;YAEtE,IAAI,QAAQ,CAAC,MAAM,KAAK,cAAc,EAAE,CAAC;gBACvC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBACjC,YAAY,IAAI,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC;YACnD,CAAC;QACH,CAAC;QAED,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YACrB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,YAAY,qBAAqB,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,uBAAuB;QACrB,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAEnC,kBAAkB;QAClB,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC9D,KAAK,CAAC,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,CAAC;YACrC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,KAAK,EAAE,CAAC,CAAC;QACjC,CAAC;QAED,gBAAgB;QAChB,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5D,KAAK,CAAC,IAAI,CAAC,UAAU,IAAI,QAAQ,CAAC,CAAC;YACnC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,KAAK,EAAE,CAAC,CAAC;QACjC,CAAC;QAED,oBAAoB;QACpB,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAChE,KAAK,CAAC,IAAI,CAAC,UAAU,IAAI,YAAY,CAAC,CAAC;YACvC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,UAAU,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;YAC3C,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,QAAQ,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;YACvC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,qBAAqB,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;YACzE,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,qBAAqB,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;YACzE,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,qBAAqB,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;YACzE,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,qBAAqB,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;YACzE,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,sBAAsB,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;QACzD,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED,yBAAyB;IAEjB,YAAY,CAAC,MAAmB;QACtC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACpC,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAE,CAAC;QAC/C,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAErB,mDAAmD;QACnD,IAAI,OAAO,CAAC,MAAM,GAAG,KAAK,EAAE,CAAC;YAC3B,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC;QAC5C,CAAC;QAED,6BAA6B;QAC7B,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC9B,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YAC1D,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QACvE,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YACnC,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YAC1D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,IAAY,EAAE,SAAiC,EAAE;QACxE,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAC5C,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5D,CAAC;IAEO,YAAY,CAAC,IAAY,EAAE,MAA8B;QAC/D,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;aACtC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;aACtC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,KAAK,GAAG,CAAC;aAC1C,IAAI,CAAC,GAAG,CAAC,CAAC;QAEb,OAAO,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,UAAU,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;IACtD,CAAC;IAEO,UAAU,CAAC,YAAsB,EAAE,CAAS;QAClD,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAExC,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QACrD,OAAO,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IAC1C,CAAC;IAEO,cAAc,CAAC,UAAkB,EAAE,SAAiB;QAC1D,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;QACnD,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,IAAI,SAAS,CAAC,CAAC,MAAM,CAAC;IAClE,CAAC;CACF,CAAA;AA5XY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;yDAOiC,sBAAa,oBAAb,sBAAa;GAN9C,cAAc,CA4X1B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\infrastructure\\adapters\\metrics.adapter.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\n\r\n/**\r\n * Metrics Adapter\r\n * \r\n * Provides abstraction layer for metrics collection and reporting.\r\n * Supports multiple metrics backends and provides standardized\r\n * metrics recording interface for AI operations.\r\n */\r\n@Injectable()\r\nexport class MetricsAdapter {\r\n  private readonly logger = new Logger(MetricsAdapter.name);\r\n  private readonly metrics = new Map<string, MetricEntry[]>();\r\n  private readonly counters = new Map<string, number>();\r\n  private readonly gauges = new Map<string, number>();\r\n\r\n  constructor(private readonly configService: ConfigService) {}\r\n\r\n  /**\r\n   * Records an AI operation metric\r\n   */\r\n  recordAiOperation(\r\n    type: string,\r\n    duration: number,\r\n    requestId: string,\r\n    metadata?: Record<string, any>\r\n  ): void {\r\n    const metric: MetricEntry = {\r\n      name: 'ai_operation',\r\n      type: 'histogram',\r\n      value: duration,\r\n      labels: {\r\n        operation_type: type,\r\n        request_id: requestId,\r\n        ...metadata,\r\n      },\r\n      timestamp: new Date(),\r\n    };\r\n\r\n    this.recordMetric(metric);\r\n    this.logger.debug(`Recorded AI operation metric: ${type}, duration: ${duration}ms`);\r\n  }\r\n\r\n  /**\r\n   * Records model performance metrics\r\n   */\r\n  recordModelPerformance(\r\n    modelId: string,\r\n    metrics: ModelPerformanceMetrics\r\n  ): void {\r\n    // Record accuracy\r\n    this.recordMetric({\r\n      name: 'model_accuracy',\r\n      type: 'gauge',\r\n      value: metrics.accuracy,\r\n      labels: { model_id: modelId },\r\n      timestamp: new Date(),\r\n    });\r\n\r\n    // Record latency\r\n    this.recordMetric({\r\n      name: 'model_latency',\r\n      type: 'histogram',\r\n      value: metrics.latency,\r\n      labels: { model_id: modelId },\r\n      timestamp: new Date(),\r\n    });\r\n\r\n    // Record throughput\r\n    this.recordMetric({\r\n      name: 'model_throughput',\r\n      type: 'gauge',\r\n      value: metrics.throughput,\r\n      labels: { model_id: modelId },\r\n      timestamp: new Date(),\r\n    });\r\n\r\n    // Record cost\r\n    if (metrics.cost !== undefined) {\r\n      this.recordMetric({\r\n        name: 'model_cost',\r\n        type: 'counter',\r\n        value: metrics.cost,\r\n        labels: { model_id: modelId },\r\n        timestamp: new Date(),\r\n      });\r\n    }\r\n\r\n    this.logger.debug(`Recorded model performance metrics for: ${modelId}`);\r\n  }\r\n\r\n  /**\r\n   * Records provider health metrics\r\n   */\r\n  recordProviderHealth(\r\n    providerId: string,\r\n    health: ProviderHealthMetrics\r\n  ): void {\r\n    // Record availability\r\n    this.recordMetric({\r\n      name: 'provider_availability',\r\n      type: 'gauge',\r\n      value: health.available ? 1 : 0,\r\n      labels: { provider_id: providerId },\r\n      timestamp: new Date(),\r\n    });\r\n\r\n    // Record response time\r\n    this.recordMetric({\r\n      name: 'provider_response_time',\r\n      type: 'histogram',\r\n      value: health.responseTime,\r\n      labels: { provider_id: providerId },\r\n      timestamp: new Date(),\r\n    });\r\n\r\n    // Record error rate\r\n    this.recordMetric({\r\n      name: 'provider_error_rate',\r\n      type: 'gauge',\r\n      value: health.errorRate,\r\n      labels: { provider_id: providerId },\r\n      timestamp: new Date(),\r\n    });\r\n\r\n    this.logger.debug(`Recorded provider health metrics for: ${providerId}`);\r\n  }\r\n\r\n  /**\r\n   * Records cache performance metrics\r\n   */\r\n  recordCacheMetrics(\r\n    cacheType: string,\r\n    operation: 'hit' | 'miss' | 'set' | 'delete',\r\n    duration?: number\r\n  ): void {\r\n    // Record cache operation counter\r\n    this.incrementCounter(`cache_operations_total`, {\r\n      cache_type: cacheType,\r\n      operation,\r\n    });\r\n\r\n    // Record cache operation duration if provided\r\n    if (duration !== undefined) {\r\n      this.recordMetric({\r\n        name: 'cache_operation_duration',\r\n        type: 'histogram',\r\n        value: duration,\r\n        labels: {\r\n          cache_type: cacheType,\r\n          operation,\r\n        },\r\n        timestamp: new Date(),\r\n      });\r\n    }\r\n\r\n    this.logger.debug(`Recorded cache metric: ${cacheType} ${operation}`);\r\n  }\r\n\r\n  /**\r\n   * Records pipeline execution metrics\r\n   */\r\n  recordPipelineExecution(\r\n    pipelineId: string,\r\n    status: 'started' | 'completed' | 'failed',\r\n    duration?: number,\r\n    stageCount?: number\r\n  ): void {\r\n    // Record pipeline status counter\r\n    this.incrementCounter('pipeline_executions_total', {\r\n      pipeline_id: pipelineId,\r\n      status,\r\n    });\r\n\r\n    // Record pipeline duration if completed\r\n    if (duration !== undefined) {\r\n      this.recordMetric({\r\n        name: 'pipeline_duration',\r\n        type: 'histogram',\r\n        value: duration,\r\n        labels: { pipeline_id: pipelineId },\r\n        timestamp: new Date(),\r\n      });\r\n    }\r\n\r\n    // Record stage count if provided\r\n    if (stageCount !== undefined) {\r\n      this.recordMetric({\r\n        name: 'pipeline_stages',\r\n        type: 'gauge',\r\n        value: stageCount,\r\n        labels: { pipeline_id: pipelineId },\r\n        timestamp: new Date(),\r\n      });\r\n    }\r\n\r\n    this.logger.debug(`Recorded pipeline execution metric: ${pipelineId} ${status}`);\r\n  }\r\n\r\n  /**\r\n   * Records error metrics\r\n   */\r\n  recordError(\r\n    errorType: string,\r\n    component: string,\r\n    severity: 'low' | 'medium' | 'high' = 'medium',\r\n    metadata?: Record<string, any>\r\n  ): void {\r\n    this.incrementCounter('errors_total', {\r\n      error_type: errorType,\r\n      component,\r\n      severity,\r\n      ...metadata,\r\n    });\r\n\r\n    this.logger.debug(`Recorded error metric: ${errorType} in ${component}`);\r\n  }\r\n\r\n  /**\r\n   * Records custom metric\r\n   */\r\n  recordCustomMetric(\r\n    name: string,\r\n    value: number,\r\n    type: 'counter' | 'gauge' | 'histogram' = 'gauge',\r\n    labels?: Record<string, string>\r\n  ): void {\r\n    this.recordMetric({\r\n      name,\r\n      type,\r\n      value,\r\n      labels: labels || {},\r\n      timestamp: new Date(),\r\n    });\r\n\r\n    this.logger.debug(`Recorded custom metric: ${name} = ${value}`);\r\n  }\r\n\r\n  /**\r\n   * Gets current metric values\r\n   */\r\n  getMetrics(): MetricsSnapshot {\r\n    const snapshot: MetricsSnapshot = {\r\n      counters: Object.fromEntries(this.counters),\r\n      gauges: Object.fromEntries(this.gauges),\r\n      histograms: {},\r\n      timestamp: new Date(),\r\n    };\r\n\r\n    // Calculate histogram statistics\r\n    for (const [name, entries] of this.metrics) {\r\n      if (entries.length > 0 && entries[0].type === 'histogram') {\r\n        const values = entries.map(e => e.value).sort((a, b) => a - b);\r\n        snapshot.histograms[name] = {\r\n          count: values.length,\r\n          sum: values.reduce((sum, v) => sum + v, 0),\r\n          min: values[0],\r\n          max: values[values.length - 1],\r\n          avg: values.reduce((sum, v) => sum + v, 0) / values.length,\r\n          p50: this.percentile(values, 0.5),\r\n          p95: this.percentile(values, 0.95),\r\n          p99: this.percentile(values, 0.99),\r\n        };\r\n      }\r\n    }\r\n\r\n    return snapshot;\r\n  }\r\n\r\n  /**\r\n   * Gets metrics for a specific time range\r\n   */\r\n  getMetricsInRange(startTime: Date, endTime: Date): MetricEntry[] {\r\n    const result: MetricEntry[] = [];\r\n\r\n    for (const entries of this.metrics.values()) {\r\n      const filtered = entries.filter(\r\n        entry => entry.timestamp >= startTime && entry.timestamp <= endTime\r\n      );\r\n      result.push(...filtered);\r\n    }\r\n\r\n    return result.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());\r\n  }\r\n\r\n  /**\r\n   * Clears old metrics to prevent memory leaks\r\n   */\r\n  cleanup(olderThan: Date = new Date(Date.now() - 24 * 60 * 60 * 1000)): void {\r\n    let totalRemoved = 0;\r\n\r\n    for (const [name, entries] of this.metrics) {\r\n      const originalLength = entries.length;\r\n      const filtered = entries.filter(entry => entry.timestamp > olderThan);\r\n      \r\n      if (filtered.length !== originalLength) {\r\n        this.metrics.set(name, filtered);\r\n        totalRemoved += originalLength - filtered.length;\r\n      }\r\n    }\r\n\r\n    if (totalRemoved > 0) {\r\n      this.logger.debug(`Cleaned up ${totalRemoved} old metric entries`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Exports metrics in Prometheus format\r\n   */\r\n  exportPrometheusMetrics(): string {\r\n    const lines: string[] = [];\r\n    const snapshot = this.getMetrics();\r\n\r\n    // Export counters\r\n    for (const [name, value] of Object.entries(snapshot.counters)) {\r\n      lines.push(`# TYPE ${name} counter`);\r\n      lines.push(`${name} ${value}`);\r\n    }\r\n\r\n    // Export gauges\r\n    for (const [name, value] of Object.entries(snapshot.gauges)) {\r\n      lines.push(`# TYPE ${name} gauge`);\r\n      lines.push(`${name} ${value}`);\r\n    }\r\n\r\n    // Export histograms\r\n    for (const [name, stats] of Object.entries(snapshot.histograms)) {\r\n      lines.push(`# TYPE ${name} histogram`);\r\n      lines.push(`${name}_count ${stats.count}`);\r\n      lines.push(`${name}_sum ${stats.sum}`);\r\n      lines.push(`${name}_bucket{le=\"0.1\"} ${this.getBucketCount(name, 0.1)}`);\r\n      lines.push(`${name}_bucket{le=\"0.5\"} ${this.getBucketCount(name, 0.5)}`);\r\n      lines.push(`${name}_bucket{le=\"1.0\"} ${this.getBucketCount(name, 1.0)}`);\r\n      lines.push(`${name}_bucket{le=\"5.0\"} ${this.getBucketCount(name, 5.0)}`);\r\n      lines.push(`${name}_bucket{le=\"+Inf\"} ${stats.count}`);\r\n    }\r\n\r\n    return lines.join('\\n');\r\n  }\r\n\r\n  // Private helper methods\r\n\r\n  private recordMetric(metric: MetricEntry): void {\r\n    if (!this.metrics.has(metric.name)) {\r\n      this.metrics.set(metric.name, []);\r\n    }\r\n\r\n    const entries = this.metrics.get(metric.name)!;\r\n    entries.push(metric);\r\n\r\n    // Keep only recent entries to prevent memory leaks\r\n    if (entries.length > 10000) {\r\n      entries.splice(0, entries.length - 10000);\r\n    }\r\n\r\n    // Update counters and gauges\r\n    if (metric.type === 'counter') {\r\n      const key = this.getMetricKey(metric.name, metric.labels);\r\n      this.counters.set(key, (this.counters.get(key) || 0) + metric.value);\r\n    } else if (metric.type === 'gauge') {\r\n      const key = this.getMetricKey(metric.name, metric.labels);\r\n      this.gauges.set(key, metric.value);\r\n    }\r\n  }\r\n\r\n  private incrementCounter(name: string, labels: Record<string, string> = {}): void {\r\n    const key = this.getMetricKey(name, labels);\r\n    this.counters.set(key, (this.counters.get(key) || 0) + 1);\r\n  }\r\n\r\n  private getMetricKey(name: string, labels: Record<string, string>): string {\r\n    const labelPairs = Object.entries(labels)\r\n      .sort(([a], [b]) => a.localeCompare(b))\r\n      .map(([key, value]) => `${key}=\"${value}\"`)\r\n      .join(',');\r\n    \r\n    return labelPairs ? `${name}{${labelPairs}}` : name;\r\n  }\r\n\r\n  private percentile(sortedValues: number[], p: number): number {\r\n    if (sortedValues.length === 0) return 0;\r\n    \r\n    const index = Math.ceil(sortedValues.length * p) - 1;\r\n    return sortedValues[Math.max(0, index)];\r\n  }\r\n\r\n  private getBucketCount(metricName: string, threshold: number): number {\r\n    const entries = this.metrics.get(metricName) || [];\r\n    return entries.filter(entry => entry.value <= threshold).length;\r\n  }\r\n}\r\n\r\n// Type definitions\r\ninterface MetricEntry {\r\n  name: string;\r\n  type: 'counter' | 'gauge' | 'histogram';\r\n  value: number;\r\n  labels: Record<string, string>;\r\n  timestamp: Date;\r\n}\r\n\r\ninterface ModelPerformanceMetrics {\r\n  accuracy: number;\r\n  latency: number;\r\n  throughput: number;\r\n  cost?: number;\r\n}\r\n\r\ninterface ProviderHealthMetrics {\r\n  available: boolean;\r\n  responseTime: number;\r\n  errorRate: number;\r\n}\r\n\r\ninterface MetricsSnapshot {\r\n  counters: Record<string, number>;\r\n  gauges: Record<string, number>;\r\n  histograms: Record<string, HistogramStats>;\r\n  timestamp: Date;\r\n}\r\n\r\ninterface HistogramStats {\r\n  count: number;\r\n  sum: number;\r\n  min: number;\r\n  max: number;\r\n  avg: number;\r\n  p50: number;\r\n  p95: number;\r\n  p99: number;\r\n}"], "version": 3}