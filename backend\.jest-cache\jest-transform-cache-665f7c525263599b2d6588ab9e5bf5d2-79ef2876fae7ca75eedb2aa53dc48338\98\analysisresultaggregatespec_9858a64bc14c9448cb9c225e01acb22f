8ab0ffb6c778b83ed7e5151c0366c107
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const unique_entity_id_value_object_1 = require("../../../../../shared-kernel/value-objects/unique-entity-id.value-object");
const analysis_result_entity_1 = require("../../entities/analysis-result.entity");
const analysis_result_factory_1 = require("../../factories/analysis-result.factory");
const analysis_result_aggregate_1 = require("../analysis-result.aggregate");
describe('AnalysisResultAggregate', () => {
    let aggregate;
    beforeEach(() => {
        aggregate = new analysis_result_aggregate_1.AnalysisResultAggregate();
    });
    describe('Analysis Result Management', () => {
        it('should create and add a new analysis result', () => {
            const request = {
                requestId: 'test-request-123',
                modelId: unique_entity_id_value_object_1.UniqueEntityId.generate(),
                analysisType: analysis_result_entity_1.AnalysisType.CLASSIFICATION,
                inputData: {
                    data: { test: 'input' },
                    format: 'json',
                    size: 100,
                    checksum: 'checksum',
                    preprocessingApplied: [],
                    validationRules: [],
                },
            };
            const result = aggregate.createAnalysisResult(request);
            expect(result.requestId).toBe('test-request-123');
            expect(aggregate.getAllAnalysisResults()).toHaveLength(1);
            expect(aggregate.getAnalysisResult(result.id)).toBe(result);
        });
        it('should throw error when creating duplicate request ID', () => {
            const request = {
                requestId: 'duplicate-request',
                modelId: unique_entity_id_value_object_1.UniqueEntityId.generate(),
                analysisType: analysis_result_entity_1.AnalysisType.CLASSIFICATION,
                inputData: {
                    data: { test: 'input' },
                    format: 'json',
                    size: 100,
                    checksum: 'checksum',
                    preprocessingApplied: [],
                    validationRules: [],
                },
            };
            aggregate.createAnalysisResult(request);
            expect(() => aggregate.createAnalysisResult(request)).toThrow("Analysis result with request ID 'duplicate-request' already exists");
        });
        it('should add existing analysis result', () => {
            const result = analysis_result_factory_1.AnalysisResultFactory.createForTesting();
            aggregate.addAnalysisResult(result);
            expect(aggregate.getAllAnalysisResults()).toHaveLength(1);
            expect(aggregate.getAnalysisResult(result.id)).toBe(result);
        });
        it('should throw error when adding duplicate analysis result', () => {
            const result = analysis_result_factory_1.AnalysisResultFactory.createForTesting();
            aggregate.addAnalysisResult(result);
            expect(() => aggregate.addAnalysisResult(result)).toThrow(`Analysis result with ID '${result.id.toString()}' already exists in aggregate`);
        });
        it('should remove analysis result', () => {
            const result = analysis_result_factory_1.AnalysisResultFactory.createForTesting();
            aggregate.addAnalysisResult(result);
            aggregate.removeAnalysisResult(result.id);
            expect(aggregate.getAllAnalysisResults()).toHaveLength(0);
            expect(result.status).toBe(analysis_result_entity_1.AnalysisStatus.CANCELLED);
        });
        it('should throw error when removing non-existent analysis result', () => {
            const nonExistentId = unique_entity_id_value_object_1.UniqueEntityId.generate();
            expect(() => aggregate.removeAnalysisResult(nonExistentId)).toThrow(`Analysis result with ID '${nonExistentId.toString()}' not found`);
        });
    });
    describe('Analysis Result Querying', () => {
        beforeEach(() => {
            // Create test analysis results
            const results = [
                analysis_result_factory_1.AnalysisResultFactory.createForTesting({
                    requestId: 'request-1',
                    analysisType: analysis_result_entity_1.AnalysisType.CLASSIFICATION,
                    tags: ['test', 'classification'],
                }),
                analysis_result_factory_1.AnalysisResultFactory.createForTesting({
                    requestId: 'request-2',
                    analysisType: analysis_result_entity_1.AnalysisType.ANOMALY_DETECTION,
                    tags: ['test', 'anomaly'],
                }),
                analysis_result_factory_1.AnalysisResultFactory.createCompletedForTesting({
                    requestId: 'request-3',
                    analysisType: analysis_result_entity_1.AnalysisType.CLASSIFICATION,
                    tags: ['completed', 'classification'],
                }),
            ];
            results.forEach(result => aggregate.addAnalysisResult(result));
        });
        it('should get all analysis results', () => {
            const results = aggregate.getAllAnalysisResults();
            expect(results).toHaveLength(3);
        });
        it('should find analysis results by request ID', () => {
            const query = {
                requestId: 'request-1',
            };
            const results = aggregate.findAnalysisResults(query);
            expect(results).toHaveLength(1);
            expect(results[0].requestId).toBe('request-1');
        });
        it('should find analysis results by analysis type', () => {
            const query = {
                analysisType: analysis_result_entity_1.AnalysisType.CLASSIFICATION,
            };
            const results = aggregate.findAnalysisResults(query);
            expect(results).toHaveLength(2);
            expect(results.every(r => r.analysisType === analysis_result_entity_1.AnalysisType.CLASSIFICATION)).toBe(true);
        });
        it('should find analysis results by status', () => {
            const query = {
                status: analysis_result_entity_1.AnalysisStatus.COMPLETED,
            };
            const results = aggregate.findAnalysisResults(query);
            expect(results).toHaveLength(1);
            expect(results[0].status).toBe(analysis_result_entity_1.AnalysisStatus.COMPLETED);
        });
        it('should find analysis results by tags', () => {
            const query = {
                tags: ['classification'],
            };
            const results = aggregate.findAnalysisResults(query);
            expect(results).toHaveLength(2);
            expect(results.every(r => r.hasTag('classification'))).toBe(true);
        });
        it('should find analysis results by confidence range', () => {
            const query = {
                minConfidence: 0.9,
            };
            const results = aggregate.findAnalysisResults(query);
            expect(results).toHaveLength(1); // Only the completed one has high confidence
            expect(results[0].confidence).toBeGreaterThanOrEqual(0.9);
        });
        it('should find analysis results by multiple criteria', () => {
            const query = {
                analysisType: analysis_result_entity_1.AnalysisType.CLASSIFICATION,
                status: analysis_result_entity_1.AnalysisStatus.COMPLETED,
            };
            const results = aggregate.findAnalysisResults(query);
            expect(results).toHaveLength(1);
            expect(results[0].analysisType).toBe(analysis_result_entity_1.AnalysisType.CLASSIFICATION);
            expect(results[0].status).toBe(analysis_result_entity_1.AnalysisStatus.COMPLETED);
        });
        it('should find by request ID', () => {
            const result = aggregate.findByRequestId('request-1');
            expect(result).toBeDefined();
            expect(result.requestId).toBe('request-1');
        });
        it('should return undefined for non-existent request ID', () => {
            const result = aggregate.findByRequestId('non-existent');
            expect(result).toBeUndefined();
        });
    });
    describe('Status-based Queries', () => {
        beforeEach(() => {
            const results = [
                analysis_result_factory_1.AnalysisResultFactory.createForTesting({ requestId: 'pending-1' }),
                analysis_result_factory_1.AnalysisResultFactory.createForTesting({ requestId: 'pending-2' }),
                analysis_result_factory_1.AnalysisResultFactory.createCompletedForTesting({ requestId: 'completed-1' }),
                analysis_result_factory_1.AnalysisResultFactory.createFailedForTesting({ requestId: 'failed-1' }),
            ];
            // Create a processing result
            const processingResult = analysis_result_factory_1.AnalysisResultFactory.createForTesting({ requestId: 'processing-1' });
            processingResult.startProcessing();
            results.push(processingResult);
            results.forEach(result => aggregate.addAnalysisResult(result));
        });
        it('should get pending results', () => {
            const results = aggregate.getPendingResults();
            expect(results).toHaveLength(2);
            expect(results.every(r => r.status === analysis_result_entity_1.AnalysisStatus.PENDING)).toBe(true);
        });
        it('should get processing results', () => {
            const results = aggregate.getProcessingResults();
            expect(results).toHaveLength(1);
            expect(results[0].status).toBe(analysis_result_entity_1.AnalysisStatus.PROCESSING);
        });
        it('should get completed results', () => {
            const results = aggregate.getCompletedResults();
            expect(results).toHaveLength(1);
            expect(results[0].status).toBe(analysis_result_entity_1.AnalysisStatus.COMPLETED);
        });
        it('should get failed results', () => {
            const results = aggregate.getFailedResults();
            expect(results).toHaveLength(1);
            expect(results[0].status).toBe(analysis_result_entity_1.AnalysisStatus.FAILED);
        });
        it('should get retryable failed results', () => {
            const results = aggregate.getRetryableFailedResults();
            expect(results).toHaveLength(1);
            expect(results[0].isRetryable()).toBe(true);
        });
        it('should get high confidence results', () => {
            const results = aggregate.getHighConfidenceResults();
            expect(results).toHaveLength(1);
            expect(results[0].hasHighConfidence()).toBe(true);
        });
    });
    describe('Bulk Operations', () => {
        beforeEach(() => {
            const results = [
                analysis_result_factory_1.AnalysisResultFactory.createForTesting({ requestId: 'bulk-1' }),
                analysis_result_factory_1.AnalysisResultFactory.createForTesting({ requestId: 'bulk-2' }),
                analysis_result_factory_1.AnalysisResultFactory.createCompletedForTesting({ requestId: 'bulk-3' }),
            ];
            // Create a processing result
            const processingResult = analysis_result_factory_1.AnalysisResultFactory.createForTesting({ requestId: 'bulk-processing' });
            processingResult.startProcessing();
            results.push(processingResult);
            results.forEach(result => aggregate.addAnalysisResult(result));
        });
        it('should cancel all active results', () => {
            const cancelledResults = aggregate.cancelAllActiveResults();
            expect(cancelledResults).toHaveLength(3); // 2 pending + 1 processing
            expect(cancelledResults.every(r => r.status === analysis_result_entity_1.AnalysisStatus.CANCELLED)).toBe(true);
        });
        it('should cancel results by query criteria', () => {
            const query = {
                status: analysis_result_entity_1.AnalysisStatus.PENDING,
            };
            const cancelledResults = aggregate.cancelResults(query);
            expect(cancelledResults).toHaveLength(2);
            expect(cancelledResults.every(r => r.status === analysis_result_entity_1.AnalysisStatus.CANCELLED)).toBe(true);
        });
    });
    describe('Statistics', () => {
        beforeEach(() => {
            const results = [
                analysis_result_factory_1.AnalysisResultFactory.createForTesting({
                    requestId: 'stats-1',
                    analysisType: analysis_result_entity_1.AnalysisType.CLASSIFICATION
                }),
                analysis_result_factory_1.AnalysisResultFactory.createForTesting({
                    requestId: 'stats-2',
                    analysisType: analysis_result_entity_1.AnalysisType.ANOMALY_DETECTION
                }),
                analysis_result_factory_1.AnalysisResultFactory.createCompletedForTesting({
                    requestId: 'stats-3',
                    analysisType: analysis_result_entity_1.AnalysisType.CLASSIFICATION
                }),
                analysis_result_factory_1.AnalysisResultFactory.createFailedForTesting({
                    requestId: 'stats-4',
                    analysisType: analysis_result_entity_1.AnalysisType.CLASSIFICATION
                }),
            ];
            results.forEach(result => aggregate.addAnalysisResult(result));
        });
        it('should calculate aggregate statistics', () => {
            const stats = aggregate.getStatistics();
            expect(stats.totalResults).toBe(4);
            expect(stats.statusDistribution[analysis_result_entity_1.AnalysisStatus.PENDING]).toBe(2);
            expect(stats.statusDistribution[analysis_result_entity_1.AnalysisStatus.COMPLETED]).toBe(1);
            expect(stats.statusDistribution[analysis_result_entity_1.AnalysisStatus.FAILED]).toBe(1);
            expect(stats.typeDistribution[analysis_result_entity_1.AnalysisType.CLASSIFICATION]).toBe(3);
            expect(stats.typeDistribution[analysis_result_entity_1.AnalysisType.ANOMALY_DETECTION]).toBe(1);
            expect(stats.successRate).toBe(0.25); // 1 successful out of 4
            expect(stats.retryableFailures).toBe(1);
            expect(stats.completionRate).toBe(0.5); // 2 terminal out of 4
        });
        it('should return empty statistics for empty aggregate', () => {
            const emptyAggregate = new analysis_result_aggregate_1.AnalysisResultAggregate();
            const stats = emptyAggregate.getStatistics();
            expect(stats.totalResults).toBe(0);
            expect(stats.averageConfidence).toBe(0);
            expect(stats.successRate).toBe(0);
        });
    });
    describe('Batch Operations', () => {
        it('should create batch of analysis results', () => {
            const requests = [
                {
                    requestId: 'batch-1',
                    modelId: unique_entity_id_value_object_1.UniqueEntityId.generate(),
                    analysisType: analysis_result_entity_1.AnalysisType.CLASSIFICATION,
                    inputData: {
                        data: { test: 'input' },
                        format: 'json',
                        size: 100,
                        checksum: 'checksum',
                        preprocessingApplied: [],
                        validationRules: [],
                    },
                },
                {
                    requestId: 'batch-2',
                    modelId: unique_entity_id_value_object_1.UniqueEntityId.generate(),
                    analysisType: analysis_result_entity_1.AnalysisType.ANOMALY_DETECTION,
                    inputData: {
                        data: { test: 'input' },
                        format: 'json',
                        size: 100,
                        checksum: 'checksum',
                        preprocessingApplied: [],
                        validationRules: [],
                    },
                },
            ];
            const batch = aggregate.createBatch(requests);
            expect(batch.results).toHaveLength(2);
            expect(batch.totalCount).toBe(2);
            expect(batch.pendingCount).toBe(2);
            expect(batch.successCount).toBe(0);
            expect(batch.failureCount).toBe(0);
        });
        it('should handle errors in batch creation', () => {
            // First create a result to cause duplicate error
            aggregate.createAnalysisResult({
                requestId: 'duplicate',
                modelId: unique_entity_id_value_object_1.UniqueEntityId.generate(),
                analysisType: analysis_result_entity_1.AnalysisType.CLASSIFICATION,
                inputData: {
                    data: { test: 'input' },
                    format: 'json',
                    size: 100,
                    checksum: 'checksum',
                    preprocessingApplied: [],
                    validationRules: [],
                },
            });
            const requests = [
                {
                    requestId: 'duplicate', // This will fail
                    modelId: unique_entity_id_value_object_1.UniqueEntityId.generate(),
                    analysisType: analysis_result_entity_1.AnalysisType.CLASSIFICATION,
                    inputData: {
                        data: { test: 'input' },
                        format: 'json',
                        size: 100,
                        checksum: 'checksum',
                        preprocessingApplied: [],
                        validationRules: [],
                    },
                },
                {
                    requestId: 'valid',
                    modelId: unique_entity_id_value_object_1.UniqueEntityId.generate(),
                    analysisType: analysis_result_entity_1.AnalysisType.ANOMALY_DETECTION,
                    inputData: {
                        data: { test: 'input' },
                        format: 'json',
                        size: 100,
                        checksum: 'checksum',
                        preprocessingApplied: [],
                        validationRules: [],
                    },
                },
            ];
            const batch = aggregate.createBatch(requests);
            expect(batch.results).toHaveLength(1); // Only the valid one
            expect(batch.totalCount).toBe(1);
        });
    });
    describe('Grouping and Sorting', () => {
        beforeEach(() => {
            const results = [
                analysis_result_factory_1.AnalysisResultFactory.createForTesting({
                    requestId: 'group-1',
                    analysisType: analysis_result_entity_1.AnalysisType.CLASSIFICATION
                }),
                analysis_result_factory_1.AnalysisResultFactory.createForTesting({
                    requestId: 'group-2',
                    analysisType: analysis_result_entity_1.AnalysisType.CLASSIFICATION
                }),
                analysis_result_factory_1.AnalysisResultFactory.createCompletedForTesting({
                    requestId: 'group-3',
                    analysisType: analysis_result_entity_1.AnalysisType.ANOMALY_DETECTION
                }),
            ];
            results.forEach(result => aggregate.addAnalysisResult(result));
        });
        it('should group by analysis type', () => {
            const groups = aggregate.groupByAnalysisType();
            expect(groups.size).toBe(2);
            expect(groups.get(analysis_result_entity_1.AnalysisType.CLASSIFICATION)).toHaveLength(2);
            expect(groups.get(analysis_result_entity_1.AnalysisType.ANOMALY_DETECTION)).toHaveLength(1);
        });
        it('should group by status', () => {
            const groups = aggregate.groupByStatus();
            expect(groups.size).toBe(2);
            expect(groups.get(analysis_result_entity_1.AnalysisStatus.PENDING)).toHaveLength(2);
            expect(groups.get(analysis_result_entity_1.AnalysisStatus.COMPLETED)).toHaveLength(1);
        });
        it('should get most recent results', () => {
            const results = aggregate.getMostRecentResults(2);
            expect(results).toHaveLength(2);
            // Results should be sorted by creation date (most recent first)
            expect(results[0].createdAt.getTime()).toBeGreaterThanOrEqual(results[1].createdAt.getTime());
        });
        it('should get results by confidence', () => {
            const results = aggregate.getResultsByConfidence();
            expect(results).toHaveLength(3);
            // Should be sorted by confidence (highest first)
            for (let i = 0; i < results.length - 1; i++) {
                expect(results[i].confidence).toBeGreaterThanOrEqual(results[i + 1].confidence);
            }
        });
        it('should get results by quality', () => {
            const results = aggregate.getResultsByQuality(2);
            expect(results).toHaveLength(2);
            // Should be sorted by quality score (highest first)
            for (let i = 0; i < results.length - 1; i++) {
                expect(results[i].getQualityScore()).toBeGreaterThanOrEqual(results[i + 1].getQualityScore());
            }
        });
    });
    describe('Relationship Queries', () => {
        it('should find by correlation ID', () => {
            const correlationId = 'test-correlation';
            const results = [
                analysis_result_factory_1.AnalysisResultFactory.createForTesting({
                    requestId: 'corr-1',
                    correlationId
                }),
                analysis_result_factory_1.AnalysisResultFactory.createForTesting({
                    requestId: 'corr-2',
                    correlationId
                }),
                analysis_result_factory_1.AnalysisResultFactory.createForTesting({
                    requestId: 'corr-3',
                    correlationId: 'different'
                }),
            ];
            results.forEach(result => aggregate.addAnalysisResult(result));
            const correlatedResults = aggregate.findByCorrelationId(correlationId);
            expect(correlatedResults).toHaveLength(2);
            expect(correlatedResults.every(r => r.correlationId === correlationId)).toBe(true);
        });
        it('should get child results', () => {
            const parentId = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const parent = analysis_result_factory_1.AnalysisResultFactory.createForTesting({
                requestId: 'parent'
            });
            const child1 = analysis_result_factory_1.AnalysisResultFactory.createForTesting({
                requestId: 'child-1',
                parentAnalysisId: parentId
            });
            const child2 = analysis_result_factory_1.AnalysisResultFactory.createForTesting({
                requestId: 'child-2',
                parentAnalysisId: parentId
            });
            [parent, child1, child2].forEach(result => aggregate.addAnalysisResult(result));
            const childResults = aggregate.getChildResults(parentId);
            expect(childResults).toHaveLength(2);
            expect(childResults.every(r => r.parentAnalysisId?.equals(parentId))).toBe(true);
        });
        it('should get results by model ID', () => {
            const modelId = unique_entity_id_value_object_1.UniqueEntityId.generate();
            const results = [
                analysis_result_factory_1.AnalysisResultFactory.createForTesting({
                    requestId: 'model-1',
                    modelId
                }),
                analysis_result_factory_1.AnalysisResultFactory.createForTesting({
                    requestId: 'model-2',
                    modelId
                }),
                analysis_result_factory_1.AnalysisResultFactory.createForTesting({
                    requestId: 'model-3'
                }),
            ];
            results.forEach(result => aggregate.addAnalysisResult(result));
            const modelResults = aggregate.getResultsByModel(modelId);
            expect(modelResults).toHaveLength(2);
            expect(modelResults.every(r => r.modelId.equals(modelId))).toBe(true);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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