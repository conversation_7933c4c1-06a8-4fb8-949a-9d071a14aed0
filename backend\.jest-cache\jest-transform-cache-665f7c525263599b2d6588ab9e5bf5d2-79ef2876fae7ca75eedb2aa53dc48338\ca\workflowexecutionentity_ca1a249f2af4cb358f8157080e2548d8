3e05dc4f368a88982c452636f4afa7f1
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowExecution = void 0;
const typeorm_1 = require("typeorm");
const notification_workflow_entity_1 = require("./notification-workflow.entity");
const workflow_execution_context_entity_1 = require("./workflow-execution-context.entity");
/**
 * Workflow Execution Entity
 *
 * Represents a single execution instance of a notification workflow
 * with comprehensive tracking and state management.
 */
let WorkflowExecution = class WorkflowExecution {
    // Computed properties
    get isRunning() {
        return this.status === 'running';
    }
    get isCompleted() {
        return this.status === 'completed';
    }
    get isFailed() {
        return this.status === 'failed';
    }
    get isCancelled() {
        return this.status === 'cancelled';
    }
    get isTimedOut() {
        return this.status === 'timeout';
    }
    get isFinished() {
        return ['completed', 'failed', 'cancelled', 'timeout'].includes(this.status);
    }
    get progress() {
        if (this.totalSteps === 0)
            return 0;
        return (this.stepsCompleted / this.totalSteps) * 100;
    }
    get executionTime() {
        if (!this.startedAt)
            return 0;
        const endTime = this.completedAt || new Date();
        return endTime.getTime() - this.startedAt.getTime();
    }
    get isOverdue() {
        return this.timeoutAt && new Date() > this.timeoutAt;
    }
    get successRate() {
        if (this.totalSteps === 0)
            return 0;
        return (this.stepsCompleted / this.totalSteps) * 100;
    }
    /**
     * Get execution summary
     */
    getExecutionSummary() {
        return {
            id: this.id,
            workflowId: this.workflowId,
            status: this.status,
            triggerType: this.triggerType,
            triggeredBy: this.triggeredBy,
            startedAt: this.startedAt,
            completedAt: this.completedAt,
            duration: this.duration,
            progress: this.progress,
            stepsCompleted: this.stepsCompleted,
            totalSteps: this.totalSteps,
            currentStep: this.currentStep,
            priority: this.priority,
            retryCount: this.retryCount,
            correlationId: this.correlationId,
            traceId: this.traceId,
        };
    }
    /**
     * Get execution metrics
     */
    getExecutionMetrics() {
        return {
            executionTime: this.executionTime,
            successRate: this.successRate,
            progress: this.progress,
            stepsPerMinute: this.duration > 0 ? (this.stepsCompleted / (this.duration / 60000)) : 0,
            isOverdue: this.isOverdue,
            retryCount: this.retryCount,
        };
    }
    /**
     * Get alert information from input
     */
    getAlertInfo() {
        return this.input?.alert || null;
    }
    /**
     * Get user information from input
     */
    getUserInfo() {
        return this.input?.user || null;
    }
    /**
     * Get metric values from input
     */
    getMetricValues() {
        return this.input?.metricValues || null;
    }
    /**
     * Get escalation level from context
     */
    getEscalationLevel() {
        return this.context?.escalationLevel || 1;
    }
    /**
     * Check if execution is acknowledged
     */
    isAcknowledged() {
        return this.context?.acknowledged || false;
    }
    /**
     * Get oncall schedule from context
     */
    getOncallSchedule() {
        return this.context?.oncallSchedule || null;
    }
    /**
     * Get correlated alerts
     */
    getCorrelatedAlerts() {
        return this.context?.correlatedAlerts || [];
    }
    /**
     * Get execution variables
     */
    getVariables() {
        return this.context?.variables || {};
    }
    /**
     * Set execution variable
     */
    setVariable(key, value) {
        if (!this.context) {
            this.context = {};
        }
        if (!this.context.variables) {
            this.context.variables = {};
        }
        this.context.variables[key] = value;
    }
    /**
     * Get execution variable
     */
    getVariable(key) {
        return this.context?.variables?.[key];
    }
    /**
     * Check if execution has specific tag
     */
    hasTag(tag) {
        return this.metadata?.testing?.testMode || false;
    }
    /**
     * Check if execution is in test mode
     */
    isTestMode() {
        return this.metadata?.testing?.testMode || false;
    }
    /**
     * Check if execution is validate only
     */
    isValidateOnly() {
        return this.metadata?.testing?.validateOnly || false;
    }
    /**
     * Get step overrides for specific step
     */
    getStepOverrides(stepId) {
        return this.metadata?.stepOverrides?.[stepId] || {};
    }
    /**
     * Get template variables
     */
    getTemplateVariables() {
        return this.metadata?.templateVariables || {};
    }
    /**
     * Get integration config for specific integration
     */
    getIntegrationConfig(integration) {
        return this.metadata?.integrationConfig?.[integration] || {};
    }
    /**
     * Calculate estimated completion time
     */
    getEstimatedCompletionTime() {
        if (!this.startedAt || this.isFinished) {
            return null;
        }
        const elapsedTime = Date.now() - this.startedAt.getTime();
        const progressRate = this.progress / 100;
        if (progressRate === 0) {
            return null;
        }
        const estimatedTotalTime = elapsedTime / progressRate;
        const remainingTime = estimatedTotalTime - elapsedTime;
        return new Date(Date.now() + remainingTime);
    }
    /**
     * Check if execution should be retried
     */
    shouldRetry(maxRetries = 3) {
        return this.isFailed && this.retryCount < maxRetries;
    }
    /**
     * Get execution status for display
     */
    getDisplayStatus() {
        switch (this.status) {
            case 'pending': return 'Pending';
            case 'running': return 'Running';
            case 'completed': return 'Completed';
            case 'failed': return 'Failed';
            case 'cancelled': return 'Cancelled';
            case 'timeout': return 'Timed Out';
            default: return 'Unknown';
        }
    }
};
exports.WorkflowExecution = WorkflowExecution;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], WorkflowExecution.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'workflow_id' }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], WorkflowExecution.prototype, "workflowId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'status', default: 'pending' }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], WorkflowExecution.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'input', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], WorkflowExecution.prototype, "input", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'context', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], WorkflowExecution.prototype, "context", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'result', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_a = typeof Record !== "undefined" && Record) === "function" ? _a : Object)
], WorkflowExecution.prototype, "result", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'error', type: 'text', nullable: true }),
    __metadata("design:type", String)
], WorkflowExecution.prototype, "error", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'triggered_by' }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], WorkflowExecution.prototype, "triggeredBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'trigger_type', default: 'manual' }),
    __metadata("design:type", String)
], WorkflowExecution.prototype, "triggerType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'trigger_data', type: 'jsonb', nullable: true }),
    __metadata("design:type", typeof (_b = typeof Record !== "undefined" && Record) === "function" ? _b : Object)
], WorkflowExecution.prototype, "triggerData", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'started_at', type: 'timestamp', nullable: true }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], WorkflowExecution.prototype, "startedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'completed_at', type: 'timestamp', nullable: true }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], WorkflowExecution.prototype, "completedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'duration', type: 'bigint', nullable: true }),
    __metadata("design:type", Number)
], WorkflowExecution.prototype, "duration", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'steps_completed', default: 0 }),
    __metadata("design:type", Number)
], WorkflowExecution.prototype, "stepsCompleted", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'total_steps', default: 0 }),
    __metadata("design:type", Number)
], WorkflowExecution.prototype, "totalSteps", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'current_step', nullable: true }),
    __metadata("design:type", String)
], WorkflowExecution.prototype, "currentStep", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'priority', default: 'medium' }),
    __metadata("design:type", String)
], WorkflowExecution.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'timeout_at', type: 'timestamp', nullable: true }),
    __metadata("design:type", typeof (_e = typeof Date !== "undefined" && Date) === "function" ? _e : Object)
], WorkflowExecution.prototype, "timeoutAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'retry_count', default: 0 }),
    __metadata("design:type", Number)
], WorkflowExecution.prototype, "retryCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'correlation_id', nullable: true }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], WorkflowExecution.prototype, "correlationId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'trace_id', nullable: true }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], WorkflowExecution.prototype, "traceId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'metadata', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], WorkflowExecution.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", typeof (_f = typeof Date !== "undefined" && Date) === "function" ? _f : Object)
], WorkflowExecution.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => notification_workflow_entity_1.NotificationWorkflow, workflow => workflow.executions),
    (0, typeorm_1.JoinColumn)({ name: 'workflow_id' }),
    __metadata("design:type", typeof (_g = typeof notification_workflow_entity_1.NotificationWorkflow !== "undefined" && notification_workflow_entity_1.NotificationWorkflow) === "function" ? _g : Object)
], WorkflowExecution.prototype, "workflow", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => workflow_execution_context_entity_1.WorkflowExecutionContext, context => context.execution),
    __metadata("design:type", Array)
], WorkflowExecution.prototype, "contexts", void 0);
exports.WorkflowExecution = WorkflowExecution = __decorate([
    (0, typeorm_1.Entity)('workflow_executions'),
    (0, typeorm_1.Index)(['workflowId', 'status']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['triggeredBy']),
    (0, typeorm_1.Index)(['startedAt']),
    (0, typeorm_1.Index)(['completedAt'])
], WorkflowExecution);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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