f6f209677a5247157b93119290bbe0c5
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var DataSerializerService_1;
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataSerializerService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const zlib = __importStar(require("zlib"));
const util_1 = require("util");
/**
 * Data Serializer Service
 *
 * Handles efficient data transformation for AI operations.
 * Supports multiple serialization formats (JSON, Protocol Buffers),
 * data compression, and optimization for high-performance AI workflows.
 */
let DataSerializerService = DataSerializerService_1 = class DataSerializerService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(DataSerializerService_1.name);
        // Promisified compression functions
        this.gzip = (0, util_1.promisify)(zlib.gzip);
        this.gunzip = (0, util_1.promisify)(zlib.gunzip);
        this.deflate = (0, util_1.promisify)(zlib.deflate);
        this.inflate = (0, util_1.promisify)(zlib.inflate);
        this.compressionEnabled = this.configService.get('ai.serialization.compression.enabled', true);
        this.compressionLevel = this.configService.get('ai.serialization.compression.level', 6);
        this.compressionThreshold = this.configService.get('ai.serialization.compression.threshold', 1024);
    }
    /**
     * Serializes data to JSON format with optional compression
     */
    async serializeToJson(data, options = {}) {
        this.logger.debug('Serializing data to JSON format');
        try {
            const startTime = Date.now();
            // Convert to JSON string
            const jsonString = JSON.stringify(data, options.replacer, options.space);
            const originalSize = Buffer.byteLength(jsonString, 'utf8');
            let serializedBuffer;
            let compressed = false;
            // Apply compression if enabled and data exceeds threshold
            if (this.shouldCompress(originalSize, options)) {
                serializedBuffer = await this.compressData(Buffer.from(jsonString, 'utf8'), options.compressionType);
                compressed = true;
                this.logger.debug(`JSON data compressed: ${originalSize} -> ${serializedBuffer.length} bytes`);
            }
            else {
                serializedBuffer = Buffer.from(jsonString, 'utf8');
            }
            const processingTime = Date.now() - startTime;
            return {
                data: serializedBuffer,
                format: 'json',
                compressed,
                compressionType: compressed ? (options.compressionType || 'gzip') : undefined,
                originalSize,
                compressedSize: serializedBuffer.length,
                compressionRatio: compressed ? originalSize / serializedBuffer.length : 1,
                processingTime,
                metadata: {
                    encoding: 'utf8',
                    ...options.metadata,
                },
            };
        }
        catch (error) {
            this.logger.error('Failed to serialize data to JSON', error);
            throw new SerializationError(`JSON serialization failed: ${error.message}`, 'json', error);
        }
    }
    /**
     * Deserializes JSON data with optional decompression
     */
    async deserializeFromJson(serializedData, options = {}) {
        this.logger.debug('Deserializing data from JSON format');
        try {
            const startTime = Date.now();
            let buffer;
            let compressed = false;
            let compressionType;
            // Handle different input types
            if (Buffer.isBuffer(serializedData)) {
                buffer = serializedData;
            }
            else {
                buffer = serializedData.data;
                compressed = serializedData.compressed || false;
                compressionType = serializedData.compressionType;
            }
            // Decompress if needed
            if (compressed && compressionType) {
                buffer = await this.decompressData(buffer, compressionType);
                this.logger.debug(`JSON data decompressed: ${buffer.length} bytes`);
            }
            // Parse JSON
            const jsonString = buffer.toString('utf8');
            const data = JSON.parse(jsonString, options.reviver);
            const processingTime = Date.now() - startTime;
            this.logger.debug(`JSON deserialization completed in ${processingTime}ms`);
            return data;
        }
        catch (error) {
            this.logger.error('Failed to deserialize JSON data', error);
            throw new SerializationError(`JSON deserialization failed: ${error.message}`, 'json', error);
        }
    }
    /**
     * Serializes data to Protocol Buffers format (mock implementation)
     */
    async serializeToProtobuf(data, schema, options = {}) {
        this.logger.debug(`Serializing data to Protocol Buffers format with schema: ${schema}`);
        try {
            const startTime = Date.now();
            // Mock Protocol Buffers serialization
            // In real implementation, this would use protobuf.js or similar library
            const mockProtobufData = this.mockProtobufSerialization(data, schema);
            const originalSize = mockProtobufData.length;
            let serializedBuffer;
            let compressed = false;
            // Apply compression if enabled and data exceeds threshold
            if (this.shouldCompress(originalSize, options)) {
                serializedBuffer = await this.compressData(mockProtobufData, options.compressionType);
                compressed = true;
                this.logger.debug(`Protobuf data compressed: ${originalSize} -> ${serializedBuffer.length} bytes`);
            }
            else {
                serializedBuffer = mockProtobufData;
            }
            const processingTime = Date.now() - startTime;
            return {
                data: serializedBuffer,
                format: 'protobuf',
                schema,
                compressed,
                compressionType: compressed ? (options.compressionType || 'gzip') : undefined,
                originalSize,
                compressedSize: serializedBuffer.length,
                compressionRatio: compressed ? originalSize / serializedBuffer.length : 1,
                processingTime,
                metadata: {
                    schema,
                    ...options.metadata,
                },
            };
        }
        catch (error) {
            this.logger.error('Failed to serialize data to Protocol Buffers', error);
            throw new SerializationError(`Protobuf serialization failed: ${error.message}`, 'protobuf', error);
        }
    }
    /**
     * Deserializes Protocol Buffers data (mock implementation)
     */
    async deserializeFromProtobuf(serializedData, schema, options = {}) {
        this.logger.debug(`Deserializing data from Protocol Buffers format with schema: ${schema}`);
        try {
            const startTime = Date.now();
            let buffer;
            let compressed = false;
            let compressionType;
            // Handle different input types
            if (Buffer.isBuffer(serializedData)) {
                buffer = serializedData;
            }
            else {
                buffer = serializedData.data;
                compressed = serializedData.compressed || false;
                compressionType = serializedData.compressionType;
            }
            // Decompress if needed
            if (compressed && compressionType) {
                buffer = await this.decompressData(buffer, compressionType);
                this.logger.debug(`Protobuf data decompressed: ${buffer.length} bytes`);
            }
            // Mock Protocol Buffers deserialization
            const data = this.mockProtobufDeserialization(buffer, schema);
            const processingTime = Date.now() - startTime;
            this.logger.debug(`Protobuf deserialization completed in ${processingTime}ms`);
            return data;
        }
        catch (error) {
            this.logger.error('Failed to deserialize Protocol Buffers data', error);
            throw new SerializationError(`Protobuf deserialization failed: ${error.message}`, 'protobuf', error);
        }
    }
    /**
     * Serializes data to binary format with custom encoding
     */
    async serializeToBinary(data, encoding = 'base64', options = {}) {
        this.logger.debug(`Serializing data to binary format with encoding: ${encoding}`);
        try {
            const startTime = Date.now();
            // Convert data to buffer
            let buffer;
            if (Buffer.isBuffer(data)) {
                buffer = data;
            }
            else if (typeof data === 'string') {
                buffer = Buffer.from(data, 'utf8');
            }
            else {
                buffer = Buffer.from(JSON.stringify(data), 'utf8');
            }
            const originalSize = buffer.length;
            let serializedBuffer;
            let compressed = false;
            // Apply compression if enabled and data exceeds threshold
            if (this.shouldCompress(originalSize, options)) {
                serializedBuffer = await this.compressData(buffer, options.compressionType);
                compressed = true;
                this.logger.debug(`Binary data compressed: ${originalSize} -> ${serializedBuffer.length} bytes`);
            }
            else {
                serializedBuffer = buffer;
            }
            const processingTime = Date.now() - startTime;
            return {
                data: serializedBuffer,
                format: 'binary',
                compressed,
                compressionType: compressed ? (options.compressionType || 'gzip') : undefined,
                originalSize,
                compressedSize: serializedBuffer.length,
                compressionRatio: compressed ? originalSize / serializedBuffer.length : 1,
                processingTime,
                metadata: {
                    encoding,
                    ...options.metadata,
                },
            };
        }
        catch (error) {
            this.logger.error('Failed to serialize data to binary format', error);
            throw new SerializationError(`Binary serialization failed: ${error.message}`, 'binary', error);
        }
    }
    /**
     * Deserializes binary data with custom encoding
     */
    async deserializeFromBinary(serializedData, encoding = 'base64', options = {}) {
        this.logger.debug(`Deserializing data from binary format with encoding: ${encoding}`);
        try {
            const startTime = Date.now();
            let buffer;
            let compressed = false;
            let compressionType;
            // Handle different input types
            if (Buffer.isBuffer(serializedData)) {
                buffer = serializedData;
            }
            else {
                buffer = serializedData.data;
                compressed = serializedData.compressed || false;
                compressionType = serializedData.compressionType;
            }
            // Decompress if needed
            if (compressed && compressionType) {
                buffer = await this.decompressData(buffer, compressionType);
                this.logger.debug(`Binary data decompressed: ${buffer.length} bytes`);
            }
            const processingTime = Date.now() - startTime;
            this.logger.debug(`Binary deserialization completed in ${processingTime}ms`);
            return buffer;
        }
        catch (error) {
            this.logger.error('Failed to deserialize binary data', error);
            throw new SerializationError(`Binary deserialization failed: ${error.message}`, 'binary', error);
        }
    }
    /**
     * Compresses data using specified compression algorithm
     */
    async compressData(data, compressionType = 'gzip') {
        this.logger.debug(`Compressing data using ${compressionType}`);
        try {
            const options = { level: this.compressionLevel };
            switch (compressionType) {
                case 'gzip':
                    return await this.gzip(data, options);
                case 'deflate':
                    return await this.deflate(data, options);
                default:
                    throw new Error(`Unsupported compression type: ${compressionType}`);
            }
        }
        catch (error) {
            this.logger.error(`Failed to compress data using ${compressionType}`, error);
            throw new SerializationError(`Compression failed: ${error.message}`, compressionType, error);
        }
    }
    /**
     * Decompresses data using specified compression algorithm
     */
    async decompressData(data, compressionType) {
        this.logger.debug(`Decompressing data using ${compressionType}`);
        try {
            switch (compressionType) {
                case 'gzip':
                    return await this.gunzip(data);
                case 'deflate':
                    return await this.inflate(data);
                default:
                    throw new Error(`Unsupported compression type: ${compressionType}`);
            }
        }
        catch (error) {
            this.logger.error(`Failed to decompress data using ${compressionType}`, error);
            throw new SerializationError(`Decompression failed: ${error.message}`, compressionType, error);
        }
    }
    /**
     * Batch serializes multiple data items
     */
    async batchSerialize(items, globalOptions = {}) {
        this.logger.debug(`Batch serializing ${items.length} items`);
        try {
            const startTime = Date.now();
            const results = [];
            const errors = [];
            for (let i = 0; i < items.length; i++) {
                const item = items[i];
                const options = { ...globalOptions, ...item.options };
                try {
                    let result;
                    switch (item.format) {
                        case 'json':
                            result = await this.serializeToJson(item.data, options);
                            break;
                        case 'protobuf':
                            result = await this.serializeToProtobuf(item.data, options.schema || 'default', options);
                            break;
                        case 'binary':
                            result = await this.serializeToBinary(item.data, options.encoding || 'base64', options);
                            break;
                        default:
                            throw new Error(`Unsupported format: ${item.format}`);
                    }
                    results.push(result);
                }
                catch (error) {
                    errors.push({ index: i, error });
                    results.push(null); // Placeholder for failed serialization
                }
            }
            const processingTime = Date.now() - startTime;
            const totalOriginalSize = results.filter(r => r).reduce((sum, r) => sum + r.originalSize, 0);
            const totalCompressedSize = results.filter(r => r).reduce((sum, r) => sum + r.compressedSize, 0);
            return {
                results,
                errors,
                summary: {
                    total: items.length,
                    successful: results.filter(r => r).length,
                    failed: errors.length,
                    totalOriginalSize,
                    totalCompressedSize,
                    overallCompressionRatio: totalOriginalSize > 0 ? totalOriginalSize / totalCompressedSize : 1,
                    processingTime,
                },
            };
        }
        catch (error) {
            this.logger.error('Failed to batch serialize data', error);
            throw new SerializationError(`Batch serialization failed: ${error.message}`, 'batch', error);
        }
    }
    /**
     * Batch deserializes multiple data items
     */
    async batchDeserialize(items, globalOptions = {}) {
        this.logger.debug(`Batch deserializing ${items.length} items`);
        try {
            const startTime = Date.now();
            const results = [];
            const errors = [];
            for (let i = 0; i < items.length; i++) {
                const item = items[i];
                const options = { ...globalOptions, ...item.options };
                try {
                    let result;
                    switch (item.format) {
                        case 'json':
                            result = await this.deserializeFromJson(item.data, options);
                            break;
                        case 'protobuf':
                            result = await this.deserializeFromProtobuf(item.data, options.schema || 'default', options);
                            break;
                        case 'binary':
                            result = await this.deserializeFromBinary(item.data, options.encoding || 'base64', options);
                            break;
                        default:
                            throw new Error(`Unsupported format: ${item.format}`);
                    }
                    results.push(result);
                }
                catch (error) {
                    errors.push({ index: i, error });
                    results.push(null); // Placeholder for failed deserialization
                }
            }
            const processingTime = Date.now() - startTime;
            return {
                results,
                errors,
                summary: {
                    total: items.length,
                    successful: results.filter(r => r !== null).length,
                    failed: errors.length,
                    processingTime,
                },
            };
        }
        catch (error) {
            this.logger.error('Failed to batch deserialize data', error);
            throw new SerializationError(`Batch deserialization failed: ${error.message}`, 'batch', error);
        }
    }
    /**
     * Gets serialization statistics
     */
    getSerializationStats() {
        return {
            compressionEnabled: this.compressionEnabled,
            compressionLevel: this.compressionLevel,
            compressionThreshold: this.compressionThreshold,
            supportedFormats: ['json', 'protobuf', 'binary'],
            supportedCompressionTypes: ['gzip', 'deflate'],
            supportedEncodings: ['base64', 'hex', 'utf8'],
        };
    }
    // Private helper methods
    shouldCompress(dataSize, options) {
        if (options.forceCompression !== undefined) {
            return options.forceCompression;
        }
        return this.compressionEnabled && dataSize >= this.compressionThreshold;
    }
    mockProtobufSerialization(data, schema) {
        // Mock Protocol Buffers serialization
        // In real implementation, this would use protobuf.js or similar library
        const mockData = {
            schema,
            data: JSON.stringify(data),
            timestamp: Date.now(),
        };
        return Buffer.from(JSON.stringify(mockData), 'utf8');
    }
    mockProtobufDeserialization(buffer, schema) {
        // Mock Protocol Buffers deserialization
        // In real implementation, this would use protobuf.js or similar library
        const mockData = JSON.parse(buffer.toString('utf8'));
        if (mockData.schema !== schema) {
            throw new Error(`Schema mismatch: expected ${schema}, got ${mockData.schema}`);
        }
        return JSON.parse(mockData.data);
    }
};
exports.DataSerializerService = DataSerializerService;
exports.DataSerializerService = DataSerializerService = DataSerializerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], DataSerializerService);
class SerializationError extends Error {
    constructor(message, format, originalError) {
        super(message);
        this.format = format;
        this.originalError = originalError;
        this.name = 'SerializationError';
    }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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