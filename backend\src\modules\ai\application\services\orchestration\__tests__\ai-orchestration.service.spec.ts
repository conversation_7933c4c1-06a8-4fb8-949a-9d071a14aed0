import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { EventBus, CommandBus, QueryBus } from '@nestjs/cqrs';
import { AiOrchestrationService } from '../ai-orchestration.service';
import { ModelSelectionService } from '../model-selection.service';
import { LoadBalancerService } from '../load-balancer.service';
import { CircuitBreakerService } from '../../resilience/circuit-breaker.service';
import { AiCacheService } from '../../caching/ai-cache.service';
import { MetricsAdapter } from '../../../../infrastructure/adapters/metrics.adapter';
import { 
  AI_MODEL_PROVIDER,
  AiModelProvider 
} from '../../../../domain/services/ai-model-provider.interface';
import { 
  PREDICTION_ENGINE,
  PredictionEngine 
} from '../../../../domain/services/prediction-engine.interface';

describe('AiOrchestrationService', () => {
  let service: AiOrchestrationService;
  let mockAiModelProvider: jest.Mocked<AiModelProvider>;
  let mockPredictionEngine: jest.Mocked<PredictionEngine>;
  let mockModelSelectionService: jest.Mocked<ModelSelectionService>;
  let mockLoadBalancerService: jest.Mocked<LoadBalancerService>;
  let mockCircuitBreakerService: jest.Mocked<CircuitBreakerService>;
  let mockCacheService: jest.Mocked<AiCacheService>;
  let mockMetricsAdapter: jest.Mocked<MetricsAdapter>;
  let mockEventBus: jest.Mocked<EventBus>;
  let mockCommandBus: jest.Mocked<CommandBus>;
  let mockQueryBus: jest.Mocked<QueryBus>;
  let mockConfigService: jest.Mocked<ConfigService>;

  beforeEach(async () => {
    // Create mocks
    mockAiModelProvider = {
      analyze: jest.fn(),
    } as any;

    mockPredictionEngine = {
      predict: jest.fn(),
    } as any;

    mockModelSelectionService = {
      selectOptimalModel: jest.fn(),
      selectRealTimeModel: jest.fn(),
    } as any;

    mockLoadBalancerService = {
      getAvailableProviders: jest.fn(),
      checkProviderHealth: jest.fn(),
    } as any;

    mockCircuitBreakerService = {
      execute: jest.fn(),
      getStatus: jest.fn(),
    } as any;

    mockCacheService = {
      get: jest.fn(),
      set: jest.fn().mockResolvedValue(undefined),
      checkHealth: jest.fn(),
    } as any;

    mockMetricsAdapter = {
      recordAiOperation: jest.fn(),
    } as any;

    mockEventBus = {
      publish: jest.fn(),
    } as any;

    mockCommandBus = {
      execute: jest.fn(),
    } as any;

    mockQueryBus = {
      execute: jest.fn(),
    } as any;

    mockConfigService = {
      get: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AiOrchestrationService,
        {
          provide: AI_MODEL_PROVIDER,
          useValue: mockAiModelProvider,
        },
        {
          provide: PREDICTION_ENGINE,
          useValue: mockPredictionEngine,
        },
        {
          provide: ModelSelectionService,
          useValue: mockModelSelectionService,
        },
        {
          provide: LoadBalancerService,
          useValue: mockLoadBalancerService,
        },
        {
          provide: CircuitBreakerService,
          useValue: mockCircuitBreakerService,
        },
        {
          provide: AiCacheService,
          useValue: mockCacheService,
        },
        {
          provide: MetricsAdapter,
          useValue: mockMetricsAdapter,
        },
        {
          provide: EventBus,
          useValue: mockEventBus,
        },
        {
          provide: CommandBus,
          useValue: mockCommandBus,
        },
        {
          provide: QueryBus,
          useValue: mockQueryBus,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<AiOrchestrationService>(AiOrchestrationService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('orchestrateAnalysis', () => {
    const mockRequest = {
      id: 'test-request-1',
      type: 'threat-analysis',
      data: { content: 'test data' },
      priority: 'high' as const,
    };

    const mockModelConfig = {
      providerType: 'openai',
      modelId: 'gpt-4',
      parameters: {},
    };

    const mockProviders = [
      {
        id: 'provider-1',
        type: 'openai',
        status: 'healthy',
        load: 0.5,
      },
    ];

    const mockAnalysisResult = {
      id: 'result-1',
      result: { threat_detected: true, confidence: 0.95 },
      confidence: 0.95,
      metadata: { model: 'gpt-4' },
      timestamp: new Date(),
    };

    it('should orchestrate analysis successfully with cache miss', async () => {
      // Arrange
      mockCacheService.get.mockResolvedValue(null);
      mockModelSelectionService.selectOptimalModel.mockResolvedValue(mockModelConfig);
      mockLoadBalancerService.getAvailableProviders.mockResolvedValue(mockProviders);
      mockCircuitBreakerService.execute.mockResolvedValue(mockAnalysisResult);
      mockCacheService.set.mockResolvedValue(undefined);
      mockConfigService.get.mockReturnValue(3600); // Default cache TTL

      // Act
      const result = await service.orchestrateAnalysis(mockRequest);

      // Assert
      expect(result).toEqual(mockAnalysisResult);
      expect(mockCacheService.get).toHaveBeenCalledWith(expect.any(String));
      expect(mockModelSelectionService.selectOptimalModel).toHaveBeenCalledWith(mockRequest);
      expect(mockLoadBalancerService.getAvailableProviders).toHaveBeenCalledWith('openai');
      expect(mockCircuitBreakerService.execute).toHaveBeenCalled();
      expect(mockCacheService.set).toHaveBeenCalledWith(
        expect.any(String),
        mockAnalysisResult,
        3600
      );
      expect(mockMetricsAdapter.recordAiOperation).toHaveBeenCalledWith(
        'success',
        expect.any(Number),
        expect.any(String)
      );
    });

    it('should return cached result when available', async () => {
      // Arrange
      mockCacheService.get.mockResolvedValue(mockAnalysisResult);

      // Act
      const result = await service.orchestrateAnalysis(mockRequest);

      // Assert
      expect(result).toEqual(mockAnalysisResult);
      expect(mockCacheService.get).toHaveBeenCalledWith(expect.any(String));
      expect(mockModelSelectionService.selectOptimalModel).not.toHaveBeenCalled();
      expect(mockMetricsAdapter.recordAiOperation).toHaveBeenCalledWith(
        'cache_hit',
        expect.any(Number),
        expect.any(String)
      );
    });

    it('should handle provider failures with fallback', async () => {
      // Arrange
      const multipleProviders = [
        { id: 'provider-1', type: 'openai', status: 'healthy', load: 0.5 },
        { id: 'provider-2', type: 'openai', status: 'healthy', load: 0.3 },
      ];

      mockCacheService.get.mockResolvedValue(null);
      mockModelSelectionService.selectOptimalModel.mockResolvedValue(mockModelConfig);
      mockLoadBalancerService.getAvailableProviders.mockResolvedValue(multipleProviders);
      
      // First provider fails, second succeeds
      mockCircuitBreakerService.execute
        .mockRejectedValueOnce(new Error('Provider 1 failed'))
        .mockResolvedValueOnce(mockAnalysisResult);

      // Act
      const result = await service.orchestrateAnalysis(mockRequest);

      // Assert
      expect(result).toEqual(mockAnalysisResult);
      expect(mockCircuitBreakerService.execute).toHaveBeenCalledTimes(2);
    });

    it('should throw error when all providers fail', async () => {
      // Arrange
      mockCacheService.get.mockResolvedValue(null);
      mockModelSelectionService.selectOptimalModel.mockResolvedValue(mockModelConfig);
      mockLoadBalancerService.getAvailableProviders.mockResolvedValue(mockProviders);
      mockCircuitBreakerService.execute.mockRejectedValue(new Error('All providers failed'));

      // Act & Assert
      await expect(service.orchestrateAnalysis(mockRequest)).rejects.toThrow('Analysis failed');
      expect(mockMetricsAdapter.recordAiOperation).toHaveBeenCalledWith(
        'error',
        expect.any(Number),
        expect.any(String)
      );
    });

    it('should handle configuration values correctly', async () => {
      // Arrange
      mockConfigService.get.mockReturnValue(7200); // 2 hours cache TTL
      mockCacheService.get.mockResolvedValue(null);
      mockModelSelectionService.selectOptimalModel.mockResolvedValue(mockModelConfig);
      mockLoadBalancerService.getAvailableProviders.mockResolvedValue(mockProviders);
      mockCircuitBreakerService.execute.mockResolvedValue(mockAnalysisResult);

      // Act
      await service.orchestrateAnalysis(mockRequest);

      // Assert
      expect(mockConfigService.get).toHaveBeenCalledWith('ai.cacheTtl', 3600);
      expect(mockCacheService.set).toHaveBeenCalledWith(
        expect.any(String),
        mockAnalysisResult,
        7200
      );
    });
  });

  describe('orchestrateBatchAnalysis', () => {
    const mockRequests = [
      {
        id: 'batch-request-1',
        type: 'threat-analysis',
        data: { content: 'test data 1' },
      },
      {
        id: 'batch-request-2',
        type: 'threat-analysis',
        data: { content: 'test data 2' },
      },
    ];

    it('should process batch requests successfully', async () => {
      // Arrange
      mockConfigService.get.mockReturnValue(2); // Concurrency limit
      
      // Mock model selection for batch processing
      mockModelSelectionService.selectOptimalModel.mockResolvedValue({
        providerType: 'openai',
        modelId: 'gpt-4',
        parameters: {},
      });
      
      // Mock individual analysis calls
      jest.spyOn(service, 'orchestrateAnalysis')
        .mockResolvedValueOnce({
          id: 'result-1',
          result: { threat_detected: true },
          confidence: 0.9,
          metadata: {},
          timestamp: new Date(),
        })
        .mockResolvedValueOnce({
          id: 'result-2',
          result: { threat_detected: false },
          confidence: 0.8,
          metadata: {},
          timestamp: new Date(),
        });

      // Act
      const results = await service.orchestrateBatchAnalysis(mockRequests);

      // Assert
      expect(results).toHaveLength(2);
      expect(results[0].id).toBe('result-1');
      expect(results[1].id).toBe('result-2');
      expect(service.orchestrateAnalysis).toHaveBeenCalledTimes(2);
    });

    it('should handle batch processing errors gracefully', async () => {
      // Arrange
      // Mock model selection to fail, which should cause the batch to return empty results
      mockModelSelectionService.selectOptimalModel.mockRejectedValue(new Error('Model selection failed'));

      // Act
      const results = await service.orchestrateBatchAnalysis(mockRequests);

      // Assert
      expect(results).toHaveLength(0); // Should return empty array when all requests fail model selection
    });
  });

  describe('orchestrateRealTimePrediction', () => {
    const mockPredictionRequest = {
      modelId: 'model-1',
      input: { data: 'test input' },
      options: { timeout: 1000 },
    };

    const mockPredictionResult = {
      prediction: { category: 'safe', score: 0.95 },
      confidence: 0.95,
      metadata: { model: 'model-1' },
    };

    it('should orchestrate real-time prediction successfully', async () => {
      // Arrange
      const mockModelConfig = {
        modelId: 'model-1',
        providerType: 'openai',
        parameters: {},
      };

      mockModelSelectionService.selectRealTimeModel.mockResolvedValue(mockModelConfig);
      mockPredictionEngine.predict.mockResolvedValue(mockPredictionResult);
      mockConfigService.get.mockReturnValue(1000);

      // Act
      const result = await service.orchestrateRealTimePrediction(mockPredictionRequest);

      // Assert
      expect(result).toEqual(mockPredictionResult);
      expect(mockModelSelectionService.selectRealTimeModel).toHaveBeenCalledWith(
        mockPredictionRequest
      );
      expect(mockPredictionEngine.predict).toHaveBeenCalledWith(
        mockPredictionRequest,
        expect.objectContaining({
          modelConfig: mockModelConfig,
          timeout: 1000,
          priority: 'high',
        })
      );
    });

    it('should handle real-time prediction failures', async () => {
      // Arrange
      mockModelSelectionService.selectRealTimeModel.mockRejectedValue(
        new Error('Model selection failed')
      );

      // Act & Assert
      await expect(service.orchestrateRealTimePrediction(mockPredictionRequest))
        .rejects.toThrow('Prediction failed');
    });
  });

  describe('checkHealth', () => {
    it('should return healthy status when all components are healthy', async () => {
      // Arrange
      const mockProviderHealth = { 'provider-1': { healthy: true } };
      const mockCacheHealth = { status: 'healthy' };
      const mockCircuitBreakerStatus = { 'cb-1': { state: 'closed' } };

      mockLoadBalancerService.checkProviderHealth.mockResolvedValue(mockProviderHealth);
      mockCacheService.checkHealth.mockResolvedValue(mockCacheHealth);
      mockCircuitBreakerService.getStatus.mockReturnValue(mockCircuitBreakerStatus);

      // Act
      const health = await service.checkHealth();

      // Assert
      expect(health.status).toBe('healthy');
      expect(health.providers).toEqual(mockProviderHealth);
      expect(health.cache).toEqual(mockCacheHealth);
      expect(health.circuitBreakers).toEqual(mockCircuitBreakerStatus);
      expect(health.timestamp).toBeInstanceOf(Date);
    });

    it('should return unhealthy status when health check fails', async () => {
      // Arrange
      mockLoadBalancerService.checkProviderHealth.mockRejectedValue(
        new Error('Health check failed')
      );

      // Act
      const health = await service.checkHealth();

      // Assert
      expect(health.status).toBe('unhealthy');
      expect(health.error).toBe('Health check failed');
      expect(health.timestamp).toBeInstanceOf(Date);
    });
  });

  describe('private helper methods', () => {
    it('should generate unique request IDs', () => {
      // Use reflection to access private method for testing
      const generateRequestId = (service as any).generateRequestId.bind(service);
      
      const id1 = generateRequestId();
      const id2 = generateRequestId();
      
      expect(id1).toMatch(/^ai-req-\d+-[a-z0-9]+$/);
      expect(id2).toMatch(/^ai-req-\d+-[a-z0-9]+$/);
      expect(id1).not.toBe(id2);
    });

    it('should generate cache keys consistently', () => {
      const generateCacheKey = (service as any).generateCacheKey.bind(service);
      
      const request1 = { id: '1', type: 'test', data: { value: 'same' } };
      const request2 = { id: '1', type: 'test', data: { value: 'same' } }; // Same ID for same content
      const request3 = { id: '1', type: 'test', data: { value: 'different' } };
      
      const key1 = generateCacheKey(request1);
      const key2 = generateCacheKey(request2);
      const key3 = generateCacheKey(request3);
      
      expect(key1).toBe(key2); // Same content should generate same key
      expect(key1).not.toBe(key3); // Different content should generate different key
    });
  });

  describe('error handling', () => {
    it('should handle orchestration errors with proper error types', async () => {
      // Arrange
      const mockRequest = {
        id: 'error-request',
        type: 'test',
        data: {},
      };

      mockCacheService.get.mockRejectedValue(new Error('Cache error'));

      // Act & Assert
      await expect(service.orchestrateAnalysis(mockRequest)).rejects.toThrow();
      expect(mockMetricsAdapter.recordAiOperation).toHaveBeenCalledWith(
        'error',
        expect.any(Number),
        expect.any(String)
      );
    });
  });
});
