"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MissingBullSharedConfigurationError = void 0;
class MissingBullSharedConfigurationError extends Error {
    constructor(configKey, queueName) {
        super(`Configuration "${configKey}" referenced from the "Queue(${queueName})" options does not exist.`);
    }
}
exports.MissingBullSharedConfigurationError = MissingBullSharedConfigurationError;
