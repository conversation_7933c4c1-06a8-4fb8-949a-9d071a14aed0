b3f5fb3bb21dfd43d68705fc741a633d
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfidenceScore = void 0;
const base_value_object_1 = require("../../../../shared-kernel/value-objects/base-value-object");
/**
 * Confidence Score Value Object
 *
 * Represents a confidence score for AI predictions and analysis results.
 * Ensures the score is within valid bounds (0.0 to 1.0) and provides
 * utility methods for confidence level classification.
 */
class ConfidenceScore extends base_value_object_1.BaseValueObject {
    constructor(value) {
        super(value);
    }
    validate() {
        super.validate();
        if (typeof this._value !== 'number') {
            throw new Error('Confidence score must be a number');
        }
        if (isNaN(this._value)) {
            throw new Error('Confidence score cannot be NaN');
        }
        if (!isFinite(this._value)) {
            throw new Error('Confidence score must be finite');
        }
        if (this._value < ConfidenceScore.MIN_CONFIDENCE) {
            throw new Error(`Confidence score cannot be less than ${ConfidenceScore.MIN_CONFIDENCE}`);
        }
        if (this._value > ConfidenceScore.MAX_CONFIDENCE) {
            throw new Error(`Confidence score cannot be greater than ${ConfidenceScore.MAX_CONFIDENCE}`);
        }
    }
    /**
     * Creates a confidence score from a percentage (0-100)
     */
    static fromPercentage(percentage) {
        if (percentage < 0 || percentage > 100) {
            throw new Error('Percentage must be between 0 and 100');
        }
        return new ConfidenceScore(percentage / 100);
    }
    /**
     * Creates a confidence score from a fraction (numerator/denominator)
     */
    static fromFraction(numerator, denominator) {
        if (denominator === 0) {
            throw new Error('Denominator cannot be zero');
        }
        if (numerator < 0 || denominator < 0) {
            throw new Error('Numerator and denominator must be non-negative');
        }
        return new ConfidenceScore(numerator / denominator);
    }
    /**
     * Creates a low confidence score
     */
    static low() {
        return new ConfidenceScore(ConfidenceScore.LOW_THRESHOLD);
    }
    /**
     * Creates a medium confidence score
     */
    static medium() {
        return new ConfidenceScore(ConfidenceScore.MEDIUM_THRESHOLD);
    }
    /**
     * Creates a high confidence score
     */
    static high() {
        return new ConfidenceScore(ConfidenceScore.HIGH_THRESHOLD);
    }
    /**
     * Creates a maximum confidence score
     */
    static maximum() {
        return new ConfidenceScore(ConfidenceScore.MAX_CONFIDENCE);
    }
    /**
     * Creates a minimum confidence score
     */
    static minimum() {
        return new ConfidenceScore(ConfidenceScore.MIN_CONFIDENCE);
    }
    /**
     * Gets the confidence level as a string
     */
    getLevel() {
        if (this._value < 0.2) {
            return 'very_low';
        }
        else if (this._value < ConfidenceScore.LOW_THRESHOLD) {
            return 'low';
        }
        else if (this._value < ConfidenceScore.MEDIUM_THRESHOLD) {
            return 'medium';
        }
        else if (this._value < ConfidenceScore.HIGH_THRESHOLD) {
            return 'high';
        }
        else {
            return 'very_high';
        }
    }
    /**
     * Checks if the confidence is low
     */
    isLow() {
        return this._value < ConfidenceScore.LOW_THRESHOLD;
    }
    /**
     * Checks if the confidence is medium
     */
    isMedium() {
        return this._value >= ConfidenceScore.LOW_THRESHOLD && this._value < ConfidenceScore.MEDIUM_THRESHOLD;
    }
    /**
     * Checks if the confidence is high
     */
    isHigh() {
        return this._value >= ConfidenceScore.HIGH_THRESHOLD;
    }
    /**
     * Checks if the confidence meets a minimum threshold
     */
    meetsThreshold(threshold) {
        return this._value >= threshold;
    }
    /**
     * Gets the confidence as a percentage
     */
    toPercentage() {
        return this._value * 100;
    }
    /**
     * Gets the confidence as a percentage string
     */
    toPercentageString(decimals = 1) {
        return `${(this._value * 100).toFixed(decimals)}%`;
    }
    /**
     * Combines this confidence with another using weighted average
     */
    combineWith(other, weight = 0.5) {
        if (weight < 0 || weight > 1) {
            throw new Error('Weight must be between 0 and 1');
        }
        const combinedValue = this._value * weight + other._value * (1 - weight);
        return new ConfidenceScore(combinedValue);
    }
    /**
     * Gets the inverse confidence (1 - confidence)
     */
    inverse() {
        return new ConfidenceScore(1 - this._value);
    }
    /**
     * Multiplies this confidence by a factor
     */
    multiply(factor) {
        if (factor < 0) {
            throw new Error('Factor cannot be negative');
        }
        const result = this._value * factor;
        return new ConfidenceScore(Math.min(result, ConfidenceScore.MAX_CONFIDENCE));
    }
    /**
     * Adds another confidence score (capped at maximum)
     */
    add(other) {
        const result = this._value + other._value;
        return new ConfidenceScore(Math.min(result, ConfidenceScore.MAX_CONFIDENCE));
    }
    /**
     * Subtracts another confidence score (floored at minimum)
     */
    subtract(other) {
        const result = this._value - other._value;
        return new ConfidenceScore(Math.max(result, ConfidenceScore.MIN_CONFIDENCE));
    }
    /**
     * Gets the absolute difference between this and another confidence score
     */
    differenceFrom(other) {
        return Math.abs(this._value - other._value);
    }
    /**
     * Checks if this confidence is greater than another
     */
    isGreaterThan(other) {
        return this._value > other._value;
    }
    /**
     * Checks if this confidence is less than another
     */
    isLessThan(other) {
        return this._value < other._value;
    }
    /**
     * Rounds the confidence to a specified number of decimal places
     */
    round(decimals = 2) {
        const factor = Math.pow(10, decimals);
        const rounded = Math.round(this._value * factor) / factor;
        return new ConfidenceScore(rounded);
    }
    /**
     * Converts to a human-readable string
     */
    toString() {
        return `${this.toPercentageString()} (${this.getLevel()})`;
    }
    /**
     * Converts to JSON representation
     */
    toJSON() {
        return {
            value: this._value,
            percentage: this.toPercentage(),
            level: this.getLevel(),
            isHigh: this.isHigh(),
            isMedium: this.isMedium(),
            isLow: this.isLow(),
        };
    }
    /**
     * Creates a ConfidenceScore from JSON
     */
    static fromJSON(json) {
        return new ConfidenceScore(json.value);
    }
    /**
     * Calculates the average of multiple confidence scores
     */
    static average(scores) {
        if (scores.length === 0) {
            throw new Error('Cannot calculate average of empty array');
        }
        const sum = scores.reduce((acc, score) => acc + score._value, 0);
        return new ConfidenceScore(sum / scores.length);
    }
    /**
     * Calculates the weighted average of multiple confidence scores
     */
    static weightedAverage(scores, weights) {
        if (scores.length === 0) {
            throw new Error('Cannot calculate weighted average of empty array');
        }
        if (scores.length !== weights.length) {
            throw new Error('Scores and weights arrays must have the same length');
        }
        const totalWeight = weights.reduce((acc, weight) => acc + weight, 0);
        if (totalWeight === 0) {
            throw new Error('Total weight cannot be zero');
        }
        const weightedSum = scores.reduce((acc, score, index) => acc + score._value * weights[index], 0);
        return new ConfidenceScore(weightedSum / totalWeight);
    }
    /**
     * Gets the maximum confidence from an array
     */
    static max(scores) {
        if (scores.length === 0) {
            throw new Error('Cannot find maximum of empty array');
        }
        const maxValue = Math.max(...scores.map(score => score._value));
        return new ConfidenceScore(maxValue);
    }
    /**
     * Gets the minimum confidence from an array
     */
    static min(scores) {
        if (scores.length === 0) {
            throw new Error('Cannot find minimum of empty array');
        }
        const minValue = Math.min(...scores.map(score => score._value));
        return new ConfidenceScore(minValue);
    }
}
exports.ConfidenceScore = ConfidenceScore;
ConfidenceScore.MIN_CONFIDENCE = 0.0;
ConfidenceScore.MAX_CONFIDENCE = 1.0;
// Confidence level thresholds
ConfidenceScore.LOW_THRESHOLD = 0.3;
ConfidenceScore.MEDIUM_THRESHOLD = 0.7;
ConfidenceScore.HIGH_THRESHOLD = 0.9;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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