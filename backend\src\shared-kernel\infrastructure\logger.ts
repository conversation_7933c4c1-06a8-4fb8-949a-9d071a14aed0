import { Injectable, LoggerService as NestLoggerService } from '@nestjs/common';

/**
 * Logger interface for consistent logging across the application
 */
export interface ILogger {
  log(message: string, context?: any): void;
  error(message: string, context?: any): void;
  warn(message: string, context?: any): void;
  debug(message: string, context?: any): void;
  verbose(message: string, context?: any): void;
  info(message: string, context?: any): void;
}

/**
 * Logger implementation
 * 
 * Provides structured logging with context support.
 * Can be extended to integrate with external logging services.
 */
@Injectable()
export class Logger implements ILogger, NestLoggerService {
  private context?: string;

  constructor(context?: string) {
    this.context = context;
  }

  /**
   * Set the logger context
   */
  setContext(context: string): void {
    this.context = context;
  }

  /**
   * Log an informational message
   */
  log(message: string, context?: any): void {
    this.writeLog('LOG', message, context);
  }

  /**
   * Log an error message
   */
  error(message: string, context?: any): void {
    this.writeLog('ERROR', message, context);
  }

  /**
   * Log a warning message
   */
  warn(message: string, context?: any): void {
    this.writeLog('WARN', message, context);
  }

  /**
   * Log a debug message
   */
  debug(message: string, context?: any): void {
    this.writeLog('DEBUG', message, context);
  }

  /**
   * Log a verbose message
   */
  verbose(message: string, context?: any): void {
    this.writeLog('VERBOSE', message, context);
  }

  /**
   * Log an info message (alias for log)
   */
  info(message: string, context?: any): void {
    this.log(message, context);
  }

  /**
   * Write log entry with timestamp and context
   */
  private writeLog(level: string, message: string, context?: any): void {
    const timestamp = new Date().toISOString();
    const logContext = this.context || 'Application';
    
    const logEntry = {
      timestamp,
      level,
      context: logContext,
      message,
      ...(context && { data: context })
    };

    // In production, this would integrate with a proper logging service
    console.log(JSON.stringify(logEntry));
  }
}
