b027ee10588c99f19e43b53a0814c0d8
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ModelSelectionService_1;
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelSelectionService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const ai_model_repository_interface_1 = require("../../../domain/repositories/ai-model.repository.interface");
const model_performance_service_1 = require("../optimization/model-performance.service");
const ai_cache_service_1 = require("../caching/ai-cache.service");
/**
 * Model Selection Service
 *
 * Intelligently selects the optimal AI model for specific tasks
 * based on performance metrics, cost, latency requirements, and availability.
 * Implements sophisticated model routing and recommendation algorithms.
 */
let ModelSelectionService = ModelSelectionService_1 = class ModelSelectionService {
    constructor(modelRepository, performanceService, cacheService, configService) {
        this.modelRepository = modelRepository;
        this.performanceService = performanceService;
        this.cacheService = cacheService;
        this.configService = configService;
        this.logger = new common_1.Logger(ModelSelectionService_1.name);
        this.modelSelectionCache = new Map();
    }
    /**
     * Selects optimal model for analysis request
     */
    async selectOptimalModel(request) {
        this.logger.debug(`Selecting optimal model for request type: ${request.taskType}`);
        try {
            // Check cache for recent selection
            const cachedSelection = await this.getCachedSelection(request);
            if (cachedSelection && this.isCacheValid(cachedSelection)) {
                return cachedSelection.configuration;
            }
            // Get available models for task type
            const availableModels = await this.getAvailableModels(request.taskType);
            if (availableModels.length === 0) {
                throw new ModelSelectionError(`No models available for task type: ${request.taskType}`);
            }
            // Score and rank models
            const scoredModels = await this.scoreModels(availableModels, request);
            // Select best model based on requirements
            const selectedModel = this.selectBestModel(scoredModels, request);
            // Cache selection result
            await this.cacheSelection(request, selectedModel);
            this.logger.debug(`Selected model: ${selectedModel.modelId} for task: ${request.taskType}`);
            return selectedModel;
        }
        catch (error) {
            this.logger.error(`Model selection failed for task: ${request.taskType}`, error);
            throw new ModelSelectionError(`Model selection failed: ${error.message}`);
        }
    }
    /**
     * Selects model optimized for real-time processing
     */
    async selectRealTimeModel(request) {
        const realTimeRequest = {
            ...request,
            requirements: {
                ...request.requirements,
                maxLatency: 1000, // 1 second max
                priority: 'latency',
                realTime: true,
            },
        };
        return this.selectOptimalModel(realTimeRequest);
    }
    /**
     * Selects model optimized for batch processing
     */
    async selectBatchModel(request) {
        const batchRequest = {
            ...request,
            requirements: {
                ...request.requirements,
                priority: 'throughput',
                batch: true,
                costOptimized: true,
            },
        };
        return this.selectOptimalModel(batchRequest);
    }
    /**
     * Selects model optimized for high accuracy
     */
    async selectHighAccuracyModel(request) {
        const accuracyRequest = {
            ...request,
            requirements: {
                ...request.requirements,
                priority: 'accuracy',
                minAccuracy: 0.95,
            },
        };
        return this.selectOptimalModel(accuracyRequest);
    }
    /**
     * Gets model recommendations for a task type
     */
    async getModelRecommendations(taskType) {
        try {
            const availableModels = await this.getAvailableModels(taskType);
            const recommendations = [];
            for (const model of availableModels) {
                const performance = await this.performanceService.getModelMetrics(model.id);
                const recommendation = this.createRecommendation(model, performance);
                recommendations.push(recommendation);
            }
            // Sort by overall score
            recommendations.sort((a, b) => b.overallScore - a.overallScore);
            return recommendations.slice(0, 5); // Top 5 recommendations
        }
        catch (error) {
            this.logger.error(`Failed to get model recommendations for: ${taskType}`, error);
            throw new ModelSelectionError(`Recommendations failed: ${error.message}`);
        }
    }
    /**
     * Evaluates model performance for selection
     */
    async evaluateModelPerformance(modelId) {
        try {
            const model = await this.modelRepository.findById(modelId);
            if (!model) {
                throw new Error(`Model not found: ${modelId}`);
            }
            const metrics = await this.performanceService.getModelMetrics(modelId);
            const costMetrics = await this.performanceService.getCostMetrics(modelId);
            return {
                modelId,
                accuracy: metrics.accuracy,
                precision: metrics.precision,
                recall: metrics.recall,
                f1Score: metrics.f1Score,
                latency: metrics.averageLatency,
                throughput: metrics.throughput,
                costPerRequest: costMetrics.costPerRequest,
                availability: metrics.availability,
                lastUpdated: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`Model evaluation failed for: ${modelId}`, error);
            throw new ModelSelectionError(`Evaluation failed: ${error.message}`);
        }
    }
    /**
     * Updates model selection strategy based on feedback
     */
    async updateSelectionStrategy(feedback) {
        try {
            // Update model performance metrics
            await this.performanceService.updateModelMetrics(feedback.modelId, {
                accuracy: feedback.actualAccuracy,
                latency: feedback.actualLatency,
                userSatisfaction: feedback.userRating,
            });
            // Adjust selection weights based on feedback
            await this.adjustSelectionWeights(feedback);
            // Clear related cache entries
            await this.invalidateSelectionCache(feedback.modelId);
            this.logger.debug(`Updated selection strategy for model: ${feedback.modelId}`);
        }
        catch (error) {
            this.logger.error(`Failed to update selection strategy`, error);
            throw new ModelSelectionError(`Strategy update failed: ${error.message}`);
        }
    }
    // Private helper methods
    async getAvailableModels(taskType) {
        const models = await this.modelRepository.findByTaskType(taskType);
        // Filter by availability and health status
        return models.filter(model => model.status === 'active' &&
            model.healthStatus === 'healthy');
    }
    async scoreModels(models, request) {
        const scoredModels = [];
        for (const model of models) {
            const score = await this.calculateModelScore(model, request);
            scoredModels.push({ model, score });
        }
        return scoredModels.sort((a, b) => b.score.total - a.score.total);
    }
    async calculateModelScore(model, request) {
        const performance = await this.performanceService.getModelMetrics(model.id);
        const requirements = request.requirements || {};
        // Calculate individual scores (0-1 scale)
        const accuracyScore = this.calculateAccuracyScore(performance.accuracy, requirements);
        const latencyScore = this.calculateLatencyScore(performance.averageLatency, requirements);
        const costScore = this.calculateCostScore(performance.costPerRequest, requirements);
        const availabilityScore = this.calculateAvailabilityScore(performance.availability);
        const throughputScore = this.calculateThroughputScore(performance.throughput, requirements);
        // Apply weights based on requirements priority
        const weights = this.getSelectionWeights(requirements);
        const total = (accuracyScore * weights.accuracy +
            latencyScore * weights.latency +
            costScore * weights.cost +
            availabilityScore * weights.availability +
            throughputScore * weights.throughput);
        return {
            accuracy: accuracyScore,
            latency: latencyScore,
            cost: costScore,
            availability: availabilityScore,
            throughput: throughputScore,
            total,
        };
    }
    selectBestModel(scoredModels, request) {
        if (scoredModels.length === 0) {
            throw new Error('No scored models available');
        }
        const bestModel = scoredModels[0];
        return {
            modelId: bestModel.model.id,
            providerType: bestModel.model.providerType,
            modelType: bestModel.model.type,
            version: bestModel.model.version,
            parameters: this.getOptimalParameters(bestModel.model, request),
            score: bestModel.score.total,
            selectedAt: new Date(),
        };
    }
    getOptimalParameters(model, request) {
        const baseParams = model.defaultParameters || {};
        const requirements = request.requirements || {};
        // Adjust parameters based on requirements
        if (requirements.realTime) {
            return {
                ...baseParams,
                temperature: 0.1, // Lower temperature for consistency
                maxTokens: Math.min(baseParams.maxTokens || 1000, 500),
            };
        }
        if (requirements.highAccuracy) {
            return {
                ...baseParams,
                temperature: 0.0, // Deterministic output
                topP: 0.9,
            };
        }
        return baseParams;
    }
    calculateAccuracyScore(accuracy, requirements) {
        const minAccuracy = requirements.minAccuracy || 0.7;
        return Math.max(0, Math.min(1, (accuracy - minAccuracy) / (1 - minAccuracy)));
    }
    calculateLatencyScore(latency, requirements) {
        const maxLatency = requirements.maxLatency || 5000; // 5 seconds default
        return Math.max(0, Math.min(1, (maxLatency - latency) / maxLatency));
    }
    calculateCostScore(cost, requirements) {
        const maxCost = requirements.maxCost || 0.01; // $0.01 per request default
        return Math.max(0, Math.min(1, (maxCost - cost) / maxCost));
    }
    calculateAvailabilityScore(availability) {
        return availability; // Already 0-1 scale
    }
    calculateThroughputScore(throughput, requirements) {
        const minThroughput = requirements.minThroughput || 10; // 10 requests/second default
        return Math.min(1, throughput / minThroughput);
    }
    getSelectionWeights(requirements) {
        const priority = requirements.priority || 'balanced';
        const weightProfiles = {
            accuracy: { accuracy: 0.5, latency: 0.1, cost: 0.1, availability: 0.2, throughput: 0.1 },
            latency: { accuracy: 0.1, latency: 0.5, cost: 0.1, availability: 0.2, throughput: 0.1 },
            cost: { accuracy: 0.2, latency: 0.1, cost: 0.5, availability: 0.1, throughput: 0.1 },
            throughput: { accuracy: 0.1, latency: 0.1, cost: 0.1, availability: 0.2, throughput: 0.5 },
            balanced: { accuracy: 0.25, latency: 0.25, cost: 0.2, availability: 0.2, throughput: 0.1 },
        };
        return weightProfiles[priority] || weightProfiles.balanced;
    }
    async getCachedSelection(request) {
        const cacheKey = this.generateSelectionCacheKey(request);
        return this.modelSelectionCache.get(cacheKey) || null;
    }
    isCacheValid(selection) {
        const cacheTimeout = this.configService.get('ai.modelSelectionCacheTtl', 300000); // 5 minutes
        return Date.now() - selection.timestamp.getTime() < cacheTimeout;
    }
    async cacheSelection(request, configuration) {
        const cacheKey = this.generateSelectionCacheKey(request);
        const result = {
            configuration,
            timestamp: new Date(),
        };
        this.modelSelectionCache.set(cacheKey, result);
        // Clean up old cache entries periodically
        if (this.modelSelectionCache.size > 1000) {
            this.cleanupCache();
        }
    }
    generateSelectionCacheKey(request) {
        const keyData = {
            taskType: request.taskType,
            requirements: request.requirements,
        };
        return require('crypto')
            .createHash('md5')
            .update(JSON.stringify(keyData))
            .digest('hex');
    }
    cleanupCache() {
        const entries = Array.from(this.modelSelectionCache.entries());
        const cutoff = Date.now() - 600000; // 10 minutes
        for (const [key, value] of entries) {
            if (value.timestamp.getTime() < cutoff) {
                this.modelSelectionCache.delete(key);
            }
        }
    }
    createRecommendation(model, performance) {
        return {
            modelId: model.id,
            modelName: model.name,
            providerType: model.providerType,
            accuracy: performance.accuracy,
            latency: performance.averageLatency,
            costPerRequest: performance.costPerRequest,
            overallScore: this.calculateOverallScore(performance),
            strengths: this.identifyModelStrengths(model, performance),
            weaknesses: this.identifyModelWeaknesses(model, performance),
            useCases: model.recommendedUseCases || [],
        };
    }
    calculateOverallScore(performance) {
        // Simple weighted average for overall score
        return (performance.accuracy * 0.3 +
            (1 - performance.averageLatency / 10000) * 0.3 + // Normalize latency
            (1 - performance.costPerRequest * 1000) * 0.2 + // Normalize cost
            performance.availability * 0.2);
    }
    identifyModelStrengths(model, performance) {
        const strengths = [];
        if (performance.accuracy > 0.9)
            strengths.push('High accuracy');
        if (performance.averageLatency < 1000)
            strengths.push('Low latency');
        if (performance.costPerRequest < 0.001)
            strengths.push('Cost effective');
        if (performance.availability > 0.99)
            strengths.push('High availability');
        return strengths;
    }
    identifyModelWeaknesses(model, performance) {
        const weaknesses = [];
        if (performance.accuracy < 0.8)
            weaknesses.push('Lower accuracy');
        if (performance.averageLatency > 5000)
            weaknesses.push('Higher latency');
        if (performance.costPerRequest > 0.01)
            weaknesses.push('Higher cost');
        if (performance.availability < 0.95)
            weaknesses.push('Availability concerns');
        return weaknesses;
    }
    async adjustSelectionWeights(feedback) {
        // Implementation for adjusting selection algorithm weights based on feedback
        // This would involve machine learning or statistical analysis
    }
    async invalidateSelectionCache(modelId) {
        // Remove cache entries related to the specific model
        for (const [key, value] of this.modelSelectionCache.entries()) {
            if (value.configuration.modelId === modelId) {
                this.modelSelectionCache.delete(key);
            }
        }
    }
};
exports.ModelSelectionService = ModelSelectionService;
exports.ModelSelectionService = ModelSelectionService = ModelSelectionService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(ai_model_repository_interface_1.AI_MODEL_REPOSITORY)),
    __metadata("design:paramtypes", [typeof (_a = typeof ai_model_repository_interface_1.AiModelRepository !== "undefined" && ai_model_repository_interface_1.AiModelRepository) === "function" ? _a : Object, typeof (_b = typeof model_performance_service_1.ModelPerformanceService !== "undefined" && model_performance_service_1.ModelPerformanceService) === "function" ? _b : Object, typeof (_c = typeof ai_cache_service_1.AiCacheService !== "undefined" && ai_cache_service_1.AiCacheService) === "function" ? _c : Object, typeof (_d = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _d : Object])
], ModelSelectionService);
class ModelSelectionError extends Error {
    constructor(message) {
        super(message);
        this.name = 'ModelSelectionError';
    }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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