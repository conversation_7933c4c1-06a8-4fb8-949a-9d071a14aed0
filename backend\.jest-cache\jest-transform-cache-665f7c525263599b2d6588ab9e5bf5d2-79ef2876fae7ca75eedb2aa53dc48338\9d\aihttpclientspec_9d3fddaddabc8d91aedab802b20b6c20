56fc5a6eec62d58af6a0562f23298a0a
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const axios_1 = require("@nestjs/axios");
const ai_http_client_1 = require("../ai-http.client");
const rxjs_1 = require("rxjs");
describe('AiHttpClient', () => {
    let client;
    let httpService;
    let configService;
    const mockAxiosResponse = (data, status = 200) => ({
        data,
        status,
        statusText: 'OK',
        headers: {},
        config: {},
    });
    const mockAxiosError = (status, message) => ({
        name: 'AxiosError',
        message,
        response: {
            status,
            statusText: message,
            data: { message },
            headers: {},
            config: {},
        },
        config: {},
        isAxiosError: true,
        toJSON: () => ({}),
    });
    beforeEach(async () => {
        const mockHttpService = {
            request: jest.fn(),
        };
        const mockConfigService = {
            get: jest.fn(),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                ai_http_client_1.AiHttpClient,
                { provide: axios_1.HttpService, useValue: mockHttpService },
                { provide: config_1.ConfigService, useValue: mockConfigService },
            ],
        }).compile();
        client = module.get(ai_http_client_1.AiHttpClient);
        httpService = module.get(axios_1.HttpService);
        configService = module.get(config_1.ConfigService);
        // Setup default config values
        configService.get.mockImplementation((key, defaultValue) => {
            const config = {
                'ai.http.timeout': 30000,
                'ai.http.retries': 3,
            };
            return config[key] || defaultValue;
        });
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('sendAnalysisRequest', () => {
        it('should send analysis request successfully', async () => {
            const mockResponse = {
                id: 'analysis-123',
                result: { threat_level: 'high', confidence: 0.95 },
                confidence: 0.95,
                model: 'threat-detector-v1',
                timestamp: new Date().toISOString(),
            };
            httpService.request.mockReturnValue((0, rxjs_1.of)(mockAxiosResponse(mockResponse)));
            const payload = {
                data: { event: 'suspicious_login' },
                model: 'threat-detector-v1',
            };
            const result = await client.sendAnalysisRequest('http://ai-service/analyze', payload);
            expect(result).toMatchObject({
                id: expect.any(String),
                result: { threat_level: 'high', confidence: 0.95 },
                confidence: 0.95,
                metadata: expect.objectContaining({
                    model: 'threat-detector-v1',
                }),
                timestamp: expect.any(Date),
            });
            expect(httpService.request).toHaveBeenCalledWith(expect.objectContaining({
                method: 'POST',
                url: 'http://ai-service/analyze',
                data: payload,
                timeout: 30000,
                headers: expect.objectContaining({
                    'Content-Type': 'application/json',
                    'User-Agent': 'Sentinel-AI-Client/1.0',
                    'X-Request-ID': expect.any(String),
                }),
            }));
        });
        it('should handle analysis request failure', async () => {
            const error = mockAxiosError(500, 'Internal Server Error');
            httpService.request.mockReturnValue((0, rxjs_1.throwError)(() => error));
            const payload = { data: { event: 'test' } };
            await expect(client.sendAnalysisRequest('http://ai-service/analyze', payload)).rejects.toThrow('Analysis request failed');
        });
        it('should use custom timeout and headers', async () => {
            const mockResponse = { id: 'test', result: {}, confidence: 0.8 };
            httpService.request.mockReturnValue((0, rxjs_1.of)(mockAxiosResponse(mockResponse)));
            const payload = { data: { event: 'test' } };
            const options = {
                timeout: 60000,
                apiKey: 'test-key',
                customHeaders: { 'X-Custom': 'value' },
            };
            await client.sendAnalysisRequest('http://ai-service/analyze', payload, options);
            expect(httpService.request).toHaveBeenCalledWith(expect.objectContaining({
                timeout: 60000,
                headers: expect.objectContaining({
                    'Authorization': 'Bearer test-key',
                    'X-Custom': 'value',
                }),
            }));
        });
    });
    describe('sendTrainingRequest', () => {
        it('should send training request successfully', async () => {
            const mockResponse = {
                jobId: 'job-123',
                status: 'completed',
                modelId: 'model-456',
                metrics: { accuracy: 0.95 },
                timestamp: new Date().toISOString(),
            };
            httpService.request.mockReturnValue((0, rxjs_1.of)(mockAxiosResponse(mockResponse)));
            const payload = {
                trainingData: [{ input: 'test', output: 'result' }],
                modelConfig: { type: 'classifier' },
            };
            const result = await client.sendTrainingRequest('http://ai-service/train', payload);
            expect(result).toMatchObject({
                jobId: 'job-123',
                status: 'completed',
                modelId: 'model-456',
                metrics: { accuracy: 0.95 },
                timestamp: expect.any(Date),
            });
            expect(httpService.request).toHaveBeenCalledWith(expect.objectContaining({
                method: 'POST',
                url: 'http://ai-service/train',
                data: payload,
                timeout: 300000, // 5 minutes default for training
            }));
        });
        it('should handle training request failure', async () => {
            const error = mockAxiosError(400, 'Invalid training data');
            httpService.request.mockReturnValue((0, rxjs_1.throwError)(() => error));
            const payload = {
                trainingData: [],
                modelConfig: { type: 'classifier' },
            };
            await expect(client.sendTrainingRequest('http://ai-service/train', payload)).rejects.toThrow('Training request failed');
        });
    });
    describe('sendPredictionRequest', () => {
        it('should send prediction request successfully', async () => {
            const mockResponse = {
                prediction: 'malicious',
                confidence: 0.87,
                alternatives: ['benign'],
                model: 'classifier-v2',
                latency: 150,
                timestamp: new Date().toISOString(),
            };
            httpService.request.mockReturnValue((0, rxjs_1.of)(mockAxiosResponse(mockResponse)));
            const payload = {
                input: { features: [1, 2, 3] },
                model: 'classifier-v2',
            };
            const result = await client.sendPredictionRequest('http://ai-service/predict', payload);
            expect(result).toMatchObject({
                prediction: 'malicious',
                confidence: 0.87,
                alternatives: ['benign'],
                metadata: expect.objectContaining({
                    model: 'classifier-v2',
                    latency: 150,
                }),
                timestamp: expect.any(Date),
            });
            expect(httpService.request).toHaveBeenCalledWith(expect.objectContaining({
                method: 'POST',
                url: 'http://ai-service/predict',
                data: payload,
                timeout: 5000, // 5 seconds default for predictions
            }));
        });
        it('should handle prediction request failure', async () => {
            const error = mockAxiosError(422, 'Invalid input format');
            httpService.request.mockReturnValue((0, rxjs_1.throwError)(() => error));
            const payload = { input: null };
            await expect(client.sendPredictionRequest('http://ai-service/predict', payload)).rejects.toThrow('Prediction request failed');
        });
    });
    describe('sendHealthCheckRequest', () => {
        it('should send health check request successfully', async () => {
            const mockResponse = {
                healthy: true,
                status: 'operational',
                responseTime: 45,
                version: '1.2.3',
            };
            httpService.request.mockReturnValue((0, rxjs_1.of)(mockAxiosResponse(mockResponse)));
            const result = await client.sendHealthCheckRequest('http://ai-service');
            expect(result).toMatchObject({
                healthy: true,
                status: 'operational',
                responseTime: 45,
                version: '1.2.3',
                timestamp: expect.any(Date),
            });
            expect(httpService.request).toHaveBeenCalledWith(expect.objectContaining({
                method: 'GET',
                url: 'http://ai-service/health',
                timeout: 5000,
            }));
        });
        it('should handle health check failure gracefully', async () => {
            const error = mockAxiosError(503, 'Service Unavailable');
            httpService.request.mockReturnValue((0, rxjs_1.throwError)(() => error));
            const result = await client.sendHealthCheckRequest('http://ai-service');
            expect(result).toMatchObject({
                healthy: false,
                status: 'unhealthy',
                error: expect.any(String),
                timestamp: expect.any(Date),
            });
        });
        it('should consider 200 status as healthy when no explicit healthy field', async () => {
            const mockResponse = { status: 'ok' };
            httpService.request.mockReturnValue((0, rxjs_1.of)(mockAxiosResponse(mockResponse, 200)));
            const result = await client.sendHealthCheckRequest('http://ai-service');
            expect(result.healthy).toBe(true);
        });
    });
    describe('sendBatchRequest', () => {
        it('should send batch request successfully', async () => {
            const mockResponse = {
                batchId: 'batch-123',
                results: [
                    { id: '1', result: 'success' },
                    { id: '2', result: 'success' },
                ],
                total: 2,
                successful: 2,
                failed: 0,
                processingTime: 1500,
                timestamp: new Date().toISOString(),
            };
            httpService.request.mockReturnValue((0, rxjs_1.of)(mockAxiosResponse(mockResponse)));
            const payloads = [
                { id: '1', data: { input: 'test1' }, type: 'analysis' },
                { id: '2', data: { input: 'test2' }, type: 'analysis' },
            ];
            const result = await client.sendBatchRequest('http://ai-service/batch', payloads);
            expect(result).toMatchObject({
                batchId: 'batch-123',
                results: expect.arrayContaining([
                    { id: '1', result: 'success' },
                    { id: '2', result: 'success' },
                ]),
                summary: {
                    total: 2,
                    successful: 2,
                    failed: 0,
                    processingTime: 1500,
                },
                timestamp: expect.any(Date),
            });
            expect(httpService.request).toHaveBeenCalledWith(expect.objectContaining({
                method: 'POST',
                url: 'http://ai-service/batch',
                data: expect.objectContaining({
                    requests: payloads,
                    batchId: expect.any(String),
                    timestamp: expect.any(String),
                }),
                timeout: 120000, // 2 minutes default for batch
            }));
        });
        it('should handle batch request failure', async () => {
            const error = mockAxiosError(413, 'Payload Too Large');
            httpService.request.mockReturnValue((0, rxjs_1.throwError)(() => error));
            const payloads = [{ id: '1', data: {}, type: 'test' }];
            await expect(client.sendBatchRequest('http://ai-service/batch', payloads)).rejects.toThrow('Batch request failed');
        });
    });
    describe('uploadFile', () => {
        it('should upload file successfully', async () => {
            const mockResponse = {
                fileId: 'file-123',
                filename: 'test.csv',
                size: 1024,
                url: 'http://storage/file-123',
            };
            httpService.request.mockReturnValue((0, rxjs_1.of)(mockAxiosResponse(mockResponse)));
            const fileBuffer = Buffer.from('test,data\n1,2\n3,4');
            const result = await client.uploadFile('http://ai-service/upload', fileBuffer, 'test.csv');
            expect(result).toEqual(mockResponse);
            expect(httpService.request).toHaveBeenCalledWith(expect.objectContaining({
                method: 'POST',
                url: 'http://ai-service/upload',
                data: expect.any(FormData),
                timeout: 60000,
                headers: expect.objectContaining({
                    'Content-Type': 'multipart/form-data',
                }),
            }));
        });
        it('should handle file upload failure', async () => {
            const error = mockAxiosError(413, 'File Too Large');
            httpService.request.mockReturnValue((0, rxjs_1.throwError)(() => error));
            const fileBuffer = Buffer.from('test data');
            await expect(client.uploadFile('http://ai-service/upload', fileBuffer, 'test.txt')).rejects.toThrow('File upload failed');
        });
    });
    describe('downloadFile', () => {
        it('should download file successfully', async () => {
            const fileData = new ArrayBuffer(1024);
            const mockResponse = mockAxiosResponse(fileData);
            mockResponse.headers = {
                'content-type': 'application/octet-stream',
                'content-disposition': 'attachment; filename="downloaded.bin"',
            };
            httpService.request.mockReturnValue((0, rxjs_1.of)(mockResponse));
            const result = await client.downloadFile('http://ai-service/files', 'file-123');
            expect(result).toMatchObject({
                data: expect.any(Buffer),
                filename: 'downloaded.bin',
                contentType: 'application/octet-stream',
                size: 1024,
            });
            expect(httpService.request).toHaveBeenCalledWith(expect.objectContaining({
                method: 'GET',
                url: 'http://ai-service/files/file-123',
                responseType: 'arraybuffer',
                timeout: 60000,
            }));
        });
        it('should handle file download failure', async () => {
            const error = mockAxiosError(404, 'File Not Found');
            httpService.request.mockReturnValue((0, rxjs_1.throwError)(() => error));
            await expect(client.downloadFile('http://ai-service/files', 'nonexistent')).rejects.toThrow('File download failed');
        });
        it('should use default filename when content-disposition header is missing', async () => {
            const fileData = new ArrayBuffer(512);
            const mockResponse = mockAxiosResponse(fileData);
            mockResponse.headers = { 'content-type': 'text/plain' };
            httpService.request.mockReturnValue((0, rxjs_1.of)(mockResponse));
            const result = await client.downloadFile('http://ai-service/files', 'file-123');
            expect(result.filename).toBe('download');
        });
    });
    describe('streamRequest', () => {
        it('should handle stream request successfully', async () => {
            const mockStream = new ReadableStream();
            httpService.request.mockReturnValue((0, rxjs_1.of)(mockAxiosResponse(mockStream)));
            const payload = { data: 'streaming test' };
            const result = await client.streamRequest('http://ai-service/stream', payload);
            expect(result).toBe(mockStream);
            expect(httpService.request).toHaveBeenCalledWith(expect.objectContaining({
                method: 'POST',
                url: 'http://ai-service/stream',
                data: payload,
                responseType: 'stream',
            }));
        });
        it('should handle stream request failure', async () => {
            const error = mockAxiosError(500, 'Stream Error');
            httpService.request.mockReturnValue((0, rxjs_1.throwError)(() => error));
            const payload = { data: 'test' };
            await expect(client.streamRequest('http://ai-service/stream', payload)).rejects.toThrow('Stream request failed');
        });
    });
    describe('error handling', () => {
        it('should handle timeout errors', async () => {
            const timeoutError = new Error('timeout of 30000ms exceeded');
            timeoutError['code'] = 'ECONNABORTED';
            httpService.request.mockReturnValue((0, rxjs_1.throwError)(() => timeoutError));
            await expect(client.sendAnalysisRequest('http://ai-service/analyze', { data: {} })).rejects.toThrow('Request timeout');
        });
        it('should handle connection refused errors', async () => {
            const connectionError = new Error('connect ECONNREFUSED');
            connectionError['code'] = 'ECONNREFUSED';
            httpService.request.mockReturnValue((0, rxjs_1.throwError)(() => connectionError));
            await expect(client.sendAnalysisRequest('http://ai-service/analyze', { data: {} })).rejects.toThrow('Connection refused');
        });
        it('should handle unknown errors', async () => {
            const unknownError = new Error('Unknown error');
            httpService.request.mockReturnValue((0, rxjs_1.throwError)(() => unknownError));
            await expect(client.sendAnalysisRequest('http://ai-service/analyze', { data: {} })).rejects.toThrow('Unknown error');
        });
    });
    describe('request configuration', () => {
        it('should generate unique request IDs', async () => {
            const mockResponse = { id: 'test', result: {}, confidence: 0.5 };
            httpService.request.mockReturnValue((0, rxjs_1.of)(mockAxiosResponse(mockResponse)));
            await client.sendAnalysisRequest('http://ai-service/analyze', { data: {} });
            await client.sendAnalysisRequest('http://ai-service/analyze', { data: {} });
            const calls = httpService.request.mock.calls;
            const requestId1 = calls[0][0].headers['X-Request-ID'];
            const requestId2 = calls[1][0].headers['X-Request-ID'];
            expect(requestId1).toBeDefined();
            expect(requestId2).toBeDefined();
            expect(requestId1).not.toBe(requestId2);
        });
        it('should use configured timeout and retries', () => {
            configService.get.mockImplementation((key, defaultValue) => {
                const config = {
                    'ai.http.timeout': 45000,
                    'ai.http.retries': 5,
                };
                return config[key] || defaultValue;
            });
            // Create new instance to pick up new config
            const newClient = new ai_http_client_1.AiHttpClient(httpService, configService);
            const mockResponse = { id: 'test', result: {}, confidence: 0.5 };
            httpService.request.mockReturnValue((0, rxjs_1.of)(mockAxiosResponse(mockResponse)));
            newClient.sendAnalysisRequest('http://ai-service/analyze', { data: {} });
            expect(httpService.request).toHaveBeenCalledWith(expect.objectContaining({
                timeout: 45000,
            }));
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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