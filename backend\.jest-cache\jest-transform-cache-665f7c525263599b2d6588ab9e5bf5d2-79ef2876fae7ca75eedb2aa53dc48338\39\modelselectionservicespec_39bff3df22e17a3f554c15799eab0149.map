{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\orchestration\\__tests__\\model-selection.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,2CAA+C;AAC/C,wEAAmE;AACnE,4FAAuF;AACvF,qEAAgE;AAChE,iHAGuE;AAEvE,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;IACrC,IAAI,OAA8B,CAAC;IACnC,IAAI,mBAAmD,CAAC;IACxD,IAAI,sBAA4D,CAAC;IACjE,IAAI,gBAA6C,CAAC;IAClD,IAAI,iBAA6C,CAAC;IAElD,MAAM,aAAa,GAAG;QACpB,EAAE,EAAE,SAAS;QACb,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE,kBAAkB;QACxB,YAAY,EAAE,QAAQ;QACtB,OAAO,EAAE,OAAO;QAChB,MAAM,EAAE,QAAQ;QAChB,YAAY,EAAE,SAAS;QACvB,iBAAiB,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE;QACvC,mBAAmB,EAAE,CAAC,iBAAiB,CAAC;KACzC,CAAC;IAEF,MAAM,sBAAsB,GAAG;QAC7B,QAAQ,EAAE,IAAI;QACd,SAAS,EAAE,IAAI;QACf,MAAM,EAAE,IAAI;QACZ,OAAO,EAAE,IAAI;QACb,cAAc,EAAE,IAAI;QACpB,UAAU,EAAE,EAAE;QACd,cAAc,EAAE,KAAK;QACrB,YAAY,EAAE,IAAI;KACnB,CAAC;IAEF,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,eAAe;QACf,mBAAmB,GAAG;YACpB,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;YACnB,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;YACzB,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YACf,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;SACX,CAAC;QAET,sBAAsB,GAAG;YACvB,eAAe,EAAE,IAAI,CAAC,EAAE,EAAE;YAC1B,cAAc,EAAE,IAAI,CAAC,EAAE,EAAE;YACzB,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;SACvB,CAAC;QAET,gBAAgB,GAAG;YACjB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;YACd,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;YACd,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;YACjB,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;SAChB,CAAC;QAET,iBAAiB,GAAG;YAClB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;SACR,CAAC;QAET,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,+CAAqB;gBACrB;oBACE,OAAO,EAAE,mDAAmB;oBAC5B,QAAQ,EAAE,mBAAmB;iBAC9B;gBACD;oBACE,OAAO,EAAE,mDAAuB;oBAChC,QAAQ,EAAE,sBAAsB;iBACjC;gBACD;oBACE,OAAO,EAAE,iCAAc;oBACvB,QAAQ,EAAE,gBAAgB;iBAC3B;gBACD;oBACE,OAAO,EAAE,sBAAa;oBACtB,QAAQ,EAAE,iBAAiB;iBAC5B;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAAwB,+CAAqB,CAAC,CAAC;IACrE,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,MAAM,WAAW,GAAG;YAClB,QAAQ,EAAE,iBAAiB;YAC3B,YAAY,EAAE;gBACZ,UAAU,EAAE,IAAI;gBAChB,WAAW,EAAE,GAAG;gBAChB,QAAQ,EAAE,UAAmB;aAC9B;SACF,CAAC;QAEF,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,UAAU;YACV,mBAAmB,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC;YACtE,sBAAsB,CAAC,eAAe,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,CAAC;YACjF,iBAAiB,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,sBAAsB;YAErE,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;YAE7D,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;gBACrB,OAAO,EAAE,SAAS;gBAClB,YAAY,EAAE,QAAQ;gBACtB,SAAS,EAAE,kBAAkB;gBAC7B,OAAO,EAAE,OAAO;gBAChB,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC9B,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBACzB,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;aAC7B,CAAC,CAAC;YACH,MAAM,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;YACnF,MAAM,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,KAAK,IAAI,EAAE;YACvE,UAAU;YACV,MAAM,eAAe,GAAG;gBACtB,aAAa,EAAE;oBACb,OAAO,EAAE,cAAc;oBACvB,YAAY,EAAE,QAAQ;oBACtB,SAAS,EAAE,kBAAkB;oBAC7B,OAAO,EAAE,OAAO;oBAChB,UAAU,EAAE,EAAE;oBACd,KAAK,EAAE,IAAI;oBACX,UAAU,EAAE,IAAI,IAAI,EAAE;iBACvB;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,sCAAsC;YACtC,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,oBAAoB,CAAC,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YACpF,IAAI,CAAC,KAAK,CAAC,OAAc,EAAE,cAAc,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAEjE,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;YAE7D,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;YACtD,MAAM,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,UAAU;YACV,mBAAmB,CAAC,cAAc,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YAEzD,eAAe;YACf,MAAM,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;iBAClD,OAAO,CAAC,OAAO,CAAC,oDAAoD,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,UAAU;YACV,MAAM,cAAc,GAAG;gBACrB,GAAG,aAAa;gBAChB,EAAE,EAAE,iBAAiB;gBACrB,MAAM,EAAE,UAAU;aACnB,CAAC;YAEF,mBAAmB,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC,CAAC;YACtF,sBAAsB,CAAC,eAAe,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,CAAC;YAEjF,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;YAE7D,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,8BAA8B;QACxE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,UAAU;YACV,mBAAmB,CAAC,cAAc,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAElF,eAAe;YACf,MAAM,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;iBAClD,OAAO,CAAC,OAAO,CAAC,wCAAwC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,MAAM,WAAW,GAAG;YAClB,QAAQ,EAAE,iBAAiB;YAC3B,YAAY,EAAE;gBACZ,QAAQ,EAAE,UAAmB;aAC9B;SACF,CAAC;QAEF,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;YACtE,UAAU;YACV,mBAAmB,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC;YACtE,sBAAsB,CAAC,eAAe,CAAC,iBAAiB,CAAC;gBACvD,GAAG,sBAAsB;gBACzB,cAAc,EAAE,GAAG,EAAE,oBAAoB;aAC1C,CAAC,CAAC;YAEH,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;YAE9D,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC;gBAChC,WAAW,EAAE,GAAG,EAAE,oCAAoC;gBACtD,SAAS,EAAE,GAAG,EAAE,2BAA2B;aAC5C,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,MAAM,WAAW,GAAG;YAClB,QAAQ,EAAE,iBAAiB;SAC5B,CAAC;QAEF,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,UAAU;YACV,mBAAmB,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC;YACtE,sBAAsB,CAAC,eAAe,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,CAAC;YAEjF,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YAE3D,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7B,+DAA+D;QACjE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,MAAM,WAAW,GAAG;YAClB,QAAQ,EAAE,iBAAiB;SAC5B,CAAC;QAEF,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,UAAU;YACV,mBAAmB,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC;YACtE,sBAAsB,CAAC,eAAe,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,CAAC;YAEjF,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;YAElE,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC;gBAChC,WAAW,EAAE,GAAG,EAAE,uBAAuB;gBACzC,IAAI,EAAE,GAAG;aACV,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,UAAU;YACV,MAAM,MAAM,GAAG;gBACb,EAAE,GAAG,aAAa,EAAE,EAAE,EAAE,SAAS,EAAE;gBACnC,EAAE,GAAG,aAAa,EAAE,EAAE,EAAE,SAAS,EAAE;gBACnC,EAAE,GAAG,aAAa,EAAE,EAAE,EAAE,SAAS,EAAE;aACpC,CAAC;YAEF,mBAAmB,CAAC,cAAc,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAC7D,sBAAsB,CAAC,eAAe,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,CAAC;YAEjF,MAAM;YACN,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,CAAC;YAEjF,SAAS;YACT,MAAM,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;gBACjC,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC3B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC7B,YAAY,EAAE,QAAQ;gBACtB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,IAAI;gBACb,cAAc,EAAE,KAAK;gBACrB,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAChC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;gBAC5B,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;gBAC7B,QAAQ,EAAE,CAAC,iBAAiB,CAAC;aAC9B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,UAAU;YACV,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;gBACnD,GAAG,aAAa;gBAChB,EAAE,EAAE,SAAS,CAAC,EAAE;aACjB,CAAC,CAAC,CAAC;YAEJ,mBAAmB,CAAC,cAAc,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAC7D,sBAAsB,CAAC,eAAe,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,CAAC;YAEjF,MAAM;YACN,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,CAAC;YAEjF,SAAS;YACT,MAAM,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,UAAU;YACV,mBAAmB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAC9D,sBAAsB,CAAC,eAAe,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,CAAC;YACjF,sBAAsB,CAAC,cAAc,CAAC,iBAAiB,CAAC;gBACtD,cAAc,EAAE,KAAK;aACtB,CAAC,CAAC;YAEH,MAAM;YACN,MAAM,UAAU,GAAG,MAAM,OAAO,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;YAErE,SAAS;YACT,MAAM,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC;gBACzB,OAAO,EAAE,SAAS;gBAClB,QAAQ,EAAE,IAAI;gBACd,SAAS,EAAE,IAAI;gBACf,MAAM,EAAE,IAAI;gBACZ,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,IAAI;gBACb,UAAU,EAAE,EAAE;gBACd,cAAc,EAAE,KAAK;gBACrB,YAAY,EAAE,IAAI;gBAClB,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;aAC9B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,UAAU;YACV,mBAAmB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAErD,eAAe;YACf,MAAM,MAAM,CAAC,OAAO,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAC;iBAC3D,OAAO,CAAC,OAAO,CAAC,kDAAkD,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,MAAM,YAAY,GAAG;YACnB,OAAO,EAAE,SAAS;YAClB,cAAc,EAAE,IAAI;YACpB,aAAa,EAAE,IAAI;YACnB,UAAU,EAAE,GAAG;YACf,QAAQ,EAAE,kBAAkB;SAC7B,CAAC;QAEF,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,UAAU;YACV,sBAAsB,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEvE,MAAM;YACN,MAAM,OAAO,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;YAEpD,SAAS;YACT,MAAM,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,CAAC,oBAAoB,CACpE,SAAS,EACT;gBACE,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,IAAI;gBACb,gBAAgB,EAAE,GAAG;aACtB,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,UAAU;YACV,sBAAsB,CAAC,kBAAkB,CAAC,iBAAiB,CACzD,IAAI,KAAK,CAAC,eAAe,CAAC,CAC3B,CAAC;YAEF,eAAe;YACf,MAAM,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;iBACxD,OAAO,CAAC,OAAO,CAAC,uCAAuC,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,UAAU;YACV,MAAM,mBAAmB,GAAI,OAAe,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC/E,sBAAsB,CAAC,eAAe,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,CAAC;YAEjF,MAAM,OAAO,GAAG;gBACd,QAAQ,EAAE,iBAAiB;gBAC3B,YAAY,EAAE;oBACZ,QAAQ,EAAE,UAAU;oBACpB,WAAW,EAAE,GAAG;oBAChB,UAAU,EAAE,IAAI;iBACjB;aACF,CAAC;YAEF,MAAM;YACN,MAAM,KAAK,GAAG,MAAM,mBAAmB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;YAEhE,SAAS;YACT,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC;gBACpB,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC5B,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC3B,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBACxB,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAChC,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC9B,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC1B,CAAC,CAAC;YACH,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,UAAU;YACV,MAAM,mBAAmB,GAAI,OAAe,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAE/E,MAAM;YACN,MAAM,eAAe,GAAG,mBAAmB,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAC;YACtE,MAAM,cAAc,GAAG,mBAAmB,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,CAAC;YACpE,MAAM,eAAe,GAAG,mBAAmB,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAC;YAEtE,SAAS;YACT,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YAC1E,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACxE,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,UAAU;YACV,MAAM,yBAAyB,GAAI,OAAe,CAAC,yBAAyB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAE3F,MAAM,QAAQ,GAAG;gBACf,QAAQ,EAAE,iBAAiB;gBAC3B,YAAY,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE;aACvC,CAAC;YAEF,MAAM,QAAQ,GAAG;gBACf,QAAQ,EAAE,iBAAiB;gBAC3B,YAAY,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE;aACvC,CAAC;YAEF,MAAM,QAAQ,GAAG;gBACf,QAAQ,EAAE,iBAAiB;gBAC3B,YAAY,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE;aACtC,CAAC;YAEF,MAAM;YACN,MAAM,IAAI,GAAG,yBAAyB,CAAC,QAAQ,CAAC,CAAC;YACjD,MAAM,IAAI,GAAG,yBAAyB,CAAC,QAAQ,CAAC,CAAC;YACjD,MAAM,IAAI,GAAG,yBAAyB,CAAC,QAAQ,CAAC,CAAC;YAEjD,SAAS;YACT,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,yCAAyC;YAClE,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,oDAAoD;QACnF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,UAAU;YACV,MAAM,YAAY,GAAI,OAAe,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACjE,iBAAiB,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY;YAE3D,MAAM,eAAe,GAAG;gBACtB,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,EAAE,eAAe;aACzD,CAAC;YAEF,MAAM,YAAY,GAAG;gBACnB,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,EAAE,iBAAiB;aAC5D,CAAC;YAEF,eAAe;YACf,MAAM,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+CAA+C,EAAE,GAAG,EAAE;QAC7D,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,UAAU;YACV,MAAM,sBAAsB,GAAI,OAAe,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAErF,MAAM,sBAAsB,GAAG;gBAC7B,QAAQ,EAAE,IAAI;gBACd,cAAc,EAAE,GAAG;gBACnB,cAAc,EAAE,MAAM;gBACtB,YAAY,EAAE,KAAK;aACpB,CAAC;YAEF,MAAM;YACN,MAAM,SAAS,GAAG,sBAAsB,CAAC,aAAa,EAAE,sBAAsB,CAAC,CAAC;YAEhF,SAAS;YACT,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAC7C,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAC3C,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;YAC9C,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,UAAU;YACV,MAAM,uBAAuB,GAAI,OAAe,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAEvF,MAAM,qBAAqB,GAAG;gBAC5B,QAAQ,EAAE,IAAI;gBACd,cAAc,EAAE,IAAI;gBACpB,cAAc,EAAE,IAAI;gBACpB,YAAY,EAAE,IAAI;aACnB,CAAC;YAEF,MAAM;YACN,MAAM,UAAU,GAAG,uBAAuB,CAAC,aAAa,EAAE,qBAAqB,CAAC,CAAC;YAEjF,SAAS;YACT,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;YAC/C,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;YAC/C,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAC5C,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\orchestration\\__tests__\\model-selection.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { ModelSelectionService } from '../model-selection.service';\r\nimport { ModelPerformanceService } from '../../optimization/model-performance.service';\r\nimport { AiCacheService } from '../../caching/ai-cache.service';\r\nimport { \r\n  AI_MODEL_REPOSITORY,\r\n  AiModelRepository \r\n} from '../../../../domain/repositories/ai-model.repository.interface';\r\n\r\ndescribe('ModelSelectionService', () => {\r\n  let service: ModelSelectionService;\r\n  let mockModelRepository: jest.Mocked<AiModelRepository>;\r\n  let mockPerformanceService: jest.Mocked<ModelPerformanceService>;\r\n  let mockCacheService: jest.Mocked<AiCacheService>;\r\n  let mockConfigService: jest.Mocked<ConfigService>;\r\n\r\n  const mockModelInfo = {\r\n    id: 'model-1',\r\n    name: 'Test Model',\r\n    type: 'threat-detection',\r\n    providerType: 'openai',\r\n    version: '1.0.0',\r\n    status: 'active',\r\n    healthStatus: 'healthy',\r\n    defaultParameters: { temperature: 0.7 },\r\n    recommendedUseCases: ['threat-analysis'],\r\n  };\r\n\r\n  const mockPerformanceMetrics = {\r\n    accuracy: 0.95,\r\n    precision: 0.92,\r\n    recall: 0.88,\r\n    f1Score: 0.90,\r\n    averageLatency: 1500,\r\n    throughput: 50,\r\n    costPerRequest: 0.005,\r\n    availability: 0.99,\r\n  };\r\n\r\n  beforeEach(async () => {\r\n    // Create mocks\r\n    mockModelRepository = {\r\n      findById: jest.fn(),\r\n      findByTaskType: jest.fn(),\r\n      save: jest.fn(),\r\n      delete: jest.fn(),\r\n    } as any;\r\n\r\n    mockPerformanceService = {\r\n      getModelMetrics: jest.fn(),\r\n      getCostMetrics: jest.fn(),\r\n      updateModelMetrics: jest.fn(),\r\n    } as any;\r\n\r\n    mockCacheService = {\r\n      get: jest.fn(),\r\n      set: jest.fn(),\r\n      delete: jest.fn(),\r\n      checkHealth: jest.fn(),\r\n    } as any;\r\n\r\n    mockConfigService = {\r\n      get: jest.fn(),\r\n    } as any;\r\n\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        ModelSelectionService,\r\n        {\r\n          provide: AI_MODEL_REPOSITORY,\r\n          useValue: mockModelRepository,\r\n        },\r\n        {\r\n          provide: ModelPerformanceService,\r\n          useValue: mockPerformanceService,\r\n        },\r\n        {\r\n          provide: AiCacheService,\r\n          useValue: mockCacheService,\r\n        },\r\n        {\r\n          provide: ConfigService,\r\n          useValue: mockConfigService,\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    service = module.get<ModelSelectionService>(ModelSelectionService);\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('selectOptimalModel', () => {\r\n    const mockRequest = {\r\n      taskType: 'threat-analysis',\r\n      requirements: {\r\n        maxLatency: 2000,\r\n        minAccuracy: 0.9,\r\n        priority: 'accuracy' as const,\r\n      },\r\n    };\r\n\r\n    it('should select optimal model based on requirements', async () => {\r\n      // Arrange\r\n      mockModelRepository.findByTaskType.mockResolvedValue([mockModelInfo]);\r\n      mockPerformanceService.getModelMetrics.mockResolvedValue(mockPerformanceMetrics);\r\n      mockConfigService.get.mockReturnValue(300000); // 5 minutes cache TTL\r\n\r\n      // Act\r\n      const result = await service.selectOptimalModel(mockRequest);\r\n\r\n      // Assert\r\n      expect(result).toEqual({\r\n        modelId: 'model-1',\r\n        providerType: 'openai',\r\n        modelType: 'threat-detection',\r\n        version: '1.0.0',\r\n        parameters: expect.any(Object),\r\n        score: expect.any(Number),\r\n        selectedAt: expect.any(Date),\r\n      });\r\n      expect(mockModelRepository.findByTaskType).toHaveBeenCalledWith('threat-analysis');\r\n      expect(mockPerformanceService.getModelMetrics).toHaveBeenCalledWith('model-1');\r\n    });\r\n\r\n    it('should return cached selection when available and valid', async () => {\r\n      // Arrange\r\n      const cachedSelection = {\r\n        configuration: {\r\n          modelId: 'cached-model',\r\n          providerType: 'openai',\r\n          modelType: 'threat-detection',\r\n          version: '1.0.0',\r\n          parameters: {},\r\n          score: 0.95,\r\n          selectedAt: new Date(),\r\n        },\r\n        timestamp: new Date(),\r\n      };\r\n\r\n      // Mock cache hit with valid timestamp\r\n      jest.spyOn(service as any, 'getCachedSelection').mockResolvedValue(cachedSelection);\r\n      jest.spyOn(service as any, 'isCacheValid').mockReturnValue(true);\r\n\r\n      // Act\r\n      const result = await service.selectOptimalModel(mockRequest);\r\n\r\n      // Assert\r\n      expect(result).toEqual(cachedSelection.configuration);\r\n      expect(mockModelRepository.findByTaskType).not.toHaveBeenCalled();\r\n    });\r\n\r\n    it('should throw error when no models are available', async () => {\r\n      // Arrange\r\n      mockModelRepository.findByTaskType.mockResolvedValue([]);\r\n\r\n      // Act & Assert\r\n      await expect(service.selectOptimalModel(mockRequest))\r\n        .rejects.toThrow('No models available for task type: threat-analysis');\r\n    });\r\n\r\n    it('should filter out unhealthy models', async () => {\r\n      // Arrange\r\n      const unhealthyModel = {\r\n        ...mockModelInfo,\r\n        id: 'unhealthy-model',\r\n        status: 'inactive',\r\n      };\r\n      \r\n      mockModelRepository.findByTaskType.mockResolvedValue([mockModelInfo, unhealthyModel]);\r\n      mockPerformanceService.getModelMetrics.mockResolvedValue(mockPerformanceMetrics);\r\n\r\n      // Act\r\n      const result = await service.selectOptimalModel(mockRequest);\r\n\r\n      // Assert\r\n      expect(result.modelId).toBe('model-1'); // Only healthy model selected\r\n    });\r\n\r\n    it('should handle model selection errors gracefully', async () => {\r\n      // Arrange\r\n      mockModelRepository.findByTaskType.mockRejectedValue(new Error('Database error'));\r\n\r\n      // Act & Assert\r\n      await expect(service.selectOptimalModel(mockRequest))\r\n        .rejects.toThrow('Model selection failed: Database error');\r\n    });\r\n  });\r\n\r\n  describe('selectRealTimeModel', () => {\r\n    const mockRequest = {\r\n      taskType: 'threat-analysis',\r\n      requirements: {\r\n        priority: 'balanced' as const,\r\n      },\r\n    };\r\n\r\n    it('should select model optimized for real-time processing', async () => {\r\n      // Arrange\r\n      mockModelRepository.findByTaskType.mockResolvedValue([mockModelInfo]);\r\n      mockPerformanceService.getModelMetrics.mockResolvedValue({\r\n        ...mockPerformanceMetrics,\r\n        averageLatency: 500, // Low latency model\r\n      });\r\n\r\n      // Act\r\n      const result = await service.selectRealTimeModel(mockRequest);\r\n\r\n      // Assert\r\n      expect(result.parameters).toEqual({\r\n        temperature: 0.1, // Lower temperature for consistency\r\n        maxTokens: 500, // Reduced tokens for speed\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('selectBatchModel', () => {\r\n    const mockRequest = {\r\n      taskType: 'threat-analysis',\r\n    };\r\n\r\n    it('should select model optimized for batch processing', async () => {\r\n      // Arrange\r\n      mockModelRepository.findByTaskType.mockResolvedValue([mockModelInfo]);\r\n      mockPerformanceService.getModelMetrics.mockResolvedValue(mockPerformanceMetrics);\r\n\r\n      // Act\r\n      const result = await service.selectBatchModel(mockRequest);\r\n\r\n      // Assert\r\n      expect(result).toBeDefined();\r\n      // Batch optimization should prioritize throughput over latency\r\n    });\r\n  });\r\n\r\n  describe('selectHighAccuracyModel', () => {\r\n    const mockRequest = {\r\n      taskType: 'threat-analysis',\r\n    };\r\n\r\n    it('should select model optimized for high accuracy', async () => {\r\n      // Arrange\r\n      mockModelRepository.findByTaskType.mockResolvedValue([mockModelInfo]);\r\n      mockPerformanceService.getModelMetrics.mockResolvedValue(mockPerformanceMetrics);\r\n\r\n      // Act\r\n      const result = await service.selectHighAccuracyModel(mockRequest);\r\n\r\n      // Assert\r\n      expect(result.parameters).toEqual({\r\n        temperature: 0.0, // Deterministic output\r\n        topP: 0.9,\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('getModelRecommendations', () => {\r\n    it('should return top model recommendations', async () => {\r\n      // Arrange\r\n      const models = [\r\n        { ...mockModelInfo, id: 'model-1' },\r\n        { ...mockModelInfo, id: 'model-2' },\r\n        { ...mockModelInfo, id: 'model-3' },\r\n      ];\r\n\r\n      mockModelRepository.findByTaskType.mockResolvedValue(models);\r\n      mockPerformanceService.getModelMetrics.mockResolvedValue(mockPerformanceMetrics);\r\n\r\n      // Act\r\n      const recommendations = await service.getModelRecommendations('threat-analysis');\r\n\r\n      // Assert\r\n      expect(recommendations).toHaveLength(3);\r\n      expect(recommendations[0]).toEqual({\r\n        modelId: expect.any(String),\r\n        modelName: expect.any(String),\r\n        providerType: 'openai',\r\n        accuracy: 0.95,\r\n        latency: 1500,\r\n        costPerRequest: 0.005,\r\n        overallScore: expect.any(Number),\r\n        strengths: expect.any(Array),\r\n        weaknesses: expect.any(Array),\r\n        useCases: ['threat-analysis'],\r\n      });\r\n    });\r\n\r\n    it('should limit recommendations to top 5', async () => {\r\n      // Arrange\r\n      const models = Array.from({ length: 10 }, (_, i) => ({\r\n        ...mockModelInfo,\r\n        id: `model-${i}`,\r\n      }));\r\n\r\n      mockModelRepository.findByTaskType.mockResolvedValue(models);\r\n      mockPerformanceService.getModelMetrics.mockResolvedValue(mockPerformanceMetrics);\r\n\r\n      // Act\r\n      const recommendations = await service.getModelRecommendations('threat-analysis');\r\n\r\n      // Assert\r\n      expect(recommendations).toHaveLength(5);\r\n    });\r\n  });\r\n\r\n  describe('evaluateModelPerformance', () => {\r\n    it('should evaluate model performance correctly', async () => {\r\n      // Arrange\r\n      mockModelRepository.findById.mockResolvedValue(mockModelInfo);\r\n      mockPerformanceService.getModelMetrics.mockResolvedValue(mockPerformanceMetrics);\r\n      mockPerformanceService.getCostMetrics.mockResolvedValue({\r\n        costPerRequest: 0.005,\r\n      });\r\n\r\n      // Act\r\n      const evaluation = await service.evaluateModelPerformance('model-1');\r\n\r\n      // Assert\r\n      expect(evaluation).toEqual({\r\n        modelId: 'model-1',\r\n        accuracy: 0.95,\r\n        precision: 0.92,\r\n        recall: 0.88,\r\n        f1Score: 0.90,\r\n        latency: 1500,\r\n        throughput: 50,\r\n        costPerRequest: 0.005,\r\n        availability: 0.99,\r\n        lastUpdated: expect.any(Date),\r\n      });\r\n    });\r\n\r\n    it('should throw error for non-existent model', async () => {\r\n      // Arrange\r\n      mockModelRepository.findById.mockResolvedValue(null);\r\n\r\n      // Act & Assert\r\n      await expect(service.evaluateModelPerformance('non-existent'))\r\n        .rejects.toThrow('Evaluation failed: Model not found: non-existent');\r\n    });\r\n  });\r\n\r\n  describe('updateSelectionStrategy', () => {\r\n    const mockFeedback = {\r\n      modelId: 'model-1',\r\n      actualAccuracy: 0.93,\r\n      actualLatency: 1200,\r\n      userRating: 4.5,\r\n      comments: 'Good performance',\r\n    };\r\n\r\n    it('should update selection strategy based on feedback', async () => {\r\n      // Arrange\r\n      mockPerformanceService.updateModelMetrics.mockResolvedValue(undefined);\r\n\r\n      // Act\r\n      await service.updateSelectionStrategy(mockFeedback);\r\n\r\n      // Assert\r\n      expect(mockPerformanceService.updateModelMetrics).toHaveBeenCalledWith(\r\n        'model-1',\r\n        {\r\n          accuracy: 0.93,\r\n          latency: 1200,\r\n          userSatisfaction: 4.5,\r\n        }\r\n      );\r\n    });\r\n\r\n    it('should handle feedback update errors', async () => {\r\n      // Arrange\r\n      mockPerformanceService.updateModelMetrics.mockRejectedValue(\r\n        new Error('Update failed')\r\n      );\r\n\r\n      // Act & Assert\r\n      await expect(service.updateSelectionStrategy(mockFeedback))\r\n        .rejects.toThrow('Strategy update failed: Update failed');\r\n    });\r\n  });\r\n\r\n  describe('model scoring', () => {\r\n    it('should calculate model scores correctly', async () => {\r\n      // Arrange\r\n      const calculateModelScore = (service as any).calculateModelScore.bind(service);\r\n      mockPerformanceService.getModelMetrics.mockResolvedValue(mockPerformanceMetrics);\r\n\r\n      const request = {\r\n        taskType: 'threat-analysis',\r\n        requirements: {\r\n          priority: 'accuracy',\r\n          minAccuracy: 0.9,\r\n          maxLatency: 2000,\r\n        },\r\n      };\r\n\r\n      // Act\r\n      const score = await calculateModelScore(mockModelInfo, request);\r\n\r\n      // Assert\r\n      expect(score).toEqual({\r\n        accuracy: expect.any(Number),\r\n        latency: expect.any(Number),\r\n        cost: expect.any(Number),\r\n        availability: expect.any(Number),\r\n        throughput: expect.any(Number),\r\n        total: expect.any(Number),\r\n      });\r\n      expect(score.total).toBeGreaterThan(0);\r\n      expect(score.total).toBeLessThanOrEqual(1);\r\n    });\r\n\r\n    it('should apply different weights based on priority', async () => {\r\n      // Arrange\r\n      const getSelectionWeights = (service as any).getSelectionWeights.bind(service);\r\n\r\n      // Act\r\n      const accuracyWeights = getSelectionWeights({ priority: 'accuracy' });\r\n      const latencyWeights = getSelectionWeights({ priority: 'latency' });\r\n      const balancedWeights = getSelectionWeights({ priority: 'balanced' });\r\n\r\n      // Assert\r\n      expect(accuracyWeights.accuracy).toBeGreaterThan(latencyWeights.accuracy);\r\n      expect(latencyWeights.latency).toBeGreaterThan(accuracyWeights.latency);\r\n      expect(balancedWeights.accuracy).toBe(0.25);\r\n      expect(balancedWeights.latency).toBe(0.25);\r\n    });\r\n  });\r\n\r\n  describe('cache management', () => {\r\n    it('should generate consistent cache keys', () => {\r\n      // Arrange\r\n      const generateSelectionCacheKey = (service as any).generateSelectionCacheKey.bind(service);\r\n      \r\n      const request1 = {\r\n        taskType: 'threat-analysis',\r\n        requirements: { priority: 'accuracy' },\r\n      };\r\n      \r\n      const request2 = {\r\n        taskType: 'threat-analysis',\r\n        requirements: { priority: 'accuracy' },\r\n      };\r\n      \r\n      const request3 = {\r\n        taskType: 'threat-analysis',\r\n        requirements: { priority: 'latency' },\r\n      };\r\n\r\n      // Act\r\n      const key1 = generateSelectionCacheKey(request1);\r\n      const key2 = generateSelectionCacheKey(request2);\r\n      const key3 = generateSelectionCacheKey(request3);\r\n\r\n      // Assert\r\n      expect(key1).toBe(key2); // Same requests should generate same key\r\n      expect(key1).not.toBe(key3); // Different requests should generate different keys\r\n    });\r\n\r\n    it('should validate cache entries correctly', () => {\r\n      // Arrange\r\n      const isCacheValid = (service as any).isCacheValid.bind(service);\r\n      mockConfigService.get.mockReturnValue(300000); // 5 minutes\r\n\r\n      const recentSelection = {\r\n        timestamp: new Date(Date.now() - 60000), // 1 minute ago\r\n      };\r\n      \r\n      const oldSelection = {\r\n        timestamp: new Date(Date.now() - 600000), // 10 minutes ago\r\n      };\r\n\r\n      // Act & Assert\r\n      expect(isCacheValid(recentSelection)).toBe(true);\r\n      expect(isCacheValid(oldSelection)).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('model strengths and weaknesses identification', () => {\r\n    it('should identify model strengths correctly', () => {\r\n      // Arrange\r\n      const identifyModelStrengths = (service as any).identifyModelStrengths.bind(service);\r\n      \r\n      const highPerformanceMetrics = {\r\n        accuracy: 0.95,\r\n        averageLatency: 500,\r\n        costPerRequest: 0.0005,\r\n        availability: 0.999,\r\n      };\r\n\r\n      // Act\r\n      const strengths = identifyModelStrengths(mockModelInfo, highPerformanceMetrics);\r\n\r\n      // Assert\r\n      expect(strengths).toContain('High accuracy');\r\n      expect(strengths).toContain('Low latency');\r\n      expect(strengths).toContain('Cost effective');\r\n      expect(strengths).toContain('High availability');\r\n    });\r\n\r\n    it('should identify model weaknesses correctly', () => {\r\n      // Arrange\r\n      const identifyModelWeaknesses = (service as any).identifyModelWeaknesses.bind(service);\r\n      \r\n      const lowPerformanceMetrics = {\r\n        accuracy: 0.75,\r\n        averageLatency: 8000,\r\n        costPerRequest: 0.02,\r\n        availability: 0.90,\r\n      };\r\n\r\n      // Act\r\n      const weaknesses = identifyModelWeaknesses(mockModelInfo, lowPerformanceMetrics);\r\n\r\n      // Assert\r\n      expect(weaknesses).toContain('Lower accuracy');\r\n      expect(weaknesses).toContain('Higher latency');\r\n      expect(weaknesses).toContain('Higher cost');\r\n      expect(weaknesses).toContain('Availability concerns');\r\n    });\r\n  });\r\n});"], "version": 3}