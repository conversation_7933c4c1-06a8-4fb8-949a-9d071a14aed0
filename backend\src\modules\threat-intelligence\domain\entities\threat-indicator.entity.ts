import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ThreatIntelligence } from './threat-intelligence.entity';

/**
 * Threat Indicator Types
 */
export enum ThreatIndicatorType {
  IP_ADDRESS = 'ip_address',
  DOMAIN = 'domain',
  URL = 'url',
  FILE_HASH = 'file_hash',
  EMAIL = 'email',
  REGISTRY_KEY = 'registry_key',
  MUTEX = 'mutex',
  USER_AGENT = 'user_agent',
  CERTIFICATE = 'certificate',
  ASN = 'asn',
}

/**
 * Threat Indicator Confidence Levels
 */
export enum ThreatIndicatorConfidence {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  VERY_HIGH = 'very_high',
}

/**
 * Threat Indicator Status
 */
export enum ThreatIndicatorStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  EXPIRED = 'expired',
  UNDER_REVIEW = 'under_review',
}

/**
 * Threat Indicator Entity
 * 
 * Represents individual indicators of compromise (IoCs) and threat indicators
 * that can be used for threat detection and analysis
 */
@Entity('threat_indicators')
@Index(['type', 'value'])
@Index(['confidence', 'status'])
@Index(['threatIntelligenceId'])
@Index(['firstSeen', 'lastSeen'])
export class ThreatIndicator {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'enum',
    enum: ThreatIndicatorType,
    comment: 'Type of threat indicator',
  })
  @Index()
  type: ThreatIndicatorType;

  @Column({
    type: 'text',
    comment: 'The actual indicator value (IP, domain, hash, etc.)',
  })
  @Index()
  value: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Human-readable description of the indicator',
  })
  description?: string;

  @Column({
    type: 'enum',
    enum: ThreatIndicatorConfidence,
    default: ThreatIndicatorConfidence.MEDIUM,
    comment: 'Confidence level of the indicator',
  })
  confidence: ThreatIndicatorConfidence;

  @Column({
    type: 'enum',
    enum: ThreatIndicatorStatus,
    default: ThreatIndicatorStatus.ACTIVE,
    comment: 'Current status of the indicator',
  })
  @Index()
  status: ThreatIndicatorStatus;

  @Column({
    type: 'jsonb',
    nullable: true,
    comment: 'Additional metadata and context for the indicator',
  })
  metadata?: Record<string, any>;

  @Column({
    type: 'text',
    array: true,
    default: '{}',
    comment: 'Tags associated with this indicator',
  })
  tags: string[];

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Source of the threat indicator',
  })
  source?: string;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'When this indicator was first observed',
  })
  @Index()
  firstSeen?: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'When this indicator was last observed',
  })
  @Index()
  lastSeen?: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'When this indicator expires and should no longer be considered active',
  })
  expiresAt?: Date;

  @Column({
    type: 'integer',
    default: 0,
    comment: 'Number of times this indicator has been observed',
  })
  observationCount: number;

  @Column({
    type: 'decimal',
    precision: 3,
    scale: 2,
    nullable: true,
    comment: 'Severity score from 0.00 to 10.00',
  })
  severityScore?: number;

  // Relationships
  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'Associated threat intelligence record',
  })
  threatIntelligenceId?: string;

  @ManyToOne(() => ThreatIntelligence, (threat) => threat.indicators, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'threatIntelligenceId' })
  threatIntelligence?: ThreatIntelligence;

  // Timestamps
  @CreateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    comment: 'When the indicator was created',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
    comment: 'When the indicator was last updated',
  })
  updatedAt: Date;

  /**
   * Check if the indicator is currently active
   */
  isActive(): boolean {
    if (this.status !== ThreatIndicatorStatus.ACTIVE) {
      return false;
    }

    if (this.expiresAt && this.expiresAt < new Date()) {
      return false;
    }

    return true;
  }

  /**
   * Check if the indicator has expired
   */
  isExpired(): boolean {
    return this.expiresAt ? this.expiresAt < new Date() : false;
  }

  /**
   * Get the age of the indicator in days
   */
  getAgeInDays(): number {
    const now = new Date();
    const created = new Date(this.createdAt);
    const diffTime = Math.abs(now.getTime() - created.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * Update the last seen timestamp and increment observation count
   */
  recordObservation(): void {
    this.lastSeen = new Date();
    this.observationCount += 1;
  }
}
