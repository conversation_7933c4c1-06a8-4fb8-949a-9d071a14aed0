{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\communication\\message-queue.client.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAC/C,uCAA2C;AAC3C,+BAA8C;AAE9C;;;;;;GAMG;AAEI,IAAM,kBAAkB,0BAAxB,MAAM,kBAAkB;IAI7B,YAC8B,aAAqC,EACrC,aAAqC,EACnC,eAAuC,EAC5C,UAAkC,EAC1C,aAA4B;QAJA,kBAAa,GAAb,aAAa,CAAO;QACpB,kBAAa,GAAb,aAAa,CAAO;QAClB,oBAAe,GAAf,eAAe,CAAO;QAC3B,eAAU,GAAV,UAAU,CAAO;QAC1C,kBAAa,GAAb,aAAa,CAAe;QAR9B,WAAM,GAAG,IAAI,eAAM,CAAC,oBAAkB,CAAC,IAAI,CAAC,CAAC;QAU5D,IAAI,CAAC,iBAAiB,GAAG;YACvB,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,mBAAmB,EAAE,CAAC,CAAC;YAChE,OAAO,EAAE;gBACP,IAAI,EAAE,aAAa;gBACnB,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,uBAAuB,EAAE,IAAI,CAAC;aACrE;YACD,gBAAgB,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,2BAA2B,EAAE,GAAG,CAAC;YAClF,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,uBAAuB,EAAE,EAAE,CAAC;SAC1E,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACxB,OAA+B,EAC/B,UAA+B,EAAE;QAEjC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;QAEpE,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACjD,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;YAE9E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,OAAO,CAAC,SAAS,YAAY,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;YAErF,OAAO;gBACL,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE;gBACxB,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,SAAS,EAAE,aAAa;gBACxB,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE,UAAU,CAAC,QAAQ,IAAI,CAAC;gBAClC,KAAK,EAAE,UAAU,CAAC,KAAK,IAAI,CAAC;gBAC5B,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,OAAO,CAAC,SAAS,EAAE,EAAE,KAAK,CAAC,CAAC;YACnF,MAAM,IAAI,iBAAiB,CAAC,qCAAqC,KAAK,CAAC,OAAO,EAAE,EAAE,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QAC9G,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACxB,OAA+B,EAC/B,UAA+B,EAAE;QAEjC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;QAEpE,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC;gBACtC,GAAG,OAAO;gBACV,kEAAkE;gBAClE,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,EAAE;gBAChC,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,OAAO,EAAE,SAAS;aAC/C,CAAC,CAAC;YAEH,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;YAE7E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,OAAO,CAAC,SAAS,YAAY,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;YAErF,OAAO;gBACL,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE;gBACxB,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,SAAS,EAAE,aAAa;gBACxB,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE,UAAU,CAAC,QAAQ,IAAI,CAAC;gBAClC,KAAK,EAAE,UAAU,CAAC,KAAK,IAAI,CAAC;gBAC5B,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,OAAO,CAAC,SAAS,EAAE,EAAE,KAAK,CAAC,CAAC;YACnF,MAAM,IAAI,iBAAiB,CAAC,qCAAqC,KAAK,CAAC,OAAO,EAAE,EAAE,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QAC9G,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CAC1B,OAAiC,EACjC,UAA+B,EAAE;QAEjC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;QAEtE,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC;gBACtC,GAAG,OAAO;gBACV,iEAAiE;gBACjE,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,CAAC;gBAC/B,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,KAAK,EAAE,aAAa;aACjD,CAAC,CAAC;YAEH,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;YAE3E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,OAAO,CAAC,SAAS,YAAY,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;YAEvF,OAAO;gBACL,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE;gBACxB,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,SAAS,EAAE,eAAe;gBAC1B,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE,UAAU,CAAC,QAAQ,IAAI,CAAC;gBAClC,KAAK,EAAE,UAAU,CAAC,KAAK,IAAI,CAAC;gBAC5B,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,OAAO,CAAC,SAAS,EAAE,EAAE,KAAK,CAAC,CAAC;YACrF,MAAM,IAAI,iBAAiB,CAAC,uCAAuC,KAAK,CAAC,OAAO,EAAE,EAAE,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QAChH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACrB,OAA4B,EAC5B,UAA+B,EAAE;QAEjC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,OAAO,CAAC,SAAS,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QAEjG,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC;gBACtC,GAAG,OAAO;gBACV,8DAA8D;gBAC9D,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,CAAC;gBAC/B,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,OAAO,EAAE,aAAa;aACnD,CAAC,CAAC;YAEH,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,eAAe,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;YAE5E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,OAAO,CAAC,SAAS,YAAY,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;YAElF,OAAO;gBACL,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE;gBACxB,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,SAAS,EAAE,UAAU;gBACrB,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE,UAAU,CAAC,QAAQ,IAAI,CAAC;gBAClC,KAAK,EAAE,UAAU,CAAC,KAAK,IAAI,CAAC;gBAC5B,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,OAAO,CAAC,SAAS,EAAE,EAAE,KAAK,CAAC,CAAC;YAChF,MAAM,IAAI,iBAAiB,CAAC,kCAAkC,KAAK,CAAC,OAAO,EAAE,EAAE,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QAC3G,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,KAAa,EAAE,SAAiB;QACjD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,KAAK,gBAAgB,SAAS,EAAE,CAAC,CAAC;QAE3E,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAC7C,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAEtC,IAAI,CAAC,GAAG,EAAE,CAAC;gBACT,OAAO;oBACL,KAAK;oBACL,SAAS;oBACT,MAAM,EAAE,WAAW;oBACnB,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC;YACJ,CAAC;YAED,MAAM,KAAK,GAAG,MAAM,GAAG,CAAC,QAAQ,EAAE,CAAC;YACnC,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC;YAChC,MAAM,IAAI,GAAG,MAAM,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAE3C,OAAO;gBACL,KAAK;gBACL,SAAS;gBACT,MAAM,EAAE,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;gBACxC,QAAQ,EAAE,OAAO,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACrD,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,MAAM,EAAE,GAAG,CAAC,WAAW;gBACvB,KAAK,EAAE,GAAG,CAAC,YAAY;gBACvB,QAAQ,EAAE,GAAG,CAAC,YAAY;gBAC1B,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,iBAAiB,CAAC,QAAQ;gBACjE,SAAS,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;gBAClC,WAAW,EAAE,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS;gBACpE,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS;gBACjE,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;YAC/D,MAAM,IAAI,iBAAiB,CAAC,6BAA6B,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAC1F,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,KAAa,EAAE,SAAiB;QAC9C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,KAAK,gBAAgB,SAAS,EAAE,CAAC,CAAC;QAEvE,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAC7C,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAEtC,IAAI,CAAC,GAAG,EAAE,CAAC;gBACT,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mCAAmC,KAAK,EAAE,CAAC,CAAC;gBAC7D,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,KAAK,GAAG,MAAM,GAAG,CAAC,QAAQ,EAAE,CAAC;YAEnC,IAAI,KAAK,KAAK,WAAW,IAAI,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAChD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,+BAA+B,KAAK,YAAY,KAAK,EAAE,CAAC,CAAC;gBAC1E,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,GAAG,CAAC,MAAM,EAAE,CAAC;YACnB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,KAAK,EAAE,CAAC,CAAC;YAC1D,OAAO,IAAI,CAAC;QAEd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,IAAI,iBAAiB,CAAC,yBAAyB,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACtF,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,KAAa,EAAE,SAAiB;QAC7C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,KAAK,gBAAgB,SAAS,EAAE,CAAC,CAAC;QAErE,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAC7C,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAEtC,IAAI,CAAC,GAAG,EAAE,CAAC;gBACT,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,KAAK,EAAE,CAAC,CAAC;gBACtD,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,KAAK,GAAG,MAAM,GAAG,CAAC,QAAQ,EAAE,CAAC;YAEnC,IAAI,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACvB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8BAA8B,KAAK,YAAY,KAAK,EAAE,CAAC,CAAC;gBACzE,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;YAClB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC;YACxD,OAAO,IAAI,CAAC;QAEd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC;YAC1D,MAAM,IAAI,iBAAiB,CAAC,wBAAwB,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACrF,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,SAAiB;QACnC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,SAAS,EAAE,CAAC,CAAC;QAE5D,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAE7C,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAC9E,KAAK,CAAC,UAAU,EAAE;gBAClB,KAAK,CAAC,SAAS,EAAE;gBACjB,KAAK,CAAC,YAAY,EAAE;gBACpB,KAAK,CAAC,SAAS,EAAE;gBACjB,KAAK,CAAC,UAAU,EAAE;gBAClB,KAAK,CAAC,SAAS,EAAE;aAClB,CAAC,CAAC;YAEH,OAAO;gBACL,SAAS;gBACT,MAAM,EAAE;oBACN,OAAO,EAAE,OAAO,CAAC,MAAM;oBACvB,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,SAAS,EAAE,SAAS,CAAC,MAAM;oBAC3B,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,OAAO,EAAE,OAAO,CAAC,MAAM;oBACvB,MAAM,EAAE,MAAM,CAAC,MAAM;iBACtB;gBACD,QAAQ,EAAE,MAAM,KAAK,CAAC,QAAQ,EAAE;gBAChC,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,SAAS,EAAE,EAAE,KAAK,CAAC,CAAC;YACzE,MAAM,IAAI,iBAAiB,CAAC,mCAAmC,KAAK,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;QACpG,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CACnB,SAAiB,EACjB,MAAiB,EACjB,KAAK,GAAG,CAAC,EACT,GAAG,GAAG,EAAE;QAER,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,MAAM,gBAAgB,SAAS,EAAE,CAAC,CAAC;QAEhF,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAC7C,IAAI,IAAI,GAAU,EAAE,CAAC;YAErB,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,SAAS;oBACZ,IAAI,GAAG,MAAM,KAAK,CAAC,UAAU,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;oBAC1C,MAAM;gBACR,KAAK,QAAQ;oBACX,IAAI,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;oBACzC,MAAM;gBACR,KAAK,WAAW;oBACd,IAAI,GAAG,MAAM,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;oBAC5C,MAAM;gBACR,KAAK,QAAQ;oBACX,IAAI,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;oBACzC,MAAM;gBACR,KAAK,SAAS;oBACZ,IAAI,GAAG,MAAM,KAAK,CAAC,UAAU,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;oBAC1C,MAAM;gBACR,KAAK,QAAQ;oBACX,IAAI,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;oBACzC,MAAM;gBACR;oBACE,MAAM,IAAI,KAAK,CAAC,uBAAuB,MAAM,EAAE,CAAC,CAAC;YACrD,CAAC;YAED,OAAO,OAAO,CAAC,GAAG,CAChB,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;gBACvB,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE;gBACxB,SAAS;gBACT,MAAM,EAAE,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,QAAQ,EAAE,CAAC;gBACvD,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,MAAM,EAAE,GAAG,CAAC,WAAW;gBACvB,KAAK,EAAE,GAAG,CAAC,YAAY;gBACvB,QAAQ,EAAE,OAAO,GAAG,CAAC,QAAQ,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;gBACjE,QAAQ,EAAE,GAAG,CAAC,YAAY;gBAC1B,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,iBAAiB,CAAC,QAAQ;gBACjE,SAAS,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;gBAClC,WAAW,EAAE,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS;gBACpE,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS;aAClE,CAAC,CAAC,CACJ,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,MAAM,gBAAgB,SAAS,EAAE,EAAE,KAAK,CAAC,CAAC;YAC7F,MAAM,IAAI,iBAAiB,CAAC,iCAAiC,KAAK,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;QAClG,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACd,SAAiB,EACjB,QAAgB,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW;IAChD,SAAiC,WAAW;QAE5C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,SAAS,YAAY,KAAK,eAAe,MAAM,EAAE,CAAC,CAAC;QAExF,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAC7C,MAAM,WAAW,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAErD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,WAAW,CAAC,MAAM,qBAAqB,SAAS,EAAE,CAAC,CAAC;YACjF,OAAO,WAAW,CAAC,MAAM,CAAC;QAE5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,SAAS,EAAE,EAAE,KAAK,CAAC,CAAC;YAChE,MAAM,IAAI,iBAAiB,CAAC,0BAA0B,KAAK,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;QAC3F,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,SAAiB;QAChC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,SAAS,EAAE,CAAC,CAAC;QAEjD,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAC7C,MAAM,KAAK,CAAC,KAAK,EAAE,CAAC;YACpB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,SAAS,EAAE,CAAC,CAAC;QAElD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,SAAS,EAAE,EAAE,KAAK,CAAC,CAAC;YAChE,MAAM,IAAI,iBAAiB,CAAC,0BAA0B,KAAK,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;QAC3F,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,SAAiB;QACjC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,SAAS,EAAE,CAAC,CAAC;QAElD,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAC7C,MAAM,KAAK,CAAC,MAAM,EAAE,CAAC;YACrB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,SAAS,EAAE,CAAC,CAAC;QAEnD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,SAAS,EAAE,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,IAAI,iBAAiB,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;QAC5F,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACf,SAAiB,EACjB,OAAe,EACf,OAAY,EACZ,YAAkB,EAClB,UAA+B,EAAE;QAEjC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,OAAO,cAAc,SAAS,SAAS,YAAY,EAAE,CAAC,CAAC;QAE5F,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAC7C,MAAM,KAAK,GAAG,YAAY,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAElD,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;YACzD,CAAC;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC;gBACtC,GAAG,OAAO;gBACV,KAAK;aACN,CAAC,CAAC;YAEH,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;YAE1D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,OAAO,YAAY,GAAG,CAAC,EAAE,YAAY,KAAK,IAAI,CAAC,CAAC;YAEpF,OAAO;gBACL,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE;gBACxB,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBACxD,SAAS;gBACT,MAAM,EAAE,SAAS;gBACjB,QAAQ,EAAE,UAAU,CAAC,QAAQ,IAAI,CAAC;gBAClC,KAAK,EAAE,UAAU,CAAC,KAAK,IAAI,CAAC;gBAC5B,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,OAAO,cAAc,SAAS,EAAE,EAAE,KAAK,CAAC,CAAC;YACtF,MAAM,IAAI,iBAAiB,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;QAC5F,CAAC;IACH,CAAC;IAED,yBAAyB;IAEjB,cAAc,CAAC,SAAiB;QACtC,QAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,aAAa;gBAChB,OAAO,IAAI,CAAC,aAAa,CAAC;YAC5B,KAAK,aAAa;gBAChB,OAAO,IAAI,CAAC,aAAa,CAAC;YAC5B,KAAK,eAAe;gBAClB,OAAO,IAAI,CAAC,eAAe,CAAC;YAC9B,KAAK,UAAU;gBACb,OAAO,IAAI,CAAC,UAAU,CAAC;YACzB;gBACE,MAAM,IAAI,KAAK,CAAC,uBAAuB,SAAS,EAAE,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,OAA4B;QAClD,OAAO;YACL,GAAG,IAAI,CAAC,iBAAiB;YACzB,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;gBACzB,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI,aAAa;gBAC3C,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI;aACrC,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO;YAClC,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,IAAI,IAAI,CAAC,iBAAiB,CAAC,gBAAgB;YACrF,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,iBAAiB,CAAC,YAAY;YACzE,KAAK,EAAE,OAAO,CAAC,KAAK;SACrB,CAAC;IACJ,CAAC;IAEO,oBAAoB,CAAC,KAAa;QACxC,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,SAAS;gBACZ,OAAO,SAAS,CAAC;YACnB,KAAK,QAAQ;gBACX,OAAO,QAAQ,CAAC;YAClB,KAAK,WAAW;gBACd,OAAO,WAAW,CAAC;YACrB,KAAK,QAAQ;gBACX,OAAO,QAAQ,CAAC;YAClB,KAAK,SAAS;gBACZ,OAAO,SAAS,CAAC;YACnB,KAAK,QAAQ;gBACX,OAAO,QAAQ,CAAC;YAClB;gBACE,OAAO,SAAS,CAAC;QACrB,CAAC;IACH,CAAC;IAEO,iBAAiB;QACvB,OAAO,UAAU,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAC3E,CAAC;CACF,CAAA;AAnhBY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;IAMR,WAAA,IAAA,kBAAW,EAAC,aAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,kBAAW,EAAC,aAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,kBAAW,EAAC,eAAe,CAAC,CAAA;IAC5B,WAAA,IAAA,kBAAW,EAAC,UAAU,CAAC,CAAA;yDAHoC,YAAK,oBAAL,YAAK,oDACL,YAAK,oBAAL,YAAK,oDACD,YAAK,oBAAL,YAAK,oDACf,YAAK,oBAAL,YAAK,oDAC3B,sBAAa,oBAAb,sBAAa;GATpC,kBAAkB,CAmhB9B;AA6HD,MAAM,iBAAkB,SAAQ,KAAK;IACnC,YACE,OAAe,EACC,UAAmB,EACnB,aAAmB;QAEnC,KAAK,CAAC,OAAO,CAAC,CAAC;QAHC,eAAU,GAAV,UAAU,CAAS;QACnB,kBAAa,GAAb,aAAa,CAAM;QAGnC,IAAI,CAAC,IAAI,GAAG,mBAAmB,CAAC;IAClC,CAAC;CACF", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\communication\\message-queue.client.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { InjectQueue } from '@nestjs/bull';\r\nimport { Queue, Job, JobOptions } from 'bull';\r\n\r\n/**\r\n * Message Queue Client for AI Operations\r\n * \r\n * Handles asynchronous AI request processing through message queues.\r\n * Implements queue-based communication patterns, message persistence,\r\n * and delivery guarantees for reliable AI operations.\r\n */\r\n@Injectable()\r\nexport class MessageQueueClient {\r\n  private readonly logger = new Logger(MessageQueueClient.name);\r\n  private readonly defaultJobOptions: JobOptions;\r\n\r\n  constructor(\r\n    @InjectQueue('ai-analysis') private readonly analysisQueue: Queue,\r\n    @InjectQueue('ai-training') private readonly trainingQueue: Queue,\r\n    @InjectQueue('ai-prediction') private readonly predictionQueue: Queue,\r\n    @InjectQueue('ai-batch') private readonly batchQueue: Queue,\r\n    private readonly configService: ConfigService,\r\n  ) {\r\n    this.defaultJobOptions = {\r\n      attempts: this.configService.get<number>('ai.queue.attempts', 3),\r\n      backoff: {\r\n        type: 'exponential',\r\n        delay: this.configService.get<number>('ai.queue.backoffDelay', 2000),\r\n      },\r\n      removeOnComplete: this.configService.get<number>('ai.queue.removeOnComplete', 100),\r\n      removeOnFail: this.configService.get<number>('ai.queue.removeOnFail', 50),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Queues an analysis request for asynchronous processing\r\n   */\r\n  async queueAnalysisRequest(\r\n    payload: AiAnalysisQueuePayload,\r\n    options: QueueRequestOptions = {}\r\n  ): Promise<AiQueueJobResult> {\r\n    this.logger.debug(`Queuing analysis request: ${payload.requestId}`);\r\n\r\n    try {\r\n      const jobOptions = this.buildJobOptions(options);\r\n      const job = await this.analysisQueue.add('analyze-data', payload, jobOptions);\r\n\r\n      this.logger.debug(`Analysis request queued: ${payload.requestId}, jobId: ${job.id}`);\r\n\r\n      return {\r\n        jobId: job.id.toString(),\r\n        requestId: payload.requestId,\r\n        queueName: 'ai-analysis',\r\n        status: 'queued',\r\n        priority: jobOptions.priority || 0,\r\n        delay: jobOptions.delay || 0,\r\n        timestamp: new Date(),\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to queue analysis request: ${payload.requestId}`, error);\r\n      throw new MessageQueueError(`Failed to queue analysis request: ${error.message}`, payload.requestId, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Queues a training request for asynchronous processing\r\n   */\r\n  async queueTrainingRequest(\r\n    payload: AiTrainingQueuePayload,\r\n    options: QueueRequestOptions = {}\r\n  ): Promise<AiQueueJobResult> {\r\n    this.logger.debug(`Queuing training request: ${payload.requestId}`);\r\n\r\n    try {\r\n      const jobOptions = this.buildJobOptions({\r\n        ...options,\r\n        // Training jobs typically have higher priority and longer timeout\r\n        priority: options.priority || 10,\r\n        timeout: options.timeout || 3600000, // 1 hour\r\n      });\r\n\r\n      const job = await this.trainingQueue.add('train-model', payload, jobOptions);\r\n\r\n      this.logger.debug(`Training request queued: ${payload.requestId}, jobId: ${job.id}`);\r\n\r\n      return {\r\n        jobId: job.id.toString(),\r\n        requestId: payload.requestId,\r\n        queueName: 'ai-training',\r\n        status: 'queued',\r\n        priority: jobOptions.priority || 0,\r\n        delay: jobOptions.delay || 0,\r\n        timestamp: new Date(),\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to queue training request: ${payload.requestId}`, error);\r\n      throw new MessageQueueError(`Failed to queue training request: ${error.message}`, payload.requestId, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Queues a prediction request for asynchronous processing\r\n   */\r\n  async queuePredictionRequest(\r\n    payload: AiPredictionQueuePayload,\r\n    options: QueueRequestOptions = {}\r\n  ): Promise<AiQueueJobResult> {\r\n    this.logger.debug(`Queuing prediction request: ${payload.requestId}`);\r\n\r\n    try {\r\n      const jobOptions = this.buildJobOptions({\r\n        ...options,\r\n        // Prediction jobs typically have high priority and short timeout\r\n        priority: options.priority || 5,\r\n        timeout: options.timeout || 30000, // 30 seconds\r\n      });\r\n\r\n      const job = await this.predictionQueue.add('predict', payload, jobOptions);\r\n\r\n      this.logger.debug(`Prediction request queued: ${payload.requestId}, jobId: ${job.id}`);\r\n\r\n      return {\r\n        jobId: job.id.toString(),\r\n        requestId: payload.requestId,\r\n        queueName: 'ai-prediction',\r\n        status: 'queued',\r\n        priority: jobOptions.priority || 0,\r\n        delay: jobOptions.delay || 0,\r\n        timestamp: new Date(),\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to queue prediction request: ${payload.requestId}`, error);\r\n      throw new MessageQueueError(`Failed to queue prediction request: ${error.message}`, payload.requestId, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Queues a batch processing request for asynchronous processing\r\n   */\r\n  async queueBatchRequest(\r\n    payload: AiBatchQueuePayload,\r\n    options: QueueRequestOptions = {}\r\n  ): Promise<AiQueueJobResult> {\r\n    this.logger.debug(`Queuing batch request: ${payload.requestId}, items: ${payload.items.length}`);\r\n\r\n    try {\r\n      const jobOptions = this.buildJobOptions({\r\n        ...options,\r\n        // Batch jobs typically have lower priority but longer timeout\r\n        priority: options.priority || 1,\r\n        timeout: options.timeout || 1800000, // 30 minutes\r\n      });\r\n\r\n      const job = await this.batchQueue.add('process-batch', payload, jobOptions);\r\n\r\n      this.logger.debug(`Batch request queued: ${payload.requestId}, jobId: ${job.id}`);\r\n\r\n      return {\r\n        jobId: job.id.toString(),\r\n        requestId: payload.requestId,\r\n        queueName: 'ai-batch',\r\n        status: 'queued',\r\n        priority: jobOptions.priority || 0,\r\n        delay: jobOptions.delay || 0,\r\n        timestamp: new Date(),\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to queue batch request: ${payload.requestId}`, error);\r\n      throw new MessageQueueError(`Failed to queue batch request: ${error.message}`, payload.requestId, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Gets the status of a queued job\r\n   */\r\n  async getJobStatus(jobId: string, queueName: string): Promise<AiQueueJobStatus> {\r\n    this.logger.debug(`Getting job status: ${jobId} from queue: ${queueName}`);\r\n\r\n    try {\r\n      const queue = this.getQueueByName(queueName);\r\n      const job = await queue.getJob(jobId);\r\n\r\n      if (!job) {\r\n        return {\r\n          jobId,\r\n          queueName,\r\n          status: 'not_found',\r\n          timestamp: new Date(),\r\n        };\r\n      }\r\n\r\n      const state = await job.getState();\r\n      const progress = job.progress();\r\n      const logs = await queue.getJobLogs(jobId);\r\n\r\n      return {\r\n        jobId,\r\n        queueName,\r\n        status: this.mapBullStateToStatus(state),\r\n        progress: typeof progress === 'number' ? progress : 0,\r\n        data: job.data,\r\n        result: job.returnvalue,\r\n        error: job.failedReason,\r\n        attempts: job.attemptsMade,\r\n        maxAttempts: job.opts.attempts || this.defaultJobOptions.attempts,\r\n        createdAt: new Date(job.timestamp),\r\n        processedAt: job.processedOn ? new Date(job.processedOn) : undefined,\r\n        finishedAt: job.finishedOn ? new Date(job.finishedOn) : undefined,\r\n        logs: logs.logs,\r\n        timestamp: new Date(),\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to get job status: ${jobId}`, error);\r\n      throw new MessageQueueError(`Failed to get job status: ${error.message}`, jobId, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cancels a queued job\r\n   */\r\n  async cancelJob(jobId: string, queueName: string): Promise<boolean> {\r\n    this.logger.debug(`Cancelling job: ${jobId} from queue: ${queueName}`);\r\n\r\n    try {\r\n      const queue = this.getQueueByName(queueName);\r\n      const job = await queue.getJob(jobId);\r\n\r\n      if (!job) {\r\n        this.logger.warn(`Job not found for cancellation: ${jobId}`);\r\n        return false;\r\n      }\r\n\r\n      const state = await job.getState();\r\n      \r\n      if (state === 'completed' || state === 'failed') {\r\n        this.logger.warn(`Cannot cancel job in state: ${state}, jobId: ${jobId}`);\r\n        return false;\r\n      }\r\n\r\n      await job.remove();\r\n      this.logger.debug(`Job cancelled successfully: ${jobId}`);\r\n      return true;\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to cancel job: ${jobId}`, error);\r\n      throw new MessageQueueError(`Failed to cancel job: ${error.message}`, jobId, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Retries a failed job\r\n   */\r\n  async retryJob(jobId: string, queueName: string): Promise<boolean> {\r\n    this.logger.debug(`Retrying job: ${jobId} from queue: ${queueName}`);\r\n\r\n    try {\r\n      const queue = this.getQueueByName(queueName);\r\n      const job = await queue.getJob(jobId);\r\n\r\n      if (!job) {\r\n        this.logger.warn(`Job not found for retry: ${jobId}`);\r\n        return false;\r\n      }\r\n\r\n      const state = await job.getState();\r\n      \r\n      if (state !== 'failed') {\r\n        this.logger.warn(`Cannot retry job in state: ${state}, jobId: ${jobId}`);\r\n        return false;\r\n      }\r\n\r\n      await job.retry();\r\n      this.logger.debug(`Job retried successfully: ${jobId}`);\r\n      return true;\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to retry job: ${jobId}`, error);\r\n      throw new MessageQueueError(`Failed to retry job: ${error.message}`, jobId, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Gets queue statistics\r\n   */\r\n  async getQueueStats(queueName: string): Promise<AiQueueStats> {\r\n    this.logger.debug(`Getting queue statistics: ${queueName}`);\r\n\r\n    try {\r\n      const queue = this.getQueueByName(queueName);\r\n      \r\n      const [waiting, active, completed, failed, delayed, paused] = await Promise.all([\r\n        queue.getWaiting(),\r\n        queue.getActive(),\r\n        queue.getCompleted(),\r\n        queue.getFailed(),\r\n        queue.getDelayed(),\r\n        queue.getPaused(),\r\n      ]);\r\n\r\n      return {\r\n        queueName,\r\n        counts: {\r\n          waiting: waiting.length,\r\n          active: active.length,\r\n          completed: completed.length,\r\n          failed: failed.length,\r\n          delayed: delayed.length,\r\n          paused: paused.length,\r\n        },\r\n        isPaused: await queue.isPaused(),\r\n        timestamp: new Date(),\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to get queue statistics: ${queueName}`, error);\r\n      throw new MessageQueueError(`Failed to get queue statistics: ${error.message}`, queueName, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Gets jobs by status from a queue\r\n   */\r\n  async getJobsByStatus(\r\n    queueName: string,\r\n    status: JobStatus,\r\n    start = 0,\r\n    end = 10\r\n  ): Promise<AiQueueJob[]> {\r\n    this.logger.debug(`Getting jobs by status: ${status} from queue: ${queueName}`);\r\n\r\n    try {\r\n      const queue = this.getQueueByName(queueName);\r\n      let jobs: Job[] = [];\r\n\r\n      switch (status) {\r\n        case 'waiting':\r\n          jobs = await queue.getWaiting(start, end);\r\n          break;\r\n        case 'active':\r\n          jobs = await queue.getActive(start, end);\r\n          break;\r\n        case 'completed':\r\n          jobs = await queue.getCompleted(start, end);\r\n          break;\r\n        case 'failed':\r\n          jobs = await queue.getFailed(start, end);\r\n          break;\r\n        case 'delayed':\r\n          jobs = await queue.getDelayed(start, end);\r\n          break;\r\n        case 'paused':\r\n          jobs = await queue.getPaused(start, end);\r\n          break;\r\n        default:\r\n          throw new Error(`Invalid job status: ${status}`);\r\n      }\r\n\r\n      return Promise.all(\r\n        jobs.map(async (job) => ({\r\n          jobId: job.id.toString(),\r\n          queueName,\r\n          status: this.mapBullStateToStatus(await job.getState()),\r\n          data: job.data,\r\n          result: job.returnvalue,\r\n          error: job.failedReason,\r\n          progress: typeof job.progress() === 'number' ? job.progress() : 0,\r\n          attempts: job.attemptsMade,\r\n          maxAttempts: job.opts.attempts || this.defaultJobOptions.attempts,\r\n          createdAt: new Date(job.timestamp),\r\n          processedAt: job.processedOn ? new Date(job.processedOn) : undefined,\r\n          finishedAt: job.finishedOn ? new Date(job.finishedOn) : undefined,\r\n        }))\r\n      );\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to get jobs by status: ${status} from queue: ${queueName}`, error);\r\n      throw new MessageQueueError(`Failed to get jobs by status: ${error.message}`, queueName, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cleans up old jobs from a queue\r\n   */\r\n  async cleanQueue(\r\n    queueName: string,\r\n    grace: number = 24 * 60 * 60 * 1000, // 24 hours\r\n    status: 'completed' | 'failed' = 'completed'\r\n  ): Promise<number> {\r\n    this.logger.debug(`Cleaning queue: ${queueName}, grace: ${grace}ms, status: ${status}`);\r\n\r\n    try {\r\n      const queue = this.getQueueByName(queueName);\r\n      const cleanedJobs = await queue.clean(grace, status);\r\n\r\n      this.logger.debug(`Cleaned ${cleanedJobs.length} jobs from queue: ${queueName}`);\r\n      return cleanedJobs.length;\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to clean queue: ${queueName}`, error);\r\n      throw new MessageQueueError(`Failed to clean queue: ${error.message}`, queueName, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Pauses a queue\r\n   */\r\n  async pauseQueue(queueName: string): Promise<void> {\r\n    this.logger.debug(`Pausing queue: ${queueName}`);\r\n\r\n    try {\r\n      const queue = this.getQueueByName(queueName);\r\n      await queue.pause();\r\n      this.logger.debug(`Queue paused: ${queueName}`);\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to pause queue: ${queueName}`, error);\r\n      throw new MessageQueueError(`Failed to pause queue: ${error.message}`, queueName, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Resumes a paused queue\r\n   */\r\n  async resumeQueue(queueName: string): Promise<void> {\r\n    this.logger.debug(`Resuming queue: ${queueName}`);\r\n\r\n    try {\r\n      const queue = this.getQueueByName(queueName);\r\n      await queue.resume();\r\n      this.logger.debug(`Queue resumed: ${queueName}`);\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to resume queue: ${queueName}`, error);\r\n      throw new MessageQueueError(`Failed to resume queue: ${error.message}`, queueName, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Adds a job with scheduled execution\r\n   */\r\n  async scheduleJob(\r\n    queueName: string,\r\n    jobType: string,\r\n    payload: any,\r\n    scheduleTime: Date,\r\n    options: QueueRequestOptions = {}\r\n  ): Promise<AiQueueJobResult> {\r\n    this.logger.debug(`Scheduling job: ${jobType} in queue: ${queueName} for: ${scheduleTime}`);\r\n\r\n    try {\r\n      const queue = this.getQueueByName(queueName);\r\n      const delay = scheduleTime.getTime() - Date.now();\r\n\r\n      if (delay < 0) {\r\n        throw new Error('Schedule time must be in the future');\r\n      }\r\n\r\n      const jobOptions = this.buildJobOptions({\r\n        ...options,\r\n        delay,\r\n      });\r\n\r\n      const job = await queue.add(jobType, payload, jobOptions);\r\n\r\n      this.logger.debug(`Job scheduled: ${jobType}, jobId: ${job.id}, delay: ${delay}ms`);\r\n\r\n      return {\r\n        jobId: job.id.toString(),\r\n        requestId: payload.requestId || this.generateRequestId(),\r\n        queueName,\r\n        status: 'delayed',\r\n        priority: jobOptions.priority || 0,\r\n        delay: jobOptions.delay || 0,\r\n        timestamp: new Date(),\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to schedule job: ${jobType} in queue: ${queueName}`, error);\r\n      throw new MessageQueueError(`Failed to schedule job: ${error.message}`, queueName, error);\r\n    }\r\n  }\r\n\r\n  // Private helper methods\r\n\r\n  private getQueueByName(queueName: string): Queue {\r\n    switch (queueName) {\r\n      case 'ai-analysis':\r\n        return this.analysisQueue;\r\n      case 'ai-training':\r\n        return this.trainingQueue;\r\n      case 'ai-prediction':\r\n        return this.predictionQueue;\r\n      case 'ai-batch':\r\n        return this.batchQueue;\r\n      default:\r\n        throw new Error(`Unknown queue name: ${queueName}`);\r\n    }\r\n  }\r\n\r\n  private buildJobOptions(options: QueueRequestOptions): JobOptions {\r\n    return {\r\n      ...this.defaultJobOptions,\r\n      priority: options.priority,\r\n      delay: options.delay,\r\n      timeout: options.timeout,\r\n      attempts: options.attempts,\r\n      backoff: options.backoff ? {\r\n        type: options.backoff.type || 'exponential',\r\n        delay: options.backoff.delay || 2000,\r\n      } : this.defaultJobOptions.backoff,\r\n      removeOnComplete: options.removeOnComplete ?? this.defaultJobOptions.removeOnComplete,\r\n      removeOnFail: options.removeOnFail ?? this.defaultJobOptions.removeOnFail,\r\n      jobId: options.jobId,\r\n    };\r\n  }\r\n\r\n  private mapBullStateToStatus(state: string): JobStatus {\r\n    switch (state) {\r\n      case 'waiting':\r\n        return 'waiting';\r\n      case 'active':\r\n        return 'active';\r\n      case 'completed':\r\n        return 'completed';\r\n      case 'failed':\r\n        return 'failed';\r\n      case 'delayed':\r\n        return 'delayed';\r\n      case 'paused':\r\n        return 'paused';\r\n      default:\r\n        return 'unknown';\r\n    }\r\n  }\r\n\r\n  private generateRequestId(): string {\r\n    return `mq-req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n}\r\n\r\n// Type definitions\r\ninterface QueueRequestOptions {\r\n  priority?: number;\r\n  delay?: number;\r\n  timeout?: number;\r\n  attempts?: number;\r\n  backoff?: {\r\n    type: 'fixed' | 'exponential';\r\n    delay: number;\r\n  };\r\n  removeOnComplete?: number;\r\n  removeOnFail?: number;\r\n  jobId?: string;\r\n}\r\n\r\ninterface AiAnalysisQueuePayload {\r\n  requestId: string;\r\n  data: any;\r\n  model?: string;\r\n  parameters?: any;\r\n  callback?: {\r\n    url: string;\r\n    method: 'POST' | 'PUT';\r\n    headers?: Record<string, string>;\r\n  };\r\n}\r\n\r\ninterface AiTrainingQueuePayload {\r\n  requestId: string;\r\n  trainingData: any;\r\n  modelConfig: any;\r\n  parameters?: any;\r\n  callback?: {\r\n    url: string;\r\n    method: 'POST' | 'PUT';\r\n    headers?: Record<string, string>;\r\n  };\r\n}\r\n\r\ninterface AiPredictionQueuePayload {\r\n  requestId: string;\r\n  input: any;\r\n  model?: string;\r\n  parameters?: any;\r\n  callback?: {\r\n    url: string;\r\n    method: 'POST' | 'PUT';\r\n    headers?: Record<string, string>;\r\n  };\r\n}\r\n\r\ninterface AiBatchQueuePayload {\r\n  requestId: string;\r\n  items: Array<{\r\n    id: string;\r\n    type: 'analysis' | 'prediction' | 'training';\r\n    data: any;\r\n  }>;\r\n  callback?: {\r\n    url: string;\r\n    method: 'POST' | 'PUT';\r\n    headers?: Record<string, string>;\r\n  };\r\n}\r\n\r\ninterface AiQueueJobResult {\r\n  jobId: string;\r\n  requestId: string;\r\n  queueName: string;\r\n  status: JobStatus;\r\n  priority: number;\r\n  delay: number;\r\n  timestamp: Date;\r\n}\r\n\r\ninterface AiQueueJobStatus {\r\n  jobId: string;\r\n  queueName: string;\r\n  status: JobStatus;\r\n  progress?: number;\r\n  data?: any;\r\n  result?: any;\r\n  error?: string;\r\n  attempts?: number;\r\n  maxAttempts?: number;\r\n  createdAt?: Date;\r\n  processedAt?: Date;\r\n  finishedAt?: Date;\r\n  logs?: string[];\r\n  timestamp: Date;\r\n}\r\n\r\ninterface AiQueueJob {\r\n  jobId: string;\r\n  queueName: string;\r\n  status: JobStatus;\r\n  data: any;\r\n  result?: any;\r\n  error?: string;\r\n  progress: number;\r\n  attempts: number;\r\n  maxAttempts: number;\r\n  createdAt: Date;\r\n  processedAt?: Date;\r\n  finishedAt?: Date;\r\n}\r\n\r\ninterface AiQueueStats {\r\n  queueName: string;\r\n  counts: {\r\n    waiting: number;\r\n    active: number;\r\n    completed: number;\r\n    failed: number;\r\n    delayed: number;\r\n    paused: number;\r\n  };\r\n  isPaused: boolean;\r\n  timestamp: Date;\r\n}\r\n\r\ntype JobStatus = 'waiting' | 'active' | 'completed' | 'failed' | 'delayed' | 'paused' | 'unknown' | 'not_found';\r\n\r\nclass MessageQueueError extends Error {\r\n  constructor(\r\n    message: string,\r\n    public readonly identifier?: string,\r\n    public readonly originalError?: any\r\n  ) {\r\n    super(message);\r\n    this.name = 'MessageQueueError';\r\n  }\r\n}"], "version": 3}