6a1cc3dc3da4b638c4872f6b1a0c98cd
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ApiVersioningService_1;
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiVersioningService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const logger_service_1 = require("../../infrastructure/logging/logger.service");
const distributed_cache_service_1 = require("../../infrastructure/cache/distributed-cache.service");
/**
 * API versioning service providing comprehensive version management
 * Handles version detection, compatibility checks, and deprecation warnings
 */
let ApiVersioningService = ApiVersioningService_1 = class ApiVersioningService {
    constructor(configService, loggerService, cacheService) {
        this.configService = configService;
        this.loggerService = loggerService;
        this.cacheService = cacheService;
        this.logger = new common_1.Logger(ApiVersioningService_1.name);
        this.supportedVersions = new Map();
        this.versionMappings = new Map();
        this.deprecationWarnings = new Map();
        this.initializeVersions();
    }
    /**
     * Extract API version from request
     */
    extractVersion(request) {
        // Check header first (preferred method)
        const headerVersion = request.headers['api-version'];
        if (headerVersion) {
            return this.normalizeVersion(headerVersion);
        }
        // Check URL path
        const pathMatch = request.path.match(/^\/api\/v(\d+(?:\.\d+)?)/);
        if (pathMatch) {
            return this.normalizeVersion(pathMatch[1]);
        }
        // Check query parameter
        const queryVersion = request.query['api-version'];
        if (queryVersion) {
            return this.normalizeVersion(queryVersion);
        }
        // Check Accept header for version
        const acceptHeader = request.headers.accept;
        if (acceptHeader) {
            const versionMatch = acceptHeader.match(/application\/vnd\.sentinel\.v(\d+(?:\.\d+)?)/);
            if (versionMatch) {
                return this.normalizeVersion(versionMatch[1]);
            }
        }
        // Default to latest stable version
        return this.getLatestStableVersion();
    }
    /**
     * Validate API version
     */
    validateVersion(version) {
        const normalizedVersion = this.normalizeVersion(version);
        return this.supportedVersions.has(normalizedVersion);
    }
    /**
     * Get version information
     */
    getVersionInfo(version) {
        const normalizedVersion = this.normalizeVersion(version);
        return this.supportedVersions.get(normalizedVersion) || null;
    }
    /**
     * Check if version is deprecated
     */
    isVersionDeprecated(version) {
        const versionInfo = this.getVersionInfo(version);
        return versionInfo?.status === 'deprecated' || versionInfo?.status === 'sunset';
    }
    /**
     * Get deprecation warnings for version
     */
    getDeprecationWarnings(version, endpoint) {
        const normalizedVersion = this.normalizeVersion(version);
        const warnings = this.deprecationWarnings.get(normalizedVersion) || [];
        if (endpoint) {
            return warnings.filter(warning => !warning.endpoint || warning.endpoint === endpoint);
        }
        return warnings;
    }
    /**
     * Check version compatibility
     */
    checkCompatibility(sourceVersion, targetVersion) {
        const source = this.getVersionInfo(sourceVersion);
        const target = this.getVersionInfo(targetVersion);
        if (!source || !target) {
            throw new common_1.BadRequestException('Invalid version specified');
        }
        const compatibility = {
            sourceVersion,
            targetVersion,
            compatible: this.areVersionsCompatible(sourceVersion, targetVersion),
            breakingChanges: this.getBreakingChangesBetweenVersions(sourceVersion, targetVersion),
            migrationRequired: false,
        };
        compatibility.migrationRequired = compatibility.breakingChanges.length > 0;
        if (compatibility.migrationRequired) {
            compatibility.migrationSteps = this.getMigrationSteps(sourceVersion, targetVersion);
        }
        return compatibility;
    }
    /**
     * Get supported versions
     */
    getSupportedVersions() {
        return Array.from(this.supportedVersions.values())
            .sort((a, b) => this.compareVersions(b.version, a.version));
    }
    /**
     * Get latest stable version
     */
    getLatestStableVersion() {
        const activeVersions = Array.from(this.supportedVersions.values())
            .filter(v => v.status === 'active')
            .sort((a, b) => this.compareVersions(b.version, a.version));
        return activeVersions[0]?.version || '2.0';
    }
    /**
     * Transform response for version compatibility
     */
    async transformResponse(data, targetVersion, sourceVersion) {
        if (!sourceVersion) {
            sourceVersion = this.getLatestStableVersion();
        }
        if (sourceVersion === targetVersion) {
            return data;
        }
        const cacheKey = `response_transform_${sourceVersion}_${targetVersion}_${JSON.stringify(data).substring(0, 100)}`;
        try {
            // Check cache first
            const cached = await this.cacheService.get(cacheKey);
            if (cached) {
                return cached;
            }
            // Apply version-specific transformations
            const transformed = await this.applyVersionTransformations(data, sourceVersion, targetVersion);
            // Cache for 5 minutes
            await this.cacheService.set(cacheKey, transformed, { ttl: 300 });
            return transformed;
        }
        catch (error) {
            this.loggerService.error('Response transformation failed', error instanceof Error ? error.message : String(error), {
                sourceVersion,
                targetVersion,
            });
            return data; // Return original data on transformation failure
        }
    }
    /**
     * Generate version migration guide
     */
    generateMigrationGuide(fromVersion, toVersion) {
        const fromInfo = this.getVersionInfo(fromVersion);
        const toInfo = this.getVersionInfo(toVersion);
        if (!fromInfo || !toInfo) {
            throw new common_1.BadRequestException('Invalid version specified for migration guide');
        }
        return {
            overview: `Migration guide from API v${fromVersion} to v${toVersion}`,
            breakingChanges: this.getDetailedBreakingChanges(fromVersion, toVersion),
            newFeatures: toInfo.features.filter(f => !fromInfo.features.includes(f)),
            deprecatedFeatures: this.getDeprecatedFeatures(fromVersion, toVersion),
            migrationSteps: this.getDetailedMigrationSteps(fromVersion, toVersion),
        };
    }
    /**
     * Log version usage for analytics
     */
    async logVersionUsage(version, endpoint, userId) {
        try {
            const logData = {
                version,
                endpoint,
                userId,
                timestamp: new Date(),
                userAgent: 'unknown', // Would be extracted from request
            };
            // Log for analytics
            this.logger.log('API version usage', logData);
            // Store in cache for analytics aggregation
            const usageKey = `version_usage_${version}_${new Date().toISOString().split('T')[0]}`;
            const currentUsage = await this.cacheService.get(usageKey) || 0;
            await this.cacheService.set(usageKey, currentUsage + 1, { ttl: 86400 }); // 24 hours
        }
        catch (error) {
            this.logger.error('Failed to log version usage', { error: error instanceof Error ? error.message : String(error) });
        }
    }
    /**
     * Get version usage statistics
     */
    async getVersionUsageStats(timeRange = '7d') {
        try {
            // This would typically query a proper analytics database
            // For now, return mock data
            return {
                totalRequests: 10000,
                versionDistribution: {
                    '2.0': 7500,
                    '1.5': 2000,
                    '1.0': 500,
                },
                topEndpoints: [
                    { endpoint: '/api/v2/events', count: 2500, version: '2.0' },
                    { endpoint: '/api/v2/vulnerabilities', count: 2000, version: '2.0' },
                    { endpoint: '/api/v1/assets', count: 1500, version: '1.0' },
                ],
                deprecatedVersionUsage: {
                    '1.0': 500,
                    '1.5': 2000,
                },
            };
        }
        catch (error) {
            this.loggerService.error('Failed to get version usage stats', error instanceof Error ? error.message : String(error));
            return {
                totalRequests: 0,
                versionDistribution: {},
                topEndpoints: [],
                deprecatedVersionUsage: {},
            };
        }
    }
    // Private helper methods
    initializeVersions() {
        const versions = [
            {
                version: '1.0',
                status: 'sunset',
                releaseDate: new Date('2023-01-01'),
                deprecationDate: new Date('2024-01-01'),
                sunsetDate: new Date('2024-06-01'),
                features: ['basic-events', 'basic-assets', 'basic-vulnerabilities'],
                breakingChanges: [],
            },
            {
                version: '1.5',
                status: 'deprecated',
                releaseDate: new Date('2023-06-01'),
                deprecationDate: new Date('2024-06-01'),
                sunsetDate: new Date('2025-01-01'),
                features: ['basic-events', 'basic-assets', 'basic-vulnerabilities', 'threat-intelligence', 'basic-correlation'],
                breakingChanges: ['event-schema-change', 'asset-id-format-change'],
            },
            {
                version: '2.0',
                status: 'active',
                releaseDate: new Date('2024-01-01'),
                features: [
                    'advanced-events',
                    'advanced-assets',
                    'advanced-vulnerabilities',
                    'threat-intelligence',
                    'advanced-correlation',
                    'real-time-streaming',
                    'advanced-analytics',
                    'ai-ml-integration',
                    'incident-response',
                    'compliance-audit',
                ],
                breakingChanges: [
                    'complete-api-redesign',
                    'new-authentication-model',
                    'restructured-response-format',
                    'new-error-handling',
                ],
            },
        ];
        versions.forEach(version => {
            this.supportedVersions.set(version.version, version);
        });
        // Initialize version mappings
        this.versionMappings.set('1', '1.0');
        this.versionMappings.set('1.0', '1.0');
        this.versionMappings.set('1.5', '1.5');
        this.versionMappings.set('2', '2.0');
        this.versionMappings.set('2.0', '2.0');
        // Initialize deprecation warnings
        this.initializeDeprecationWarnings();
        this.logger.log('API versions initialized', {
            supportedVersions: Array.from(this.supportedVersions.keys()),
            latestVersion: this.getLatestStableVersion(),
        });
    }
    initializeDeprecationWarnings() {
        // Version 1.0 warnings
        this.deprecationWarnings.set('1.0', [
            {
                version: '1.0',
                deprecationDate: new Date('2024-01-01'),
                sunsetDate: new Date('2024-06-01'),
                replacement: 'v2.0',
                migrationGuide: '/docs/migration/v1-to-v2',
                severity: 'critical',
            },
        ]);
        // Version 1.5 warnings
        this.deprecationWarnings.set('1.5', [
            {
                version: '1.5',
                deprecationDate: new Date('2024-06-01'),
                sunsetDate: new Date('2025-01-01'),
                replacement: 'v2.0',
                migrationGuide: '/docs/migration/v1.5-to-v2',
                severity: 'warning',
            },
            {
                version: '1.5',
                endpoint: '/api/v1.5/events/legacy',
                feature: 'Legacy event format',
                deprecationDate: new Date('2024-03-01'),
                replacement: '/api/v2/events',
                severity: 'warning',
            },
        ]);
    }
    normalizeVersion(version) {
        // Remove 'v' prefix if present
        const cleanVersion = version.replace(/^v/, '');
        // Map to full version if needed
        return this.versionMappings.get(cleanVersion) || cleanVersion;
    }
    compareVersions(a, b) {
        const aParts = a.split('.').map(Number);
        const bParts = b.split('.').map(Number);
        for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {
            const aPart = aParts[i] || 0;
            const bPart = bParts[i] || 0;
            if (aPart > bPart)
                return 1;
            if (aPart < bPart)
                return -1;
        }
        return 0;
    }
    areVersionsCompatible(sourceVersion, targetVersion) {
        // Major version changes are not compatible
        const sourceMajor = parseInt(sourceVersion.split('.')[0]);
        const targetMajor = parseInt(targetVersion.split('.')[0]);
        return sourceMajor === targetMajor;
    }
    getBreakingChangesBetweenVersions(fromVersion, toVersion) {
        const toInfo = this.getVersionInfo(toVersion);
        return toInfo?.breakingChanges || [];
    }
    getMigrationSteps(fromVersion, toVersion) {
        // Return version-specific migration steps
        if (fromVersion === '1.0' && toVersion === '2.0') {
            return [
                'Update authentication to use JWT tokens',
                'Migrate to new response format',
                'Update error handling',
                'Migrate event schema',
                'Update asset ID format',
            ];
        }
        if (fromVersion === '1.5' && toVersion === '2.0') {
            return [
                'Update authentication headers',
                'Migrate to new response format',
                'Update error handling',
            ];
        }
        return [];
    }
    async applyVersionTransformations(data, fromVersion, toVersion) {
        // Apply version-specific data transformations
        let transformed = { ...data };
        // Example transformations
        if (fromVersion === '1.0' && toVersion === '2.0') {
            // Transform v1.0 format to v2.0
            transformed = this.transformV1ToV2(transformed);
        }
        else if (fromVersion === '1.5' && toVersion === '2.0') {
            // Transform v1.5 format to v2.0
            transformed = this.transformV15ToV2(transformed);
        }
        return transformed;
    }
    transformV1ToV2(data) {
        // Example transformation from v1.0 to v2.0
        if (data.events) {
            data.events = data.events.map((event) => ({
                ...event,
                metadata: event.meta || {},
                timestamp: event.time || event.timestamp,
            }));
        }
        return data;
    }
    transformV15ToV2(data) {
        // Example transformation from v1.5 to v2.0
        if (data.pagination) {
            data.pagination = {
                ...data.pagination,
                hasMore: data.pagination.has_more,
            };
            delete data.pagination.has_more;
        }
        return data;
    }
    getDetailedBreakingChanges(fromVersion, toVersion) {
        // Return detailed breaking changes with solutions
        return [
            {
                change: 'Authentication model changed',
                impact: 'API keys no longer supported',
                solution: 'Migrate to JWT token authentication',
                example: 'Authorization: Bearer <jwt-token>',
            },
            {
                change: 'Response format restructured',
                impact: 'Response wrapper changed',
                solution: 'Update response parsing logic',
                example: '{ "success": true, "data": {...}, "metadata": {...} }',
            },
        ];
    }
    getDeprecatedFeatures(fromVersion, toVersion) {
        return [
            {
                feature: 'Legacy event format',
                replacement: 'New structured event format',
                timeline: 'Deprecated in v1.5, removed in v2.0',
            },
        ];
    }
    getDetailedMigrationSteps(fromVersion, toVersion) {
        return [
            {
                step: 1,
                title: 'Update Authentication',
                description: 'Replace API key authentication with JWT tokens',
                code: `// Old (v1.x)
fetch('/api/v1/events', {
  headers: { 'X-API-Key': 'your-api-key' }
});

// New (v2.0)
fetch('/api/v2/events', {
  headers: { 'Authorization': 'Bearer your-jwt-token' }
});`,
            },
            {
                step: 2,
                title: 'Update Response Handling',
                description: 'Adapt to new response format structure',
                code: `// Old (v1.x)
const events = response.events;

// New (v2.0)
const events = response.data.events;`,
            },
        ];
    }
};
exports.ApiVersioningService = ApiVersioningService;
exports.ApiVersioningService = ApiVersioningService = ApiVersioningService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object, typeof (_b = typeof logger_service_1.LoggerService !== "undefined" && logger_service_1.LoggerService) === "function" ? _b : Object, typeof (_c = typeof distributed_cache_service_1.DistributedCacheService !== "undefined" && distributed_cache_service_1.DistributedCacheService) === "function" ? _c : Object])
], ApiVersioningService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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