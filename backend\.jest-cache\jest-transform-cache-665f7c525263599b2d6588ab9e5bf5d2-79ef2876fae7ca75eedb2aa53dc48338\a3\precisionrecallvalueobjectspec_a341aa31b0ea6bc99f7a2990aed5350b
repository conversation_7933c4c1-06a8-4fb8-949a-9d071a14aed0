d9ea11c88f01f9d907e37a770e6c02c2
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const precision_recall_value_object_1 = require("../precision-recall.value-object");
describe('PrecisionRecall Value Object', () => {
    describe('Construction', () => {
        it('should create a valid precision-recall pair', () => {
            const pr = new precision_recall_value_object_1.PrecisionRecall(0.85, 0.75);
            expect(pr.precision).toBe(0.85);
            expect(pr.recall).toBe(0.75);
        });
        it('should accept minimum values', () => {
            const pr = new precision_recall_value_object_1.PrecisionRecall(0.0, 0.0);
            expect(pr.precision).toBe(0.0);
            expect(pr.recall).toBe(0.0);
        });
        it('should accept maximum values', () => {
            const pr = new precision_recall_value_object_1.PrecisionRecall(1.0, 1.0);
            expect(pr.precision).toBe(1.0);
            expect(pr.recall).toBe(1.0);
        });
        it('should throw error for invalid precision', () => {
            expect(() => new precision_recall_value_object_1.PrecisionRecall(-0.1, 0.5)).toThrow('Precision must be between 0 and 1');
            expect(() => new precision_recall_value_object_1.PrecisionRecall(1.1, 0.5)).toThrow('Precision must be between 0 and 1');
        });
        it('should throw error for invalid recall', () => {
            expect(() => new precision_recall_value_object_1.PrecisionRecall(0.5, -0.1)).toThrow('Recall must be between 0 and 1');
            expect(() => new precision_recall_value_object_1.PrecisionRecall(0.5, 1.1)).toThrow('Recall must be between 0 and 1');
        });
        it('should throw error for NaN values', () => {
            expect(() => new precision_recall_value_object_1.PrecisionRecall(NaN, 0.5)).toThrow('Precision and recall cannot be NaN');
            expect(() => new precision_recall_value_object_1.PrecisionRecall(0.5, NaN)).toThrow('Precision and recall cannot be NaN');
        });
        it('should throw error for infinite values', () => {
            expect(() => new precision_recall_value_object_1.PrecisionRecall(Infinity, 0.5)).toThrow('Precision and recall must be finite');
            expect(() => new precision_recall_value_object_1.PrecisionRecall(0.5, Infinity)).toThrow('Precision and recall must be finite');
        });
        it('should throw error for non-number values', () => {
            expect(() => new precision_recall_value_object_1.PrecisionRecall('0.5', 0.5)).toThrow('Precision and recall must be numbers');
            expect(() => new precision_recall_value_object_1.PrecisionRecall(0.5, '0.5')).toThrow('Precision and recall must be numbers');
        });
    });
    describe('Factory Methods', () => {
        it('should create from confusion matrix', () => {
            const pr = precision_recall_value_object_1.PrecisionRecall.fromConfusionMatrix(80, 20, 10); // TP=80, FP=20, FN=10
            expect(pr.precision).toBe(0.8); // 80 / (80 + 20)
            expect(pr.recall).toBeCloseTo(0.8889, 4); // 80 / (80 + 10)
        });
        it('should handle zero denominators in confusion matrix', () => {
            const pr1 = precision_recall_value_object_1.PrecisionRecall.fromConfusionMatrix(0, 0, 10); // No positive predictions
            expect(pr1.precision).toBe(0);
            expect(pr1.recall).toBe(0);
            const pr2 = precision_recall_value_object_1.PrecisionRecall.fromConfusionMatrix(0, 20, 0); // No actual positives
            expect(pr2.precision).toBe(0);
            expect(pr2.recall).toBe(0);
        });
        it('should throw error for negative confusion matrix values', () => {
            expect(() => precision_recall_value_object_1.PrecisionRecall.fromConfusionMatrix(-1, 20, 10))
                .toThrow('Confusion matrix values cannot be negative');
        });
        it('should create from percentages', () => {
            const pr = precision_recall_value_object_1.PrecisionRecall.fromPercentages(85, 75);
            expect(pr.precision).toBe(0.85);
            expect(pr.recall).toBe(0.75);
        });
        it('should throw error for invalid percentages', () => {
            expect(() => precision_recall_value_object_1.PrecisionRecall.fromPercentages(-10, 75))
                .toThrow('Percentages must be between 0 and 100');
            expect(() => precision_recall_value_object_1.PrecisionRecall.fromPercentages(85, 110))
                .toThrow('Percentages must be between 0 and 100');
        });
        it('should create predefined precision-recall pairs', () => {
            const perfect = precision_recall_value_object_1.PrecisionRecall.perfect();
            expect(perfect.precision).toBe(1.0);
            expect(perfect.recall).toBe(1.0);
            const zero = precision_recall_value_object_1.PrecisionRecall.zero();
            expect(zero.precision).toBe(0.0);
            expect(zero.recall).toBe(0.0);
        });
    });
    describe('F-Score Calculations', () => {
        it('should calculate F1 score', () => {
            const pr = new precision_recall_value_object_1.PrecisionRecall(0.8, 0.6);
            const f1 = pr.getF1Score();
            expect(f1).toBeCloseTo(0.6857, 4); // 2 * 0.8 * 0.6 / (0.8 + 0.6)
        });
        it('should handle zero precision and recall in F1', () => {
            const pr = new precision_recall_value_object_1.PrecisionRecall(0.0, 0.0);
            expect(pr.getF1Score()).toBe(0);
        });
        it('should calculate F-beta scores', () => {
            const pr = new precision_recall_value_object_1.PrecisionRecall(0.8, 0.6);
            const f2 = pr.getF2Score(); // Beta = 2, favors recall
            const f05 = pr.getF05Score(); // Beta = 0.5, favors precision
            expect(f2).toBeCloseTo(0.6316, 4);
            expect(f05).toBeCloseTo(0.7407, 4);
        });
        it('should throw error for invalid beta', () => {
            const pr = new precision_recall_value_object_1.PrecisionRecall(0.8, 0.6);
            expect(() => pr.getFBetaScore(0)).toThrow('Beta must be greater than 0');
            expect(() => pr.getFBetaScore(-1)).toThrow('Beta must be greater than 0');
        });
        it('should calculate harmonic mean', () => {
            const pr = new precision_recall_value_object_1.PrecisionRecall(0.8, 0.6);
            expect(pr.getHarmonicMean()).toBe(pr.getF1Score());
        });
        it('should calculate arithmetic mean', () => {
            const pr = new precision_recall_value_object_1.PrecisionRecall(0.8, 0.6);
            expect(pr.getArithmeticMean()).toBe(0.7);
        });
        it('should calculate geometric mean', () => {
            const pr = new precision_recall_value_object_1.PrecisionRecall(0.8, 0.6);
            expect(pr.getGeometricMean()).toBeCloseTo(0.6928, 4); // sqrt(0.8 * 0.6)
        });
    });
    describe('Comparison and Analysis', () => {
        it('should check which metric is higher', () => {
            const pr1 = new precision_recall_value_object_1.PrecisionRecall(0.8, 0.6);
            const pr2 = new precision_recall_value_object_1.PrecisionRecall(0.6, 0.8);
            expect(pr1.isPrecisionHigher()).toBe(true);
            expect(pr1.isRecallHigher()).toBe(false);
            expect(pr2.isPrecisionHigher()).toBe(false);
            expect(pr2.isRecallHigher()).toBe(true);
        });
        it('should check if balanced', () => {
            const balanced = new precision_recall_value_object_1.PrecisionRecall(0.8, 0.82);
            const unbalanced = new precision_recall_value_object_1.PrecisionRecall(0.8, 0.6);
            expect(balanced.isBalanced(0.05)).toBe(true);
            expect(unbalanced.isBalanced(0.05)).toBe(false);
        });
        it('should calculate trade-off ratio', () => {
            const pr = new precision_recall_value_object_1.PrecisionRecall(0.8, 0.4);
            expect(pr.getTradeOffRatio()).toBe(2.0); // 0.8 / 0.4
        });
        it('should handle zero recall in trade-off ratio', () => {
            const pr = new precision_recall_value_object_1.PrecisionRecall(0.8, 0.0);
            expect(pr.getTradeOffRatio()).toBe(Infinity);
        });
        it('should handle zero precision and recall in trade-off ratio', () => {
            const pr = new precision_recall_value_object_1.PrecisionRecall(0.0, 0.0);
            expect(pr.getTradeOffRatio()).toBe(1);
        });
        it('should check threshold compliance', () => {
            const pr = new precision_recall_value_object_1.PrecisionRecall(0.8, 0.6);
            expect(pr.meetsBothThresholds(0.7, 0.5)).toBe(true);
            expect(pr.meetsBothThresholds(0.9, 0.5)).toBe(false);
            expect(pr.meetsEitherThreshold(0.9, 0.5)).toBe(true);
            expect(pr.meetsEitherThreshold(0.9, 0.7)).toBe(false);
        });
    });
    describe('Conversions', () => {
        it('should convert to percentages', () => {
            const pr = new precision_recall_value_object_1.PrecisionRecall(0.85, 0.75);
            expect(pr.getPrecisionPercentage()).toBe(85);
            expect(pr.getRecallPercentage()).toBe(75);
            expect(pr.getPrecisionPercentageString()).toBe('85.0%');
            expect(pr.getRecallPercentageString()).toBe('75.0%');
            expect(pr.getPrecisionPercentageString(2)).toBe('85.00%');
        });
        it('should convert to string', () => {
            const pr = new precision_recall_value_object_1.PrecisionRecall(0.85, 0.75);
            const str = pr.toString();
            expect(str).toContain('Precision: 85.0%');
            expect(str).toContain('Recall: 75.0%');
            expect(str).toContain('F1:');
        });
    });
    describe('Mathematical Operations', () => {
        it('should combine with another precision-recall pair', () => {
            const pr1 = new precision_recall_value_object_1.PrecisionRecall(0.8, 0.6);
            const pr2 = new precision_recall_value_object_1.PrecisionRecall(0.6, 0.8);
            const combined = pr1.combineWith(pr2, 0.7);
            expect(combined.precision).toBeCloseTo(0.74); // 0.8 * 0.7 + 0.6 * 0.3
            expect(combined.recall).toBeCloseTo(0.66); // 0.6 * 0.7 + 0.8 * 0.3
        });
        it('should throw error for invalid weight in combine', () => {
            const pr1 = new precision_recall_value_object_1.PrecisionRecall(0.8, 0.6);
            const pr2 = new precision_recall_value_object_1.PrecisionRecall(0.6, 0.8);
            expect(() => pr1.combineWith(pr2, -0.1)).toThrow('Weight must be between 0 and 1');
            expect(() => pr1.combineWith(pr2, 1.1)).toThrow('Weight must be between 0 and 1');
        });
        it('should calculate distances', () => {
            const pr1 = new precision_recall_value_object_1.PrecisionRecall(0.8, 0.6);
            const pr2 = new precision_recall_value_object_1.PrecisionRecall(0.6, 0.8);
            const euclidean = pr1.distanceFrom(pr2);
            const manhattan = pr1.manhattanDistanceFrom(pr2);
            expect(euclidean).toBeCloseTo(0.2828, 4); // sqrt((0.8-0.6)^2 + (0.6-0.8)^2)
            expect(manhattan).toBe(0.4); // |0.8-0.6| + |0.6-0.8|
        });
        it('should compare based on F1 score', () => {
            const pr1 = new precision_recall_value_object_1.PrecisionRecall(0.9, 0.7); // F1 ≈ 0.788
            const pr2 = new precision_recall_value_object_1.PrecisionRecall(0.7, 0.9); // F1 ≈ 0.788
            const pr3 = new precision_recall_value_object_1.PrecisionRecall(0.6, 0.6); // F1 = 0.6
            expect(pr1.isBetterThan(pr3)).toBe(true);
            expect(pr3.isBetterThan(pr1)).toBe(false);
        });
        it('should round precision and recall', () => {
            const pr = new precision_recall_value_object_1.PrecisionRecall(0.8567, 0.7234);
            const rounded = pr.round(2);
            expect(rounded.precision).toBe(0.86);
            expect(rounded.recall).toBe(0.72);
        });
    });
    describe('Static Utility Methods', () => {
        it('should calculate average', () => {
            const pairs = [
                new precision_recall_value_object_1.PrecisionRecall(0.8, 0.6),
                new precision_recall_value_object_1.PrecisionRecall(0.6, 0.8),
                new precision_recall_value_object_1.PrecisionRecall(0.9, 0.7),
            ];
            const average = precision_recall_value_object_1.PrecisionRecall.average(pairs);
            expect(average.precision).toBeCloseTo(0.7667, 3);
            expect(average.recall).toBeCloseTo(0.7, 3);
        });
        it('should throw error for empty array in average', () => {
            expect(() => precision_recall_value_object_1.PrecisionRecall.average([])).toThrow('Cannot calculate average of empty array');
        });
        it('should calculate weighted average', () => {
            const pairs = [
                new precision_recall_value_object_1.PrecisionRecall(0.8, 0.6),
                new precision_recall_value_object_1.PrecisionRecall(0.6, 0.8),
            ];
            const weights = [0.7, 0.3];
            const weightedAvg = precision_recall_value_object_1.PrecisionRecall.weightedAverage(pairs, weights);
            expect(weightedAvg.precision).toBeCloseTo(0.74);
            expect(weightedAvg.recall).toBeCloseTo(0.66);
        });
        it('should throw error for mismatched arrays in weighted average', () => {
            const pairs = [new precision_recall_value_object_1.PrecisionRecall(0.8, 0.6)];
            const weights = [0.7, 0.3];
            expect(() => precision_recall_value_object_1.PrecisionRecall.weightedAverage(pairs, weights))
                .toThrow('Pairs and weights arrays must have the same length');
        });
        it('should throw error for zero total weight', () => {
            const pairs = [new precision_recall_value_object_1.PrecisionRecall(0.8, 0.6), new precision_recall_value_object_1.PrecisionRecall(0.6, 0.8)];
            const weights = [0, 0];
            expect(() => precision_recall_value_object_1.PrecisionRecall.weightedAverage(pairs, weights))
                .toThrow('Total weight cannot be zero');
        });
        it('should find best metrics', () => {
            const pairs = [
                new precision_recall_value_object_1.PrecisionRecall(0.8, 0.6), // F1 ≈ 0.686
                new precision_recall_value_object_1.PrecisionRecall(0.6, 0.8), // F1 ≈ 0.686
                new precision_recall_value_object_1.PrecisionRecall(0.9, 0.7), // F1 ≈ 0.788
            ];
            const bestF1 = precision_recall_value_object_1.PrecisionRecall.bestF1(pairs);
            const bestPrecision = precision_recall_value_object_1.PrecisionRecall.bestPrecision(pairs);
            const bestRecall = precision_recall_value_object_1.PrecisionRecall.bestRecall(pairs);
            expect(bestF1.precision).toBe(0.9);
            expect(bestPrecision.precision).toBe(0.9);
            expect(bestRecall.recall).toBe(0.8);
        });
        it('should throw error for empty array in best methods', () => {
            expect(() => precision_recall_value_object_1.PrecisionRecall.bestF1([])).toThrow('Cannot find best F1 of empty array');
            expect(() => precision_recall_value_object_1.PrecisionRecall.bestPrecision([])).toThrow('Cannot find best precision of empty array');
            expect(() => precision_recall_value_object_1.PrecisionRecall.bestRecall([])).toThrow('Cannot find best recall of empty array');
        });
    });
    describe('JSON Serialization', () => {
        it('should serialize to JSON', () => {
            const pr = new precision_recall_value_object_1.PrecisionRecall(0.85, 0.75);
            const json = pr.toJSON();
            expect(json.precision).toBe(0.85);
            expect(json.recall).toBe(0.75);
            expect(json.precisionPercentage).toBe(85);
            expect(json.recallPercentage).toBe(75);
            expect(json.f1Score).toBeCloseTo(0.7969, 4);
            expect(json.isBalanced).toBe(false);
        });
        it('should deserialize from JSON', () => {
            const json = { precision: 0.85, recall: 0.75 };
            const pr = precision_recall_value_object_1.PrecisionRecall.fromJSON(json);
            expect(pr.precision).toBe(0.85);
            expect(pr.recall).toBe(0.75);
        });
    });
    describe('Equality', () => {
        it('should be equal to another precision-recall pair with same values', () => {
            const pr1 = new precision_recall_value_object_1.PrecisionRecall(0.85, 0.75);
            const pr2 = new precision_recall_value_object_1.PrecisionRecall(0.85, 0.75);
            expect(pr1.equals(pr2)).toBe(true);
        });
        it('should not be equal to precision-recall pair with different values', () => {
            const pr1 = new precision_recall_value_object_1.PrecisionRecall(0.85, 0.75);
            const pr2 = new precision_recall_value_object_1.PrecisionRecall(0.75, 0.85);
            expect(pr1.equals(pr2)).toBe(false);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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