{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\events\\analysis-result-status-changed.domain-event.ts", "mappings": ";;;AAAA,0FAAqF;AAIrF;;;;;;GAMG;AACH,MAAa,gCAAiC,SAAQ,mCAAe;IACnE,YACkB,gBAAgC,EAChC,SAAiB,EACjB,cAA8B,EAC9B,SAAyB,EACzC,OAAwB,EACxB,UAAiB;QAEjB,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAPX,qBAAgB,GAAhB,gBAAgB,CAAgB;QAChC,cAAS,GAAT,SAAS,CAAQ;QACjB,mBAAc,GAAd,cAAc,CAAgB;QAC9B,cAAS,GAAT,SAAS,CAAgB;IAK3C,CAAC;IAEM,YAAY;QACjB,OAAO,6BAA6B,CAAC;IACvC,CAAC;IAEM,eAAe;QACpB,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,YAAY;QACjB,OAAO;YACL,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE;YAClD,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;CACF;AA5BD,4EA4BC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\events\\analysis-result-status-changed.domain-event.ts"], "sourcesContent": ["import { BaseDomainEvent } from '../../../../shared-kernel/domain/base-domain-event';\r\nimport { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { AnalysisStatus } from '../entities/analysis-result.entity';\r\n\r\n/**\r\n * Analysis Result Status Changed Domain Event\r\n * \r\n * Published when an analysis result's status changes.\r\n * This event can trigger various downstream processes such as\r\n * workflow updates, notifications, and status tracking.\r\n */\r\nexport class AnalysisResultStatusChangedEvent extends BaseDomainEvent {\r\n  constructor(\r\n    public readonly analysisResultId: UniqueEntityId,\r\n    public readonly requestId: string,\r\n    public readonly previousStatus: AnalysisStatus,\r\n    public readonly newStatus: AnalysisStatus,\r\n    eventId?: UniqueEntityId,\r\n    occurredOn?: Date\r\n  ) {\r\n    super(eventId, occurredOn);\r\n  }\r\n\r\n  public getEventName(): string {\r\n    return 'AnalysisResultStatusChanged';\r\n  }\r\n\r\n  public getEventVersion(): string {\r\n    return '1.0';\r\n  }\r\n\r\n  public getEventData(): Record<string, any> {\r\n    return {\r\n      analysisResultId: this.analysisResultId.toString(),\r\n      requestId: this.requestId,\r\n      previousStatus: this.previousStatus,\r\n      newStatus: this.newStatus,\r\n    };\r\n  }\r\n}"], "version": 3}