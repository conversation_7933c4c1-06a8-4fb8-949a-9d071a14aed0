c1cee1518e22701b06d5ec23a05ca9a6
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalysisResultFactory = void 0;
const unique_entity_id_value_object_1 = require("../../../../shared-kernel/value-objects/unique-entity-id.value-object");
const analysis_result_entity_1 = require("../entities/analysis-result.entity");
class AnalysisResultFactory {
    /**
     * Creates a new Analysis Result entity
     */
    static create(request, id) {
        this.validateCreateRequest(request);
        const props = {
            requestId: request.requestId.trim(),
            modelId: request.modelId,
            analysisType: request.analysisType,
            inputData: request.inputData,
            outputData: this.createDefaultOutput(),
            confidence: 0,
            processingTime: 0,
            status: analysis_result_entity_1.AnalysisStatus.PENDING,
            metadata: this.createDefaultMetadata(request.metadata),
            tags: request.tags ? [...request.tags.map(tag => tag.trim().toLowerCase())] : [],
            correlationId: request.correlationId?.trim(),
            parentAnalysisId: request.parentAnalysisId,
            errorDetails: undefined,
            completedAt: undefined,
        };
        return analysis_result_entity_1.AnalysisResult.create(props, id);
    }
    /**
     * Reconstitutes an Analysis Result entity from persistence data
     */
    static reconstitute(data) {
        this.validateReconstitutionData(data);
        const props = {
            requestId: data.requestId,
            modelId: data.modelId,
            analysisType: data.analysisType,
            inputData: data.inputData,
            outputData: data.outputData,
            confidence: data.confidence,
            processingTime: data.processingTime,
            status: data.status,
            metadata: data.metadata,
            tags: data.tags,
            correlationId: data.correlationId,
            parentAnalysisId: data.parentAnalysisId,
            childAnalysisIds: data.childAnalysisIds,
            errorDetails: data.errorDetails,
            createdAt: data.createdAt,
            updatedAt: data.updatedAt,
            completedAt: data.completedAt,
        };
        const id = unique_entity_id_value_object_1.UniqueEntityId.fromString(data.id);
        return analysis_result_entity_1.AnalysisResult.reconstitute(props, id);
    }
    /**
     * Creates a default output structure
     */
    static createDefaultOutput() {
        return {
            results: {},
            format: 'json',
            size: 0,
            checksum: '',
            postprocessingApplied: [],
            validationStatus: 'pending',
            qualityScore: 0,
        };
    }
    /**
     * Creates default metadata
     */
    static createDefaultMetadata(partial) {
        return {
            version: partial?.version ?? '1.0.0',
            algorithm: partial?.algorithm ?? 'unknown',
            parameters: partial?.parameters ?? {},
            environment: partial?.environment ?? 'production',
            resourceUsage: partial?.resourceUsage ?? this.createDefaultResourceUsage(),
            performanceMetrics: partial?.performanceMetrics ?? this.createDefaultPerformanceMetrics(),
            qualityMetrics: partial?.qualityMetrics ?? this.createDefaultQualityMetrics(),
        };
    }
    /**
     * Creates default resource usage
     */
    static createDefaultResourceUsage() {
        return {
            cpuTime: 0,
            memoryUsage: 0,
            gpuTime: 0,
            networkIO: 0,
            diskIO: 0,
        };
    }
    /**
     * Creates default performance metrics
     */
    static createDefaultPerformanceMetrics() {
        return {
            throughput: 0,
            latency: 0,
            accuracy: 0,
            precision: 0,
            recall: 0,
            f1Score: 0,
        };
    }
    /**
     * Creates default quality metrics
     */
    static createDefaultQualityMetrics() {
        return {
            dataQuality: 0,
            resultReliability: 0,
            consistencyScore: 0,
            completenessScore: 0,
        };
    }
    /**
     * Validates create request
     */
    static validateCreateRequest(request) {
        if (!request.requestId || request.requestId.trim().length === 0) {
            throw new Error('Request ID is required');
        }
        if (request.requestId.length > 255) {
            throw new Error('Request ID cannot exceed 255 characters');
        }
        if (!request.modelId) {
            throw new Error('Model ID is required');
        }
        if (!Object.values(analysis_result_entity_1.AnalysisType).includes(request.analysisType)) {
            throw new Error('Invalid analysis type');
        }
        if (!request.inputData) {
            throw new Error('Input data is required');
        }
        this.validateInputData(request.inputData);
        if (request.tags && request.tags.some(tag => !tag || tag.trim().length === 0)) {
            throw new Error('All tags must be non-empty strings');
        }
        if (request.correlationId && request.correlationId.trim().length === 0) {
            throw new Error('Correlation ID cannot be empty if provided');
        }
    }
    /**
     * Validates input data
     */
    static validateInputData(inputData) {
        if (!inputData.data) {
            throw new Error('Input data content is required');
        }
        if (!inputData.format || inputData.format.trim().length === 0) {
            throw new Error('Input data format is required');
        }
        if (inputData.size < 0) {
            throw new Error('Input data size cannot be negative');
        }
        if (!inputData.checksum || inputData.checksum.trim().length === 0) {
            throw new Error('Input data checksum is required');
        }
        if (!Array.isArray(inputData.preprocessingApplied)) {
            throw new Error('Preprocessing applied must be an array');
        }
        if (!Array.isArray(inputData.validationRules)) {
            throw new Error('Validation rules must be an array');
        }
    }
    /**
     * Validates reconstitution data
     */
    static validateReconstitutionData(data) {
        if (!data.id || !unique_entity_id_value_object_1.UniqueEntityId.isValid(data.id)) {
            throw new Error('Valid ID is required for reconstitution');
        }
        if (!data.requestId || data.requestId.trim().length === 0) {
            throw new Error('Request ID is required');
        }
        if (!data.modelId) {
            throw new Error('Model ID is required');
        }
        if (!Object.values(analysis_result_entity_1.AnalysisType).includes(data.analysisType)) {
            throw new Error('Invalid analysis type');
        }
        if (!Object.values(analysis_result_entity_1.AnalysisStatus).includes(data.status)) {
            throw new Error('Invalid analysis status');
        }
        if (!data.inputData) {
            throw new Error('Input data is required');
        }
        if (!data.metadata) {
            throw new Error('Metadata is required');
        }
        if (data.confidence < 0 || data.confidence > 1) {
            throw new Error('Confidence must be between 0 and 1');
        }
        if (data.processingTime < 0) {
            throw new Error('Processing time cannot be negative');
        }
        if (!Array.isArray(data.tags)) {
            throw new Error('Tags must be an array');
        }
        if (!Array.isArray(data.childAnalysisIds)) {
            throw new Error('Child analysis IDs must be an array');
        }
        if (!data.createdAt || !(data.createdAt instanceof Date)) {
            throw new Error('Valid creation date is required');
        }
        if (!data.updatedAt || !(data.updatedAt instanceof Date)) {
            throw new Error('Valid update date is required');
        }
        if (data.completedAt && !(data.completedAt instanceof Date)) {
            throw new Error('Completed date must be a valid Date if provided');
        }
        // Status-specific validations
        if (data.status === analysis_result_entity_1.AnalysisStatus.COMPLETED && !data.outputData) {
            throw new Error('Output data is required for completed analysis');
        }
        if (data.status === analysis_result_entity_1.AnalysisStatus.FAILED && !data.errorDetails) {
            throw new Error('Error details are required for failed analysis');
        }
    }
    /**
     * Creates a test Analysis Result with minimal configuration
     */
    static createForTesting(overrides) {
        const defaultRequest = {
            requestId: 'test-request-123',
            modelId: unique_entity_id_value_object_1.UniqueEntityId.generate(),
            analysisType: analysis_result_entity_1.AnalysisType.CLASSIFICATION,
            inputData: {
                data: { test: 'data' },
                format: 'json',
                size: 100,
                checksum: 'test-checksum',
                preprocessingApplied: [],
                validationRules: [],
            },
            tags: ['test'],
            correlationId: 'test-correlation-123',
        };
        const request = { ...defaultRequest, ...overrides };
        return this.create(request);
    }
    /**
     * Creates an Analysis Result with completed status for testing
     */
    static createCompletedForTesting(overrides) {
        const analysisResult = this.createForTesting(overrides);
        // Start processing
        analysisResult.startProcessing();
        // Complete with test data
        const outputData = {
            results: { prediction: 'test-result', confidence: 0.95 },
            format: 'json',
            size: 200,
            checksum: 'output-checksum',
            postprocessingApplied: ['normalization'],
            validationStatus: 'passed',
            qualityScore: 0.9,
        };
        analysisResult.complete(outputData, 1500, 0.95);
        return analysisResult;
    }
    /**
     * Creates an Analysis Result with failed status for testing
     */
    static createFailedForTesting(overrides) {
        const analysisResult = this.createForTesting(overrides);
        // Start processing
        analysisResult.startProcessing();
        // Fail with test error
        const errorDetails = {
            code: 'TEST_ERROR',
            message: 'Test error for unit testing',
            context: { test: true },
            retryable: true,
            category: 'processing_error',
        };
        analysisResult.fail(errorDetails);
        return analysisResult;
    }
}
exports.AnalysisResultFactory = AnalysisResultFactory;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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