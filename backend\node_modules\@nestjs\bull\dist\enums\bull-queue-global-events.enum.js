"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BullQueueGlobalEvents = void 0;
var BullQueueGlobalEvents;
(function (BullQueueGlobalEvents) {
    BullQueueGlobalEvents["ERROR"] = "global:error";
    BullQueueGlobalEvents["WAITING"] = "global:waiting";
    BullQueueGlobalEvents["ACTIVE"] = "global:active";
    BullQueueGlobalEvents["STALLED"] = "global:stalled";
    BullQueueGlobalEvents["PROGRESS"] = "global:progress";
    BullQueueGlobalEvents["COMPLETED"] = "global:completed";
    BullQueueGlobalEvents["FAILED"] = "global:failed";
    BullQueueGlobalEvents["PAUSED"] = "global:paused";
    BullQueueGlobalEvents["RESUMED"] = "global:resumed";
    BullQueueGlobalEvents["CLEANED"] = "global:cleaned";
    BullQueueGlobalEvents["DRAINED"] = "global:drained";
    BullQueueGlobalEvents["REMOVED"] = "global:removed";
})(BullQueueGlobalEvents || (exports.BullQueueGlobalEvents = BullQueueGlobalEvents = {}));
