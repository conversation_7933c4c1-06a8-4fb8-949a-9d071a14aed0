import { Injectable, Logger, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { 
  AI_MODEL_REPOSITORY,
  AiModelRepository 
} from '../../../domain/repositories/ai-model.repository.interface';
import { ModelPerformanceService } from '../optimization/model-performance.service';
import { AiCacheService } from '../caching/ai-cache.service';

/**
 * Model Selection Service
 * 
 * Intelligently selects the optimal AI model for specific tasks
 * based on performance metrics, cost, latency requirements, and availability.
 * Implements sophisticated model routing and recommendation algorithms.
 */
@Injectable()
export class ModelSelectionService {
  private readonly logger = new Logger(ModelSelectionService.name);
  private readonly modelSelectionCache = new Map<string, ModelSelectionResult>();

  constructor(
    @Inject(AI_MODEL_REPOSITORY)
    private readonly modelRepository: AiModelRepository,
    private readonly performanceService: ModelPerformanceService,
    private readonly cacheService: AiCacheService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Selects optimal model for analysis request
   */
  async selectOptimalModel(request: ModelSelectionRequest): Promise<ModelConfiguration> {
    this.logger.debug(`Selecting optimal model for request type: ${request.taskType}`);

    try {
      // Check cache for recent selection
      const cachedSelection = await this.getCachedSelection(request);
      if (cachedSelection && this.isCacheValid(cachedSelection)) {
        return cachedSelection.configuration;
      }

      // Get available models for task type
      const availableModels = await this.getAvailableModels(request.taskType);
      
      if (availableModels.length === 0) {
        throw new ModelSelectionError(`No models available for task type: ${request.taskType}`);
      }

      // Score and rank models
      const scoredModels = await this.scoreModels(availableModels, request);
      
      // Select best model based on requirements
      const selectedModel = this.selectBestModel(scoredModels, request);
      
      // Cache selection result
      await this.cacheSelection(request, selectedModel);
      
      this.logger.debug(`Selected model: ${selectedModel.modelId} for task: ${request.taskType}`);
      
      return selectedModel;
      
    } catch (error) {
      this.logger.error(`Model selection failed for task: ${request.taskType}`, error);
      throw new ModelSelectionError(`Model selection failed: ${error.message}`);
    }
  }

  /**
   * Selects model optimized for real-time processing
   */
  async selectRealTimeModel(request: ModelSelectionRequest): Promise<ModelConfiguration> {
    const realTimeRequest = {
      ...request,
      requirements: {
        ...request.requirements,
        maxLatency: 1000, // 1 second max
        priority: 'latency',
        realTime: true,
      },
    };

    return this.selectOptimalModel(realTimeRequest);
  }

  /**
   * Selects model optimized for batch processing
   */
  async selectBatchModel(request: ModelSelectionRequest): Promise<ModelConfiguration> {
    const batchRequest = {
      ...request,
      requirements: {
        ...request.requirements,
        priority: 'throughput',
        batch: true,
        costOptimized: true,
      },
    };

    return this.selectOptimalModel(batchRequest);
  }

  /**
   * Selects model optimized for high accuracy
   */
  async selectHighAccuracyModel(request: ModelSelectionRequest): Promise<ModelConfiguration> {
    const accuracyRequest = {
      ...request,
      requirements: {
        ...request.requirements,
        priority: 'accuracy',
        minAccuracy: 0.95,
        highAccuracy: true,
      },
    };

    return this.selectOptimalModel(accuracyRequest);
  }

  /**
   * Gets model recommendations for a task type
   */
  async getModelRecommendations(taskType: string): Promise<ModelRecommendation[]> {
    try {
      const availableModels = await this.getAvailableModels(taskType);
      const recommendations: ModelRecommendation[] = [];

      for (const model of availableModels) {
        const performance = await this.performanceService.getModelMetrics(model.id);
        const recommendation = this.createRecommendation(model, performance);
        recommendations.push(recommendation);
      }

      // Sort by overall score
      recommendations.sort((a, b) => b.overallScore - a.overallScore);
      
      return recommendations.slice(0, 5); // Top 5 recommendations
      
    } catch (error) {
      this.logger.error(`Failed to get model recommendations for: ${taskType}`, error);
      throw new ModelSelectionError(`Recommendations failed: ${error.message}`);
    }
  }

  /**
   * Evaluates model performance for selection
   */
  async evaluateModelPerformance(modelId: string): Promise<ModelEvaluation> {
    try {
      const model = await this.modelRepository.findById(modelId);
      if (!model) {
        throw new Error(`Model not found: ${modelId}`);
      }

      const metrics = await this.performanceService.getModelMetrics(modelId);
      const costMetrics = await this.performanceService.getCostMetrics(modelId);
      
      return {
        modelId,
        accuracy: metrics.accuracy,
        precision: metrics.precision,
        recall: metrics.recall,
        f1Score: metrics.f1Score,
        latency: metrics.averageLatency,
        throughput: metrics.throughput,
        costPerRequest: costMetrics.costPerRequest,
        availability: metrics.availability,
        lastUpdated: new Date(),
      };
      
    } catch (error) {
      this.logger.error(`Model evaluation failed for: ${modelId}`, error);
      throw new ModelSelectionError(`Evaluation failed: ${error.message}`);
    }
  }

  /**
   * Updates model selection strategy based on feedback
   */
  async updateSelectionStrategy(feedback: ModelFeedback): Promise<void> {
    try {
      // Update model performance metrics
      await this.performanceService.updateModelMetrics(feedback.modelId, {
        accuracy: feedback.actualAccuracy,
        latency: feedback.actualLatency,
        userSatisfaction: feedback.userRating,
      });

      // Adjust selection weights based on feedback
      await this.adjustSelectionWeights(feedback);
      
      // Clear related cache entries
      await this.invalidateSelectionCache(feedback.modelId);
      
      this.logger.debug(`Updated selection strategy for model: ${feedback.modelId}`);
      
    } catch (error) {
      this.logger.error(`Failed to update selection strategy`, error);
      throw new ModelSelectionError(`Strategy update failed: ${error.message}`);
    }
  }

  // Private helper methods

  private async getAvailableModels(taskType: string): Promise<ModelInfo[]> {
    const models = await this.modelRepository.findByTaskType(taskType);
    
    // Filter by availability and health status
    return models.filter(model => 
      model.status === 'active' && 
      model.healthStatus === 'healthy'
    );
  }

  private async scoreModels(
    models: ModelInfo[],
    request: ModelSelectionRequest
  ): Promise<ScoredModel[]> {
    const scoredModels: ScoredModel[] = [];

    for (const model of models) {
      const score = await this.calculateModelScore(model, request);
      scoredModels.push({ model, score });
    }

    return scoredModels.sort((a, b) => b.score.total - a.score.total);
  }

  private async calculateModelScore(
    model: ModelInfo,
    request: ModelSelectionRequest
  ): Promise<ModelScore> {
    const performance = await this.performanceService.getModelMetrics(model.id);
    const requirements = request.requirements || {};

    // Calculate individual scores (0-1 scale)
    const accuracyScore = this.calculateAccuracyScore(performance.accuracy, requirements);
    const latencyScore = this.calculateLatencyScore(performance.averageLatency, requirements);
    const costScore = this.calculateCostScore(performance.costPerRequest, requirements);
    const availabilityScore = this.calculateAvailabilityScore(performance.availability);
    const throughputScore = this.calculateThroughputScore(performance.throughput, requirements);

    // Apply weights based on requirements priority
    const weights = this.getSelectionWeights(requirements);
    
    const total = (
      accuracyScore * weights.accuracy +
      latencyScore * weights.latency +
      costScore * weights.cost +
      availabilityScore * weights.availability +
      throughputScore * weights.throughput
    );

    return {
      accuracy: accuracyScore,
      latency: latencyScore,
      cost: costScore,
      availability: availabilityScore,
      throughput: throughputScore,
      total,
    };
  }

  private selectBestModel(
    scoredModels: ScoredModel[],
    request: ModelSelectionRequest
  ): ModelConfiguration {
    if (scoredModels.length === 0) {
      throw new Error('No scored models available');
    }

    const bestModel = scoredModels[0];
    
    return {
      modelId: bestModel.model.id,
      providerType: bestModel.model.providerType,
      modelType: bestModel.model.type,
      version: bestModel.model.version,
      parameters: this.getOptimalParameters(bestModel.model, request),
      score: bestModel.score.total,
      selectedAt: new Date(),
    };
  }

  private getOptimalParameters(model: ModelInfo, request: ModelSelectionRequest): any {
    const baseParams = model.defaultParameters || {};
    const requirements = request.requirements || {};

    // Adjust parameters based on requirements
    if (requirements.realTime) {
      return {
        ...baseParams,
        temperature: 0.1, // Lower temperature for consistency
        maxTokens: Math.min(baseParams.maxTokens || 1000, 500),
      };
    }

    if (requirements.highAccuracy) {
      return {
        ...baseParams,
        temperature: 0.0, // Deterministic output
        topP: 0.9,
      };
    }

    return baseParams;
  }

  private calculateAccuracyScore(accuracy: number, requirements: any): number {
    const minAccuracy = requirements.minAccuracy || 0.7;
    return Math.max(0, Math.min(1, (accuracy - minAccuracy) / (1 - minAccuracy)));
  }

  private calculateLatencyScore(latency: number, requirements: any): number {
    const maxLatency = requirements.maxLatency || 5000; // 5 seconds default
    return Math.max(0, Math.min(1, (maxLatency - latency) / maxLatency));
  }

  private calculateCostScore(cost: number, requirements: any): number {
    const maxCost = requirements.maxCost || 0.01; // $0.01 per request default
    return Math.max(0, Math.min(1, (maxCost - cost) / maxCost));
  }

  private calculateAvailabilityScore(availability: number): number {
    return availability; // Already 0-1 scale
  }

  private calculateThroughputScore(throughput: number, requirements: any): number {
    const minThroughput = requirements.minThroughput || 10; // 10 requests/second default
    return Math.min(1, throughput / minThroughput);
  }

  private getSelectionWeights(requirements: any): SelectionWeights {
    const priority = requirements.priority || 'balanced';
    
    const weightProfiles: Record<string, SelectionWeights> = {
      accuracy: { accuracy: 0.5, latency: 0.1, cost: 0.1, availability: 0.2, throughput: 0.1 },
      latency: { accuracy: 0.1, latency: 0.5, cost: 0.1, availability: 0.2, throughput: 0.1 },
      cost: { accuracy: 0.2, latency: 0.1, cost: 0.5, availability: 0.1, throughput: 0.1 },
      throughput: { accuracy: 0.1, latency: 0.1, cost: 0.1, availability: 0.2, throughput: 0.5 },
      balanced: { accuracy: 0.25, latency: 0.25, cost: 0.2, availability: 0.2, throughput: 0.1 },
    };

    return weightProfiles[priority] || weightProfiles.balanced;
  }

  private async getCachedSelection(request: ModelSelectionRequest): Promise<ModelSelectionResult | null> {
    const cacheKey = this.generateSelectionCacheKey(request);
    return this.modelSelectionCache.get(cacheKey) || null;
  }

  private isCacheValid(selection: ModelSelectionResult): boolean {
    const cacheTimeout = this.configService.get<number>('ai.modelSelectionCacheTtl', 300000); // 5 minutes
    return Date.now() - selection.timestamp.getTime() < cacheTimeout;
  }

  private async cacheSelection(
    request: ModelSelectionRequest,
    configuration: ModelConfiguration
  ): Promise<void> {
    const cacheKey = this.generateSelectionCacheKey(request);
    const result: ModelSelectionResult = {
      configuration,
      timestamp: new Date(),
    };
    
    this.modelSelectionCache.set(cacheKey, result);
    
    // Clean up old cache entries periodically
    if (this.modelSelectionCache.size > 1000) {
      this.cleanupCache();
    }
  }

  private generateSelectionCacheKey(request: ModelSelectionRequest): string {
    const keyData = {
      taskType: request.taskType,
      requirements: request.requirements,
    };
    
    return require('crypto')
      .createHash('md5')
      .update(JSON.stringify(keyData))
      .digest('hex');
  }

  private cleanupCache(): void {
    const entries = Array.from(this.modelSelectionCache.entries());
    const cutoff = Date.now() - 600000; // 10 minutes
    
    for (const [key, value] of entries) {
      if (value.timestamp.getTime() < cutoff) {
        this.modelSelectionCache.delete(key);
      }
    }
  }

  private createRecommendation(model: ModelInfo, performance: any): ModelRecommendation {
    return {
      modelId: model.id,
      modelName: model.name,
      providerType: model.providerType,
      accuracy: performance.accuracy,
      latency: performance.averageLatency,
      costPerRequest: performance.costPerRequest,
      overallScore: this.calculateOverallScore(performance),
      strengths: this.identifyModelStrengths(model, performance),
      weaknesses: this.identifyModelWeaknesses(model, performance),
      useCases: model.recommendedUseCases || [],
    };
  }

  private calculateOverallScore(performance: any): number {
    // Simple weighted average for overall score
    return (
      performance.accuracy * 0.3 +
      (1 - performance.averageLatency / 10000) * 0.3 + // Normalize latency
      (1 - performance.costPerRequest * 1000) * 0.2 + // Normalize cost
      performance.availability * 0.2
    );
  }

  private identifyModelStrengths(model: ModelInfo, performance: any): string[] {
    const strengths: string[] = [];
    
    if (performance.accuracy > 0.9) strengths.push('High accuracy');
    if (performance.averageLatency < 1000) strengths.push('Low latency');
    if (performance.costPerRequest < 0.001) strengths.push('Cost effective');
    if (performance.availability > 0.99) strengths.push('High availability');
    
    return strengths;
  }

  private identifyModelWeaknesses(model: ModelInfo, performance: any): string[] {
    const weaknesses: string[] = [];
    
    if (performance.accuracy < 0.8) weaknesses.push('Lower accuracy');
    if (performance.averageLatency > 5000) weaknesses.push('Higher latency');
    if (performance.costPerRequest > 0.01) weaknesses.push('Higher cost');
    if (performance.availability < 0.95) weaknesses.push('Availability concerns');
    
    return weaknesses;
  }

  private async adjustSelectionWeights(feedback: ModelFeedback): Promise<void> {
    // Implementation for adjusting selection algorithm weights based on feedback
    // This would involve machine learning or statistical analysis
  }

  private async invalidateSelectionCache(modelId: string): Promise<void> {
    // Remove cache entries related to the specific model
    for (const [key, value] of this.modelSelectionCache.entries()) {
      if (value.configuration.modelId === modelId) {
        this.modelSelectionCache.delete(key);
      }
    }
  }
}

// Type definitions
interface ModelSelectionRequest {
  taskType: string;
  requirements?: {
    maxLatency?: number;
    minAccuracy?: number;
    maxCost?: number;
    minThroughput?: number;
    priority?: 'accuracy' | 'latency' | 'cost' | 'throughput' | 'balanced';
    realTime?: boolean;
    batch?: boolean;
    highAccuracy?: boolean;
    costOptimized?: boolean;
  };
  context?: any;
}

interface ModelConfiguration {
  modelId: string;
  providerType: string;
  modelType: string;
  version: string;
  parameters: any;
  score: number;
  selectedAt: Date;
}

interface ModelInfo {
  id: string;
  name: string;
  type: string;
  providerType: string;
  version: string;
  status: string;
  healthStatus: string;
  defaultParameters?: any;
  recommendedUseCases?: string[];
}

interface ModelScore {
  accuracy: number;
  latency: number;
  cost: number;
  availability: number;
  throughput: number;
  total: number;
}

interface ScoredModel {
  model: ModelInfo;
  score: ModelScore;
}

interface SelectionWeights {
  accuracy: number;
  latency: number;
  cost: number;
  availability: number;
  throughput: number;
}

interface ModelSelectionResult {
  configuration: ModelConfiguration;
  timestamp: Date;
}

interface ModelRecommendation {
  modelId: string;
  modelName: string;
  providerType: string;
  accuracy: number;
  latency: number;
  costPerRequest: number;
  overallScore: number;
  strengths: string[];
  weaknesses: string[];
  useCases: string[];
}

interface ModelEvaluation {
  modelId: string;
  accuracy: number;
  precision: number;
  recall: number;
  f1Score: number;
  latency: number;
  throughput: number;
  costPerRequest: number;
  availability: number;
  lastUpdated: Date;
}

interface ModelFeedback {
  modelId: string;
  actualAccuracy: number;
  actualLatency: number;
  userRating: number;
  comments?: string;
}

class ModelSelectionError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ModelSelectionError';
  }
}
