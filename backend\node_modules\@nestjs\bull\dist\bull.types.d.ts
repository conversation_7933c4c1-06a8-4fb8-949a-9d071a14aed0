import { Done<PERSON><PERSON>back, Job } from 'bull';
import { BullQueueAdvancedProcessor, BullQueueAdvancedSeparateProcessor } from './interfaces/bull.interfaces';
export type RequireOnlyOne<T, Keys extends keyof T = keyof T> = Pick<T, Exclude<keyof T, Keys>> & {
    [K in Keys]-?: Required<Pick<T, K>> & Partial<Record<Exclude<Keys, K>, undefined>>;
}[Keys];
export type BullQueueProcessor = BullQueueProcessorCallback | BullQueueAdvancedProcessor | BullQueueSeparateProcessor | BullQueueAdvancedSeparateProcessor;
export type BullQueueProcessorCallback = (job: Job, done?: DoneCallback) => void;
export type BullQueueSeparateProcessor = string;
export type BullQueueEvent = 'error' | 'waiting' | 'active' | 'stalled' | 'progress' | 'completed' | 'failed' | 'paused' | 'resumed' | 'cleaned' | 'drained' | 'removed' | 'global:error' | 'global:waiting' | 'global:active' | 'global:stalled' | 'global:progress' | 'global:completed' | 'global:failed' | 'global:paused' | 'global:resumed' | 'global:cleaned' | 'global:drained' | 'global:removed';
export type BullQueueEventOptions = RequireOnlyOne<{
    eventName: BullQueueEvent;
    name?: string;
    id?: string;
}, 'id' | 'name'>;
export type QueueEventDecoratorOptions = RequireOnlyOne<{
    id?: string;
    name?: string;
}, 'id' | 'name'>;
//# sourceMappingURL=bull.types.d.ts.map