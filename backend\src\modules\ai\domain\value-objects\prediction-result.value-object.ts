import { BaseValueObject } from '../../../../shared-kernel/value-objects/base-value-object';
import { ConfidenceScore } from './confidence-score.value-object';

/**
 * Prediction Result Value Object
 * 
 * Represents the result of an AI prediction with the predicted value,
 * confidence score, and additional metadata. Provides utility methods
 * for result validation, comparison, and interpretation.
 */

export interface PredictionResultProps<T = any> {
  prediction: T;
  confidence: ConfidenceScore;
  alternatives?: Array<{ value: T; confidence: ConfidenceScore }>;
  metadata?: Record<string, any>;
  timestamp: Date;
}

export class PredictionResult<T = any> extends BaseValueObject<PredictionResultProps<T>> {
  constructor(
    prediction: T,
    confidence: ConfidenceScore,
    alternatives?: Array<{ value: T; confidence: ConfidenceScore }>,
    metadata?: Record<string, any>,
    timestamp?: Date
  ) {
    super({
      prediction,
      confidence,
      alternatives: alternatives || [],
      metadata: metadata || {},
      timestamp: timestamp || new Date(),
    });
  }

  protected validate(): void {
    super.validate();
    
    if (this._value.prediction === undefined || this._value.prediction === null) {
      throw new Error('Prediction value cannot be null or undefined');
    }

    if (!(this._value.confidence instanceof ConfidenceScore)) {
      throw new Error('Confidence must be a ConfidenceScore instance');
    }

    if (!Array.isArray(this._value.alternatives)) {
      throw new Error('Alternatives must be an array');
    }

    if (!(this._value.timestamp instanceof Date)) {
      throw new Error('Timestamp must be a Date instance');
    }

    // Validate alternatives
    for (const alternative of this._value.alternatives) {
      if (!alternative.hasOwnProperty('value') || !alternative.hasOwnProperty('confidence')) {
        throw new Error('Each alternative must have value and confidence properties');
      }
      
      if (!(alternative.confidence instanceof ConfidenceScore)) {
        throw new Error('Alternative confidence must be a ConfidenceScore instance');
      }
    }

    // Validate that alternatives are sorted by confidence (highest first)
    for (let i = 0; i < this._value.alternatives.length - 1; i++) {
      if (this._value.alternatives[i].confidence.isLessThan(this._value.alternatives[i + 1].confidence)) {
        throw new Error('Alternatives must be sorted by confidence in descending order');
      }
    }
  }

  /**
   * Creates a simple prediction result with just a value and confidence
   */
  public static simple<T>(prediction: T, confidence: number): PredictionResult<T> {
    return new PredictionResult(prediction, new ConfidenceScore(confidence));
  }

  /**
   * Creates a prediction result with alternatives
   */
  public static withAlternatives<T>(
    prediction: T,
    confidence: number,
    alternatives: Array<{ value: T; confidence: number }>
  ): PredictionResult<T> {
    const sortedAlternatives = alternatives
      .map(alt => ({ value: alt.value, confidence: new ConfidenceScore(alt.confidence) }))
      .sort((a, b) => b.confidence.value - a.confidence.value);

    return new PredictionResult(
      prediction,
      new ConfidenceScore(confidence),
      sortedAlternatives
    );
  }

  /**
   * Creates a binary classification result
   */
  public static binary(
    prediction: boolean,
    confidence: number,
    positiveLabel: string = 'positive',
    negativeLabel: string = 'negative'
  ): PredictionResult<string> {
    const label = prediction ? positiveLabel : negativeLabel;
    const alternativeLabel = prediction ? negativeLabel : positiveLabel;
    const alternativeConfidence = 1 - confidence;

    return new PredictionResult(
      label,
      new ConfidenceScore(confidence),
      [{ value: alternativeLabel, confidence: new ConfidenceScore(alternativeConfidence) }]
    );
  }

  /**
   * Creates a multi-class classification result
   */
  public static multiClass<T>(
    predictions: Array<{ value: T; confidence: number }>,
    metadata?: Record<string, any>
  ): PredictionResult<T> {
    if (predictions.length === 0) {
      throw new Error('At least one prediction is required');
    }

    // Sort by confidence (highest first)
    const sorted = predictions
      .map(pred => ({ value: pred.value, confidence: new ConfidenceScore(pred.confidence) }))
      .sort((a, b) => b.confidence.value - a.confidence.value);

    const topPrediction = sorted[0];
    const alternatives = sorted.slice(1);

    return new PredictionResult(
      topPrediction.value,
      topPrediction.confidence,
      alternatives,
      metadata
    );
  }

  /**
   * Creates a regression result
   */
  public static regression(
    value: number,
    confidence: number,
    range?: { min: number; max: number },
    metadata?: Record<string, any>
  ): PredictionResult<number> {
    const resultMetadata = { ...metadata };
    if (range) {
      resultMetadata.range = range;
      resultMetadata.withinRange = value >= range.min && value <= range.max;
    }

    return new PredictionResult(
      value,
      new ConfidenceScore(confidence),
      [],
      resultMetadata
    );
  }

  // Getters
  get prediction(): T {
    return this._value.prediction;
  }

  get confidence(): ConfidenceScore {
    return this._value.confidence;
  }

  get alternatives(): Array<{ value: T; confidence: ConfidenceScore }> {
    return [...this._value.alternatives];
  }

  get metadata(): Record<string, any> {
    return { ...this._value.metadata };
  }

  get timestamp(): Date {
    return this._value.timestamp;
  }

  /**
   * Checks if the prediction has high confidence
   */
  public hasHighConfidence(threshold: number = 0.8): boolean {
    return this._value.confidence.meetsThreshold(threshold);
  }

  /**
   * Checks if the prediction has low confidence
   */
  public hasLowConfidence(threshold: number = 0.3): boolean {
    return this._value.confidence.value < threshold;
  }

  /**
   * Gets the number of alternatives
   */
  public getAlternativeCount(): number {
    return this._value.alternatives.length;
  }

  /**
   * Checks if there are alternatives available
   */
  public hasAlternatives(): boolean {
    return this._value.alternatives.length > 0;
  }

  /**
   * Gets the top N alternatives
   */
  public getTopAlternatives(n: number): Array<{ value: T; confidence: ConfidenceScore }> {
    return this._value.alternatives.slice(0, n);
  }

  /**
   * Gets alternatives above a confidence threshold
   */
  public getAlternativesAboveThreshold(threshold: number): Array<{ value: T; confidence: ConfidenceScore }> {
    return this._value.alternatives.filter(alt => alt.confidence.meetsThreshold(threshold));
  }

  /**
   * Gets the confidence gap between the prediction and the best alternative
   */
  public getConfidenceGap(): number {
    if (this._value.alternatives.length === 0) {
      return this._value.confidence.value;
    }
    
    const bestAlternative = this._value.alternatives[0];
    return this._value.confidence.value - bestAlternative.confidence.value;
  }

  /**
   * Checks if the prediction is significantly better than alternatives
   */
  public isSignificantlyBetter(threshold: number = 0.1): boolean {
    return this.getConfidenceGap() >= threshold;
  }

  /**
   * Gets the entropy of the prediction distribution
   */
  public getEntropy(): number {
    const allPredictions = [
      { confidence: this._value.confidence },
      ...this._value.alternatives
    ];

    let entropy = 0;
    for (const pred of allPredictions) {
      const p = pred.confidence.value;
      if (p > 0) {
        entropy -= p * Math.log2(p);
      }
    }

    return entropy;
  }

  /**
   * Checks if the prediction is ambiguous (low confidence gap)
   */
  public isAmbiguous(threshold: number = 0.1): boolean {
    return this.getConfidenceGap() < threshold;
  }

  /**
   * Gets the age of the prediction in milliseconds
   */
  public getAge(): number {
    return Date.now() - this._value.timestamp.getTime();
  }

  /**
   * Checks if the prediction is fresh (within time limit)
   */
  public isFresh(maxAgeMs: number): boolean {
    return this.getAge() <= maxAgeMs;
  }

  /**
   * Adds metadata to the prediction result
   */
  public withMetadata(metadata: Record<string, any>): PredictionResult<T> {
    return new PredictionResult(
      this._value.prediction,
      this._value.confidence,
      this._value.alternatives,
      { ...this._value.metadata, ...metadata },
      this._value.timestamp
    );
  }

  /**
   * Updates the confidence score
   */
  public withConfidence(confidence: ConfidenceScore): PredictionResult<T> {
    return new PredictionResult(
      this._value.prediction,
      confidence,
      this._value.alternatives,
      this._value.metadata,
      this._value.timestamp
    );
  }

  /**
   * Adds alternatives to the prediction result
   */
  public withAlternatives(alternatives: Array<{ value: T; confidence: ConfidenceScore }>): PredictionResult<T> {
    const sortedAlternatives = [...alternatives].sort((a, b) => b.confidence.value - a.confidence.value);
    
    return new PredictionResult(
      this._value.prediction,
      this._value.confidence,
      sortedAlternatives,
      this._value.metadata,
      this._value.timestamp
    );
  }

  /**
   * Compares this prediction result with another
   */
  public compareTo(other: PredictionResult<T>): number {
    return this._value.confidence.value - other._value.confidence.value;
  }

  /**
   * Checks if this prediction is better than another
   */
  public isBetterThan(other: PredictionResult<T>): boolean {
    return this._value.confidence.isGreaterThan(other._value.confidence);
  }

  /**
   * Converts to a human-readable string
   */
  public toString(): string {
    const predictionStr = typeof this._value.prediction === 'object' 
      ? JSON.stringify(this._value.prediction) 
      : String(this._value.prediction);
    
    let result = `Prediction: ${predictionStr} (${this._value.confidence.toString()})`;
    
    if (this._value.alternatives.length > 0) {
      const altStr = this._value.alternatives
        .slice(0, 3) // Show top 3 alternatives
        .map(alt => `${alt.value} (${alt.confidence.toPercentageString()})`)
        .join(', ');
      result += ` | Alternatives: ${altStr}`;
    }
    
    return result;
  }

  /**
   * Converts to JSON representation
   */
  public toJSON(): Record<string, any> {
    return {
      prediction: this._value.prediction,
      confidence: this._value.confidence.toJSON(),
      alternatives: this._value.alternatives.map(alt => ({
        value: alt.value,
        confidence: alt.confidence.toJSON(),
      })),
      metadata: this._value.metadata,
      timestamp: this._value.timestamp.toISOString(),
      hasHighConfidence: this.hasHighConfidence(),
      hasLowConfidence: this.hasLowConfidence(),
      confidenceGap: this.getConfidenceGap(),
      isAmbiguous: this.isAmbiguous(),
      entropy: this.getEntropy(),
      age: this.getAge(),
    };
  }

  /**
   * Creates a PredictionResult from JSON
   */
  public static fromJSON<T>(json: Record<string, any>): PredictionResult<T> {
    const alternatives = json.alternatives?.map((alt: any) => ({
      value: alt.value,
      confidence: ConfidenceScore.fromJSON(alt.confidence),
    })) || [];

    return new PredictionResult(
      json.prediction,
      ConfidenceScore.fromJSON(json.confidence),
      alternatives,
      json.metadata,
      new Date(json.timestamp)
    );
  }

  /**
   * Combines multiple prediction results using ensemble methods
   */
  public static ensemble<T>(
    results: PredictionResult<T>[],
    method: 'average' | 'weighted' | 'max' = 'average',
    weights?: number[]
  ): PredictionResult<T> {
    if (results.length === 0) {
      throw new Error('Cannot create ensemble from empty results');
    }

    if (method === 'weighted' && (!weights || weights.length !== results.length)) {
      throw new Error('Weights array must have the same length as results array');
    }

    // For simplicity, we'll use the most confident prediction as the ensemble result
    // In a real implementation, this would depend on the specific ensemble method
    const sortedResults = [...results].sort((a, b) => b.confidence.value - a.confidence.value);
    const bestResult = sortedResults[0];

    // Combine metadata from all results
    const combinedMetadata: Record<string, any> = results.reduce((acc, result) => ({ ...acc, ...result.metadata }), {});
    combinedMetadata.ensembleMethod = method;
    combinedMetadata.ensembleSize = results.length;

    return new PredictionResult(
      bestResult.prediction,
      bestResult.confidence,
      bestResult.alternatives,
      combinedMetadata
    );
  }
}