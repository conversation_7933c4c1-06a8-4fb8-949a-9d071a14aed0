e286448b560985d2a2a1fd65d0faa9d1
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccuracyScore = void 0;
const base_value_object_1 = require("../../../../shared-kernel/value-objects/base-value-object");
/**
 * Accuracy Score Value Object
 *
 * Represents an accuracy score for AI model performance evaluation.
 * Ensures the score is within valid bounds (0.0 to 1.0) and provides
 * utility methods for accuracy level classification and comparison.
 */
class AccuracyScore extends base_value_object_1.BaseValueObject {
    constructor(value) {
        super(value);
    }
    validate() {
        super.validate();
        if (typeof this._value !== 'number') {
            throw new Error('Accuracy score must be a number');
        }
        if (isNaN(this._value)) {
            throw new Error('Accuracy score cannot be NaN');
        }
        if (!isFinite(this._value)) {
            throw new Error('Accuracy score must be finite');
        }
        if (this._value < AccuracyScore.MIN_ACCURACY) {
            throw new Error(`Accuracy score cannot be less than ${AccuracyScore.MIN_ACCURACY}`);
        }
        if (this._value > AccuracyScore.MAX_ACCURACY) {
            throw new Error(`Accuracy score cannot be greater than ${AccuracyScore.MAX_ACCURACY}`);
        }
    }
    /**
     * Creates an accuracy score from correct and total predictions
     */
    static fromPredictions(correct, total) {
        if (total <= 0) {
            throw new Error('Total predictions must be greater than zero');
        }
        if (correct < 0) {
            throw new Error('Correct predictions cannot be negative');
        }
        if (correct > total) {
            throw new Error('Correct predictions cannot exceed total predictions');
        }
        return new AccuracyScore(correct / total);
    }
    /**
     * Creates an accuracy score from a percentage (0-100)
     */
    static fromPercentage(percentage) {
        if (percentage < 0 || percentage > 100) {
            throw new Error('Percentage must be between 0 and 100');
        }
        return new AccuracyScore(percentage / 100);
    }
    /**
     * Creates a perfect accuracy score
     */
    static perfect() {
        return new AccuracyScore(AccuracyScore.MAX_ACCURACY);
    }
    /**
     * Creates a zero accuracy score
     */
    static zero() {
        return new AccuracyScore(AccuracyScore.MIN_ACCURACY);
    }
    /**
     * Creates a random accuracy score (for baseline comparison)
     */
    static random() {
        return new AccuracyScore(0.5);
    }
    /**
     * Gets the accuracy level as a string
     */
    getLevel() {
        if (this._value < AccuracyScore.POOR_THRESHOLD) {
            return 'poor';
        }
        else if (this._value < AccuracyScore.FAIR_THRESHOLD) {
            return 'fair';
        }
        else if (this._value < AccuracyScore.GOOD_THRESHOLD) {
            return 'good';
        }
        else if (this._value < AccuracyScore.EXCELLENT_THRESHOLD) {
            return 'very_good';
        }
        else {
            return 'excellent';
        }
    }
    /**
     * Checks if the accuracy is poor
     */
    isPoor() {
        return this._value < AccuracyScore.POOR_THRESHOLD;
    }
    /**
     * Checks if the accuracy is fair
     */
    isFair() {
        return this._value >= AccuracyScore.POOR_THRESHOLD && this._value < AccuracyScore.FAIR_THRESHOLD;
    }
    /**
     * Checks if the accuracy is good
     */
    isGood() {
        return this._value >= AccuracyScore.GOOD_THRESHOLD;
    }
    /**
     * Checks if the accuracy is excellent
     */
    isExcellent() {
        return this._value >= AccuracyScore.EXCELLENT_THRESHOLD;
    }
    /**
     * Checks if the accuracy meets a minimum threshold
     */
    meetsThreshold(threshold) {
        return this._value >= threshold;
    }
    /**
     * Gets the accuracy as a percentage
     */
    toPercentage() {
        return this._value * 100;
    }
    /**
     * Gets the accuracy as a percentage string
     */
    toPercentageString(decimals = 1) {
        return `${(this._value * 100).toFixed(decimals)}%`;
    }
    /**
     * Gets the error rate (1 - accuracy)
     */
    getErrorRate() {
        return 1 - this._value;
    }
    /**
     * Gets the error rate as a percentage
     */
    getErrorRatePercentage() {
        return this.getErrorRate() * 100;
    }
    /**
     * Calculates the improvement over another accuracy score
     */
    improvementOver(baseline) {
        return this._value - baseline._value;
    }
    /**
     * Calculates the relative improvement over another accuracy score
     */
    relativeImprovementOver(baseline) {
        if (baseline._value === 0) {
            return this._value === 0 ? 0 : Infinity;
        }
        return (this._value - baseline._value) / baseline._value;
    }
    /**
     * Checks if this accuracy is significantly better than another
     */
    isSignificantlyBetterThan(other, threshold = 0.05) {
        return this.improvementOver(other) >= threshold;
    }
    /**
     * Combines this accuracy with another using weighted average
     */
    combineWith(other, weight = 0.5) {
        if (weight < 0 || weight > 1) {
            throw new Error('Weight must be between 0 and 1');
        }
        const combinedValue = this._value * weight + other._value * (1 - weight);
        return new AccuracyScore(combinedValue);
    }
    /**
     * Gets the absolute difference between this and another accuracy score
     */
    differenceFrom(other) {
        return Math.abs(this._value - other._value);
    }
    /**
     * Checks if this accuracy is greater than another
     */
    isGreaterThan(other) {
        return this._value > other._value;
    }
    /**
     * Checks if this accuracy is less than another
     */
    isLessThan(other) {
        return this._value < other._value;
    }
    /**
     * Rounds the accuracy to a specified number of decimal places
     */
    round(decimals = 3) {
        const factor = Math.pow(10, decimals);
        const rounded = Math.round(this._value * factor) / factor;
        return new AccuracyScore(rounded);
    }
    /**
     * Converts to a human-readable string
     */
    toString() {
        return `${this.toPercentageString()} (${this.getLevel()})`;
    }
    /**
     * Converts to JSON representation
     */
    toJSON() {
        return {
            value: this._value,
            percentage: this.toPercentage(),
            level: this.getLevel(),
            errorRate: this.getErrorRate(),
            errorRatePercentage: this.getErrorRatePercentage(),
            isGood: this.isGood(),
            isExcellent: this.isExcellent(),
        };
    }
    /**
     * Creates an AccuracyScore from JSON
     */
    static fromJSON(json) {
        return new AccuracyScore(json.value);
    }
    /**
     * Calculates the average of multiple accuracy scores
     */
    static average(scores) {
        if (scores.length === 0) {
            throw new Error('Cannot calculate average of empty array');
        }
        const sum = scores.reduce((acc, score) => acc + score._value, 0);
        return new AccuracyScore(sum / scores.length);
    }
    /**
     * Calculates the weighted average of multiple accuracy scores
     */
    static weightedAverage(scores, weights) {
        if (scores.length === 0) {
            throw new Error('Cannot calculate weighted average of empty array');
        }
        if (scores.length !== weights.length) {
            throw new Error('Scores and weights arrays must have the same length');
        }
        const totalWeight = weights.reduce((acc, weight) => acc + weight, 0);
        if (totalWeight === 0) {
            throw new Error('Total weight cannot be zero');
        }
        const weightedSum = scores.reduce((acc, score, index) => acc + score._value * weights[index], 0);
        return new AccuracyScore(weightedSum / totalWeight);
    }
    /**
     * Gets the maximum accuracy from an array
     */
    static max(scores) {
        if (scores.length === 0) {
            throw new Error('Cannot find maximum of empty array');
        }
        const maxValue = Math.max(...scores.map(score => score._value));
        return new AccuracyScore(maxValue);
    }
    /**
     * Gets the minimum accuracy from an array
     */
    static min(scores) {
        if (scores.length === 0) {
            throw new Error('Cannot find minimum of empty array');
        }
        const minValue = Math.min(...scores.map(score => score._value));
        return new AccuracyScore(minValue);
    }
}
exports.AccuracyScore = AccuracyScore;
AccuracyScore.MIN_ACCURACY = 0.0;
AccuracyScore.MAX_ACCURACY = 1.0;
// Accuracy level thresholds
AccuracyScore.POOR_THRESHOLD = 0.5;
AccuracyScore.FAIR_THRESHOLD = 0.7;
AccuracyScore.GOOD_THRESHOLD = 0.8;
AccuracyScore.EXCELLENT_THRESHOLD = 0.95;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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