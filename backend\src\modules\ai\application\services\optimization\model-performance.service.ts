import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * Model Performance Service
 * 
 * Tracks and analyzes AI model performance metrics including
 * accuracy, latency, cost, and availability. Provides performance
 * optimization recommendations and trend analysis.
 */
@Injectable()
export class ModelPerformanceService {
  private readonly logger = new Logger(ModelPerformanceService.name);
  private readonly performanceData = new Map<string, ModelPerformanceData>();
  private readonly costData = new Map<string, ModelCostData>();

  constructor(private readonly configService: ConfigService) {}

  /**
   * Gets performance metrics for a model
   */
  async getModelMetrics(modelId: string): Promise<ModelMetrics> {
    const data = this.performanceData.get(modelId);
    
    if (!data) {
      // Return default metrics for new models
      return {
        accuracy: 0.8,
        precision: 0.8,
        recall: 0.8,
        f1Score: 0.8,
        averageLatency: 2000,
        throughput: 10,
        availability: 0.95,
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        lastUpdated: new Date(),
      };
    }

    return this.calculateMetrics(data);
  }

  /**
   * Gets cost metrics for a model
   */
  async getCostMetrics(modelId: string): Promise<ModelCostMetrics> {
    const data = this.costData.get(modelId);
    
    if (!data) {
      return {
        costPerRequest: 0.01,
        totalCost: 0,
        costTrend: 'stable',
        lastUpdated: new Date(),
      };
    }

    return {
      costPerRequest: data.totalCost / Math.max(data.totalRequests, 1),
      totalCost: data.totalCost,
      costTrend: this.calculateCostTrend(data),
      lastUpdated: data.lastUpdated,
    };
  }

  /**
   * Updates model performance metrics
   */
  async updateModelMetrics(
    modelId: string, 
    update: ModelMetricsUpdate
  ): Promise<void> {
    let data = this.performanceData.get(modelId);
    
    if (!data) {
      data = {
        modelId,
        accuracyHistory: [],
        latencyHistory: [],
        requestHistory: [],
        errorHistory: [],
        createdAt: new Date(),
        lastUpdated: new Date(),
      };
      this.performanceData.set(modelId, data);
    }

    // Update accuracy if provided
    if (update.accuracy !== undefined) {
      data.accuracyHistory.push({
        value: update.accuracy,
        timestamp: new Date(),
      });
      this.trimHistory(data.accuracyHistory);
    }

    // Update latency if provided
    if (update.latency !== undefined) {
      data.latencyHistory.push({
        value: update.latency,
        timestamp: new Date(),
      });
      this.trimHistory(data.latencyHistory);
    }

    // Update user satisfaction if provided
    if (update.userSatisfaction !== undefined) {
      // Store user satisfaction as a special metric
      data.accuracyHistory.push({
        value: update.userSatisfaction / 5, // Convert 1-5 scale to 0-1
        timestamp: new Date(),
        type: 'user_satisfaction',
      });
    }

    data.lastUpdated = new Date();
    
    this.logger.debug(`Updated performance metrics for model: ${modelId}`);
  }

  /**
   * Records a request completion
   */
  async recordRequest(
    modelId: string,
    request: RequestRecord
  ): Promise<void> {
    let data = this.performanceData.get(modelId);
    
    if (!data) {
      data = {
        modelId,
        accuracyHistory: [],
        latencyHistory: [],
        requestHistory: [],
        errorHistory: [],
        createdAt: new Date(),
        lastUpdated: new Date(),
      };
      this.performanceData.set(modelId, data);
    }

    // Record request
    data.requestHistory.push({
      timestamp: new Date(),
      success: request.success,
      latency: request.latency,
      cost: request.cost,
    });
    this.trimHistory(data.requestHistory);

    // Record latency
    if (request.latency) {
      data.latencyHistory.push({
        value: request.latency,
        timestamp: new Date(),
      });
      this.trimHistory(data.latencyHistory);
    }

    // Record error if request failed
    if (!request.success && request.error) {
      data.errorHistory.push({
        error: request.error,
        timestamp: new Date(),
      });
      this.trimHistory(data.errorHistory);
    }

    // Update cost data
    if (request.cost) {
      await this.updateCostData(modelId, request.cost);
    }

    data.lastUpdated = new Date();
  }

  /**
   * Gets performance trends for a model
   */
  async getPerformanceTrends(modelId: string): Promise<PerformanceTrends> {
    const data = this.performanceData.get(modelId);
    
    if (!data) {
      return {
        accuracyTrend: 'stable',
        latencyTrend: 'stable',
        errorRateTrend: 'stable',
        throughputTrend: 'stable',
      };
    }

    return {
      accuracyTrend: this.calculateTrend(data.accuracyHistory.map(h => h.value)),
      latencyTrend: this.calculateTrend(data.latencyHistory.map(h => h.value)),
      errorRateTrend: this.calculateErrorRateTrend(data),
      throughputTrend: this.calculateThroughputTrend(data),
    };
  }

  /**
   * Gets performance comparison between models
   */
  async compareModels(modelIds: string[]): Promise<ModelComparison[]> {
    const comparisons: ModelComparison[] = [];
    
    for (const modelId of modelIds) {
      const metrics = await this.getModelMetrics(modelId);
      const costMetrics = await this.getCostMetrics(modelId);
      const trends = await this.getPerformanceTrends(modelId);
      
      comparisons.push({
        modelId,
        metrics,
        costMetrics,
        trends,
        overallScore: this.calculateOverallScore(metrics, costMetrics),
      });
    }
    
    return comparisons.sort((a, b) => b.overallScore - a.overallScore);
  }

  /**
   * Gets performance alerts for models
   */
  async getPerformanceAlerts(): Promise<PerformanceAlert[]> {
    const alerts: PerformanceAlert[] = [];
    
    for (const [modelId, data] of this.performanceData) {
      const metrics = this.calculateMetrics(data);
      
      // Check accuracy degradation
      if (metrics.accuracy < 0.8) {
        alerts.push({
          modelId,
          type: 'accuracy_degradation',
          severity: metrics.accuracy < 0.7 ? 'high' : 'medium',
          message: `Model accuracy dropped to ${(metrics.accuracy * 100).toFixed(1)}%`,
          timestamp: new Date(),
        });
      }
      
      // Check high latency
      if (metrics.averageLatency > 5000) {
        alerts.push({
          modelId,
          type: 'high_latency',
          severity: metrics.averageLatency > 10000 ? 'high' : 'medium',
          message: `Model latency increased to ${metrics.averageLatency}ms`,
          timestamp: new Date(),
        });
      }
      
      // Check high error rate
      const errorRate = metrics.failedRequests / Math.max(metrics.totalRequests, 1);
      if (errorRate > 0.1) {
        alerts.push({
          modelId,
          type: 'high_error_rate',
          severity: errorRate > 0.2 ? 'high' : 'medium',
          message: `Model error rate increased to ${(errorRate * 100).toFixed(1)}%`,
          timestamp: new Date(),
        });
      }
    }
    
    return alerts;
  }

  /**
   * Gets optimization recommendations for a model
   */
  async getOptimizationRecommendations(modelId: string): Promise<OptimizationRecommendation[]> {
    const metrics = await this.getModelMetrics(modelId);
    const trends = await this.getPerformanceTrends(modelId);
    const recommendations: OptimizationRecommendation[] = [];
    
    // Accuracy recommendations
    if (metrics.accuracy < 0.85) {
      recommendations.push({
        type: 'accuracy_improvement',
        priority: 'high',
        description: 'Consider retraining the model with more diverse data',
        expectedImpact: 'Improve accuracy by 5-10%',
        effort: 'high',
      });
    }
    
    // Latency recommendations
    if (metrics.averageLatency > 3000) {
      recommendations.push({
        type: 'latency_optimization',
        priority: 'medium',
        description: 'Optimize model parameters or use model quantization',
        expectedImpact: 'Reduce latency by 20-30%',
        effort: 'medium',
      });
    }
    
    // Cost recommendations
    const costMetrics = await this.getCostMetrics(modelId);
    if (costMetrics.costPerRequest > 0.02) {
      recommendations.push({
        type: 'cost_optimization',
        priority: 'medium',
        description: 'Consider using a more cost-effective model variant',
        expectedImpact: 'Reduce cost by 30-50%',
        effort: 'low',
      });
    }
    
    return recommendations;
  }

  // Private helper methods

  private calculateMetrics(data: ModelPerformanceData): ModelMetrics {
    const recentRequests = data.requestHistory.slice(-100); // Last 100 requests
    const recentLatencies = data.latencyHistory.slice(-50); // Last 50 latency measurements
    const recentAccuracies = data.accuracyHistory.slice(-20); // Last 20 accuracy measurements
    
    const totalRequests = recentRequests.length;
    const successfulRequests = recentRequests.filter(r => r.success).length;
    const failedRequests = totalRequests - successfulRequests;
    
    const averageLatency = recentLatencies.length > 0
      ? recentLatencies.reduce((sum, l) => sum + l.value, 0) / recentLatencies.length
      : 2000;
    
    const accuracy = recentAccuracies.length > 0
      ? recentAccuracies.reduce((sum, a) => sum + a.value, 0) / recentAccuracies.length
      : 0.8;
    
    const throughput = totalRequests > 0 
      ? totalRequests / Math.max(1, (Date.now() - data.createdAt.getTime()) / 1000)
      : 0;
    
    return {
      accuracy,
      precision: accuracy * 0.95, // Approximation
      recall: accuracy * 0.9, // Approximation
      f1Score: accuracy * 0.92, // Approximation
      averageLatency,
      throughput,
      availability: successfulRequests / Math.max(totalRequests, 1),
      totalRequests,
      successfulRequests,
      failedRequests,
      lastUpdated: data.lastUpdated,
    };
  }

  private async updateCostData(modelId: string, cost: number): Promise<void> {
    let data = this.costData.get(modelId);
    
    if (!data) {
      data = {
        modelId,
        totalCost: 0,
        totalRequests: 0,
        costHistory: [],
        createdAt: new Date(),
        lastUpdated: new Date(),
      };
      this.costData.set(modelId, data);
    }
    
    data.totalCost += cost;
    data.totalRequests++;
    data.costHistory.push({
      cost,
      timestamp: new Date(),
    });
    
    this.trimHistory(data.costHistory);
    data.lastUpdated = new Date();
  }

  private calculateCostTrend(data: ModelCostData): 'increasing' | 'decreasing' | 'stable' {
    if (data.costHistory.length < 10) {
      return 'stable';
    }
    
    const recent = data.costHistory.slice(-10);
    const older = data.costHistory.slice(-20, -10);
    
    const recentAvg = recent.reduce((sum, c) => sum + c.cost, 0) / recent.length;
    const olderAvg = older.reduce((sum, c) => sum + c.cost, 0) / older.length;
    
    const change = (recentAvg - olderAvg) / olderAvg;
    
    if (change > 0.1) return 'increasing';
    if (change < -0.1) return 'decreasing';
    return 'stable';
  }

  private calculateTrend(values: number[]): 'improving' | 'degrading' | 'stable' {
    if (values.length < 5) {
      return 'stable';
    }
    
    const recent = values.slice(-5);
    const older = values.slice(-10, -5);
    
    if (older.length === 0) {
      return 'stable';
    }
    
    const recentAvg = recent.reduce((sum, v) => sum + v, 0) / recent.length;
    const olderAvg = older.reduce((sum, v) => sum + v, 0) / older.length;
    
    const change = (recentAvg - olderAvg) / olderAvg;
    
    if (change > 0.05) return 'improving';
    if (change < -0.05) return 'degrading';
    return 'stable';
  }

  private calculateErrorRateTrend(data: ModelPerformanceData): 'improving' | 'degrading' | 'stable' {
    const recentRequests = data.requestHistory.slice(-20);
    const olderRequests = data.requestHistory.slice(-40, -20);
    
    if (recentRequests.length === 0 || olderRequests.length === 0) {
      return 'stable';
    }
    
    const recentErrorRate = recentRequests.filter(r => !r.success).length / recentRequests.length;
    const olderErrorRate = olderRequests.filter(r => !r.success).length / olderRequests.length;
    
    const change = recentErrorRate - olderErrorRate;
    
    if (change > 0.05) return 'degrading';
    if (change < -0.05) return 'improving';
    return 'stable';
  }

  private calculateThroughputTrend(data: ModelPerformanceData): 'improving' | 'degrading' | 'stable' {
    const now = Date.now();
    const oneHourAgo = now - 3600000;
    const twoHoursAgo = now - 7200000;
    
    const recentRequests = data.requestHistory.filter(
      r => r.timestamp.getTime() > oneHourAgo
    ).length;
    
    const olderRequests = data.requestHistory.filter(
      r => r.timestamp.getTime() > twoHoursAgo && r.timestamp.getTime() <= oneHourAgo
    ).length;
    
    if (olderRequests === 0) {
      return 'stable';
    }
    
    const change = (recentRequests - olderRequests) / olderRequests;
    
    if (change > 0.2) return 'improving';
    if (change < -0.2) return 'degrading';
    return 'stable';
  }

  private calculateOverallScore(
    metrics: ModelMetrics, 
    costMetrics: ModelCostMetrics
  ): number {
    const accuracyScore = metrics.accuracy;
    const latencyScore = Math.max(0, 1 - (metrics.averageLatency / 10000));
    const availabilityScore = metrics.availability;
    const costScore = Math.max(0, 1 - (costMetrics.costPerRequest / 0.1));
    
    return (
      accuracyScore * 0.3 +
      latencyScore * 0.25 +
      availabilityScore * 0.25 +
      costScore * 0.2
    );
  }

  private trimHistory<T>(history: T[], maxLength: number = 1000): void {
    if (history.length > maxLength) {
      history.splice(0, history.length - maxLength);
    }
  }
}

// Type definitions
interface ModelPerformanceData {
  modelId: string;
  accuracyHistory: Array<{ value: number; timestamp: Date; type?: string }>;
  latencyHistory: Array<{ value: number; timestamp: Date }>;
  requestHistory: Array<{ timestamp: Date; success: boolean; latency?: number; cost?: number }>;
  errorHistory: Array<{ error: string; timestamp: Date }>;
  createdAt: Date;
  lastUpdated: Date;
}

interface ModelCostData {
  modelId: string;
  totalCost: number;
  totalRequests: number;
  costHistory: Array<{ cost: number; timestamp: Date }>;
  createdAt: Date;
  lastUpdated: Date;
}

interface ModelMetrics {
  accuracy: number;
  precision: number;
  recall: number;
  f1Score: number;
  averageLatency: number;
  throughput: number;
  availability: number;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  lastUpdated: Date;
}

interface ModelCostMetrics {
  costPerRequest: number;
  totalCost: number;
  costTrend: 'increasing' | 'decreasing' | 'stable';
  lastUpdated: Date;
}

interface ModelMetricsUpdate {
  accuracy?: number;
  latency?: number;
  userSatisfaction?: number;
}

interface RequestRecord {
  success: boolean;
  latency?: number;
  cost?: number;
  error?: string;
}

interface PerformanceTrends {
  accuracyTrend: 'improving' | 'degrading' | 'stable';
  latencyTrend: 'improving' | 'degrading' | 'stable';
  errorRateTrend: 'improving' | 'degrading' | 'stable';
  throughputTrend: 'improving' | 'degrading' | 'stable';
}

interface ModelComparison {
  modelId: string;
  metrics: ModelMetrics;
  costMetrics: ModelCostMetrics;
  trends: PerformanceTrends;
  overallScore: number;
}

interface PerformanceAlert {
  modelId: string;
  type: 'accuracy_degradation' | 'high_latency' | 'high_error_rate';
  severity: 'low' | 'medium' | 'high';
  message: string;
  timestamp: Date;
}

interface OptimizationRecommendation {
  type: 'accuracy_improvement' | 'latency_optimization' | 'cost_optimization';
  priority: 'low' | 'medium' | 'high';
  description: string;
  expectedImpact: string;
  effort: 'low' | 'medium' | 'high';
}