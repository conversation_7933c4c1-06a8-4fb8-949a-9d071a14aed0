{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai-ml\\domain\\entities\\model-configuration.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCAQiB;AACjB,+DAAoD;AAEpD;;;GAGG;AAOI,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IAyP7B;;OAEG;IACH,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,YAAY,EAAE,aAAa,IAAI,KAAK,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,YAAY,EAAE,iBAAiB,IAAI,KAAK,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,UAAU,EAAE,cAAc,IAAI,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,UAAU,EAAE,iBAAiB,IAAI,EAAE,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,WAAmB,EAAE,eAAuB,CAAC;QAClE,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAO,CAAC,CAAC;QAE/B,MAAM,SAAS,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,IAAI,CAAC,CAAC,GAAG,WAAW,CAAC;QACtE,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,eAAe,IAAI,CAAC,CAAC,GAAG,YAAY,CAAC;QACzE,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,IAAI,CAAC,CAAC;QAErD,OAAO,SAAS,GAAG,UAAU,GAAG,WAAW,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,MAAc,EAAE,YAAoB,EAAE,IAAY,EAAE,OAAgB;QACnF,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,IAAI,CAAC,UAAU,GAAG;gBAChB,aAAa,EAAE,CAAC;gBAChB,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,CAAC;gBACZ,mBAAmB,EAAE,CAAC;gBACtB,WAAW,EAAE,CAAC;aACf,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACzE,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC;QAC1E,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;QACpE,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAEpD,+BAA+B;QAC/B,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;QACpD,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,mBAAmB,IAAI,CAAC,CAAC;QAC5D,IAAI,CAAC,UAAU,CAAC,mBAAmB,GAAG,CAAC,UAAU,GAAG,CAAC,aAAa,GAAG,CAAC,CAAC,GAAG,YAAY,CAAC,GAAG,aAAa,CAAC;QAExG,sBAAsB;QACtB,MAAM,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,IAAI,CAAC,CAAC,GAAG,CAAC,aAAa,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;QACtG,MAAM,qBAAqB,GAAG,kBAAkB,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrE,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,CAAC,qBAAqB,GAAG,aAAa,CAAC,GAAG,GAAG,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,wBAAwB,CAAC,OAA0D;QACjF,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC7B,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;QAC/B,CAAC;QACD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,YAAsE;QACxF,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAO,KAAK,CAAC;QAEnC,IAAI,IAAI,CAAC,UAAU,CAAC,iBAAiB,IAAI,YAAY,CAAC,iBAAiB,EAAE,CAAC;YACxE,IAAI,YAAY,CAAC,iBAAiB,IAAI,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;gBACxE,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,CAAC,eAAe,IAAI,YAAY,CAAC,eAAe,EAAE,CAAC;YACpE,IAAI,YAAY,CAAC,eAAe,IAAI,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC;gBACpE,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACxC,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACxC,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACrC,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YAC/C,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;QAC5D,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;CACF,CAAA;AAlYY,gDAAkB;AAE7B;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;8CACpB;AAMX;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;gDACzB;AAMb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;uDAC1B;AAMpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uDACpB;AAkBrB;IAbC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE;YACJ,uBAAuB;YACvB,mBAAmB;YACnB,eAAe;YACf,kBAAkB;YAClB,eAAe;YACf,gBAAgB;YAChB,qBAAqB;YACrB,kBAAkB;SACnB;KACF,CAAC;;gDACW;AAUb;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,QAAQ,EAAE,cAAc,EAAE,WAAW,EAAE,WAAW,EAAE,aAAa,EAAE,aAAa,EAAE,QAAQ,CAAC;QAClG,OAAO,EAAE,QAAQ;KAClB,CAAC;;oDACyG;AAM3G;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;mDACR;AAUhB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,CAAC;QACjE,OAAO,EAAE,QAAQ;KAClB,CAAC;;kDACoE;AAMtE;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uDAC5B;AAMrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDAC5B;AAMhB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sDAUxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sDAS7D;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sDAO7D;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,qBAAqB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;8DAUrE;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;wDASxC;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0DAOjE;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uDAO9D;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;wDAS/D;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sDAQ7D;AAMF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;gDAC1B;AAUhB;IALC,IAAA,gBAAM,EAAC;QACN,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,CAAC,aAAa,EAAE,SAAS,EAAE,YAAY,CAAC;QAC9C,OAAO,EAAE,aAAa;KACvB,CAAC;;uDACoD;AAMtD;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;qDAC5B;AAMnB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAC/B,MAAM,oBAAN,MAAM;oDAAc;AAM/B;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;qDAC3B;AAMlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDAC1C;AAGnB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;qDAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;qDAAC;AAIhB;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,iCAAW,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,kBAAkB,CAAC;;wDAChC;6BAvPjB,kBAAkB;IAN9B,IAAA,gBAAM,EAAC,sBAAsB,CAAC;IAC9B,IAAA,eAAK,EAAC,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;IACjC,IAAA,eAAK,EAAC,CAAC,MAAM,CAAC,CAAC;IACf,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,SAAS,CAAC,CAAC;GACN,kBAAkB,CAkY9B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai-ml\\domain\\entities\\model-configuration.entity.ts"], "sourcesContent": ["import {\r\n  Entity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  OneToMany,\r\n} from 'typeorm';\r\nimport { AnalysisJob } from './analysis-job.entity';\r\n\r\n/**\r\n * Model Configuration entity\r\n * Represents AI/ML model configurations and settings\r\n */\r\n@Entity('model_configurations')\r\n@Index(['name'], { unique: true })\r\n@Index(['type'])\r\n@Index(['provider'])\r\n@Index(['status'])\r\n@Index(['version'])\r\nexport class ModelConfiguration {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  /**\r\n   * Model name/identifier\r\n   */\r\n  @Column({ unique: true, length: 255 })\r\n  name: string;\r\n\r\n  /**\r\n   * Model display name\r\n   */\r\n  @Column({ name: 'display_name', length: 255 })\r\n  displayName: string;\r\n\r\n  /**\r\n   * Model description\r\n   */\r\n  @Column({ type: 'text', nullable: true })\r\n  description?: string;\r\n\r\n  /**\r\n   * Model type\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: [\r\n      'vulnerability_scanner',\r\n      'threat_classifier',\r\n      'risk_assessor',\r\n      'anomaly_detector',\r\n      'nlp_processor',\r\n      'image_analyzer',\r\n      'behavioral_analyzer',\r\n      'predictive_model',\r\n    ],\r\n  })\r\n  type: string;\r\n\r\n  /**\r\n   * AI service provider\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['openai', 'azure_openai', 'anthropic', 'google_ai', 'aws_bedrock', 'huggingface', 'custom'],\r\n    default: 'openai',\r\n  })\r\n  provider: 'openai' | 'azure_openai' | 'anthropic' | 'google_ai' | 'aws_bedrock' | 'huggingface' | 'custom';\r\n\r\n  /**\r\n   * Model version\r\n   */\r\n  @Column({ length: 100 })\r\n  version: string;\r\n\r\n  /**\r\n   * Model status\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['active', 'inactive', 'deprecated', 'training', 'testing'],\r\n    default: 'active',\r\n  })\r\n  status: 'active' | 'inactive' | 'deprecated' | 'training' | 'testing';\r\n\r\n  /**\r\n   * Model endpoint URL\r\n   */\r\n  @Column({ name: 'endpoint_url', nullable: true })\r\n  endpointUrl?: string;\r\n\r\n  /**\r\n   * API key or authentication token\r\n   */\r\n  @Column({ name: 'api_key', nullable: true })\r\n  apiKey?: string;\r\n\r\n  /**\r\n   * Model parameters and configuration\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  parameters?: {\r\n    temperature?: number;\r\n    maxTokens?: number;\r\n    topP?: number;\r\n    frequencyPenalty?: number;\r\n    presencePenalty?: number;\r\n    stopSequences?: string[];\r\n    systemPrompt?: string;\r\n    contextWindow?: number;\r\n  };\r\n\r\n  /**\r\n   * Rate limiting configuration\r\n   */\r\n  @Column({ name: 'rate_limits', type: 'jsonb', nullable: true })\r\n  rateLimits?: {\r\n    requestsPerMinute?: number;\r\n    requestsPerHour?: number;\r\n    requestsPerDay?: number;\r\n    tokensPerMinute?: number;\r\n    tokensPerHour?: number;\r\n    tokensPerDay?: number;\r\n    concurrentRequests?: number;\r\n  };\r\n\r\n  /**\r\n   * Cost configuration\r\n   */\r\n  @Column({ name: 'cost_config', type: 'jsonb', nullable: true })\r\n  costConfig?: {\r\n    inputTokenCost?: number;\r\n    outputTokenCost?: number;\r\n    requestCost?: number;\r\n    currency?: string;\r\n    billingModel?: 'per_token' | 'per_request' | 'subscription';\r\n  };\r\n\r\n  /**\r\n   * Performance metrics\r\n   */\r\n  @Column({ name: 'performance_metrics', type: 'jsonb', nullable: true })\r\n  performanceMetrics?: {\r\n    averageLatency?: number;\r\n    throughput?: number;\r\n    accuracy?: number;\r\n    precision?: number;\r\n    recall?: number;\r\n    f1Score?: number;\r\n    uptime?: number;\r\n    errorRate?: number;\r\n  };\r\n\r\n  /**\r\n   * Model capabilities\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  capabilities?: {\r\n    supportedInputTypes?: string[];\r\n    supportedOutputTypes?: string[];\r\n    maxInputSize?: number;\r\n    maxOutputSize?: number;\r\n    supportsBatch?: boolean;\r\n    supportsStreaming?: boolean;\r\n    supportsFineTuning?: boolean;\r\n  };\r\n\r\n  /**\r\n   * Circuit breaker configuration\r\n   */\r\n  @Column({ name: 'circuit_breaker', type: 'jsonb', nullable: true })\r\n  circuitBreaker?: {\r\n    enabled?: boolean;\r\n    failureThreshold?: number;\r\n    recoveryTimeout?: number;\r\n    monitoringPeriod?: number;\r\n    halfOpenMaxCalls?: number;\r\n  };\r\n\r\n  /**\r\n   * Retry configuration\r\n   */\r\n  @Column({ name: 'retry_config', type: 'jsonb', nullable: true })\r\n  retryConfig?: {\r\n    maxRetries?: number;\r\n    initialDelay?: number;\r\n    maxDelay?: number;\r\n    backoffMultiplier?: number;\r\n    retryableErrors?: string[];\r\n  };\r\n\r\n  /**\r\n   * Model training information\r\n   */\r\n  @Column({ name: 'training_info', type: 'jsonb', nullable: true })\r\n  trainingInfo?: {\r\n    trainingData?: string;\r\n    trainingDate?: string;\r\n    datasetSize?: number;\r\n    epochs?: number;\r\n    validationAccuracy?: number;\r\n    testAccuracy?: number;\r\n    hyperparameters?: Record<string, any>;\r\n  };\r\n\r\n  /**\r\n   * Usage statistics\r\n   */\r\n  @Column({ name: 'usage_stats', type: 'jsonb', nullable: true })\r\n  usageStats?: {\r\n    totalRequests?: number;\r\n    totalTokens?: number;\r\n    totalCost?: number;\r\n    averageResponseTime?: number;\r\n    successRate?: number;\r\n    lastUsed?: string;\r\n  };\r\n\r\n  /**\r\n   * Model tags for categorization\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  tags?: string[];\r\n\r\n  /**\r\n   * Environment where model is deployed\r\n   */\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ['development', 'staging', 'production'],\r\n    default: 'development',\r\n  })\r\n  environment: 'development' | 'staging' | 'production';\r\n\r\n  /**\r\n   * Whether model is default for its type\r\n   */\r\n  @Column({ name: 'is_default', default: false })\r\n  isDefault: boolean;\r\n\r\n  /**\r\n   * Additional metadata\r\n   */\r\n  @Column({ type: 'jsonb', nullable: true })\r\n  metadata?: Record<string, any>;\r\n\r\n  /**\r\n   * User who created the configuration\r\n   */\r\n  @Column({ name: 'created_by', type: 'uuid' })\r\n  createdBy: string;\r\n\r\n  /**\r\n   * User who last updated the configuration\r\n   */\r\n  @Column({ name: 'updated_by', type: 'uuid', nullable: true })\r\n  updatedBy?: string;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relationships\r\n  @OneToMany(() => AnalysisJob, job => job.modelConfiguration)\r\n  analysisJobs: AnalysisJob[];\r\n\r\n  /**\r\n   * Check if model is available for use\r\n   */\r\n  get isAvailable(): boolean {\r\n    return this.status === 'active';\r\n  }\r\n\r\n  /**\r\n   * Check if model supports batch processing\r\n   */\r\n  get supportsBatch(): boolean {\r\n    return this.capabilities?.supportsBatch || false;\r\n  }\r\n\r\n  /**\r\n   * Check if model supports streaming\r\n   */\r\n  get supportsStreaming(): boolean {\r\n    return this.capabilities?.supportsStreaming || false;\r\n  }\r\n\r\n  /**\r\n   * Get current cost per token\r\n   */\r\n  get costPerToken(): number {\r\n    return this.costConfig?.inputTokenCost || 0;\r\n  }\r\n\r\n  /**\r\n   * Get rate limit for requests per minute\r\n   */\r\n  get requestsPerMinute(): number {\r\n    return this.rateLimits?.requestsPerMinute || 60;\r\n  }\r\n\r\n  /**\r\n   * Calculate estimated cost for a request\r\n   */\r\n  calculateEstimatedCost(inputTokens: number, outputTokens: number = 0): number {\r\n    if (!this.costConfig) return 0;\r\n\r\n    const inputCost = (this.costConfig.inputTokenCost || 0) * inputTokens;\r\n    const outputCost = (this.costConfig.outputTokenCost || 0) * outputTokens;\r\n    const requestCost = this.costConfig.requestCost || 0;\r\n\r\n    return inputCost + outputCost + requestCost;\r\n  }\r\n\r\n  /**\r\n   * Update usage statistics\r\n   */\r\n  updateUsageStats(tokens: number, responseTime: number, cost: number, success: boolean): void {\r\n    if (!this.usageStats) {\r\n      this.usageStats = {\r\n        totalRequests: 0,\r\n        totalTokens: 0,\r\n        totalCost: 0,\r\n        averageResponseTime: 0,\r\n        successRate: 0,\r\n      };\r\n    }\r\n\r\n    this.usageStats.totalRequests = (this.usageStats.totalRequests || 0) + 1;\r\n    this.usageStats.totalTokens = (this.usageStats.totalTokens || 0) + tokens;\r\n    this.usageStats.totalCost = (this.usageStats.totalCost || 0) + cost;\r\n    this.usageStats.lastUsed = new Date().toISOString();\r\n\r\n    // Update average response time\r\n    const totalRequests = this.usageStats.totalRequests;\r\n    const currentAvg = this.usageStats.averageResponseTime || 0;\r\n    this.usageStats.averageResponseTime = (currentAvg * (totalRequests - 1) + responseTime) / totalRequests;\r\n\r\n    // Update success rate\r\n    const successfulRequests = Math.round((this.usageStats.successRate || 0) * (totalRequests - 1) / 100);\r\n    const newSuccessfulRequests = successfulRequests + (success ? 1 : 0);\r\n    this.usageStats.successRate = (newSuccessfulRequests / totalRequests) * 100;\r\n  }\r\n\r\n  /**\r\n   * Update performance metrics\r\n   */\r\n  updatePerformanceMetrics(metrics: Partial<ModelConfiguration['performanceMetrics']>): void {\r\n    if (!this.performanceMetrics) {\r\n      this.performanceMetrics = {};\r\n    }\r\n    Object.assign(this.performanceMetrics, metrics);\r\n  }\r\n\r\n  /**\r\n   * Check if rate limit is exceeded\r\n   */\r\n  isRateLimitExceeded(currentUsage: { requestsPerMinute?: number; tokensPerMinute?: number }): boolean {\r\n    if (!this.rateLimits) return false;\r\n\r\n    if (this.rateLimits.requestsPerMinute && currentUsage.requestsPerMinute) {\r\n      if (currentUsage.requestsPerMinute >= this.rateLimits.requestsPerMinute) {\r\n        return true;\r\n      }\r\n    }\r\n\r\n    if (this.rateLimits.tokensPerMinute && currentUsage.tokensPerMinute) {\r\n      if (currentUsage.tokensPerMinute >= this.rateLimits.tokensPerMinute) {\r\n        return true;\r\n      }\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Validate model configuration\r\n   */\r\n  validate(): string[] {\r\n    const errors: string[] = [];\r\n\r\n    if (!this.name) {\r\n      errors.push('Model name is required');\r\n    }\r\n\r\n    if (!this.type) {\r\n      errors.push('Model type is required');\r\n    }\r\n\r\n    if (!this.provider) {\r\n      errors.push('Provider is required');\r\n    }\r\n\r\n    if (!this.version) {\r\n      errors.push('Version is required');\r\n    }\r\n\r\n    if (this.provider !== 'custom' && !this.apiKey) {\r\n      errors.push('API key is required for external providers');\r\n    }\r\n\r\n    return errors;\r\n  }\r\n}\r\n"], "version": 3}