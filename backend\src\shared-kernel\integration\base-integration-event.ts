import { v4 as uuidv4 } from 'uuid';

/**
 * Base Integration Event
 *
 * Abstract base class for all integration events in the system
 */
export abstract class BaseIntegrationEvent<T = any> {
  public readonly id: string;
  public readonly timestamp: Date;
  public readonly version: string;
  public readonly source: string;
  public readonly eventType: string;
  public readonly eventData: T;
  private readonly _correlationId?: string;
  private readonly _causationId?: string;
  private readonly _metadata: Record<string, any>;

  constructor(
    eventType: string,
    eventData: T,
    options?: {
      source?: string;
      version?: string;
      correlationId?: string;
      causationId?: string;
      metadata?: Record<string, any>;
    }
  ) {
    this.id = uuidv4();
    this.timestamp = new Date();
    this.eventType = eventType;
    this.eventData = eventData;
    this.source = options?.source || 'sentinel-backend';
    this.version = options?.version || '1.0';
    this._correlationId = options?.correlationId;
    this._causationId = options?.causationId;
    this._metadata = options?.metadata || {};
  }

  /**
   * Get event ID
   */
  getId(): string {
    return this.id;
  }

  /**
   * Get event timestamp
   */
  getTimestamp(): Date {
    return this.timestamp;
  }

  /**
   * Get event type
   */
  getEventType(): string {
    return this.eventType;
  }

  /**
   * Get event source
   */
  getSource(): string {
    return this.source;
  }

  /**
   * Get event version
   */
  getVersion(): string {
    return this.version;
  }

  /**
   * Get event data
   */
  getEventData(): any {
    return this.eventData;
  }

  /**
   * Convert event to JSON
   */
  toJSON(): any {
    return {
      id: this.id,
      timestamp: this.timestamp.toISOString(),
      eventType: this.eventType,
      source: this.source,
      version: this.version,
      data: this.eventData,
    };
  }

  /**
   * Get event metadata
   */
  getMetadata(): any {
    return {
      id: this.id,
      timestamp: this.timestamp,
      eventType: this.eventType,
      source: this.source,
      version: this.version,
    };
  }

  /**
   * Validate event data
   */
  abstract validate(): boolean;

  /**
   * Get event priority
   */
  abstract getPriority(): 'low' | 'medium' | 'high' | 'critical';

  /**
   * Check if event requires immediate processing
   */
  abstract requiresImmediateProcessing(): boolean;

  /**
   * Get event correlation ID for tracking related events
   */
  getCorrelationId(): string | null {
    return this._correlationId || (this.eventData as any)?.correlationId || null;
  }

  /**
   * Get event causation ID for tracking event chains
   */
  getCausationId(): string | null {
    return this._causationId || null;
  }

  /**
   * Get event metadata
   */
  getEventMetadata(): Record<string, any> {
    return { ...this._metadata };
  }

  /**
   * Get event tags for categorization
   */
  getTags(): string[] {
    return (this.eventData as any)?.tags || [];
  }

  /**
   * Check if event is retryable in case of processing failure
   */
  isRetryable(): boolean {
    return true; // Default to retryable
  }

  /**
   * Get maximum retry attempts
   */
  getMaxRetryAttempts(): number {
    return 3; // Default retry attempts
  }

  /**
   * Get retry delay in milliseconds
   */
  getRetryDelay(): number {
    return 1000; // Default 1 second delay
  }
}
