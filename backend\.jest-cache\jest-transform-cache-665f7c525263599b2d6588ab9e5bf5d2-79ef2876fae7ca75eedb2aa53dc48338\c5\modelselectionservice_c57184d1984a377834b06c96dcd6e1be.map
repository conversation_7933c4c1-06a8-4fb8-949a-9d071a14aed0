{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\orchestration\\model-selection.service.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAA4D;AAC5D,2CAA+C;AAC/C,8GAGoE;AACpE,yFAAoF;AACpF,kEAA6D;AAE7D;;;;;;GAMG;AAEI,IAAM,qBAAqB,6BAA3B,MAAM,qBAAqB;IAIhC,YAEE,eAAmD,EAClC,kBAA2C,EAC3C,YAA4B,EAC5B,aAA4B;QAH5B,oBAAe,GAAf,eAAe,CAAmB;QAClC,uBAAkB,GAAlB,kBAAkB,CAAyB;QAC3C,iBAAY,GAAZ,YAAY,CAAgB;QAC5B,kBAAa,GAAb,aAAa,CAAe;QAR9B,WAAM,GAAG,IAAI,eAAM,CAAC,uBAAqB,CAAC,IAAI,CAAC,CAAC;QAChD,wBAAmB,GAAG,IAAI,GAAG,EAAgC,CAAC;IAQ5E,CAAC;IAEJ;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,OAA8B;QACrD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6CAA6C,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QAEnF,IAAI,CAAC;YACH,mCAAmC;YACnC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAC/D,IAAI,eAAe,IAAI,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,EAAE,CAAC;gBAC1D,OAAO,eAAe,CAAC,aAAa,CAAC;YACvC,CAAC;YAED,qCAAqC;YACrC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAExE,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,MAAM,IAAI,mBAAmB,CAAC,sCAAsC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC1F,CAAC;YAED,wBAAwB;YACxB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;YAEtE,0CAA0C;YAC1C,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;YAElE,yBAAyB;YACzB,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;YAElD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,aAAa,CAAC,OAAO,cAAc,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;YAE5F,OAAO,aAAa,CAAC;QAEvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,OAAO,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YACjF,MAAM,IAAI,mBAAmB,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,OAA8B;QACtD,MAAM,eAAe,GAAG;YACtB,GAAG,OAAO;YACV,YAAY,EAAE;gBACZ,GAAG,OAAO,CAAC,YAAY;gBACvB,UAAU,EAAE,IAAI,EAAE,eAAe;gBACjC,QAAQ,EAAE,SAAS;gBACnB,QAAQ,EAAE,IAAI;aACf;SACF,CAAC;QAEF,OAAO,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,OAA8B;QACnD,MAAM,YAAY,GAAG;YACnB,GAAG,OAAO;YACV,YAAY,EAAE;gBACZ,GAAG,OAAO,CAAC,YAAY;gBACvB,QAAQ,EAAE,YAAY;gBACtB,KAAK,EAAE,IAAI;gBACX,aAAa,EAAE,IAAI;aACpB;SACF,CAAC;QAEF,OAAO,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAAC,OAA8B;QAC1D,MAAM,eAAe,GAAG;YACtB,GAAG,OAAO;YACV,YAAY,EAAE;gBACZ,GAAG,OAAO,CAAC,YAAY;gBACvB,QAAQ,EAAE,UAAU;gBACpB,WAAW,EAAE,IAAI;gBACjB,YAAY,EAAE,IAAI;aACnB;SACF,CAAC;QAEF,OAAO,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAAC,QAAgB;QAC5C,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YAChE,MAAM,eAAe,GAA0B,EAAE,CAAC;YAElD,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE,CAAC;gBACpC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBAC5E,MAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;gBACrE,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACvC,CAAC;YAED,wBAAwB;YACxB,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC;YAEhE,OAAO,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,wBAAwB;QAE9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4CAA4C,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YACjF,MAAM,IAAI,mBAAmB,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAAC,OAAe;QAC5C,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAC3D,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,MAAM,IAAI,KAAK,CAAC,oBAAoB,OAAO,EAAE,CAAC,CAAC;YACjD,CAAC;YAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACvE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAE1E,OAAO;gBACL,OAAO;gBACP,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,OAAO,EAAE,OAAO,CAAC,cAAc;gBAC/B,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,cAAc,EAAE,WAAW,CAAC,cAAc;gBAC1C,YAAY,EAAE,OAAO,CAAC,YAAY;gBAClC,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;YACpE,MAAM,IAAI,mBAAmB,CAAC,sBAAsB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAAC,QAAuB;QACnD,IAAI,CAAC;YACH,mCAAmC;YACnC,MAAM,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,OAAO,EAAE;gBACjE,QAAQ,EAAE,QAAQ,CAAC,cAAc;gBACjC,OAAO,EAAE,QAAQ,CAAC,aAAa;gBAC/B,gBAAgB,EAAE,QAAQ,CAAC,UAAU;aACtC,CAAC,CAAC;YAEH,6CAA6C;YAC7C,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;YAE5C,8BAA8B;YAC9B,MAAM,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAEtD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yCAAyC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;QAEjF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAChE,MAAM,IAAI,mBAAmB,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAED,yBAAyB;IAEjB,KAAK,CAAC,kBAAkB,CAAC,QAAgB;QAC/C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAEnE,2CAA2C;QAC3C,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC3B,KAAK,CAAC,MAAM,KAAK,QAAQ;YACzB,KAAK,CAAC,YAAY,KAAK,SAAS,CACjC,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,WAAW,CACvB,MAAmB,EACnB,OAA8B;QAE9B,MAAM,YAAY,GAAkB,EAAE,CAAC;QAEvC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAC7D,YAAY,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;QACtC,CAAC;QAED,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACpE,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAC/B,KAAgB,EAChB,OAA8B;QAE9B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC5E,MAAM,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,EAAE,CAAC;QAEhD,0CAA0C;QAC1C,MAAM,aAAa,GAAG,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QACtF,MAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;QAC1F,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;QACpF,MAAM,iBAAiB,GAAG,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QACpF,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QAE5F,+CAA+C;QAC/C,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;QAEvD,MAAM,KAAK,GAAG,CACZ,aAAa,GAAG,OAAO,CAAC,QAAQ;YAChC,YAAY,GAAG,OAAO,CAAC,OAAO;YAC9B,SAAS,GAAG,OAAO,CAAC,IAAI;YACxB,iBAAiB,GAAG,OAAO,CAAC,YAAY;YACxC,eAAe,GAAG,OAAO,CAAC,UAAU,CACrC,CAAC;QAEF,OAAO;YACL,QAAQ,EAAE,aAAa;YACvB,OAAO,EAAE,YAAY;YACrB,IAAI,EAAE,SAAS;YACf,YAAY,EAAE,iBAAiB;YAC/B,UAAU,EAAE,eAAe;YAC3B,KAAK;SACN,CAAC;IACJ,CAAC;IAEO,eAAe,CACrB,YAA2B,EAC3B,OAA8B;QAE9B,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;QAED,MAAM,SAAS,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;QAElC,OAAO;YACL,OAAO,EAAE,SAAS,CAAC,KAAK,CAAC,EAAE;YAC3B,YAAY,EAAE,SAAS,CAAC,KAAK,CAAC,YAAY;YAC1C,SAAS,EAAE,SAAS,CAAC,KAAK,CAAC,IAAI;YAC/B,OAAO,EAAE,SAAS,CAAC,KAAK,CAAC,OAAO;YAChC,UAAU,EAAE,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC;YAC/D,KAAK,EAAE,SAAS,CAAC,KAAK,CAAC,KAAK;YAC5B,UAAU,EAAE,IAAI,IAAI,EAAE;SACvB,CAAC;IACJ,CAAC;IAEO,oBAAoB,CAAC,KAAgB,EAAE,OAA8B;QAC3E,MAAM,UAAU,GAAG,KAAK,CAAC,iBAAiB,IAAI,EAAE,CAAC;QACjD,MAAM,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,EAAE,CAAC;QAEhD,0CAA0C;QAC1C,IAAI,YAAY,CAAC,QAAQ,EAAE,CAAC;YAC1B,OAAO;gBACL,GAAG,UAAU;gBACb,WAAW,EAAE,GAAG,EAAE,oCAAoC;gBACtD,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,SAAS,IAAI,IAAI,EAAE,GAAG,CAAC;aACvD,CAAC;QACJ,CAAC;QAED,IAAI,YAAY,CAAC,YAAY,EAAE,CAAC;YAC9B,OAAO;gBACL,GAAG,UAAU;gBACb,WAAW,EAAE,GAAG,EAAE,uBAAuB;gBACzC,IAAI,EAAE,GAAG;aACV,CAAC;QACJ,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,sBAAsB,CAAC,QAAgB,EAAE,YAAiB;QAChE,MAAM,WAAW,GAAG,YAAY,CAAC,WAAW,IAAI,GAAG,CAAC;QACpD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;IAChF,CAAC;IAEO,qBAAqB,CAAC,OAAe,EAAE,YAAiB;QAC9D,MAAM,UAAU,GAAG,YAAY,CAAC,UAAU,IAAI,IAAI,CAAC,CAAC,oBAAoB;QACxE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,UAAU,GAAG,OAAO,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC;IACvE,CAAC;IAEO,kBAAkB,CAAC,IAAY,EAAE,YAAiB;QACxD,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC,4BAA4B;QAC1E,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;IAC9D,CAAC;IAEO,0BAA0B,CAAC,YAAoB;QACrD,OAAO,YAAY,CAAC,CAAC,oBAAoB;IAC3C,CAAC;IAEO,wBAAwB,CAAC,UAAkB,EAAE,YAAiB;QACpE,MAAM,aAAa,GAAG,YAAY,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC,6BAA6B;QACrF,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,GAAG,aAAa,CAAC,CAAC;IACjD,CAAC;IAEO,mBAAmB,CAAC,YAAiB;QAC3C,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,IAAI,UAAU,CAAC;QAErD,MAAM,cAAc,GAAqC;YACvD,QAAQ,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,YAAY,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,EAAE;YACxF,OAAO,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,YAAY,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,EAAE;YACvF,IAAI,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,YAAY,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,EAAE;YACpF,UAAU,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,YAAY,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,EAAE;YAC1F,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,YAAY,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,EAAE;SAC3F,CAAC;QAEF,OAAO,cAAc,CAAC,QAAQ,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC;IAC7D,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,OAA8B;QAC7D,MAAM,QAAQ,GAAG,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;QACzD,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC;IACxD,CAAC;IAEO,YAAY,CAAC,SAA+B;QAClD,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,2BAA2B,EAAE,MAAM,CAAC,CAAC,CAAC,YAAY;QACtG,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,YAAY,CAAC;IACnE,CAAC;IAEO,KAAK,CAAC,cAAc,CAC1B,OAA8B,EAC9B,aAAiC;QAEjC,MAAM,QAAQ,GAAG,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;QACzD,MAAM,MAAM,GAAyB;YACnC,aAAa;YACb,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAE/C,0CAA0C;QAC1C,IAAI,IAAI,CAAC,mBAAmB,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC;YACzC,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC;IACH,CAAC;IAEO,yBAAyB,CAAC,OAA8B;QAC9D,MAAM,OAAO,GAAG;YACd,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,YAAY,EAAE,OAAO,CAAC,YAAY;SACnC,CAAC;QAEF,OAAO,OAAO,CAAC,QAAQ,CAAC;aACrB,UAAU,CAAC,KAAK,CAAC;aACjB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;aAC/B,MAAM,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC;IAEO,YAAY;QAClB,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC,CAAC;QAC/D,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,aAAa;QAEjD,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,OAAO,EAAE,CAAC;YACnC,IAAI,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,MAAM,EAAE,CAAC;gBACvC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;IACH,CAAC;IAEO,oBAAoB,CAAC,KAAgB,EAAE,WAAgB;QAC7D,OAAO;YACL,OAAO,EAAE,KAAK,CAAC,EAAE;YACjB,SAAS,EAAE,KAAK,CAAC,IAAI;YACrB,YAAY,EAAE,KAAK,CAAC,YAAY;YAChC,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,OAAO,EAAE,WAAW,CAAC,cAAc;YACnC,cAAc,EAAE,WAAW,CAAC,cAAc;YAC1C,YAAY,EAAE,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC;YACrD,SAAS,EAAE,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,WAAW,CAAC;YAC1D,UAAU,EAAE,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,WAAW,CAAC;YAC5D,QAAQ,EAAE,KAAK,CAAC,mBAAmB,IAAI,EAAE;SAC1C,CAAC;IACJ,CAAC;IAEO,qBAAqB,CAAC,WAAgB;QAC5C,4CAA4C;QAC5C,OAAO,CACL,WAAW,CAAC,QAAQ,GAAG,GAAG;YAC1B,CAAC,CAAC,GAAG,WAAW,CAAC,cAAc,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,oBAAoB;YACrE,CAAC,CAAC,GAAG,WAAW,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,iBAAiB;YACjE,WAAW,CAAC,YAAY,GAAG,GAAG,CAC/B,CAAC;IACJ,CAAC;IAEO,sBAAsB,CAAC,KAAgB,EAAE,WAAgB;QAC/D,MAAM,SAAS,GAAa,EAAE,CAAC;QAE/B,IAAI,WAAW,CAAC,QAAQ,GAAG,GAAG;YAAE,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAChE,IAAI,WAAW,CAAC,cAAc,GAAG,IAAI;YAAE,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACrE,IAAI,WAAW,CAAC,cAAc,GAAG,KAAK;YAAE,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACzE,IAAI,WAAW,CAAC,YAAY,GAAG,IAAI;YAAE,SAAS,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAEzE,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,uBAAuB,CAAC,KAAgB,EAAE,WAAgB;QAChE,MAAM,UAAU,GAAa,EAAE,CAAC;QAEhC,IAAI,WAAW,CAAC,QAAQ,GAAG,GAAG;YAAE,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAClE,IAAI,WAAW,CAAC,cAAc,GAAG,IAAI;YAAE,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACzE,IAAI,WAAW,CAAC,cAAc,GAAG,IAAI;YAAE,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACtE,IAAI,WAAW,CAAC,YAAY,GAAG,IAAI;YAAE,UAAU,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAE9E,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,QAAuB;QAC1D,6EAA6E;QAC7E,8DAA8D;IAChE,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,OAAe;QACpD,qDAAqD;QACrD,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,EAAE,CAAC;YAC9D,IAAI,KAAK,CAAC,aAAa,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;gBAC5C,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;IACH,CAAC;CACF,CAAA;AAxbY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;IAMR,WAAA,IAAA,eAAM,EAAC,mDAAmB,CAAC,CAAA;yDACM,iDAAiB,oBAAjB,iDAAiB,oDACd,mDAAuB,oBAAvB,mDAAuB,oDAC7B,iCAAc,oBAAd,iCAAc,oDACb,sBAAa,oBAAb,sBAAa;GATpC,qBAAqB,CAwbjC;AAsGD,MAAM,mBAAoB,SAAQ,KAAK;IACrC,YAAY,OAAe;QACzB,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,qBAAqB,CAAC;IACpC,CAAC;CACF", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\orchestration\\model-selection.service.ts"], "sourcesContent": ["import { Injectable, Logger, Inject } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { \r\n  AI_MODEL_REPOSITORY,\r\n  AiModelRepository \r\n} from '../../../domain/repositories/ai-model.repository.interface';\r\nimport { ModelPerformanceService } from '../optimization/model-performance.service';\r\nimport { AiCacheService } from '../caching/ai-cache.service';\r\n\r\n/**\r\n * Model Selection Service\r\n * \r\n * Intelligently selects the optimal AI model for specific tasks\r\n * based on performance metrics, cost, latency requirements, and availability.\r\n * Implements sophisticated model routing and recommendation algorithms.\r\n */\r\n@Injectable()\r\nexport class ModelSelectionService {\r\n  private readonly logger = new Logger(ModelSelectionService.name);\r\n  private readonly modelSelectionCache = new Map<string, ModelSelectionResult>();\r\n\r\n  constructor(\r\n    @Inject(AI_MODEL_REPOSITORY)\r\n    private readonly modelRepository: AiModelRepository,\r\n    private readonly performanceService: ModelPerformanceService,\r\n    private readonly cacheService: AiCacheService,\r\n    private readonly configService: ConfigService,\r\n  ) {}\r\n\r\n  /**\r\n   * Selects optimal model for analysis request\r\n   */\r\n  async selectOptimalModel(request: ModelSelectionRequest): Promise<ModelConfiguration> {\r\n    this.logger.debug(`Selecting optimal model for request type: ${request.taskType}`);\r\n\r\n    try {\r\n      // Check cache for recent selection\r\n      const cachedSelection = await this.getCachedSelection(request);\r\n      if (cachedSelection && this.isCacheValid(cachedSelection)) {\r\n        return cachedSelection.configuration;\r\n      }\r\n\r\n      // Get available models for task type\r\n      const availableModels = await this.getAvailableModels(request.taskType);\r\n      \r\n      if (availableModels.length === 0) {\r\n        throw new ModelSelectionError(`No models available for task type: ${request.taskType}`);\r\n      }\r\n\r\n      // Score and rank models\r\n      const scoredModels = await this.scoreModels(availableModels, request);\r\n      \r\n      // Select best model based on requirements\r\n      const selectedModel = this.selectBestModel(scoredModels, request);\r\n      \r\n      // Cache selection result\r\n      await this.cacheSelection(request, selectedModel);\r\n      \r\n      this.logger.debug(`Selected model: ${selectedModel.modelId} for task: ${request.taskType}`);\r\n      \r\n      return selectedModel;\r\n      \r\n    } catch (error) {\r\n      this.logger.error(`Model selection failed for task: ${request.taskType}`, error);\r\n      throw new ModelSelectionError(`Model selection failed: ${error.message}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Selects model optimized for real-time processing\r\n   */\r\n  async selectRealTimeModel(request: ModelSelectionRequest): Promise<ModelConfiguration> {\r\n    const realTimeRequest = {\r\n      ...request,\r\n      requirements: {\r\n        ...request.requirements,\r\n        maxLatency: 1000, // 1 second max\r\n        priority: 'latency',\r\n        realTime: true,\r\n      },\r\n    };\r\n\r\n    return this.selectOptimalModel(realTimeRequest);\r\n  }\r\n\r\n  /**\r\n   * Selects model optimized for batch processing\r\n   */\r\n  async selectBatchModel(request: ModelSelectionRequest): Promise<ModelConfiguration> {\r\n    const batchRequest = {\r\n      ...request,\r\n      requirements: {\r\n        ...request.requirements,\r\n        priority: 'throughput',\r\n        batch: true,\r\n        costOptimized: true,\r\n      },\r\n    };\r\n\r\n    return this.selectOptimalModel(batchRequest);\r\n  }\r\n\r\n  /**\r\n   * Selects model optimized for high accuracy\r\n   */\r\n  async selectHighAccuracyModel(request: ModelSelectionRequest): Promise<ModelConfiguration> {\r\n    const accuracyRequest = {\r\n      ...request,\r\n      requirements: {\r\n        ...request.requirements,\r\n        priority: 'accuracy',\r\n        minAccuracy: 0.95,\r\n        highAccuracy: true,\r\n      },\r\n    };\r\n\r\n    return this.selectOptimalModel(accuracyRequest);\r\n  }\r\n\r\n  /**\r\n   * Gets model recommendations for a task type\r\n   */\r\n  async getModelRecommendations(taskType: string): Promise<ModelRecommendation[]> {\r\n    try {\r\n      const availableModels = await this.getAvailableModels(taskType);\r\n      const recommendations: ModelRecommendation[] = [];\r\n\r\n      for (const model of availableModels) {\r\n        const performance = await this.performanceService.getModelMetrics(model.id);\r\n        const recommendation = this.createRecommendation(model, performance);\r\n        recommendations.push(recommendation);\r\n      }\r\n\r\n      // Sort by overall score\r\n      recommendations.sort((a, b) => b.overallScore - a.overallScore);\r\n      \r\n      return recommendations.slice(0, 5); // Top 5 recommendations\r\n      \r\n    } catch (error) {\r\n      this.logger.error(`Failed to get model recommendations for: ${taskType}`, error);\r\n      throw new ModelSelectionError(`Recommendations failed: ${error.message}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Evaluates model performance for selection\r\n   */\r\n  async evaluateModelPerformance(modelId: string): Promise<ModelEvaluation> {\r\n    try {\r\n      const model = await this.modelRepository.findById(modelId);\r\n      if (!model) {\r\n        throw new Error(`Model not found: ${modelId}`);\r\n      }\r\n\r\n      const metrics = await this.performanceService.getModelMetrics(modelId);\r\n      const costMetrics = await this.performanceService.getCostMetrics(modelId);\r\n      \r\n      return {\r\n        modelId,\r\n        accuracy: metrics.accuracy,\r\n        precision: metrics.precision,\r\n        recall: metrics.recall,\r\n        f1Score: metrics.f1Score,\r\n        latency: metrics.averageLatency,\r\n        throughput: metrics.throughput,\r\n        costPerRequest: costMetrics.costPerRequest,\r\n        availability: metrics.availability,\r\n        lastUpdated: new Date(),\r\n      };\r\n      \r\n    } catch (error) {\r\n      this.logger.error(`Model evaluation failed for: ${modelId}`, error);\r\n      throw new ModelSelectionError(`Evaluation failed: ${error.message}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Updates model selection strategy based on feedback\r\n   */\r\n  async updateSelectionStrategy(feedback: ModelFeedback): Promise<void> {\r\n    try {\r\n      // Update model performance metrics\r\n      await this.performanceService.updateModelMetrics(feedback.modelId, {\r\n        accuracy: feedback.actualAccuracy,\r\n        latency: feedback.actualLatency,\r\n        userSatisfaction: feedback.userRating,\r\n      });\r\n\r\n      // Adjust selection weights based on feedback\r\n      await this.adjustSelectionWeights(feedback);\r\n      \r\n      // Clear related cache entries\r\n      await this.invalidateSelectionCache(feedback.modelId);\r\n      \r\n      this.logger.debug(`Updated selection strategy for model: ${feedback.modelId}`);\r\n      \r\n    } catch (error) {\r\n      this.logger.error(`Failed to update selection strategy`, error);\r\n      throw new ModelSelectionError(`Strategy update failed: ${error.message}`);\r\n    }\r\n  }\r\n\r\n  // Private helper methods\r\n\r\n  private async getAvailableModels(taskType: string): Promise<ModelInfo[]> {\r\n    const models = await this.modelRepository.findByTaskType(taskType);\r\n    \r\n    // Filter by availability and health status\r\n    return models.filter(model => \r\n      model.status === 'active' && \r\n      model.healthStatus === 'healthy'\r\n    );\r\n  }\r\n\r\n  private async scoreModels(\r\n    models: ModelInfo[],\r\n    request: ModelSelectionRequest\r\n  ): Promise<ScoredModel[]> {\r\n    const scoredModels: ScoredModel[] = [];\r\n\r\n    for (const model of models) {\r\n      const score = await this.calculateModelScore(model, request);\r\n      scoredModels.push({ model, score });\r\n    }\r\n\r\n    return scoredModels.sort((a, b) => b.score.total - a.score.total);\r\n  }\r\n\r\n  private async calculateModelScore(\r\n    model: ModelInfo,\r\n    request: ModelSelectionRequest\r\n  ): Promise<ModelScore> {\r\n    const performance = await this.performanceService.getModelMetrics(model.id);\r\n    const requirements = request.requirements || {};\r\n\r\n    // Calculate individual scores (0-1 scale)\r\n    const accuracyScore = this.calculateAccuracyScore(performance.accuracy, requirements);\r\n    const latencyScore = this.calculateLatencyScore(performance.averageLatency, requirements);\r\n    const costScore = this.calculateCostScore(performance.costPerRequest, requirements);\r\n    const availabilityScore = this.calculateAvailabilityScore(performance.availability);\r\n    const throughputScore = this.calculateThroughputScore(performance.throughput, requirements);\r\n\r\n    // Apply weights based on requirements priority\r\n    const weights = this.getSelectionWeights(requirements);\r\n    \r\n    const total = (\r\n      accuracyScore * weights.accuracy +\r\n      latencyScore * weights.latency +\r\n      costScore * weights.cost +\r\n      availabilityScore * weights.availability +\r\n      throughputScore * weights.throughput\r\n    );\r\n\r\n    return {\r\n      accuracy: accuracyScore,\r\n      latency: latencyScore,\r\n      cost: costScore,\r\n      availability: availabilityScore,\r\n      throughput: throughputScore,\r\n      total,\r\n    };\r\n  }\r\n\r\n  private selectBestModel(\r\n    scoredModels: ScoredModel[],\r\n    request: ModelSelectionRequest\r\n  ): ModelConfiguration {\r\n    if (scoredModels.length === 0) {\r\n      throw new Error('No scored models available');\r\n    }\r\n\r\n    const bestModel = scoredModels[0];\r\n    \r\n    return {\r\n      modelId: bestModel.model.id,\r\n      providerType: bestModel.model.providerType,\r\n      modelType: bestModel.model.type,\r\n      version: bestModel.model.version,\r\n      parameters: this.getOptimalParameters(bestModel.model, request),\r\n      score: bestModel.score.total,\r\n      selectedAt: new Date(),\r\n    };\r\n  }\r\n\r\n  private getOptimalParameters(model: ModelInfo, request: ModelSelectionRequest): any {\r\n    const baseParams = model.defaultParameters || {};\r\n    const requirements = request.requirements || {};\r\n\r\n    // Adjust parameters based on requirements\r\n    if (requirements.realTime) {\r\n      return {\r\n        ...baseParams,\r\n        temperature: 0.1, // Lower temperature for consistency\r\n        maxTokens: Math.min(baseParams.maxTokens || 1000, 500),\r\n      };\r\n    }\r\n\r\n    if (requirements.highAccuracy) {\r\n      return {\r\n        ...baseParams,\r\n        temperature: 0.0, // Deterministic output\r\n        topP: 0.9,\r\n      };\r\n    }\r\n\r\n    return baseParams;\r\n  }\r\n\r\n  private calculateAccuracyScore(accuracy: number, requirements: any): number {\r\n    const minAccuracy = requirements.minAccuracy || 0.7;\r\n    return Math.max(0, Math.min(1, (accuracy - minAccuracy) / (1 - minAccuracy)));\r\n  }\r\n\r\n  private calculateLatencyScore(latency: number, requirements: any): number {\r\n    const maxLatency = requirements.maxLatency || 5000; // 5 seconds default\r\n    return Math.max(0, Math.min(1, (maxLatency - latency) / maxLatency));\r\n  }\r\n\r\n  private calculateCostScore(cost: number, requirements: any): number {\r\n    const maxCost = requirements.maxCost || 0.01; // $0.01 per request default\r\n    return Math.max(0, Math.min(1, (maxCost - cost) / maxCost));\r\n  }\r\n\r\n  private calculateAvailabilityScore(availability: number): number {\r\n    return availability; // Already 0-1 scale\r\n  }\r\n\r\n  private calculateThroughputScore(throughput: number, requirements: any): number {\r\n    const minThroughput = requirements.minThroughput || 10; // 10 requests/second default\r\n    return Math.min(1, throughput / minThroughput);\r\n  }\r\n\r\n  private getSelectionWeights(requirements: any): SelectionWeights {\r\n    const priority = requirements.priority || 'balanced';\r\n    \r\n    const weightProfiles: Record<string, SelectionWeights> = {\r\n      accuracy: { accuracy: 0.5, latency: 0.1, cost: 0.1, availability: 0.2, throughput: 0.1 },\r\n      latency: { accuracy: 0.1, latency: 0.5, cost: 0.1, availability: 0.2, throughput: 0.1 },\r\n      cost: { accuracy: 0.2, latency: 0.1, cost: 0.5, availability: 0.1, throughput: 0.1 },\r\n      throughput: { accuracy: 0.1, latency: 0.1, cost: 0.1, availability: 0.2, throughput: 0.5 },\r\n      balanced: { accuracy: 0.25, latency: 0.25, cost: 0.2, availability: 0.2, throughput: 0.1 },\r\n    };\r\n\r\n    return weightProfiles[priority] || weightProfiles.balanced;\r\n  }\r\n\r\n  private async getCachedSelection(request: ModelSelectionRequest): Promise<ModelSelectionResult | null> {\r\n    const cacheKey = this.generateSelectionCacheKey(request);\r\n    return this.modelSelectionCache.get(cacheKey) || null;\r\n  }\r\n\r\n  private isCacheValid(selection: ModelSelectionResult): boolean {\r\n    const cacheTimeout = this.configService.get<number>('ai.modelSelectionCacheTtl', 300000); // 5 minutes\r\n    return Date.now() - selection.timestamp.getTime() < cacheTimeout;\r\n  }\r\n\r\n  private async cacheSelection(\r\n    request: ModelSelectionRequest,\r\n    configuration: ModelConfiguration\r\n  ): Promise<void> {\r\n    const cacheKey = this.generateSelectionCacheKey(request);\r\n    const result: ModelSelectionResult = {\r\n      configuration,\r\n      timestamp: new Date(),\r\n    };\r\n    \r\n    this.modelSelectionCache.set(cacheKey, result);\r\n    \r\n    // Clean up old cache entries periodically\r\n    if (this.modelSelectionCache.size > 1000) {\r\n      this.cleanupCache();\r\n    }\r\n  }\r\n\r\n  private generateSelectionCacheKey(request: ModelSelectionRequest): string {\r\n    const keyData = {\r\n      taskType: request.taskType,\r\n      requirements: request.requirements,\r\n    };\r\n    \r\n    return require('crypto')\r\n      .createHash('md5')\r\n      .update(JSON.stringify(keyData))\r\n      .digest('hex');\r\n  }\r\n\r\n  private cleanupCache(): void {\r\n    const entries = Array.from(this.modelSelectionCache.entries());\r\n    const cutoff = Date.now() - 600000; // 10 minutes\r\n    \r\n    for (const [key, value] of entries) {\r\n      if (value.timestamp.getTime() < cutoff) {\r\n        this.modelSelectionCache.delete(key);\r\n      }\r\n    }\r\n  }\r\n\r\n  private createRecommendation(model: ModelInfo, performance: any): ModelRecommendation {\r\n    return {\r\n      modelId: model.id,\r\n      modelName: model.name,\r\n      providerType: model.providerType,\r\n      accuracy: performance.accuracy,\r\n      latency: performance.averageLatency,\r\n      costPerRequest: performance.costPerRequest,\r\n      overallScore: this.calculateOverallScore(performance),\r\n      strengths: this.identifyModelStrengths(model, performance),\r\n      weaknesses: this.identifyModelWeaknesses(model, performance),\r\n      useCases: model.recommendedUseCases || [],\r\n    };\r\n  }\r\n\r\n  private calculateOverallScore(performance: any): number {\r\n    // Simple weighted average for overall score\r\n    return (\r\n      performance.accuracy * 0.3 +\r\n      (1 - performance.averageLatency / 10000) * 0.3 + // Normalize latency\r\n      (1 - performance.costPerRequest * 1000) * 0.2 + // Normalize cost\r\n      performance.availability * 0.2\r\n    );\r\n  }\r\n\r\n  private identifyModelStrengths(model: ModelInfo, performance: any): string[] {\r\n    const strengths: string[] = [];\r\n    \r\n    if (performance.accuracy > 0.9) strengths.push('High accuracy');\r\n    if (performance.averageLatency < 1000) strengths.push('Low latency');\r\n    if (performance.costPerRequest < 0.001) strengths.push('Cost effective');\r\n    if (performance.availability > 0.99) strengths.push('High availability');\r\n    \r\n    return strengths;\r\n  }\r\n\r\n  private identifyModelWeaknesses(model: ModelInfo, performance: any): string[] {\r\n    const weaknesses: string[] = [];\r\n    \r\n    if (performance.accuracy < 0.8) weaknesses.push('Lower accuracy');\r\n    if (performance.averageLatency > 5000) weaknesses.push('Higher latency');\r\n    if (performance.costPerRequest > 0.01) weaknesses.push('Higher cost');\r\n    if (performance.availability < 0.95) weaknesses.push('Availability concerns');\r\n    \r\n    return weaknesses;\r\n  }\r\n\r\n  private async adjustSelectionWeights(feedback: ModelFeedback): Promise<void> {\r\n    // Implementation for adjusting selection algorithm weights based on feedback\r\n    // This would involve machine learning or statistical analysis\r\n  }\r\n\r\n  private async invalidateSelectionCache(modelId: string): Promise<void> {\r\n    // Remove cache entries related to the specific model\r\n    for (const [key, value] of this.modelSelectionCache.entries()) {\r\n      if (value.configuration.modelId === modelId) {\r\n        this.modelSelectionCache.delete(key);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// Type definitions\r\ninterface ModelSelectionRequest {\r\n  taskType: string;\r\n  requirements?: {\r\n    maxLatency?: number;\r\n    minAccuracy?: number;\r\n    maxCost?: number;\r\n    minThroughput?: number;\r\n    priority?: 'accuracy' | 'latency' | 'cost' | 'throughput' | 'balanced';\r\n    realTime?: boolean;\r\n    batch?: boolean;\r\n    highAccuracy?: boolean;\r\n    costOptimized?: boolean;\r\n  };\r\n  context?: any;\r\n}\r\n\r\ninterface ModelConfiguration {\r\n  modelId: string;\r\n  providerType: string;\r\n  modelType: string;\r\n  version: string;\r\n  parameters: any;\r\n  score: number;\r\n  selectedAt: Date;\r\n}\r\n\r\ninterface ModelInfo {\r\n  id: string;\r\n  name: string;\r\n  type: string;\r\n  providerType: string;\r\n  version: string;\r\n  status: string;\r\n  healthStatus: string;\r\n  defaultParameters?: any;\r\n  recommendedUseCases?: string[];\r\n}\r\n\r\ninterface ModelScore {\r\n  accuracy: number;\r\n  latency: number;\r\n  cost: number;\r\n  availability: number;\r\n  throughput: number;\r\n  total: number;\r\n}\r\n\r\ninterface ScoredModel {\r\n  model: ModelInfo;\r\n  score: ModelScore;\r\n}\r\n\r\ninterface SelectionWeights {\r\n  accuracy: number;\r\n  latency: number;\r\n  cost: number;\r\n  availability: number;\r\n  throughput: number;\r\n}\r\n\r\ninterface ModelSelectionResult {\r\n  configuration: ModelConfiguration;\r\n  timestamp: Date;\r\n}\r\n\r\ninterface ModelRecommendation {\r\n  modelId: string;\r\n  modelName: string;\r\n  providerType: string;\r\n  accuracy: number;\r\n  latency: number;\r\n  costPerRequest: number;\r\n  overallScore: number;\r\n  strengths: string[];\r\n  weaknesses: string[];\r\n  useCases: string[];\r\n}\r\n\r\ninterface ModelEvaluation {\r\n  modelId: string;\r\n  accuracy: number;\r\n  precision: number;\r\n  recall: number;\r\n  f1Score: number;\r\n  latency: number;\r\n  throughput: number;\r\n  costPerRequest: number;\r\n  availability: number;\r\n  lastUpdated: Date;\r\n}\r\n\r\ninterface ModelFeedback {\r\n  modelId: string;\r\n  actualAccuracy: number;\r\n  actualLatency: number;\r\n  userRating: number;\r\n  comments?: string;\r\n}\r\n\r\nclass ModelSelectionError extends Error {\r\n  constructor(message: string) {\r\n    super(message);\r\n    this.name = 'ModelSelectionError';\r\n  }\r\n}\r\n"], "version": 3}