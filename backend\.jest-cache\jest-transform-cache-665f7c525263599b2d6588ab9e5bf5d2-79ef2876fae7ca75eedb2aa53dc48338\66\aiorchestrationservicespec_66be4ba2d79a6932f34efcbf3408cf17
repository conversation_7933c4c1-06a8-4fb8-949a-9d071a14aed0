8a84eb005d4c357b9e6cce92b4e05412
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const cqrs_1 = require("@nestjs/cqrs");
const ai_orchestration_service_1 = require("../ai-orchestration.service");
const model_selection_service_1 = require("../model-selection.service");
const load_balancer_service_1 = require("../load-balancer.service");
const circuit_breaker_service_1 = require("../../resilience/circuit-breaker.service");
const ai_cache_service_1 = require("../../caching/ai-cache.service");
const metrics_adapter_1 = require("../../../../infrastructure/adapters/metrics.adapter");
const ai_model_provider_interface_1 = require("../../../../domain/services/ai-model-provider.interface");
const prediction_engine_interface_1 = require("../../../../domain/services/prediction-engine.interface");
describe('AiOrchestrationService', () => {
    let service;
    let mockAiModelProvider;
    let mockPredictionEngine;
    let mockModelSelectionService;
    let mockLoadBalancerService;
    let mockCircuitBreakerService;
    let mockCacheService;
    let mockMetricsAdapter;
    let mockEventBus;
    let mockCommandBus;
    let mockQueryBus;
    let mockConfigService;
    beforeEach(async () => {
        // Create mocks
        mockAiModelProvider = {
            analyze: jest.fn(),
        };
        mockPredictionEngine = {
            predict: jest.fn(),
        };
        mockModelSelectionService = {
            selectOptimalModel: jest.fn(),
            selectRealTimeModel: jest.fn(),
        };
        mockLoadBalancerService = {
            getAvailableProviders: jest.fn(),
            checkProviderHealth: jest.fn(),
        };
        mockCircuitBreakerService = {
            execute: jest.fn(),
            getStatus: jest.fn(),
        };
        mockCacheService = {
            get: jest.fn(),
            set: jest.fn(),
            checkHealth: jest.fn(),
        };
        mockMetricsAdapter = {
            recordAiOperation: jest.fn(),
        };
        mockEventBus = {
            publish: jest.fn(),
        };
        mockCommandBus = {
            execute: jest.fn(),
        };
        mockQueryBus = {
            execute: jest.fn(),
        };
        mockConfigService = {
            get: jest.fn(),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                ai_orchestration_service_1.AiOrchestrationService,
                {
                    provide: ai_model_provider_interface_1.AI_MODEL_PROVIDER,
                    useValue: mockAiModelProvider,
                },
                {
                    provide: prediction_engine_interface_1.PREDICTION_ENGINE,
                    useValue: mockPredictionEngine,
                },
                {
                    provide: model_selection_service_1.ModelSelectionService,
                    useValue: mockModelSelectionService,
                },
                {
                    provide: load_balancer_service_1.LoadBalancerService,
                    useValue: mockLoadBalancerService,
                },
                {
                    provide: circuit_breaker_service_1.CircuitBreakerService,
                    useValue: mockCircuitBreakerService,
                },
                {
                    provide: ai_cache_service_1.AiCacheService,
                    useValue: mockCacheService,
                },
                {
                    provide: metrics_adapter_1.MetricsAdapter,
                    useValue: mockMetricsAdapter,
                },
                {
                    provide: cqrs_1.EventBus,
                    useValue: mockEventBus,
                },
                {
                    provide: cqrs_1.CommandBus,
                    useValue: mockCommandBus,
                },
                {
                    provide: cqrs_1.QueryBus,
                    useValue: mockQueryBus,
                },
                {
                    provide: config_1.ConfigService,
                    useValue: mockConfigService,
                },
            ],
        }).compile();
        service = module.get(ai_orchestration_service_1.AiOrchestrationService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('orchestrateAnalysis', () => {
        const mockRequest = {
            id: 'test-request-1',
            type: 'threat-analysis',
            data: { content: 'test data' },
            priority: 'high',
        };
        const mockModelConfig = {
            providerType: 'openai',
            modelId: 'gpt-4',
            parameters: {},
        };
        const mockProviders = [
            {
                id: 'provider-1',
                type: 'openai',
                status: 'healthy',
                load: 0.5,
            },
        ];
        const mockAnalysisResult = {
            id: 'result-1',
            result: { threat_detected: true, confidence: 0.95 },
            confidence: 0.95,
            metadata: { model: 'gpt-4' },
            timestamp: new Date(),
        };
        it('should orchestrate analysis successfully with cache miss', async () => {
            // Arrange
            mockCacheService.get.mockResolvedValue(null);
            mockModelSelectionService.selectOptimalModel.mockResolvedValue(mockModelConfig);
            mockLoadBalancerService.getAvailableProviders.mockResolvedValue(mockProviders);
            mockCircuitBreakerService.execute.mockResolvedValue(mockAnalysisResult);
            mockCacheService.set.mockResolvedValue(undefined);
            // Act
            const result = await service.orchestrateAnalysis(mockRequest);
            // Assert
            expect(result).toEqual(mockAnalysisResult);
            expect(mockCacheService.get).toHaveBeenCalledWith(expect.any(String));
            expect(mockModelSelectionService.selectOptimalModel).toHaveBeenCalledWith(mockRequest);
            expect(mockLoadBalancerService.getAvailableProviders).toHaveBeenCalledWith('openai');
            expect(mockCircuitBreakerService.execute).toHaveBeenCalled();
            expect(mockCacheService.set).toHaveBeenCalledWith(expect.any(String), mockAnalysisResult, expect.any(Number));
            expect(mockMetricsAdapter.recordAiOperation).toHaveBeenCalledWith('success', expect.any(Number), expect.any(String));
        });
        it('should return cached result when available', async () => {
            // Arrange
            mockCacheService.get.mockResolvedValue(mockAnalysisResult);
            // Act
            const result = await service.orchestrateAnalysis(mockRequest);
            // Assert
            expect(result).toEqual(mockAnalysisResult);
            expect(mockCacheService.get).toHaveBeenCalledWith(expect.any(String));
            expect(mockModelSelectionService.selectOptimalModel).not.toHaveBeenCalled();
            expect(mockMetricsAdapter.recordAiOperation).toHaveBeenCalledWith('cache_hit', expect.any(Number), expect.any(String));
        });
        it('should handle provider failures with fallback', async () => {
            // Arrange
            const multipleProviders = [
                { id: 'provider-1', type: 'openai', status: 'healthy', load: 0.5 },
                { id: 'provider-2', type: 'openai', status: 'healthy', load: 0.3 },
            ];
            mockCacheService.get.mockResolvedValue(null);
            mockModelSelectionService.selectOptimalModel.mockResolvedValue(mockModelConfig);
            mockLoadBalancerService.getAvailableProviders.mockResolvedValue(multipleProviders);
            // First provider fails, second succeeds
            mockCircuitBreakerService.execute
                .mockRejectedValueOnce(new Error('Provider 1 failed'))
                .mockResolvedValueOnce(mockAnalysisResult);
            // Act
            const result = await service.orchestrateAnalysis(mockRequest);
            // Assert
            expect(result).toEqual(mockAnalysisResult);
            expect(mockCircuitBreakerService.execute).toHaveBeenCalledTimes(2);
        });
        it('should throw error when all providers fail', async () => {
            // Arrange
            mockCacheService.get.mockResolvedValue(null);
            mockModelSelectionService.selectOptimalModel.mockResolvedValue(mockModelConfig);
            mockLoadBalancerService.getAvailableProviders.mockResolvedValue(mockProviders);
            mockCircuitBreakerService.execute.mockRejectedValue(new Error('All providers failed'));
            // Act & Assert
            await expect(service.orchestrateAnalysis(mockRequest)).rejects.toThrow('Analysis failed');
            expect(mockMetricsAdapter.recordAiOperation).toHaveBeenCalledWith('error', expect.any(Number), expect.any(String));
        });
        it('should handle configuration values correctly', async () => {
            // Arrange
            mockConfigService.get.mockReturnValue(7200); // 2 hours cache TTL
            mockCacheService.get.mockResolvedValue(null);
            mockModelSelectionService.selectOptimalModel.mockResolvedValue(mockModelConfig);
            mockLoadBalancerService.getAvailableProviders.mockResolvedValue(mockProviders);
            mockCircuitBreakerService.execute.mockResolvedValue(mockAnalysisResult);
            // Act
            await service.orchestrateAnalysis(mockRequest);
            // Assert
            expect(mockConfigService.get).toHaveBeenCalledWith('ai.cacheTtl', 3600);
            expect(mockCacheService.set).toHaveBeenCalledWith(expect.any(String), mockAnalysisResult, 7200);
        });
    });
    describe('orchestrateBatchAnalysis', () => {
        const mockRequests = [
            {
                id: 'batch-request-1',
                type: 'threat-analysis',
                data: { content: 'test data 1' },
            },
            {
                id: 'batch-request-2',
                type: 'threat-analysis',
                data: { content: 'test data 2' },
            },
        ];
        it('should process batch requests successfully', async () => {
            // Arrange
            mockConfigService.get.mockReturnValue(2); // Concurrency limit
            // Mock individual analysis calls
            jest.spyOn(service, 'orchestrateAnalysis')
                .mockResolvedValueOnce({
                id: 'result-1',
                result: { threat_detected: true },
                confidence: 0.9,
                metadata: {},
                timestamp: new Date(),
            })
                .mockResolvedValueOnce({
                id: 'result-2',
                result: { threat_detected: false },
                confidence: 0.8,
                metadata: {},
                timestamp: new Date(),
            });
            // Act
            const results = await service.orchestrateBatchAnalysis(mockRequests);
            // Assert
            expect(results).toHaveLength(2);
            expect(results[0].id).toBe('result-1');
            expect(results[1].id).toBe('result-2');
            expect(service.orchestrateAnalysis).toHaveBeenCalledTimes(2);
        });
        it('should handle batch processing errors gracefully', async () => {
            // Arrange
            jest.spyOn(service, 'orchestrateAnalysis')
                .mockRejectedValue(new Error('Batch processing failed'));
            // Act & Assert
            await expect(service.orchestrateBatchAnalysis(mockRequests))
                .rejects.toThrow('Batch analysis failed');
        });
    });
    describe('orchestrateRealTimePrediction', () => {
        const mockPredictionRequest = {
            modelId: 'model-1',
            input: { data: 'test input' },
            options: { timeout: 1000 },
        };
        const mockPredictionResult = {
            prediction: { category: 'safe', score: 0.95 },
            confidence: 0.95,
            metadata: { model: 'model-1' },
        };
        it('should orchestrate real-time prediction successfully', async () => {
            // Arrange
            const mockModelConfig = {
                modelId: 'model-1',
                providerType: 'openai',
                parameters: {},
            };
            mockModelSelectionService.selectRealTimeModel.mockResolvedValue(mockModelConfig);
            mockPredictionEngine.predict.mockResolvedValue(mockPredictionResult);
            mockConfigService.get.mockReturnValue(1000);
            // Act
            const result = await service.orchestrateRealTimePrediction(mockPredictionRequest);
            // Assert
            expect(result).toEqual(mockPredictionResult);
            expect(mockModelSelectionService.selectRealTimeModel).toHaveBeenCalledWith(mockPredictionRequest);
            expect(mockPredictionEngine.predict).toHaveBeenCalledWith(mockPredictionRequest, expect.objectContaining({
                modelConfig: mockModelConfig,
                timeout: 1000,
                priority: 'high',
            }));
        });
        it('should handle real-time prediction failures', async () => {
            // Arrange
            mockModelSelectionService.selectRealTimeModel.mockRejectedValue(new Error('Model selection failed'));
            // Act & Assert
            await expect(service.orchestrateRealTimePrediction(mockPredictionRequest))
                .rejects.toThrow('Prediction failed');
        });
    });
    describe('checkHealth', () => {
        it('should return healthy status when all components are healthy', async () => {
            // Arrange
            const mockProviderHealth = { 'provider-1': { healthy: true } };
            const mockCacheHealth = { status: 'healthy' };
            const mockCircuitBreakerStatus = { 'cb-1': { state: 'closed' } };
            mockLoadBalancerService.checkProviderHealth.mockResolvedValue(mockProviderHealth);
            mockCacheService.checkHealth.mockResolvedValue(mockCacheHealth);
            mockCircuitBreakerService.getStatus.mockReturnValue(mockCircuitBreakerStatus);
            // Act
            const health = await service.checkHealth();
            // Assert
            expect(health.status).toBe('healthy');
            expect(health.providers).toEqual(mockProviderHealth);
            expect(health.cache).toEqual(mockCacheHealth);
            expect(health.circuitBreakers).toEqual(mockCircuitBreakerStatus);
            expect(health.timestamp).toBeInstanceOf(Date);
        });
        it('should return unhealthy status when health check fails', async () => {
            // Arrange
            mockLoadBalancerService.checkProviderHealth.mockRejectedValue(new Error('Health check failed'));
            // Act
            const health = await service.checkHealth();
            // Assert
            expect(health.status).toBe('unhealthy');
            expect(health.error).toBe('Health check failed');
            expect(health.timestamp).toBeInstanceOf(Date);
        });
    });
    describe('private helper methods', () => {
        it('should generate unique request IDs', () => {
            // Use reflection to access private method for testing
            const generateRequestId = service.generateRequestId.bind(service);
            const id1 = generateRequestId();
            const id2 = generateRequestId();
            expect(id1).toMatch(/^ai-req-\d+-[a-z0-9]+$/);
            expect(id2).toMatch(/^ai-req-\d+-[a-z0-9]+$/);
            expect(id1).not.toBe(id2);
        });
        it('should generate cache keys consistently', () => {
            const generateCacheKey = service.generateCacheKey.bind(service);
            const request1 = { id: '1', type: 'test', data: { value: 'same' } };
            const request2 = { id: '1', type: 'test', data: { value: 'same' } }; // Same ID for same content
            const request3 = { id: '1', type: 'test', data: { value: 'different' } };
            const key1 = generateCacheKey(request1);
            const key2 = generateCacheKey(request2);
            const key3 = generateCacheKey(request3);
            expect(key1).toBe(key2); // Same content should generate same key
            expect(key1).not.toBe(key3); // Different content should generate different key
        });
    });
    describe('error handling', () => {
        it('should handle orchestration errors with proper error types', async () => {
            // Arrange
            const mockRequest = {
                id: 'error-request',
                type: 'test',
                data: {},
            };
            mockCacheService.get.mockRejectedValue(new Error('Cache error'));
            // Act & Assert
            await expect(service.orchestrateAnalysis(mockRequest)).rejects.toThrow();
            expect(mockMetricsAdapter.recordAiOperation).toHaveBeenCalledWith('error', expect.any(Number), expect.any(String));
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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