import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * AI Cache Service
 * 
 * Provides caching capabilities for AI operations including
 * analysis results, model metadata, and request responses.
 * Implements intelligent cache strategies and performance optimization.
 */
@Injectable()
export class AiCacheService {
  private readonly logger = new Logger(AiCacheService.name);
  private readonly cache = new Map<string, CacheEntry>();
  private readonly defaultTtl: number;

  constructor(private readonly configService: ConfigService) {
    this.defaultTtl = this.configService.get<number>('ai.cache.defaultTtl', 3600000); // 1 hour
  }

  /**
   * Gets a value from cache
   */
  async get<T>(key: string): Promise<T | null> {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Check if entry has expired
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      return null;
    }

    // Update access time for LRU
    entry.lastAccessed = new Date();
    entry.accessCount++;

    this.logger.debug(`Cache hit for key: ${key}`);
    return entry.value as T;
  }

  /**
   * Sets a value in cache
   */
  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    const expiresAt = new Date(Date.now() + (ttl || this.defaultTtl));
    
    const entry: CacheEntry = {
      key,
      value,
      createdAt: new Date(),
      lastAccessed: new Date(),
      expiresAt,
      accessCount: 0,
      size: this.calculateSize(value),
    };

    this.cache.set(key, entry);
    this.logger.debug(`Cache set for key: ${key}, TTL: ${ttl || this.defaultTtl}ms`);

    // Cleanup expired entries periodically
    if (this.cache.size % 100 === 0) {
      await this.cleanup();
    }
  }

  /**
   * Deletes a value from cache
   */
  async delete(key: string): Promise<boolean> {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.logger.debug(`Cache deleted for key: ${key}`);
    }
    return deleted;
  }

  /**
   * Checks if a key exists in cache
   */
  async has(key: string): Promise<boolean> {
    const entry = this.cache.get(key);
    if (!entry) {
      return false;
    }

    if (this.isExpired(entry)) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  /**
   * Clears all cache entries
   */
  async clear(): Promise<void> {
    this.cache.clear();
    this.logger.log('Cache cleared');
  }

  /**
   * Gets cache statistics
   */
  async getStats(): Promise<CacheStats> {
    const entries = Array.from(this.cache.values());
    const now = new Date();
    
    const stats: CacheStats = {
      totalEntries: entries.length,
      expiredEntries: entries.filter(entry => this.isExpired(entry)).length,
      totalSize: entries.reduce((sum, entry) => sum + entry.size, 0),
      averageAccessCount: entries.length > 0 
        ? entries.reduce((sum, entry) => sum + entry.accessCount, 0) / entries.length 
        : 0,
      oldestEntry: entries.length > 0 
        ? Math.min(...entries.map(entry => entry.createdAt.getTime()))
        : null,
      newestEntry: entries.length > 0 
        ? Math.max(...entries.map(entry => entry.createdAt.getTime()))
        : null,
    };

    return stats;
  }

  /**
   * Performs cache health check
   */
  async checkHealth(): Promise<CacheHealthStatus> {
    try {
      const testKey = 'health-check-' + Date.now();
      const testValue = { test: true };
      
      // Test write
      await this.set(testKey, testValue, 1000);
      
      // Test read
      const retrieved = await this.get(testKey);
      
      // Test delete
      await this.delete(testKey);
      
      const stats = await this.getStats();
      
      return {
        status: 'healthy',
        responseTime: 0, // In-memory cache is instant
        stats,
        timestamp: new Date(),
      };
      
    } catch (error) {
      this.logger.error('Cache health check failed', error);
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date(),
      };
    }
  }

  /**
   * Invalidates cache entries matching a pattern
   */
  async invalidatePattern(pattern: string): Promise<number> {
    const regex = new RegExp(pattern);
    let deletedCount = 0;
    
    for (const [key] of this.cache) {
      if (regex.test(key)) {
        this.cache.delete(key);
        deletedCount++;
      }
    }
    
    this.logger.debug(`Invalidated ${deletedCount} cache entries matching pattern: ${pattern}`);
    return deletedCount;
  }

  /**
   * Gets or sets a value with a factory function
   */
  async getOrSet<T>(
    key: string, 
    factory: () => Promise<T>, 
    ttl?: number
  ): Promise<T> {
    const cached = await this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    const value = await factory();
    await this.set(key, value, ttl);
    return value;
  }

  /**
   * Refreshes cache entry with new TTL
   */
  async refresh(key: string, ttl?: number): Promise<boolean> {
    const entry = this.cache.get(key);
    if (!entry) {
      return false;
    }

    entry.expiresAt = new Date(Date.now() + (ttl || this.defaultTtl));
    this.logger.debug(`Cache refreshed for key: ${key}`);
    return true;
  }

  // Private helper methods

  private isExpired(entry: CacheEntry): boolean {
    return new Date() > entry.expiresAt;
  }

  private calculateSize(value: any): number {
    // Simple size calculation - can be improved
    try {
      return JSON.stringify(value).length;
    } catch {
      return 0;
    }
  }

  private async cleanup(): Promise<void> {
    const expiredKeys: string[] = [];
    
    for (const [key, entry] of this.cache) {
      if (this.isExpired(entry)) {
        expiredKeys.push(key);
      }
    }
    
    for (const key of expiredKeys) {
      this.cache.delete(key);
    }
    
    if (expiredKeys.length > 0) {
      this.logger.debug(`Cleaned up ${expiredKeys.length} expired cache entries`);
    }
  }
}

// Type definitions
interface CacheEntry {
  key: string;
  value: any;
  createdAt: Date;
  lastAccessed: Date;
  expiresAt: Date;
  accessCount: number;
  size: number;
}

interface CacheStats {
  totalEntries: number;
  expiredEntries: number;
  totalSize: number;
  averageAccessCount: number;
  oldestEntry: number | null;
  newestEntry: number | null;
}

interface CacheHealthStatus {
  status: 'healthy' | 'unhealthy';
  responseTime?: number;
  stats?: CacheStats;
  error?: string;
  timestamp: Date;
}