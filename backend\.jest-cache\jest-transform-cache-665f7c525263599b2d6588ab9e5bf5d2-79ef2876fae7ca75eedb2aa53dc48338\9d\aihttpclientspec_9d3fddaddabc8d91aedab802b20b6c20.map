{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\communication\\__tests__\\ai-http.client.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,2CAA+C;AAC/C,yCAA4C;AAC5C,sDAAiD;AACjD,+BAAsC;AAGtC,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;IAC5B,IAAI,MAAoB,CAAC;IACzB,IAAI,WAAqC,CAAC;IAC1C,IAAI,aAAyC,CAAC;IAE9C,MAAM,iBAAiB,GAAG,CAAI,IAAO,EAAE,MAAM,GAAG,GAAG,EAAoB,EAAE,CAAC,CAAC;QACzE,IAAI;QACJ,MAAM;QACN,UAAU,EAAE,IAAI;QAChB,OAAO,EAAE,EAAE;QACX,MAAM,EAAE,EAAS;KAClB,CAAC,CAAC;IAEH,MAAM,cAAc,GAAG,CAAC,MAAc,EAAE,OAAe,EAAc,EAAE,CAAC,CAAC;QACvE,IAAI,EAAE,YAAY;QAClB,OAAO;QACP,QAAQ,EAAE;YACR,MAAM;YACN,UAAU,EAAE,OAAO;YACnB,IAAI,EAAE,EAAE,OAAO,EAAE;YACjB,OAAO,EAAE,EAAE;YACX,MAAM,EAAE,EAAS;SAClB;QACD,MAAM,EAAE,EAAS;QACjB,YAAY,EAAE,IAAI;QAClB,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC;KACnB,CAAC,CAAC;IAEH,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,eAAe,GAAG;YACtB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;SACnB,CAAC;QAEF,MAAM,iBAAiB,GAAG;YACxB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;SACf,CAAC;QAEF,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,6BAAY;gBACZ,EAAE,OAAO,EAAE,mBAAW,EAAE,QAAQ,EAAE,eAAe,EAAE;gBACnD,EAAE,OAAO,EAAE,sBAAa,EAAE,QAAQ,EAAE,iBAAiB,EAAE;aACxD;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,MAAM,GAAG,MAAM,CAAC,GAAG,CAAe,6BAAY,CAAC,CAAC;QAChD,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,mBAAW,CAAC,CAAC;QACtC,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,sBAAa,CAAC,CAAC;QAE1C,8BAA8B;QAC9B,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAW,EAAE,YAAkB,EAAE,EAAE;YACvE,MAAM,MAAM,GAAG;gBACb,iBAAiB,EAAE,KAAK;gBACxB,iBAAiB,EAAE,CAAC;aACrB,CAAC;YACF,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,YAAY,GAAG;gBACnB,EAAE,EAAE,cAAc;gBAClB,MAAM,EAAE,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE;gBAClD,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,oBAAoB;gBAC3B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;YAEF,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,IAAA,SAAE,EAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEzE,MAAM,OAAO,GAAG;gBACd,IAAI,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE;gBACnC,KAAK,EAAE,oBAAoB;aAC5B,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,mBAAmB,CAAC,2BAA2B,EAAE,OAAO,CAAC,CAAC;YAEtF,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC;gBAC3B,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBACtB,MAAM,EAAE,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE;gBAClD,UAAU,EAAE,IAAI;gBAChB,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAChC,KAAK,EAAE,oBAAoB;iBAC5B,CAAC;gBACF,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAC9C,MAAM,CAAC,gBAAgB,CAAC;gBACtB,MAAM,EAAE,MAAM;gBACd,GAAG,EAAE,2BAA2B;gBAChC,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAC/B,cAAc,EAAE,kBAAkB;oBAClC,YAAY,EAAE,wBAAwB;oBACtC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBACnC,CAAC;aACH,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,KAAK,GAAG,cAAc,CAAC,GAAG,EAAE,uBAAuB,CAAC,CAAC;YAC3D,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,IAAA,iBAAU,EAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;YAE7D,MAAM,OAAO,GAAG,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,CAAC;YAE5C,MAAM,MAAM,CACV,MAAM,CAAC,mBAAmB,CAAC,2BAA2B,EAAE,OAAO,CAAC,CACjE,CAAC,OAAO,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,YAAY,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;YACjE,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,IAAA,SAAE,EAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEzE,MAAM,OAAO,GAAG,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,CAAC;YAC5C,MAAM,OAAO,GAAG;gBACd,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,UAAU;gBAClB,aAAa,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE;aACvC,CAAC;YAEF,MAAM,MAAM,CAAC,mBAAmB,CAAC,2BAA2B,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YAEhF,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAC9C,MAAM,CAAC,gBAAgB,CAAC;gBACtB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAC/B,eAAe,EAAE,iBAAiB;oBAClC,UAAU,EAAE,OAAO;iBACpB,CAAC;aACH,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,YAAY,GAAG;gBACnB,KAAK,EAAE,SAAS;gBAChB,MAAM,EAAE,WAAW;gBACnB,OAAO,EAAE,WAAW;gBACpB,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;gBAC3B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;YAEF,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,IAAA,SAAE,EAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEzE,MAAM,OAAO,GAAG;gBACd,YAAY,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;gBACnD,WAAW,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE;aACpC,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,mBAAmB,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAC;YAEpF,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC;gBAC3B,KAAK,EAAE,SAAS;gBAChB,MAAM,EAAE,WAAW;gBACnB,OAAO,EAAE,WAAW;gBACpB,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;gBAC3B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAC9C,MAAM,CAAC,gBAAgB,CAAC;gBACtB,MAAM,EAAE,MAAM;gBACd,GAAG,EAAE,yBAAyB;gBAC9B,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,MAAM,EAAE,iCAAiC;aACnD,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,KAAK,GAAG,cAAc,CAAC,GAAG,EAAE,uBAAuB,CAAC,CAAC;YAC3D,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,IAAA,iBAAU,EAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;YAE7D,MAAM,OAAO,GAAG;gBACd,YAAY,EAAE,EAAE;gBAChB,WAAW,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE;aACpC,CAAC;YAEF,MAAM,MAAM,CACV,MAAM,CAAC,mBAAmB,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAC/D,CAAC,OAAO,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,YAAY,GAAG;gBACnB,UAAU,EAAE,WAAW;gBACvB,UAAU,EAAE,IAAI;gBAChB,YAAY,EAAE,CAAC,QAAQ,CAAC;gBACxB,KAAK,EAAE,eAAe;gBACtB,OAAO,EAAE,GAAG;gBACZ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;YAEF,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,IAAA,SAAE,EAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEzE,MAAM,OAAO,GAAG;gBACd,KAAK,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;gBAC9B,KAAK,EAAE,eAAe;aACvB,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,qBAAqB,CAAC,2BAA2B,EAAE,OAAO,CAAC,CAAC;YAExF,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC;gBAC3B,UAAU,EAAE,WAAW;gBACvB,UAAU,EAAE,IAAI;gBAChB,YAAY,EAAE,CAAC,QAAQ,CAAC;gBACxB,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAChC,KAAK,EAAE,eAAe;oBACtB,OAAO,EAAE,GAAG;iBACb,CAAC;gBACF,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAC9C,MAAM,CAAC,gBAAgB,CAAC;gBACtB,MAAM,EAAE,MAAM;gBACd,GAAG,EAAE,2BAA2B;gBAChC,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,IAAI,EAAE,oCAAoC;aACpD,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,KAAK,GAAG,cAAc,CAAC,GAAG,EAAE,sBAAsB,CAAC,CAAC;YAC1D,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,IAAA,iBAAU,EAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;YAE7D,MAAM,OAAO,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;YAEhC,MAAM,MAAM,CACV,MAAM,CAAC,qBAAqB,CAAC,2BAA2B,EAAE,OAAO,CAAC,CACnE,CAAC,OAAO,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,YAAY,GAAG;gBACnB,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,aAAa;gBACrB,YAAY,EAAE,EAAE;gBAChB,OAAO,EAAE,OAAO;aACjB,CAAC;YAEF,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,IAAA,SAAE,EAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEzE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,sBAAsB,CAAC,mBAAmB,CAAC,CAAC;YAExE,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC;gBAC3B,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,aAAa;gBACrB,YAAY,EAAE,EAAE;gBAChB,OAAO,EAAE,OAAO;gBAChB,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAC9C,MAAM,CAAC,gBAAgB,CAAC;gBACtB,MAAM,EAAE,KAAK;gBACb,GAAG,EAAE,0BAA0B;gBAC/B,OAAO,EAAE,IAAI;aACd,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,MAAM,KAAK,GAAG,cAAc,CAAC,GAAG,EAAE,qBAAqB,CAAC,CAAC;YACzD,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,IAAA,iBAAU,EAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;YAE7D,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,sBAAsB,CAAC,mBAAmB,CAAC,CAAC;YAExE,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC;gBAC3B,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,WAAW;gBACnB,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBACzB,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;aAC5B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sEAAsE,EAAE,KAAK,IAAI,EAAE;YACpF,MAAM,YAAY,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;YACtC,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,IAAA,SAAE,EAAC,iBAAiB,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;YAE9E,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,sBAAsB,CAAC,mBAAmB,CAAC,CAAC;YAExE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,YAAY,GAAG;gBACnB,OAAO,EAAE,WAAW;gBACpB,OAAO,EAAE;oBACP,EAAE,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE;oBAC9B,EAAE,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE;iBAC/B;gBACD,KAAK,EAAE,CAAC;gBACR,UAAU,EAAE,CAAC;gBACb,MAAM,EAAE,CAAC;gBACT,cAAc,EAAE,IAAI;gBACpB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;YAEF,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,IAAA,SAAE,EAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEzE,MAAM,QAAQ,GAAG;gBACf,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE;gBACvD,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE;aACxD,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC,yBAAyB,EAAE,QAAQ,CAAC,CAAC;YAElF,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC;gBAC3B,OAAO,EAAE,WAAW;gBACpB,OAAO,EAAE,MAAM,CAAC,eAAe,CAAC;oBAC9B,EAAE,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE;oBAC9B,EAAE,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE;iBAC/B,CAAC;gBACF,OAAO,EAAE;oBACP,KAAK,EAAE,CAAC;oBACR,UAAU,EAAE,CAAC;oBACb,MAAM,EAAE,CAAC;oBACT,cAAc,EAAE,IAAI;iBACrB;gBACD,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAC9C,MAAM,CAAC,gBAAgB,CAAC;gBACtB,MAAM,EAAE,MAAM;gBACd,GAAG,EAAE,yBAAyB;gBAC9B,IAAI,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAC5B,QAAQ,EAAE,QAAQ;oBAClB,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC3B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC9B,CAAC;gBACF,OAAO,EAAE,MAAM,EAAE,8BAA8B;aAChD,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,KAAK,GAAG,cAAc,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC;YACvD,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,IAAA,iBAAU,EAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;YAE7D,MAAM,QAAQ,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;YAEvD,MAAM,MAAM,CACV,MAAM,CAAC,gBAAgB,CAAC,yBAAyB,EAAE,QAAQ,CAAC,CAC7D,CAAC,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,YAAY,GAAG;gBACnB,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE,UAAU;gBACpB,IAAI,EAAE,IAAI;gBACV,GAAG,EAAE,yBAAyB;aAC/B,CAAC;YAEF,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,IAAA,SAAE,EAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEzE,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACtD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,UAAU,CACpC,0BAA0B,EAC1B,UAAU,EACV,UAAU,CACX,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAErC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAC9C,MAAM,CAAC,gBAAgB,CAAC;gBACtB,MAAM,EAAE,MAAM;gBACd,GAAG,EAAE,0BAA0B;gBAC/B,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAC/B,cAAc,EAAE,qBAAqB;iBACtC,CAAC;aACH,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,KAAK,GAAG,cAAc,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;YACpD,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,IAAA,iBAAU,EAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;YAE7D,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAE5C,MAAM,MAAM,CACV,MAAM,CAAC,UAAU,CAAC,0BAA0B,EAAE,UAAU,EAAE,UAAU,CAAC,CACtE,CAAC,OAAO,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,QAAQ,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,YAAY,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACjD,YAAY,CAAC,OAAO,GAAG;gBACrB,cAAc,EAAE,0BAA0B;gBAC1C,qBAAqB,EAAE,uCAAuC;aAC/D,CAAC;YAEF,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,IAAA,SAAE,EAAC,YAAY,CAAC,CAAC,CAAC;YAEtD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,yBAAyB,EAAE,UAAU,CAAC,CAAC;YAEhF,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC;gBAC3B,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBACxB,QAAQ,EAAE,gBAAgB;gBAC1B,WAAW,EAAE,0BAA0B;gBACvC,IAAI,EAAE,IAAI;aACX,CAAC,CAAC;YAEH,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAC9C,MAAM,CAAC,gBAAgB,CAAC;gBACtB,MAAM,EAAE,KAAK;gBACb,GAAG,EAAE,kCAAkC;gBACvC,YAAY,EAAE,aAAa;gBAC3B,OAAO,EAAE,KAAK;aACf,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,KAAK,GAAG,cAAc,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;YACpD,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,IAAA,iBAAU,EAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;YAE7D,MAAM,MAAM,CACV,MAAM,CAAC,YAAY,CAAC,yBAAyB,EAAE,aAAa,CAAC,CAC9D,CAAC,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wEAAwE,EAAE,KAAK,IAAI,EAAE;YACtF,MAAM,QAAQ,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,CAAC;YACtC,MAAM,YAAY,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACjD,YAAY,CAAC,OAAO,GAAG,EAAE,cAAc,EAAE,YAAY,EAAE,CAAC;YAExD,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,IAAA,SAAE,EAAC,YAAY,CAAC,CAAC,CAAC;YAEtD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,yBAAyB,EAAE,UAAU,CAAC,CAAC;YAEhF,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,UAAU,GAAG,IAAI,cAAc,EAAE,CAAC;YACxC,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,IAAA,SAAE,EAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAEvE,MAAM,OAAO,GAAG,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC;YAC3C,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,0BAA0B,EAAE,OAAO,CAAC,CAAC;YAE/E,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEhC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAC9C,MAAM,CAAC,gBAAgB,CAAC;gBACtB,MAAM,EAAE,MAAM;gBACd,GAAG,EAAE,0BAA0B;gBAC/B,IAAI,EAAE,OAAO;gBACb,YAAY,EAAE,QAAQ;aACvB,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,KAAK,GAAG,cAAc,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;YAClD,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,IAAA,iBAAU,EAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;YAE7D,MAAM,OAAO,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;YAEjC,MAAM,MAAM,CACV,MAAM,CAAC,aAAa,CAAC,0BAA0B,EAAE,OAAO,CAAC,CAC1D,CAAC,OAAO,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,MAAM,YAAY,GAAG,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;YAC9D,YAAY,CAAC,MAAM,CAAC,GAAG,cAAc,CAAC;YACtC,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,IAAA,iBAAU,EAAC,GAAG,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC;YAEpE,MAAM,MAAM,CACV,MAAM,CAAC,mBAAmB,CAAC,2BAA2B,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CACtE,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,eAAe,GAAG,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;YAC1D,eAAe,CAAC,MAAM,CAAC,GAAG,cAAc,CAAC;YACzC,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,IAAA,iBAAU,EAAC,GAAG,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC;YAEvE,MAAM,MAAM,CACV,MAAM,CAAC,mBAAmB,CAAC,2BAA2B,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CACtE,CAAC,OAAO,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YAC5C,MAAM,YAAY,GAAG,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;YAChD,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,IAAA,iBAAU,EAAC,GAAG,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC;YAEpE,MAAM,MAAM,CACV,MAAM,CAAC,mBAAmB,CAAC,2BAA2B,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CACtE,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,YAAY,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;YACjE,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,IAAA,SAAE,EAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEzE,MAAM,MAAM,CAAC,mBAAmB,CAAC,2BAA2B,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;YAC5E,MAAM,MAAM,CAAC,mBAAmB,CAAC,2BAA2B,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;YAE5E,MAAM,KAAK,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;YAC7C,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YACvD,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAEvD,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACjC,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACjC,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAW,EAAE,YAAkB,EAAE,EAAE;gBACvE,MAAM,MAAM,GAAG;oBACb,iBAAiB,EAAE,KAAK;oBACxB,iBAAiB,EAAE,CAAC;iBACrB,CAAC;gBACF,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC;YACrC,CAAC,CAAC,CAAC;YAEH,4CAA4C;YAC5C,MAAM,SAAS,GAAG,IAAI,6BAAY,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;YAE/D,MAAM,YAAY,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;YACjE,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,IAAA,SAAE,EAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAEzE,SAAS,CAAC,mBAAmB,CAAC,2BAA2B,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;YAEzE,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAC9C,MAAM,CAAC,gBAAgB,CAAC;gBACtB,OAAO,EAAE,KAAK;aACf,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\communication\\__tests__\\ai-http.client.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { HttpService } from '@nestjs/axios';\r\nimport { AiHttpClient } from '../ai-http.client';\r\nimport { of, throwError } from 'rxjs';\r\nimport { AxiosResponse, AxiosError } from 'axios';\r\n\r\ndescribe('AiHttpClient', () => {\r\n  let client: AiHttpClient;\r\n  let httpService: jest.Mocked<HttpService>;\r\n  let configService: jest.Mocked<ConfigService>;\r\n\r\n  const mockAxiosResponse = <T>(data: T, status = 200): AxiosResponse<T> => ({\r\n    data,\r\n    status,\r\n    statusText: 'OK',\r\n    headers: {},\r\n    config: {} as any,\r\n  });\r\n\r\n  const mockAxiosError = (status: number, message: string): AxiosError => ({\r\n    name: 'AxiosError',\r\n    message,\r\n    response: {\r\n      status,\r\n      statusText: message,\r\n      data: { message },\r\n      headers: {},\r\n      config: {} as any,\r\n    },\r\n    config: {} as any,\r\n    isAxiosError: true,\r\n    toJSON: () => ({}),\r\n  });\r\n\r\n  beforeEach(async () => {\r\n    const mockHttpService = {\r\n      request: jest.fn(),\r\n    };\r\n\r\n    const mockConfigService = {\r\n      get: jest.fn(),\r\n    };\r\n\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        AiHttpClient,\r\n        { provide: HttpService, useValue: mockHttpService },\r\n        { provide: ConfigService, useValue: mockConfigService },\r\n      ],\r\n    }).compile();\r\n\r\n    client = module.get<AiHttpClient>(AiHttpClient);\r\n    httpService = module.get(HttpService);\r\n    configService = module.get(ConfigService);\r\n\r\n    // Setup default config values\r\n    configService.get.mockImplementation((key: string, defaultValue?: any) => {\r\n      const config = {\r\n        'ai.http.timeout': 30000,\r\n        'ai.http.retries': 3,\r\n      };\r\n      return config[key] || defaultValue;\r\n    });\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('sendAnalysisRequest', () => {\r\n    it('should send analysis request successfully', async () => {\r\n      const mockResponse = {\r\n        id: 'analysis-123',\r\n        result: { threat_level: 'high', confidence: 0.95 },\r\n        confidence: 0.95,\r\n        model: 'threat-detector-v1',\r\n        timestamp: new Date().toISOString(),\r\n      };\r\n\r\n      httpService.request.mockReturnValue(of(mockAxiosResponse(mockResponse)));\r\n\r\n      const payload = {\r\n        data: { event: 'suspicious_login' },\r\n        model: 'threat-detector-v1',\r\n      };\r\n\r\n      const result = await client.sendAnalysisRequest('http://ai-service/analyze', payload);\r\n\r\n      expect(result).toMatchObject({\r\n        id: expect.any(String),\r\n        result: { threat_level: 'high', confidence: 0.95 },\r\n        confidence: 0.95,\r\n        metadata: expect.objectContaining({\r\n          model: 'threat-detector-v1',\r\n        }),\r\n        timestamp: expect.any(Date),\r\n      });\r\n\r\n      expect(httpService.request).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          method: 'POST',\r\n          url: 'http://ai-service/analyze',\r\n          data: payload,\r\n          timeout: 30000,\r\n          headers: expect.objectContaining({\r\n            'Content-Type': 'application/json',\r\n            'User-Agent': 'Sentinel-AI-Client/1.0',\r\n            'X-Request-ID': expect.any(String),\r\n          }),\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should handle analysis request failure', async () => {\r\n      const error = mockAxiosError(500, 'Internal Server Error');\r\n      httpService.request.mockReturnValue(throwError(() => error));\r\n\r\n      const payload = { data: { event: 'test' } };\r\n\r\n      await expect(\r\n        client.sendAnalysisRequest('http://ai-service/analyze', payload)\r\n      ).rejects.toThrow('Analysis request failed');\r\n    });\r\n\r\n    it('should use custom timeout and headers', async () => {\r\n      const mockResponse = { id: 'test', result: {}, confidence: 0.8 };\r\n      httpService.request.mockReturnValue(of(mockAxiosResponse(mockResponse)));\r\n\r\n      const payload = { data: { event: 'test' } };\r\n      const options = {\r\n        timeout: 60000,\r\n        apiKey: 'test-key',\r\n        customHeaders: { 'X-Custom': 'value' },\r\n      };\r\n\r\n      await client.sendAnalysisRequest('http://ai-service/analyze', payload, options);\r\n\r\n      expect(httpService.request).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          timeout: 60000,\r\n          headers: expect.objectContaining({\r\n            'Authorization': 'Bearer test-key',\r\n            'X-Custom': 'value',\r\n          }),\r\n        })\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('sendTrainingRequest', () => {\r\n    it('should send training request successfully', async () => {\r\n      const mockResponse = {\r\n        jobId: 'job-123',\r\n        status: 'completed',\r\n        modelId: 'model-456',\r\n        metrics: { accuracy: 0.95 },\r\n        timestamp: new Date().toISOString(),\r\n      };\r\n\r\n      httpService.request.mockReturnValue(of(mockAxiosResponse(mockResponse)));\r\n\r\n      const payload = {\r\n        trainingData: [{ input: 'test', output: 'result' }],\r\n        modelConfig: { type: 'classifier' },\r\n      };\r\n\r\n      const result = await client.sendTrainingRequest('http://ai-service/train', payload);\r\n\r\n      expect(result).toMatchObject({\r\n        jobId: 'job-123',\r\n        status: 'completed',\r\n        modelId: 'model-456',\r\n        metrics: { accuracy: 0.95 },\r\n        timestamp: expect.any(Date),\r\n      });\r\n\r\n      expect(httpService.request).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          method: 'POST',\r\n          url: 'http://ai-service/train',\r\n          data: payload,\r\n          timeout: 300000, // 5 minutes default for training\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should handle training request failure', async () => {\r\n      const error = mockAxiosError(400, 'Invalid training data');\r\n      httpService.request.mockReturnValue(throwError(() => error));\r\n\r\n      const payload = {\r\n        trainingData: [],\r\n        modelConfig: { type: 'classifier' },\r\n      };\r\n\r\n      await expect(\r\n        client.sendTrainingRequest('http://ai-service/train', payload)\r\n      ).rejects.toThrow('Training request failed');\r\n    });\r\n  });\r\n\r\n  describe('sendPredictionRequest', () => {\r\n    it('should send prediction request successfully', async () => {\r\n      const mockResponse = {\r\n        prediction: 'malicious',\r\n        confidence: 0.87,\r\n        alternatives: ['benign'],\r\n        model: 'classifier-v2',\r\n        latency: 150,\r\n        timestamp: new Date().toISOString(),\r\n      };\r\n\r\n      httpService.request.mockReturnValue(of(mockAxiosResponse(mockResponse)));\r\n\r\n      const payload = {\r\n        input: { features: [1, 2, 3] },\r\n        model: 'classifier-v2',\r\n      };\r\n\r\n      const result = await client.sendPredictionRequest('http://ai-service/predict', payload);\r\n\r\n      expect(result).toMatchObject({\r\n        prediction: 'malicious',\r\n        confidence: 0.87,\r\n        alternatives: ['benign'],\r\n        metadata: expect.objectContaining({\r\n          model: 'classifier-v2',\r\n          latency: 150,\r\n        }),\r\n        timestamp: expect.any(Date),\r\n      });\r\n\r\n      expect(httpService.request).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          method: 'POST',\r\n          url: 'http://ai-service/predict',\r\n          data: payload,\r\n          timeout: 5000, // 5 seconds default for predictions\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should handle prediction request failure', async () => {\r\n      const error = mockAxiosError(422, 'Invalid input format');\r\n      httpService.request.mockReturnValue(throwError(() => error));\r\n\r\n      const payload = { input: null };\r\n\r\n      await expect(\r\n        client.sendPredictionRequest('http://ai-service/predict', payload)\r\n      ).rejects.toThrow('Prediction request failed');\r\n    });\r\n  });\r\n\r\n  describe('sendHealthCheckRequest', () => {\r\n    it('should send health check request successfully', async () => {\r\n      const mockResponse = {\r\n        healthy: true,\r\n        status: 'operational',\r\n        responseTime: 45,\r\n        version: '1.2.3',\r\n      };\r\n\r\n      httpService.request.mockReturnValue(of(mockAxiosResponse(mockResponse)));\r\n\r\n      const result = await client.sendHealthCheckRequest('http://ai-service');\r\n\r\n      expect(result).toMatchObject({\r\n        healthy: true,\r\n        status: 'operational',\r\n        responseTime: 45,\r\n        version: '1.2.3',\r\n        timestamp: expect.any(Date),\r\n      });\r\n\r\n      expect(httpService.request).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          method: 'GET',\r\n          url: 'http://ai-service/health',\r\n          timeout: 5000,\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should handle health check failure gracefully', async () => {\r\n      const error = mockAxiosError(503, 'Service Unavailable');\r\n      httpService.request.mockReturnValue(throwError(() => error));\r\n\r\n      const result = await client.sendHealthCheckRequest('http://ai-service');\r\n\r\n      expect(result).toMatchObject({\r\n        healthy: false,\r\n        status: 'unhealthy',\r\n        error: expect.any(String),\r\n        timestamp: expect.any(Date),\r\n      });\r\n    });\r\n\r\n    it('should consider 200 status as healthy when no explicit healthy field', async () => {\r\n      const mockResponse = { status: 'ok' };\r\n      httpService.request.mockReturnValue(of(mockAxiosResponse(mockResponse, 200)));\r\n\r\n      const result = await client.sendHealthCheckRequest('http://ai-service');\r\n\r\n      expect(result.healthy).toBe(true);\r\n    });\r\n  });\r\n\r\n  describe('sendBatchRequest', () => {\r\n    it('should send batch request successfully', async () => {\r\n      const mockResponse = {\r\n        batchId: 'batch-123',\r\n        results: [\r\n          { id: '1', result: 'success' },\r\n          { id: '2', result: 'success' },\r\n        ],\r\n        total: 2,\r\n        successful: 2,\r\n        failed: 0,\r\n        processingTime: 1500,\r\n        timestamp: new Date().toISOString(),\r\n      };\r\n\r\n      httpService.request.mockReturnValue(of(mockAxiosResponse(mockResponse)));\r\n\r\n      const payloads = [\r\n        { id: '1', data: { input: 'test1' }, type: 'analysis' },\r\n        { id: '2', data: { input: 'test2' }, type: 'analysis' },\r\n      ];\r\n\r\n      const result = await client.sendBatchRequest('http://ai-service/batch', payloads);\r\n\r\n      expect(result).toMatchObject({\r\n        batchId: 'batch-123',\r\n        results: expect.arrayContaining([\r\n          { id: '1', result: 'success' },\r\n          { id: '2', result: 'success' },\r\n        ]),\r\n        summary: {\r\n          total: 2,\r\n          successful: 2,\r\n          failed: 0,\r\n          processingTime: 1500,\r\n        },\r\n        timestamp: expect.any(Date),\r\n      });\r\n\r\n      expect(httpService.request).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          method: 'POST',\r\n          url: 'http://ai-service/batch',\r\n          data: expect.objectContaining({\r\n            requests: payloads,\r\n            batchId: expect.any(String),\r\n            timestamp: expect.any(String),\r\n          }),\r\n          timeout: 120000, // 2 minutes default for batch\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should handle batch request failure', async () => {\r\n      const error = mockAxiosError(413, 'Payload Too Large');\r\n      httpService.request.mockReturnValue(throwError(() => error));\r\n\r\n      const payloads = [{ id: '1', data: {}, type: 'test' }];\r\n\r\n      await expect(\r\n        client.sendBatchRequest('http://ai-service/batch', payloads)\r\n      ).rejects.toThrow('Batch request failed');\r\n    });\r\n  });\r\n\r\n  describe('uploadFile', () => {\r\n    it('should upload file successfully', async () => {\r\n      const mockResponse = {\r\n        fileId: 'file-123',\r\n        filename: 'test.csv',\r\n        size: 1024,\r\n        url: 'http://storage/file-123',\r\n      };\r\n\r\n      httpService.request.mockReturnValue(of(mockAxiosResponse(mockResponse)));\r\n\r\n      const fileBuffer = Buffer.from('test,data\\n1,2\\n3,4');\r\n      const result = await client.uploadFile(\r\n        'http://ai-service/upload',\r\n        fileBuffer,\r\n        'test.csv'\r\n      );\r\n\r\n      expect(result).toEqual(mockResponse);\r\n\r\n      expect(httpService.request).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          method: 'POST',\r\n          url: 'http://ai-service/upload',\r\n          data: expect.any(FormData),\r\n          timeout: 60000,\r\n          headers: expect.objectContaining({\r\n            'Content-Type': 'multipart/form-data',\r\n          }),\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should handle file upload failure', async () => {\r\n      const error = mockAxiosError(413, 'File Too Large');\r\n      httpService.request.mockReturnValue(throwError(() => error));\r\n\r\n      const fileBuffer = Buffer.from('test data');\r\n\r\n      await expect(\r\n        client.uploadFile('http://ai-service/upload', fileBuffer, 'test.txt')\r\n      ).rejects.toThrow('File upload failed');\r\n    });\r\n  });\r\n\r\n  describe('downloadFile', () => {\r\n    it('should download file successfully', async () => {\r\n      const fileData = new ArrayBuffer(1024);\r\n      const mockResponse = mockAxiosResponse(fileData);\r\n      mockResponse.headers = {\r\n        'content-type': 'application/octet-stream',\r\n        'content-disposition': 'attachment; filename=\"downloaded.bin\"',\r\n      };\r\n\r\n      httpService.request.mockReturnValue(of(mockResponse));\r\n\r\n      const result = await client.downloadFile('http://ai-service/files', 'file-123');\r\n\r\n      expect(result).toMatchObject({\r\n        data: expect.any(Buffer),\r\n        filename: 'downloaded.bin',\r\n        contentType: 'application/octet-stream',\r\n        size: 1024,\r\n      });\r\n\r\n      expect(httpService.request).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          method: 'GET',\r\n          url: 'http://ai-service/files/file-123',\r\n          responseType: 'arraybuffer',\r\n          timeout: 60000,\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should handle file download failure', async () => {\r\n      const error = mockAxiosError(404, 'File Not Found');\r\n      httpService.request.mockReturnValue(throwError(() => error));\r\n\r\n      await expect(\r\n        client.downloadFile('http://ai-service/files', 'nonexistent')\r\n      ).rejects.toThrow('File download failed');\r\n    });\r\n\r\n    it('should use default filename when content-disposition header is missing', async () => {\r\n      const fileData = new ArrayBuffer(512);\r\n      const mockResponse = mockAxiosResponse(fileData);\r\n      mockResponse.headers = { 'content-type': 'text/plain' };\r\n\r\n      httpService.request.mockReturnValue(of(mockResponse));\r\n\r\n      const result = await client.downloadFile('http://ai-service/files', 'file-123');\r\n\r\n      expect(result.filename).toBe('download');\r\n    });\r\n  });\r\n\r\n  describe('streamRequest', () => {\r\n    it('should handle stream request successfully', async () => {\r\n      const mockStream = new ReadableStream();\r\n      httpService.request.mockReturnValue(of(mockAxiosResponse(mockStream)));\r\n\r\n      const payload = { data: 'streaming test' };\r\n      const result = await client.streamRequest('http://ai-service/stream', payload);\r\n\r\n      expect(result).toBe(mockStream);\r\n\r\n      expect(httpService.request).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          method: 'POST',\r\n          url: 'http://ai-service/stream',\r\n          data: payload,\r\n          responseType: 'stream',\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should handle stream request failure', async () => {\r\n      const error = mockAxiosError(500, 'Stream Error');\r\n      httpService.request.mockReturnValue(throwError(() => error));\r\n\r\n      const payload = { data: 'test' };\r\n\r\n      await expect(\r\n        client.streamRequest('http://ai-service/stream', payload)\r\n      ).rejects.toThrow('Stream request failed');\r\n    });\r\n  });\r\n\r\n  describe('error handling', () => {\r\n    it('should handle timeout errors', async () => {\r\n      const timeoutError = new Error('timeout of 30000ms exceeded');\r\n      timeoutError['code'] = 'ECONNABORTED';\r\n      httpService.request.mockReturnValue(throwError(() => timeoutError));\r\n\r\n      await expect(\r\n        client.sendAnalysisRequest('http://ai-service/analyze', { data: {} })\r\n      ).rejects.toThrow('Request timeout');\r\n    });\r\n\r\n    it('should handle connection refused errors', async () => {\r\n      const connectionError = new Error('connect ECONNREFUSED');\r\n      connectionError['code'] = 'ECONNREFUSED';\r\n      httpService.request.mockReturnValue(throwError(() => connectionError));\r\n\r\n      await expect(\r\n        client.sendAnalysisRequest('http://ai-service/analyze', { data: {} })\r\n      ).rejects.toThrow('Connection refused');\r\n    });\r\n\r\n    it('should handle unknown errors', async () => {\r\n      const unknownError = new Error('Unknown error');\r\n      httpService.request.mockReturnValue(throwError(() => unknownError));\r\n\r\n      await expect(\r\n        client.sendAnalysisRequest('http://ai-service/analyze', { data: {} })\r\n      ).rejects.toThrow('Unknown error');\r\n    });\r\n  });\r\n\r\n  describe('request configuration', () => {\r\n    it('should generate unique request IDs', async () => {\r\n      const mockResponse = { id: 'test', result: {}, confidence: 0.5 };\r\n      httpService.request.mockReturnValue(of(mockAxiosResponse(mockResponse)));\r\n\r\n      await client.sendAnalysisRequest('http://ai-service/analyze', { data: {} });\r\n      await client.sendAnalysisRequest('http://ai-service/analyze', { data: {} });\r\n\r\n      const calls = httpService.request.mock.calls;\r\n      const requestId1 = calls[0][0].headers['X-Request-ID'];\r\n      const requestId2 = calls[1][0].headers['X-Request-ID'];\r\n\r\n      expect(requestId1).toBeDefined();\r\n      expect(requestId2).toBeDefined();\r\n      expect(requestId1).not.toBe(requestId2);\r\n    });\r\n\r\n    it('should use configured timeout and retries', () => {\r\n      configService.get.mockImplementation((key: string, defaultValue?: any) => {\r\n        const config = {\r\n          'ai.http.timeout': 45000,\r\n          'ai.http.retries': 5,\r\n        };\r\n        return config[key] || defaultValue;\r\n      });\r\n\r\n      // Create new instance to pick up new config\r\n      const newClient = new AiHttpClient(httpService, configService);\r\n\r\n      const mockResponse = { id: 'test', result: {}, confidence: 0.5 };\r\n      httpService.request.mockReturnValue(of(mockAxiosResponse(mockResponse)));\r\n\r\n      newClient.sendAnalysisRequest('http://ai-service/analyze', { data: {} });\r\n\r\n      expect(httpService.request).toHaveBeenCalledWith(\r\n        expect.objectContaining({\r\n          timeout: 45000,\r\n        })\r\n      );\r\n    });\r\n  });\r\n});"], "version": 3}