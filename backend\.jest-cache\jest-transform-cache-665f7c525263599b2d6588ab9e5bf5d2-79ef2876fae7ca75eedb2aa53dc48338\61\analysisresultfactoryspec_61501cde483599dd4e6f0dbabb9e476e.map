{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\factories\\__tests__\\analysis-result.factory.spec.ts", "mappings": ";;AAAA,4HAA0G;AAC1G,kFAAoG;AACpG,wEAAoH;AAEpH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;IACrC,IAAI,kBAA+C,CAAC;IACpD,IAAI,cAA6B,CAAC;IAClC,IAAI,uBAA2C,CAAC;IAEhD,UAAU,CAAC,GAAG,EAAE;QACd,cAAc,GAAG;YACf,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;YACvB,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,GAAG;YACT,QAAQ,EAAE,gBAAgB;YAC1B,oBAAoB,EAAE,CAAC,eAAe,CAAC;YACvC,eAAe,EAAE,CAAC,UAAU,CAAC;SAC9B,CAAC;QAEF,kBAAkB,GAAG;YACnB,SAAS,EAAE,kBAAkB;YAC7B,OAAO,EAAE,8CAAc,CAAC,QAAQ,EAAE;YAClC,YAAY,EAAE,qCAAY,CAAC,cAAc;YACzC,SAAS,EAAE,cAAc;YACzB,IAAI,EAAE,CAAC,MAAM,EAAE,gBAAgB,CAAC;YAChC,aAAa,EAAE,iBAAiB;YAChC,gBAAgB,EAAE,8CAAc,CAAC,QAAQ,EAAE;SAC5C,CAAC;QAEF,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,uBAAuB,GAAG;YACxB,EAAE,EAAE,8CAAc,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;YACxC,SAAS,EAAE,kBAAkB;YAC7B,OAAO,EAAE,8CAAc,CAAC,QAAQ,EAAE;YAClC,YAAY,EAAE,qCAAY,CAAC,cAAc;YACzC,SAAS,EAAE,cAAc;YACzB,UAAU,EAAE;gBACV,OAAO,EAAE,EAAE,UAAU,EAAE,aAAa,EAAE;gBACtC,MAAM,EAAE,MAAM;gBACd,IAAI,EAAE,GAAG;gBACT,QAAQ,EAAE,iBAAiB;gBAC3B,qBAAqB,EAAE,CAAC,YAAY,CAAC;gBACrC,gBAAgB,EAAE,QAAQ;gBAC1B,YAAY,EAAE,GAAG;aAClB;YACD,UAAU,EAAE,IAAI;YAChB,cAAc,EAAE,IAAI;YACpB,MAAM,EAAE,uCAAc,CAAC,SAAS;YAChC,QAAQ,EAAE;gBACR,OAAO,EAAE,OAAO;gBAChB,SAAS,EAAE,gBAAgB;gBAC3B,UAAU,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE;gBAChC,WAAW,EAAE,MAAM;gBACnB,aAAa,EAAE;oBACb,OAAO,EAAE,IAAI;oBACb,WAAW,EAAE,GAAG;oBAChB,OAAO,EAAE,GAAG;oBACZ,SAAS,EAAE,GAAG;oBACd,MAAM,EAAE,EAAE;iBACX;gBACD,kBAAkB,EAAE;oBAClB,UAAU,EAAE,EAAE;oBACd,OAAO,EAAE,GAAG;oBACZ,QAAQ,EAAE,IAAI;oBACd,SAAS,EAAE,IAAI;oBACf,MAAM,EAAE,IAAI;oBACZ,OAAO,EAAE,IAAI;iBACd;gBACD,cAAc,EAAE;oBACd,WAAW,EAAE,GAAG;oBAChB,iBAAiB,EAAE,IAAI;oBACvB,gBAAgB,EAAE,GAAG;oBACrB,iBAAiB,EAAE,IAAI;iBACxB;aACF;YACD,IAAI,EAAE,CAAC,MAAM,EAAE,gBAAgB,CAAC;YAChC,aAAa,EAAE,iBAAiB;YAChC,gBAAgB,EAAE,8CAAc,CAAC,QAAQ,EAAE;YAC3C,gBAAgB,EAAE,CAAC,8CAAc,CAAC,QAAQ,EAAE,CAAC;YAC7C,YAAY,EAAE,SAAS;YACvB,SAAS,EAAE,GAAG;YACd,SAAS,EAAE,GAAG;YACd,WAAW,EAAE,GAAG;SACjB,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;QACtB,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,MAAM,GAAG,+CAAqB,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;YAEhE,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;YAC5D,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrE,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;YAClE,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;YAC/D,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,uCAAc,CAAC,OAAO,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC,CAAC;YACxD,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;YACpE,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE,MAAM,CAAC,kBAAkB,CAAC,gBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzF,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,QAAQ,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YAC3C,MAAM,MAAM,GAAG,+CAAqB,CAAC,MAAM,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;YAE1E,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oEAAoE,EAAE,GAAG,EAAE;YAC5E,MAAM,cAAc,GAAgC;gBAClD,SAAS,EAAE,iBAAiB;gBAC5B,OAAO,EAAE,8CAAc,CAAC,QAAQ,EAAE;gBAClC,YAAY,EAAE,qCAAY,CAAC,iBAAiB;gBAC5C,SAAS,EAAE,cAAc;aAC1B,CAAC;YAEF,MAAM,MAAM,GAAG,+CAAqB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAE5D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAChC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,aAAa,EAAE,CAAC;YAC7C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,aAAa,EAAE,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,MAAM,mBAAmB,GAAgC;gBACvD,GAAG,kBAAkB;gBACrB,QAAQ,EAAE;oBACR,OAAO,EAAE,OAAO;oBAChB,SAAS,EAAE,kBAAkB;iBAC9B;aACF,CAAC;YAEF,MAAM,MAAM,GAAG,+CAAqB,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;YAEjE,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,gBAAgB;QAC1E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,wBAAwB,GAAgC;gBAC5D,GAAG,kBAAkB;gBACrB,IAAI,EAAE,CAAC,MAAM,EAAE,gBAAgB,EAAE,OAAO,CAAC;aAC1C,CAAC;YAEF,MAAM,MAAM,GAAG,+CAAqB,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC;YAEtE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;YACnE,MAAM,qBAAqB,GAAgC;gBACzD,GAAG,kBAAkB;gBACrB,SAAS,EAAE,sBAAsB;gBACjC,aAAa,EAAE,qBAAqB;aACrC,CAAC;YAEF,MAAM,MAAM,GAAG,+CAAqB,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;YAEnE,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;YAC1B,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;gBACjD,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC;gBAEhE,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;YAC/F,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;gBAC3D,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;gBAEnE,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;YAC/F,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;gBACpD,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,SAAS,EAAE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;gBAE7E,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,yCAAyC,CAAC,CAAC;YAChH,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;gBACjD,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,OAAO,EAAE,IAAW,EAAE,CAAC;gBAEvE,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;YAC7F,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;gBACtD,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,YAAY,EAAE,SAAyB,EAAE,CAAC;gBAE1F,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;YAC9F,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;gBACnD,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,SAAS,EAAE,IAAW,EAAE,CAAC;gBAEzE,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;YAC/F,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;gBACnD,MAAM,gBAAgB,GAAG,EAAE,GAAG,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;gBAC3D,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAC;gBAE9E,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;YACvG,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;gBACxD,MAAM,gBAAgB,GAAG,EAAE,GAAG,cAAc,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;gBAC3D,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAC;gBAE9E,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC;YACtG,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,gBAAgB,GAAG,EAAE,GAAG,cAAc,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;gBACzD,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAC;gBAE9E,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;YAC3G,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;gBAC/C,MAAM,gBAAgB,GAAG,EAAE,GAAG,cAAc,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;gBAC7D,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAC;gBAE9E,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;YACxG,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;gBAC9D,MAAM,gBAAgB,GAAG,EAAE,GAAG,cAAc,EAAE,oBAAoB,EAAE,WAAkB,EAAE,CAAC;gBACzF,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAC;gBAE9E,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,wCAAwC,CAAC,CAAC;YAC/G,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,gBAAgB,GAAG,EAAE,GAAG,cAAc,EAAE,eAAe,EAAE,WAAkB,EAAE,CAAC;gBACpF,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAC;gBAE9E,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAC;YAC1G,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;gBACnD,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,IAAI,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC;gBAEtE,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;YAC3G,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;gBACrD,MAAM,cAAc,GAAG,EAAE,GAAG,kBAAkB,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC;gBAEvE,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;YACnH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,MAAM,GAAG,+CAAqB,CAAC,YAAY,CAAC,uBAAuB,CAAC,CAAC;YAE3E,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,EAAE,CAAC,CAAC;YAC9D,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;YACjE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1E,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;YACvE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;YACnE,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;YAC3E,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;YACjE,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;YACjE,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;YAC1B,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;gBAC3C,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,EAAE,EAAE,YAAY,EAAE,CAAC;gBAErE,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,yCAAyC,CAAC,CAAC;YACnH,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;gBACjD,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC;gBAElE,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;YAClG,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;gBACjD,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,OAAO,EAAE,IAAW,EAAE,CAAC;gBAEzE,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;YAChG,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;gBACtD,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,YAAY,EAAE,SAAyB,EAAE,CAAC;gBAE5F,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;YACjG,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;gBAC/C,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,MAAM,EAAE,SAA2B,EAAE,CAAC;gBAExF,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;YACnG,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;gBACnD,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,SAAS,EAAE,IAAW,EAAE,CAAC;gBAE3E,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;YAClG,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;gBACjD,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,QAAQ,EAAE,IAAW,EAAE,CAAC;gBAE1E,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;YAChG,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;gBACnD,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;gBAEpE,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;YAC9G,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;gBACzD,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,cAAc,EAAE,CAAC,GAAG,EAAE,CAAC;gBAEzE,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;YAC9G,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;gBAC7C,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,IAAI,EAAE,WAAkB,EAAE,CAAC;gBAE7E,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;YACjG,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;gBAC3D,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,gBAAgB,EAAE,WAAkB,EAAE,CAAC;gBAEzF,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,qCAAqC,CAAC,CAAC;YAC/G,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;gBACtD,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,SAAS,EAAE,SAAgB,EAAE,CAAC;gBAEhF,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;YAC3G,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;gBACpD,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,SAAS,EAAE,SAAgB,EAAE,CAAC;gBAEhF,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC;YACzG,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;gBACvD,MAAM,WAAW,GAAG,EAAE,GAAG,uBAAuB,EAAE,WAAW,EAAE,SAAgB,EAAE,CAAC;gBAElF,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,iDAAiD,CAAC,CAAC;YAC3H,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,6DAA6D,EAAE,GAAG,EAAE;gBACrE,MAAM,WAAW,GAAG;oBAClB,GAAG,uBAAuB;oBAC1B,MAAM,EAAE,uCAAc,CAAC,SAAS;oBAChC,UAAU,EAAE,IAAW;iBACxB,CAAC;gBAEF,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,gDAAgD,CAAC,CAAC;YAC1H,CAAC,CAAC,CAAC;YAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;gBACpE,MAAM,WAAW,GAAG;oBAClB,GAAG,uBAAuB;oBAC1B,MAAM,EAAE,uCAAc,CAAC,MAAM;oBAC7B,YAAY,EAAE,SAAS;iBACxB,CAAC;gBAEF,MAAM,CAAC,GAAG,EAAE,CAAC,+CAAqB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,gDAAgD,CAAC,CAAC;YAC1H,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,MAAM,GAAG,+CAAqB,CAAC,gBAAgB,EAAE,CAAC;YAExD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,qCAAY,CAAC,cAAc,CAAC,CAAC;YAC9D,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,uCAAc,CAAC,OAAO,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,MAAM,SAAS,GAAG;gBAChB,SAAS,EAAE,gBAAgB;gBAC3B,YAAY,EAAE,qCAAY,CAAC,iBAAiB;gBAC5C,IAAI,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;aACzB,CAAC;YAEF,MAAM,MAAM,GAAG,+CAAqB,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAEjE,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,qCAAY,CAAC,iBAAiB,CAAC,CAAC;YACjE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,0BAA0B;QACvF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,MAAM,GAAG,+CAAqB,CAAC,yBAAyB,EAAE,CAAC;YAEjE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,uCAAc,CAAC,SAAS,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;YACvE,MAAM,SAAS,GAAG;gBAChB,SAAS,EAAE,mBAAmB;gBAC9B,YAAY,EAAE,qCAAY,CAAC,YAAY;aACxC,CAAC;YAEF,MAAM,MAAM,GAAG,+CAAqB,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC;YAE1E,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,qCAAY,CAAC,YAAY,CAAC,CAAC;YAC5D,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,uCAAc,CAAC,SAAS,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,MAAM,GAAG,+CAAqB,CAAC,sBAAsB,EAAE,CAAC;YAE9D,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,uCAAc,CAAC,MAAM,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,YAAa,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,YAAa,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,SAAS,GAAG;gBAChB,SAAS,EAAE,gBAAgB;gBAC3B,YAAY,EAAE,qCAAY,CAAC,gBAAgB;aAC5C,CAAC;YAEF,MAAM,MAAM,GAAG,+CAAqB,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;YAEvE,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,qCAAY,CAAC,gBAAgB,CAAC,CAAC;YAChE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,uCAAc,CAAC,MAAM,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\factories\\__tests__\\analysis-result.factory.spec.ts"], "sourcesContent": ["import { UniqueEntityId } from '../../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { AnalysisType, AnalysisStatus, AnalysisInput } from '../../entities/analysis-result.entity';\r\nimport { AnalysisResultFactory, CreateAnalysisResultRequest, ReconstitutionData } from '../analysis-result.factory';\r\n\r\ndescribe('AnalysisResultFactory', () => {\r\n  let validCreateRequest: CreateAnalysisResultRequest;\r\n  let validInputData: AnalysisInput;\r\n  let validReconstitutionData: ReconstitutionData;\r\n\r\n  beforeEach(() => {\r\n    validInputData = {\r\n      data: { test: 'input' },\r\n      format: 'json',\r\n      size: 100,\r\n      checksum: 'input-checksum',\r\n      preprocessingApplied: ['normalization'],\r\n      validationRules: ['required'],\r\n    };\r\n\r\n    validCreateRequest = {\r\n      requestId: 'test-request-123',\r\n      modelId: UniqueEntityId.generate(),\r\n      analysisType: AnalysisType.CLASSIFICATION,\r\n      inputData: validInputData,\r\n      tags: ['test', 'classification'],\r\n      correlationId: 'correlation-123',\r\n      parentAnalysisId: UniqueEntityId.generate(),\r\n    };\r\n\r\n    const now = new Date();\r\n    validReconstitutionData = {\r\n      id: UniqueEntityId.generate().toString(),\r\n      requestId: 'test-request-123',\r\n      modelId: UniqueEntityId.generate(),\r\n      analysisType: AnalysisType.CLASSIFICATION,\r\n      inputData: validInputData,\r\n      outputData: {\r\n        results: { prediction: 'test-result' },\r\n        format: 'json',\r\n        size: 200,\r\n        checksum: 'output-checksum',\r\n        postprocessingApplied: ['formatting'],\r\n        validationStatus: 'passed',\r\n        qualityScore: 0.9,\r\n      },\r\n      confidence: 0.85,\r\n      processingTime: 1500,\r\n      status: AnalysisStatus.COMPLETED,\r\n      metadata: {\r\n        version: '1.0.0',\r\n        algorithm: 'test-algorithm',\r\n        parameters: { param1: 'value1' },\r\n        environment: 'test',\r\n        resourceUsage: {\r\n          cpuTime: 1000,\r\n          memoryUsage: 512,\r\n          gpuTime: 500,\r\n          networkIO: 100,\r\n          diskIO: 50,\r\n        },\r\n        performanceMetrics: {\r\n          throughput: 10,\r\n          latency: 100,\r\n          accuracy: 0.95,\r\n          precision: 0.92,\r\n          recall: 0.88,\r\n          f1Score: 0.90,\r\n        },\r\n        qualityMetrics: {\r\n          dataQuality: 0.9,\r\n          resultReliability: 0.85,\r\n          consistencyScore: 0.8,\r\n          completenessScore: 0.95,\r\n        },\r\n      },\r\n      tags: ['test', 'classification'],\r\n      correlationId: 'correlation-123',\r\n      parentAnalysisId: UniqueEntityId.generate(),\r\n      childAnalysisIds: [UniqueEntityId.generate()],\r\n      errorDetails: undefined,\r\n      createdAt: now,\r\n      updatedAt: now,\r\n      completedAt: now,\r\n    };\r\n  });\r\n\r\n  describe('create', () => {\r\n    it('should create a valid analysis result', () => {\r\n      const result = AnalysisResultFactory.create(validCreateRequest);\r\n\r\n      expect(result.requestId).toBe(validCreateRequest.requestId);\r\n      expect(result.modelId.equals(validCreateRequest.modelId)).toBe(true);\r\n      expect(result.analysisType).toBe(validCreateRequest.analysisType);\r\n      expect(result.inputData).toEqual(validCreateRequest.inputData);\r\n      expect(result.status).toBe(AnalysisStatus.PENDING);\r\n      expect(result.confidence).toBe(0);\r\n      expect(result.processingTime).toBe(0);\r\n      expect(result.tags).toEqual(['test', 'classification']);\r\n      expect(result.correlationId).toBe(validCreateRequest.correlationId);\r\n      expect(result.parentAnalysisId?.equals(validCreateRequest.parentAnalysisId!)).toBe(true);\r\n      expect(result.childAnalysisIds).toEqual([]);\r\n      expect(result.createdAt).toBeInstanceOf(Date);\r\n      expect(result.updatedAt).toBeInstanceOf(Date);\r\n    });\r\n\r\n    it('should create with custom ID', () => {\r\n      const customId = UniqueEntityId.generate();\r\n      const result = AnalysisResultFactory.create(validCreateRequest, customId);\r\n\r\n      expect(result.id.equals(customId)).toBe(true);\r\n    });\r\n\r\n    it('should create with default values when optional fields are omitted', () => {\r\n      const minimalRequest: CreateAnalysisResultRequest = {\r\n        requestId: 'minimal-request',\r\n        modelId: UniqueEntityId.generate(),\r\n        analysisType: AnalysisType.ANOMALY_DETECTION,\r\n        inputData: validInputData,\r\n      };\r\n\r\n      const result = AnalysisResultFactory.create(minimalRequest);\r\n\r\n      expect(result.tags).toEqual([]);\r\n      expect(result.correlationId).toBeUndefined();\r\n      expect(result.parentAnalysisId).toBeUndefined();\r\n      expect(result.metadata.version).toBe('1.0.0');\r\n      expect(result.metadata.algorithm).toBe('unknown');\r\n      expect(result.metadata.environment).toBe('production');\r\n    });\r\n\r\n    it('should create with partial metadata', () => {\r\n      const requestWithMetadata: CreateAnalysisResultRequest = {\r\n        ...validCreateRequest,\r\n        metadata: {\r\n          version: '2.0.0',\r\n          algorithm: 'custom-algorithm',\r\n        },\r\n      };\r\n\r\n      const result = AnalysisResultFactory.create(requestWithMetadata);\r\n\r\n      expect(result.metadata.version).toBe('2.0.0');\r\n      expect(result.metadata.algorithm).toBe('custom-algorithm');\r\n      expect(result.metadata.environment).toBe('production'); // Default value\r\n    });\r\n\r\n    it('should normalize tags to lowercase', () => {\r\n      const requestWithUppercaseTags: CreateAnalysisResultRequest = {\r\n        ...validCreateRequest,\r\n        tags: ['TEST', 'Classification', 'UPPER'],\r\n      };\r\n\r\n      const result = AnalysisResultFactory.create(requestWithUppercaseTags);\r\n\r\n      expect(result.tags).toEqual(['test', 'classification', 'upper']);\r\n    });\r\n\r\n    it('should trim whitespace from request ID and correlation ID', () => {\r\n      const requestWithWhitespace: CreateAnalysisResultRequest = {\r\n        ...validCreateRequest,\r\n        requestId: '  test-request-123  ',\r\n        correlationId: '  correlation-123  ',\r\n      };\r\n\r\n      const result = AnalysisResultFactory.create(requestWithWhitespace);\r\n\r\n      expect(result.requestId).toBe('test-request-123');\r\n      expect(result.correlationId).toBe('correlation-123');\r\n    });\r\n\r\n    describe('validation', () => {\r\n      it('should throw error for empty request ID', () => {\r\n        const invalidRequest = { ...validCreateRequest, requestId: '' };\r\n\r\n        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('Request ID is required');\r\n      });\r\n\r\n      it('should throw error for whitespace-only request ID', () => {\r\n        const invalidRequest = { ...validCreateRequest, requestId: '   ' };\r\n\r\n        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('Request ID is required');\r\n      });\r\n\r\n      it('should throw error for request ID too long', () => {\r\n        const invalidRequest = { ...validCreateRequest, requestId: 'a'.repeat(256) };\r\n\r\n        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('Request ID cannot exceed 255 characters');\r\n      });\r\n\r\n      it('should throw error for missing model ID', () => {\r\n        const invalidRequest = { ...validCreateRequest, modelId: null as any };\r\n\r\n        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('Model ID is required');\r\n      });\r\n\r\n      it('should throw error for invalid analysis type', () => {\r\n        const invalidRequest = { ...validCreateRequest, analysisType: 'invalid' as AnalysisType };\r\n\r\n        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('Invalid analysis type');\r\n      });\r\n\r\n      it('should throw error for missing input data', () => {\r\n        const invalidRequest = { ...validCreateRequest, inputData: null as any };\r\n\r\n        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('Input data is required');\r\n      });\r\n\r\n      it('should throw error for invalid input data', () => {\r\n        const invalidInputData = { ...validInputData, data: null };\r\n        const invalidRequest = { ...validCreateRequest, inputData: invalidInputData };\r\n\r\n        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('Input data content is required');\r\n      });\r\n\r\n      it('should throw error for empty input data format', () => {\r\n        const invalidInputData = { ...validInputData, format: '' };\r\n        const invalidRequest = { ...validCreateRequest, inputData: invalidInputData };\r\n\r\n        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('Input data format is required');\r\n      });\r\n\r\n      it('should throw error for negative input data size', () => {\r\n        const invalidInputData = { ...validInputData, size: -1 };\r\n        const invalidRequest = { ...validCreateRequest, inputData: invalidInputData };\r\n\r\n        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('Input data size cannot be negative');\r\n      });\r\n\r\n      it('should throw error for empty checksum', () => {\r\n        const invalidInputData = { ...validInputData, checksum: '' };\r\n        const invalidRequest = { ...validCreateRequest, inputData: invalidInputData };\r\n\r\n        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('Input data checksum is required');\r\n      });\r\n\r\n      it('should throw error for invalid preprocessing applied', () => {\r\n        const invalidInputData = { ...validInputData, preprocessingApplied: 'not-array' as any };\r\n        const invalidRequest = { ...validCreateRequest, inputData: invalidInputData };\r\n\r\n        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('Preprocessing applied must be an array');\r\n      });\r\n\r\n      it('should throw error for invalid validation rules', () => {\r\n        const invalidInputData = { ...validInputData, validationRules: 'not-array' as any };\r\n        const invalidRequest = { ...validCreateRequest, inputData: invalidInputData };\r\n\r\n        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('Validation rules must be an array');\r\n      });\r\n\r\n      it('should throw error for empty tag in array', () => {\r\n        const invalidRequest = { ...validCreateRequest, tags: ['valid', ''] };\r\n\r\n        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('All tags must be non-empty strings');\r\n      });\r\n\r\n      it('should throw error for empty correlation ID', () => {\r\n        const invalidRequest = { ...validCreateRequest, correlationId: '   ' };\r\n\r\n        expect(() => AnalysisResultFactory.create(invalidRequest)).toThrow('Correlation ID cannot be empty if provided');\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('reconstitute', () => {\r\n    it('should reconstitute a valid analysis result', () => {\r\n      const result = AnalysisResultFactory.reconstitute(validReconstitutionData);\r\n\r\n      expect(result.id.toString()).toBe(validReconstitutionData.id);\r\n      expect(result.requestId).toBe(validReconstitutionData.requestId);\r\n      expect(result.modelId.equals(validReconstitutionData.modelId)).toBe(true);\r\n      expect(result.analysisType).toBe(validReconstitutionData.analysisType);\r\n      expect(result.status).toBe(validReconstitutionData.status);\r\n      expect(result.confidence).toBe(validReconstitutionData.confidence);\r\n      expect(result.processingTime).toBe(validReconstitutionData.processingTime);\r\n      expect(result.createdAt).toBe(validReconstitutionData.createdAt);\r\n      expect(result.updatedAt).toBe(validReconstitutionData.updatedAt);\r\n      expect(result.completedAt).toBe(validReconstitutionData.completedAt);\r\n    });\r\n\r\n    describe('validation', () => {\r\n      it('should throw error for invalid ID', () => {\r\n        const invalidData = { ...validReconstitutionData, id: 'invalid-id' };\r\n\r\n        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Valid ID is required for reconstitution');\r\n      });\r\n\r\n      it('should throw error for empty request ID', () => {\r\n        const invalidData = { ...validReconstitutionData, requestId: '' };\r\n\r\n        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Request ID is required');\r\n      });\r\n\r\n      it('should throw error for missing model ID', () => {\r\n        const invalidData = { ...validReconstitutionData, modelId: null as any };\r\n\r\n        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Model ID is required');\r\n      });\r\n\r\n      it('should throw error for invalid analysis type', () => {\r\n        const invalidData = { ...validReconstitutionData, analysisType: 'invalid' as AnalysisType };\r\n\r\n        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Invalid analysis type');\r\n      });\r\n\r\n      it('should throw error for invalid status', () => {\r\n        const invalidData = { ...validReconstitutionData, status: 'invalid' as AnalysisStatus };\r\n\r\n        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Invalid analysis status');\r\n      });\r\n\r\n      it('should throw error for missing input data', () => {\r\n        const invalidData = { ...validReconstitutionData, inputData: null as any };\r\n\r\n        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Input data is required');\r\n      });\r\n\r\n      it('should throw error for missing metadata', () => {\r\n        const invalidData = { ...validReconstitutionData, metadata: null as any };\r\n\r\n        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Metadata is required');\r\n      });\r\n\r\n      it('should throw error for invalid confidence', () => {\r\n        const invalidData = { ...validReconstitutionData, confidence: 1.5 };\r\n\r\n        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Confidence must be between 0 and 1');\r\n      });\r\n\r\n      it('should throw error for negative processing time', () => {\r\n        const invalidData = { ...validReconstitutionData, processingTime: -100 };\r\n\r\n        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Processing time cannot be negative');\r\n      });\r\n\r\n      it('should throw error for invalid tags', () => {\r\n        const invalidData = { ...validReconstitutionData, tags: 'not-array' as any };\r\n\r\n        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Tags must be an array');\r\n      });\r\n\r\n      it('should throw error for invalid child analysis IDs', () => {\r\n        const invalidData = { ...validReconstitutionData, childAnalysisIds: 'not-array' as any };\r\n\r\n        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Child analysis IDs must be an array');\r\n      });\r\n\r\n      it('should throw error for invalid creation date', () => {\r\n        const invalidData = { ...validReconstitutionData, createdAt: 'invalid' as any };\r\n\r\n        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Valid creation date is required');\r\n      });\r\n\r\n      it('should throw error for invalid update date', () => {\r\n        const invalidData = { ...validReconstitutionData, updatedAt: 'invalid' as any };\r\n\r\n        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Valid update date is required');\r\n      });\r\n\r\n      it('should throw error for invalid completed date', () => {\r\n        const invalidData = { ...validReconstitutionData, completedAt: 'invalid' as any };\r\n\r\n        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Completed date must be a valid Date if provided');\r\n      });\r\n\r\n      it('should throw error for completed status without output data', () => {\r\n        const invalidData = { \r\n          ...validReconstitutionData, \r\n          status: AnalysisStatus.COMPLETED,\r\n          outputData: null as any \r\n        };\r\n\r\n        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Output data is required for completed analysis');\r\n      });\r\n\r\n      it('should throw error for failed status without error details', () => {\r\n        const invalidData = { \r\n          ...validReconstitutionData, \r\n          status: AnalysisStatus.FAILED,\r\n          errorDetails: undefined \r\n        };\r\n\r\n        expect(() => AnalysisResultFactory.reconstitute(invalidData)).toThrow('Error details are required for failed analysis');\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('createForTesting', () => {\r\n    it('should create a test analysis result with default values', () => {\r\n      const result = AnalysisResultFactory.createForTesting();\r\n\r\n      expect(result.requestId).toBe('test-request-123');\r\n      expect(result.analysisType).toBe(AnalysisType.CLASSIFICATION);\r\n      expect(result.status).toBe(AnalysisStatus.PENDING);\r\n      expect(result.tags).toEqual(['test']);\r\n      expect(result.correlationId).toBe('test-correlation-123');\r\n      expect(result.confidence).toBe(0);\r\n      expect(result.processingTime).toBe(0);\r\n    });\r\n\r\n    it('should create a test analysis result with overrides', () => {\r\n      const overrides = {\r\n        requestId: 'custom-request',\r\n        analysisType: AnalysisType.ANOMALY_DETECTION,\r\n        tags: ['custom', 'test'],\r\n      };\r\n\r\n      const result = AnalysisResultFactory.createForTesting(overrides);\r\n\r\n      expect(result.requestId).toBe('custom-request');\r\n      expect(result.analysisType).toBe(AnalysisType.ANOMALY_DETECTION);\r\n      expect(result.tags).toEqual(['custom', 'test']);\r\n      expect(result.correlationId).toBe('test-correlation-123'); // Default value preserved\r\n    });\r\n  });\r\n\r\n  describe('createCompletedForTesting', () => {\r\n    it('should create a completed test analysis result', () => {\r\n      const result = AnalysisResultFactory.createCompletedForTesting();\r\n\r\n      expect(result.status).toBe(AnalysisStatus.COMPLETED);\r\n      expect(result.confidence).toBe(0.95);\r\n      expect(result.processingTime).toBe(1500);\r\n      expect(result.outputData).toBeDefined();\r\n      expect(result.completedAt).toBeInstanceOf(Date);\r\n      expect(result.isSuccessful()).toBe(true);\r\n    });\r\n\r\n    it('should create a completed test analysis result with overrides', () => {\r\n      const overrides = {\r\n        requestId: 'completed-request',\r\n        analysisType: AnalysisType.NLP_ANALYSIS,\r\n      };\r\n\r\n      const result = AnalysisResultFactory.createCompletedForTesting(overrides);\r\n\r\n      expect(result.requestId).toBe('completed-request');\r\n      expect(result.analysisType).toBe(AnalysisType.NLP_ANALYSIS);\r\n      expect(result.status).toBe(AnalysisStatus.COMPLETED);\r\n    });\r\n  });\r\n\r\n  describe('createFailedForTesting', () => {\r\n    it('should create a failed test analysis result', () => {\r\n      const result = AnalysisResultFactory.createFailedForTesting();\r\n\r\n      expect(result.status).toBe(AnalysisStatus.FAILED);\r\n      expect(result.errorDetails).toBeDefined();\r\n      expect(result.errorDetails!.code).toBe('TEST_ERROR');\r\n      expect(result.errorDetails!.retryable).toBe(true);\r\n      expect(result.completedAt).toBeInstanceOf(Date);\r\n      expect(result.isSuccessful()).toBe(false);\r\n      expect(result.isRetryable()).toBe(true);\r\n    });\r\n\r\n    it('should create a failed test analysis result with overrides', () => {\r\n      const overrides = {\r\n        requestId: 'failed-request',\r\n        analysisType: AnalysisType.THREAT_DETECTION,\r\n      };\r\n\r\n      const result = AnalysisResultFactory.createFailedForTesting(overrides);\r\n\r\n      expect(result.requestId).toBe('failed-request');\r\n      expect(result.analysisType).toBe(AnalysisType.THREAT_DETECTION);\r\n      expect(result.status).toBe(AnalysisStatus.FAILED);\r\n    });\r\n  });\r\n});"], "version": 3}