{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\orchestration\\__tests__\\ai-orchestration.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,2CAA+C;AAC/C,uCAA8D;AAC9D,0EAAqE;AACrE,wEAAmE;AACnE,oEAA+D;AAC/D,sFAAiF;AACjF,qEAAgE;AAChE,yFAAqF;AACrF,yGAGiE;AACjE,yGAGiE;AAEjE,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;IACtC,IAAI,OAA+B,CAAC;IACpC,IAAI,mBAAiD,CAAC;IACtD,IAAI,oBAAmD,CAAC;IACxD,IAAI,yBAA6D,CAAC;IAClE,IAAI,uBAAyD,CAAC;IAC9D,IAAI,yBAA6D,CAAC;IAClE,IAAI,gBAA6C,CAAC;IAClD,IAAI,kBAA+C,CAAC;IACpD,IAAI,YAAmC,CAAC;IACxC,IAAI,cAAuC,CAAC;IAC5C,IAAI,YAAmC,CAAC;IACxC,IAAI,iBAA6C,CAAC;IAElD,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,eAAe;QACf,mBAAmB,GAAG;YACpB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;SACZ,CAAC;QAET,oBAAoB,GAAG;YACrB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;SACZ,CAAC;QAET,yBAAyB,GAAG;YAC1B,kBAAkB,EAAE,IAAI,CAAC,EAAE,EAAE;YAC7B,mBAAmB,EAAE,IAAI,CAAC,EAAE,EAAE;SACxB,CAAC;QAET,uBAAuB,GAAG;YACxB,qBAAqB,EAAE,IAAI,CAAC,EAAE,EAAE;YAChC,mBAAmB,EAAE,IAAI,CAAC,EAAE,EAAE;SACxB,CAAC;QAET,yBAAyB,GAAG;YAC1B,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;YAClB,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;SACd,CAAC;QAET,gBAAgB,GAAG;YACjB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;YACd,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC;YAC3C,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;SAChB,CAAC;QAET,kBAAkB,GAAG;YACnB,iBAAiB,EAAE,IAAI,CAAC,EAAE,EAAE;SACtB,CAAC;QAET,YAAY,GAAG;YACb,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;SACZ,CAAC;QAET,cAAc,GAAG;YACf,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;SACZ,CAAC;QAET,YAAY,GAAG;YACb,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;SACZ,CAAC;QAET,iBAAiB,GAAG;YAClB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;SACR,CAAC;QAET,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,iDAAsB;gBACtB;oBACE,OAAO,EAAE,+CAAiB;oBAC1B,QAAQ,EAAE,mBAAmB;iBAC9B;gBACD;oBACE,OAAO,EAAE,+CAAiB;oBAC1B,QAAQ,EAAE,oBAAoB;iBAC/B;gBACD;oBACE,OAAO,EAAE,+CAAqB;oBAC9B,QAAQ,EAAE,yBAAyB;iBACpC;gBACD;oBACE,OAAO,EAAE,2CAAmB;oBAC5B,QAAQ,EAAE,uBAAuB;iBAClC;gBACD;oBACE,OAAO,EAAE,+CAAqB;oBAC9B,QAAQ,EAAE,yBAAyB;iBACpC;gBACD;oBACE,OAAO,EAAE,iCAAc;oBACvB,QAAQ,EAAE,gBAAgB;iBAC3B;gBACD;oBACE,OAAO,EAAE,gCAAc;oBACvB,QAAQ,EAAE,kBAAkB;iBAC7B;gBACD;oBACE,OAAO,EAAE,eAAQ;oBACjB,QAAQ,EAAE,YAAY;iBACvB;gBACD;oBACE,OAAO,EAAE,iBAAU;oBACnB,QAAQ,EAAE,cAAc;iBACzB;gBACD;oBACE,OAAO,EAAE,eAAQ;oBACjB,QAAQ,EAAE,YAAY;iBACvB;gBACD;oBACE,OAAO,EAAE,sBAAa;oBACtB,QAAQ,EAAE,iBAAiB;iBAC5B;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAAyB,iDAAsB,CAAC,CAAC;IACvE,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,MAAM,WAAW,GAAG;YAClB,EAAE,EAAE,gBAAgB;YACpB,IAAI,EAAE,iBAAiB;YACvB,IAAI,EAAE,EAAE,OAAO,EAAE,WAAW,EAAE;YAC9B,QAAQ,EAAE,MAAe;SAC1B,CAAC;QAEF,MAAM,eAAe,GAAG;YACtB,YAAY,EAAE,QAAQ;YACtB,OAAO,EAAE,OAAO;YAChB,UAAU,EAAE,EAAE;SACf,CAAC;QAEF,MAAM,aAAa,GAAG;YACpB;gBACE,EAAE,EAAE,YAAY;gBAChB,IAAI,EAAE,QAAQ;gBACd,MAAM,EAAE,SAAS;gBACjB,IAAI,EAAE,GAAG;aACV;SACF,CAAC;QAEF,MAAM,kBAAkB,GAAG;YACzB,EAAE,EAAE,UAAU;YACd,MAAM,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE;YACnD,UAAU,EAAE,IAAI;YAChB,QAAQ,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE;YAC5B,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,EAAE,CAAC,0DAA0D,EAAE,KAAK,IAAI,EAAE;YACxE,UAAU;YACV,gBAAgB,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAC7C,yBAAyB,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAChF,uBAAuB,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAC/E,yBAAyB,CAAC,OAAO,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;YACxE,gBAAgB,CAAC,GAAG,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAElD,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;YAE9D,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAC3C,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;YACtE,MAAM,CAAC,yBAAyB,CAAC,kBAAkB,CAAC,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;YACvF,MAAM,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YACrF,MAAM,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,gBAAgB,EAAE,CAAC;YAC7D,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAC/C,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAClB,kBAAkB,EAClB,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;YACF,MAAM,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,CAAC,oBAAoB,CAC/D,SAAS,EACT,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAClB,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,UAAU;YACV,gBAAgB,CAAC,GAAG,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;YAE3D,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;YAE9D,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAC3C,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;YACtE,MAAM,CAAC,yBAAyB,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;YAC5E,MAAM,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,CAAC,oBAAoB,CAC/D,WAAW,EACX,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAClB,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC7D,UAAU;YACV,MAAM,iBAAiB,GAAG;gBACxB,EAAE,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,EAAE;gBAClE,EAAE,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,EAAE;aACnE,CAAC;YAEF,gBAAgB,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAC7C,yBAAyB,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAChF,uBAAuB,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YAEnF,wCAAwC;YACxC,yBAAyB,CAAC,OAAO;iBAC9B,qBAAqB,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;iBACrD,qBAAqB,CAAC,kBAAkB,CAAC,CAAC;YAE7C,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;YAE9D,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAC3C,MAAM,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,UAAU;YACV,gBAAgB,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAC7C,yBAAyB,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAChF,uBAAuB,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAC/E,yBAAyB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC;YAEvF,eAAe;YACf,MAAM,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YAC1F,MAAM,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,CAAC,oBAAoB,CAC/D,OAAO,EACP,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAClB,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,UAAU;YACV,iBAAiB,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,oBAAoB;YACjE,gBAAgB,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAC7C,yBAAyB,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAChF,uBAAuB,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAC/E,yBAAyB,CAAC,OAAO,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;YAExE,MAAM;YACN,MAAM,OAAO,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;YAE/C,SAAS;YACT,MAAM,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;YACxE,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAC/C,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAClB,kBAAkB,EAClB,IAAI,CACL,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACxC,MAAM,YAAY,GAAG;YACnB;gBACE,EAAE,EAAE,iBAAiB;gBACrB,IAAI,EAAE,iBAAiB;gBACvB,IAAI,EAAE,EAAE,OAAO,EAAE,aAAa,EAAE;aACjC;YACD;gBACE,EAAE,EAAE,iBAAiB;gBACrB,IAAI,EAAE,iBAAiB;gBACvB,IAAI,EAAE,EAAE,OAAO,EAAE,aAAa,EAAE;aACjC;SACF,CAAC;QAEF,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,UAAU;YACV,iBAAiB,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB;YAE9D,4CAA4C;YAC5C,yBAAyB,CAAC,kBAAkB,CAAC,iBAAiB,CAAC;gBAC7D,YAAY,EAAE,QAAQ;gBACtB,OAAO,EAAE,OAAO;gBAChB,UAAU,EAAE,EAAE;aACf,CAAC,CAAC;YAEH,iCAAiC;YACjC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,qBAAqB,CAAC;iBACvC,qBAAqB,CAAC;gBACrB,EAAE,EAAE,UAAU;gBACd,MAAM,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE;gBACjC,UAAU,EAAE,GAAG;gBACf,QAAQ,EAAE,EAAE;gBACZ,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;iBACD,qBAAqB,CAAC;gBACrB,EAAE,EAAE,UAAU;gBACd,MAAM,EAAE,EAAE,eAAe,EAAE,KAAK,EAAE;gBAClC,UAAU,EAAE,GAAG;gBACf,QAAQ,EAAE,EAAE;gBACZ,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEL,MAAM;YACN,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;YAErE,SAAS;YACT,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACvC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACvC,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,UAAU;YACV,qFAAqF;YACrF,yBAAyB,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC,CAAC;YAEpG,MAAM;YACN,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;YAErE,SAAS;YACT,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,mEAAmE;QACtG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE,GAAG,EAAE;QAC7C,MAAM,qBAAqB,GAAG;YAC5B,OAAO,EAAE,SAAS;YAClB,KAAK,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE;YAC7B,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;SAC3B,CAAC;QAEF,MAAM,oBAAoB,GAAG;YAC3B,UAAU,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE;YAC7C,UAAU,EAAE,IAAI;YAChB,QAAQ,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE;SAC/B,CAAC;QAEF,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;YACpE,UAAU;YACV,MAAM,eAAe,GAAG;gBACtB,OAAO,EAAE,SAAS;gBAClB,YAAY,EAAE,QAAQ;gBACtB,UAAU,EAAE,EAAE;aACf,CAAC;YAEF,yBAAyB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YACjF,oBAAoB,CAAC,OAAO,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;YACrE,iBAAiB,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAE5C,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,6BAA6B,CAAC,qBAAqB,CAAC,CAAC;YAElF,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;YAC7C,MAAM,CAAC,yBAAyB,CAAC,mBAAmB,CAAC,CAAC,oBAAoB,CACxE,qBAAqB,CACtB,CAAC;YACF,MAAM,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,oBAAoB,CACvD,qBAAqB,EACrB,MAAM,CAAC,gBAAgB,CAAC;gBACtB,WAAW,EAAE,eAAe;gBAC5B,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,MAAM;aACjB,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,UAAU;YACV,yBAAyB,CAAC,mBAAmB,CAAC,iBAAiB,CAC7D,IAAI,KAAK,CAAC,wBAAwB,CAAC,CACpC,CAAC;YAEF,eAAe;YACf,MAAM,MAAM,CAAC,OAAO,CAAC,6BAA6B,CAAC,qBAAqB,CAAC,CAAC;iBACvE,OAAO,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,8DAA8D,EAAE,KAAK,IAAI,EAAE;YAC5E,UAAU;YACV,MAAM,kBAAkB,GAAG,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC;YAC/D,MAAM,eAAe,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;YAC9C,MAAM,wBAAwB,GAAG,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,CAAC;YAEjE,uBAAuB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;YAClF,gBAAgB,CAAC,WAAW,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAChE,yBAAyB,CAAC,SAAS,CAAC,eAAe,CAAC,wBAAwB,CAAC,CAAC;YAE9E,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,WAAW,EAAE,CAAC;YAE3C,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;YACjE,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;YACtE,UAAU;YACV,uBAAuB,CAAC,mBAAmB,CAAC,iBAAiB,CAC3D,IAAI,KAAK,CAAC,qBAAqB,CAAC,CACjC,CAAC;YAEF,MAAM;YACN,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,WAAW,EAAE,CAAC;YAE3C,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,sDAAsD;YACtD,MAAM,iBAAiB,GAAI,OAAe,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAE3E,MAAM,GAAG,GAAG,iBAAiB,EAAE,CAAC;YAChC,MAAM,GAAG,GAAG,iBAAiB,EAAE,CAAC;YAEhC,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;YAC9C,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;YAC9C,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,gBAAgB,GAAI,OAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAEzE,MAAM,QAAQ,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,CAAC;YACpE,MAAM,QAAQ,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC,2BAA2B;YAChG,MAAM,QAAQ,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,EAAE,CAAC;YAEzE,MAAM,IAAI,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YACxC,MAAM,IAAI,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YACxC,MAAM,IAAI,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAExC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,wCAAwC;YACjE,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,kDAAkD;QACjF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,4DAA4D,EAAE,KAAK,IAAI,EAAE;YAC1E,UAAU;YACV,MAAM,WAAW,GAAG;gBAClB,EAAE,EAAE,eAAe;gBACnB,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,EAAE;aACT,CAAC;YAEF,gBAAgB,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;YAEjE,eAAe;YACf,MAAM,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACzE,MAAM,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,CAAC,oBAAoB,CAC/D,OAAO,EACP,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAClB,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\orchestration\\__tests__\\ai-orchestration.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { EventBus, CommandBus, QueryBus } from '@nestjs/cqrs';\r\nimport { AiOrchestrationService } from '../ai-orchestration.service';\r\nimport { ModelSelectionService } from '../model-selection.service';\r\nimport { LoadBalancerService } from '../load-balancer.service';\r\nimport { CircuitBreakerService } from '../../resilience/circuit-breaker.service';\r\nimport { AiCacheService } from '../../caching/ai-cache.service';\r\nimport { MetricsAdapter } from '../../../../infrastructure/adapters/metrics.adapter';\r\nimport { \r\n  AI_MODEL_PROVIDER,\r\n  AiModelProvider \r\n} from '../../../../domain/services/ai-model-provider.interface';\r\nimport { \r\n  PREDICTION_ENGINE,\r\n  PredictionEngine \r\n} from '../../../../domain/services/prediction-engine.interface';\r\n\r\ndescribe('AiOrchestrationService', () => {\r\n  let service: AiOrchestrationService;\r\n  let mockAiModelProvider: jest.Mocked<AiModelProvider>;\r\n  let mockPredictionEngine: jest.Mocked<PredictionEngine>;\r\n  let mockModelSelectionService: jest.Mocked<ModelSelectionService>;\r\n  let mockLoadBalancerService: jest.Mocked<LoadBalancerService>;\r\n  let mockCircuitBreakerService: jest.Mocked<CircuitBreakerService>;\r\n  let mockCacheService: jest.Mocked<AiCacheService>;\r\n  let mockMetricsAdapter: jest.Mocked<MetricsAdapter>;\r\n  let mockEventBus: jest.Mocked<EventBus>;\r\n  let mockCommandBus: jest.Mocked<CommandBus>;\r\n  let mockQueryBus: jest.Mocked<QueryBus>;\r\n  let mockConfigService: jest.Mocked<ConfigService>;\r\n\r\n  beforeEach(async () => {\r\n    // Create mocks\r\n    mockAiModelProvider = {\r\n      analyze: jest.fn(),\r\n    } as any;\r\n\r\n    mockPredictionEngine = {\r\n      predict: jest.fn(),\r\n    } as any;\r\n\r\n    mockModelSelectionService = {\r\n      selectOptimalModel: jest.fn(),\r\n      selectRealTimeModel: jest.fn(),\r\n    } as any;\r\n\r\n    mockLoadBalancerService = {\r\n      getAvailableProviders: jest.fn(),\r\n      checkProviderHealth: jest.fn(),\r\n    } as any;\r\n\r\n    mockCircuitBreakerService = {\r\n      execute: jest.fn(),\r\n      getStatus: jest.fn(),\r\n    } as any;\r\n\r\n    mockCacheService = {\r\n      get: jest.fn(),\r\n      set: jest.fn().mockResolvedValue(undefined),\r\n      checkHealth: jest.fn(),\r\n    } as any;\r\n\r\n    mockMetricsAdapter = {\r\n      recordAiOperation: jest.fn(),\r\n    } as any;\r\n\r\n    mockEventBus = {\r\n      publish: jest.fn(),\r\n    } as any;\r\n\r\n    mockCommandBus = {\r\n      execute: jest.fn(),\r\n    } as any;\r\n\r\n    mockQueryBus = {\r\n      execute: jest.fn(),\r\n    } as any;\r\n\r\n    mockConfigService = {\r\n      get: jest.fn(),\r\n    } as any;\r\n\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        AiOrchestrationService,\r\n        {\r\n          provide: AI_MODEL_PROVIDER,\r\n          useValue: mockAiModelProvider,\r\n        },\r\n        {\r\n          provide: PREDICTION_ENGINE,\r\n          useValue: mockPredictionEngine,\r\n        },\r\n        {\r\n          provide: ModelSelectionService,\r\n          useValue: mockModelSelectionService,\r\n        },\r\n        {\r\n          provide: LoadBalancerService,\r\n          useValue: mockLoadBalancerService,\r\n        },\r\n        {\r\n          provide: CircuitBreakerService,\r\n          useValue: mockCircuitBreakerService,\r\n        },\r\n        {\r\n          provide: AiCacheService,\r\n          useValue: mockCacheService,\r\n        },\r\n        {\r\n          provide: MetricsAdapter,\r\n          useValue: mockMetricsAdapter,\r\n        },\r\n        {\r\n          provide: EventBus,\r\n          useValue: mockEventBus,\r\n        },\r\n        {\r\n          provide: CommandBus,\r\n          useValue: mockCommandBus,\r\n        },\r\n        {\r\n          provide: QueryBus,\r\n          useValue: mockQueryBus,\r\n        },\r\n        {\r\n          provide: ConfigService,\r\n          useValue: mockConfigService,\r\n        },\r\n      ],\r\n    }).compile();\r\n\r\n    service = module.get<AiOrchestrationService>(AiOrchestrationService);\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('orchestrateAnalysis', () => {\r\n    const mockRequest = {\r\n      id: 'test-request-1',\r\n      type: 'threat-analysis',\r\n      data: { content: 'test data' },\r\n      priority: 'high' as const,\r\n    };\r\n\r\n    const mockModelConfig = {\r\n      providerType: 'openai',\r\n      modelId: 'gpt-4',\r\n      parameters: {},\r\n    };\r\n\r\n    const mockProviders = [\r\n      {\r\n        id: 'provider-1',\r\n        type: 'openai',\r\n        status: 'healthy',\r\n        load: 0.5,\r\n      },\r\n    ];\r\n\r\n    const mockAnalysisResult = {\r\n      id: 'result-1',\r\n      result: { threat_detected: true, confidence: 0.95 },\r\n      confidence: 0.95,\r\n      metadata: { model: 'gpt-4' },\r\n      timestamp: new Date(),\r\n    };\r\n\r\n    it('should orchestrate analysis successfully with cache miss', async () => {\r\n      // Arrange\r\n      mockCacheService.get.mockResolvedValue(null);\r\n      mockModelSelectionService.selectOptimalModel.mockResolvedValue(mockModelConfig);\r\n      mockLoadBalancerService.getAvailableProviders.mockResolvedValue(mockProviders);\r\n      mockCircuitBreakerService.execute.mockResolvedValue(mockAnalysisResult);\r\n      mockCacheService.set.mockResolvedValue(undefined);\r\n\r\n      // Act\r\n      const result = await service.orchestrateAnalysis(mockRequest);\r\n\r\n      // Assert\r\n      expect(result).toEqual(mockAnalysisResult);\r\n      expect(mockCacheService.get).toHaveBeenCalledWith(expect.any(String));\r\n      expect(mockModelSelectionService.selectOptimalModel).toHaveBeenCalledWith(mockRequest);\r\n      expect(mockLoadBalancerService.getAvailableProviders).toHaveBeenCalledWith('openai');\r\n      expect(mockCircuitBreakerService.execute).toHaveBeenCalled();\r\n      expect(mockCacheService.set).toHaveBeenCalledWith(\r\n        expect.any(String),\r\n        mockAnalysisResult,\r\n        expect.any(Number)\r\n      );\r\n      expect(mockMetricsAdapter.recordAiOperation).toHaveBeenCalledWith(\r\n        'success',\r\n        expect.any(Number),\r\n        expect.any(String)\r\n      );\r\n    });\r\n\r\n    it('should return cached result when available', async () => {\r\n      // Arrange\r\n      mockCacheService.get.mockResolvedValue(mockAnalysisResult);\r\n\r\n      // Act\r\n      const result = await service.orchestrateAnalysis(mockRequest);\r\n\r\n      // Assert\r\n      expect(result).toEqual(mockAnalysisResult);\r\n      expect(mockCacheService.get).toHaveBeenCalledWith(expect.any(String));\r\n      expect(mockModelSelectionService.selectOptimalModel).not.toHaveBeenCalled();\r\n      expect(mockMetricsAdapter.recordAiOperation).toHaveBeenCalledWith(\r\n        'cache_hit',\r\n        expect.any(Number),\r\n        expect.any(String)\r\n      );\r\n    });\r\n\r\n    it('should handle provider failures with fallback', async () => {\r\n      // Arrange\r\n      const multipleProviders = [\r\n        { id: 'provider-1', type: 'openai', status: 'healthy', load: 0.5 },\r\n        { id: 'provider-2', type: 'openai', status: 'healthy', load: 0.3 },\r\n      ];\r\n\r\n      mockCacheService.get.mockResolvedValue(null);\r\n      mockModelSelectionService.selectOptimalModel.mockResolvedValue(mockModelConfig);\r\n      mockLoadBalancerService.getAvailableProviders.mockResolvedValue(multipleProviders);\r\n      \r\n      // First provider fails, second succeeds\r\n      mockCircuitBreakerService.execute\r\n        .mockRejectedValueOnce(new Error('Provider 1 failed'))\r\n        .mockResolvedValueOnce(mockAnalysisResult);\r\n\r\n      // Act\r\n      const result = await service.orchestrateAnalysis(mockRequest);\r\n\r\n      // Assert\r\n      expect(result).toEqual(mockAnalysisResult);\r\n      expect(mockCircuitBreakerService.execute).toHaveBeenCalledTimes(2);\r\n    });\r\n\r\n    it('should throw error when all providers fail', async () => {\r\n      // Arrange\r\n      mockCacheService.get.mockResolvedValue(null);\r\n      mockModelSelectionService.selectOptimalModel.mockResolvedValue(mockModelConfig);\r\n      mockLoadBalancerService.getAvailableProviders.mockResolvedValue(mockProviders);\r\n      mockCircuitBreakerService.execute.mockRejectedValue(new Error('All providers failed'));\r\n\r\n      // Act & Assert\r\n      await expect(service.orchestrateAnalysis(mockRequest)).rejects.toThrow('Analysis failed');\r\n      expect(mockMetricsAdapter.recordAiOperation).toHaveBeenCalledWith(\r\n        'error',\r\n        expect.any(Number),\r\n        expect.any(String)\r\n      );\r\n    });\r\n\r\n    it('should handle configuration values correctly', async () => {\r\n      // Arrange\r\n      mockConfigService.get.mockReturnValue(7200); // 2 hours cache TTL\r\n      mockCacheService.get.mockResolvedValue(null);\r\n      mockModelSelectionService.selectOptimalModel.mockResolvedValue(mockModelConfig);\r\n      mockLoadBalancerService.getAvailableProviders.mockResolvedValue(mockProviders);\r\n      mockCircuitBreakerService.execute.mockResolvedValue(mockAnalysisResult);\r\n\r\n      // Act\r\n      await service.orchestrateAnalysis(mockRequest);\r\n\r\n      // Assert\r\n      expect(mockConfigService.get).toHaveBeenCalledWith('ai.cacheTtl', 3600);\r\n      expect(mockCacheService.set).toHaveBeenCalledWith(\r\n        expect.any(String),\r\n        mockAnalysisResult,\r\n        7200\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('orchestrateBatchAnalysis', () => {\r\n    const mockRequests = [\r\n      {\r\n        id: 'batch-request-1',\r\n        type: 'threat-analysis',\r\n        data: { content: 'test data 1' },\r\n      },\r\n      {\r\n        id: 'batch-request-2',\r\n        type: 'threat-analysis',\r\n        data: { content: 'test data 2' },\r\n      },\r\n    ];\r\n\r\n    it('should process batch requests successfully', async () => {\r\n      // Arrange\r\n      mockConfigService.get.mockReturnValue(2); // Concurrency limit\r\n      \r\n      // Mock model selection for batch processing\r\n      mockModelSelectionService.selectOptimalModel.mockResolvedValue({\r\n        providerType: 'openai',\r\n        modelId: 'gpt-4',\r\n        parameters: {},\r\n      });\r\n      \r\n      // Mock individual analysis calls\r\n      jest.spyOn(service, 'orchestrateAnalysis')\r\n        .mockResolvedValueOnce({\r\n          id: 'result-1',\r\n          result: { threat_detected: true },\r\n          confidence: 0.9,\r\n          metadata: {},\r\n          timestamp: new Date(),\r\n        })\r\n        .mockResolvedValueOnce({\r\n          id: 'result-2',\r\n          result: { threat_detected: false },\r\n          confidence: 0.8,\r\n          metadata: {},\r\n          timestamp: new Date(),\r\n        });\r\n\r\n      // Act\r\n      const results = await service.orchestrateBatchAnalysis(mockRequests);\r\n\r\n      // Assert\r\n      expect(results).toHaveLength(2);\r\n      expect(results[0].id).toBe('result-1');\r\n      expect(results[1].id).toBe('result-2');\r\n      expect(service.orchestrateAnalysis).toHaveBeenCalledTimes(2);\r\n    });\r\n\r\n    it('should handle batch processing errors gracefully', async () => {\r\n      // Arrange\r\n      // Mock model selection to fail, which should cause the batch to return empty results\r\n      mockModelSelectionService.selectOptimalModel.mockRejectedValue(new Error('Model selection failed'));\r\n\r\n      // Act\r\n      const results = await service.orchestrateBatchAnalysis(mockRequests);\r\n\r\n      // Assert\r\n      expect(results).toHaveLength(0); // Should return empty array when all requests fail model selection\r\n    });\r\n  });\r\n\r\n  describe('orchestrateRealTimePrediction', () => {\r\n    const mockPredictionRequest = {\r\n      modelId: 'model-1',\r\n      input: { data: 'test input' },\r\n      options: { timeout: 1000 },\r\n    };\r\n\r\n    const mockPredictionResult = {\r\n      prediction: { category: 'safe', score: 0.95 },\r\n      confidence: 0.95,\r\n      metadata: { model: 'model-1' },\r\n    };\r\n\r\n    it('should orchestrate real-time prediction successfully', async () => {\r\n      // Arrange\r\n      const mockModelConfig = {\r\n        modelId: 'model-1',\r\n        providerType: 'openai',\r\n        parameters: {},\r\n      };\r\n\r\n      mockModelSelectionService.selectRealTimeModel.mockResolvedValue(mockModelConfig);\r\n      mockPredictionEngine.predict.mockResolvedValue(mockPredictionResult);\r\n      mockConfigService.get.mockReturnValue(1000);\r\n\r\n      // Act\r\n      const result = await service.orchestrateRealTimePrediction(mockPredictionRequest);\r\n\r\n      // Assert\r\n      expect(result).toEqual(mockPredictionResult);\r\n      expect(mockModelSelectionService.selectRealTimeModel).toHaveBeenCalledWith(\r\n        mockPredictionRequest\r\n      );\r\n      expect(mockPredictionEngine.predict).toHaveBeenCalledWith(\r\n        mockPredictionRequest,\r\n        expect.objectContaining({\r\n          modelConfig: mockModelConfig,\r\n          timeout: 1000,\r\n          priority: 'high',\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should handle real-time prediction failures', async () => {\r\n      // Arrange\r\n      mockModelSelectionService.selectRealTimeModel.mockRejectedValue(\r\n        new Error('Model selection failed')\r\n      );\r\n\r\n      // Act & Assert\r\n      await expect(service.orchestrateRealTimePrediction(mockPredictionRequest))\r\n        .rejects.toThrow('Prediction failed');\r\n    });\r\n  });\r\n\r\n  describe('checkHealth', () => {\r\n    it('should return healthy status when all components are healthy', async () => {\r\n      // Arrange\r\n      const mockProviderHealth = { 'provider-1': { healthy: true } };\r\n      const mockCacheHealth = { status: 'healthy' };\r\n      const mockCircuitBreakerStatus = { 'cb-1': { state: 'closed' } };\r\n\r\n      mockLoadBalancerService.checkProviderHealth.mockResolvedValue(mockProviderHealth);\r\n      mockCacheService.checkHealth.mockResolvedValue(mockCacheHealth);\r\n      mockCircuitBreakerService.getStatus.mockReturnValue(mockCircuitBreakerStatus);\r\n\r\n      // Act\r\n      const health = await service.checkHealth();\r\n\r\n      // Assert\r\n      expect(health.status).toBe('healthy');\r\n      expect(health.providers).toEqual(mockProviderHealth);\r\n      expect(health.cache).toEqual(mockCacheHealth);\r\n      expect(health.circuitBreakers).toEqual(mockCircuitBreakerStatus);\r\n      expect(health.timestamp).toBeInstanceOf(Date);\r\n    });\r\n\r\n    it('should return unhealthy status when health check fails', async () => {\r\n      // Arrange\r\n      mockLoadBalancerService.checkProviderHealth.mockRejectedValue(\r\n        new Error('Health check failed')\r\n      );\r\n\r\n      // Act\r\n      const health = await service.checkHealth();\r\n\r\n      // Assert\r\n      expect(health.status).toBe('unhealthy');\r\n      expect(health.error).toBe('Health check failed');\r\n      expect(health.timestamp).toBeInstanceOf(Date);\r\n    });\r\n  });\r\n\r\n  describe('private helper methods', () => {\r\n    it('should generate unique request IDs', () => {\r\n      // Use reflection to access private method for testing\r\n      const generateRequestId = (service as any).generateRequestId.bind(service);\r\n      \r\n      const id1 = generateRequestId();\r\n      const id2 = generateRequestId();\r\n      \r\n      expect(id1).toMatch(/^ai-req-\\d+-[a-z0-9]+$/);\r\n      expect(id2).toMatch(/^ai-req-\\d+-[a-z0-9]+$/);\r\n      expect(id1).not.toBe(id2);\r\n    });\r\n\r\n    it('should generate cache keys consistently', () => {\r\n      const generateCacheKey = (service as any).generateCacheKey.bind(service);\r\n      \r\n      const request1 = { id: '1', type: 'test', data: { value: 'same' } };\r\n      const request2 = { id: '1', type: 'test', data: { value: 'same' } }; // Same ID for same content\r\n      const request3 = { id: '1', type: 'test', data: { value: 'different' } };\r\n      \r\n      const key1 = generateCacheKey(request1);\r\n      const key2 = generateCacheKey(request2);\r\n      const key3 = generateCacheKey(request3);\r\n      \r\n      expect(key1).toBe(key2); // Same content should generate same key\r\n      expect(key1).not.toBe(key3); // Different content should generate different key\r\n    });\r\n  });\r\n\r\n  describe('error handling', () => {\r\n    it('should handle orchestration errors with proper error types', async () => {\r\n      // Arrange\r\n      const mockRequest = {\r\n        id: 'error-request',\r\n        type: 'test',\r\n        data: {},\r\n      };\r\n\r\n      mockCacheService.get.mockRejectedValue(new Error('Cache error'));\r\n\r\n      // Act & Assert\r\n      await expect(service.orchestrateAnalysis(mockRequest)).rejects.toThrow();\r\n      expect(mockMetricsAdapter.recordAiOperation).toHaveBeenCalledWith(\r\n        'error',\r\n        expect.any(Number),\r\n        expect.any(String)\r\n      );\r\n    });\r\n  });\r\n});\r\n"], "version": 3}