import { Injectable } from '@nestjs/common';
import { EnrichedEvent } from '../../domain/entities/event/enriched-event.entity';
import { CorrelatedEvent, CorrelationType, CorrelationAnalysis } from '../../domain/entities/event/correlated-event.entity';
import { IOC } from '../../domain/value-objects/threat-indicators/ioc.value-object';

import { DomainEventPublisher } from '../../../../shared-kernel/domain/domain-event-publisher';
import { Logger } from '../../../../shared-kernel/infrastructure/logger';

/**
 * Correlation Configuration
 */
export interface CorrelationConfig {
  /** Time window for temporal correlation (minutes) */
  temporalWindow: number;
  /** Minimum events required for correlation */
  minEvents: number;
  /** Minimum confidence threshold */
  minConfidence: number;
  /** Maximum events to correlate */
  maxEvents: number;
  /** Enable different correlation types */
  enabledTypes: CorrelationType[];
  /** Correlation algorithms to use */
  algorithms: string[];
}

/**
 * Correlation Request
 */
export interface CorrelationRequest {
  /** Events to correlate */
  events: EnrichedEvent[];
  /** Correlation configuration */
  config?: Partial<CorrelationConfig>;
  /** Time window override */
  timeWindow?: number;
  /** Focus on specific correlation types */
  focusTypes?: CorrelationType[];
}

/**
 * Correlation Result
 */
export interface CorrelationResult {
  /** Correlation success */
  success: boolean;
  /** Correlated events found */
  correlatedEvents: CorrelatedEvent[];
  /** Processing duration */
  duration: number;
  /** Correlation statistics */
  statistics: {
    eventsProcessed: number;
    correlationsFound: number;
    averageConfidence: number;
    typeDistribution: Record<CorrelationType, number>;
  };
  /** Processing errors */
  errors: Array<{
    type: string;
    message: string;
    affectedEvents: string[];
  }>;
}

/**
 * Correlation Application Service
 * 
 * Analyzes enriched events to identify relationships and patterns
 * that indicate coordinated attacks or related security incidents.
 * 
 * Key responsibilities:
 * - Multi-dimensional event correlation
 * - Attack chain reconstruction
 * - Campaign attribution
 * - Pattern recognition and analysis
 * - Threat escalation based on correlations
 */
@Injectable()
export class CorrelationService {
  private readonly logger = new Logger(CorrelationService.name);
  private readonly defaultConfig: CorrelationConfig = {
    temporalWindow: 60, // 60 minutes
    minEvents: 2,
    minConfidence: 50,
    maxEvents: 100,
    enabledTypes: Object.values(CorrelationType),
    algorithms: ['temporal', 'spatial', 'indicator', 'behavioral'],
  };

  constructor(
    private readonly eventPublisher: DomainEventPublisher
  ) {}

  /**
   * Correlate enriched events
   */
  async correlateEvents(request: CorrelationRequest): Promise<CorrelationResult> {
    const config = { ...this.defaultConfig, ...request.config };
    const startTime = Date.now();
    
    this.logger.info('Starting event correlation', {
      eventCount: request.events.length,
      timeWindow: request.timeWindow || config.temporalWindow,
      enabledTypes: config.enabledTypes,
    });

    const result: CorrelationResult = {
      success: false,
      correlatedEvents: [],
      duration: 0,
      statistics: {
        eventsProcessed: request.events.length,
        correlationsFound: 0,
        averageConfidence: 0,
        typeDistribution: {} as Record<CorrelationType, number>,
      },
      errors: [],
    };

    try {
      // Filter and prepare events
      const eligibleEvents = this.filterEligibleEvents(request.events, config);
      
      if (eligibleEvents.length < config.minEvents) {
        this.logger.warn('Insufficient events for correlation', {
          eligibleEvents: eligibleEvents.length,
          minRequired: config.minEvents,
        });
        result.success = true; // Not an error, just no correlations
        return result;
      }

      // Perform different types of correlation
      const correlations: CorrelatedEvent[] = [];

      for (const correlationType of config.enabledTypes) {
        if (request.focusTypes && !request.focusTypes.includes(correlationType)) {
          continue;
        }

        try {
          const typeCorrelations = await this.performCorrelationType(
            correlationType,
            eligibleEvents,
            config
          );
          correlations.push(...typeCorrelations);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          this.logger.error(`Correlation type ${correlationType} failed`, {
            error: errorMessage,
            eventCount: eligibleEvents.length,
          });

          result.errors.push({
            type: correlationType,
            message: errorMessage,
            affectedEvents: eligibleEvents.map(e => e.id.toString()),
          });
        }
      }

      // Remove duplicate correlations and merge similar ones
      const uniqueCorrelations = this.deduplicateCorrelations(correlations);

      // Publish domain events for correlations
      for (const correlation of uniqueCorrelations) {
        await this.eventPublisher.publishAll(correlation.getDomainEvents());
        correlation.clearDomainEvents();
      }

      result.correlatedEvents = uniqueCorrelations;
      result.statistics = this.calculateStatistics(uniqueCorrelations);
      result.success = true;

      this.logger.info('Event correlation completed', {
        eventsProcessed: eligibleEvents.length,
        correlationsFound: uniqueCorrelations.length,
        averageConfidence: result.statistics.averageConfidence,
        duration: result.duration,
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error('Event correlation failed', {
        error: errorMessage,
        stack: errorStack,
        eventCount: request.events.length,
      });

      result.errors.push({
        type: 'general',
        message: errorMessage,
        affectedEvents: request.events.map(e => e.id.toString()),
      });
    } finally {
      result.duration = Date.now() - startTime;
    }

    return result;
  }

  /**
   * Perform real-time correlation for streaming events
   */
  async correlateStreamingEvents(
    newEvent: EnrichedEvent,
    recentEvents: EnrichedEvent[],
    config?: Partial<CorrelationConfig>
  ): Promise<CorrelatedEvent[]> {
    const correlationConfig = { ...this.defaultConfig, ...config };
    
    // Filter recent events within time window
    const timeWindow = correlationConfig.temporalWindow * 60 * 1000; // Convert to milliseconds
    const cutoffTime = new Date(Date.now() - timeWindow);
    
    const eligibleEvents = recentEvents.filter(event => 
      event.createdAt && event.createdAt >= cutoffTime
    );

    // Add the new event
    const allEvents = [newEvent, ...eligibleEvents];

    const correlationRequest: CorrelationRequest = {
      events: allEvents,
      config: correlationConfig,
      focusTypes: [CorrelationType.TEMPORAL, CorrelationType.INDICATOR],
    };

    const result = await this.correlateEvents(correlationRequest);
    return result.correlatedEvents;
  }

  /**
   * Filter events eligible for correlation
   */
  private filterEligibleEvents(
    events: EnrichedEvent[],
    config: CorrelationConfig
  ): EnrichedEvent[] {
    return events
      .filter(event => event.isReadyForCorrelation())
      .filter(event => event.enrichmentScore >= config.minConfidence)
      .slice(0, config.maxEvents)
      .sort((a, b) => b.enrichmentScore - a.enrichmentScore);
  }

  /**
   * Perform specific correlation type
   */
  private async performCorrelationType(
    type: CorrelationType,
    events: EnrichedEvent[],
    config: CorrelationConfig
  ): Promise<CorrelatedEvent[]> {
    switch (type) {
      case CorrelationType.TEMPORAL:
        return this.performTemporalCorrelation(events, config);
      case CorrelationType.SPATIAL:
        return this.performSpatialCorrelation(events, config);
      case CorrelationType.INDICATOR:
        return this.performIndicatorCorrelation(events, config);
      case CorrelationType.BEHAVIORAL:
        return this.performBehavioralCorrelation(events, config);
      case CorrelationType.CAMPAIGN:
        return this.performCampaignCorrelation(events, config);
      case CorrelationType.ATTACK_CHAIN:
        return this.performAttackChainCorrelation(events, config);
      default:
        return [];
    }
  }

  /**
   * Perform temporal correlation
   */
  private async performTemporalCorrelation(
    events: EnrichedEvent[],
    config: CorrelationConfig
  ): Promise<CorrelatedEvent[]> {
    const correlations: CorrelatedEvent[] = [];
    const timeWindow = config.temporalWindow * 60 * 1000; // Convert to milliseconds

    // Group events by time windows
    const timeGroups = this.groupEventsByTime(events, timeWindow);

    for (const group of timeGroups) {
      if (group.length < config.minEvents) continue;

      const [primaryEvent, ...relatedEvents] = group;
      
      const analysis: CorrelationAnalysis = {
        type: CorrelationType.TEMPORAL,
        patterns: [{
          type: 'temporal_burst',
          description: `${group.length} events within ${config.temporalWindow} minutes`,
          confidence: this.calculateTemporalConfidence(group),
          matchingEvents: group.map(e => e.id.toString()),
          attributes: { timeWindow: config.temporalWindow },
          strength: Math.min(1.0, group.length / 10),
        }],
        commonIndicators: this.findCommonIndicators(group),
        temporalCorrelation: {
          timeWindow,
          sequence: group.map((event, index) => ({
            eventId: event.id.toString(),
            timestamp: event.createdAt || new Date(),
            order: index,
          })),
          timeGaps: this.calculateTimeGaps(group),
          patternType: this.determineTemporalPattern(group),
          regularity: this.calculateTemporalRegularity(group),
        },
      };

      const correlation = CorrelatedEvent.create(
        primaryEvent,
        relatedEvents,
        analysis,
        {
          engineVersion: '1.0',
          algorithmsUsed: ['temporal_clustering'],
          processingDuration: 0,
          correlatedAt: new Date(),
          dataSources: ['enriched_events'],
          rulesApplied: ['temporal_window'],
          performanceMetrics: {
            eventsProcessed: group.length,
            correlationsFound: 1,
            falsePositives: 0,
            processingRate: 1000,
          },
        }
      );

      correlations.push(correlation);
    }

    return correlations;
  }

  /**
   * Perform indicator-based correlation
   */
  private async performIndicatorCorrelation(
    events: EnrichedEvent[],
    config: CorrelationConfig
  ): Promise<CorrelatedEvent[]> {
    const correlations: CorrelatedEvent[] = [];

    // Group events by common indicators
    const indicatorGroups = this.groupEventsByIndicators(events);

    for (const [indicators, group] of indicatorGroups) {
      if (group.length < config.minEvents) continue;

      const [primaryEvent, ...relatedEvents] = group;
      
      const analysis: CorrelationAnalysis = {
        type: CorrelationType.INDICATOR,
        patterns: [{
          type: 'common_indicators',
          description: `${indicators.length} common indicators across ${group.length} events`,
          confidence: this.calculateIndicatorConfidence(indicators, group),
          matchingEvents: group.map(e => e.id.toString()),
          attributes: { indicatorCount: indicators.length },
          strength: Math.min(1.0, indicators.length / 5),
        }],
        commonIndicators: indicators,
      };

      const correlation = CorrelatedEvent.create(
        primaryEvent,
        relatedEvents,
        analysis,
        {
          engineVersion: '1.0',
          algorithmsUsed: ['indicator_matching'],
          processingDuration: 0,
          correlatedAt: new Date(),
          dataSources: ['threat_intelligence'],
          rulesApplied: ['common_indicators'],
          performanceMetrics: {
            eventsProcessed: group.length,
            correlationsFound: 1,
            falsePositives: 0,
            processingRate: 1000,
          },
        }
      );

      correlations.push(correlation);
    }

    return correlations;
  }

  /**
   * Perform spatial correlation
   */
  private async performSpatialCorrelation(
    _events: EnrichedEvent[],
    _config: CorrelationConfig
  ): Promise<CorrelatedEvent[]> {
    // Implementation would analyze network proximity, geographic location, etc.
    return [];
  }

  /**
   * Perform behavioral correlation
   */
  private async performBehavioralCorrelation(
    _events: EnrichedEvent[],
    _config: CorrelationConfig
  ): Promise<CorrelatedEvent[]> {
    // Implementation would analyze user behavior patterns, anomalies, etc.
    return [];
  }

  /**
   * Perform campaign correlation
   */
  private async performCampaignCorrelation(
    _events: EnrichedEvent[],
    _config: CorrelationConfig
  ): Promise<CorrelatedEvent[]> {
    // Implementation would analyze threat actor attribution, TTPs, etc.
    return [];
  }

  /**
   * Perform attack chain correlation
   */
  private async performAttackChainCorrelation(
    _events: EnrichedEvent[],
    _config: CorrelationConfig
  ): Promise<CorrelatedEvent[]> {
    // Implementation would analyze MITRE ATT&CK techniques, kill chain phases, etc.
    return [];
  }

  /**
   * Group events by time windows
   */
  private groupEventsByTime(events: EnrichedEvent[], timeWindow: number): EnrichedEvent[][] {
    const groups: EnrichedEvent[][] = [];
    const sortedEvents = events.sort((a, b) => 
      (a.createdAt?.getTime() || 0) - (b.createdAt?.getTime() || 0)
    );

    let currentGroup: EnrichedEvent[] = [];
    let groupStartTime: number | null = null;

    for (const event of sortedEvents) {
      const eventTime = event.createdAt?.getTime() || 0;
      
      if (groupStartTime === null || eventTime - groupStartTime <= timeWindow) {
        currentGroup.push(event);
        if (groupStartTime === null) {
          groupStartTime = eventTime;
        }
      } else {
        if (currentGroup.length >= 2) {
          groups.push(currentGroup);
        }
        currentGroup = [event];
        groupStartTime = eventTime;
      }
    }

    if (currentGroup.length >= 2) {
      groups.push(currentGroup);
    }

    return groups;
  }

  /**
   * Group events by common indicators
   */
  private groupEventsByIndicators(events: EnrichedEvent[]): Map<IOC[], EnrichedEvent[]> {
    const groups = new Map<string, EnrichedEvent[]>();

    for (const event of events) {
      const indicators = event.indicators;
      if (indicators.length === 0) continue;

      // Create a key from sorted indicator values
      const key = indicators
        .map(ioc => `${ioc.type}:${ioc.value}`)
        .sort()
        .join('|');

      if (!groups.has(key)) {
        groups.set(key, []);
      }
      groups.get(key)!.push(event);
    }

    // Convert back to IOC[] keys
    const result = new Map<IOC[], EnrichedEvent[]>();
    for (const [, eventGroup] of groups) {
      if (eventGroup.length >= 2) {
        const indicators = eventGroup[0].indicators;
        result.set(indicators, eventGroup);
      }
    }

    return result;
  }

  /**
   * Find common indicators across events
   */
  private findCommonIndicators(events: EnrichedEvent[]): IOC[] {
    if (events.length === 0) return [];

    const indicatorSets = events.map(event => new Set(
      event.indicators.map(ioc => `${ioc.type}:${ioc.value}`)
    ));

    const commonIndicatorKeys = indicatorSets.reduce((common, current) => 
      new Set([...common].filter(x => current.has(x)))
    );

    // Convert back to IOC objects
    const firstEvent = events[0];
    return firstEvent.indicators.filter(ioc => 
      commonIndicatorKeys.has(`${ioc.type}:${ioc.value}`)
    );
  }

  /**
   * Calculate temporal confidence
   */
  private calculateTemporalConfidence(events: EnrichedEvent[]): number {
    const baseConfidence = Math.min(90, events.length * 15);
    const qualityBonus = events.reduce((sum, e) => sum + e.enrichmentScore, 0) / events.length * 0.1;
    return Math.min(100, baseConfidence + qualityBonus);
  }

  /**
   * Calculate indicator confidence
   */
  private calculateIndicatorConfidence(indicators: IOC[], events: EnrichedEvent[]): number {
    const baseConfidence = Math.min(95, indicators.length * 20);
    const eventCountBonus = Math.min(20, events.length * 5);
    return Math.min(100, baseConfidence + eventCountBonus);
  }

  /**
   * Calculate time gaps between events
   */
  private calculateTimeGaps(events: EnrichedEvent[]): number[] {
    const sortedEvents = events.sort((a, b) => 
      (a.createdAt?.getTime() || 0) - (b.createdAt?.getTime() || 0)
    );

    const gaps: number[] = [];
    for (let i = 1; i < sortedEvents.length; i++) {
      const gap = (sortedEvents[i].createdAt?.getTime() || 0) - 
                   (sortedEvents[i-1].createdAt?.getTime() || 0);
      gaps.push(gap);
    }

    return gaps;
  }

  /**
   * Determine temporal pattern type
   */
  private determineTemporalPattern(events: EnrichedEvent[]): 'burst' | 'periodic' | 'sequential' | 'simultaneous' {
    const timeGaps = this.calculateTimeGaps(events);
    
    if (timeGaps.every(gap => gap < 60000)) { // All within 1 minute
      return 'simultaneous';
    }
    
    if (timeGaps.length <= 2) {
      return 'sequential';
    }

    const avgGap = timeGaps.reduce((sum, gap) => sum + gap, 0) / timeGaps.length;
    const variance = timeGaps.reduce((sum, gap) => sum + Math.pow(gap - avgGap, 2), 0) / timeGaps.length;
    
    if (variance < avgGap * 0.1) {
      return 'periodic';
    }
    
    return 'burst';
  }

  /**
   * Calculate temporal regularity
   */
  private calculateTemporalRegularity(events: EnrichedEvent[]): number {
    const timeGaps = this.calculateTimeGaps(events);
    if (timeGaps.length === 0) return 100;

    const avgGap = timeGaps.reduce((sum, gap) => sum + gap, 0) / timeGaps.length;
    const variance = timeGaps.reduce((sum, gap) => sum + Math.pow(gap - avgGap, 2), 0) / timeGaps.length;
    const stdDev = Math.sqrt(variance);
    
    const regularity = Math.max(0, 100 - (stdDev / avgGap) * 100);
    return Math.min(100, regularity);
  }

  /**
   * Remove duplicate correlations
   */
  private deduplicateCorrelations(correlations: CorrelatedEvent[]): CorrelatedEvent[] {
    const seen = new Set<string>();
    const unique: CorrelatedEvent[] = [];

    for (const correlation of correlations) {
      const key = correlation.allEventIds
        .map(id => id.toString())
        .sort()
        .join('|');

      if (!seen.has(key)) {
        seen.add(key);
        unique.push(correlation);
      }
    }

    return unique;
  }

  /**
   * Calculate correlation statistics
   */
  private calculateStatistics(correlations: CorrelatedEvent[]): {
    eventsProcessed: number;
    correlationsFound: number;
    averageConfidence: number;
    typeDistribution: Record<CorrelationType, number>;
  } {
    const typeDistribution = {} as Record<CorrelationType, number>;
    
    for (const type of Object.values(CorrelationType)) {
      typeDistribution[type] = 0;
    }

    let totalConfidence = 0;
    for (const correlation of correlations) {
      typeDistribution[correlation.correlationType]++;
      totalConfidence += correlation.score;
    }

    return {
      eventsProcessed: correlations.reduce((sum, c) => sum + c.eventCount, 0),
      correlationsFound: correlations.length,
      averageConfidence: correlations.length > 0 ? totalConfidence / correlations.length : 0,
      typeDistribution,
    };
  }
}
