2c6401afe34545f3bd76b6e95b55966e
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThreatIntelligence = exports.ThreatStatus = exports.ThreatType = exports.ThreatConfidence = exports.ThreatSeverity = void 0;
const typeorm_1 = require("typeorm");
const class_validator_1 = require("class-validator");
const indicator_of_compromise_entity_1 = require("./indicator-of-compromise.entity");
const threat_indicator_entity_1 = require("./threat-indicator.entity");
const threat_actor_entity_1 = require("./threat-actor.entity");
const threat_campaign_entity_1 = require("./threat-campaign.entity");
/**
 * Threat intelligence severity levels
 */
var ThreatSeverity;
(function (ThreatSeverity) {
    ThreatSeverity["CRITICAL"] = "critical";
    ThreatSeverity["HIGH"] = "high";
    ThreatSeverity["MEDIUM"] = "medium";
    ThreatSeverity["LOW"] = "low";
    ThreatSeverity["INFO"] = "info";
})(ThreatSeverity || (exports.ThreatSeverity = ThreatSeverity = {}));
/**
 * Threat intelligence confidence levels
 */
var ThreatConfidence;
(function (ThreatConfidence) {
    ThreatConfidence["HIGH"] = "high";
    ThreatConfidence["MEDIUM"] = "medium";
    ThreatConfidence["LOW"] = "low";
    ThreatConfidence["UNKNOWN"] = "unknown";
})(ThreatConfidence || (exports.ThreatConfidence = ThreatConfidence = {}));
/**
 * Threat intelligence types
 */
var ThreatType;
(function (ThreatType) {
    ThreatType["MALWARE"] = "malware";
    ThreatType["PHISHING"] = "phishing";
    ThreatType["BOTNET"] = "botnet";
    ThreatType["APT"] = "apt";
    ThreatType["RANSOMWARE"] = "ransomware";
    ThreatType["TROJAN"] = "trojan";
    ThreatType["BACKDOOR"] = "backdoor";
    ThreatType["ROOTKIT"] = "rootkit";
    ThreatType["SPYWARE"] = "spyware";
    ThreatType["ADWARE"] = "adware";
    ThreatType["VULNERABILITY"] = "vulnerability";
    ThreatType["EXPLOIT"] = "exploit";
    ThreatType["SUSPICIOUS_ACTIVITY"] = "suspicious_activity";
    ThreatType["ATTACK_PATTERN"] = "attack_pattern";
    ThreatType["TOOL"] = "tool";
    ThreatType["INFRASTRUCTURE"] = "infrastructure";
    ThreatType["OTHER"] = "other";
})(ThreatType || (exports.ThreatType = ThreatType = {}));
/**
 * Threat intelligence status
 */
var ThreatStatus;
(function (ThreatStatus) {
    ThreatStatus["ACTIVE"] = "active";
    ThreatStatus["INACTIVE"] = "inactive";
    ThreatStatus["EXPIRED"] = "expired";
    ThreatStatus["REVOKED"] = "revoked";
    ThreatStatus["UNDER_REVIEW"] = "under_review";
})(ThreatStatus || (exports.ThreatStatus = ThreatStatus = {}));
/**
 * Threat intelligence entity
 * Represents comprehensive threat intelligence information including IOCs, attribution, and analysis
 */
let ThreatIntelligence = class ThreatIntelligence {
    /**
     * Check if threat intelligence is currently active
     */
    get isActive() {
        return this.status === ThreatStatus.ACTIVE &&
            (!this.expiresAt || this.expiresAt > new Date());
    }
    /**
     * Check if threat intelligence is expired
     */
    get isExpired() {
        return this.expiresAt ? this.expiresAt <= new Date() : false;
    }
    /**
     * Get age in days
     */
    get ageInDays() {
        return Math.floor((Date.now() - this.firstSeen.getTime()) / (1000 * 60 * 60 * 24));
    }
    /**
     * Get threat intelligence summary
     */
    getSummary() {
        return {
            id: this.id,
            title: this.title,
            threatType: this.threatType,
            severity: this.severity,
            confidence: this.confidence,
            status: this.status,
            isActive: this.isActive,
            isExpired: this.isExpired,
            ageInDays: this.ageInDays,
            observationCount: this.observationCount,
            indicatorCount: this.indicators?.length || 0,
            threatActor: this.threatActor?.name,
            threatCampaign: this.threatCampaign?.name,
            dataSource: this.dataSource.name,
            tags: this.tags,
        };
    }
    /**
     * Update observation count and last observed timestamp
     */
    recordObservation() {
        this.observationCount += 1;
        this.lastObserved = new Date();
    }
    /**
     * Calculate risk score based on multiple factors
     */
    calculateRiskScore() {
        let score = 0;
        // Base score from severity
        const severityScores = {
            [ThreatSeverity.CRITICAL]: 9.0,
            [ThreatSeverity.HIGH]: 7.0,
            [ThreatSeverity.MEDIUM]: 5.0,
            [ThreatSeverity.LOW]: 3.0,
            [ThreatSeverity.INFO]: 1.0,
        };
        score += severityScores[this.severity];
        // Confidence modifier
        const confidenceModifiers = {
            [ThreatConfidence.HIGH]: 1.0,
            [ThreatConfidence.MEDIUM]: 0.8,
            [ThreatConfidence.LOW]: 0.6,
            [ThreatConfidence.UNKNOWN]: 0.4,
        };
        score *= confidenceModifiers[this.confidence];
        // Observation frequency boost
        if (this.observationCount > 10) {
            score += 1.0;
        }
        else if (this.observationCount > 5) {
            score += 0.5;
        }
        // Recency factor
        const daysSinceLastSeen = this.lastSeen ?
            Math.floor((Date.now() - this.lastSeen.getTime()) / (1000 * 60 * 60 * 24)) :
            this.ageInDays;
        if (daysSinceLastSeen <= 7) {
            score += 1.0; // Very recent
        }
        else if (daysSinceLastSeen <= 30) {
            score += 0.5; // Recent
        }
        // Attribution factor
        if (this.isAttributed && this.threatActor) {
            score += 0.5;
        }
        // Cap at 10.0
        this.riskScore = Math.min(score, 10.0);
        return this.riskScore;
    }
    /**
     * Check if threat intelligence matches given criteria
     */
    matches(criteria) {
        if (criteria.threatTypes && !criteria.threatTypes.includes(this.threatType)) {
            return false;
        }
        if (criteria.severities && !criteria.severities.includes(this.severity)) {
            return false;
        }
        if (criteria.tags && this.tags) {
            const hasMatchingTag = criteria.tags.some(tag => this.tags.includes(tag));
            if (!hasMatchingTag) {
                return false;
            }
        }
        if (criteria.sectors && this.targetedSectors) {
            const hasMatchingSector = criteria.sectors.some(sector => this.targetedSectors.includes(sector));
            if (!hasMatchingSector) {
                return false;
            }
        }
        if (criteria.platforms && this.affectedPlatforms) {
            const hasMatchingPlatform = criteria.platforms.some(platform => this.affectedPlatforms.includes(platform));
            if (!hasMatchingPlatform) {
                return false;
            }
        }
        return true;
    }
    /**
     * Export threat intelligence for reporting
     */
    exportForReporting() {
        return {
            id: this.id,
            title: this.title,
            description: this.description,
            threatType: this.threatType,
            severity: this.severity,
            confidence: this.confidence,
            status: this.status,
            firstSeen: this.firstSeen,
            lastSeen: this.lastSeen,
            expiresAt: this.expiresAt,
            tags: this.tags,
            dataSource: this.dataSource,
            mitreAttack: this.mitreAttack,
            killChain: this.killChain,
            targetedSectors: this.targetedSectors,
            targetedCountries: this.targetedCountries,
            affectedPlatforms: this.affectedPlatforms,
            riskScore: this.riskScore,
            observationCount: this.observationCount,
            lastObserved: this.lastObserved,
            isIoc: this.isIoc,
            isAttributed: this.isAttributed,
            threatActor: this.threatActor?.name,
            threatCampaign: this.threatCampaign?.name,
            indicatorCount: this.indicators?.length || 0,
            references: this.references,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }
    /**
     * Activate threat intelligence
     */
    activate() {
        this.status = ThreatStatus.ACTIVE;
    }
    /**
     * Deactivate threat intelligence
     */
    deactivate() {
        this.status = ThreatStatus.INACTIVE;
    }
    /**
     * Mark as expired
     */
    expire() {
        this.status = ThreatStatus.EXPIRED;
        this.expiresAt = new Date();
    }
    /**
     * Revoke threat intelligence
     */
    revoke(reason) {
        this.status = ThreatStatus.REVOKED;
        if (reason) {
            this.customAttributes = {
                ...this.customAttributes,
                revocationReason: reason,
                revokedAt: new Date(),
            };
        }
    }
};
exports.ThreatIntelligence = ThreatIntelligence;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ThreatIntelligence.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ThreatIntelligence.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ThreatIntelligence.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ThreatType,
    }),
    (0, class_validator_1.IsEnum)(ThreatType),
    __metadata("design:type", String)
], ThreatIntelligence.prototype, "threatType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ThreatSeverity,
        default: ThreatSeverity.MEDIUM,
    }),
    (0, class_validator_1.IsEnum)(ThreatSeverity),
    __metadata("design:type", String)
], ThreatIntelligence.prototype, "severity", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ThreatConfidence,
        default: ThreatConfidence.MEDIUM,
    }),
    (0, class_validator_1.IsEnum)(ThreatConfidence),
    __metadata("design:type", String)
], ThreatIntelligence.prototype, "confidence", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ThreatStatus,
        default: ThreatStatus.ACTIVE,
    }),
    (0, class_validator_1.IsEnum)(ThreatStatus),
    __metadata("design:type", String)
], ThreatIntelligence.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", typeof (_a = typeof Date !== "undefined" && Date) === "function" ? _a : Object)
], ThreatIntelligence.prototype, "firstSeen", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", typeof (_b = typeof Date !== "undefined" && Date) === "function" ? _b : Object)
], ThreatIntelligence.prototype, "lastSeen", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", typeof (_c = typeof Date !== "undefined" && Date) === "function" ? _c : Object)
], ThreatIntelligence.prototype, "expiresAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ThreatIntelligence.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ThreatIntelligence.prototype, "dataSource", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ThreatIntelligence.prototype, "mitreAttack", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ThreatIntelligence.prototype, "killChain", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ThreatIntelligence.prototype, "targetedSectors", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ThreatIntelligence.prototype, "targetedCountries", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ThreatIntelligence.prototype, "affectedPlatforms", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ThreatIntelligence.prototype, "technicalDetails", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ThreatIntelligence.prototype, "behavioralAnalysis", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ThreatIntelligence.prototype, "references", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ThreatIntelligence.prototype, "stixData", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 3, scale: 1, nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ThreatIntelligence.prototype, "riskScore", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'integer', default: 0 }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ThreatIntelligence.prototype, "observationCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", typeof (_d = typeof Date !== "undefined" && Date) === "function" ? _d : Object)
], ThreatIntelligence.prototype, "lastObserved", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ThreatIntelligence.prototype, "isIoc", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ThreatIntelligence.prototype, "isAttributed", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", typeof (_e = typeof Record !== "undefined" && Record) === "function" ? _e : Object)
], ThreatIntelligence.prototype, "customAttributes", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => indicator_of_compromise_entity_1.IndicatorOfCompromise, (ioc) => ioc.threatIntelligence),
    __metadata("design:type", Array)
], ThreatIntelligence.prototype, "indicators", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => threat_indicator_entity_1.ThreatIndicator, (indicator) => indicator.threatIntelligence),
    __metadata("design:type", Array)
], ThreatIntelligence.prototype, "threatIndicators", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => threat_actor_entity_1.ThreatActor, (actor) => actor.threatIntelligence, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'threat_actor_id' }),
    __metadata("design:type", typeof (_f = typeof threat_actor_entity_1.ThreatActor !== "undefined" && threat_actor_entity_1.ThreatActor) === "function" ? _f : Object)
], ThreatIntelligence.prototype, "threatActor", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], ThreatIntelligence.prototype, "threatActorId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => threat_campaign_entity_1.ThreatCampaign, (campaign) => campaign.threatIntelligence, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'threat_campaign_id' }),
    __metadata("design:type", typeof (_g = typeof threat_campaign_entity_1.ThreatCampaign !== "undefined" && threat_campaign_entity_1.ThreatCampaign) === "function" ? _g : Object)
], ThreatIntelligence.prototype, "threatCampaign", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], ThreatIntelligence.prototype, "threatCampaignId", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", typeof (_h = typeof Date !== "undefined" && Date) === "function" ? _h : Object)
], ThreatIntelligence.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", typeof (_j = typeof Date !== "undefined" && Date) === "function" ? _j : Object)
], ThreatIntelligence.prototype, "updatedAt", void 0);
exports.ThreatIntelligence = ThreatIntelligence = __decorate([
    (0, typeorm_1.Entity)('threat_intelligence'),
    (0, typeorm_1.Index)(['threatType', 'severity']),
    (0, typeorm_1.Index)(['status', 'firstSeen']),
    (0, typeorm_1.Index)(['confidence', 'severity']),
    (0, typeorm_1.Index)(['tags'], { where: 'tags IS NOT NULL' })
], ThreatIntelligence);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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