/**
 * Response utility functions for the Sentinel platform
 * Provides consistent API response formatting and helper functions
 */

import { HttpStatus } from '@nestjs/common';
import { Request } from 'express';
import { 
  ApiResponse, 
  ApiError, 
  ResponseMetadata, 
  PaginationInfo, 
  PaginatedResponse 
} from '../../global.types';
import { getCorrelationId } from './correlation-id.util';

/**
 * Create a successful API response
 */
export function createSuccessResponse<T>(
  data: T,
  request?: Request,
  statusCode: number = HttpStatus.OK,
  pagination?: PaginationInfo
): ApiResponse<T> {
  const metadata = createResponseMetadata(request, statusCode);
  
  return {
    success: true,
    data,
    metadata,
    ...(pagination && { pagination }),
  };
}

/**
 * Create an error API response
 */
export function createErrorResponse(
  error: ApiError | string,
  request?: Request,
  statusCode: number = HttpStatus.INTERNAL_SERVER_ERROR
): ApiResponse<null> {
  const metadata = createResponseMetadata(request, statusCode);
  
  const apiError: ApiError = typeof error === 'string' 
    ? {
        code: 'GENERAL_ERROR',
        message: error,
        timestamp: new Date().toISOString(),
        correlationId: metadata.correlationId,
      }
    : error;

  return {
    success: false,
    error: apiError,
    metadata,
  };
}

/**
 * Create a paginated response
 */
export function createPaginatedResponse<T>(
  items: T[],
  pagination: PaginationInfo,
  request?: Request,
  statusCode: number = HttpStatus.OK
): ApiResponse<T[]> {
  const metadata = createResponseMetadata(request, statusCode);
  
  return {
    success: true,
    data: items,
    metadata,
    pagination,
  };
}

/**
 * Create response metadata
 */
export function createResponseMetadata(
  request?: Request,
  statusCode: number = HttpStatus.OK
): ResponseMetadata {
  const timestamp = new Date().toISOString();
  const correlationId = request ? getCorrelationId(request) : generateCorrelationId();
  
  return {
    correlationId,
    timestamp,
    path: request?.url || '',
    method: request?.method || '',
    statusCode,
    version: process.env.API_VERSION || 'v1',
  };
}

/**
 * Create pagination info
 */
export function createPaginationInfo(
  page: number,
  limit: number,
  total: number
): PaginationInfo {
  const totalPages = Math.ceil(total / limit);
  
  return {
    page,
    limit,
    total,
    totalPages,
    hasNext: page < totalPages,
    hasPrevious: page > 1,
  };
}

/**
 * Transform data to paginated response format
 */
export function toPaginatedResponse<T>(
  items: T[],
  page: number,
  limit: number,
  total: number
): PaginatedResponse<T> {
  const pagination = createPaginationInfo(page, limit, total);
  
  return {
    items,
    pagination,
  };
}

/**
 * Create API error object
 */
export function createApiError(
  code: string,
  message: string,
  details?: Record<string, any>,
  correlationId?: string
): ApiError {
  return {
    code,
    message,
    details,
    timestamp: new Date().toISOString(),
    correlationId: correlationId || generateCorrelationId(),
  };
}

/**
 * Create validation error response
 */
export function createValidationErrorResponse(
  errors: ValidationError[],
  request?: Request
): ApiResponse<null> {
  const apiError = createApiError(
    'VALIDATION_FAILED',
    'Request validation failed',
    { errors },
    request ? getCorrelationId(request) : undefined
  );
  
  return createErrorResponse(apiError, request, HttpStatus.BAD_REQUEST);
}

/**
 * Create not found error response
 */
export function createNotFoundResponse(
  resource: string,
  id?: string | number,
  request?: Request
): ApiResponse<null> {
  const message = id 
    ? `${resource} with ID '${id}' not found`
    : `${resource} not found`;
    
  const apiError = createApiError(
    'RESOURCE_NOT_FOUND',
    message,
    { resource, id },
    request ? getCorrelationId(request) : undefined
  );
  
  return createErrorResponse(apiError, request, HttpStatus.NOT_FOUND);
}

/**
 * Create unauthorized error response
 */
export function createUnauthorizedResponse(
  message: string = 'Authentication required',
  request?: Request
): ApiResponse<null> {
  const apiError = createApiError(
    'UNAUTHORIZED',
    message,
    undefined,
    request ? getCorrelationId(request) : undefined
  );
  
  return createErrorResponse(apiError, request, HttpStatus.UNAUTHORIZED);
}

/**
 * Create forbidden error response
 */
export function createForbiddenResponse(
  message: string = 'Access denied',
  request?: Request
): ApiResponse<null> {
  const apiError = createApiError(
    'FORBIDDEN',
    message,
    undefined,
    request ? getCorrelationId(request) : undefined
  );
  
  return createErrorResponse(apiError, request, HttpStatus.FORBIDDEN);
}

/**
 * Create conflict error response
 */
export function createConflictResponse(
  message: string,
  details?: Record<string, any>,
  request?: Request
): ApiResponse<null> {
  const apiError = createApiError(
    'CONFLICT',
    message,
    details,
    request ? getCorrelationId(request) : undefined
  );
  
  return createErrorResponse(apiError, request, HttpStatus.CONFLICT);
}

/**
 * Create rate limit exceeded response
 */
export function createRateLimitResponse(
  retryAfter?: number,
  request?: Request
): ApiResponse<null> {
  const apiError = createApiError(
    'RATE_LIMIT_EXCEEDED',
    'Too many requests',
    { retryAfter },
    request ? getCorrelationId(request) : undefined
  );
  
  return createErrorResponse(apiError, request, HttpStatus.TOO_MANY_REQUESTS);
}

/**
 * Create service unavailable response
 */
export function createServiceUnavailableResponse(
  service?: string,
  request?: Request
): ApiResponse<null> {
  const message = service 
    ? `${service} service is currently unavailable`
    : 'Service temporarily unavailable';
    
  const apiError = createApiError(
    'SERVICE_UNAVAILABLE',
    message,
    { service },
    request ? getCorrelationId(request) : undefined
  );
  
  return createErrorResponse(apiError, request, HttpStatus.SERVICE_UNAVAILABLE);
}

/**
 * Transform exception to API error
 */
export function transformExceptionToApiError(
  exception: Error,
  code?: string,
  correlationId?: string
): ApiError {
  return {
    code: code || 'INTERNAL_SERVER_ERROR',
    message: exception.message,
    details: {
      name: exception.name,
      stack: process.env.NODE_ENV === 'development' ? exception.stack : undefined,
    },
    timestamp: new Date().toISOString(),
    correlationId: correlationId || generateCorrelationId(),
  };
}

/**
 * Check if response is successful
 */
export function isSuccessResponse<T>(response: ApiResponse<T>): boolean {
  return response.success === true;
}

/**
 * Check if response is an error
 */
export function isErrorResponse<T>(response: ApiResponse<T>): boolean {
  return response.success === false;
}

/**
 * Extract data from response
 */
export function extractResponseData<T>(response: ApiResponse<T>): T | null {
  return isSuccessResponse(response) ? response.data || null : null;
}

/**
 * Extract error from response
 */
export function extractResponseError<T>(response: ApiResponse<T>): ApiError | null {
  return isErrorResponse(response) ? response.error || null : null;
}

/**
 * Generate correlation ID (fallback)
 */
function generateCorrelationId(): string {
  return require('uuid').v4();
}

/**
 * Validation error interface
 */
export interface ValidationError {
  field: string;
  value: any;
  constraints: Record<string, string>;
}

/**
 * Response builder class for fluent API
 */
export class ResponseBuilder<T = any> {
  private _data?: T;
  private _error?: ApiError;
  private _statusCode: number = HttpStatus.OK;
  private _pagination?: PaginationInfo;
  private _request?: Request;

  static success<T>(data?: T): ResponseBuilder<T> {
    const builder = new ResponseBuilder<T>();
    builder._data = data;
    return builder;
  }

  static error(error: ApiError | string): ResponseBuilder<null> {
    const builder = new ResponseBuilder<null>();
    builder._error = typeof error === 'string' 
      ? createApiError('GENERAL_ERROR', error)
      : error;
    builder._statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
    return builder;
  }

  static paginated<T>(items: T[], pagination: PaginationInfo): ResponseBuilder<T[]> {
    const builder = new ResponseBuilder<T[]>();
    builder._data = items;
    builder._pagination = pagination;
    return builder;
  }

  withStatusCode(statusCode: number): this {
    this._statusCode = statusCode;
    return this;
  }

  withRequest(request: Request): this {
    this._request = request;
    return this;
  }

  withPagination(pagination: PaginationInfo): this {
    this._pagination = pagination;
    return this;
  }

  build(): ApiResponse<T> {
    if (this._error) {
      return createErrorResponse(this._error, this._request, this._statusCode);
    }

    if (this._pagination) {
      return createPaginatedResponse(
        this._data as any,
        this._pagination,
        this._request,
        this._statusCode
      ) as ApiResponse<T>;
    }

    return createSuccessResponse(this._data, this._request, this._statusCode);
  }
}

/**
 * Response utility constants
 */
export const RESPONSE_MESSAGES = {
  SUCCESS: 'Operation completed successfully',
  CREATED: 'Resource created successfully',
  UPDATED: 'Resource updated successfully',
  DELETED: 'Resource deleted successfully',
  NOT_FOUND: 'Resource not found',
  UNAUTHORIZED: 'Authentication required',
  FORBIDDEN: 'Access denied',
  VALIDATION_FAILED: 'Request validation failed',
  INTERNAL_ERROR: 'Internal server error',
  SERVICE_UNAVAILABLE: 'Service temporarily unavailable',
  RATE_LIMIT_EXCEEDED: 'Too many requests',
} as const;