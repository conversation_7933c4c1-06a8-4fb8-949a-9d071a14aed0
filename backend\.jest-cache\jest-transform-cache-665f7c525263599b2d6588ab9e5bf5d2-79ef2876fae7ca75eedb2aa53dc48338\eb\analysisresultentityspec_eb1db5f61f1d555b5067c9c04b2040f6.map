{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\entities\\__tests__\\analysis-result.entity.spec.ts", "mappings": ";;AAAA,4HAA0G;AAC1G,sEASmC;AACnC,4GAA+F;AAC/F,0HAA4G;AAC5G,4GAA+F;AAE/F,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;IACrC,IAAI,UAAqE,CAAC;IAC1E,IAAI,aAA4B,CAAC;IACjC,IAAI,cAA8B,CAAC;IACnC,IAAI,YAA8B,CAAC;IAEnC,UAAU,CAAC,GAAG,EAAE;QACd,aAAa,GAAG;YACd,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;YACvB,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,GAAG;YACT,QAAQ,EAAE,gBAAgB;YAC1B,oBAAoB,EAAE,CAAC,eAAe,CAAC;YACvC,eAAe,EAAE,CAAC,UAAU,CAAC;SAC9B,CAAC;QAEF,cAAc,GAAG;YACf,OAAO,EAAE,EAAE,UAAU,EAAE,aAAa,EAAE;YACtC,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,GAAG;YACT,QAAQ,EAAE,iBAAiB;YAC3B,qBAAqB,EAAE,CAAC,YAAY,CAAC;YACrC,gBAAgB,EAAE,QAAQ;YAC1B,YAAY,EAAE,GAAG;SAClB,CAAC;QAEF,YAAY,GAAG;YACb,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,gBAAgB;YAC3B,UAAU,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE;YAChC,WAAW,EAAE,MAAM;YACnB,aAAa,EAAE;gBACb,OAAO,EAAE,IAAI;gBACb,WAAW,EAAE,GAAG;gBAChB,OAAO,EAAE,GAAG;gBACZ,SAAS,EAAE,GAAG;gBACd,MAAM,EAAE,EAAE;aACX;YACD,kBAAkB,EAAE;gBAClB,UAAU,EAAE,EAAE;gBACd,OAAO,EAAE,GAAG;gBACZ,QAAQ,EAAE,IAAI;gBACd,SAAS,EAAE,IAAI;gBACf,MAAM,EAAE,IAAI;gBACZ,OAAO,EAAE,IAAI;aACd;YACD,cAAc,EAAE;gBACd,WAAW,EAAE,GAAG;gBAChB,iBAAiB,EAAE,IAAI;gBACvB,gBAAgB,EAAE,GAAG;gBACrB,iBAAiB,EAAE,IAAI;aACxB;SACF,CAAC;QAEF,UAAU,GAAG;YACX,SAAS,EAAE,kBAAkB;YAC7B,OAAO,EAAE,8CAAc,CAAC,QAAQ,EAAE;YAClC,YAAY,EAAE,qCAAY,CAAC,cAAc;YACzC,SAAS,EAAE,aAAa;YACxB,UAAU,EAAE,cAAc;YAC1B,UAAU,EAAE,IAAI;YAChB,cAAc,EAAE,IAAI;YACpB,MAAM,EAAE,uCAAc,CAAC,OAAO;YAC9B,QAAQ,EAAE,YAAY;YACtB,IAAI,EAAE,CAAC,MAAM,EAAE,gBAAgB,CAAC;YAChC,aAAa,EAAE,iBAAiB;YAChC,gBAAgB,EAAE,8CAAc,CAAC,QAAQ,EAAE;YAC3C,YAAY,EAAE,SAAS;YACvB,WAAW,EAAE,SAAS;SACvB,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,MAAM,GAAG,uCAAc,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAEjD,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,uCAAc,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7D,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,MAAM,GAAG,uCAAc,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACjD,MAAM,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC;YAEnC,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,iEAA0B,CAAC,CAAC;YAC7D,MAAM,CAAE,MAAM,CAAC,CAAC,CAAgC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,QAAQ,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YAC3C,MAAM,MAAM,GAAG,uCAAc,CAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAE3D,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC;YAEtD,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,OAAO,EAAE,IAAW,EAAE,CAAC;YAE7D,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,YAAY,EAAE,SAAyB,EAAE,CAAC;YAEhF,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;QACrF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;YAExD,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;QAClG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,cAAc,EAAE,CAAC,GAAG,EAAE,CAAC;YAE7D,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;QAClG,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,SAAS,EAAE,IAAW,EAAE,CAAC;YAE/D,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,YAAY,GAAG,EAAE,GAAG,UAAU,EAAE,QAAQ,EAAE,IAAW,EAAE,CAAC;YAE9D,MAAM,CAAC,GAAG,EAAE,CAAC,uCAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,EAAE,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,KAAK,GAAG;gBACZ,GAAG,UAAU;gBACb,gBAAgB,EAAE,CAAC,8CAAc,CAAC,QAAQ,EAAE,CAAC;gBAC7C,SAAS,EAAE,GAAG;gBACd,SAAS,EAAE,GAAG;aACf,CAAC;YAEF,MAAM,MAAM,GAAG,uCAAc,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YAEtD,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,IAAI,MAAsB,CAAC;QAE3B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,uCAAc,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAC3C,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,uBAAuB;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,GAAG,EAAE;YACrD,MAAM,CAAC,eAAe,EAAE,CAAC;YAEzB,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,uCAAc,CAAC,UAAU,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,8EAAgC,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;YAC7D,yBAAyB;YACzB,MAAM,CAAC,eAAe,EAAE,CAAC;YACzB,MAAM,CAAC,WAAW,EAAE,CAAC;YAErB,MAAM,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC,OAAO,CAAC,wDAAwD,CAAC,CAAC;QAC3G,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,GAAG,EAAE;YACzD,MAAM,CAAC,eAAe,EAAE,CAAC;YACzB,MAAM,CAAC,WAAW,EAAE,CAAC;YAErB,MAAM,CAAC,QAAQ,CAAC,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAE5C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,uCAAc,CAAC,SAAS,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,8EAAgC,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,OAAO,CAC/D,6CAA6C,CAC9C,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,CAAC,eAAe,EAAE,CAAC;YACzB,MAAM,CAAC,WAAW,EAAE,CAAC;YAErB,MAAM,YAAY,GAAiB;gBACjC,IAAI,EAAE,YAAY;gBAClB,OAAO,EAAE,YAAY;gBACrB,OAAO,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;gBACvB,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,sCAAa,CAAC,gBAAgB;aACzC,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAE1B,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,uCAAc,CAAC,MAAM,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,8EAAgC,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,CAAC,eAAe,EAAE,CAAC;YACzB,MAAM,CAAC,QAAQ,CAAC,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAE5C,MAAM,YAAY,GAAiB;gBACjC,IAAI,EAAE,YAAY;gBAClB,OAAO,EAAE,YAAY;gBACrB,OAAO,EAAE,EAAE;gBACX,SAAS,EAAE,KAAK;gBAChB,QAAQ,EAAE,sCAAa,CAAC,gBAAgB;aACzC,CAAC;YAEF,MAAM,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,kCAAkC,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wBAAwB,EAAE,GAAG,EAAE;YAChC,MAAM,CAAC,MAAM,EAAE,CAAC;YAEhB,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,uCAAc,CAAC,SAAS,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,8EAAgC,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,MAAM,CAAC,eAAe,EAAE,CAAC;YACzB,MAAM,CAAC,QAAQ,CAAC,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAE5C,MAAM,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,6CAA6C,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,IAAI,MAAsB,CAAC;QAE3B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,uCAAc,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAC3C,MAAM,CAAC,WAAW,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wBAAwB,EAAE,GAAG,EAAE;YAChC,MAAM,WAAW,GAAG;gBAClB,OAAO,EAAE,OAAO;gBAChB,SAAS,EAAE,eAAe;aAC3B,CAAC;YAEF,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAEnC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACxD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,qBAAqB;YAC1F,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,iEAA0B,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,IAAI,MAAsB,CAAC;QAE3B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,uCAAc,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAC3C,MAAM,CAAC,WAAW,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gBAAgB,EAAE,GAAG,EAAE;YACxB,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAEzB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,iEAA0B,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,iBAAiB;YAExC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAEzB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;YAC/D,MAAM,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mBAAmB,EAAE,GAAG,EAAE;YAC3B,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAEzB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,iEAA0B,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yBAAyB,EAAE,GAAG,EAAE;YACjC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB;YAC7D,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACzC,IAAI,MAAsB,CAAC;QAE3B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,uCAAc,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAC3C,MAAM,CAAC,WAAW,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,OAAO,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YAC1C,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAEjC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9D,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,iEAA0B,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,OAAO,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YAC1C,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YACjC,MAAM,CAAC,WAAW,EAAE,CAAC;YAErB,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAEjC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,OAAO,GAAG,8CAAc,CAAC,QAAQ,EAAE,CAAC;YAC1C,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YACjC,MAAM,CAAC,WAAW,EAAE,CAAC;YAErB,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YAEpC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,iEAA0B,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,IAAI,MAAsB,CAAC;QAE3B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,uCAAc,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YAClC,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAExC,MAAM,CAAC,eAAe,EAAE,CAAC;YACzB,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAExC,MAAM,CAAC,QAAQ,CAAC,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE1C,MAAM,CAAC,eAAe,EAAE,CAAC;YACzB,MAAM,CAAC,QAAQ,CAAC,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAC5C,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,CAAC,eAAe,EAAE,CAAC;YACzB,MAAM,CAAC,QAAQ,CAAC,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAE5C,MAAM,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;YACnC,MAAM,CAAC,eAAe,EAAE,CAAC;YAEzB,MAAM,cAAc,GAAiB;gBACnC,IAAI,EAAE,iBAAiB;gBACvB,OAAO,EAAE,iBAAiB;gBAC1B,OAAO,EAAE,EAAE;gBACX,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,sCAAa,CAAC,aAAa;aACtC,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC5B,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAExC,MAAM,iBAAiB,GAAiB;gBACtC,IAAI,EAAE,qBAAqB;gBAC3B,OAAO,EAAE,qBAAqB;gBAC9B,OAAO,EAAE,EAAE;gBACX,SAAS,EAAE,KAAK;gBAChB,QAAQ,EAAE,sCAAa,CAAC,gBAAgB;aACzC,CAAC;YAEF,MAAM,OAAO,GAAG,uCAAc,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAClD,OAAO,CAAC,eAAe,EAAE,CAAC;YAC1B,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,IAAI,MAAsB,CAAC;QAE3B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,uCAAc,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,CAAC,eAAe,EAAE,CAAC;YAEzB,4CAA4C;YAC5C,UAAU,CAAC,GAAG,EAAE;gBACd,MAAM,CAAC,QAAQ,CAAC,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;gBAC5C,MAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;gBAEtC,MAAM,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,EAAE,EAAE,CAAC,CAAC;QACT,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;YAClE,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,YAAY,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC;YAE9C,MAAM,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,YAAY,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAC5B,MAAM,CAAC,eAAe,EAAE,CAAC;YACzB,MAAM,CAAC,QAAQ,CAAC,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAE5C,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;YAEpC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC9C,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACjD,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACnD,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAC3D,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,IAAI,MAAsB,CAAC;QAE3B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,GAAG,uCAAc,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;YACzB,MAAM,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CAAC;YACzC,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;YACnC,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;YAEjC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACtB,QAAQ,CAAC,IAAI,CAAC,8CAAc,CAAC,QAAQ,EAAE,CAAC,CAAC;YACzC,SAAS,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YAC/B,QAAQ,CAAC,OAAO,GAAG,UAAU,CAAC;YAE9B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,aAAa,EAAE,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\entities\\__tests__\\analysis-result.entity.spec.ts"], "sourcesContent": ["import { UniqueEntityId } from '../../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { \r\n  AnalysisResult, \r\n  AnalysisType,\r\n  AnalysisStatus,\r\n  AnalysisInput,\r\n  AnalysisOutput,\r\n  AnalysisMetadata,\r\n  ErrorDetails,\r\n  ErrorCategory\r\n} from '../analysis-result.entity';\r\nimport { AnalysisResultCreatedEvent } from '../../events/analysis-result-created.domain-event';\r\nimport { AnalysisResultStatusChangedEvent } from '../../events/analysis-result-status-changed.domain-event';\r\nimport { AnalysisResultUpdatedEvent } from '../../events/analysis-result-updated.domain-event';\r\n\r\ndescribe('AnalysisResult Entity', () => {\r\n  let validProps: Omit<any, 'createdAt' | 'updatedAt' | 'childAnalysisIds'>;\r\n  let mockInputData: AnalysisInput;\r\n  let mockOutputData: AnalysisOutput;\r\n  let mockMetadata: AnalysisMetadata;\r\n\r\n  beforeEach(() => {\r\n    mockInputData = {\r\n      data: { test: 'input' },\r\n      format: 'json',\r\n      size: 100,\r\n      checksum: 'input-checksum',\r\n      preprocessingApplied: ['normalization'],\r\n      validationRules: ['required'],\r\n    };\r\n\r\n    mockOutputData = {\r\n      results: { prediction: 'test-result' },\r\n      format: 'json',\r\n      size: 200,\r\n      checksum: 'output-checksum',\r\n      postprocessingApplied: ['formatting'],\r\n      validationStatus: 'passed',\r\n      qualityScore: 0.9,\r\n    };\r\n\r\n    mockMetadata = {\r\n      version: '1.0.0',\r\n      algorithm: 'test-algorithm',\r\n      parameters: { param1: 'value1' },\r\n      environment: 'test',\r\n      resourceUsage: {\r\n        cpuTime: 1000,\r\n        memoryUsage: 512,\r\n        gpuTime: 500,\r\n        networkIO: 100,\r\n        diskIO: 50,\r\n      },\r\n      performanceMetrics: {\r\n        throughput: 10,\r\n        latency: 100,\r\n        accuracy: 0.95,\r\n        precision: 0.92,\r\n        recall: 0.88,\r\n        f1Score: 0.90,\r\n      },\r\n      qualityMetrics: {\r\n        dataQuality: 0.9,\r\n        resultReliability: 0.85,\r\n        consistencyScore: 0.8,\r\n        completenessScore: 0.95,\r\n      },\r\n    };\r\n\r\n    validProps = {\r\n      requestId: 'test-request-123',\r\n      modelId: UniqueEntityId.generate(),\r\n      analysisType: AnalysisType.CLASSIFICATION,\r\n      inputData: mockInputData,\r\n      outputData: mockOutputData,\r\n      confidence: 0.85,\r\n      processingTime: 1500,\r\n      status: AnalysisStatus.PENDING,\r\n      metadata: mockMetadata,\r\n      tags: ['test', 'classification'],\r\n      correlationId: 'correlation-123',\r\n      parentAnalysisId: UniqueEntityId.generate(),\r\n      errorDetails: undefined,\r\n      completedAt: undefined,\r\n    };\r\n  });\r\n\r\n  describe('Creation', () => {\r\n    it('should create a valid analysis result', () => {\r\n      const result = AnalysisResult.create(validProps);\r\n\r\n      expect(result).toBeInstanceOf(AnalysisResult);\r\n      expect(result.requestId).toBe(validProps.requestId);\r\n      expect(result.modelId.equals(validProps.modelId)).toBe(true);\r\n      expect(result.analysisType).toBe(validProps.analysisType);\r\n      expect(result.status).toBe(validProps.status);\r\n      expect(result.confidence).toBe(validProps.confidence);\r\n      expect(result.childAnalysisIds).toEqual([]);\r\n      expect(result.createdAt).toBeInstanceOf(Date);\r\n      expect(result.updatedAt).toBeInstanceOf(Date);\r\n    });\r\n\r\n    it('should generate a domain event when created', () => {\r\n      const result = AnalysisResult.create(validProps);\r\n      const events = result.domainEvents;\r\n\r\n      expect(events).toHaveLength(1);\r\n      expect(events[0]).toBeInstanceOf(AnalysisResultCreatedEvent);\r\n      expect((events[0] as AnalysisResultCreatedEvent).requestId).toBe(validProps.requestId);\r\n    });\r\n\r\n    it('should create with custom ID', () => {\r\n      const customId = UniqueEntityId.generate();\r\n      const result = AnalysisResult.create(validProps, customId);\r\n\r\n      expect(result.id.equals(customId)).toBe(true);\r\n    });\r\n\r\n    it('should throw error for empty request ID', () => {\r\n      const invalidProps = { ...validProps, requestId: '' };\r\n\r\n      expect(() => AnalysisResult.create(invalidProps)).toThrow('Request ID is required');\r\n    });\r\n\r\n    it('should throw error for missing model ID', () => {\r\n      const invalidProps = { ...validProps, modelId: null as any };\r\n\r\n      expect(() => AnalysisResult.create(invalidProps)).toThrow('Model ID is required');\r\n    });\r\n\r\n    it('should throw error for invalid analysis type', () => {\r\n      const invalidProps = { ...validProps, analysisType: 'invalid' as AnalysisType };\r\n\r\n      expect(() => AnalysisResult.create(invalidProps)).toThrow('Invalid analysis type');\r\n    });\r\n\r\n    it('should throw error for invalid confidence', () => {\r\n      const invalidProps = { ...validProps, confidence: 1.5 };\r\n\r\n      expect(() => AnalysisResult.create(invalidProps)).toThrow('Confidence must be between 0 and 1');\r\n    });\r\n\r\n    it('should throw error for negative processing time', () => {\r\n      const invalidProps = { ...validProps, processingTime: -100 };\r\n\r\n      expect(() => AnalysisResult.create(invalidProps)).toThrow('Processing time cannot be negative');\r\n    });\r\n\r\n    it('should throw error for missing input data', () => {\r\n      const invalidProps = { ...validProps, inputData: null as any };\r\n\r\n      expect(() => AnalysisResult.create(invalidProps)).toThrow('Input data is required');\r\n    });\r\n\r\n    it('should throw error for missing metadata', () => {\r\n      const invalidProps = { ...validProps, metadata: null as any };\r\n\r\n      expect(() => AnalysisResult.create(invalidProps)).toThrow('Metadata is required');\r\n    });\r\n  });\r\n\r\n  describe('Reconstitution', () => {\r\n    it('should reconstitute from valid props', () => {\r\n      const id = UniqueEntityId.generate();\r\n      const now = new Date();\r\n      const props = {\r\n        ...validProps,\r\n        childAnalysisIds: [UniqueEntityId.generate()],\r\n        createdAt: now,\r\n        updatedAt: now,\r\n      };\r\n\r\n      const result = AnalysisResult.reconstitute(props, id);\r\n\r\n      expect(result.id.equals(id)).toBe(true);\r\n      expect(result.requestId).toBe(props.requestId);\r\n      expect(result.childAnalysisIds).toHaveLength(1);\r\n      expect(result.createdAt).toBe(now);\r\n      expect(result.updatedAt).toBe(now);\r\n    });\r\n  });\r\n\r\n  describe('Status Management', () => {\r\n    let result: AnalysisResult;\r\n\r\n    beforeEach(() => {\r\n      result = AnalysisResult.create(validProps);\r\n      result.clearEvents(); // Clear creation event\r\n    });\r\n\r\n    it('should start processing from pending status', () => {\r\n      result.startProcessing();\r\n\r\n      expect(result.status).toBe(AnalysisStatus.PROCESSING);\r\n      expect(result.domainEvents).toHaveLength(1);\r\n      expect(result.domainEvents[0]).toBeInstanceOf(AnalysisResultStatusChangedEvent);\r\n    });\r\n\r\n    it('should not start processing from non-pending status', () => {\r\n      // First start processing\r\n      result.startProcessing();\r\n      result.clearEvents();\r\n\r\n      expect(() => result.startProcessing()).toThrow('Cannot start processing analysis in status: processing');\r\n    });\r\n\r\n    it('should complete analysis from processing status', () => {\r\n      result.startProcessing();\r\n      result.clearEvents();\r\n\r\n      result.complete(mockOutputData, 1500, 0.95);\r\n\r\n      expect(result.status).toBe(AnalysisStatus.COMPLETED);\r\n      expect(result.outputData).toEqual(mockOutputData);\r\n      expect(result.processingTime).toBe(1500);\r\n      expect(result.confidence).toBe(0.95);\r\n      expect(result.completedAt).toBeInstanceOf(Date);\r\n      expect(result.domainEvents).toHaveLength(1);\r\n      expect(result.domainEvents[0]).toBeInstanceOf(AnalysisResultStatusChangedEvent);\r\n    });\r\n\r\n    it('should not complete analysis from non-processing status', () => {\r\n      expect(() => result.complete(mockOutputData, 1500, 0.95)).toThrow(\r\n        'Cannot complete analysis in status: pending'\r\n      );\r\n    });\r\n\r\n    it('should fail analysis with error details', () => {\r\n      result.startProcessing();\r\n      result.clearEvents();\r\n\r\n      const errorDetails: ErrorDetails = {\r\n        code: 'TEST_ERROR',\r\n        message: 'Test error',\r\n        context: { test: true },\r\n        retryable: true,\r\n        category: ErrorCategory.PROCESSING_ERROR,\r\n      };\r\n\r\n      result.fail(errorDetails);\r\n\r\n      expect(result.status).toBe(AnalysisStatus.FAILED);\r\n      expect(result.errorDetails).toEqual(errorDetails);\r\n      expect(result.completedAt).toBeInstanceOf(Date);\r\n      expect(result.domainEvents).toHaveLength(1);\r\n      expect(result.domainEvents[0]).toBeInstanceOf(AnalysisResultStatusChangedEvent);\r\n    });\r\n\r\n    it('should not fail completed analysis', () => {\r\n      result.startProcessing();\r\n      result.complete(mockOutputData, 1500, 0.95);\r\n\r\n      const errorDetails: ErrorDetails = {\r\n        code: 'TEST_ERROR',\r\n        message: 'Test error',\r\n        context: {},\r\n        retryable: false,\r\n        category: ErrorCategory.PROCESSING_ERROR,\r\n      };\r\n\r\n      expect(() => result.fail(errorDetails)).toThrow('Cannot fail a completed analysis');\r\n    });\r\n\r\n    it('should cancel analysis', () => {\r\n      result.cancel();\r\n\r\n      expect(result.status).toBe(AnalysisStatus.CANCELLED);\r\n      expect(result.completedAt).toBeInstanceOf(Date);\r\n      expect(result.domainEvents).toHaveLength(1);\r\n      expect(result.domainEvents[0]).toBeInstanceOf(AnalysisResultStatusChangedEvent);\r\n    });\r\n\r\n    it('should not cancel completed or failed analysis', () => {\r\n      result.startProcessing();\r\n      result.complete(mockOutputData, 1500, 0.95);\r\n\r\n      expect(() => result.cancel()).toThrow('Cannot cancel analysis in status: completed');\r\n    });\r\n  });\r\n\r\n  describe('Metadata Management', () => {\r\n    let result: AnalysisResult;\r\n\r\n    beforeEach(() => {\r\n      result = AnalysisResult.create(validProps);\r\n      result.clearEvents();\r\n    });\r\n\r\n    it('should update metadata', () => {\r\n      const newMetadata = {\r\n        version: '2.0.0',\r\n        algorithm: 'new-algorithm',\r\n      };\r\n\r\n      result.updateMetadata(newMetadata);\r\n\r\n      expect(result.metadata.version).toBe('2.0.0');\r\n      expect(result.metadata.algorithm).toBe('new-algorithm');\r\n      expect(result.metadata.parameters).toEqual(mockMetadata.parameters); // Original preserved\r\n      expect(result.domainEvents).toHaveLength(1);\r\n      expect(result.domainEvents[0]).toBeInstanceOf(AnalysisResultUpdatedEvent);\r\n    });\r\n  });\r\n\r\n  describe('Tag Management', () => {\r\n    let result: AnalysisResult;\r\n\r\n    beforeEach(() => {\r\n      result = AnalysisResult.create(validProps);\r\n      result.clearEvents();\r\n    });\r\n\r\n    it('should add tag', () => {\r\n      result.addTag('new-tag');\r\n\r\n      expect(result.tags).toContain('new-tag');\r\n      expect(result.domainEvents).toHaveLength(1);\r\n      expect(result.domainEvents[0]).toBeInstanceOf(AnalysisResultUpdatedEvent);\r\n    });\r\n\r\n    it('should not add duplicate tag', () => {\r\n      const initialLength = result.tags.length;\r\n      result.addTag('test'); // Already exists\r\n\r\n      expect(result.tags).toHaveLength(initialLength);\r\n      expect(result.domainEvents).toHaveLength(0);\r\n    });\r\n\r\n    it('should normalize tag case', () => {\r\n      result.addTag('NEW-TAG');\r\n\r\n      expect(result.tags).toContain('new-tag');\r\n    });\r\n\r\n    it('should throw error for empty tag', () => {\r\n      expect(() => result.addTag('')).toThrow('Tag cannot be empty');\r\n      expect(() => result.addTag('   ')).toThrow('Tag cannot be empty');\r\n    });\r\n\r\n    it('should remove tag', () => {\r\n      result.removeTag('test');\r\n\r\n      expect(result.tags).not.toContain('test');\r\n      expect(result.domainEvents).toHaveLength(1);\r\n      expect(result.domainEvents[0]).toBeInstanceOf(AnalysisResultUpdatedEvent);\r\n    });\r\n\r\n    it('should check if has tag', () => {\r\n      expect(result.hasTag('test')).toBe(true);\r\n      expect(result.hasTag('TEST')).toBe(true); // Case insensitive\r\n      expect(result.hasTag('nonexistent')).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('Child Analysis Management', () => {\r\n    let result: AnalysisResult;\r\n\r\n    beforeEach(() => {\r\n      result = AnalysisResult.create(validProps);\r\n      result.clearEvents();\r\n    });\r\n\r\n    it('should add child analysis', () => {\r\n      const childId = UniqueEntityId.generate();\r\n      result.addChildAnalysis(childId);\r\n\r\n      expect(result.childAnalysisIds).toHaveLength(1);\r\n      expect(result.childAnalysisIds[0].equals(childId)).toBe(true);\r\n      expect(result.domainEvents).toHaveLength(1);\r\n      expect(result.domainEvents[0]).toBeInstanceOf(AnalysisResultUpdatedEvent);\r\n    });\r\n\r\n    it('should not add duplicate child analysis', () => {\r\n      const childId = UniqueEntityId.generate();\r\n      result.addChildAnalysis(childId);\r\n      result.clearEvents();\r\n\r\n      result.addChildAnalysis(childId);\r\n\r\n      expect(result.childAnalysisIds).toHaveLength(1);\r\n      expect(result.domainEvents).toHaveLength(0);\r\n    });\r\n\r\n    it('should remove child analysis', () => {\r\n      const childId = UniqueEntityId.generate();\r\n      result.addChildAnalysis(childId);\r\n      result.clearEvents();\r\n\r\n      result.removeChildAnalysis(childId);\r\n\r\n      expect(result.childAnalysisIds).toHaveLength(0);\r\n      expect(result.domainEvents).toHaveLength(1);\r\n      expect(result.domainEvents[0]).toBeInstanceOf(AnalysisResultUpdatedEvent);\r\n    });\r\n  });\r\n\r\n  describe('Status Checks', () => {\r\n    let result: AnalysisResult;\r\n\r\n    beforeEach(() => {\r\n      result = AnalysisResult.create(validProps);\r\n    });\r\n\r\n    it('should check if terminal', () => {\r\n      expect(result.isTerminal()).toBe(false);\r\n\r\n      result.startProcessing();\r\n      expect(result.isTerminal()).toBe(false);\r\n\r\n      result.complete(mockOutputData, 1500, 0.95);\r\n      expect(result.isTerminal()).toBe(true);\r\n    });\r\n\r\n    it('should check if successful', () => {\r\n      expect(result.isSuccessful()).toBe(false);\r\n\r\n      result.startProcessing();\r\n      result.complete(mockOutputData, 1500, 0.95);\r\n      expect(result.isSuccessful()).toBe(true);\r\n    });\r\n\r\n    it('should check high confidence', () => {\r\n      result.startProcessing();\r\n      result.complete(mockOutputData, 1500, 0.95);\r\n\r\n      expect(result.hasHighConfidence()).toBe(true);\r\n      expect(result.hasHighConfidence(0.98)).toBe(false);\r\n    });\r\n\r\n    it('should check if retryable', () => {\r\n      result.startProcessing();\r\n      \r\n      const retryableError: ErrorDetails = {\r\n        code: 'RETRYABLE_ERROR',\r\n        message: 'Retryable error',\r\n        context: {},\r\n        retryable: true,\r\n        category: ErrorCategory.NETWORK_ERROR,\r\n      };\r\n\r\n      result.fail(retryableError);\r\n      expect(result.isRetryable()).toBe(true);\r\n\r\n      const nonRetryableError: ErrorDetails = {\r\n        code: 'NON_RETRYABLE_ERROR',\r\n        message: 'Non-retryable error',\r\n        context: {},\r\n        retryable: false,\r\n        category: ErrorCategory.INPUT_VALIDATION,\r\n      };\r\n\r\n      const result2 = AnalysisResult.create(validProps);\r\n      result2.startProcessing();\r\n      result2.fail(nonRetryableError);\r\n      expect(result2.isRetryable()).toBe(false);\r\n    });\r\n  });\r\n\r\n  describe('Utility Methods', () => {\r\n    let result: AnalysisResult;\r\n\r\n    beforeEach(() => {\r\n      result = AnalysisResult.create(validProps);\r\n    });\r\n\r\n    it('should get duration for completed analysis', () => {\r\n      result.startProcessing();\r\n      \r\n      // Wait a bit to ensure different timestamps\r\n      setTimeout(() => {\r\n        result.complete(mockOutputData, 1500, 0.95);\r\n        const duration = result.getDuration();\r\n        \r\n        expect(duration).toBeGreaterThan(0);\r\n      }, 10);\r\n    });\r\n\r\n    it('should return undefined duration for incomplete analysis', () => {\r\n      expect(result.getDuration()).toBeUndefined();\r\n    });\r\n\r\n    it('should calculate quality score', () => {\r\n      const qualityScore = result.getQualityScore();\r\n\r\n      expect(qualityScore).toBeGreaterThan(0);\r\n      expect(qualityScore).toBeLessThanOrEqual(1);\r\n    });\r\n\r\n    it('should get summary', () => {\r\n      result.startProcessing();\r\n      result.complete(mockOutputData, 1500, 0.95);\r\n\r\n      const summary = result.getSummary();\r\n\r\n      expect(summary.id).toBe(result.id.toString());\r\n      expect(summary.requestId).toBe(result.requestId);\r\n      expect(summary.analysisType).toBe(result.analysisType);\r\n      expect(summary.status).toBe(result.status);\r\n      expect(summary.confidence).toBe(result.confidence);\r\n      expect(summary.processingTime).toBe(result.processingTime);\r\n      expect(summary.isSuccessful).toBe(true);\r\n      expect(summary.qualityScore).toBeGreaterThan(0);\r\n    });\r\n  });\r\n\r\n  describe('Immutability', () => {\r\n    let result: AnalysisResult;\r\n\r\n    beforeEach(() => {\r\n      result = AnalysisResult.create(validProps);\r\n    });\r\n\r\n    it('should return copies of arrays and objects', () => {\r\n      const tags = result.tags;\r\n      const childIds = result.childAnalysisIds;\r\n      const inputData = result.inputData;\r\n      const metadata = result.metadata;\r\n\r\n      tags.push('modified');\r\n      childIds.push(UniqueEntityId.generate());\r\n      inputData.data.modified = true;\r\n      metadata.version = 'modified';\r\n\r\n      expect(result.tags).not.toContain('modified');\r\n      expect(result.childAnalysisIds).toHaveLength(0);\r\n      expect(result.inputData.data.modified).toBeUndefined();\r\n      expect(result.metadata.version).toBe('1.0.0');\r\n    });\r\n  });\r\n});"], "version": 3}