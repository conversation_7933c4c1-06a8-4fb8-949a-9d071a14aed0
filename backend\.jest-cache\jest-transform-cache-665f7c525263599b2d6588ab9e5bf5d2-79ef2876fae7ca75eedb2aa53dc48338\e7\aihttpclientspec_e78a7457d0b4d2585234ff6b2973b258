27637a157053c415064d5e3141917c5f
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// Mock axios
jest.mock('axios');
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const ai_http_client_1 = require("../ai-http.client");
const axios_1 = __importDefault(require("axios"));
const mockedAxios = axios_1.default;
describe('AiHttpClient', () => {
    let client;
    let configService;
    let mockAxiosInstance;
    const mockAxiosResponse = (data, status = 200) => ({
        data,
        status,
        statusText: 'OK',
        headers: {},
        config: {},
    });
    const mockAxiosError = (status, message) => ({
        name: 'AxiosError',
        message,
        response: {
            status,
            statusText: message,
            data: { message },
            headers: {},
            config: {},
        },
        config: {},
        isAxiosError: true,
        toJSON: () => ({}),
    });
    beforeEach(async () => {
        // Reset mocks
        jest.clearAllMocks();
        // Mock axios instance
        mockAxiosInstance = {
            request: jest.fn(),
        };
        mockedAxios.create.mockReturnValue(mockAxiosInstance);
        const mockConfigService = {
            get: jest.fn(),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                ai_http_client_1.AiHttpClient,
                { provide: config_1.ConfigService, useValue: mockConfigService },
            ],
        }).compile();
        client = module.get(ai_http_client_1.AiHttpClient);
        configService = module.get(config_1.ConfigService);
        // Setup default config values
        configService.get.mockImplementation((key, defaultValue) => {
            const config = {
                'ai.http.timeout': 30000,
                'ai.http.retries': 3,
            };
            return config[key] || defaultValue;
        });
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('sendAnalysisRequest', () => {
        it('should send analysis request successfully', async () => {
            const mockResponse = {
                id: 'analysis-123',
                result: { threat_level: 'high', confidence: 0.95 },
                confidence: 0.95,
                model: 'threat-detector-v1',
                timestamp: new Date().toISOString(),
            };
            mockAxiosInstance.request.mockResolvedValue(mockAxiosResponse(mockResponse));
            const payload = {
                data: { event: 'suspicious_login' },
                model: 'threat-detector-v1',
            };
            const result = await client.sendAnalysisRequest('http://ai-service/analyze', payload);
            expect(result).toMatchObject({
                id: expect.any(String),
                result: { threat_level: 'high', confidence: 0.95 },
                confidence: 0.95,
                metadata: expect.objectContaining({
                    model: 'threat-detector-v1',
                }),
                timestamp: expect.any(Date),
            });
            expect(mockAxiosInstance.request).toHaveBeenCalledWith(expect.objectContaining({
                method: 'POST',
                url: 'http://ai-service/analyze',
                data: payload,
                timeout: 30000,
                headers: expect.objectContaining({
                    'Content-Type': 'application/json',
                    'User-Agent': 'Sentinel-AI-Client/1.0',
                    'X-Request-ID': expect.any(String),
                }),
            }));
        });
        it('should handle analysis request failure', async () => {
            const error = mockAxiosError(500, 'Internal Server Error');
            mockAxiosInstance.request.mockRejectedValue(error);
            const payload = { data: { event: 'test' } };
            await expect(client.sendAnalysisRequest('http://ai-service/analyze', payload)).rejects.toThrow('Analysis request failed');
        });
        it('should use custom timeout and headers', async () => {
            const mockResponse = { id: 'test', result: {}, confidence: 0.8 };
            mockAxiosInstance.request.mockResolvedValue(mockAxiosResponse(mockResponse));
            const payload = { data: { event: 'test' } };
            const options = {
                timeout: 60000,
                apiKey: 'test-key',
                customHeaders: { 'X-Custom': 'value' },
            };
            await client.sendAnalysisRequest('http://ai-service/analyze', payload, options);
            expect(mockAxiosInstance.request).toHaveBeenCalledWith(expect.objectContaining({
                timeout: 60000,
                headers: expect.objectContaining({
                    'Authorization': 'Bearer test-key',
                    'X-Custom': 'value',
                }),
            }));
        });
    });
    describe('sendTrainingRequest', () => {
        it('should send training request successfully', async () => {
            const mockResponse = {
                jobId: 'job-123',
                status: 'completed',
                modelId: 'model-456',
                metrics: { accuracy: 0.95 },
                timestamp: new Date().toISOString(),
            };
            mockAxiosInstance.request.mockResolvedValue(mockAxiosResponse(mockResponse));
            const payload = {
                trainingData: [{ input: 'test', output: 'result' }],
                modelConfig: { type: 'classifier' },
            };
            const result = await client.sendTrainingRequest('http://ai-service/train', payload);
            expect(result).toMatchObject({
                jobId: 'job-123',
                status: 'completed',
                modelId: 'model-456',
                metrics: { accuracy: 0.95 },
                timestamp: expect.any(Date),
            });
            expect(mockAxiosInstance.request).toHaveBeenCalledWith(expect.objectContaining({
                method: 'POST',
                url: 'http://ai-service/train',
                data: payload,
                timeout: 300000, // 5 minutes default for training
            }));
        });
        it('should handle training request failure', async () => {
            const error = mockAxiosError(400, 'Invalid training data');
            mockAxiosInstance.request.mockRejectedValue(error);
            const payload = {
                trainingData: [],
                modelConfig: { type: 'classifier' },
            };
            await expect(client.sendTrainingRequest('http://ai-service/train', payload)).rejects.toThrow('Training request failed');
        });
    });
    describe('sendPredictionRequest', () => {
        it('should send prediction request successfully', async () => {
            const mockResponse = {
                prediction: 'malicious',
                confidence: 0.87,
                alternatives: ['benign'],
                model: 'classifier-v2',
                latency: 150,
                timestamp: new Date().toISOString(),
            };
            mockAxiosInstance.request.mockResolvedValue(mockAxiosResponse(mockResponse));
            const payload = {
                input: { features: [1, 2, 3] },
                model: 'classifier-v2',
            };
            const result = await client.sendPredictionRequest('http://ai-service/predict', payload);
            expect(result).toMatchObject({
                prediction: 'malicious',
                confidence: 0.87,
                alternatives: ['benign'],
                metadata: expect.objectContaining({
                    model: 'classifier-v2',
                    latency: 150,
                }),
                timestamp: expect.any(Date),
            });
            expect(mockAxiosInstance.request).toHaveBeenCalledWith(expect.objectContaining({
                method: 'POST',
                url: 'http://ai-service/predict',
                data: payload,
                timeout: 5000, // 5 seconds default for predictions
            }));
        });
        it('should handle prediction request failure', async () => {
            const error = mockAxiosError(422, 'Invalid input format');
            mockAxiosInstance.request.mockRejectedValue(error);
            const payload = { input: null };
            await expect(client.sendPredictionRequest('http://ai-service/predict', payload)).rejects.toThrow('Prediction request failed');
        });
    });
    describe('sendHealthCheckRequest', () => {
        it('should send health check request successfully', async () => {
            const mockResponse = {
                healthy: true,
                status: 'operational',
                responseTime: 45,
                version: '1.2.3',
            };
            mockAxiosInstance.request.mockResolvedValue(mockAxiosResponse(mockResponse));
            const result = await client.sendHealthCheckRequest('http://ai-service');
            expect(result).toMatchObject({
                healthy: true,
                status: 'operational',
                responseTime: 45,
                version: '1.2.3',
                timestamp: expect.any(Date),
            });
            expect(mockAxiosInstance.request).toHaveBeenCalledWith(expect.objectContaining({
                method: 'GET',
                url: 'http://ai-service/health',
                timeout: 5000,
            }));
        });
        it('should handle health check failure gracefully', async () => {
            const error = mockAxiosError(503, 'Service Unavailable');
            mockAxiosInstance.request.mockRejectedValue(error);
            const result = await client.sendHealthCheckRequest('http://ai-service');
            expect(result).toMatchObject({
                healthy: false,
                status: 'unhealthy',
                error: expect.any(String),
                timestamp: expect.any(Date),
            });
        });
        it('should consider 200 status as healthy when no explicit healthy field', async () => {
            const mockResponse = { status: 'ok' };
            mockAxiosInstance.request.mockResolvedValue(mockAxiosResponse(mockResponse, 200));
            const result = await client.sendHealthCheckRequest('http://ai-service');
            expect(result.healthy).toBe(true);
        });
    });
    describe('sendBatchRequest', () => {
        it('should send batch request successfully', async () => {
            const mockResponse = {
                batchId: 'batch-123',
                results: [
                    { id: '1', result: 'success' },
                    { id: '2', result: 'success' },
                ],
                total: 2,
                successful: 2,
                failed: 0,
                processingTime: 1500,
                timestamp: new Date().toISOString(),
            };
            mockAxiosInstance.request.mockResolvedValue(mockAxiosResponse(mockResponse));
            const payloads = [
                { id: '1', data: { input: 'test1' }, type: 'analysis' },
                { id: '2', data: { input: 'test2' }, type: 'analysis' },
            ];
            const result = await client.sendBatchRequest('http://ai-service/batch', payloads);
            expect(result).toMatchObject({
                batchId: 'batch-123',
                results: expect.arrayContaining([
                    { id: '1', result: 'success' },
                    { id: '2', result: 'success' },
                ]),
                summary: {
                    total: 2,
                    successful: 2,
                    failed: 0,
                    processingTime: 1500,
                },
                timestamp: expect.any(Date),
            });
            expect(mockAxiosInstance.request).toHaveBeenCalledWith(expect.objectContaining({
                method: 'POST',
                url: 'http://ai-service/batch',
                data: expect.objectContaining({
                    requests: payloads,
                    batchId: expect.any(String),
                    timestamp: expect.any(String),
                }),
                timeout: 120000, // 2 minutes default for batch
            }));
        });
        it('should handle batch request failure', async () => {
            const error = mockAxiosError(413, 'Payload Too Large');
            mockAxiosInstance.request.mockRejectedValue(error);
            const payloads = [{ id: '1', data: {}, type: 'test' }];
            await expect(client.sendBatchRequest('http://ai-service/batch', payloads)).rejects.toThrow('Batch request failed');
        });
    });
    describe('uploadFile', () => {
        it('should upload file successfully', async () => {
            const mockResponse = {
                fileId: 'file-123',
                filename: 'test.csv',
                size: 1024,
                url: 'http://storage/file-123',
            };
            mockAxiosInstance.request.mockResolvedValue(mockAxiosResponse(mockResponse));
            const fileBuffer = Buffer.from('test,data\n1,2\n3,4');
            const result = await client.uploadFile('http://ai-service/upload', fileBuffer, 'test.csv');
            expect(result).toEqual(mockResponse);
            expect(mockAxiosInstance.request).toHaveBeenCalledWith(expect.objectContaining({
                method: 'POST',
                url: 'http://ai-service/upload',
                data: expect.any(FormData),
                timeout: 60000,
                headers: expect.objectContaining({
                    'Content-Type': 'multipart/form-data',
                }),
            }));
        });
        it('should handle file upload failure', async () => {
            const error = mockAxiosError(413, 'File Too Large');
            mockAxiosInstance.request.mockRejectedValue(error);
            const fileBuffer = Buffer.from('test data');
            await expect(client.uploadFile('http://ai-service/upload', fileBuffer, 'test.txt')).rejects.toThrow('File upload failed');
        });
    });
    describe('downloadFile', () => {
        it('should download file successfully', async () => {
            const fileData = new ArrayBuffer(1024);
            const mockResponse = mockAxiosResponse(fileData);
            mockResponse.headers = {
                'content-type': 'application/octet-stream',
                'content-disposition': 'attachment; filename="downloaded.bin"',
            };
            mockAxiosInstance.request.mockResolvedValue(mockResponse);
            const result = await client.downloadFile('http://ai-service/files', 'file-123');
            expect(result).toMatchObject({
                data: expect.any(Buffer),
                filename: 'downloaded.bin',
                contentType: 'application/octet-stream',
                size: 1024,
            });
            expect(mockAxiosInstance.request).toHaveBeenCalledWith(expect.objectContaining({
                method: 'GET',
                url: 'http://ai-service/files/file-123',
                responseType: 'arraybuffer',
                timeout: 60000,
            }));
        });
        it('should handle file download failure', async () => {
            const error = mockAxiosError(404, 'File Not Found');
            mockAxiosInstance.request.mockRejectedValue(error);
            await expect(client.downloadFile('http://ai-service/files', 'nonexistent')).rejects.toThrow('File download failed');
        });
        it('should use default filename when content-disposition header is missing', async () => {
            const fileData = new ArrayBuffer(512);
            const mockResponse = mockAxiosResponse(fileData);
            mockResponse.headers = { 'content-type': 'text/plain' };
            mockAxiosInstance.request.mockResolvedValue(mockResponse);
            const result = await client.downloadFile('http://ai-service/files', 'file-123');
            expect(result.filename).toBe('download');
        });
    });
    describe('streamRequest', () => {
        it('should handle stream request successfully', async () => {
            const mockStream = new ReadableStream();
            mockAxiosInstance.request.mockResolvedValue(mockAxiosResponse(mockStream));
            const payload = { data: 'streaming test' };
            const result = await client.streamRequest('http://ai-service/stream', payload);
            expect(result).toBe(mockStream);
            expect(mockAxiosInstance.request).toHaveBeenCalledWith(expect.objectContaining({
                method: 'POST',
                url: 'http://ai-service/stream',
                data: payload,
                responseType: 'stream',
            }));
        });
        it('should handle stream request failure', async () => {
            const error = mockAxiosError(500, 'Stream Error');
            mockAxiosInstance.request.mockRejectedValue(error);
            const payload = { data: 'test' };
            await expect(client.streamRequest('http://ai-service/stream', payload)).rejects.toThrow('Stream request failed');
        });
    });
    describe('error handling', () => {
        it('should handle timeout errors', async () => {
            const timeoutError = new Error('timeout of 30000ms exceeded');
            timeoutError['code'] = 'ECONNABORTED';
            mockAxiosInstance.request.mockRejectedValue(timeoutError);
            await expect(client.sendAnalysisRequest('http://ai-service/analyze', { data: {} })).rejects.toThrow('Request timeout');
        });
        it('should handle connection refused errors', async () => {
            const connectionError = new Error('connect ECONNREFUSED');
            connectionError['code'] = 'ECONNREFUSED';
            mockAxiosInstance.request.mockRejectedValue(connectionError);
            await expect(client.sendAnalysisRequest('http://ai-service/analyze', { data: {} })).rejects.toThrow('Connection refused');
        });
        it('should handle unknown errors', async () => {
            const unknownError = new Error('Unknown error');
            mockAxiosInstance.request.mockRejectedValue(unknownError);
            await expect(client.sendAnalysisRequest('http://ai-service/analyze', { data: {} })).rejects.toThrow('Unknown error');
        });
    });
    describe('request configuration', () => {
        it('should generate unique request IDs', async () => {
            const mockResponse = { id: 'test', result: {}, confidence: 0.5 };
            mockAxiosInstance.request.mockResolvedValue(mockAxiosResponse(mockResponse));
            await client.sendAnalysisRequest('http://ai-service/analyze', { data: {} });
            await client.sendAnalysisRequest('http://ai-service/analyze', { data: {} });
            const calls = mockAxiosInstance.request.mock.calls;
            const requestId1 = calls[0][0].headers['X-Request-ID'];
            const requestId2 = calls[1][0].headers['X-Request-ID'];
            expect(requestId1).toBeDefined();
            expect(requestId2).toBeDefined();
            expect(requestId1).not.toBe(requestId2);
        });
        it('should use configured timeout and retries', async () => {
            configService.get.mockImplementation((key, defaultValue) => {
                const config = {
                    'ai.http.timeout': 45000,
                    'ai.http.retries': 5,
                };
                return config[key] || defaultValue;
            });
            // Create new instance to pick up new config
            const newClient = new ai_http_client_1.AiHttpClient(configService);
            const mockResponse = { id: 'test', result: {}, confidence: 0.5 };
            mockAxiosInstance.request.mockResolvedValue(mockAxiosResponse(mockResponse));
            await newClient.sendAnalysisRequest('http://ai-service/analyze', { data: {} });
            expect(mockAxiosInstance.request).toHaveBeenCalledWith(expect.objectContaining({
                timeout: 45000,
            }));
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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