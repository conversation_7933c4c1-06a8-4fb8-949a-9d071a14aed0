acdd6c83666469eef0475235d1d794b9
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationDashboardController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../../auth/guards/roles.guard");
const roles_decorator_1 = require("../../auth/decorators/roles.decorator");
const current_user_decorator_1 = require("../../auth/decorators/current-user.decorator");
const logging_interceptor_1 = require("../../common/interceptors/logging.interceptor");
const cache_interceptor_1 = require("../../common/interceptors/cache.interceptor");
const notification_dashboard_service_1 = require("../services/notification-dashboard.service");
const dashboard_query_dto_1 = require("../dto/dashboard-query.dto");
const user_entity_1 = require("../../auth/entities/user.entity");
/**
 * Notification Dashboard Controller
 *
 * Provides comprehensive REST API endpoints for notification monitoring dashboards including:
 * - Real-time dashboard data aggregation with live metrics and status updates
 * - Health status dashboard APIs with provider monitoring and circuit breaker status
 * - Cost analytics dashboard endpoints with budget tracking and optimization insights
 * - Performance metrics APIs with trend analysis and comparative benchmarking
 * - Alert dashboard endpoints with real-time status updates and escalation tracking
 *
 * Features:
 * - Real-time data aggregation with WebSocket integration
 * - Multi-dimensional analytics with flexible filtering and grouping
 * - Performance optimization with intelligent caching and data compression
 * - Role-based access control with granular dashboard permissions
 * - Export capabilities with multiple format support
 * - Integration with monitoring infrastructure and alerting systems
 */
let NotificationDashboardController = class NotificationDashboardController {
    constructor(dashboardService) {
        this.dashboardService = dashboardService;
    }
    /**
     * Get dashboard overview with key metrics
     */
    async getDashboardOverview(query, user) {
        return await this.dashboardService.getDashboardOverview(query, user);
    }
    /**
     * Get real-time metrics
     */
    async getRealTimeMetrics(metrics, interval = '30s', user) {
        return await this.dashboardService.getRealTimeMetrics(metrics, interval, user);
    }
    /**
     * Get provider health dashboard
     */
    async getProviderHealthDashboard(includeHistory = false, timeRange = '24h', user) {
        return await this.dashboardService.getProviderHealthDashboard(includeHistory, timeRange, user);
    }
    /**
     * Get cost analytics dashboard
     */
    async getCostAnalyticsDashboard(timeRange = '30d', groupBy, includeForecast = false, user) {
        return await this.dashboardService.getCostAnalyticsDashboard(timeRange, groupBy, includeForecast, user);
    }
    /**
     * Get performance metrics dashboard
     */
    async getPerformanceMetricsDashboard(timeRange = '24h', providers, includeComparison = false, user) {
        return await this.dashboardService.getPerformanceMetricsDashboard(timeRange, providers, includeComparison, user);
    }
    /**
     * Get alert dashboard
     */
    async getAlertDashboard(status, severity, timeRange = '24h', user) {
        return await this.dashboardService.getAlertDashboard(status, severity, timeRange, user);
    }
    /**
     * Get queue metrics dashboard
     */
    async getQueueMetricsDashboard(includeHistory = false, timeRange = '1h', user) {
        return await this.dashboardService.getQueueMetricsDashboard(includeHistory, timeRange, user);
    }
    /**
     * Get template analytics dashboard
     */
    async getTemplateAnalyticsDashboard(timeRange = '30d', includeOptimization = false, user) {
        return await this.dashboardService.getTemplateAnalyticsDashboard(timeRange, includeOptimization, user);
    }
};
exports.NotificationDashboardController = NotificationDashboardController;
__decorate([
    (0, common_1.Get)('overview'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get dashboard overview',
        description: 'Retrieve comprehensive dashboard overview with key performance metrics and status indicators',
    }),
    (0, swagger_1.ApiQuery)({ name: 'timeRange', required: false, type: String, description: 'Time range for metrics (default: 24h)' }),
    (0, swagger_1.ApiQuery)({ name: 'includeComparison', required: false, type: Boolean, description: 'Include comparison with previous period' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Dashboard overview retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                summary: {
                    type: 'object',
                    properties: {
                        totalNotifications: { type: 'number' },
                        successRate: { type: 'number' },
                        avgDeliveryTime: { type: 'number' },
                        totalCost: { type: 'number' },
                        activeAlerts: { type: 'number' },
                    },
                },
                providers: {
                    type: 'object',
                    properties: {
                        healthy: { type: 'number' },
                        total: { type: 'number' },
                        healthPercentage: { type: 'number' },
                    },
                },
                trends: {
                    type: 'array',
                    items: { type: 'object' },
                },
                alerts: {
                    type: 'array',
                    items: { type: 'object' },
                },
            },
        },
    }),
    (0, roles_decorator_1.Roles)('user', 'admin', 'operator'),
    (0, common_1.UseInterceptors)(cache_interceptor_1.CacheInterceptor),
    __param(0, (0, common_1.Query)(common_1.ValidationPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_b = typeof dashboard_query_dto_1.DashboardQueryDto !== "undefined" && dashboard_query_dto_1.DashboardQueryDto) === "function" ? _b : Object, typeof (_c = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _c : Object]),
    __metadata("design:returntype", Promise)
], NotificationDashboardController.prototype, "getDashboardOverview", null);
__decorate([
    (0, common_1.Get)('metrics/realtime'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get real-time metrics',
        description: 'Retrieve real-time notification metrics with live updates and current status',
    }),
    (0, swagger_1.ApiQuery)({ name: 'metrics', required: false, type: String, description: 'Comma-separated list of metrics to include' }),
    (0, swagger_1.ApiQuery)({ name: 'interval', required: false, type: String, description: 'Update interval (default: 30s)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Real-time metrics retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                timestamp: { type: 'string' },
                metrics: {
                    type: 'object',
                    properties: {
                        deliveryRate: { type: 'number' },
                        errorRate: { type: 'number' },
                        avgResponseTime: { type: 'number' },
                        queueDepth: { type: 'number' },
                        activeConnections: { type: 'number' },
                    },
                },
                providers: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            name: { type: 'string' },
                            healthy: { type: 'boolean' },
                            responseTime: { type: 'number' },
                            throughput: { type: 'number' },
                        },
                    },
                },
            },
        },
    }),
    (0, roles_decorator_1.Roles)('user', 'admin', 'operator'),
    __param(0, (0, common_1.Query)('metrics')),
    __param(1, (0, common_1.Query)('interval')),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, typeof (_d = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _d : Object]),
    __metadata("design:returntype", Promise)
], NotificationDashboardController.prototype, "getRealTimeMetrics", null);
__decorate([
    (0, common_1.Get)('health'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get provider health dashboard',
        description: 'Retrieve comprehensive provider health status with circuit breaker information and performance metrics',
    }),
    (0, swagger_1.ApiQuery)({ name: 'includeHistory', required: false, type: Boolean, description: 'Include health history data' }),
    (0, swagger_1.ApiQuery)({ name: 'timeRange', required: false, type: String, description: 'Time range for health data (default: 24h)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Provider health dashboard retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                overall: {
                    type: 'object',
                    properties: {
                        healthy: { type: 'boolean' },
                        healthyProviders: { type: 'number' },
                        totalProviders: { type: 'number' },
                        healthPercentage: { type: 'number' },
                    },
                },
                providers: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            name: { type: 'string' },
                            healthy: { type: 'boolean' },
                            responseTime: { type: 'number' },
                            uptime: { type: 'number' },
                            circuitBreakerState: { type: 'string' },
                            lastChecked: { type: 'string' },
                        },
                    },
                },
                circuitBreakers: { type: 'object' },
                healthHistory: { type: 'array', items: { type: 'object' } },
            },
        },
    }),
    (0, roles_decorator_1.Roles)('user', 'admin', 'operator'),
    (0, common_1.UseInterceptors)(cache_interceptor_1.CacheInterceptor),
    __param(0, (0, common_1.Query)('includeHistory')),
    __param(1, (0, common_1.Query)('timeRange')),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Boolean, String, typeof (_e = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _e : Object]),
    __metadata("design:returntype", Promise)
], NotificationDashboardController.prototype, "getProviderHealthDashboard", null);
__decorate([
    (0, common_1.Get)('cost'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get cost analytics dashboard',
        description: 'Retrieve comprehensive cost analytics with budget tracking, optimization insights, and forecasting',
    }),
    (0, swagger_1.ApiQuery)({ name: 'timeRange', required: false, type: String, description: 'Time range for cost data (default: 30d)' }),
    (0, swagger_1.ApiQuery)({ name: 'groupBy', required: false, type: String, description: 'Group cost data by provider, channel, or time' }),
    (0, swagger_1.ApiQuery)({ name: 'includeForecast', required: false, type: Boolean, description: 'Include cost forecast data' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Cost analytics dashboard retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                summary: {
                    type: 'object',
                    properties: {
                        totalCost: { type: 'number' },
                        avgCostPerNotification: { type: 'number' },
                        costTrend: { type: 'string' },
                        budgetUtilization: { type: 'number' },
                    },
                },
                budgets: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            name: { type: 'string' },
                            utilization: { type: 'number' },
                            remaining: { type: 'number' },
                            status: { type: 'string' },
                        },
                    },
                },
                breakdown: { type: 'array', items: { type: 'object' } },
                trends: { type: 'array', items: { type: 'object' } },
                forecast: { type: 'object' },
                optimization: { type: 'object' },
            },
        },
    }),
    (0, roles_decorator_1.Roles)('user', 'admin', 'operator'),
    (0, common_1.UseInterceptors)(cache_interceptor_1.CacheInterceptor),
    __param(0, (0, common_1.Query)('timeRange')),
    __param(1, (0, common_1.Query)('groupBy')),
    __param(2, (0, common_1.Query)('includeForecast')),
    __param(3, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Boolean, typeof (_f = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _f : Object]),
    __metadata("design:returntype", Promise)
], NotificationDashboardController.prototype, "getCostAnalyticsDashboard", null);
__decorate([
    (0, common_1.Get)('performance'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get performance metrics dashboard',
        description: 'Retrieve comprehensive performance metrics with trend analysis and comparative benchmarking',
    }),
    (0, swagger_1.ApiQuery)({ name: 'timeRange', required: false, type: String, description: 'Time range for performance data (default: 24h)' }),
    (0, swagger_1.ApiQuery)({ name: 'providers', required: false, type: String, description: 'Comma-separated list of providers to include' }),
    (0, swagger_1.ApiQuery)({ name: 'includeComparison', required: false, type: Boolean, description: 'Include comparison with previous period' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Performance metrics dashboard retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                summary: {
                    type: 'object',
                    properties: {
                        avgDeliveryTime: { type: 'number' },
                        successRate: { type: 'number' },
                        throughput: { type: 'number' },
                        errorRate: { type: 'number' },
                    },
                },
                providers: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            name: { type: 'string' },
                            deliveryTime: { type: 'number' },
                            successRate: { type: 'number' },
                            throughput: { type: 'number' },
                            ranking: { type: 'number' },
                        },
                    },
                },
                trends: { type: 'array', items: { type: 'object' } },
                comparison: { type: 'object' },
                benchmarks: { type: 'object' },
            },
        },
    }),
    (0, roles_decorator_1.Roles)('user', 'admin', 'operator'),
    (0, common_1.UseInterceptors)(cache_interceptor_1.CacheInterceptor),
    __param(0, (0, common_1.Query)('timeRange')),
    __param(1, (0, common_1.Query)('providers')),
    __param(2, (0, common_1.Query)('includeComparison')),
    __param(3, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Boolean, typeof (_g = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _g : Object]),
    __metadata("design:returntype", Promise)
], NotificationDashboardController.prototype, "getPerformanceMetricsDashboard", null);
__decorate([
    (0, common_1.Get)('alerts'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get alert dashboard',
        description: 'Retrieve comprehensive alert dashboard with real-time status updates and escalation tracking',
    }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, type: String, description: 'Filter by alert status (active, resolved, etc.)' }),
    (0, swagger_1.ApiQuery)({ name: 'severity', required: false, type: String, description: 'Filter by alert severity' }),
    (0, swagger_1.ApiQuery)({ name: 'timeRange', required: false, type: String, description: 'Time range for alert data (default: 24h)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Alert dashboard retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                summary: {
                    type: 'object',
                    properties: {
                        totalAlerts: { type: 'number' },
                        activeAlerts: { type: 'number' },
                        criticalAlerts: { type: 'number' },
                        avgResolutionTime: { type: 'number' },
                    },
                },
                alerts: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            ruleName: { type: 'string' },
                            severity: { type: 'string' },
                            status: { type: 'string' },
                            triggeredAt: { type: 'string' },
                            escalationLevel: { type: 'number' },
                        },
                    },
                },
                trends: { type: 'array', items: { type: 'object' } },
                escalations: { type: 'array', items: { type: 'object' } },
                resolution: { type: 'object' },
            },
        },
    }),
    (0, roles_decorator_1.Roles)('user', 'admin', 'operator'),
    (0, common_1.UseInterceptors)(cache_interceptor_1.CacheInterceptor),
    __param(0, (0, common_1.Query)('status')),
    __param(1, (0, common_1.Query)('severity')),
    __param(2, (0, common_1.Query)('timeRange')),
    __param(3, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, typeof (_h = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _h : Object]),
    __metadata("design:returntype", Promise)
], NotificationDashboardController.prototype, "getAlertDashboard", null);
__decorate([
    (0, common_1.Get)('queues'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get queue metrics dashboard',
        description: 'Retrieve comprehensive queue metrics with depth monitoring, processing rates, and performance analysis',
    }),
    (0, swagger_1.ApiQuery)({ name: 'includeHistory', required: false, type: Boolean, description: 'Include queue history data' }),
    (0, swagger_1.ApiQuery)({ name: 'timeRange', required: false, type: String, description: 'Time range for queue data (default: 1h)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Queue metrics dashboard retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                summary: {
                    type: 'object',
                    properties: {
                        totalQueues: { type: 'number' },
                        totalJobs: { type: 'number' },
                        processingRate: { type: 'number' },
                        avgWaitTime: { type: 'number' },
                    },
                },
                queues: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            name: { type: 'string' },
                            depth: { type: 'number' },
                            processingRate: { type: 'number' },
                            avgProcessingTime: { type: 'number' },
                            errorRate: { type: 'number' },
                        },
                    },
                },
                history: { type: 'array', items: { type: 'object' } },
                performance: { type: 'object' },
            },
        },
    }),
    (0, roles_decorator_1.Roles)('user', 'admin', 'operator'),
    (0, common_1.UseInterceptors)(cache_interceptor_1.CacheInterceptor),
    __param(0, (0, common_1.Query)('includeHistory')),
    __param(1, (0, common_1.Query)('timeRange')),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Boolean, String, typeof (_j = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _j : Object]),
    __metadata("design:returntype", Promise)
], NotificationDashboardController.prototype, "getQueueMetricsDashboard", null);
__decorate([
    (0, common_1.Get)('templates'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get template analytics dashboard',
        description: 'Retrieve comprehensive template analytics with usage patterns, performance metrics, and optimization insights',
    }),
    (0, swagger_1.ApiQuery)({ name: 'timeRange', required: false, type: String, description: 'Time range for template data (default: 30d)' }),
    (0, swagger_1.ApiQuery)({ name: 'includeOptimization', required: false, type: Boolean, description: 'Include optimization recommendations' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Template analytics dashboard retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                summary: {
                    type: 'object',
                    properties: {
                        totalTemplates: { type: 'number' },
                        activeTemplates: { type: 'number' },
                        avgUsage: { type: 'number' },
                        topPerforming: { type: 'string' },
                    },
                },
                templates: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            name: { type: 'string' },
                            usage: { type: 'number' },
                            successRate: { type: 'number' },
                            avgRenderTime: { type: 'number' },
                            ranking: { type: 'number' },
                        },
                    },
                },
                usage: { type: 'array', items: { type: 'object' } },
                performance: { type: 'object' },
                optimization: { type: 'array', items: { type: 'object' } },
            },
        },
    }),
    (0, roles_decorator_1.Roles)('user', 'admin', 'operator'),
    (0, common_1.UseInterceptors)(cache_interceptor_1.CacheInterceptor),
    __param(0, (0, common_1.Query)('timeRange')),
    __param(1, (0, common_1.Query)('includeOptimization')),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Boolean, typeof (_k = typeof user_entity_1.User !== "undefined" && user_entity_1.User) === "function" ? _k : Object]),
    __metadata("design:returntype", Promise)
], NotificationDashboardController.prototype, "getTemplateAnalyticsDashboard", null);
exports.NotificationDashboardController = NotificationDashboardController = __decorate([
    (0, swagger_1.ApiTags)('Notification Dashboard'),
    (0, common_1.Controller)('notification-dashboard'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, common_1.UseInterceptors)(logging_interceptor_1.LoggingInterceptor),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [typeof (_a = typeof notification_dashboard_service_1.NotificationDashboardService !== "undefined" && notification_dashboard_service_1.NotificationDashboardService) === "function" ? _a : Object])
], NotificationDashboardController);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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