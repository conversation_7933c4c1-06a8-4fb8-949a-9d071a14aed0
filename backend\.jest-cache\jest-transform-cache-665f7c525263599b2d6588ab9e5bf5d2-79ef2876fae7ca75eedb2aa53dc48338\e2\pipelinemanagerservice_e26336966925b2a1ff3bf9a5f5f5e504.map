{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\orchestration\\pipeline-manager.service.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAC/C,uCAAwC;AACxC,+BAA6B;AAC7B,uCAA2C;AAE3C;;;;;;GAMG;AAEI,IAAM,sBAAsB,8BAA5B,MAAM,sBAAsB;IAKjC,YAC6B,cAAsC,EACrC,eAAuC,EACtC,aAAqC,EACjD,QAAkB,EAClB,aAA4B;QAJD,mBAAc,GAAd,cAAc,CAAO;QACpB,oBAAe,GAAf,eAAe,CAAO;QACrB,kBAAa,GAAb,aAAa,CAAO;QACjD,aAAQ,GAAR,QAAQ,CAAU;QAClB,kBAAa,GAAb,aAAa,CAAe;QAT9B,WAAM,GAAG,IAAI,eAAM,CAAC,wBAAsB,CAAC,IAAI,CAAC,CAAC;QACjD,oBAAe,GAAG,IAAI,GAAG,EAA6B,CAAC;QACvD,sBAAiB,GAAG,IAAI,GAAG,EAA4B,CAAC;QASvE,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,UAA8B;QACjD,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC7C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yBAAyB,UAAU,EAAE,CAAC,CAAC;QAEvD,IAAI,CAAC;YACH,+BAA+B;YAC/B,MAAM,IAAI,CAAC,0BAA0B,CAAC,UAAU,CAAC,CAAC;YAElD,oCAAoC;YACpC,MAAM,SAAS,GAAsB;gBACnC,EAAE,EAAE,UAAU;gBACd,UAAU;gBACV,MAAM,EAAE,cAAc;gBACtB,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC;gBAChD,OAAO,EAAE,UAAU,CAAC,OAAO,IAAI,EAAE;gBACjC,OAAO,EAAE;oBACP,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,WAAW,EAAE,UAAU,CAAC,MAAM,CAAC,MAAM;oBACrC,eAAe,EAAE,CAAC;oBAClB,YAAY,EAAE,CAAC;iBAChB;gBACD,OAAO,EAAE,EAAE;aACZ,CAAC;YAEF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YAEhD,2BAA2B;YAC3B,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAEtC,OAAO,SAAS,CAAC;QAEnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,UAAU,EAAE,EAAE,KAAK,CAAC,CAAC;YACrE,MAAM,IAAI,aAAa,CAAC,6BAA6B,KAAK,CAAC,OAAO,EAAE,EAAE,UAAU,CAAC,CAAC;QACpF,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,2BAA2B,CAC/B,YAAoB,EACpB,UAA8B;QAE9B,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAC1D,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,aAAa,CAAC,gCAAgC,YAAY,EAAE,CAAC,CAAC;QAC1E,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QAClE,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,UAAkB;QACxC,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACvD,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,aAAa,CAAC,uBAAuB,UAAU,EAAE,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO;YACL,EAAE,EAAE,UAAU;YACd,MAAM,EAAE,SAAS,CAAC,MAAM;YACxB,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC;YAC3C,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC;YAC7C,OAAO,EAAE,SAAS,CAAC,OAAO;YAC1B,OAAO,EAAE,SAAS,CAAC,OAAO;YAC1B,MAAM,EAAE,SAAS,CAAC,MAAM,IAAI,EAAE;SAC/B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,UAAkB;QACrC,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACvD,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,aAAa,CAAC,uBAAuB,UAAU,EAAE,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,UAAU,EAAE,CAAC,CAAC;QAEtD,IAAI,CAAC;YACH,SAAS,CAAC,MAAM,GAAG,YAAY,CAAC;YAEhC,wBAAwB;YACxB,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;YAE1C,qBAAqB;YACrB,MAAM,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;YAE/C,SAAS,CAAC,MAAM,GAAG,WAAW,CAAC;YAC/B,SAAS,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;YAEvC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,UAAU,EAAE,CAAC,CAAC;QAEvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,UAAU,EAAE,EAAE,KAAK,CAAC,CAAC;YACrE,SAAS,CAAC,MAAM,GAAG,OAAO,CAAC;YAC3B,MAAM,IAAI,aAAa,CAAC,iCAAiC,KAAK,CAAC,OAAO,EAAE,EAAE,UAAU,CAAC,CAAC;QACxF,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,UAAkB,EAAE,OAAe;QAClD,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACvD,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,aAAa,CAAC,uBAAuB,UAAU,EAAE,CAAC,CAAC;QAC/D,CAAC;QAED,MAAM,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,CAAC;QAC3D,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,aAAa,CAAC,oBAAoB,OAAO,EAAE,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC9B,MAAM,IAAI,aAAa,CAAC,iCAAiC,OAAO,EAAE,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,UAAU,IAAI,OAAO,EAAE,CAAC,CAAC;QAErE,IAAI,CAAC;YACH,oBAAoB;YACpB,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC;YACzB,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC;YACxB,KAAK,CAAC,UAAU,GAAG,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAE/C,oBAAoB;YACpB,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QAE5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,UAAU,IAAI,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;YACzE,MAAM,IAAI,aAAa,CAAC,uBAAuB,KAAK,CAAC,OAAO,EAAE,EAAE,UAAU,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB;QACtB,MAAM,QAAQ,GAAqB,EAAE,CAAC;QAEtC,KAAK,MAAM,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAChD,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;gBACxD,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACxB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,UAAU,EAAE,EAAE,KAAK,CAAC,CAAC;YAC9E,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,wBAAwB,CAAC,IAAY,EAAE,QAA0B;QAC/D,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC3C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iCAAiC,IAAI,EAAE,CAAC,CAAC;IAC3D,CAAC;IAED,yBAAyB;IAEjB,KAAK,CAAC,eAAe,CAAC,SAA4B;QACxD,IAAI,CAAC;YACH,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC;YAC7B,SAAS,CAAC,OAAO,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAEzC,6CAA6C;YAC7C,IAAI,SAAS,CAAC,UAAU,CAAC,iBAAiB,KAAK,UAAU,EAAE,CAAC;gBAC1D,MAAM,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;YAChD,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC;YAClD,CAAC;YAED,SAAS,CAAC,MAAM,GAAG,WAAW,CAAC;YAC/B,SAAS,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;YAEvC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;QAEzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,SAAS,CAAC,MAAM,GAAG,QAAQ,CAAC;YAC5B,SAAS,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;YACvC,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,IAAI,EAAE,CAAC;YAC1C,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;gBACpB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,KAAK,EAAE,UAAU;aAClB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,SAAS,CAAC,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;YAC7D,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,iCAAiC;YACjC,MAAM,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,SAA4B;QAClE,KAAK,MAAM,KAAK,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;YACrC,IAAI,SAAS,CAAC,MAAM,KAAK,YAAY,EAAE,CAAC;gBACtC,MAAM;YACR,CAAC;YAED,2BAA2B;YAC3B,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,CAAC;gBAC/C,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC;gBACzB,SAAS;YACX,CAAC;YAED,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAE1C,IAAI,KAAK,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;gBAC1D,MAAM,IAAI,KAAK,CAAC,iBAAiB,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,SAA4B;QAChE,MAAM,aAAa,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CACjD,IAAI,CAAC,4BAA4B,CAAC,SAAS,EAAE,KAAK,CAAC,CACpD,CAAC;QAEF,MAAM,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;QAExC,sCAAsC;QACtC,MAAM,oBAAoB,GAAG,SAAS,CAAC,MAAM,CAAC,MAAM,CAClD,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAC/D,CAAC;QAEF,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,2BAA2B,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC/F,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,4BAA4B,CACxC,SAA4B,EAC5B,KAAoB;QAEpB,wBAAwB;QACxB,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QAEjD,IAAI,SAAS,CAAC,MAAM,KAAK,YAAY,EAAE,CAAC;YACtC,OAAO;QACT,CAAC;QAED,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IAC5C,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,SAA4B,EAAE,KAAoB;QAC3E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,SAAS,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;QAElE,IAAI,CAAC;YACH,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC;YACzB,KAAK,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAE7B,8BAA8B;YAC9B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAE/D,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC;YAC3B,KAAK,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;YAC3B,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;YAEtB,SAAS,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;YACpC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;YAErC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,SAAS,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;QAEpE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,KAAK,CAAC,MAAM,GAAG,QAAQ,CAAC;YACxB,KAAK,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;YAC3B,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC;YAE5B,SAAS,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;YAEjC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,SAAS,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;YAEtE,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;gBAC7B,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAC9B,SAA4B,EAC5B,KAAoB;QAEpB,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,aAAa;gBAChB,OAAO,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YACvD,KAAK,oBAAoB;gBACvB,OAAO,IAAI,CAAC,6BAA6B,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAC9D,KAAK,gBAAgB;gBACnB,OAAO,IAAI,CAAC,yBAAyB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAC1D,KAAK,kBAAkB;gBACrB,OAAO,IAAI,CAAC,2BAA2B,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAC5D,KAAK,qBAAqB;gBACxB,OAAO,IAAI,CAAC,8BAA8B,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAC/D,KAAK,aAAa;gBAChB,OAAO,IAAI,CAAC,uBAAuB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YACxD,KAAK,gBAAgB;gBACnB,OAAO,IAAI,CAAC,yBAAyB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAC1D;gBACE,MAAM,IAAI,KAAK,CAAC,uBAAuB,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAClC,SAA4B,EAC5B,KAAoB;QAEpB,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE;YACnD,UAAU,EAAE,SAAS,CAAC,EAAE;YACxB,OAAO,EAAE,KAAK,CAAC,EAAE;YACjB,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,KAAK,CAAC;YAC1C,MAAM,EAAE,KAAK,CAAC,MAAM;SACrB,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;IACxC,CAAC;IAEO,KAAK,CAAC,yBAAyB,CACrC,SAA4B,EAC5B,KAAoB;QAEpB,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE;YAChD,UAAU,EAAE,SAAS,CAAC,EAAE;YACxB,OAAO,EAAE,KAAK,CAAC,EAAE;YACjB,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,KAAK,CAAC;YAC1C,MAAM,EAAE,KAAK,CAAC,MAAM;SACrB,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;IACxC,CAAC;IAEO,KAAK,CAAC,6BAA6B,CACzC,SAA4B,EAC5B,KAAoB;QAEpB,wCAAwC;QACxC,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QACnD,gDAAgD;QAChD,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;IACjE,CAAC;IAEO,KAAK,CAAC,2BAA2B,CACvC,SAA4B,EAC5B,KAAoB;QAEpB,sCAAsC;QACtC,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QACnD,6BAA6B;QAC7B,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC;IACzC,CAAC;IAEO,KAAK,CAAC,8BAA8B,CAC1C,SAA4B,EAC5B,KAAoB;QAEpB,yCAAyC;QACzC,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QACnD,4CAA4C;QAC5C,OAAO,EAAE,eAAe,EAAE,KAAK,EAAE,CAAC;IACpC,CAAC;IAEO,KAAK,CAAC,uBAAuB,CACnC,SAA4B,EAC5B,KAAoB;QAEpB,uCAAuC;QACvC,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAC5E,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;IAC7E,CAAC;IAEO,KAAK,CAAC,yBAAyB,CACrC,SAA4B,EAC5B,KAAoB;QAEpB,+CAA+C;QAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QACnD,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,IAAI,EAAE,CAAC;QAE/C,qBAAqB;QACrB,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;YACjD,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;YAC5C,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;YACjE,OAAO,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC;QAC/B,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,aAAa,CAAC,SAA4B,EAAE,KAAoB;QACtE,IAAI,KAAK,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YACpC,OAAO,SAAS,CAAC,OAAO,CAAC;QAC3B,CAAC;QAED,IAAI,KAAK,CAAC,WAAW,IAAI,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC;YAC9D,OAAO,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QAC9C,CAAC;QAED,OAAO,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC;IAC3B,CAAC;IAEO,kBAAkB,CAAC,KAAoB,EAAE,SAA4B;QAC3E,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3D,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACtC,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC;YAC5D,OAAO,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,WAAW,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAC/B,KAAoB,EACpB,SAA4B;QAE5B,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3D,OAAO;QACT,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,CAAC,CAAC,YAAY;QACxC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,CAAC;YAClD,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,GAAG,WAAW,EAAE,CAAC;gBACzC,MAAM,IAAI,KAAK,CAAC,iCAAiC,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;YAC/D,CAAC;YAED,IAAI,SAAS,CAAC,MAAM,KAAK,YAAY,EAAE,CAAC;gBACtC,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;YACvE,CAAC;YAED,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,gBAAgB;QAC3E,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,GAAQ;QACzC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,iBAAiB,CAAC,SAA4B;QACpD,IAAI,SAAS,CAAC,OAAO,CAAC,WAAW,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAClD,OAAO,SAAS,CAAC,OAAO,CAAC,eAAe,GAAG,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC;IAC3E,CAAC;IAEO,eAAe,CAAC,SAA4B;QAClD,MAAM,YAAY,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC;QACxE,OAAO,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IAC/C,CAAC;IAEO,gBAAgB,CAAC,gBAAmC;QAC1D,OAAO,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAClC,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,MAAM,EAAE,SAAS;YACjB,MAAM,EAAE,GAAG,CAAC,MAAM,IAAI,EAAE;YACxB,YAAY,EAAE,GAAG,CAAC,YAAY,IAAI,EAAE;YACpC,iBAAiB,EAAE,GAAG,CAAC,iBAAiB,IAAI,KAAK;YACjD,WAAW,EAAE,GAAG,CAAC,WAAW;YAC5B,KAAK,EAAE,GAAG,CAAC,KAAK;YAChB,UAAU,EAAE,CAAC;SACd,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,2BAA2B;QACjC,qCAAqC;QACrC,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,EAAE;YAC/C,IAAI,EAAE,0BAA0B;YAChC,WAAW,EAAE,wCAAwC;YACrD,MAAM,EAAE;gBACN;oBACE,EAAE,EAAE,YAAY;oBAChB,IAAI,EAAE,oBAAoB;oBAC1B,IAAI,EAAE,oBAAoB;oBAC1B,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE;iBAC5C;gBACD;oBACE,EAAE,EAAE,SAAS;oBACb,IAAI,EAAE,aAAa;oBACnB,IAAI,EAAE,aAAa;oBACnB,YAAY,EAAE,CAAC,YAAY,CAAC;oBAC5B,MAAM,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE,UAAU,EAAE,GAAG,EAAE;iBACvD;gBACD;oBACE,EAAE,EAAE,UAAU;oBACd,IAAI,EAAE,mBAAmB;oBACzB,IAAI,EAAE,kBAAkB;oBACxB,YAAY,EAAE,CAAC,SAAS,CAAC;oBACzB,MAAM,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE;iBAC3B;aACF;YACD,iBAAiB,EAAE,YAAY;SAChC,CAAC,CAAC;IACL,CAAC;IAEO,mBAAmB,CACzB,QAA0B,EAC1B,UAA8B;QAE9B,OAAO;YACL,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,WAAW,EAAE,QAAQ,CAAC,WAAW;YACjC,MAAM,EAAE,QAAQ,CAAC,MAAM;YACvB,iBAAiB,EAAE,QAAQ,CAAC,iBAAiB;YAC7C,OAAO,EAAE,UAAU;SACpB,CAAC;IACJ,CAAC;IAEO,kBAAkB;QACxB,OAAO,YAAY,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAC7E,CAAC;IAEO,iBAAiB,CAAC,SAA4B,EAAE,SAAc;QACpE,gDAAgD;QAChD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,KAAY,EAAE,MAAW;QAClD,yBAAyB;QACzB,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,SAA4B;QAC5D,4BAA4B;QAC5B,KAAK,MAAM,KAAK,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;YACrC,IAAI,KAAK,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBAC/B,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC;YAC7B,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,SAA4B;QACjE,8CAA8C;QAC9C,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;IAC5C,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,SAA4B;QAC1D,mDAAmD;QACnD,8DAA8D;IAChE,CAAC;IAEO,KAAK,CAAC,0BAA0B,CAAC,UAA8B;QACrE,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzD,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC3D,CAAC;QAED,8BAA8B;QAC9B,KAAK,MAAM,KAAK,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;YACtC,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;gBACvB,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;oBACvC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC;wBACjD,MAAM,IAAI,KAAK,CAAC,uBAAuB,KAAK,eAAe,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;oBACzE,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;CACF,CAAA;AA3kBY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;IAOR,WAAA,IAAA,kBAAW,EAAC,YAAY,CAAC,CAAA;IACzB,WAAA,IAAA,kBAAW,EAAC,aAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,kBAAW,EAAC,cAAc,CAAC,CAAA;yDAFgC,YAAK,oBAAL,YAAK,oDACH,YAAK,oBAAL,YAAK,oDACN,YAAK,oBAAL,YAAK,oDACvC,eAAQ,oBAAR,eAAQ,oDACH,sBAAa,oBAAb,sBAAa;GAVpC,sBAAsB,CA2kBlC;AA+ED,MAAM,aAAc,SAAQ,KAAK;IAC/B,YAAY,OAAe,EAAkB,UAAmB;QAC9D,KAAK,CAAC,OAAO,CAAC,CAAC;QAD4B,eAAU,GAAV,UAAU,CAAS;QAE9D,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;IAC9B,CAAC;CACF", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\orchestration\\pipeline-manager.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { EventBus } from '@nestjs/cqrs';\r\nimport { Queue } from 'bull';\r\nimport { InjectQueue } from '@nestjs/bull';\r\n\r\n/**\r\n * Pipeline Manager Service\r\n * \r\n * Manages AI processing pipelines with support for complex workflows,\r\n * parallel processing, conditional logic, and error recovery.\r\n * Orchestrates multi-stage AI operations with monitoring and optimization.\r\n */\r\n@Injectable()\r\nexport class PipelineManagerService {\r\n  private readonly logger = new Logger(PipelineManagerService.name);\r\n  private readonly activePipelines = new Map<string, PipelineExecution>();\r\n  private readonly pipelineTemplates = new Map<string, PipelineTemplate>();\r\n\r\n  constructor(\r\n    @InjectQueue('ai-request') private readonly aiRequestQueue: Queue,\r\n    @InjectQueue('ai-response') private readonly aiResponseQueue: Queue,\r\n    @InjectQueue('training-job') private readonly trainingQueue: Queue,\r\n    private readonly eventBus: EventBus,\r\n    private readonly configService: ConfigService,\r\n  ) {\r\n    this.initializePipelineTemplates();\r\n  }\r\n\r\n  /**\r\n   * Creates and executes a new AI processing pipeline\r\n   */\r\n  async createPipeline(definition: PipelineDefinition): Promise<PipelineExecution> {\r\n    const pipelineId = this.generatePipelineId();\r\n    this.logger.log(`Creating AI pipeline: ${pipelineId}`);\r\n\r\n    try {\r\n      // Validate pipeline definition\r\n      await this.validatePipelineDefinition(definition);\r\n\r\n      // Create pipeline execution context\r\n      const execution: PipelineExecution = {\r\n        id: pipelineId,\r\n        definition,\r\n        status: 'initializing',\r\n        stages: this.initializeStages(definition.stages),\r\n        context: definition.context || {},\r\n        metrics: {\r\n          startTime: new Date(),\r\n          totalStages: definition.stages.length,\r\n          completedStages: 0,\r\n          failedStages: 0,\r\n        },\r\n        results: {},\r\n      };\r\n\r\n      this.activePipelines.set(pipelineId, execution);\r\n\r\n      // Start pipeline execution\r\n      await this.executePipeline(execution);\r\n\r\n      return execution;\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to create pipeline: ${pipelineId}`, error);\r\n      throw new PipelineError(`Pipeline creation failed: ${error.message}`, pipelineId);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Executes a pipeline from a predefined template\r\n   */\r\n  async executePipelineFromTemplate(\r\n    templateName: string,\r\n    parameters: PipelineParameters\r\n  ): Promise<PipelineExecution> {\r\n    const template = this.pipelineTemplates.get(templateName);\r\n    if (!template) {\r\n      throw new PipelineError(`Pipeline template not found: ${templateName}`);\r\n    }\r\n\r\n    const definition = this.instantiateTemplate(template, parameters);\r\n    return this.createPipeline(definition);\r\n  }\r\n\r\n  /**\r\n   * Gets pipeline execution status and progress\r\n   */\r\n  async getPipelineStatus(pipelineId: string): Promise<PipelineStatus> {\r\n    const execution = this.activePipelines.get(pipelineId);\r\n    if (!execution) {\r\n      throw new PipelineError(`Pipeline not found: ${pipelineId}`);\r\n    }\r\n\r\n    return {\r\n      id: pipelineId,\r\n      status: execution.status,\r\n      progress: this.calculateProgress(execution),\r\n      currentStage: this.getCurrentStage(execution),\r\n      metrics: execution.metrics,\r\n      results: execution.results,\r\n      errors: execution.errors || [],\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Cancels a running pipeline\r\n   */\r\n  async cancelPipeline(pipelineId: string): Promise<void> {\r\n    const execution = this.activePipelines.get(pipelineId);\r\n    if (!execution) {\r\n      throw new PipelineError(`Pipeline not found: ${pipelineId}`);\r\n    }\r\n\r\n    this.logger.log(`Cancelling pipeline: ${pipelineId}`);\r\n\r\n    try {\r\n      execution.status = 'cancelling';\r\n      \r\n      // Cancel running stages\r\n      await this.cancelRunningStages(execution);\r\n      \r\n      // Clean up resources\r\n      await this.cleanupPipelineResources(execution);\r\n      \r\n      execution.status = 'cancelled';\r\n      execution.metrics.endTime = new Date();\r\n\r\n      this.logger.log(`Pipeline cancelled: ${pipelineId}`);\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Failed to cancel pipeline: ${pipelineId}`, error);\r\n      execution.status = 'error';\r\n      throw new PipelineError(`Pipeline cancellation failed: ${error.message}`, pipelineId);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Retries a failed pipeline stage\r\n   */\r\n  async retryStage(pipelineId: string, stageId: string): Promise<void> {\r\n    const execution = this.activePipelines.get(pipelineId);\r\n    if (!execution) {\r\n      throw new PipelineError(`Pipeline not found: ${pipelineId}`);\r\n    }\r\n\r\n    const stage = execution.stages.find(s => s.id === stageId);\r\n    if (!stage) {\r\n      throw new PipelineError(`Stage not found: ${stageId}`);\r\n    }\r\n\r\n    if (stage.status !== 'failed') {\r\n      throw new PipelineError(`Stage is not in failed state: ${stageId}`);\r\n    }\r\n\r\n    this.logger.log(`Retrying pipeline stage: ${pipelineId}/${stageId}`);\r\n\r\n    try {\r\n      // Reset stage state\r\n      stage.status = 'pending';\r\n      stage.error = undefined;\r\n      stage.retryCount = (stage.retryCount || 0) + 1;\r\n\r\n      // Execute the stage\r\n      await this.executeStage(execution, stage);\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Stage retry failed: ${pipelineId}/${stageId}`, error);\r\n      throw new PipelineError(`Stage retry failed: ${error.message}`, pipelineId);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Gets all active pipelines\r\n   */\r\n  async getActivePipelines(): Promise<PipelineStatus[]> {\r\n    const statuses: PipelineStatus[] = [];\r\n    \r\n    for (const [pipelineId] of this.activePipelines) {\r\n      try {\r\n        const status = await this.getPipelineStatus(pipelineId);\r\n        statuses.push(status);\r\n      } catch (error) {\r\n        this.logger.warn(`Failed to get status for pipeline: ${pipelineId}`, error);\r\n      }\r\n    }\r\n\r\n    return statuses;\r\n  }\r\n\r\n  /**\r\n   * Registers a new pipeline template\r\n   */\r\n  registerPipelineTemplate(name: string, template: PipelineTemplate): void {\r\n    this.pipelineTemplates.set(name, template);\r\n    this.logger.log(`Registered pipeline template: ${name}`);\r\n  }\r\n\r\n  // Private helper methods\r\n\r\n  private async executePipeline(execution: PipelineExecution): Promise<void> {\r\n    try {\r\n      execution.status = 'running';\r\n      execution.metrics.startTime = new Date();\r\n\r\n      // Execute stages based on execution strategy\r\n      if (execution.definition.executionStrategy === 'parallel') {\r\n        await this.executeStagesInParallel(execution);\r\n      } else {\r\n        await this.executeStagesSequentially(execution);\r\n      }\r\n\r\n      execution.status = 'completed';\r\n      execution.metrics.endTime = new Date();\r\n\r\n      this.logger.log(`Pipeline completed: ${execution.id}`);\r\n\r\n    } catch (error) {\r\n      execution.status = 'failed';\r\n      execution.metrics.endTime = new Date();\r\n      execution.errors = execution.errors || [];\r\n      execution.errors.push({\r\n        message: error.message,\r\n        timestamp: new Date(),\r\n        stage: 'pipeline',\r\n      });\r\n\r\n      this.logger.error(`Pipeline failed: ${execution.id}`, error);\r\n      throw error;\r\n    } finally {\r\n      // Emit pipeline completion event\r\n      await this.emitPipelineEvent(execution);\r\n    }\r\n  }\r\n\r\n  private async executeStagesSequentially(execution: PipelineExecution): Promise<void> {\r\n    for (const stage of execution.stages) {\r\n      if (execution.status === 'cancelling') {\r\n        break;\r\n      }\r\n\r\n      // Check stage dependencies\r\n      if (!this.areDependenciesMet(stage, execution)) {\r\n        stage.status = 'skipped';\r\n        continue;\r\n      }\r\n\r\n      await this.executeStage(execution, stage);\r\n\r\n      if (stage.status === 'failed' && !stage.continueOnFailure) {\r\n        throw new Error(`Stage failed: ${stage.id}`);\r\n      }\r\n    }\r\n  }\r\n\r\n  private async executeStagesInParallel(execution: PipelineExecution): Promise<void> {\r\n    const stagePromises = execution.stages.map(stage => \r\n      this.executeStageWithDependencies(execution, stage)\r\n    );\r\n\r\n    await Promise.allSettled(stagePromises);\r\n\r\n    // Check if any critical stages failed\r\n    const failedCriticalStages = execution.stages.filter(\r\n      stage => stage.status === 'failed' && !stage.continueOnFailure\r\n    );\r\n\r\n    if (failedCriticalStages.length > 0) {\r\n      throw new Error(`Critical stages failed: ${failedCriticalStages.map(s => s.id).join(', ')}`);\r\n    }\r\n  }\r\n\r\n  private async executeStageWithDependencies(\r\n    execution: PipelineExecution,\r\n    stage: PipelineStage\r\n  ): Promise<void> {\r\n    // Wait for dependencies\r\n    await this.waitForDependencies(stage, execution);\r\n\r\n    if (execution.status === 'cancelling') {\r\n      return;\r\n    }\r\n\r\n    await this.executeStage(execution, stage);\r\n  }\r\n\r\n  private async executeStage(execution: PipelineExecution, stage: PipelineStage): Promise<void> {\r\n    this.logger.debug(`Executing stage: ${execution.id}/${stage.id}`);\r\n\r\n    try {\r\n      stage.status = 'running';\r\n      stage.startTime = new Date();\r\n\r\n      // Execute stage based on type\r\n      const result = await this.executeStageByType(execution, stage);\r\n\r\n      stage.status = 'completed';\r\n      stage.endTime = new Date();\r\n      stage.result = result;\r\n\r\n      execution.metrics.completedStages++;\r\n      execution.results[stage.id] = result;\r\n\r\n      this.logger.debug(`Stage completed: ${execution.id}/${stage.id}`);\r\n\r\n    } catch (error) {\r\n      stage.status = 'failed';\r\n      stage.endTime = new Date();\r\n      stage.error = error.message;\r\n\r\n      execution.metrics.failedStages++;\r\n\r\n      this.logger.error(`Stage failed: ${execution.id}/${stage.id}`, error);\r\n\r\n      if (!stage.continueOnFailure) {\r\n        throw error;\r\n      }\r\n    }\r\n  }\r\n\r\n  private async executeStageByType(\r\n    execution: PipelineExecution,\r\n    stage: PipelineStage\r\n  ): Promise<any> {\r\n    switch (stage.type) {\r\n      case 'ai-analysis':\r\n        return this.executeAiAnalysisStage(execution, stage);\r\n      case 'data-preprocessing':\r\n        return this.executeDataPreprocessingStage(execution, stage);\r\n      case 'model-training':\r\n        return this.executeModelTrainingStage(execution, stage);\r\n      case 'model-evaluation':\r\n        return this.executeModelEvaluationStage(execution, stage);\r\n      case 'data-transformation':\r\n        return this.executeDataTransformationStage(execution, stage);\r\n      case 'conditional':\r\n        return this.executeConditionalStage(execution, stage);\r\n      case 'parallel-batch':\r\n        return this.executeParallelBatchStage(execution, stage);\r\n      default:\r\n        throw new Error(`Unknown stage type: ${stage.type}`);\r\n    }\r\n  }\r\n\r\n  private async executeAiAnalysisStage(\r\n    execution: PipelineExecution,\r\n    stage: PipelineStage\r\n  ): Promise<any> {\r\n    const job = await this.aiRequestQueue.add('analyze', {\r\n      pipelineId: execution.id,\r\n      stageId: stage.id,\r\n      data: this.getStageInput(execution, stage),\r\n      config: stage.config,\r\n    });\r\n\r\n    return this.waitForJobCompletion(job);\r\n  }\r\n\r\n  private async executeModelTrainingStage(\r\n    execution: PipelineExecution,\r\n    stage: PipelineStage\r\n  ): Promise<any> {\r\n    const job = await this.trainingQueue.add('train', {\r\n      pipelineId: execution.id,\r\n      stageId: stage.id,\r\n      data: this.getStageInput(execution, stage),\r\n      config: stage.config,\r\n    });\r\n\r\n    return this.waitForJobCompletion(job);\r\n  }\r\n\r\n  private async executeDataPreprocessingStage(\r\n    execution: PipelineExecution,\r\n    stage: PipelineStage\r\n  ): Promise<any> {\r\n    // Implementation for data preprocessing\r\n    const input = this.getStageInput(execution, stage);\r\n    // Process data according to stage configuration\r\n    return { processedData: input, metadata: { stage: stage.id } };\r\n  }\r\n\r\n  private async executeModelEvaluationStage(\r\n    execution: PipelineExecution,\r\n    stage: PipelineStage\r\n  ): Promise<any> {\r\n    // Implementation for model evaluation\r\n    const input = this.getStageInput(execution, stage);\r\n    // Evaluate model performance\r\n    return { metrics: {}, evaluation: {} };\r\n  }\r\n\r\n  private async executeDataTransformationStage(\r\n    execution: PipelineExecution,\r\n    stage: PipelineStage\r\n  ): Promise<any> {\r\n    // Implementation for data transformation\r\n    const input = this.getStageInput(execution, stage);\r\n    // Transform data according to configuration\r\n    return { transformedData: input };\r\n  }\r\n\r\n  private async executeConditionalStage(\r\n    execution: PipelineExecution,\r\n    stage: PipelineStage\r\n  ): Promise<any> {\r\n    // Implementation for conditional logic\r\n    const condition = this.evaluateCondition(execution, stage.config.condition);\r\n    return { conditionMet: condition, result: condition ? 'proceed' : 'skip' };\r\n  }\r\n\r\n  private async executeParallelBatchStage(\r\n    execution: PipelineExecution,\r\n    stage: PipelineStage\r\n  ): Promise<any> {\r\n    // Implementation for parallel batch processing\r\n    const input = this.getStageInput(execution, stage);\r\n    const batchSize = stage.config.batchSize || 10;\r\n    \r\n    // Process in batches\r\n    const results = [];\r\n    for (let i = 0; i < input.length; i += batchSize) {\r\n      const batch = input.slice(i, i + batchSize);\r\n      const batchResult = await this.processBatch(batch, stage.config);\r\n      results.push(...batchResult);\r\n    }\r\n    \r\n    return results;\r\n  }\r\n\r\n  private getStageInput(execution: PipelineExecution, stage: PipelineStage): any {\r\n    if (stage.inputSource === 'context') {\r\n      return execution.context;\r\n    }\r\n    \r\n    if (stage.inputSource && execution.results[stage.inputSource]) {\r\n      return execution.results[stage.inputSource];\r\n    }\r\n    \r\n    return stage.input || {};\r\n  }\r\n\r\n  private areDependenciesMet(stage: PipelineStage, execution: PipelineExecution): boolean {\r\n    if (!stage.dependencies || stage.dependencies.length === 0) {\r\n      return true;\r\n    }\r\n\r\n    return stage.dependencies.every(depId => {\r\n      const depStage = execution.stages.find(s => s.id === depId);\r\n      return depStage && depStage.status === 'completed';\r\n    });\r\n  }\r\n\r\n  private async waitForDependencies(\r\n    stage: PipelineStage,\r\n    execution: PipelineExecution\r\n  ): Promise<void> {\r\n    if (!stage.dependencies || stage.dependencies.length === 0) {\r\n      return;\r\n    }\r\n\r\n    const maxWaitTime = 300000; // 5 minutes\r\n    const startTime = Date.now();\r\n\r\n    while (!this.areDependenciesMet(stage, execution)) {\r\n      if (Date.now() - startTime > maxWaitTime) {\r\n        throw new Error(`Dependency timeout for stage: ${stage.id}`);\r\n      }\r\n\r\n      if (execution.status === 'cancelling') {\r\n        throw new Error('Pipeline cancelled while waiting for dependencies');\r\n      }\r\n\r\n      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second\r\n    }\r\n  }\r\n\r\n  private async waitForJobCompletion(job: any): Promise<any> {\r\n    return new Promise((resolve, reject) => {\r\n      job.finished().then(resolve).catch(reject);\r\n    });\r\n  }\r\n\r\n  private calculateProgress(execution: PipelineExecution): number {\r\n    if (execution.metrics.totalStages === 0) return 0;\r\n    return execution.metrics.completedStages / execution.metrics.totalStages;\r\n  }\r\n\r\n  private getCurrentStage(execution: PipelineExecution): string | null {\r\n    const runningStage = execution.stages.find(s => s.status === 'running');\r\n    return runningStage ? runningStage.id : null;\r\n  }\r\n\r\n  private initializeStages(stageDefinitions: StageDefinition[]): PipelineStage[] {\r\n    return stageDefinitions.map(def => ({\r\n      id: def.id,\r\n      name: def.name,\r\n      type: def.type,\r\n      status: 'pending',\r\n      config: def.config || {},\r\n      dependencies: def.dependencies || [],\r\n      continueOnFailure: def.continueOnFailure || false,\r\n      inputSource: def.inputSource,\r\n      input: def.input,\r\n      retryCount: 0,\r\n    }));\r\n  }\r\n\r\n  private initializePipelineTemplates(): void {\r\n    // Register common pipeline templates\r\n    this.registerPipelineTemplate('threat-analysis', {\r\n      name: 'Threat Analysis Pipeline',\r\n      description: 'Comprehensive threat analysis workflow',\r\n      stages: [\r\n        {\r\n          id: 'preprocess',\r\n          name: 'Data Preprocessing',\r\n          type: 'data-preprocessing',\r\n          config: { normalize: true, validate: true },\r\n        },\r\n        {\r\n          id: 'analyze',\r\n          name: 'AI Analysis',\r\n          type: 'ai-analysis',\r\n          dependencies: ['preprocess'],\r\n          config: { model: 'threat-detection', confidence: 0.8 },\r\n        },\r\n        {\r\n          id: 'evaluate',\r\n          name: 'Result Evaluation',\r\n          type: 'model-evaluation',\r\n          dependencies: ['analyze'],\r\n          config: { threshold: 0.9 },\r\n        },\r\n      ],\r\n      executionStrategy: 'sequential',\r\n    });\r\n  }\r\n\r\n  private instantiateTemplate(\r\n    template: PipelineTemplate,\r\n    parameters: PipelineParameters\r\n  ): PipelineDefinition {\r\n    return {\r\n      name: template.name,\r\n      description: template.description,\r\n      stages: template.stages,\r\n      executionStrategy: template.executionStrategy,\r\n      context: parameters,\r\n    };\r\n  }\r\n\r\n  private generatePipelineId(): string {\r\n    return `pipeline-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\r\n  }\r\n\r\n  private evaluateCondition(execution: PipelineExecution, condition: any): boolean {\r\n    // Simple condition evaluation - can be extended\r\n    return true;\r\n  }\r\n\r\n  private async processBatch(batch: any[], config: any): Promise<any[]> {\r\n    // Process batch of items\r\n    return batch.map(item => ({ ...item, processed: true }));\r\n  }\r\n\r\n  private async cancelRunningStages(execution: PipelineExecution): Promise<void> {\r\n    // Cancel any running stages\r\n    for (const stage of execution.stages) {\r\n      if (stage.status === 'running') {\r\n        stage.status = 'cancelled';\r\n      }\r\n    }\r\n  }\r\n\r\n  private async cleanupPipelineResources(execution: PipelineExecution): Promise<void> {\r\n    // Clean up any resources used by the pipeline\r\n    this.activePipelines.delete(execution.id);\r\n  }\r\n\r\n  private async emitPipelineEvent(execution: PipelineExecution): Promise<void> {\r\n    // Emit appropriate events based on pipeline status\r\n    // Implementation will be added when event classes are created\r\n  }\r\n\r\n  private async validatePipelineDefinition(definition: PipelineDefinition): Promise<void> {\r\n    if (!definition.stages || definition.stages.length === 0) {\r\n      throw new Error('Pipeline must have at least one stage');\r\n    }\r\n\r\n    // Validate stage dependencies\r\n    for (const stage of definition.stages) {\r\n      if (stage.dependencies) {\r\n        for (const depId of stage.dependencies) {\r\n          if (!definition.stages.find(s => s.id === depId)) {\r\n            throw new Error(`Invalid dependency: ${depId} for stage: ${stage.id}`);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// Type definitions\r\ninterface PipelineDefinition {\r\n  name: string;\r\n  description?: string;\r\n  stages: StageDefinition[];\r\n  executionStrategy: 'sequential' | 'parallel';\r\n  context?: any;\r\n}\r\n\r\ninterface StageDefinition {\r\n  id: string;\r\n  name: string;\r\n  type: string;\r\n  config?: any;\r\n  dependencies?: string[];\r\n  continueOnFailure?: boolean;\r\n  inputSource?: string;\r\n  input?: any;\r\n}\r\n\r\ninterface PipelineExecution {\r\n  id: string;\r\n  definition: PipelineDefinition;\r\n  status: 'initializing' | 'running' | 'completed' | 'failed' | 'cancelled' | 'cancelling';\r\n  stages: PipelineStage[];\r\n  context: any;\r\n  metrics: PipelineMetrics;\r\n  results: Record<string, any>;\r\n  errors?: PipelineError[];\r\n}\r\n\r\ninterface PipelineStage {\r\n  id: string;\r\n  name: string;\r\n  type: string;\r\n  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped' | 'cancelled';\r\n  config: any;\r\n  dependencies: string[];\r\n  continueOnFailure: boolean;\r\n  inputSource?: string;\r\n  input?: any;\r\n  startTime?: Date;\r\n  endTime?: Date;\r\n  result?: any;\r\n  error?: string;\r\n  retryCount: number;\r\n}\r\n\r\ninterface PipelineMetrics {\r\n  startTime: Date;\r\n  endTime?: Date;\r\n  totalStages: number;\r\n  completedStages: number;\r\n  failedStages: number;\r\n}\r\n\r\ninterface PipelineStatus {\r\n  id: string;\r\n  status: string;\r\n  progress: number;\r\n  currentStage: string | null;\r\n  metrics: PipelineMetrics;\r\n  results: Record<string, any>;\r\n  errors: any[];\r\n}\r\n\r\ninterface PipelineTemplate {\r\n  name: string;\r\n  description: string;\r\n  stages: StageDefinition[];\r\n  executionStrategy: 'sequential' | 'parallel';\r\n}\r\n\r\ninterface PipelineParameters {\r\n  [key: string]: any;\r\n}\r\n\r\nclass PipelineError extends Error {\r\n  constructor(message: string, public readonly pipelineId?: string) {\r\n    super(message);\r\n    this.name = 'PipelineError';\r\n  }\r\n}\r\n"], "version": 3}