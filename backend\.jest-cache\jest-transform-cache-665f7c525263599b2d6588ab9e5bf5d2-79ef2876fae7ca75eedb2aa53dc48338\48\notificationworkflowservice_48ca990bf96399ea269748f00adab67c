ea296bae703801f7788cac8323558ed4
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var NotificationWorkflowService_1;
var _a, _b, _c, _d, _e, _f, _g, _h, _j;
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationWorkflowService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const config_1 = require("@nestjs/config");
const event_emitter_1 = require("@nestjs/event-emitter");
const notification_workflow_entity_1 = require("../entities/notification-workflow.entity");
const workflow_execution_entity_1 = require("../entities/workflow-execution.entity");
const workflow_execution_context_entity_1 = require("../entities/workflow-execution-context.entity");
const workflow_engine_service_1 = require("./workflow-engine.service");
const workflow_rule_engine_service_1 = require("./workflow-rule-engine.service");
const workflow_scheduler_service_1 = require("./workflow-scheduler.service");
const notification_analytics_service_1 = require("./notification-analytics.service");
/**
 * Notification Workflow Service
 *
 * Provides comprehensive workflow management and execution including:
 * - Workflow definition processing and validation with comprehensive rule evaluation
 * - Step-by-step execution engine with conditional logic and branching
 * - Escalation workflow management with time-based triggers and automatic progression
 * - Template workflow integration with dynamic content generation
 * - Workflow analytics and performance tracking with execution metrics
 *
 * Features:
 * - Visual workflow designer support with drag-and-drop interface
 * - Advanced conditional logic with complex rule evaluation
 * - Integration with external systems and APIs
 * - Real-time workflow monitoring and execution tracking
 * - Workflow versioning and rollback capabilities
 * - Performance optimization with intelligent caching and parallel execution
 */
let NotificationWorkflowService = NotificationWorkflowService_1 = class NotificationWorkflowService {
    constructor(workflowRepository, executionRepository, contextRepository, configService, eventEmitter, workflowEngine, ruleEngine, scheduler, analyticsService) {
        this.workflowRepository = workflowRepository;
        this.executionRepository = executionRepository;
        this.contextRepository = contextRepository;
        this.configService = configService;
        this.eventEmitter = eventEmitter;
        this.workflowEngine = workflowEngine;
        this.ruleEngine = ruleEngine;
        this.scheduler = scheduler;
        this.analyticsService = analyticsService;
        this.logger = new common_1.Logger(NotificationWorkflowService_1.name);
    }
    /**
     * Create new workflow
     */
    async createWorkflow(createWorkflowDto, user) {
        try {
            this.logger.debug(`Creating workflow: ${createWorkflowDto.name}`);
            // Validate workflow definition
            const validationResult = await this.validateWorkflowDefinition(createWorkflowDto.definition);
            if (!validationResult.valid) {
                throw new common_1.BadRequestException(`Workflow validation failed: ${validationResult.errors.join(', ')}`);
            }
            // Create workflow entity
            const workflow = this.workflowRepository.create({
                name: createWorkflowDto.name,
                description: createWorkflowDto.description,
                definition: createWorkflowDto.definition,
                status: createWorkflowDto.status || 'draft',
                category: createWorkflowDto.category,
                tags: createWorkflowDto.tags,
                version: '1.0.0',
                isTemplate: createWorkflowDto.isTemplate || false,
                configuration: createWorkflowDto.configuration || {},
                metadata: createWorkflowDto.metadata || {},
                createdBy: user.id,
                updatedBy: user.id,
            });
            const savedWorkflow = await this.workflowRepository.save(workflow);
            // Schedule workflow if it has triggers
            if (createWorkflowDto.definition.triggers?.length > 0) {
                await this.scheduler.scheduleWorkflow(savedWorkflow);
            }
            // Emit workflow created event
            this.eventEmitter.emit('workflow.created', {
                workflowId: savedWorkflow.id,
                name: savedWorkflow.name,
                createdBy: user.id,
                timestamp: new Date(),
            });
            this.logger.debug(`Workflow created successfully: ${savedWorkflow.id}`);
            return {
                id: savedWorkflow.id,
                name: savedWorkflow.name,
                status: savedWorkflow.status,
                version: savedWorkflow.version,
                createdAt: savedWorkflow.createdAt,
            };
        }
        catch (error) {
            this.logger.error(`Failed to create workflow: ${error.message}`, error.stack);
            throw error;
        }
    }
    /**
     * Get workflows with filtering and pagination
     */
    async getWorkflows(query, user) {
        try {
            this.logger.debug('Retrieving workflows');
            const queryBuilder = this.workflowRepository
                .createQueryBuilder('workflow')
                .leftJoinAndSelect('workflow.executions', 'execution')
                .where('1=1');
            // Apply filters
            if (query.status) {
                queryBuilder.andWhere('workflow.status = :status', { status: query.status });
            }
            if (query.category) {
                queryBuilder.andWhere('workflow.category = :category', { category: query.category });
            }
            if (query.search) {
                queryBuilder.andWhere('(workflow.name ILIKE :search OR workflow.description ILIKE :search)', { search: `%${query.search}%` });
            }
            if (query.createdBy) {
                queryBuilder.andWhere('workflow.createdBy = :createdBy', { createdBy: query.createdBy });
            }
            if (query.isTemplate !== undefined) {
                queryBuilder.andWhere('workflow.isTemplate = :isTemplate', { isTemplate: query.isTemplate });
            }
            // Apply sorting
            const sortBy = query.sortBy || 'createdAt';
            const sortOrder = query.sortOrder || 'DESC';
            queryBuilder.orderBy(`workflow.${sortBy}`, sortOrder);
            // Apply pagination
            const page = query.page || 1;
            const limit = query.limit || 20;
            const offset = (page - 1) * limit;
            queryBuilder.skip(offset).take(limit);
            // Execute query
            const [workflows, total] = await queryBuilder.getManyAndCount();
            // Process workflows for response
            const processedWorkflows = workflows.map(workflow => ({
                id: workflow.id,
                name: workflow.name,
                description: workflow.description,
                status: workflow.status,
                category: workflow.category,
                version: workflow.version,
                isTemplate: workflow.isTemplate,
                executionCount: workflow.executions?.length || 0,
                lastExecuted: workflow.executions?.length > 0
                    ? workflow.executions.sort((a, b) => b.startedAt.getTime() - a.startedAt.getTime())[0].startedAt
                    : null,
                createdAt: workflow.createdAt,
                updatedAt: workflow.updatedAt,
            }));
            this.logger.debug(`Retrieved ${workflows.length} workflows`);
            return {
                workflows: processedWorkflows,
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit),
            };
        }
        catch (error) {
            this.logger.error(`Failed to retrieve workflows: ${error.message}`, error.stack);
            throw error;
        }
    }
    /**
     * Get workflow by ID
     */
    async getWorkflowById(id, includeHistory, includeAnalytics, user) {
        try {
            this.logger.debug(`Retrieving workflow: ${id}`);
            const queryBuilder = this.workflowRepository
                .createQueryBuilder('workflow')
                .where('workflow.id = :id', { id });
            if (includeHistory) {
                queryBuilder.leftJoinAndSelect('workflow.executions', 'execution');
            }
            const workflow = await queryBuilder.getOne();
            if (!workflow) {
                throw new common_1.NotFoundException(`Workflow not found: ${id}`);
            }
            // Get analytics if requested
            let analytics = null;
            if (includeAnalytics) {
                analytics = await this.getWorkflowAnalytics(id, '30d', true, user);
            }
            // Get execution history if requested
            let history = null;
            if (includeHistory) {
                history = workflow.executions?.map(execution => ({
                    id: execution.id,
                    status: execution.status,
                    startedAt: execution.startedAt,
                    completedAt: execution.completedAt,
                    duration: execution.duration,
                    stepsCompleted: execution.stepsCompleted,
                    totalSteps: execution.totalSteps,
                    result: execution.result,
                    error: execution.error,
                })) || [];
            }
            this.logger.debug(`Workflow retrieved: ${id}`);
            return {
                id: workflow.id,
                name: workflow.name,
                description: workflow.description,
                definition: workflow.definition,
                status: workflow.status,
                category: workflow.category,
                version: workflow.version,
                isTemplate: workflow.isTemplate,
                configuration: workflow.configuration,
                metadata: workflow.metadata,
                analytics,
                history,
                createdAt: workflow.createdAt,
                updatedAt: workflow.updatedAt,
                createdBy: workflow.createdBy,
                updatedBy: workflow.updatedBy,
            };
        }
        catch (error) {
            this.logger.error(`Failed to retrieve workflow: ${error.message}`, error.stack);
            throw error;
        }
    }
    /**
     * Update workflow
     */
    async updateWorkflow(id, updateWorkflowDto, user) {
        try {
            this.logger.debug(`Updating workflow: ${id}`);
            const workflow = await this.workflowRepository.findOne({ where: { id } });
            if (!workflow) {
                throw new common_1.NotFoundException(`Workflow not found: ${id}`);
            }
            // Validate definition if provided
            if (updateWorkflowDto.definition) {
                const validationResult = await this.validateWorkflowDefinition(updateWorkflowDto.definition);
                if (!validationResult.valid) {
                    throw new common_1.BadRequestException(`Workflow validation failed: ${validationResult.errors.join(', ')}`);
                }
            }
            // Create new version if definition changed
            let newVersion = workflow.version;
            if (updateWorkflowDto.definition && JSON.stringify(updateWorkflowDto.definition) !== JSON.stringify(workflow.definition)) {
                newVersion = this.incrementVersion(workflow.version);
            }
            // Update workflow
            Object.assign(workflow, {
                ...updateWorkflowDto,
                version: newVersion,
                updatedBy: user.id,
                updatedAt: new Date(),
            });
            const savedWorkflow = await this.workflowRepository.save(workflow);
            // Update scheduler if triggers changed
            if (updateWorkflowDto.definition?.triggers) {
                await this.scheduler.updateWorkflowSchedule(savedWorkflow);
            }
            // Emit workflow updated event
            this.eventEmitter.emit('workflow.updated', {
                workflowId: savedWorkflow.id,
                name: savedWorkflow.name,
                version: savedWorkflow.version,
                updatedBy: user.id,
                timestamp: new Date(),
            });
            this.logger.debug(`Workflow updated successfully: ${id}`);
            return {
                id: savedWorkflow.id,
                name: savedWorkflow.name,
                version: savedWorkflow.version,
                updatedAt: savedWorkflow.updatedAt,
            };
        }
        catch (error) {
            this.logger.error(`Failed to update workflow: ${error.message}`, error.stack);
            throw error;
        }
    }
    /**
     * Delete workflow
     */
    async deleteWorkflow(id, user) {
        try {
            this.logger.debug(`Deleting workflow: ${id}`);
            const workflow = await this.workflowRepository.findOne({ where: { id } });
            if (!workflow) {
                throw new common_1.NotFoundException(`Workflow not found: ${id}`);
            }
            // Cancel any running executions
            await this.cancelAllRunningExecutions(id);
            // Remove from scheduler
            await this.scheduler.unscheduleWorkflow(id);
            // Delete workflow and related data
            await this.workflowRepository.remove(workflow);
            // Emit workflow deleted event
            this.eventEmitter.emit('workflow.deleted', {
                workflowId: id,
                name: workflow.name,
                deletedBy: user.id,
                timestamp: new Date(),
            });
            this.logger.debug(`Workflow deleted successfully: ${id}`);
        }
        catch (error) {
            this.logger.error(`Failed to delete workflow: ${error.message}`, error.stack);
            throw error;
        }
    }
    /**
     * Execute workflow
     */
    async executeWorkflow(id, executeWorkflowDto, user) {
        try {
            this.logger.debug(`Executing workflow: ${id}`);
            const workflow = await this.workflowRepository.findOne({ where: { id } });
            if (!workflow) {
                throw new common_1.NotFoundException(`Workflow not found: ${id}`);
            }
            if (workflow.status !== 'active') {
                throw new common_1.BadRequestException(`Workflow is not active: ${workflow.status}`);
            }
            // Create execution record
            const execution = this.executionRepository.create({
                workflowId: id,
                status: 'running',
                input: executeWorkflowDto.input || {},
                context: executeWorkflowDto.context || {},
                triggeredBy: user.id,
                startedAt: new Date(),
                totalSteps: this.countWorkflowSteps(workflow.definition),
                stepsCompleted: 0,
            });
            const savedExecution = await this.executionRepository.save(execution);
            // Start workflow execution
            const executionPromise = this.workflowEngine.executeWorkflow(workflow, savedExecution, executeWorkflowDto);
            // Don't wait for completion, return execution info immediately
            executionPromise.catch(error => {
                this.logger.error(`Workflow execution failed: ${error.message}`, error.stack);
            });
            // Emit execution started event
            this.eventEmitter.emit('workflow.execution.started', {
                executionId: savedExecution.id,
                workflowId: id,
                triggeredBy: user.id,
                timestamp: new Date(),
            });
            this.logger.debug(`Workflow execution started: ${savedExecution.id}`);
            return {
                executionId: savedExecution.id,
                workflowId: id,
                status: 'running',
                startedAt: savedExecution.startedAt,
                estimatedDuration: this.estimateExecutionDuration(workflow),
            };
        }
        catch (error) {
            this.logger.error(`Failed to execute workflow: ${error.message}`, error.stack);
            throw error;
        }
    }
    /**
     * Get execution status
     */
    async getExecutionStatus(workflowId, executionId, user) {
        try {
            this.logger.debug(`Getting execution status: ${executionId}`);
            const execution = await this.executionRepository.findOne({
                where: { id: executionId, workflowId },
                relations: ['contexts'],
            });
            if (!execution) {
                throw new common_1.NotFoundException(`Execution not found: ${executionId}`);
            }
            // Get current step information
            const currentStep = execution.contexts?.find(ctx => ctx.status === 'running')?.stepId || null;
            const steps = execution.contexts?.map(ctx => ({
                stepId: ctx.stepId,
                status: ctx.status,
                startedAt: ctx.startedAt,
                completedAt: ctx.completedAt,
                result: ctx.result,
                error: ctx.error,
            })) || [];
            // Calculate progress
            const progress = execution.totalSteps > 0 ? (execution.stepsCompleted / execution.totalSteps) * 100 : 0;
            this.logger.debug(`Execution status retrieved: ${executionId}`);
            return {
                executionId: execution.id,
                workflowId: execution.workflowId,
                status: execution.status,
                progress,
                currentStep,
                steps,
                startedAt: execution.startedAt,
                completedAt: execution.completedAt,
                duration: execution.duration,
                result: execution.result,
                error: execution.error,
            };
        }
        catch (error) {
            this.logger.error(`Failed to get execution status: ${error.message}`, error.stack);
            throw error;
        }
    }
    /**
     * Cancel execution
     */
    async cancelExecution(workflowId, executionId, user) {
        try {
            this.logger.debug(`Cancelling execution: ${executionId}`);
            const execution = await this.executionRepository.findOne({
                where: { id: executionId, workflowId },
            });
            if (!execution) {
                throw new common_1.NotFoundException(`Execution not found: ${executionId}`);
            }
            if (execution.status !== 'running') {
                throw new common_1.BadRequestException(`Execution is not running: ${execution.status}`);
            }
            // Cancel execution
            await this.workflowEngine.cancelExecution(executionId);
            // Update execution status
            execution.status = 'cancelled';
            execution.completedAt = new Date();
            execution.duration = execution.completedAt.getTime() - execution.startedAt.getTime();
            await this.executionRepository.save(execution);
            // Emit execution cancelled event
            this.eventEmitter.emit('workflow.execution.cancelled', {
                executionId,
                workflowId,
                cancelledBy: user.id,
                timestamp: new Date(),
            });
            this.logger.debug(`Execution cancelled: ${executionId}`);
        }
        catch (error) {
            this.logger.error(`Failed to cancel execution: ${error.message}`, error.stack);
            throw error;
        }
    }
    /**
     * Get workflow analytics
     */
    async getWorkflowAnalytics(id, timeRange, includeStepAnalytics, user) {
        try {
            this.logger.debug(`Getting workflow analytics: ${id}`);
            const timeFilter = this.getTimeRangeFilter(timeRange);
            // Get execution statistics
            const executions = await this.executionRepository
                .createQueryBuilder('execution')
                .where('execution.workflowId = :id', { id })
                .andWhere('execution.startedAt BETWEEN :startDate AND :endDate', timeFilter)
                .getMany();
            const total = executions.length;
            const successful = executions.filter(e => e.status === 'completed').length;
            const failed = executions.filter(e => e.status === 'failed').length;
            const cancelled = executions.filter(e => e.status === 'cancelled').length;
            // Calculate performance metrics
            const completedExecutions = executions.filter(e => e.duration !== null);
            const avgDuration = completedExecutions.length > 0
                ? completedExecutions.reduce((sum, e) => sum + e.duration, 0) / completedExecutions.length
                : 0;
            const durations = completedExecutions.map(e => e.duration).sort((a, b) => a - b);
            const minDuration = durations.length > 0 ? durations[0] : 0;
            const maxDuration = durations.length > 0 ? durations[durations.length - 1] : 0;
            const avgStepsCompleted = executions.length > 0
                ? executions.reduce((sum, e) => sum + e.stepsCompleted, 0) / executions.length
                : 0;
            // Get trends
            const trends = await this.calculateExecutionTrends(id, timeRange);
            // Get step analytics if requested
            let stepAnalytics = null;
            if (includeStepAnalytics) {
                stepAnalytics = await this.getStepAnalytics(id, timeRange);
            }
            this.logger.debug(`Workflow analytics retrieved: ${id}`);
            return {
                workflowId: id,
                executions: {
                    total,
                    successful,
                    failed,
                    cancelled,
                    successRate: total > 0 ? (successful / total) * 100 : 0,
                },
                performance: {
                    avgDuration,
                    minDuration,
                    maxDuration,
                    avgStepsCompleted,
                },
                trends,
                stepAnalytics,
                timeRange,
                generatedAt: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`Failed to get workflow analytics: ${error.message}`, error.stack);
            throw error;
        }
    }
    /**
     * Additional methods would be implemented here for:
     * - validateWorkflow, getWorkflowTemplates, cloneWorkflow
     * - Various helper methods for validation, analytics, and processing
     */
    /**
     * Validate workflow definition
     */
    async validateWorkflowDefinition(definition) {
        const errors = [];
        const warnings = [];
        // Basic structure validation
        if (!definition.steps || !Array.isArray(definition.steps)) {
            errors.push('Workflow must have steps array');
        }
        if (!definition.startStep) {
            errors.push('Workflow must have startStep defined');
        }
        // Validate steps
        if (definition.steps) {
            for (const step of definition.steps) {
                if (!step.id) {
                    errors.push('Each step must have an id');
                }
                if (!step.type) {
                    errors.push('Each step must have a type');
                }
            }
        }
        return {
            valid: errors.length === 0,
            errors,
            warnings,
        };
    }
    /**
     * Additional private helper methods would be implemented here
     */
    incrementVersion(version) {
        const parts = version.split('.');
        const patch = parseInt(parts[2]) + 1;
        return `${parts[0]}.${parts[1]}.${patch}`;
    }
    countWorkflowSteps(definition) {
        return definition.steps?.length || 0;
    }
    estimateExecutionDuration(workflow) {
        // Simple estimation based on step count
        return (workflow.definition.steps?.length || 1) * 30000; // 30 seconds per step
    }
    async cancelAllRunningExecutions(workflowId) {
        const runningExecutions = await this.executionRepository.find({
            where: { workflowId, status: 'running' },
        });
        for (const execution of runningExecutions) {
            await this.workflowEngine.cancelExecution(execution.id);
        }
    }
    getTimeRangeFilter(timeRange) {
        const endDate = new Date();
        const startDate = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000); // Default 30 days
        return { startDate, endDate };
    }
    async calculateExecutionTrends(workflowId, timeRange) {
        // Implementation would calculate execution trends over time
        return [];
    }
    async getStepAnalytics(workflowId, timeRange) {
        // Implementation would get step-level analytics
        return [];
    }
};
exports.NotificationWorkflowService = NotificationWorkflowService;
exports.NotificationWorkflowService = NotificationWorkflowService = NotificationWorkflowService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(notification_workflow_entity_1.NotificationWorkflow)),
    __param(1, (0, typeorm_1.InjectRepository)(workflow_execution_entity_1.WorkflowExecution)),
    __param(2, (0, typeorm_1.InjectRepository)(workflow_execution_context_entity_1.WorkflowExecutionContext)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _c : Object, typeof (_d = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _d : Object, typeof (_e = typeof event_emitter_1.EventEmitter2 !== "undefined" && event_emitter_1.EventEmitter2) === "function" ? _e : Object, typeof (_f = typeof workflow_engine_service_1.WorkflowEngineService !== "undefined" && workflow_engine_service_1.WorkflowEngineService) === "function" ? _f : Object, typeof (_g = typeof workflow_rule_engine_service_1.WorkflowRuleEngine !== "undefined" && workflow_rule_engine_service_1.WorkflowRuleEngine) === "function" ? _g : Object, typeof (_h = typeof workflow_scheduler_service_1.WorkflowSchedulerService !== "undefined" && workflow_scheduler_service_1.WorkflowSchedulerService) === "function" ? _h : Object, typeof (_j = typeof notification_analytics_service_1.NotificationAnalyticsService !== "undefined" && notification_analytics_service_1.NotificationAnalyticsService) === "function" ? _j : Object])
], NotificationWorkflowService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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