import { BaseEntity } from './base-entity';
import { BaseDomainEvent } from './base-domain-event';
import { UniqueEntityId } from '../value-objects/unique-entity-id.value-object';

/**
 * Base Aggregate Root
 * 
 * Abstract base class for all aggregate roots in the domain layer.
 * Provides event handling capabilities and ensures domain invariants.
 * 
 * Key responsibilities:
 * - Manage domain events within the aggregate boundary
 * - Ensure consistency and invariants across the aggregate
 * - Provide event publishing capabilities
 * - Handle aggregate lifecycle management
 * 
 * @template T The props type for the aggregate root
 */
export abstract class BaseAggregateRoot<T> extends BaseEntity<T> {
  private _domainEvents: BaseDomainEvent[] = [];

  constructor(props: T, id?: UniqueEntityId) {
    super(props, id);
  }

  /**
   * Get all domain events for this aggregate
   */
  get domainEvents(): BaseDomainEvent[] {
    return this._domainEvents.slice(); // Return a copy to prevent external modification
  }

  /**
   * Add a domain event to the aggregate
   * 
   * @param domainEvent The domain event to add
   */
  protected addDomainEvent(domainEvent: BaseDomainEvent): void {
    // Check if event already exists to prevent duplicates
    const existingEvent = this._domainEvents.find(
      event => event.eventId.equals(domainEvent.eventId)
    );

    if (!existingEvent) {
      this._domainEvents.push(domainEvent);
    }
  }

  /**
   * Remove a specific domain event
   * 
   * @param domainEvent The domain event to remove
   */
  protected removeDomainEvent(domainEvent: BaseDomainEvent): void {
    const index = this._domainEvents.findIndex(
      event => event.eventId.equals(domainEvent.eventId)
    );

    if (index !== -1) {
      this._domainEvents.splice(index, 1);
    }
  }

  /**
   * Clear all domain events
   * This is typically called after events have been published
   */
  public clearEvents(): void {
    this._domainEvents = [];
  }

  /**
   * Mark events as dispatched
   * This is called by the event dispatcher after successful publishing
   */
  public markEventsForDispatch(): void {
    this._domainEvents.forEach(event => {
      event.markAsDispatched();
    });
  }

  /**
   * Check if the aggregate has any unpublished events
   */
  public hasUnpublishedEvents(): boolean {
    return this._domainEvents.some(event => !event.isDispatched);
  }

  /**
   * Get only unpublished events
   */
  public getUnpublishedEvents(): BaseDomainEvent[] {
    return this._domainEvents.filter(event => !event.isDispatched);
  }

  /**
   * Get events by type
   * 
   * @param eventType The type of events to retrieve
   */
  public getEventsByType<TEvent extends BaseDomainEvent>(
    eventType: new (...args: any[]) => TEvent
  ): TEvent[] {
    return this._domainEvents.filter(
      event => event instanceof eventType
    ) as TEvent[];
  }

  /**
   * Check if aggregate has events of a specific type
   * 
   * @param eventType The type of events to check for
   */
  public hasEventsOfType<TEvent extends BaseDomainEvent>(
    eventType: new (...args: any[]) => TEvent
  ): boolean {
    return this._domainEvents.some(event => event instanceof eventType);
  }

  /**
   * Get the aggregate version based on the number of events
   * This can be used for optimistic concurrency control
   */
  public getVersion(): number {
    return this._domainEvents.length;
  }

  /**
   * Create a snapshot of the aggregate state
   * Useful for event sourcing scenarios
   */
  public createSnapshot(): {
    aggregateId: string;
    version: number;
    state: T;
    timestamp: Date;
  } {
    return {
      aggregateId: this._id.toString(),
      version: this.getVersion(),
      state: { ...this.props },
      timestamp: new Date(),
    };
  }

  /**
   * Validate aggregate invariants
   * Override in concrete implementations to add specific business rules
   */
  protected validateInvariants(): void {
    // Base implementation - override in concrete classes
    if (!this._id) {
      throw new Error('Aggregate must have a valid ID');
    }
  }

  /**
   * Apply business rules and validate state
   * Called before any state changes
   */
  protected applyBusinessRules(): void {
    this.validateInvariants();
  }

  /**
   * Convert aggregate to JSON representation
   * Includes domain events for debugging purposes
   */
  public toJSON(): Record<string, any> {
    return {
      ...super.toJSON(),
      domainEvents: this._domainEvents.map(event => ({
        eventType: event.constructor.name,
        eventId: event.eventId.toString(),
        occurredOn: event.occurredOn,
        isDispatched: event.isDispatched,
      })),
      version: this.getVersion(),
    };
  }

  /**
   * Create a deep copy of the aggregate
   * Useful for testing and state management
   */
  public clone(): this {
    const clonedProps = JSON.parse(JSON.stringify(this.props));
    const cloned = Object.create(Object.getPrototypeOf(this));
    cloned.props = clonedProps;
    cloned._id = this._id;
    cloned._domainEvents = [...this._domainEvents];
    return cloned;
  }

  /**
   * Compare two aggregates for equality
   * Aggregates are equal if they have the same ID and version
   */
  public equals(object?: BaseAggregateRoot<T>): boolean {
    if (!super.equals(object)) {
      return false;
    }

    return this.getVersion() === object.getVersion();
  }

  /**
   * Get domain events (alias for domainEvents getter)
   */
  public getDomainEvents(): BaseDomainEvent[] {
    return this.domainEvents;
  }

  /**
   * Clear domain events (alias for clearEvents)
   */
  public clearDomainEvents(): void {
    this.clearEvents();
  }
}
