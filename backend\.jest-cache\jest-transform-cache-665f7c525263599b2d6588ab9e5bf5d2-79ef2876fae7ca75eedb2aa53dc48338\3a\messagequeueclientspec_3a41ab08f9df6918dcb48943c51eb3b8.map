{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\communication\\__tests__\\message-queue.client.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,2CAA+C;AAC/C,uCAA6C;AAC7C,kEAA6D;AAG7D,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;IAClC,IAAI,MAA0B,CAAC;IAC/B,IAAI,aAAyC,CAAC;IAC9C,IAAI,aAAiC,CAAC;IACtC,IAAI,aAAiC,CAAC;IACtC,IAAI,eAAmC,CAAC;IACxC,IAAI,UAA8B,CAAC;IAEnC,MAAM,eAAe,GAAG,GAAuB,EAAE,CAAC,CAAC;QACjD,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;QACd,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;QACrB,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;QACpB,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE;QACvB,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;QACpB,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;QACrB,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;QACpB,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;QACrB,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;QACnB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;QAChB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;KACT,CAAA,CAAC;IAEV,MAAM,aAAa,GAAG,CAAC,EAAU,EAAE,IAAS,EAAE,KAAK,GAAG,SAAS,EAAoB,EAAE,CAAC,CAAC;QACrF,EAAE;QACF,IAAI;QACJ,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;QACrB,IAAI,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE;QACrB,YAAY,EAAE,CAAC;QACf,WAAW,EAAE,SAAS;QACtB,YAAY,EAAE,SAAS;QACvB,WAAW,EAAE,SAAS;QACtB,UAAU,EAAE,SAAS;QACrB,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,KAAK,CAAC;QAC5C,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC;QACtC,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;KACT,CAAA,CAAC;IAEV,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,iBAAiB,GAAG;YACxB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,CAAC,GAAW,EAAE,YAAkB,EAAE,EAAE;gBACpE,MAAM,MAAM,GAAG;oBACb,mBAAmB,EAAE,CAAC;oBACtB,uBAAuB,EAAE,IAAI;oBAC7B,2BAA2B,EAAE,GAAG;oBAChC,uBAAuB,EAAE,EAAE;iBAC5B,CAAC;gBACF,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC;YACrC,CAAC,CAAC;SACH,CAAC;QAEF,aAAa,GAAG,eAAe,EAAE,CAAC;QAClC,aAAa,GAAG,eAAe,EAAE,CAAC;QAClC,eAAe,GAAG,eAAe,EAAE,CAAC;QACpC,UAAU,GAAG,eAAe,EAAE,CAAC;QAE/B,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,yCAAkB;gBAClB,EAAE,OAAO,EAAE,sBAAa,EAAE,QAAQ,EAAE,iBAAiB,EAAE;gBACvD,EAAE,OAAO,EAAE,IAAA,oBAAa,EAAC,aAAa,CAAC,EAAE,QAAQ,EAAE,aAAa,EAAE;gBAClE,EAAE,OAAO,EAAE,IAAA,oBAAa,EAAC,aAAa,CAAC,EAAE,QAAQ,EAAE,aAAa,EAAE;gBAClE,EAAE,OAAO,EAAE,IAAA,oBAAa,EAAC,eAAe,CAAC,EAAE,QAAQ,EAAE,eAAe,EAAE;gBACtE,EAAE,OAAO,EAAE,IAAA,oBAAa,EAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE;aAC7D;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,MAAM,GAAG,MAAM,CAAC,GAAG,CAAqB,yCAAkB,CAAC,CAAC;QAC5D,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,sBAAa,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,OAAO,GAAG;gBACd,SAAS,EAAE,SAAS;gBACpB,IAAI,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE;gBACnC,KAAK,EAAE,oBAAoB;aAC5B,CAAC;YAEF,MAAM,OAAO,GAAG,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAClD,aAAa,CAAC,GAAG,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAE7C,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAE1D,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC;gBAC3B,KAAK,EAAE,SAAS;gBAChB,SAAS,EAAE,SAAS;gBACpB,SAAS,EAAE,aAAa;gBACxB,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE,CAAC;gBACX,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAC5C,cAAc,EACd,OAAO,EACP,MAAM,CAAC,gBAAgB,CAAC;gBACtB,QAAQ,EAAE,CAAC;gBACX,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAC/B,IAAI,EAAE,aAAa;oBACnB,KAAK,EAAE,IAAI;iBACZ,CAAC;gBACF,gBAAgB,EAAE,GAAG;gBACrB,YAAY,EAAE,EAAE;aACjB,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,OAAO,GAAG;gBACd,SAAS,EAAE,SAAS;gBACpB,IAAI,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE;aACxB,CAAC;YAEF,MAAM,OAAO,GAAG;gBACd,QAAQ,EAAE,CAAC;gBACX,KAAK,EAAE,IAAI;gBACX,OAAO,EAAE,KAAK;gBACd,QAAQ,EAAE,CAAC;aACZ,CAAC;YAEF,MAAM,OAAO,GAAG,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAClD,aAAa,CAAC,GAAG,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAE7C,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,oBAAoB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAEnE,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEhC,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAC5C,cAAc,EACd,OAAO,EACP,MAAM,CAAC,gBAAgB,CAAC;gBACtB,QAAQ,EAAE,CAAC;gBACX,KAAK,EAAE,IAAI;gBACX,OAAO,EAAE,KAAK;gBACd,QAAQ,EAAE,CAAC;aACZ,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,OAAO,GAAG;gBACd,SAAS,EAAE,WAAW;gBACtB,IAAI,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE;aACxB,CAAC;YAEF,aAAa,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;YAE9D,MAAM,MAAM,CAAC,MAAM,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,kCAAkC,CAAC,CAAC;QACzG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,OAAO,GAAG;gBACd,SAAS,EAAE,WAAW;gBACtB,YAAY,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;gBACnD,WAAW,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE;aACpC,CAAC;YAEF,MAAM,OAAO,GAAG,aAAa,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;YACxD,aAAa,CAAC,GAAG,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAE7C,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAE1D,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC;gBAC3B,KAAK,EAAE,eAAe;gBACtB,SAAS,EAAE,WAAW;gBACtB,SAAS,EAAE,aAAa;gBACxB,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE,EAAE,EAAE,uCAAuC;gBACrD,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAC5C,aAAa,EACb,OAAO,EACP,MAAM,CAAC,gBAAgB,CAAC;gBACtB,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,OAAO,EAAE,iBAAiB;aACpC,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,OAAO,GAAG;gBACd,SAAS,EAAE,UAAU;gBACrB,KAAK,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;gBAC9B,KAAK,EAAE,eAAe;aACvB,CAAC;YAEF,MAAM,OAAO,GAAG,aAAa,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;YACvD,eAAe,CAAC,GAAG,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAE/C,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;YAE5D,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC;gBAC3B,KAAK,EAAE,cAAc;gBACrB,SAAS,EAAE,UAAU;gBACrB,SAAS,EAAE,eAAe;gBAC1B,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE,CAAC,EAAE,0CAA0C;gBACvD,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAC9C,SAAS,EACT,OAAO,EACP,MAAM,CAAC,gBAAgB,CAAC;gBACtB,QAAQ,EAAE,CAAC;gBACX,OAAO,EAAE,KAAK,EAAE,qBAAqB;aACtC,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,OAAO,GAAG;gBACd,SAAS,EAAE,WAAW;gBACtB,KAAK,EAAE;oBACL,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,UAAmB,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;oBAChE,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,YAAqB,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;iBACnE;aACF,CAAC;YAEF,MAAM,OAAO,GAAG,aAAa,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;YACxD,UAAU,CAAC,GAAG,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAE1C,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAEvD,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC;gBAC3B,KAAK,EAAE,eAAe;gBACtB,SAAS,EAAE,WAAW;gBACtB,SAAS,EAAE,UAAU;gBACrB,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE,CAAC,EAAE,mCAAmC;gBAChD,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,oBAAoB,CACzC,eAAe,EACf,OAAO,EACP,MAAM,CAAC,gBAAgB,CAAC;gBACtB,QAAQ,EAAE,CAAC;gBACX,OAAO,EAAE,OAAO,EAAE,qBAAqB;aACxC,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,KAAK,GAAG,SAAS,CAAC;YACxB,MAAM,SAAS,GAAG,aAAa,CAAC;YAEhC,MAAM,OAAO,GAAG,aAAa,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,QAAQ,CAAC,CAAC;YACzE,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YACrC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;YAExC,aAAa,CAAC,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAChD,aAAa,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAE,IAAI,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YAE7E,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YAE3D,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC;gBAC3B,KAAK;gBACL,SAAS;gBACT,MAAM,EAAE,QAAQ;gBAChB,QAAQ,EAAE,EAAE;gBACZ,IAAI,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE;gBAC9B,QAAQ,EAAE,CAAC;gBACX,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;gBAC3B,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;gBAC7B,IAAI,EAAE,CAAC,oBAAoB,CAAC;gBAC5B,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;aAC5B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YAC3C,MAAM,KAAK,GAAG,iBAAiB,CAAC;YAChC,MAAM,SAAS,GAAG,aAAa,CAAC;YAEhC,aAAa,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE7C,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YAE3D,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC;gBAC3B,KAAK;gBACL,SAAS;gBACT,MAAM,EAAE,WAAW;gBACnB,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;aAC5B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;QACzB,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,KAAK,GAAG,SAAS,CAAC;YACxB,MAAM,SAAS,GAAG,aAAa,CAAC;YAEhC,MAAM,OAAO,GAAG,aAAa,CAAC,KAAK,EAAE,EAAE,EAAE,SAAS,CAAC,CAAC;YACpD,aAAa,CAAC,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAEhD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YAExD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1B,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,gBAAgB,EAAE,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,KAAK,GAAG,iBAAiB,CAAC;YAChC,MAAM,SAAS,GAAG,aAAa,CAAC;YAEhC,aAAa,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE7C,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YAExD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,KAAK,GAAG,SAAS,CAAC;YACxB,MAAM,SAAS,GAAG,aAAa,CAAC;YAEhC,MAAM,OAAO,GAAG,aAAa,CAAC,KAAK,EAAE,EAAE,EAAE,WAAW,CAAC,CAAC;YACtD,aAAa,CAAC,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAEhD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YAExD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3B,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;QACxB,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,KAAK,GAAG,SAAS,CAAC;YACxB,MAAM,SAAS,GAAG,aAAa,CAAC;YAEhC,MAAM,OAAO,GAAG,aAAa,CAAC,KAAK,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;YACnD,aAAa,CAAC,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAEhD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YAEvD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1B,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,KAAK,GAAG,iBAAiB,CAAC;YAChC,MAAM,SAAS,GAAG,aAAa,CAAC;YAEhC,aAAa,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE7C,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YAEvD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,KAAK,GAAG,SAAS,CAAC;YACxB,MAAM,SAAS,GAAG,aAAa,CAAC;YAEhC,MAAM,OAAO,GAAG,aAAa,CAAC,KAAK,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;YACnD,aAAa,CAAC,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAEhD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YAEvD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3B,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,SAAS,GAAG,aAAa,CAAC;YAEhC,aAAa,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC,EAAE,EAAE,EAAE,CAAQ,CAAC,CAAC,CAAC,YAAY;YACzE,aAAa,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAQ,CAAC,CAAC,CAAC,WAAW;YACnE,aAAa,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAQ,CAAC,CAAC,CAAC,cAAc;YACjF,aAAa,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAQ,CAAC,CAAC,CAAC,WAAW;YACnE,aAAa,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAS,CAAC,CAAC,CAAC,YAAY;YACnE,aAAa,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAS,CAAC,CAAC,CAAC,WAAW;YACjE,aAAa,CAAC,QAAQ,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAEhD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;YAErD,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC;gBAC3B,SAAS;gBACT,MAAM,EAAE;oBACN,OAAO,EAAE,CAAC;oBACV,MAAM,EAAE,CAAC;oBACT,SAAS,EAAE,CAAC;oBACZ,MAAM,EAAE,CAAC;oBACT,OAAO,EAAE,CAAC;oBACV,MAAM,EAAE,CAAC;iBACV;gBACD,QAAQ,EAAE,KAAK;gBACf,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;aAC5B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,SAAS,GAAG,aAAa,CAAC;YAChC,MAAM,MAAM,GAAG,SAAS,CAAC;YAEzB,MAAM,QAAQ,GAAG;gBACf,aAAa,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE,SAAS,CAAC;gBACzD,aAAa,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE,SAAS,CAAC;aAC1D,CAAC;YAEF,aAAa,CAAC,UAAU,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAErD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,eAAe,CAAC,SAAS,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YAEtE,MAAM,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC;gBAC9B,KAAK,EAAE,OAAO;gBACd,SAAS;gBACT,MAAM,EAAE,SAAS;gBACjB,IAAI,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE;gBAC5B,QAAQ,EAAE,CAAC;gBACX,QAAQ,EAAE,CAAC;gBACX,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;aAC5B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,SAAS,GAAG,aAAa,CAAC;YAChC,MAAM,MAAM,GAAG,SAAgB,CAAC;YAEhC,MAAM,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAChG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,SAAS,GAAG,aAAa,CAAC;YAChC,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,WAAW;YAE9C,aAAa,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAQ,CAAC,CAAC,CAAC,iBAAiB;YAE7E,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,UAAU,CAAC,SAAS,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;YAEtE,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACvB,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC1B,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,SAAS,GAAG,aAAa,CAAC;YAEhC,MAAM,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YAEnC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,SAAS,GAAG,aAAa,CAAC;YAEhC,MAAM,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YAEpC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,gBAAgB,EAAE,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,SAAS,GAAG,aAAa,CAAC;YAChC,MAAM,OAAO,GAAG,cAAc,CAAC;YAC/B,MAAM,OAAO,GAAG,EAAE,SAAS,EAAE,eAAe,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,CAAC;YACxE,MAAM,YAAY,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,oBAAoB;YAEvE,MAAM,OAAO,GAAG,aAAa,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;YACxD,aAAa,CAAC,GAAG,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAE7C,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;YAEnF,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC;gBAC3B,KAAK,EAAE,eAAe;gBACtB,SAAS,EAAE,eAAe;gBAC1B,SAAS;gBACT,MAAM,EAAE,SAAS;gBACjB,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBACzB,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAC5C,OAAO,EACP,OAAO,EACP,MAAM,CAAC,gBAAgB,CAAC;gBACtB,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC1B,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,SAAS,GAAG,aAAa,CAAC;YAChC,MAAM,OAAO,GAAG,cAAc,CAAC;YAC/B,MAAM,OAAO,GAAG,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,CAAC;YACnE,MAAM,YAAY,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,eAAe;YAElE,MAAM,MAAM,CACV,MAAM,CAAC,WAAW,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,CAAC,CAC9D,CAAC,OAAO,CAAC,OAAO,CAAC,qCAAqC,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,YAAY,GAAG,eAAe,CAAC;YAErC,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC7C,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,GAAW,EAAE,YAAkB,EAAE,EAAE;gBACvE,MAAM,MAAM,GAAG;oBACb,mBAAmB,EAAE,CAAC;oBACtB,uBAAuB,EAAE,IAAI;oBAC7B,2BAA2B,EAAE,GAAG;oBAChC,uBAAuB,EAAE,GAAG;iBAC7B,CAAC;gBACF,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC;YACrC,CAAC,CAAC,CAAC;YAEH,4CAA4C;YAC5C,MAAM,SAAS,GAAG,IAAI,yCAAkB,CACtC,aAAa,EACb,aAAa,EACb,eAAe,EACf,UAAU,EACV,aAAa,CACd,CAAC;YAEF,MAAM,CAAE,SAAiB,CAAC,iBAAiB,CAAC,CAAC,aAAa,CAAC;gBACzD,QAAQ,EAAE,CAAC;gBACX,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAC/B,KAAK,EAAE,IAAI;iBACZ,CAAC;gBACF,gBAAgB,EAAE,GAAG;gBACrB,YAAY,EAAE,GAAG;aAClB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,KAAK,GAAG,WAAW,CAAC;YAC1B,MAAM,SAAS,GAAG,aAAa,CAAC;YAEhC,aAAa,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC,CAAC;YAE5E,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;QAClG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;YAC5C,MAAM,GAAG,GAAI,MAAc,CAAC,iBAAiB,EAAE,CAAC;YAChD,MAAM,GAAG,GAAI,MAAc,CAAC,iBAAiB,EAAE,CAAC;YAEhD,MAAM,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1B,MAAM,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;YAC1B,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC1B,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAChC,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\communication\\__tests__\\message-queue.client.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { getQueueToken } from '@nestjs/bull';\r\nimport { MessageQueueClient } from '../message-queue.client';\r\nimport { Queue, Job } from 'bull';\r\n\r\ndescribe('MessageQueueClient', () => {\r\n  let client: MessageQueueClient;\r\n  let configService: jest.Mocked<ConfigService>;\r\n  let analysisQueue: jest.Mocked<Queue>;\r\n  let trainingQueue: jest.Mocked<Queue>;\r\n  let predictionQueue: jest.Mocked<Queue>;\r\n  let batchQueue: jest.Mocked<Queue>;\r\n\r\n  const createMockQueue = (): jest.Mocked<Queue> => ({\r\n    add: jest.fn(),\r\n    getJob: jest.fn(),\r\n    getWaiting: jest.fn(),\r\n    getActive: jest.fn(),\r\n    getCompleted: jest.fn(),\r\n    getFailed: jest.fn(),\r\n    getDelayed: jest.fn(),\r\n    getPaused: jest.fn(),\r\n    getJobLogs: jest.fn(),\r\n    isPaused: jest.fn(),\r\n    pause: jest.fn(),\r\n    resume: jest.fn(),\r\n    clean: jest.fn(),\r\n  } as any);\r\n\r\n  const createMockJob = (id: string, data: any, state = 'waiting'): jest.Mocked<Job> => ({\r\n    id,\r\n    data,\r\n    timestamp: Date.now(),\r\n    opts: { attempts: 3 },\r\n    attemptsMade: 0,\r\n    returnvalue: undefined,\r\n    failedReason: undefined,\r\n    processedOn: undefined,\r\n    finishedOn: undefined,\r\n    getState: jest.fn().mockResolvedValue(state),\r\n    progress: jest.fn().mockReturnValue(0),\r\n    remove: jest.fn(),\r\n    retry: jest.fn(),\r\n  } as any);\r\n\r\n  beforeEach(async () => {\r\n    const mockConfigService = {\r\n      get: jest.fn().mockImplementation((key: string, defaultValue?: any) => {\r\n        const config = {\r\n          'ai.queue.attempts': 3,\r\n          'ai.queue.backoffDelay': 2000,\r\n          'ai.queue.removeOnComplete': 100,\r\n          'ai.queue.removeOnFail': 50,\r\n        };\r\n        return config[key] || defaultValue;\r\n      }),\r\n    };\r\n\r\n    analysisQueue = createMockQueue();\r\n    trainingQueue = createMockQueue();\r\n    predictionQueue = createMockQueue();\r\n    batchQueue = createMockQueue();\r\n\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [\r\n        MessageQueueClient,\r\n        { provide: ConfigService, useValue: mockConfigService },\r\n        { provide: getQueueToken('ai-analysis'), useValue: analysisQueue },\r\n        { provide: getQueueToken('ai-training'), useValue: trainingQueue },\r\n        { provide: getQueueToken('ai-prediction'), useValue: predictionQueue },\r\n        { provide: getQueueToken('ai-batch'), useValue: batchQueue },\r\n      ],\r\n    }).compile();\r\n\r\n    client = module.get<MessageQueueClient>(MessageQueueClient);\r\n    configService = module.get(ConfigService);\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  describe('queueAnalysisRequest', () => {\r\n    it('should queue analysis request successfully', async () => {\r\n      const payload = {\r\n        requestId: 'req-123',\r\n        data: { event: 'suspicious_login' },\r\n        model: 'threat-detector-v1',\r\n      };\r\n\r\n      const mockJob = createMockJob('job-123', payload);\r\n      analysisQueue.add.mockResolvedValue(mockJob);\r\n\r\n      const result = await client.queueAnalysisRequest(payload);\r\n\r\n      expect(result).toMatchObject({\r\n        jobId: 'job-123',\r\n        requestId: 'req-123',\r\n        queueName: 'ai-analysis',\r\n        status: 'queued',\r\n        priority: 0,\r\n        delay: 0,\r\n        timestamp: expect.any(Date),\r\n      });\r\n\r\n      expect(analysisQueue.add).toHaveBeenCalledWith(\r\n        'analyze-data',\r\n        payload,\r\n        expect.objectContaining({\r\n          attempts: 3,\r\n          backoff: expect.objectContaining({\r\n            type: 'exponential',\r\n            delay: 2000,\r\n          }),\r\n          removeOnComplete: 100,\r\n          removeOnFail: 50,\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should handle analysis request with custom options', async () => {\r\n      const payload = {\r\n        requestId: 'req-456',\r\n        data: { event: 'test' },\r\n      };\r\n\r\n      const options = {\r\n        priority: 5,\r\n        delay: 1000,\r\n        timeout: 60000,\r\n        attempts: 5,\r\n      };\r\n\r\n      const mockJob = createMockJob('job-456', payload);\r\n      analysisQueue.add.mockResolvedValue(mockJob);\r\n\r\n      const result = await client.queueAnalysisRequest(payload, options);\r\n\r\n      expect(result.priority).toBe(5);\r\n      expect(result.delay).toBe(1000);\r\n\r\n      expect(analysisQueue.add).toHaveBeenCalledWith(\r\n        'analyze-data',\r\n        payload,\r\n        expect.objectContaining({\r\n          priority: 5,\r\n          delay: 1000,\r\n          timeout: 60000,\r\n          attempts: 5,\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should handle analysis request failure', async () => {\r\n      const payload = {\r\n        requestId: 'req-error',\r\n        data: { event: 'test' },\r\n      };\r\n\r\n      analysisQueue.add.mockRejectedValue(new Error('Queue error'));\r\n\r\n      await expect(client.queueAnalysisRequest(payload)).rejects.toThrow('Failed to queue analysis request');\r\n    });\r\n  });\r\n\r\n  describe('queueTrainingRequest', () => {\r\n    it('should queue training request successfully', async () => {\r\n      const payload = {\r\n        requestId: 'train-123',\r\n        trainingData: [{ input: 'test', output: 'result' }],\r\n        modelConfig: { type: 'classifier' },\r\n      };\r\n\r\n      const mockJob = createMockJob('job-train-123', payload);\r\n      trainingQueue.add.mockResolvedValue(mockJob);\r\n\r\n      const result = await client.queueTrainingRequest(payload);\r\n\r\n      expect(result).toMatchObject({\r\n        jobId: 'job-train-123',\r\n        requestId: 'train-123',\r\n        queueName: 'ai-training',\r\n        status: 'queued',\r\n        priority: 10, // Default higher priority for training\r\n        timestamp: expect.any(Date),\r\n      });\r\n\r\n      expect(trainingQueue.add).toHaveBeenCalledWith(\r\n        'train-model',\r\n        payload,\r\n        expect.objectContaining({\r\n          priority: 10,\r\n          timeout: 3600000, // 1 hour timeout\r\n        })\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('queuePredictionRequest', () => {\r\n    it('should queue prediction request successfully', async () => {\r\n      const payload = {\r\n        requestId: 'pred-123',\r\n        input: { features: [1, 2, 3] },\r\n        model: 'classifier-v2',\r\n      };\r\n\r\n      const mockJob = createMockJob('job-pred-123', payload);\r\n      predictionQueue.add.mockResolvedValue(mockJob);\r\n\r\n      const result = await client.queuePredictionRequest(payload);\r\n\r\n      expect(result).toMatchObject({\r\n        jobId: 'job-pred-123',\r\n        requestId: 'pred-123',\r\n        queueName: 'ai-prediction',\r\n        status: 'queued',\r\n        priority: 5, // Default higher priority for predictions\r\n        timestamp: expect.any(Date),\r\n      });\r\n\r\n      expect(predictionQueue.add).toHaveBeenCalledWith(\r\n        'predict',\r\n        payload,\r\n        expect.objectContaining({\r\n          priority: 5,\r\n          timeout: 30000, // 30 seconds timeout\r\n        })\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('queueBatchRequest', () => {\r\n    it('should queue batch request successfully', async () => {\r\n      const payload = {\r\n        requestId: 'batch-123',\r\n        items: [\r\n          { id: '1', type: 'analysis' as const, data: { event: 'test1' } },\r\n          { id: '2', type: 'prediction' as const, data: { input: 'test2' } },\r\n        ],\r\n      };\r\n\r\n      const mockJob = createMockJob('job-batch-123', payload);\r\n      batchQueue.add.mockResolvedValue(mockJob);\r\n\r\n      const result = await client.queueBatchRequest(payload);\r\n\r\n      expect(result).toMatchObject({\r\n        jobId: 'job-batch-123',\r\n        requestId: 'batch-123',\r\n        queueName: 'ai-batch',\r\n        status: 'queued',\r\n        priority: 1, // Default lower priority for batch\r\n        timestamp: expect.any(Date),\r\n      });\r\n\r\n      expect(batchQueue.add).toHaveBeenCalledWith(\r\n        'process-batch',\r\n        payload,\r\n        expect.objectContaining({\r\n          priority: 1,\r\n          timeout: 1800000, // 30 minutes timeout\r\n        })\r\n      );\r\n    });\r\n  });\r\n\r\n  describe('getJobStatus', () => {\r\n    it('should get job status successfully', async () => {\r\n      const jobId = 'job-123';\r\n      const queueName = 'ai-analysis';\r\n\r\n      const mockJob = createMockJob(jobId, { requestId: 'req-123' }, 'active');\r\n      mockJob.progress.mockReturnValue(50);\r\n      mockJob.processedOn = Date.now() - 1000;\r\n\r\n      analysisQueue.getJob.mockResolvedValue(mockJob);\r\n      analysisQueue.getJobLogs.mockResolvedValue({ logs: ['Processing started'] });\r\n\r\n      const result = await client.getJobStatus(jobId, queueName);\r\n\r\n      expect(result).toMatchObject({\r\n        jobId,\r\n        queueName,\r\n        status: 'active',\r\n        progress: 50,\r\n        data: { requestId: 'req-123' },\r\n        attempts: 0,\r\n        maxAttempts: 3,\r\n        createdAt: expect.any(Date),\r\n        processedAt: expect.any(Date),\r\n        logs: ['Processing started'],\r\n        timestamp: expect.any(Date),\r\n      });\r\n    });\r\n\r\n    it('should handle job not found', async () => {\r\n      const jobId = 'nonexistent-job';\r\n      const queueName = 'ai-analysis';\r\n\r\n      analysisQueue.getJob.mockResolvedValue(null);\r\n\r\n      const result = await client.getJobStatus(jobId, queueName);\r\n\r\n      expect(result).toMatchObject({\r\n        jobId,\r\n        queueName,\r\n        status: 'not_found',\r\n        timestamp: expect.any(Date),\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('cancelJob', () => {\r\n    it('should cancel job successfully', async () => {\r\n      const jobId = 'job-123';\r\n      const queueName = 'ai-analysis';\r\n\r\n      const mockJob = createMockJob(jobId, {}, 'waiting');\r\n      analysisQueue.getJob.mockResolvedValue(mockJob);\r\n\r\n      const result = await client.cancelJob(jobId, queueName);\r\n\r\n      expect(result).toBe(true);\r\n      expect(mockJob.remove).toHaveBeenCalled();\r\n    });\r\n\r\n    it('should handle job not found for cancellation', async () => {\r\n      const jobId = 'nonexistent-job';\r\n      const queueName = 'ai-analysis';\r\n\r\n      analysisQueue.getJob.mockResolvedValue(null);\r\n\r\n      const result = await client.cancelJob(jobId, queueName);\r\n\r\n      expect(result).toBe(false);\r\n    });\r\n\r\n    it('should not cancel completed job', async () => {\r\n      const jobId = 'job-123';\r\n      const queueName = 'ai-analysis';\r\n\r\n      const mockJob = createMockJob(jobId, {}, 'completed');\r\n      analysisQueue.getJob.mockResolvedValue(mockJob);\r\n\r\n      const result = await client.cancelJob(jobId, queueName);\r\n\r\n      expect(result).toBe(false);\r\n      expect(mockJob.remove).not.toHaveBeenCalled();\r\n    });\r\n  });\r\n\r\n  describe('retryJob', () => {\r\n    it('should retry failed job successfully', async () => {\r\n      const jobId = 'job-123';\r\n      const queueName = 'ai-analysis';\r\n\r\n      const mockJob = createMockJob(jobId, {}, 'failed');\r\n      analysisQueue.getJob.mockResolvedValue(mockJob);\r\n\r\n      const result = await client.retryJob(jobId, queueName);\r\n\r\n      expect(result).toBe(true);\r\n      expect(mockJob.retry).toHaveBeenCalled();\r\n    });\r\n\r\n    it('should handle job not found for retry', async () => {\r\n      const jobId = 'nonexistent-job';\r\n      const queueName = 'ai-analysis';\r\n\r\n      analysisQueue.getJob.mockResolvedValue(null);\r\n\r\n      const result = await client.retryJob(jobId, queueName);\r\n\r\n      expect(result).toBe(false);\r\n    });\r\n\r\n    it('should not retry non-failed job', async () => {\r\n      const jobId = 'job-123';\r\n      const queueName = 'ai-analysis';\r\n\r\n      const mockJob = createMockJob(jobId, {}, 'active');\r\n      analysisQueue.getJob.mockResolvedValue(mockJob);\r\n\r\n      const result = await client.retryJob(jobId, queueName);\r\n\r\n      expect(result).toBe(false);\r\n      expect(mockJob.retry).not.toHaveBeenCalled();\r\n    });\r\n  });\r\n\r\n  describe('getQueueStats', () => {\r\n    it('should get queue statistics successfully', async () => {\r\n      const queueName = 'ai-analysis';\r\n\r\n      analysisQueue.getWaiting.mockResolvedValue([{}, {}] as any); // 2 waiting\r\n      analysisQueue.getActive.mockResolvedValue([{}] as any); // 1 active\r\n      analysisQueue.getCompleted.mockResolvedValue([{}, {}, {}] as any); // 3 completed\r\n      analysisQueue.getFailed.mockResolvedValue([{}] as any); // 1 failed\r\n      analysisQueue.getDelayed.mockResolvedValue([] as any); // 0 delayed\r\n      analysisQueue.getPaused.mockResolvedValue([] as any); // 0 paused\r\n      analysisQueue.isPaused.mockResolvedValue(false);\r\n\r\n      const result = await client.getQueueStats(queueName);\r\n\r\n      expect(result).toMatchObject({\r\n        queueName,\r\n        counts: {\r\n          waiting: 2,\r\n          active: 1,\r\n          completed: 3,\r\n          failed: 1,\r\n          delayed: 0,\r\n          paused: 0,\r\n        },\r\n        isPaused: false,\r\n        timestamp: expect.any(Date),\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('getJobsByStatus', () => {\r\n    it('should get waiting jobs successfully', async () => {\r\n      const queueName = 'ai-analysis';\r\n      const status = 'waiting';\r\n\r\n      const mockJobs = [\r\n        createMockJob('job-1', { requestId: 'req-1' }, 'waiting'),\r\n        createMockJob('job-2', { requestId: 'req-2' }, 'waiting'),\r\n      ];\r\n\r\n      analysisQueue.getWaiting.mockResolvedValue(mockJobs);\r\n\r\n      const result = await client.getJobsByStatus(queueName, status, 0, 10);\r\n\r\n      expect(result).toHaveLength(2);\r\n      expect(result[0]).toMatchObject({\r\n        jobId: 'job-1',\r\n        queueName,\r\n        status: 'waiting',\r\n        data: { requestId: 'req-1' },\r\n        progress: 0,\r\n        attempts: 0,\r\n        maxAttempts: 3,\r\n        createdAt: expect.any(Date),\r\n      });\r\n    });\r\n\r\n    it('should handle invalid job status', async () => {\r\n      const queueName = 'ai-analysis';\r\n      const status = 'invalid' as any;\r\n\r\n      await expect(client.getJobsByStatus(queueName, status)).rejects.toThrow('Invalid job status');\r\n    });\r\n  });\r\n\r\n  describe('cleanQueue', () => {\r\n    it('should clean completed jobs successfully', async () => {\r\n      const queueName = 'ai-analysis';\r\n      const grace = 24 * 60 * 60 * 1000; // 24 hours\r\n\r\n      analysisQueue.clean.mockResolvedValue([{}, {}, {}] as any); // 3 cleaned jobs\r\n\r\n      const result = await client.cleanQueue(queueName, grace, 'completed');\r\n\r\n      expect(result).toBe(3);\r\n      expect(analysisQueue.clean).toHaveBeenCalledWith(grace, 'completed');\r\n    });\r\n  });\r\n\r\n  describe('pauseQueue', () => {\r\n    it('should pause queue successfully', async () => {\r\n      const queueName = 'ai-analysis';\r\n\r\n      await client.pauseQueue(queueName);\r\n\r\n      expect(analysisQueue.pause).toHaveBeenCalled();\r\n    });\r\n  });\r\n\r\n  describe('resumeQueue', () => {\r\n    it('should resume queue successfully', async () => {\r\n      const queueName = 'ai-analysis';\r\n\r\n      await client.resumeQueue(queueName);\r\n\r\n      expect(analysisQueue.resume).toHaveBeenCalled();\r\n    });\r\n  });\r\n\r\n  describe('scheduleJob', () => {\r\n    it('should schedule job successfully', async () => {\r\n      const queueName = 'ai-analysis';\r\n      const jobType = 'analyze-data';\r\n      const payload = { requestId: 'scheduled-req', data: { event: 'test' } };\r\n      const scheduleTime = new Date(Date.now() + 60000); // 1 minute from now\r\n\r\n      const mockJob = createMockJob('scheduled-job', payload);\r\n      analysisQueue.add.mockResolvedValue(mockJob);\r\n\r\n      const result = await client.scheduleJob(queueName, jobType, payload, scheduleTime);\r\n\r\n      expect(result).toMatchObject({\r\n        jobId: 'scheduled-job',\r\n        requestId: 'scheduled-req',\r\n        queueName,\r\n        status: 'delayed',\r\n        delay: expect.any(Number),\r\n        timestamp: expect.any(Date),\r\n      });\r\n\r\n      expect(analysisQueue.add).toHaveBeenCalledWith(\r\n        jobType,\r\n        payload,\r\n        expect.objectContaining({\r\n          delay: expect.any(Number),\r\n        })\r\n      );\r\n    });\r\n\r\n    it('should handle past schedule time', async () => {\r\n      const queueName = 'ai-analysis';\r\n      const jobType = 'analyze-data';\r\n      const payload = { requestId: 'past-req', data: { event: 'test' } };\r\n      const scheduleTime = new Date(Date.now() - 60000); // 1 minute ago\r\n\r\n      await expect(\r\n        client.scheduleJob(queueName, jobType, payload, scheduleTime)\r\n      ).rejects.toThrow('Schedule time must be in the future');\r\n    });\r\n  });\r\n\r\n  describe('queue name validation', () => {\r\n    it('should handle unknown queue name', async () => {\r\n      const unknownQueue = 'unknown-queue';\r\n\r\n      await expect(client.getQueueStats(unknownQueue)).rejects.toThrow('Unknown queue name');\r\n    });\r\n  });\r\n\r\n  describe('configuration', () => {\r\n    it('should use configured queue options', () => {\r\n      configService.get.mockImplementation((key: string, defaultValue?: any) => {\r\n        const config = {\r\n          'ai.queue.attempts': 5,\r\n          'ai.queue.backoffDelay': 3000,\r\n          'ai.queue.removeOnComplete': 200,\r\n          'ai.queue.removeOnFail': 100,\r\n        };\r\n        return config[key] || defaultValue;\r\n      });\r\n\r\n      // Create new instance to pick up new config\r\n      const newClient = new MessageQueueClient(\r\n        analysisQueue,\r\n        trainingQueue,\r\n        predictionQueue,\r\n        batchQueue,\r\n        configService\r\n      );\r\n\r\n      expect((newClient as any).defaultJobOptions).toMatchObject({\r\n        attempts: 5,\r\n        backoff: expect.objectContaining({\r\n          delay: 3000,\r\n        }),\r\n        removeOnComplete: 200,\r\n        removeOnFail: 100,\r\n      });\r\n    });\r\n  });\r\n\r\n  describe('error handling', () => {\r\n    it('should handle queue operation errors', async () => {\r\n      const jobId = 'error-job';\r\n      const queueName = 'ai-analysis';\r\n\r\n      analysisQueue.getJob.mockRejectedValue(new Error('Redis connection error'));\r\n\r\n      await expect(client.getJobStatus(jobId, queueName)).rejects.toThrow('Failed to get job status');\r\n    });\r\n  });\r\n\r\n  describe('ID generation', () => {\r\n    it('should generate unique request IDs', () => {\r\n      const id1 = (client as any).generateRequestId();\r\n      const id2 = (client as any).generateRequestId();\r\n\r\n      expect(id1).toBeDefined();\r\n      expect(id2).toBeDefined();\r\n      expect(id1).not.toBe(id2);\r\n      expect(id1).toMatch(/^mq-req-/);\r\n      expect(id2).toMatch(/^mq-req-/);\r\n    });\r\n  });\r\n});"], "version": 3}