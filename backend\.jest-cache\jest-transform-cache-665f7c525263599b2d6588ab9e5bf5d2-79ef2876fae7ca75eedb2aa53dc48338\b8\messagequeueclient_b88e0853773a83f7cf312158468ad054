6f2a6de47d53d380449fef02667a975f
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var MessageQueueClient_1;
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageQueueClient = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const bull_1 = require("@nestjs/bull");
const bull_2 = require("bull");
/**
 * Message Queue Client for AI Operations
 *
 * Handles asynchronous AI request processing through message queues.
 * Implements queue-based communication patterns, message persistence,
 * and delivery guarantees for reliable AI operations.
 */
let MessageQueueClient = MessageQueueClient_1 = class MessageQueueClient {
    constructor(analysisQueue, trainingQueue, predictionQueue, batchQueue, configService) {
        this.analysisQueue = analysisQueue;
        this.trainingQueue = trainingQueue;
        this.predictionQueue = predictionQueue;
        this.batchQueue = batchQueue;
        this.configService = configService;
        this.logger = new common_1.Logger(MessageQueueClient_1.name);
        this.defaultJobOptions = {
            attempts: this.configService.get('ai.queue.attempts', 3),
            backoff: {
                type: 'exponential',
                delay: this.configService.get('ai.queue.backoffDelay', 2000),
            },
            removeOnComplete: this.configService.get('ai.queue.removeOnComplete', 100),
            removeOnFail: this.configService.get('ai.queue.removeOnFail', 50),
        };
    }
    /**
     * Queues an analysis request for asynchronous processing
     */
    async queueAnalysisRequest(payload, options = {}) {
        this.logger.debug(`Queuing analysis request: ${payload.requestId}`);
        try {
            const jobOptions = this.buildJobOptions(options);
            const job = await this.analysisQueue.add('analyze-data', payload, jobOptions);
            this.logger.debug(`Analysis request queued: ${payload.requestId}, jobId: ${job.id}`);
            return {
                jobId: job.id.toString(),
                requestId: payload.requestId,
                queueName: 'ai-analysis',
                status: 'queued',
                priority: jobOptions.priority || 0,
                delay: jobOptions.delay || 0,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`Failed to queue analysis request: ${payload.requestId}`, error);
            throw new MessageQueueError(`Failed to queue analysis request: ${error.message}`, payload.requestId, error);
        }
    }
    /**
     * Queues a training request for asynchronous processing
     */
    async queueTrainingRequest(payload, options = {}) {
        this.logger.debug(`Queuing training request: ${payload.requestId}`);
        try {
            const jobOptions = this.buildJobOptions({
                ...options,
                // Training jobs typically have higher priority and longer timeout
                priority: options.priority || 10,
                timeout: options.timeout || 3600000, // 1 hour
            });
            const job = await this.trainingQueue.add('train-model', payload, jobOptions);
            this.logger.debug(`Training request queued: ${payload.requestId}, jobId: ${job.id}`);
            return {
                jobId: job.id.toString(),
                requestId: payload.requestId,
                queueName: 'ai-training',
                status: 'queued',
                priority: jobOptions.priority || 0,
                delay: jobOptions.delay || 0,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`Failed to queue training request: ${payload.requestId}`, error);
            throw new MessageQueueError(`Failed to queue training request: ${error.message}`, payload.requestId, error);
        }
    }
    /**
     * Queues a prediction request for asynchronous processing
     */
    async queuePredictionRequest(payload, options = {}) {
        this.logger.debug(`Queuing prediction request: ${payload.requestId}`);
        try {
            const jobOptions = this.buildJobOptions({
                ...options,
                // Prediction jobs typically have high priority and short timeout
                priority: options.priority || 5,
                timeout: options.timeout || 30000, // 30 seconds
            });
            const job = await this.predictionQueue.add('predict', payload, jobOptions);
            this.logger.debug(`Prediction request queued: ${payload.requestId}, jobId: ${job.id}`);
            return {
                jobId: job.id.toString(),
                requestId: payload.requestId,
                queueName: 'ai-prediction',
                status: 'queued',
                priority: jobOptions.priority || 0,
                delay: jobOptions.delay || 0,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`Failed to queue prediction request: ${payload.requestId}`, error);
            throw new MessageQueueError(`Failed to queue prediction request: ${error.message}`, payload.requestId, error);
        }
    }
    /**
     * Queues a batch processing request for asynchronous processing
     */
    async queueBatchRequest(payload, options = {}) {
        this.logger.debug(`Queuing batch request: ${payload.requestId}, items: ${payload.items.length}`);
        try {
            const jobOptions = this.buildJobOptions({
                ...options,
                // Batch jobs typically have lower priority but longer timeout
                priority: options.priority || 1,
                timeout: options.timeout || 1800000, // 30 minutes
            });
            const job = await this.batchQueue.add('process-batch', payload, jobOptions);
            this.logger.debug(`Batch request queued: ${payload.requestId}, jobId: ${job.id}`);
            return {
                jobId: job.id.toString(),
                requestId: payload.requestId,
                queueName: 'ai-batch',
                status: 'queued',
                priority: jobOptions.priority || 0,
                delay: jobOptions.delay || 0,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`Failed to queue batch request: ${payload.requestId}`, error);
            throw new MessageQueueError(`Failed to queue batch request: ${error.message}`, payload.requestId, error);
        }
    }
    /**
     * Gets the status of a queued job
     */
    async getJobStatus(jobId, queueName) {
        this.logger.debug(`Getting job status: ${jobId} from queue: ${queueName}`);
        try {
            const queue = this.getQueueByName(queueName);
            const job = await queue.getJob(jobId);
            if (!job) {
                return {
                    jobId,
                    queueName,
                    status: 'not_found',
                    timestamp: new Date(),
                };
            }
            const state = await job.getState();
            const progress = job.progress();
            const logs = await queue.getJobLogs(jobId);
            return {
                jobId,
                queueName,
                status: this.mapBullStateToStatus(state),
                progress: typeof progress === 'number' ? progress : 0,
                data: job.data,
                result: job.returnvalue,
                error: job.failedReason,
                attempts: job.attemptsMade,
                maxAttempts: job.opts.attempts || this.defaultJobOptions.attempts,
                createdAt: new Date(job.timestamp),
                processedAt: job.processedOn ? new Date(job.processedOn) : undefined,
                finishedAt: job.finishedOn ? new Date(job.finishedOn) : undefined,
                logs: logs.logs,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`Failed to get job status: ${jobId}`, error);
            throw new MessageQueueError(`Failed to get job status: ${error.message}`, jobId, error);
        }
    }
    /**
     * Cancels a queued job
     */
    async cancelJob(jobId, queueName) {
        this.logger.debug(`Cancelling job: ${jobId} from queue: ${queueName}`);
        try {
            const queue = this.getQueueByName(queueName);
            const job = await queue.getJob(jobId);
            if (!job) {
                this.logger.warn(`Job not found for cancellation: ${jobId}`);
                return false;
            }
            const state = await job.getState();
            if (state === 'completed' || state === 'failed') {
                this.logger.warn(`Cannot cancel job in state: ${state}, jobId: ${jobId}`);
                return false;
            }
            await job.remove();
            this.logger.debug(`Job cancelled successfully: ${jobId}`);
            return true;
        }
        catch (error) {
            this.logger.error(`Failed to cancel job: ${jobId}`, error);
            throw new MessageQueueError(`Failed to cancel job: ${error.message}`, jobId, error);
        }
    }
    /**
     * Retries a failed job
     */
    async retryJob(jobId, queueName) {
        this.logger.debug(`Retrying job: ${jobId} from queue: ${queueName}`);
        try {
            const queue = this.getQueueByName(queueName);
            const job = await queue.getJob(jobId);
            if (!job) {
                this.logger.warn(`Job not found for retry: ${jobId}`);
                return false;
            }
            const state = await job.getState();
            if (state !== 'failed') {
                this.logger.warn(`Cannot retry job in state: ${state}, jobId: ${jobId}`);
                return false;
            }
            await job.retry();
            this.logger.debug(`Job retried successfully: ${jobId}`);
            return true;
        }
        catch (error) {
            this.logger.error(`Failed to retry job: ${jobId}`, error);
            throw new MessageQueueError(`Failed to retry job: ${error.message}`, jobId, error);
        }
    }
    /**
     * Gets queue statistics
     */
    async getQueueStats(queueName) {
        this.logger.debug(`Getting queue statistics: ${queueName}`);
        try {
            const queue = this.getQueueByName(queueName);
            const [waiting, active, completed, failed, delayed, paused] = await Promise.all([
                queue.getWaiting(),
                queue.getActive(),
                queue.getCompleted(),
                queue.getFailed(),
                queue.getDelayed(),
                queue.getPaused(),
            ]);
            return {
                queueName,
                counts: {
                    waiting: waiting.length,
                    active: active.length,
                    completed: completed.length,
                    failed: failed.length,
                    delayed: delayed.length,
                    paused: paused.length,
                },
                isPaused: await queue.isPaused(),
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`Failed to get queue statistics: ${queueName}`, error);
            throw new MessageQueueError(`Failed to get queue statistics: ${error.message}`, queueName, error);
        }
    }
    /**
     * Gets jobs by status from a queue
     */
    async getJobsByStatus(queueName, status, start = 0, end = 10) {
        this.logger.debug(`Getting jobs by status: ${status} from queue: ${queueName}`);
        try {
            const queue = this.getQueueByName(queueName);
            let jobs = [];
            switch (status) {
                case 'waiting':
                    jobs = await queue.getWaiting(start, end);
                    break;
                case 'active':
                    jobs = await queue.getActive(start, end);
                    break;
                case 'completed':
                    jobs = await queue.getCompleted(start, end);
                    break;
                case 'failed':
                    jobs = await queue.getFailed(start, end);
                    break;
                case 'delayed':
                    jobs = await queue.getDelayed(start, end);
                    break;
                case 'paused':
                    jobs = await queue.getPaused(start, end);
                    break;
                default:
                    throw new Error(`Invalid job status: ${status}`);
            }
            return Promise.all(jobs.map(async (job) => ({
                jobId: job.id.toString(),
                queueName,
                status: this.mapBullStateToStatus(await job.getState()),
                data: job.data,
                result: job.returnvalue,
                error: job.failedReason,
                progress: typeof job.progress() === 'number' ? job.progress() : 0,
                attempts: job.attemptsMade,
                maxAttempts: job.opts.attempts || this.defaultJobOptions.attempts,
                createdAt: new Date(job.timestamp),
                processedAt: job.processedOn ? new Date(job.processedOn) : undefined,
                finishedAt: job.finishedOn ? new Date(job.finishedOn) : undefined,
            })));
        }
        catch (error) {
            this.logger.error(`Failed to get jobs by status: ${status} from queue: ${queueName}`, error);
            throw new MessageQueueError(`Failed to get jobs by status: ${error.message}`, queueName, error);
        }
    }
    /**
     * Cleans up old jobs from a queue
     */
    async cleanQueue(queueName, grace = 24 * 60 * 60 * 1000, // 24 hours
    status = 'completed') {
        this.logger.debug(`Cleaning queue: ${queueName}, grace: ${grace}ms, status: ${status}`);
        try {
            const queue = this.getQueueByName(queueName);
            const cleanedJobs = await queue.clean(grace, status);
            this.logger.debug(`Cleaned ${cleanedJobs.length} jobs from queue: ${queueName}`);
            return cleanedJobs.length;
        }
        catch (error) {
            this.logger.error(`Failed to clean queue: ${queueName}`, error);
            throw new MessageQueueError(`Failed to clean queue: ${error.message}`, queueName, error);
        }
    }
    /**
     * Pauses a queue
     */
    async pauseQueue(queueName) {
        this.logger.debug(`Pausing queue: ${queueName}`);
        try {
            const queue = this.getQueueByName(queueName);
            await queue.pause();
            this.logger.debug(`Queue paused: ${queueName}`);
        }
        catch (error) {
            this.logger.error(`Failed to pause queue: ${queueName}`, error);
            throw new MessageQueueError(`Failed to pause queue: ${error.message}`, queueName, error);
        }
    }
    /**
     * Resumes a paused queue
     */
    async resumeQueue(queueName) {
        this.logger.debug(`Resuming queue: ${queueName}`);
        try {
            const queue = this.getQueueByName(queueName);
            await queue.resume();
            this.logger.debug(`Queue resumed: ${queueName}`);
        }
        catch (error) {
            this.logger.error(`Failed to resume queue: ${queueName}`, error);
            throw new MessageQueueError(`Failed to resume queue: ${error.message}`, queueName, error);
        }
    }
    /**
     * Adds a job with scheduled execution
     */
    async scheduleJob(queueName, jobType, payload, scheduleTime, options = {}) {
        this.logger.debug(`Scheduling job: ${jobType} in queue: ${queueName} for: ${scheduleTime}`);
        try {
            const queue = this.getQueueByName(queueName);
            const delay = scheduleTime.getTime() - Date.now();
            if (delay < 0) {
                throw new Error('Schedule time must be in the future');
            }
            const jobOptions = this.buildJobOptions({
                ...options,
                delay,
            });
            const job = await queue.add(jobType, payload, jobOptions);
            this.logger.debug(`Job scheduled: ${jobType}, jobId: ${job.id}, delay: ${delay}ms`);
            return {
                jobId: job.id.toString(),
                requestId: payload.requestId || this.generateRequestId(),
                queueName,
                status: 'delayed',
                priority: jobOptions.priority || 0,
                delay: jobOptions.delay || 0,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`Failed to schedule job: ${jobType} in queue: ${queueName}`, error);
            throw new MessageQueueError(`Failed to schedule job: ${error.message}`, queueName, error);
        }
    }
    // Private helper methods
    getQueueByName(queueName) {
        switch (queueName) {
            case 'ai-analysis':
                return this.analysisQueue;
            case 'ai-training':
                return this.trainingQueue;
            case 'ai-prediction':
                return this.predictionQueue;
            case 'ai-batch':
                return this.batchQueue;
            default:
                throw new Error(`Unknown queue name: ${queueName}`);
        }
    }
    buildJobOptions(options) {
        return {
            ...this.defaultJobOptions,
            priority: options.priority ?? this.defaultJobOptions.priority,
            delay: options.delay ?? this.defaultJobOptions.delay,
            timeout: options.timeout ?? this.defaultJobOptions.timeout,
            attempts: options.attempts ?? this.defaultJobOptions.attempts,
            backoff: options.backoff ? {
                type: options.backoff.type || 'exponential',
                delay: options.backoff.delay || 2000,
            } : this.defaultJobOptions.backoff,
            removeOnComplete: options.removeOnComplete ?? this.defaultJobOptions.removeOnComplete,
            removeOnFail: options.removeOnFail ?? this.defaultJobOptions.removeOnFail,
            jobId: options.jobId,
        };
    }
    mapBullStateToStatus(state) {
        switch (state) {
            case 'waiting':
                return 'waiting';
            case 'active':
                return 'active';
            case 'completed':
                return 'completed';
            case 'failed':
                return 'failed';
            case 'delayed':
                return 'delayed';
            case 'paused':
                return 'paused';
            default:
                return 'unknown';
        }
    }
    generateRequestId() {
        return `mq-req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
};
exports.MessageQueueClient = MessageQueueClient;
exports.MessageQueueClient = MessageQueueClient = MessageQueueClient_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, bull_1.InjectQueue)('ai-analysis')),
    __param(1, (0, bull_1.InjectQueue)('ai-training')),
    __param(2, (0, bull_1.InjectQueue)('ai-prediction')),
    __param(3, (0, bull_1.InjectQueue)('ai-batch')),
    __metadata("design:paramtypes", [typeof (_a = typeof bull_2.Queue !== "undefined" && bull_2.Queue) === "function" ? _a : Object, typeof (_b = typeof bull_2.Queue !== "undefined" && bull_2.Queue) === "function" ? _b : Object, typeof (_c = typeof bull_2.Queue !== "undefined" && bull_2.Queue) === "function" ? _c : Object, typeof (_d = typeof bull_2.Queue !== "undefined" && bull_2.Queue) === "function" ? _d : Object, typeof (_e = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _e : Object])
], MessageQueueClient);
class MessageQueueError extends Error {
    constructor(message, identifier, originalError) {
        super(message);
        this.identifier = identifier;
        this.originalError = originalError;
        this.name = 'MessageQueueError';
    }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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