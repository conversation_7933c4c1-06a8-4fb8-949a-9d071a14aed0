6ab39cf93f2b5f8c97bddaa61d767107
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalysisResult = exports.ErrorCategory = exports.AnalysisStatus = exports.AnalysisType = void 0;
const base_aggregate_root_1 = require("../../../../shared-kernel/domain/base-aggregate-root");
const analysis_result_created_domain_event_1 = require("../events/analysis-result-created.domain-event");
const analysis_result_status_changed_domain_event_1 = require("../events/analysis-result-status-changed.domain-event");
const analysis_result_updated_domain_event_1 = require("../events/analysis-result-updated.domain-event");
var AnalysisType;
(function (AnalysisType) {
    AnalysisType["THREAT_DETECTION"] = "threat_detection";
    AnalysisType["VULNERABILITY_ASSESSMENT"] = "vulnerability_assessment";
    AnalysisType["ANOMALY_DETECTION"] = "anomaly_detection";
    AnalysisType["PATTERN_RECOGNITION"] = "pattern_recognition";
    AnalysisType["CLASSIFICATION"] = "classification";
    AnalysisType["REGRESSION"] = "regression";
    AnalysisType["CLUSTERING"] = "clustering";
    AnalysisType["NLP_ANALYSIS"] = "nlp_analysis";
    AnalysisType["RELATIONSHIP_INFERENCE"] = "relationship_inference";
    AnalysisType["PREDICTIVE_ANALYSIS"] = "predictive_analysis";
    AnalysisType["RISK_SCORING"] = "risk_scoring";
    AnalysisType["BEHAVIORAL_ANALYSIS"] = "behavioral_analysis";
})(AnalysisType || (exports.AnalysisType = AnalysisType = {}));
var AnalysisStatus;
(function (AnalysisStatus) {
    AnalysisStatus["PENDING"] = "pending";
    AnalysisStatus["PROCESSING"] = "processing";
    AnalysisStatus["COMPLETED"] = "completed";
    AnalysisStatus["FAILED"] = "failed";
    AnalysisStatus["CANCELLED"] = "cancelled";
    AnalysisStatus["TIMEOUT"] = "timeout";
    AnalysisStatus["PARTIAL"] = "partial";
})(AnalysisStatus || (exports.AnalysisStatus = AnalysisStatus = {}));
var ErrorCategory;
(function (ErrorCategory) {
    ErrorCategory["INPUT_VALIDATION"] = "input_validation";
    ErrorCategory["MODEL_ERROR"] = "model_error";
    ErrorCategory["PROCESSING_ERROR"] = "processing_error";
    ErrorCategory["TIMEOUT_ERROR"] = "timeout_error";
    ErrorCategory["RESOURCE_ERROR"] = "resource_error";
    ErrorCategory["NETWORK_ERROR"] = "network_error";
    ErrorCategory["SYSTEM_ERROR"] = "system_error";
})(ErrorCategory || (exports.ErrorCategory = ErrorCategory = {}));
class AnalysisResult extends base_aggregate_root_1.BaseAggregateRoot {
    constructor(props, id) {
        super(props, id);
        this.validateInvariants();
    }
    static create(props, id) {
        const now = new Date();
        const analysisResult = new AnalysisResult({
            ...props,
            childAnalysisIds: [],
            createdAt: now,
            updatedAt: now
        }, id);
        analysisResult.addDomainEvent(new analysis_result_created_domain_event_1.AnalysisResultCreatedEvent(analysisResult.id, props.requestId, props.modelId, props.analysisType));
        return analysisResult;
    }
    static reconstitute(props, id) {
        return new AnalysisResult(props, id);
    }
    // Getters
    get requestId() {
        return this.props.requestId;
    }
    get modelId() {
        return this.props.modelId;
    }
    get analysisType() {
        return this.props.analysisType;
    }
    get inputData() {
        return { ...this.props.inputData };
    }
    get outputData() {
        return { ...this.props.outputData };
    }
    get confidence() {
        return this.props.confidence;
    }
    get processingTime() {
        return this.props.processingTime;
    }
    get status() {
        return this.props.status;
    }
    get metadata() {
        return { ...this.props.metadata };
    }
    get tags() {
        return [...this.props.tags];
    }
    get correlationId() {
        return this.props.correlationId;
    }
    get parentAnalysisId() {
        return this.props.parentAnalysisId;
    }
    get childAnalysisIds() {
        return [...this.props.childAnalysisIds];
    }
    get errorDetails() {
        return this.props.errorDetails ? { ...this.props.errorDetails } : undefined;
    }
    get createdAt() {
        return this.props.createdAt;
    }
    get updatedAt() {
        return this.props.updatedAt;
    }
    get completedAt() {
        return this.props.completedAt;
    }
    // Domain methods
    /**
     * Marks the analysis as processing
     */
    startProcessing() {
        if (this.props.status !== AnalysisStatus.PENDING) {
            throw new Error(`Cannot start processing analysis in status: ${this.props.status}`);
        }
        const previousStatus = this.props.status;
        this.props.status = AnalysisStatus.PROCESSING;
        this.props.updatedAt = new Date();
        this.addDomainEvent(new analysis_result_status_changed_domain_event_1.AnalysisResultStatusChangedEvent(this.id, this.props.requestId, previousStatus, AnalysisStatus.PROCESSING));
    }
    /**
     * Completes the analysis with results
     */
    complete(outputData, processingTime, confidence) {
        if (this.props.status !== AnalysisStatus.PROCESSING) {
            throw new Error(`Cannot complete analysis in status: ${this.props.status}`);
        }
        if (confidence < 0 || confidence > 1) {
            throw new Error('Confidence must be between 0 and 1');
        }
        if (processingTime < 0) {
            throw new Error('Processing time cannot be negative');
        }
        const previousStatus = this.props.status;
        this.props.status = AnalysisStatus.COMPLETED;
        this.props.outputData = outputData;
        this.props.processingTime = processingTime;
        this.props.confidence = confidence;
        this.props.completedAt = new Date();
        this.props.updatedAt = new Date();
        this.addDomainEvent(new analysis_result_status_changed_domain_event_1.AnalysisResultStatusChangedEvent(this.id, this.props.requestId, previousStatus, AnalysisStatus.COMPLETED));
    }
    /**
     * Marks the analysis as failed with error details
     */
    fail(errorDetails) {
        if (this.props.status === AnalysisStatus.COMPLETED) {
            throw new Error('Cannot fail a completed analysis');
        }
        const previousStatus = this.props.status;
        this.props.status = AnalysisStatus.FAILED;
        this.props.errorDetails = errorDetails;
        this.props.completedAt = new Date();
        this.props.updatedAt = new Date();
        this.addDomainEvent(new analysis_result_status_changed_domain_event_1.AnalysisResultStatusChangedEvent(this.id, this.props.requestId, previousStatus, AnalysisStatus.FAILED));
    }
    /**
     * Cancels the analysis
     */
    cancel() {
        if (this.props.status === AnalysisStatus.COMPLETED || this.props.status === AnalysisStatus.FAILED) {
            throw new Error(`Cannot cancel analysis in status: ${this.props.status}`);
        }
        const previousStatus = this.props.status;
        this.props.status = AnalysisStatus.CANCELLED;
        this.props.completedAt = new Date();
        this.props.updatedAt = new Date();
        this.addDomainEvent(new analysis_result_status_changed_domain_event_1.AnalysisResultStatusChangedEvent(this.id, this.props.requestId, previousStatus, AnalysisStatus.CANCELLED));
    }
    /**
     * Updates the analysis metadata
     */
    updateMetadata(metadata) {
        this.props.metadata = {
            ...this.props.metadata,
            ...metadata,
        };
        this.props.updatedAt = new Date();
        this.addDomainEvent(new analysis_result_updated_domain_event_1.AnalysisResultUpdatedEvent(this.id, this.props.requestId, 'metadata'));
    }
    /**
     * Adds a tag to the analysis
     */
    addTag(tag) {
        if (!tag || tag.trim().length === 0) {
            throw new Error('Tag cannot be empty');
        }
        const normalizedTag = tag.trim().toLowerCase();
        if (!this.props.tags.includes(normalizedTag)) {
            this.props.tags.push(normalizedTag);
            this.props.updatedAt = new Date();
            this.addDomainEvent(new analysis_result_updated_domain_event_1.AnalysisResultUpdatedEvent(this.id, this.props.requestId, 'tags'));
        }
    }
    /**
     * Removes a tag from the analysis
     */
    removeTag(tag) {
        const normalizedTag = tag.trim().toLowerCase();
        const index = this.props.tags.indexOf(normalizedTag);
        if (index !== -1) {
            this.props.tags.splice(index, 1);
            this.props.updatedAt = new Date();
            this.addDomainEvent(new analysis_result_updated_domain_event_1.AnalysisResultUpdatedEvent(this.id, this.props.requestId, 'tags'));
        }
    }
    /**
     * Adds a child analysis ID
     */
    addChildAnalysis(childId) {
        if (!this.props.childAnalysisIds.some(id => id.equals(childId))) {
            this.props.childAnalysisIds.push(childId);
            this.props.updatedAt = new Date();
            this.addDomainEvent(new analysis_result_updated_domain_event_1.AnalysisResultUpdatedEvent(this.id, this.props.requestId, 'children'));
        }
    }
    /**
     * Removes a child analysis ID
     */
    removeChildAnalysis(childId) {
        const index = this.props.childAnalysisIds.findIndex(id => id.equals(childId));
        if (index !== -1) {
            this.props.childAnalysisIds.splice(index, 1);
            this.props.updatedAt = new Date();
            this.addDomainEvent(new analysis_result_updated_domain_event_1.AnalysisResultUpdatedEvent(this.id, this.props.requestId, 'children'));
        }
    }
    /**
     * Checks if the analysis is in a terminal state
     */
    isTerminal() {
        return [
            AnalysisStatus.COMPLETED,
            AnalysisStatus.FAILED,
            AnalysisStatus.CANCELLED,
            AnalysisStatus.TIMEOUT
        ].includes(this.props.status);
    }
    /**
     * Checks if the analysis was successful
     */
    isSuccessful() {
        return this.props.status === AnalysisStatus.COMPLETED;
    }
    /**
     * Checks if the analysis has high confidence
     */
    hasHighConfidence(threshold = 0.8) {
        return this.props.confidence >= threshold;
    }
    /**
     * Gets the analysis duration in milliseconds
     */
    getDuration() {
        if (!this.props.completedAt) {
            return undefined;
        }
        return this.props.completedAt.getTime() - this.props.createdAt.getTime();
    }
    /**
     * Checks if the analysis has a specific tag
     */
    hasTag(tag) {
        const normalizedTag = tag.trim().toLowerCase();
        return this.props.tags.includes(normalizedTag);
    }
    /**
     * Gets the quality score based on various metrics
     */
    getQualityScore() {
        const { qualityMetrics } = this.props.metadata;
        if (!qualityMetrics) {
            return 0;
        }
        const weights = {
            dataQuality: 0.3,
            resultReliability: 0.3,
            consistencyScore: 0.2,
            completenessScore: 0.2
        };
        return (qualityMetrics.dataQuality * weights.dataQuality +
            qualityMetrics.resultReliability * weights.resultReliability +
            qualityMetrics.consistencyScore * weights.consistencyScore +
            qualityMetrics.completenessScore * weights.completenessScore);
    }
    /**
     * Checks if the error is retryable
     */
    isRetryable() {
        return this.props.errorDetails?.retryable ?? false;
    }
    /**
     * Gets a summary of the analysis result
     */
    getSummary() {
        return {
            id: this.id.toString(),
            requestId: this.props.requestId,
            analysisType: this.props.analysisType,
            status: this.props.status,
            confidence: this.props.confidence,
            processingTime: this.props.processingTime,
            qualityScore: this.getQualityScore(),
            isSuccessful: this.isSuccessful(),
            duration: this.getDuration(),
        };
    }
    validateInvariants() {
        super.validateInvariants();
        if (!this.props.requestId || this.props.requestId.trim().length === 0) {
            throw new Error('Request ID is required');
        }
        if (!this.props.modelId) {
            throw new Error('Model ID is required');
        }
        if (!Object.values(AnalysisType).includes(this.props.analysisType)) {
            throw new Error('Invalid analysis type');
        }
        if (!Object.values(AnalysisStatus).includes(this.props.status)) {
            throw new Error('Invalid analysis status');
        }
        if (this.props.confidence < 0 || this.props.confidence > 1) {
            throw new Error('Confidence must be between 0 and 1');
        }
        if (this.props.processingTime < 0) {
            throw new Error('Processing time cannot be negative');
        }
        if (!this.props.inputData) {
            throw new Error('Input data is required');
        }
        if (!this.props.metadata) {
            throw new Error('Metadata is required');
        }
        if (!Array.isArray(this.props.tags)) {
            throw new Error('Tags must be an array');
        }
        if (!Array.isArray(this.props.childAnalysisIds)) {
            throw new Error('Child analysis IDs must be an array');
        }
        if (this.props.status === AnalysisStatus.COMPLETED && !this.props.outputData) {
            throw new Error('Output data is required for completed analysis');
        }
        if (this.props.status === AnalysisStatus.FAILED && !this.props.errorDetails) {
            throw new Error('Error details are required for failed analysis');
        }
    }
}
exports.AnalysisResult = AnalysisResult;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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