abd29b9c3dd42a31df2bb4b95c2e480a
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const cqrs_1 = require("@nestjs/cqrs");
const bull_1 = require("@nestjs/bull");
const pipeline_manager_service_1 = require("../pipeline-manager.service");
describe('PipelineManagerService', () => {
    let service;
    let mockAiRequestQueue;
    let mockAiResponseQueue;
    let mockTrainingQueue;
    let mockEventBus;
    let mockConfigService;
    const mockPipelineDefinition = {
        name: 'Test Pipeline',
        description: 'Test pipeline for unit tests',
        stages: [
            {
                id: 'stage-1',
                name: 'Data Preprocessing',
                type: 'data-preprocessing',
                config: { normalize: true },
            },
            {
                id: 'stage-2',
                name: 'AI Analysis',
                type: 'ai-analysis',
                dependencies: ['stage-1'],
                config: { model: 'test-model' },
            },
        ],
        executionStrategy: 'sequential',
        context: { testData: 'test' },
    };
    beforeEach(async () => {
        // Create mocks
        mockAiRequestQueue = {
            add: jest.fn(),
            process: jest.fn(),
        };
        mockAiResponseQueue = {
            add: jest.fn(),
            process: jest.fn(),
        };
        mockTrainingQueue = {
            add: jest.fn(),
            process: jest.fn(),
        };
        mockEventBus = {
            publish: jest.fn(),
        };
        mockConfigService = {
            get: jest.fn(),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                pipeline_manager_service_1.PipelineManagerService,
                {
                    provide: (0, bull_1.getQueueToken)('ai-request'),
                    useValue: mockAiRequestQueue,
                },
                {
                    provide: (0, bull_1.getQueueToken)('ai-response'),
                    useValue: mockAiResponseQueue,
                },
                {
                    provide: (0, bull_1.getQueueToken)('training-job'),
                    useValue: mockTrainingQueue,
                },
                {
                    provide: cqrs_1.EventBus,
                    useValue: mockEventBus,
                },
                {
                    provide: config_1.ConfigService,
                    useValue: mockConfigService,
                },
            ],
        }).compile();
        service = module.get(pipeline_manager_service_1.PipelineManagerService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('createPipeline', () => {
        it('should create and execute pipeline successfully', async () => {
            // Arrange
            const mockJob = {
                finished: jest.fn().mockResolvedValue({ result: 'success' }),
            };
            mockAiRequestQueue.add.mockResolvedValue(mockJob);
            // Act
            const execution = await service.createPipeline(mockPipelineDefinition);
            // Assert
            expect(execution).toBeDefined();
            expect(execution.id).toMatch(/^pipeline-\d+-[a-z0-9]+$/);
            expect(execution.definition).toEqual(mockPipelineDefinition);
            expect(execution.stages).toHaveLength(2);
            expect(execution.status).toBe('completed');
        });
        it('should validate pipeline definition', async () => {
            // Arrange
            const invalidDefinition = {
                ...mockPipelineDefinition,
                stages: [], // Empty stages
            };
            // Act & Assert
            await expect(service.createPipeline(invalidDefinition))
                .rejects.toThrow('Pipeline must have at least one stage');
        });
        it('should validate stage dependencies', async () => {
            // Arrange
            const invalidDefinition = {
                ...mockPipelineDefinition,
                stages: [
                    {
                        id: 'stage-1',
                        name: 'Test Stage',
                        type: 'ai-analysis',
                        dependencies: ['non-existent-stage'], // Invalid dependency
                    },
                ],
            };
            // Act & Assert
            await expect(service.createPipeline(invalidDefinition))
                .rejects.toThrow('Invalid dependency: non-existent-stage for stage: stage-1');
        });
        it('should handle pipeline execution failures', async () => {
            // Arrange
            const mockJob = {
                finished: jest.fn().mockRejectedValue(new Error('Job failed')),
            };
            mockAiRequestQueue.add.mockResolvedValue(mockJob);
            // Act & Assert
            await expect(service.createPipeline(mockPipelineDefinition))
                .rejects.toThrow('Pipeline creation failed');
        });
    });
    describe('executePipelineFromTemplate', () => {
        beforeEach(() => {
            // Register a test template
            service.registerPipelineTemplate('test-template', {
                name: 'Test Template',
                description: 'Template for testing',
                stages: [
                    {
                        id: 'template-stage',
                        name: 'Template Stage',
                        type: 'ai-analysis',
                        config: { model: 'template-model' },
                    },
                ],
                executionStrategy: 'sequential',
            });
        });
        it('should execute pipeline from template successfully', async () => {
            // Arrange
            const parameters = { testParam: 'value' };
            const mockJob = {
                finished: jest.fn().mockResolvedValue({ result: 'success' }),
            };
            mockAiRequestQueue.add.mockResolvedValue(mockJob);
            // Act
            const execution = await service.executePipelineFromTemplate('test-template', parameters);
            // Assert
            expect(execution).toBeDefined();
            expect(execution.definition.name).toBe('Test Template');
            expect(execution.context).toEqual(parameters);
        });
        it('should throw error for non-existent template', async () => {
            // Act & Assert
            await expect(service.executePipelineFromTemplate('non-existent', {}))
                .rejects.toThrow('Pipeline template not found: non-existent');
        });
    });
    describe('getPipelineStatus', () => {
        it('should return pipeline status correctly', async () => {
            // Arrange
            const mockJob = {
                finished: jest.fn().mockResolvedValue({ result: 'success' }),
            };
            mockAiRequestQueue.add.mockResolvedValue(mockJob);
            const execution = await service.createPipeline(mockPipelineDefinition);
            // Act
            const status = await service.getPipelineStatus(execution.id);
            // Assert
            expect(status).toEqual({
                id: execution.id,
                status: 'completed',
                progress: 1, // 100% complete
                currentStage: null, // No stage currently running
                metrics: expect.objectContaining({
                    startTime: expect.any(Date),
                    endTime: expect.any(Date),
                    totalStages: 2,
                    completedStages: 2,
                    failedStages: 0,
                }),
                results: expect.any(Object),
                errors: [],
            });
        });
        it('should throw error for non-existent pipeline', async () => {
            // Act & Assert
            await expect(service.getPipelineStatus('non-existent'))
                .rejects.toThrow('Pipeline not found: non-existent');
        });
    });
    describe('cancelPipeline', () => {
        it('should cancel running pipeline successfully', async () => {
            // Arrange
            const mockJob = {
                finished: jest.fn().mockResolvedValue({ result: 'success' }),
            };
            mockAiRequestQueue.add.mockResolvedValue(mockJob);
            // Act
            const execution = await service.createPipeline(mockPipelineDefinition);
            await service.cancelPipeline(execution.id);
            // Assert
            const status = await service.getPipelineStatus(execution.id);
            expect(status.status).toBe('cancelled');
        });
        it('should throw error for non-existent pipeline', async () => {
            // Act & Assert
            await expect(service.cancelPipeline('non-existent'))
                .rejects.toThrow('Pipeline not found: non-existent');
        });
    });
    describe('retryStage', () => {
        it('should retry failed stage successfully', async () => {
            // Arrange
            const failingJob = {
                finished: jest.fn().mockRejectedValue(new Error('Stage failed')),
            };
            const successJob = {
                finished: jest.fn().mockResolvedValue({ result: 'success' }),
            };
            // Create a pipeline definition with continueOnFailure to allow pipeline to complete even with failed stages
            const resilientDefinition = {
                ...mockPipelineDefinition,
                stages: [
                    {
                        id: 'stage-1',
                        name: 'Data Preprocessing',
                        type: 'data-preprocessing',
                        config: { normalize: true },
                    },
                    {
                        id: 'stage-2',
                        name: 'AI Analysis',
                        type: 'ai-analysis',
                        dependencies: ['stage-1'],
                        continueOnFailure: true, // Allow pipeline to continue even if this stage fails
                        config: { model: 'test-model' },
                    },
                ],
            };
            mockAiRequestQueue.add
                .mockResolvedValueOnce(successJob) // stage-1 succeeds
                .mockResolvedValueOnce(failingJob) // stage-2 fails initially
                .mockResolvedValueOnce(successJob); // stage-2 succeeds on retry
            // Create pipeline that will have a failed stage
            const execution = await service.createPipeline(resilientDefinition);
            // Act
            await service.retryStage(execution.id, 'stage-2');
            // Assert
            const status = await service.getPipelineStatus(execution.id);
            expect(status.status).toBe('completed');
        });
        it('should throw error for non-failed stage', async () => {
            // Arrange
            const mockJob = {
                finished: jest.fn().mockResolvedValue({ result: 'success' }),
            };
            mockAiRequestQueue.add.mockResolvedValue(mockJob);
            const execution = await service.createPipeline(mockPipelineDefinition);
            // Act & Assert
            await expect(service.retryStage(execution.id, 'stage-1'))
                .rejects.toThrow('Stage is not in failed state: stage-1');
        });
    });
    describe('getActivePipelines', () => {
        it('should return all active pipelines', async () => {
            // Arrange
            const mockJob = {
                finished: jest.fn().mockResolvedValue({ result: 'success' }),
            };
            mockAiRequestQueue.add.mockResolvedValue(mockJob);
            const execution1 = await service.createPipeline(mockPipelineDefinition);
            const execution2 = await service.createPipeline({
                ...mockPipelineDefinition,
                name: 'Second Pipeline',
            });
            // Act
            const activePipelines = await service.getActivePipelines();
            // Assert
            expect(activePipelines).toHaveLength(2);
            expect(activePipelines.map(p => p.id)).toContain(execution1.id);
            expect(activePipelines.map(p => p.id)).toContain(execution2.id);
        });
    });
    describe('stage execution', () => {
        it('should execute AI analysis stage', async () => {
            // Arrange
            const mockJob = {
                finished: jest.fn().mockResolvedValue({
                    analysis: 'threat detected',
                    confidence: 0.95
                }),
            };
            mockAiRequestQueue.add.mockResolvedValue(mockJob);
            const aiAnalysisDefinition = {
                ...mockPipelineDefinition,
                stages: [
                    {
                        id: 'ai-stage',
                        name: 'AI Analysis',
                        type: 'ai-analysis',
                        config: { model: 'threat-detection' },
                    },
                ],
            };
            // Act
            const execution = await service.createPipeline(aiAnalysisDefinition);
            // Assert
            expect(mockAiRequestQueue.add).toHaveBeenCalledWith('analyze', {
                pipelineId: execution.id,
                stageId: 'ai-stage',
                data: expect.any(Object),
                config: { model: 'threat-detection' },
            });
            expect(execution.results['ai-stage']).toEqual({
                analysis: 'threat detected',
                confidence: 0.95,
            });
        });
        it('should execute model training stage', async () => {
            // Arrange
            const mockJob = {
                finished: jest.fn().mockResolvedValue({
                    modelId: 'trained-model-123',
                    metrics: { accuracy: 0.95 }
                }),
            };
            mockTrainingQueue.add.mockResolvedValue(mockJob);
            const trainingDefinition = {
                ...mockPipelineDefinition,
                stages: [
                    {
                        id: 'training-stage',
                        name: 'Model Training',
                        type: 'model-training',
                        config: { epochs: 10 },
                    },
                ],
            };
            // Act
            const execution = await service.createPipeline(trainingDefinition);
            // Assert
            expect(mockTrainingQueue.add).toHaveBeenCalledWith('train', {
                pipelineId: execution.id,
                stageId: 'training-stage',
                data: expect.any(Object),
                config: { epochs: 10 },
            });
            expect(execution.results['training-stage']).toEqual({
                modelId: 'trained-model-123',
                metrics: { accuracy: 0.95 },
            });
        });
        it('should execute data preprocessing stage', async () => {
            // Arrange
            const preprocessingDefinition = {
                ...mockPipelineDefinition,
                stages: [
                    {
                        id: 'preprocess-stage',
                        name: 'Data Preprocessing',
                        type: 'data-preprocessing',
                        config: { normalize: true, validate: true },
                    },
                ],
            };
            // Act
            const execution = await service.createPipeline(preprocessingDefinition);
            // Assert
            expect(execution.results['preprocess-stage']).toEqual({
                processedData: expect.any(Object),
                metadata: { stage: 'preprocess-stage' },
            });
        });
        it('should execute conditional stage', async () => {
            // Arrange
            const conditionalDefinition = {
                ...mockPipelineDefinition,
                stages: [
                    {
                        id: 'conditional-stage',
                        name: 'Conditional Logic',
                        type: 'conditional',
                        config: { condition: 'always_true' },
                    },
                ],
            };
            // Act
            const execution = await service.createPipeline(conditionalDefinition);
            // Assert
            expect(execution.results['conditional-stage']).toEqual({
                conditionMet: true,
                result: 'proceed',
            });
        });
        it('should execute parallel batch stage', async () => {
            // Arrange
            const batchDefinition = {
                ...mockPipelineDefinition,
                stages: [
                    {
                        id: 'batch-stage',
                        name: 'Parallel Batch',
                        type: 'parallel-batch',
                        config: { batchSize: 5 },
                        input: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], // 10 items
                    },
                ],
            };
            // Act
            const execution = await service.createPipeline(batchDefinition);
            // Assert
            expect(execution.results['batch-stage']).toHaveLength(10);
            expect(execution.results['batch-stage'][0]).toEqual({
                ...1,
                processed: true,
            });
        });
    });
    describe('parallel execution', () => {
        it('should execute stages in parallel when specified', async () => {
            // Arrange
            const mockJob = {
                finished: jest.fn().mockResolvedValue({ result: 'success' }),
            };
            mockAiRequestQueue.add.mockResolvedValue(mockJob);
            const parallelDefinition = {
                ...mockPipelineDefinition,
                executionStrategy: 'parallel',
                stages: [
                    {
                        id: 'parallel-stage-1',
                        name: 'Parallel Stage 1',
                        type: 'data-preprocessing',
                        config: {},
                    },
                    {
                        id: 'parallel-stage-2',
                        name: 'Parallel Stage 2',
                        type: 'data-preprocessing',
                        config: {},
                    },
                ],
            };
            // Act
            const execution = await service.createPipeline(parallelDefinition);
            // Assert
            expect(execution.status).toBe('completed');
            expect(execution.metrics.completedStages).toBe(2);
        });
        it('should handle stage dependencies in parallel execution', async () => {
            // Arrange
            const mockJob = {
                finished: jest.fn().mockResolvedValue({ result: 'success' }),
            };
            mockAiRequestQueue.add.mockResolvedValue(mockJob);
            const dependentParallelDefinition = {
                ...mockPipelineDefinition,
                executionStrategy: 'parallel',
                stages: [
                    {
                        id: 'independent-stage',
                        name: 'Independent Stage',
                        type: 'data-preprocessing',
                        config: {},
                    },
                    {
                        id: 'dependent-stage',
                        name: 'Dependent Stage',
                        type: 'ai-analysis',
                        dependencies: ['independent-stage'],
                        config: {},
                    },
                ],
            };
            // Act
            const execution = await service.createPipeline(dependentParallelDefinition);
            // Assert
            expect(execution.status).toBe('completed');
            expect(execution.metrics.completedStages).toBe(2);
        });
    });
    describe('error handling', () => {
        it('should handle stage failures with continueOnFailure', async () => {
            // Arrange
            const failingJob = {
                finished: jest.fn().mockRejectedValue(new Error('Stage failed')),
            };
            const successJob = {
                finished: jest.fn().mockResolvedValue({ result: 'success' }),
            };
            mockAiRequestQueue.add
                .mockResolvedValueOnce(failingJob)
                .mockResolvedValueOnce(successJob);
            const resilientDefinition = {
                ...mockPipelineDefinition,
                stages: [
                    {
                        id: 'failing-stage',
                        name: 'Failing Stage',
                        type: 'ai-analysis',
                        continueOnFailure: true,
                        config: {},
                    },
                    {
                        id: 'success-stage',
                        name: 'Success Stage',
                        type: 'data-preprocessing',
                        config: {},
                    },
                ],
            };
            // Act
            const execution = await service.createPipeline(resilientDefinition);
            // Assert
            expect(execution.status).toBe('completed');
            expect(execution.metrics.failedStages).toBe(1);
            expect(execution.metrics.completedStages).toBe(1);
        });
        it('should fail pipeline when critical stage fails', async () => {
            // Arrange
            const failingJob = {
                finished: jest.fn().mockRejectedValue(new Error('Critical stage failed')),
            };
            mockAiRequestQueue.add.mockResolvedValue(failingJob);
            const criticalDefinition = {
                ...mockPipelineDefinition,
                stages: [
                    {
                        id: 'critical-stage',
                        name: 'Critical Stage',
                        type: 'ai-analysis',
                        continueOnFailure: false, // Critical stage
                        config: {},
                    },
                ],
            };
            // Act & Assert
            await expect(service.createPipeline(criticalDefinition))
                .rejects.toThrow('Pipeline creation failed');
        });
    });
    describe('template management', () => {
        it('should register pipeline template successfully', () => {
            // Arrange
            const template = {
                name: 'Custom Template',
                description: 'Custom pipeline template',
                stages: [
                    {
                        id: 'custom-stage',
                        name: 'Custom Stage',
                        type: 'ai-analysis',
                        config: { model: 'custom-model' },
                    },
                ],
                executionStrategy: 'sequential',
            };
            // Act
            service.registerPipelineTemplate('custom-template', template);
            // Assert - Should not throw error and template should be usable
            expect(() => service.registerPipelineTemplate('custom-template', template))
                .not.toThrow();
        });
    });
    describe('pipeline metrics and monitoring', () => {
        it('should track pipeline metrics correctly', async () => {
            // Arrange
            const mockJob = {
                finished: jest.fn().mockImplementation(() => new Promise(resolve => setTimeout(() => resolve({ result: 'success' }), 10))),
            };
            mockAiRequestQueue.add.mockResolvedValue(mockJob);
            // Act
            const execution = await service.createPipeline(mockPipelineDefinition);
            // Assert
            expect(execution.metrics).toEqual({
                startTime: expect.any(Date),
                endTime: expect.any(Date),
                totalStages: 2,
                completedStages: 2,
                failedStages: 0,
            });
            expect(execution.metrics.endTime.getTime()).toBeGreaterThanOrEqual(execution.metrics.startTime.getTime());
        });
        it('should calculate progress correctly', async () => {
            // Arrange
            const mockJob = {
                finished: jest.fn().mockResolvedValue({ result: 'success' }),
            };
            mockAiRequestQueue.add.mockResolvedValue(mockJob);
            const execution = await service.createPipeline(mockPipelineDefinition);
            // Act
            const status = await service.getPipelineStatus(execution.id);
            // Assert
            expect(status.progress).toBe(1); // 100% complete (2/2 stages)
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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