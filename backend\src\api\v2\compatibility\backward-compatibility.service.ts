import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * Backward compatibility service for API v2
 * Handles migration from v1 to v2 and maintains compatibility
 */
@Injectable()
export class BackwardCompatibilityService {
  private readonly logger = new Logger(BackwardCompatibilityService.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * Transform v1 request to v2 format
   */
  transformV1RequestToV2(v1Request: any): any {
    this.logger.debug('Transforming v1 request to v2 format', {
      originalRequest: v1Request,
    });

    let v2Request = {
      ...v1Request,
      metadata: {
        version: '2.0',
        migratedFrom: '1.0',
        timestamp: new Date().toISOString(),
      },
    };

    // Transform specific v1 fields to v2 equivalents
    v2Request = this.transformRequestFields(v2Request);

    // Add v2-specific enhancements
    v2Request = this.addV2Enhancements(v2Request);

    this.logger.debug('V1 to V2 request transformation completed', {
      transformedRequest: v2Request,
    });

    return v2Request;
  }

  /**
   * Transform v2 response to v1 format for backward compatibility
   */
  transformV2ResponseToV1(v2Response: any): any {
    this.logger.debug('Transforming v2 response to v1 format', {
      originalResponse: v2Response,
    });

    // Extract v1-compatible data from v2 response
    const v1Response: any = {
      success: v2Response.success,
      data: this.extractV1Data(v2Response.data),
      message: v2Response.error?.message || 'Success',
    };

    // Add v1-specific fields if they existed
    if (v2Response.pagination) {
      v1Response.pagination = this.transformPaginationToV1(v2Response.pagination);
    }

    // Add compatibility metadata
    v1Response.metadata = {
      version: '1.0',
      generatedFrom: '2.0',
      timestamp: new Date().toISOString(),
    };

    this.logger.debug('V2 to V1 response transformation completed', {
      transformedResponse: v1Response,
    });

    return v1Response;
  }

  /**
   * Check if request is from v1 client
   */
  isV1Client(headers: any): boolean {
    const userAgent = headers['user-agent'] || '';
    const apiVersion = headers['x-api-version'] || '';
    const acceptHeader = headers['accept'] || '';

    // Check for v1 indicators
    return (
      apiVersion === '1.0' ||
      userAgent.includes('sentinel-v1') ||
      acceptHeader.includes('application/vnd.sentinel.v1+json') ||
      !headers['x-enhanced-features'] // v2 clients should have this header
    );
  }

  /**
   * Get migration recommendations for v1 clients
   */
  getMigrationRecommendations(endpoint: string): any {
    const recommendations = {
      endpoint,
      currentVersion: '1.0',
      recommendedVersion: '2.0',
      benefits: [
        'Enhanced analytics with AI-powered insights',
        'Advanced correlation analysis',
        'Real-time streaming capabilities',
        'Improved performance and caching',
        'Better error handling and validation',
      ],
      migrationSteps: [
        'Update client to use v2 endpoints',
        'Add required v2 headers (x-api-version: 2.0)',
        'Update request/response handling for new format',
        'Test with v2 enhanced features',
        'Update documentation and client libraries',
      ],
      breakingChanges: this.getBreakingChanges(endpoint),
      timeline: {
        v1Support: 'Until 2025-12-31',
        v1Deprecation: '2025-06-30',
        v2Stable: '2025-01-01',
      },
    };

    return recommendations;
  }

  /**
   * Handle deprecated v1 endpoints
   */
  handleDeprecatedEndpoint(endpoint: string, headers: any): any {
    const deprecationInfo = {
      deprecated: true,
      version: '1.0',
      sunset: '2025-12-31',
      replacement: this.getV2Replacement(endpoint),
      notice: `This v1 endpoint is deprecated. Please migrate to v2 for enhanced features.`,
      migrationGuide: `${this.configService.get('API_DOCS_URL')}/migration/v1-to-v2`,
    };

    // Log deprecation usage
    this.logger.warn('Deprecated v1 endpoint accessed', {
      endpoint,
      userAgent: headers['user-agent'],
      clientIp: headers['x-forwarded-for'] || headers['x-real-ip'],
      timestamp: new Date().toISOString(),
    });

    return deprecationInfo;
  }

  private transformRequestFields(request: any): any {
    const transformed = { ...request };

    // Transform common v1 field names to v2 equivalents
    const fieldMappings = {
      'event_type': 'eventType',
      'created_at': 'createdAt',
      'updated_at': 'updatedAt',
      'user_id': 'userId',
      'asset_id': 'assetId',
      'severity_level': 'severityLevel',
      'threat_type': 'threatType',
    };

    Object.keys(fieldMappings).forEach(oldField => {
      if (transformed[oldField] !== undefined) {
        transformed[fieldMappings[oldField]] = transformed[oldField];
        delete transformed[oldField];
      }
    });

    return transformed;
  }

  private addV2Enhancements(request: any): any {
    const enhanced = { ...request };

    // Add v2-specific enhancements
    enhanced.features = {
      analytics: true,
      correlation: true,
      realTime: false,
      advanced: true,
    };

    // Add correlation tracking
    enhanced.correlationId = `v2-compat-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

    // Add performance tracking
    enhanced.performance = {
      startTime: Date.now(),
      version: '2.0',
      compatibility: true,
    };

    return enhanced;
  }

  private extractV1Data(v2Data: any): any {
    if (!v2Data) return v2Data;

    // Remove v2-specific fields that v1 clients don't expect
    const v1Data = { ...v2Data };
    
    // Remove v2 enhancements
    delete v1Data.correlationId;
    delete v1Data.features;
    delete v1Data.performance;
    delete v1Data.links;
    delete v1Data.insights;

    // Transform field names back to v1 format
    const reverseFieldMappings = {
      'eventType': 'event_type',
      'createdAt': 'created_at',
      'updatedAt': 'updated_at',
      'userId': 'user_id',
      'assetId': 'asset_id',
      'severityLevel': 'severity_level',
      'threatType': 'threat_type',
    };

    Object.keys(reverseFieldMappings).forEach(v2Field => {
      if (v1Data[v2Field] !== undefined) {
        v1Data[reverseFieldMappings[v2Field]] = v1Data[v2Field];
        delete v1Data[v2Field];
      }
    });

    return v1Data;
  }

  private transformPaginationToV1(v2Pagination: any): any {
    return {
      page: v2Pagination.page,
      per_page: v2Pagination.limit,
      total: v2Pagination.total,
      total_pages: v2Pagination.totalPages,
      has_next: v2Pagination.hasNext,
      has_prev: v2Pagination.hasPrev,
    };
  }

  private getBreakingChanges(endpoint: string): string[] {
    const commonBreakingChanges = [
      'Response format changed to include metadata and links',
      'Field names changed from snake_case to camelCase',
      'Enhanced error format with additional details',
      'New required headers for v2 features',
    ];

    const endpointSpecificChanges = {
      '/analytics': [
        'Analytics data structure enhanced with insights',
        'New aggregation types and metrics available',
        'Time range parameters format changed',
      ],
      '/correlation': [
        'Correlation strength enum values changed',
        'New correlation types added',
        'Relationship mapping format enhanced',
      ],
    };

    const specificChanges = Object.keys(endpointSpecificChanges)
      .filter(path => endpoint.includes(path))
      .flatMap(path => endpointSpecificChanges[path]);

    return [...commonBreakingChanges, ...specificChanges];
  }

  private getV2Replacement(v1Endpoint: string): string {
    const replacements = {
      '/api/v1/analytics/summary': '/api/v2/analytics/executive/summary',
      '/api/v1/analytics/metrics': '/api/v2/analytics/security/metrics',
      '/api/v1/correlation/events': '/api/v2/correlation/cross-module',
      '/api/v1/correlation/relationships': '/api/v2/correlation/relationships/map',
    };

    return replacements[v1Endpoint] || v1Endpoint.replace('/v1/', '/v2/');
  }
}