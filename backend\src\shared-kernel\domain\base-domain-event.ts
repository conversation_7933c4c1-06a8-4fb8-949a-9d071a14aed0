import { UniqueEntityId } from '../value-objects/unique-entity-id.value-object';

/**
 * Base Domain Event
 * 
 * Abstract base class for all domain events in the system.
 * Domain events represent something important that happened in the domain.
 * 
 * Key characteristics:
 * - Immutable once created
 * - Contains all necessary information about what happened
 * - Can be used for event sourcing, integration events, and notifications
 * - Includes metadata for tracking and debugging
 * 
 * @template T The event data type
 */
export abstract class BaseDomainEvent<T = any> {
  private readonly _eventId: UniqueEntityId;
  private readonly _occurredOn: Date;
  private readonly _aggregateId: UniqueEntityId;
  private readonly _eventVersion: number;
  private readonly _eventData: T;
  private _isDispatched: boolean = false;
  private _dispatchedAt?: Date;
  private readonly _correlationId?: string;
  private readonly _causationId?: string;
  private readonly _metadata: Record<string, any>;

  constructor(
    aggregateId: UniqueEntityId,
    eventData: T,
    options?: {
      eventId?: UniqueEntityId;
      occurredOn?: Date;
      eventVersion?: number;
      correlationId?: string;
      causationId?: string;
      metadata?: Record<string, any>;
    }
  ) {
    this._eventId = options?.eventId || UniqueEntityId.generate();
    this._occurredOn = options?.occurredOn || new Date();
    this._aggregateId = aggregateId;
    this._eventVersion = options?.eventVersion || 1;
    this._eventData = eventData;
    this._correlationId = options?.correlationId;
    this._causationId = options?.causationId;
    this._metadata = options?.metadata || {};

    // Freeze the event data and metadata to make them immutable
    Object.freeze(this._eventData);
    Object.freeze(this._metadata);
    // Note: We don't freeze the entire object because we need to modify dispatch status
  }

  /**
   * Unique identifier for this event
   */
  get eventId(): UniqueEntityId {
    return this._eventId;
  }

  /**
   * When the event occurred
   */
  get occurredOn(): Date {
    return this._occurredOn;
  }

  /**
   * The aggregate that generated this event
   */
  get aggregateId(): UniqueEntityId {
    return this._aggregateId;
  }

  /**
   * Version of the event schema
   */
  get eventVersion(): number {
    return this._eventVersion;
  }

  /**
   * The event data payload
   */
  get eventData(): T {
    return this._eventData;
  }

  /**
   * Whether this event has been dispatched
   */
  get isDispatched(): boolean {
    return this._isDispatched;
  }

  /**
   * When the event was dispatched
   */
  get dispatchedAt(): Date | undefined {
    return this._dispatchedAt;
  }

  /**
   * Correlation ID for tracking related events
   */
  get correlationId(): string | undefined {
    return this._correlationId;
  }

  /**
   * Causation ID for tracking event chains
   */
  get causationId(): string | undefined {
    return this._causationId;
  }

  /**
   * Additional metadata for the event
   */
  get metadata(): Record<string, any> {
    return { ...this._metadata };
  }

  /**
   * Get the event type name
   */
  get eventType(): string {
    return this.constructor.name;
  }

  /**
   * Get the event name for event emitter
   */
  get eventName(): string {
    return this.eventType;
  }

  /**
   * Mark the event as dispatched
   * This should only be called by the event dispatcher
   */
  public markAsDispatched(): void {
    if (this._isDispatched) {
      throw new Error(`Event ${this._eventId.toString()} has already been dispatched`);
    }

    this._isDispatched = true;
    this._dispatchedAt = new Date();
  }

  /**
   * Get the age of the event in milliseconds
   */
  public getAge(): number {
    return Date.now() - this._occurredOn.getTime();
  }

  /**
   * Get the age of the event in seconds
   */
  public getAgeInSeconds(): number {
    return Math.floor(this.getAge() / 1000);
  }

  /**
   * Get the age of the event in minutes
   */
  public getAgeInMinutes(): number {
    return Math.floor(this.getAge() / (1000 * 60));
  }

  /**
   * Check if the event is stale (older than specified time)
   * 
   * @param maxAgeMs Maximum age in milliseconds
   */
  public isStale(maxAgeMs: number): boolean {
    return this.getAge() > maxAgeMs;
  }

  /**
   * Create a new event with updated metadata
   * 
   * @param newMetadata Additional metadata to merge
   */
  public withMetadata(newMetadata: Record<string, any>): this {
    const constructor = this.constructor as new (...args: any[]) => this;
    return new constructor(this._aggregateId, this._eventData, {
      eventId: this._eventId,
      occurredOn: this._occurredOn,
      eventVersion: this._eventVersion,
      correlationId: this._correlationId,
      causationId: this._causationId,
      metadata: { ...this._metadata, ...newMetadata },
    });
  }

  /**
   * Create a new event with a correlation ID
   * 
   * @param correlationId The correlation ID to set
   */
  public withCorrelationId(correlationId: string): this {
    const constructor = this.constructor as new (...args: any[]) => this;
    return new constructor(this._aggregateId, this._eventData, {
      eventId: this._eventId,
      occurredOn: this._occurredOn,
      eventVersion: this._eventVersion,
      correlationId,
      causationId: this._causationId,
      metadata: this._metadata,
    });
  }

  /**
   * Create a new event with a causation ID
   * 
   * @param causationId The causation ID to set
   */
  public withCausationId(causationId: string): this {
    const constructor = this.constructor as new (...args: any[]) => this;
    return new constructor(this._aggregateId, this._eventData, {
      eventId: this._eventId,
      occurredOn: this._occurredOn,
      eventVersion: this._eventVersion,
      correlationId: this._correlationId,
      causationId,
      metadata: this._metadata,
    });
  }

  /**
   * Convert event to JSON representation
   */
  public toJSON(): Record<string, any> {
    return {
      eventId: this._eventId.toString(),
      eventType: this.eventType,
      occurredOn: this._occurredOn.toISOString(),
      aggregateId: this._aggregateId.toString(),
      eventVersion: this._eventVersion,
      eventData: this._eventData,
      isDispatched: this._isDispatched,
      dispatchedAt: this._dispatchedAt?.toISOString(),
      correlationId: this._correlationId,
      causationId: this._causationId,
      metadata: this._metadata,
    };
  }

  /**
   * Create event from JSON representation
   */
  public static fromJSON<TEvent extends BaseDomainEvent>(
    json: Record<string, any>,
    eventClass: new (...args: any[]) => TEvent
  ): TEvent {
    return new eventClass(
      UniqueEntityId.fromString(json.aggregateId),
      json.eventData,
      {
        eventId: UniqueEntityId.fromString(json.eventId),
        occurredOn: new Date(json.occurredOn),
        eventVersion: json.eventVersion,
        correlationId: json.correlationId,
        causationId: json.causationId,
        metadata: json.metadata,
      }
    );
  }

  /**
   * Compare events for equality
   */
  public equals(other?: BaseDomainEvent): boolean {
    if (!other) {
      return false;
    }

    return this._eventId.equals(other._eventId);
  }

  /**
   * Get a string representation of the event
   */
  public toString(): string {
    return `${this.eventType}(${this._eventId.toString()})`;
  }
}
