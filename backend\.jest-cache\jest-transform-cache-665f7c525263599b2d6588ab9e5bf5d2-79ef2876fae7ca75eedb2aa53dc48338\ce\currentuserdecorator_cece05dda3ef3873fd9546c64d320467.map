{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\auth\\decorators\\current-user.decorator.ts", "mappings": ";;;AAAA,2CAAwE;AAkBxE;;;;;;;;;;;;;;;;;;GAkBG;AACU,QAAA,WAAW,GAAG,IAAA,6BAAoB,EAC7C,CAAC,IAAyC,EAAE,GAAqB,EAAE,EAAE;IACnE,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;IAChD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAyB,CAAC;IAE/C,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AAClC,CAAC,CACF,CAAC;AAEF;;;;;;;;;;GAUG;AACU,QAAA,aAAa,GAAG,IAAA,6BAAoB,EAC/C,CAAC,IAAa,EAAE,GAAqB,EAAE,EAAE;IACvC,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;IAChD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAyB,CAAC;IAC/C,OAAO,IAAI,EAAE,EAAE,CAAC;AAClB,CAAC,CACF,CAAC;AAEF;;;;;;;;;;GAUG;AACU,QAAA,gBAAgB,GAAG,IAAA,6BAAoB,EAClD,CAAC,IAAa,EAAE,GAAqB,EAAE,EAAE;IACvC,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;IAChD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAyB,CAAC;IAC/C,OAAO,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC;AAC3B,CAAC,CACF,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\infrastructure\\auth\\decorators\\current-user.decorator.ts"], "sourcesContent": ["import { createParamDecorator, ExecutionContext } from '@nestjs/common';\n\n/**\n * Interface for the authenticated user object\n */\nexport interface AuthenticatedUser {\n  id: string;\n  email: string;\n  username?: string;\n  roles: string[];\n  permissions?: string[];\n  tenantId?: string;\n  isActive: boolean;\n  lastLoginAt?: Date;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\n/**\n * Current User decorator to extract the authenticated user from the request\n * \n * @param data - Optional property name to extract from the user object\n * @returns The authenticated user object or a specific property\n * \n * @example\n * ```typescript\n * @Get('/profile')\n * getProfile(@CurrentUser() user: AuthenticatedUser) {\n *   return user;\n * }\n * \n * @Get('/user-id')\n * getUserId(@CurrentUser('id') userId: string) {\n *   return { userId };\n * }\n * ```\n */\nexport const CurrentUser = createParamDecorator(\n  (data: keyof AuthenticatedUser | undefined, ctx: ExecutionContext) => {\n    const request = ctx.switchToHttp().getRequest();\n    const user = request.user as AuthenticatedUser;\n    \n    if (!user) {\n      return null;\n    }\n    \n    return data ? user[data] : user;\n  },\n);\n\n/**\n * Current User ID decorator - shorthand for getting just the user ID\n * \n * @example\n * ```typescript\n * @Get('/my-data')\n * getMyData(@CurrentUserId() userId: string) {\n *   return this.service.getUserData(userId);\n * }\n * ```\n */\nexport const CurrentUserId = createParamDecorator(\n  (data: unknown, ctx: ExecutionContext) => {\n    const request = ctx.switchToHttp().getRequest();\n    const user = request.user as AuthenticatedUser;\n    return user?.id;\n  },\n);\n\n/**\n * Current User Roles decorator - shorthand for getting user roles\n * \n * @example\n * ```typescript\n * @Get('/check-roles')\n * checkRoles(@CurrentUserRoles() roles: string[]) {\n *   return { roles };\n * }\n * ```\n */\nexport const CurrentUserRoles = createParamDecorator(\n  (data: unknown, ctx: ExecutionContext) => {\n    const request = ctx.switchToHttp().getRequest();\n    const user = request.user as AuthenticatedUser;\n    return user?.roles || [];\n  },\n);\n"], "version": 3}