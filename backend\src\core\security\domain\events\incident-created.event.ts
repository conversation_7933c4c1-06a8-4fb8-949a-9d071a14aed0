import { BaseDomainEvent } from '../../../../shared-kernel/domain/base-domain-event';
import { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';
import { ThreatSeverity } from '../enums/threat-severity.enum';
import { IncidentPriority } from '../entities/incident/incident.entity';

/**
 * Incident Created Event Data
 */
export interface IncidentCreatedEventData {
  /** ID of the created incident */
  incidentId: string;
  /** Incident title */
  title: string;
  /** Incident severity */
  severity: ThreatSeverity;
  /** Incident priority */
  priority: IncidentPriority;
  /** Incident category */
  category: string;
  /** Incident type */
  type: string;
  /** When the incident was detected */
  detectedAt: string;
  /** When the incident was reported */
  reportedAt: string;
  /** Number of affected assets */
  affectedAssetCount: number;
  /** Whether incident commander is assigned */
  hasIncidentCommander: boolean;
  /** When the incident was created */
  timestamp: string;
  /** Incident metadata */
  metadata?: {
    relatedEventCount: number;
    relatedThreatCount: number;
    relatedVulnerabilityCount: number;
    responseTeamSize: number;
    estimatedImpact: string;
    complianceRequirements: string[];
  };
}

/**
 * Incident Created Domain Event
 * 
 * Published when a new security incident is created in the system.
 * This event triggers incident response workflows and notifications.
 * 
 * Key use cases:
 * - Initiate incident response procedures
 * - Send incident notifications to stakeholders
 * - Trigger automated response actions
 * - Update incident management dashboards
 * - Begin compliance reporting processes
 * - Escalate critical incidents
 */
export class IncidentCreatedEvent extends BaseDomainEvent<IncidentCreatedEventData> {
  constructor(
    aggregateId: UniqueEntityId,
    eventData: IncidentCreatedEventData,
    options?: {
      eventId?: UniqueEntityId;
      occurredOn?: Date;
      correlationId?: string;
      causationId?: string;
      metadata?: Record<string, any>;
    }
  ) {
    super(aggregateId, eventData, {
      eventVersion: 1,
      ...options,
      metadata: {
        eventType: 'IncidentCreated',
        domain: 'Security',
        aggregateType: 'Incident',
        ...options?.metadata,
      },
    });
  }

  /**
   * Get the incident ID
   */
  get incidentId(): string {
    return this.eventData.incidentId;
  }

  /**
   * Get the incident title
   */
  get title(): string {
    return this.eventData.title;
  }

  /**
   * Get the incident severity
   */
  get severity(): ThreatSeverity {
    return this.eventData.severity;
  }

  /**
   * Get the incident priority
   */
  get priority(): IncidentPriority {
    return this.eventData.priority;
  }

  /**
   * Get the incident category
   */
  get category(): string {
    return this.eventData.category;
  }

  /**
   * Get the incident type
   */
  get incidentType(): string {
    return this.eventData.type;
  }

  /**
   * Get detection timestamp
   */
  get detectedAt(): string {
    return this.eventData.detectedAt;
  }

  /**
   * Get reporting timestamp
   */
  get reportedAt(): string {
    return this.eventData.reportedAt;
  }

  /**
   * Get affected asset count
   */
  get affectedAssetCount(): number {
    return this.eventData.affectedAssetCount;
  }

  /**
   * Check if incident commander is assigned
   */
  get hasIncidentCommander(): boolean {
    return this.eventData.hasIncidentCommander;
  }

  /**
   * Get creation timestamp
   */
  get creationTimestamp(): string {
    return this.eventData.timestamp;
  }

  /**
   * Check if incident is critical priority
   */
  isCriticalPriority(): boolean {
    return this.eventData.priority === IncidentPriority.CRITICAL ||
           this.eventData.priority === IncidentPriority.EMERGENCY;
  }

  /**
   * Check if incident is high priority or above
   */
  isHighPriorityOrAbove(): boolean {
    return this.eventData.priority === IncidentPriority.HIGH ||
           this.eventData.priority === IncidentPriority.CRITICAL ||
           this.eventData.priority === IncidentPriority.EMERGENCY;
  }

  /**
   * Check if incident is critical severity
   */
  isCriticalSeverity(): boolean {
    return this.eventData.severity === ThreatSeverity.CRITICAL;
  }

  /**
   * Check if incident affects multiple assets
   */
  affectsMultipleAssets(): boolean {
    return this.eventData.affectedAssetCount > 1;
  }

  /**
   * Check if incident affects many assets
   */
  affectsManyAssets(): boolean {
    return this.eventData.affectedAssetCount >= 10;
  }

  /**
   * Check if incident requires immediate response
   */
  requiresImmediateResponse(): boolean {
    return this.isCriticalPriority() || 
           (this.isCriticalSeverity() && this.affectsMultipleAssets());
  }

  /**
   * Check if incident requires executive notification
   */
  requiresExecutiveNotification(): boolean {
    return this.isCriticalPriority() ||
           (this.isCriticalSeverity() && this.affectsManyAssets());
  }

  /**
   * Check if incident requires war room activation
   */
  requiresWarRoomActivation(): boolean {
    return this.eventData.priority === IncidentPriority.EMERGENCY ||
           (this.isCriticalSeverity() && this.affectsManyAssets());
  }

  /**
   * Check if incident requires external communication
   */
  requiresExternalCommunication(): boolean {
    return this.isCriticalPriority() && this.affectsManyAssets();
  }

  /**
   * Get response timeline (minutes)
   */
  getResponseTimeline(): number {
    switch (this.eventData.priority) {
      case IncidentPriority.EMERGENCY: return 15;  // 15 minutes
      case IncidentPriority.CRITICAL: return 60;   // 1 hour
      case IncidentPriority.HIGH: return 240;      // 4 hours
      case IncidentPriority.MEDIUM: return 480;    // 8 hours
      case IncidentPriority.LOW: return 1440;      // 24 hours
      default: return 2880;                        // 48 hours
    }
  }

  /**
   * Get containment timeline (hours)
   */
  getContainmentTimeline(): number {
    switch (this.eventData.severity) {
      case ThreatSeverity.CRITICAL: return 4;   // 4 hours
      case ThreatSeverity.HIGH: return 24;      // 24 hours
      case ThreatSeverity.MEDIUM: return 72;    // 3 days
      case ThreatSeverity.LOW: return 168;      // 7 days
      default: return 336;                      // 14 days
    }
  }

  /**
   * Get resolution timeline (hours)
   */
  getResolutionTimeline(): number {
    switch (this.eventData.priority) {
      case IncidentPriority.EMERGENCY: return 8;    // 8 hours
      case IncidentPriority.CRITICAL: return 24;    // 24 hours
      case IncidentPriority.HIGH: return 72;        // 3 days
      case IncidentPriority.MEDIUM: return 168;     // 7 days
      case IncidentPriority.LOW: return 720;        // 30 days
      default: return 1440;                         // 60 days
    }
  }

  /**
   * Get notification channels
   */
  getNotificationChannels(): string[] {
    const channels: string[] = ['webhook'];

    if (this.requiresImmediateResponse()) {
      channels.push('email', 'slack', 'sms', 'pager');
    } else if (this.isHighPriorityOrAbove()) {
      channels.push('email', 'slack');
    } else {
      channels.push('email');
    }

    if (this.requiresExecutiveNotification()) {
      channels.push('executive_dashboard', 'phone');
    }

    if (this.requiresWarRoomActivation()) {
      channels.push('war_room_activation');
    }

    return channels;
  }

  /**
   * Get automated response actions
   */
  getAutomatedResponseActions(): string[] {
    const actions: string[] = [];

    if (this.requiresImmediateResponse()) {
      actions.push('activate_incident_response', 'assign_incident_commander', 'create_war_room');
    }

    if (!this.eventData.hasIncidentCommander && this.isHighPriorityOrAbove()) {
      actions.push('auto_assign_commander');
    }

    if (this.affectsMultipleAssets()) {
      actions.push('asset_impact_assessment', 'network_isolation_prep');
    }

    if (this.isCriticalSeverity()) {
      actions.push('threat_intelligence_lookup', 'forensic_preparation');
    }

    if (this.requiresExternalCommunication()) {
      actions.push('prepare_external_communications', 'legal_notification');
    }

    return actions;
  }

  /**
   * Get escalation requirements
   */
  getEscalationRequirements(): {
    immediate: boolean;
    executiveLevel: boolean;
    externalAgencies: boolean;
    legalTeam: boolean;
    publicRelations: boolean;
  } {
    return {
      immediate: this.requiresImmediateResponse(),
      executiveLevel: this.requiresExecutiveNotification(),
      externalAgencies: this.requiresExternalCommunication(),
      legalTeam: this.isCriticalPriority(),
      publicRelations: this.requiresWarRoomActivation(),
    };
  }

  /**
   * Get metrics tags for this event
   */
  getMetricsTags(): Record<string, string> {
    return {
      event_type: 'incident_created',
      severity: this.eventData.severity,
      priority: this.eventData.priority,
      category: this.eventData.category,
      type: this.eventData.type,
      has_incident_commander: this.eventData.hasIncidentCommander.toString(),
      is_critical_priority: this.isCriticalPriority().toString(),
      is_critical_severity: this.isCriticalSeverity().toString(),
      affects_multiple_assets: this.affectsMultipleAssets().toString(),
      requires_immediate_response: this.requiresImmediateResponse().toString(),
      requires_executive_notification: this.requiresExecutiveNotification().toString(),
      requires_war_room: this.requiresWarRoomActivation().toString(),
      affected_asset_count: this.eventData.affectedAssetCount.toString(),
    };
  }

  /**
   * Get audit log entry
   */
  getAuditLogEntry(): {
    action: string;
    resource: string;
    resourceId: string;
    details: Record<string, any>;
    timestamp: string;
  } {
    return {
      action: 'incident_created',
      resource: 'Incident',
      resourceId: this.eventData.incidentId,
      details: {
        title: this.eventData.title,
        severity: this.eventData.severity,
        priority: this.eventData.priority,
        category: this.eventData.category,
        type: this.eventData.type,
        detectedAt: this.eventData.detectedAt,
        reportedAt: this.eventData.reportedAt,
        affectedAssetCount: this.eventData.affectedAssetCount,
        hasIncidentCommander: this.eventData.hasIncidentCommander,
        requiresImmediateResponse: this.requiresImmediateResponse(),
        requiresExecutiveNotification: this.requiresExecutiveNotification(),
        responseTimeline: this.getResponseTimeline(),
        containmentTimeline: this.getContainmentTimeline(),
        resolutionTimeline: this.getResolutionTimeline(),
        escalationRequirements: this.getEscalationRequirements(),
      },
      timestamp: this.occurredOn.toISOString(),
    };
  }

  /**
   * Create integration event for external systems
   */
  toIntegrationEvent(): {
    eventType: string;
    version: string;
    timestamp: string;
    data: {
      incidentId: string;
      incident: {
        title: string;
        severity: string;
        priority: string;
        category: string;
        type: string;
        detectedAt: string;
        reportedAt: string;
      };
      impact: {
        affectedAssetCount: number;
        hasIncidentCommander: boolean;
      };
      response: {
        responseTimeline: number;
        containmentTimeline: number;
        resolutionTimeline: number;
        automatedActions: string[];
      };
      flags: {
        requiresImmediateResponse: boolean;
        requiresExecutiveNotification: boolean;
        requiresWarRoomActivation: boolean;
        requiresExternalCommunication: boolean;
        isCriticalPriority: boolean;
        isCriticalSeverity: boolean;
      };
      escalation: {
        immediate: boolean;
        executiveLevel: boolean;
        externalAgencies: boolean;
        legalTeam: boolean;
        publicRelations: boolean;
      };
    };
    metadata: {
      correlationId?: string;
      causationId?: string;
      domain: string;
      aggregateType: string;
    };
  } {
    return {
      eventType: 'IncidentCreated',
      version: '1.0',
      timestamp: this.occurredOn.toISOString(),
      data: {
        incidentId: this.eventData.incidentId,
        incident: {
          title: this.eventData.title,
          severity: this.eventData.severity,
          priority: this.eventData.priority,
          category: this.eventData.category,
          type: this.eventData.type,
          detectedAt: this.eventData.detectedAt,
          reportedAt: this.eventData.reportedAt,
        },
        impact: {
          affectedAssetCount: this.eventData.affectedAssetCount,
          hasIncidentCommander: this.eventData.hasIncidentCommander,
        },
        response: {
          responseTimeline: this.getResponseTimeline(),
          containmentTimeline: this.getContainmentTimeline(),
          resolutionTimeline: this.getResolutionTimeline(),
          automatedActions: this.getAutomatedResponseActions(),
        },
        flags: {
          requiresImmediateResponse: this.requiresImmediateResponse(),
          requiresExecutiveNotification: this.requiresExecutiveNotification(),
          requiresWarRoomActivation: this.requiresWarRoomActivation(),
          requiresExternalCommunication: this.requiresExternalCommunication(),
          isCriticalPriority: this.isCriticalPriority(),
          isCriticalSeverity: this.isCriticalSeverity(),
        },
        escalation: this.getEscalationRequirements(),
      },
      metadata: {
        correlationId: this.correlationId,
        causationId: this.causationId,
        domain: 'Security',
        aggregateType: 'Incident',
      },
    };
  }

  /**
   * Convert to JSON representation
   */
  public toJSON(): Record<string, any> {
    return {
      ...super.toJSON(),
      eventData: this.eventData,
      analysis: {
        isCriticalPriority: this.isCriticalPriority(),
        isHighPriorityOrAbove: this.isHighPriorityOrAbove(),
        isCriticalSeverity: this.isCriticalSeverity(),
        affectsMultipleAssets: this.affectsMultipleAssets(),
        affectsManyAssets: this.affectsManyAssets(),
        requiresImmediateResponse: this.requiresImmediateResponse(),
        requiresExecutiveNotification: this.requiresExecutiveNotification(),
        requiresWarRoomActivation: this.requiresWarRoomActivation(),
        requiresExternalCommunication: this.requiresExternalCommunication(),
        responseTimeline: this.getResponseTimeline(),
        containmentTimeline: this.getContainmentTimeline(),
        resolutionTimeline: this.getResolutionTimeline(),
        notificationChannels: this.getNotificationChannels(),
        automatedResponseActions: this.getAutomatedResponseActions(),
        escalationRequirements: this.getEscalationRequirements(),
        metricsTags: this.getMetricsTags(),
      },
    };
  }

  /**
   * Create from JSON representation
   */
  static fromJSON(json: Record<string, any>): IncidentCreatedEvent {
    return new IncidentCreatedEvent(
      UniqueEntityId.fromString(json.aggregateId),
      json.eventData,
      {
        eventId: UniqueEntityId.fromString(json.eventId),
        occurredOn: new Date(json.occurredOn),
        correlationId: json.correlationId,
        causationId: json.causationId,
        metadata: json.metadata,
      }
    );
  }

  /**
   * Get human-readable description
   */
  getDescription(): string {
    const priorityText = this.eventData.priority.toUpperCase();
    const severityText = this.eventData.severity.toUpperCase();
    const assetText = this.affectsMultipleAssets() 
      ? ` affecting ${this.eventData.affectedAssetCount} assets` 
      : ' affecting single asset';
    const commanderText = this.eventData.hasIncidentCommander ? ' with commander assigned' : ' without commander';
    
    return `${priorityText} priority incident "${this.eventData.title}" (${severityText} severity)${assetText}${commanderText}`;
  }

  /**
   * Get event summary for logging
   */
  getSummary(): {
    eventType: string;
    incidentId: string;
    title: string;
    severity: string;
    priority: string;
    category: string;
    affectedAssetCount: number;
    requiresImmediateResponse: boolean;
    hasIncidentCommander: boolean;
    timestamp: string;
  } {
    return {
      eventType: 'IncidentCreated',
      incidentId: this.eventData.incidentId,
      title: this.eventData.title,
      severity: this.eventData.severity,
      priority: this.eventData.priority,
      category: this.eventData.category,
      affectedAssetCount: this.eventData.affectedAssetCount,
      requiresImmediateResponse: this.requiresImmediateResponse(),
      hasIncidentCommander: this.eventData.hasIncidentCommander,
      timestamp: this.occurredOn.toISOString(),
    };
  }
}
