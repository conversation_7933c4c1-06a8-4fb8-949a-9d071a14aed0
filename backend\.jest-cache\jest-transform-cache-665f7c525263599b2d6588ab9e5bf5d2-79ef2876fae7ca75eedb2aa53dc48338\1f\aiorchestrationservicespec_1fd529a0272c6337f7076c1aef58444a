ae4051590fcd07a1ee3a4bd1d894f368
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const cqrs_1 = require("@nestjs/cqrs");
const ai_orchestration_service_1 = require("../ai-orchestration.service");
const model_selection_service_1 = require("../model-selection.service");
const load_balancer_service_1 = require("../load-balancer.service");
const circuit_breaker_service_1 = require("../../resilience/circuit-breaker.service");
const ai_cache_service_1 = require("../../caching/ai-cache.service");
const metrics_adapter_1 = require("../../../../infrastructure/adapters/metrics.adapter");
const ai_model_provider_interface_1 = require("../../../../domain/services/ai-model-provider.interface");
const prediction_engine_interface_1 = require("../../../../domain/services/prediction-engine.interface");
describe('AiOrchestrationService', () => {
    let service;
    let mockAiModelProvider;
    let mockPredictionEngine;
    let mockModelSelectionService;
    let mockLoadBalancerService;
    let mockCircuitBreakerService;
    let mockCacheService;
    let mockMetricsAdapter;
    let mockEventBus;
    let mockCommandBus;
    let mockQueryBus;
    let mockConfigService;
    beforeEach(async () => {
        // Create mocks
        mockAiModelProvider = {
            analyze: jest.fn(),
        };
        mockPredictionEngine = {
            predict: jest.fn(),
        };
        mockModelSelectionService = {
            selectOptimalModel: jest.fn(),
            selectRealTimeModel: jest.fn(),
        };
        mockLoadBalancerService = {
            getAvailableProviders: jest.fn(),
            checkProviderHealth: jest.fn(),
        };
        mockCircuitBreakerService = {
            execute: jest.fn(),
            getStatus: jest.fn(),
        };
        mockCacheService = {
            get: jest.fn(),
            set: jest.fn().mockResolvedValue(undefined),
            checkHealth: jest.fn(),
        };
        mockMetricsAdapter = {
            recordAiOperation: jest.fn(),
        };
        mockEventBus = {
            publish: jest.fn(),
        };
        mockCommandBus = {
            execute: jest.fn(),
        };
        mockQueryBus = {
            execute: jest.fn(),
        };
        mockConfigService = {
            get: jest.fn(),
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                ai_orchestration_service_1.AiOrchestrationService,
                {
                    provide: ai_model_provider_interface_1.AI_MODEL_PROVIDER,
                    useValue: mockAiModelProvider,
                },
                {
                    provide: prediction_engine_interface_1.PREDICTION_ENGINE,
                    useValue: mockPredictionEngine,
                },
                {
                    provide: model_selection_service_1.ModelSelectionService,
                    useValue: mockModelSelectionService,
                },
                {
                    provide: load_balancer_service_1.LoadBalancerService,
                    useValue: mockLoadBalancerService,
                },
                {
                    provide: circuit_breaker_service_1.CircuitBreakerService,
                    useValue: mockCircuitBreakerService,
                },
                {
                    provide: ai_cache_service_1.AiCacheService,
                    useValue: mockCacheService,
                },
                {
                    provide: metrics_adapter_1.MetricsAdapter,
                    useValue: mockMetricsAdapter,
                },
                {
                    provide: cqrs_1.EventBus,
                    useValue: mockEventBus,
                },
                {
                    provide: cqrs_1.CommandBus,
                    useValue: mockCommandBus,
                },
                {
                    provide: cqrs_1.QueryBus,
                    useValue: mockQueryBus,
                },
                {
                    provide: config_1.ConfigService,
                    useValue: mockConfigService,
                },
            ],
        }).compile();
        service = module.get(ai_orchestration_service_1.AiOrchestrationService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('orchestrateAnalysis', () => {
        const mockRequest = {
            id: 'test-request-1',
            type: 'threat-analysis',
            data: { content: 'test data' },
            priority: 'high',
        };
        const mockModelConfig = {
            providerType: 'openai',
            modelId: 'gpt-4',
            parameters: {},
        };
        const mockProviders = [
            {
                id: 'provider-1',
                type: 'openai',
                status: 'healthy',
                load: 0.5,
            },
        ];
        const mockAnalysisResult = {
            id: 'result-1',
            result: { threat_detected: true, confidence: 0.95 },
            confidence: 0.95,
            metadata: { model: 'gpt-4' },
            timestamp: new Date(),
        };
        it('should orchestrate analysis successfully with cache miss', async () => {
            // Arrange
            mockCacheService.get.mockResolvedValue(null);
            mockModelSelectionService.selectOptimalModel.mockResolvedValue(mockModelConfig);
            mockLoadBalancerService.getAvailableProviders.mockResolvedValue(mockProviders);
            mockCircuitBreakerService.execute.mockResolvedValue(mockAnalysisResult);
            mockCacheService.set.mockResolvedValue(undefined);
            mockConfigService.get.mockReturnValue(3600); // Default cache TTL
            // Act
            const result = await service.orchestrateAnalysis(mockRequest);
            // Assert
            expect(result).toEqual(mockAnalysisResult);
            expect(mockCacheService.get).toHaveBeenCalledWith(expect.any(String));
            expect(mockModelSelectionService.selectOptimalModel).toHaveBeenCalledWith(mockRequest);
            expect(mockLoadBalancerService.getAvailableProviders).toHaveBeenCalledWith('openai');
            expect(mockCircuitBreakerService.execute).toHaveBeenCalled();
            expect(mockCacheService.set).toHaveBeenCalledWith(expect.any(String), mockAnalysisResult, 3600);
            expect(mockMetricsAdapter.recordAiOperation).toHaveBeenCalledWith('success', expect.any(Number), expect.any(String));
        });
        it('should return cached result when available', async () => {
            // Arrange
            mockCacheService.get.mockResolvedValue(mockAnalysisResult);
            // Act
            const result = await service.orchestrateAnalysis(mockRequest);
            // Assert
            expect(result).toEqual(mockAnalysisResult);
            expect(mockCacheService.get).toHaveBeenCalledWith(expect.any(String));
            expect(mockModelSelectionService.selectOptimalModel).not.toHaveBeenCalled();
            expect(mockMetricsAdapter.recordAiOperation).toHaveBeenCalledWith('cache_hit', expect.any(Number), expect.any(String));
        });
        it('should handle provider failures with fallback', async () => {
            // Arrange
            const multipleProviders = [
                { id: 'provider-1', type: 'openai', status: 'healthy', load: 0.5 },
                { id: 'provider-2', type: 'openai', status: 'healthy', load: 0.3 },
            ];
            mockCacheService.get.mockResolvedValue(null);
            mockModelSelectionService.selectOptimalModel.mockResolvedValue(mockModelConfig);
            mockLoadBalancerService.getAvailableProviders.mockResolvedValue(multipleProviders);
            // First provider fails, second succeeds
            mockCircuitBreakerService.execute
                .mockRejectedValueOnce(new Error('Provider 1 failed'))
                .mockResolvedValueOnce(mockAnalysisResult);
            // Act
            const result = await service.orchestrateAnalysis(mockRequest);
            // Assert
            expect(result).toEqual(mockAnalysisResult);
            expect(mockCircuitBreakerService.execute).toHaveBeenCalledTimes(2);
        });
        it('should throw error when all providers fail', async () => {
            // Arrange
            mockCacheService.get.mockResolvedValue(null);
            mockModelSelectionService.selectOptimalModel.mockResolvedValue(mockModelConfig);
            mockLoadBalancerService.getAvailableProviders.mockResolvedValue(mockProviders);
            mockCircuitBreakerService.execute.mockRejectedValue(new Error('All providers failed'));
            // Act & Assert
            await expect(service.orchestrateAnalysis(mockRequest)).rejects.toThrow('Analysis failed');
            expect(mockMetricsAdapter.recordAiOperation).toHaveBeenCalledWith('error', expect.any(Number), expect.any(String));
        });
        it('should handle configuration values correctly', async () => {
            // Arrange
            mockConfigService.get.mockReturnValue(7200); // 2 hours cache TTL
            mockCacheService.get.mockResolvedValue(null);
            mockModelSelectionService.selectOptimalModel.mockResolvedValue(mockModelConfig);
            mockLoadBalancerService.getAvailableProviders.mockResolvedValue(mockProviders);
            mockCircuitBreakerService.execute.mockResolvedValue(mockAnalysisResult);
            // Act
            await service.orchestrateAnalysis(mockRequest);
            // Assert
            expect(mockConfigService.get).toHaveBeenCalledWith('ai.cacheTtl', 3600);
            expect(mockCacheService.set).toHaveBeenCalledWith(expect.any(String), mockAnalysisResult, 7200);
        });
    });
    describe('orchestrateBatchAnalysis', () => {
        const mockRequests = [
            {
                id: 'batch-request-1',
                type: 'threat-analysis',
                data: { content: 'test data 1' },
            },
            {
                id: 'batch-request-2',
                type: 'threat-analysis',
                data: { content: 'test data 2' },
            },
        ];
        it('should process batch requests successfully', async () => {
            // Arrange
            mockConfigService.get.mockReturnValue(2); // Concurrency limit
            // Mock model selection for batch processing
            mockModelSelectionService.selectOptimalModel.mockResolvedValue({
                providerType: 'openai',
                modelId: 'gpt-4',
                parameters: {},
            });
            // Mock individual analysis calls
            jest.spyOn(service, 'orchestrateAnalysis')
                .mockResolvedValueOnce({
                id: 'result-1',
                result: { threat_detected: true },
                confidence: 0.9,
                metadata: {},
                timestamp: new Date(),
            })
                .mockResolvedValueOnce({
                id: 'result-2',
                result: { threat_detected: false },
                confidence: 0.8,
                metadata: {},
                timestamp: new Date(),
            });
            // Act
            const results = await service.orchestrateBatchAnalysis(mockRequests);
            // Assert
            expect(results).toHaveLength(2);
            expect(results[0].id).toBe('result-1');
            expect(results[1].id).toBe('result-2');
            expect(service.orchestrateAnalysis).toHaveBeenCalledTimes(2);
        });
        it('should handle batch processing errors gracefully', async () => {
            // Arrange
            // Mock model selection to fail, which should cause the batch to return empty results
            mockModelSelectionService.selectOptimalModel.mockRejectedValue(new Error('Model selection failed'));
            // Act
            const results = await service.orchestrateBatchAnalysis(mockRequests);
            // Assert
            expect(results).toHaveLength(0); // Should return empty array when all requests fail model selection
        });
    });
    describe('orchestrateRealTimePrediction', () => {
        const mockPredictionRequest = {
            modelId: 'model-1',
            input: { data: 'test input' },
            options: { timeout: 1000 },
        };
        const mockPredictionResult = {
            prediction: { category: 'safe', score: 0.95 },
            confidence: 0.95,
            metadata: { model: 'model-1' },
        };
        it('should orchestrate real-time prediction successfully', async () => {
            // Arrange
            const mockModelConfig = {
                modelId: 'model-1',
                providerType: 'openai',
                parameters: {},
            };
            mockModelSelectionService.selectRealTimeModel.mockResolvedValue(mockModelConfig);
            mockPredictionEngine.predict.mockResolvedValue(mockPredictionResult);
            mockConfigService.get.mockReturnValue(1000);
            // Act
            const result = await service.orchestrateRealTimePrediction(mockPredictionRequest);
            // Assert
            expect(result).toEqual(mockPredictionResult);
            expect(mockModelSelectionService.selectRealTimeModel).toHaveBeenCalledWith(mockPredictionRequest);
            expect(mockPredictionEngine.predict).toHaveBeenCalledWith(mockPredictionRequest, expect.objectContaining({
                modelConfig: mockModelConfig,
                timeout: 1000,
                priority: 'high',
            }));
        });
        it('should handle real-time prediction failures', async () => {
            // Arrange
            mockModelSelectionService.selectRealTimeModel.mockRejectedValue(new Error('Model selection failed'));
            // Act & Assert
            await expect(service.orchestrateRealTimePrediction(mockPredictionRequest))
                .rejects.toThrow('Prediction failed');
        });
    });
    describe('checkHealth', () => {
        it('should return healthy status when all components are healthy', async () => {
            // Arrange
            const mockProviderHealth = { 'provider-1': { healthy: true } };
            const mockCacheHealth = { status: 'healthy' };
            const mockCircuitBreakerStatus = { 'cb-1': { state: 'closed' } };
            mockLoadBalancerService.checkProviderHealth.mockResolvedValue(mockProviderHealth);
            mockCacheService.checkHealth.mockResolvedValue(mockCacheHealth);
            mockCircuitBreakerService.getStatus.mockReturnValue(mockCircuitBreakerStatus);
            // Act
            const health = await service.checkHealth();
            // Assert
            expect(health.status).toBe('healthy');
            expect(health.providers).toEqual(mockProviderHealth);
            expect(health.cache).toEqual(mockCacheHealth);
            expect(health.circuitBreakers).toEqual(mockCircuitBreakerStatus);
            expect(health.timestamp).toBeInstanceOf(Date);
        });
        it('should return unhealthy status when health check fails', async () => {
            // Arrange
            mockLoadBalancerService.checkProviderHealth.mockRejectedValue(new Error('Health check failed'));
            // Act
            const health = await service.checkHealth();
            // Assert
            expect(health.status).toBe('unhealthy');
            expect(health.error).toBe('Health check failed');
            expect(health.timestamp).toBeInstanceOf(Date);
        });
    });
    describe('private helper methods', () => {
        it('should generate unique request IDs', () => {
            // Use reflection to access private method for testing
            const generateRequestId = service.generateRequestId.bind(service);
            const id1 = generateRequestId();
            const id2 = generateRequestId();
            expect(id1).toMatch(/^ai-req-\d+-[a-z0-9]+$/);
            expect(id2).toMatch(/^ai-req-\d+-[a-z0-9]+$/);
            expect(id1).not.toBe(id2);
        });
        it('should generate cache keys consistently', () => {
            const generateCacheKey = service.generateCacheKey.bind(service);
            const request1 = { id: '1', type: 'test', data: { value: 'same' } };
            const request2 = { id: '1', type: 'test', data: { value: 'same' } }; // Same ID for same content
            const request3 = { id: '1', type: 'test', data: { value: 'different' } };
            const key1 = generateCacheKey(request1);
            const key2 = generateCacheKey(request2);
            const key3 = generateCacheKey(request3);
            expect(key1).toBe(key2); // Same content should generate same key
            expect(key1).not.toBe(key3); // Different content should generate different key
        });
    });
    describe('error handling', () => {
        it('should handle orchestration errors with proper error types', async () => {
            // Arrange
            const mockRequest = {
                id: 'error-request',
                type: 'test',
                data: {},
            };
            mockCacheService.get.mockRejectedValue(new Error('Cache error'));
            // Act & Assert
            await expect(service.orchestrateAnalysis(mockRequest)).rejects.toThrow();
            expect(mockMetricsAdapter.recordAiOperation).toHaveBeenCalledWith('error', expect.any(Number), expect.any(String));
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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