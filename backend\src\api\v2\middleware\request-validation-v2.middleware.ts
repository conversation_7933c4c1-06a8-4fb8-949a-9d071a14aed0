import {
  Injectable,
  NestMiddleware,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { ConfigService } from '@nestjs/config';

/**
 * Enhanced request validation middleware for API v2
 * Provides advanced validation with custom error formatting and logging
 */
@Injectable()
export class RequestValidationV2Middleware implements NestMiddleware {
  private readonly logger = new Logger(RequestValidationV2Middleware.name);

  constructor(private readonly configService: ConfigService) {}

  async use(req: Request, res: Response, next: NextFunction) {
    const startTime = Date.now();
    
    try {
      // Enhanced request validation for v2 endpoints
      await this.validateRequest(req);
      
      // Add v2-specific headers
      this.addV2Headers(res);
      
      // Log request for v2 analytics
      this.logV2Request(req, startTime);
      
      next();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error(`V2 Request validation failed: ${errorMessage}`, {
        url: req.url,
        method: req.method,
        body: req.body,
        query: req.query,
        error: errorStack,
      });

      throw new BadRequestException({
        error: 'V2_VALIDATION_ERROR',
        message: errorMessage,
        timestamp: new Date().toISOString(),
        path: req.url,
        version: '2.0',
        details: (error as any)?.constraints || {},
      });
    }
  }

  private async validateRequest(req: Request): Promise<void> {
    // Enhanced validation for v2 API
    const maxBodySize = this.configService.get<number>('API_V2_MAX_BODY_SIZE', 10485760); // 10MB
    const maxQueryParams = this.configService.get<number>('API_V2_MAX_QUERY_PARAMS', 100);
    
    // Check body size
    if (req.body && JSON.stringify(req.body).length > maxBodySize) {
      throw new Error('Request body exceeds maximum allowed size for API v2');
    }
    
    // Check query parameter count
    if (Object.keys(req.query).length > maxQueryParams) {
      throw new Error('Too many query parameters for API v2');
    }
    
    // Validate required v2 headers
    this.validateV2Headers(req);
    
    // Enhanced content type validation
    this.validateContentType(req);
  }

  private validateV2Headers(req: Request): void {
    const requiredHeaders = ['user-agent', 'accept'];
    const recommendedHeaders = ['x-request-id', 'x-client-version'];
    
    for (const header of requiredHeaders) {
      if (!req.headers[header]) {
        throw new Error(`Missing required header: ${header}`);
      }
    }
    
    // Log missing recommended headers
    for (const header of recommendedHeaders) {
      if (!req.headers[header]) {
        this.logger.warn(`Missing recommended header: ${header}`, {
          url: req.url,
          method: req.method,
        });
      }
    }
  }

  private validateContentType(req: Request): void {
    if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
      const contentType = req.headers['content-type'];
      const allowedTypes = [
        'application/json',
        'application/x-www-form-urlencoded',
        'multipart/form-data',
      ];
      
      if (!contentType || !allowedTypes.some(type => contentType.includes(type))) {
        throw new Error(`Unsupported content type: ${contentType}`);
      }
    }
  }

  private addV2Headers(res: Response): void {
    res.setHeader('X-API-Version', '2.0');
    res.setHeader('X-Enhanced-Features', 'analytics,correlation,streaming,search');
    res.setHeader('X-Rate-Limit-Version', 'v2');
    res.setHeader('X-Response-Format', 'enhanced');
  }

  private logV2Request(req: Request, startTime: number): void {
    const processingTime = Date.now() - startTime;
    
    this.logger.log(`V2 Request processed`, {
      method: req.method,
      url: req.url,
      userAgent: req.headers['user-agent'],
      clientVersion: req.headers['x-client-version'],
      requestId: req.headers['x-request-id'],
      processingTime,
      bodySize: req.body ? JSON.stringify(req.body).length : 0,
      queryParamCount: Object.keys(req.query).length,
    });
  }
}