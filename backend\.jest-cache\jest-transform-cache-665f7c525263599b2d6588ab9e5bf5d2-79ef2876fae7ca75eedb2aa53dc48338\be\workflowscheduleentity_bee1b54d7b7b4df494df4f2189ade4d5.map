{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\entities\\workflow-schedule.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCASiB;AACjB,iFAAsE;AAEtE;;;;;GAKG;AAQI,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAqJ3B,sBAAsB;IACtB,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,WAAW,KAAK,MAAM,CAAC;IACrC,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,WAAW,KAAK,OAAO,CAAC;IACtC,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,WAAW,KAAK,SAAS,CAAC;IACxC,CAAC;IAED,IAAI,WAAW;QACb,IAAI,IAAI,CAAC,cAAc,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QACxC,OAAO,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,GAAG,CAAC;IACzD,CAAC;IAED,IAAI,WAAW;QACb,IAAI,IAAI,CAAC,cAAc,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QACxC,OAAO,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,GAAG,CAAC;IACzD,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC;IAC/D,CAAC;IAED,IAAI,sBAAsB;QACxB,IAAI,CAAC,IAAI,CAAC,aAAa;YAAE,OAAO,CAAC,CAAC,CAAC;QACnC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC;QACxE,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACrD,CAAC;IAED,IAAI,SAAS;QACX,yEAAyE;QACzE,MAAM,sBAAsB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,WAAW;QAC/D,MAAM,gBAAgB,GAAG,IAAI,CAAC,WAAW;YACvC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,GAAG,sBAAsB,CAAC;QAErE,OAAO,IAAI,CAAC,WAAW,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO;YACL,eAAe,EAAE,IAAI,CAAC,cAAc;YACpC,oBAAoB,EAAE,IAAI,CAAC,YAAY;YACvC,gBAAgB,EAAE,IAAI,CAAC,YAAY;YACnC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,sBAAsB,EAAE,IAAI,CAAC,sBAAsB;YACnD,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,MAAM,MAAM,GAAwB;YAClC,IAAI,EAAE,IAAI,CAAC,WAAW;SACvB,CAAC;QAEF,QAAQ,IAAI,CAAC,WAAW,EAAE,CAAC;YACzB,KAAK,MAAM;gBACT,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;gBAC5C,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAChC,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;gBAC1C,MAAM;YACR,KAAK,OAAO;gBACV,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;gBAClC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;gBACxC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;gBACpC,MAAM;YACR,KAAK,SAAS;gBACZ,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;gBACtC,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;gBAC1C,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC;gBACnD,MAAM;QACV,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO,KAAK,CAAC;QACjC,IAAI,IAAI,CAAC,WAAW,KAAK,MAAM;YAAE,OAAO,KAAK,CAAC;QAC9C,IAAI,CAAC,IAAI,CAAC,aAAa;YAAE,OAAO,KAAK,CAAC;QAEtC,OAAO,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,aAAa,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,SAAiB,EAAE,SAAc;QAC5C,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO,KAAK,CAAC;QACjC,IAAI,IAAI,CAAC,WAAW,KAAK,OAAO;YAAE,OAAO,KAAK,CAAC;QAC/C,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS;YAAE,OAAO,KAAK,CAAC;QAE/C,sBAAsB;QACtB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC7D,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;gBACvD,IAAI,UAAU,KAAK,KAAK;oBAAE,OAAO,KAAK,CAAC;YACzC,CAAC;QACH,CAAC;QAED,mBAAmB;QACnB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClD,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBACxC,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;gBACnE,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,SAAS,CAAC,QAAQ,EAAE,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC7E,OAAO,KAAK,CAAC;gBACf,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,IAAY,EAAE,MAAc;QACzC,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO,KAAK,CAAC;QACjC,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS;YAAE,OAAO,KAAK,CAAC;QACjD,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI;YAAE,OAAO,KAAK,CAAC;QAC5C,IAAI,IAAI,CAAC,aAAa,KAAK,MAAM;YAAE,OAAO,KAAK,CAAC;QAEhD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,OAAgB,EAAE,KAAc;QAC9C,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,aAAa,GAAG,IAAI,IAAI,EAAE,CAAC;QAEhC,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;YAC9B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACxB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;YAC9B,IAAI,CAAC,SAAS,GAAG,KAAK,IAAI,eAAe,CAAC;QAC5C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,IAAI,IAAI,CAAC,WAAW,KAAK,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACxD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,kEAAkE;QAClE,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,6BAA6B;IACvE,CAAC;IAED;;OAEG;IACH,uBAAuB;QACrB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,eAAe,CAAC;QACnD,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;YAC5C,OAAO,IAAI,CAAC,CAAC,wBAAwB;QACvC,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC;QAE3D,yEAAyE;QACzE,MAAM,WAAW,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC;QACnC,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEnD,OAAO,WAAW,IAAI,SAAS,IAAI,WAAW,IAAI,OAAO,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,EAAE,aAAa,CAAC;QACxD,IAAI,CAAC,aAAa;YAAE,OAAO,KAAK,CAAC;QAEjC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QAEvB,iBAAiB;QACjB,IAAI,aAAa,CAAC,YAAY,EAAE,CAAC;YAC/B,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;YAC/B,IAAI,SAAS,KAAK,CAAC,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC,CAAC,qBAAqB;gBAC7D,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,uBAAuB;QACvB,IAAI,aAAa,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,EAAE,CAAC;YACvE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,8BAA8B;QAC9B,IAAI,aAAa,CAAC,YAAY,EAAE,CAAC;YAC/B,yDAAyD;YACzD,8CAA8C;YAC9C,MAAM,KAAK,GAAG,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;YACjC,MAAM,GAAG,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC;YAE1B,4BAA4B;YAC5B,IAAI,CAAC,KAAK,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE,IAAI,GAAG,KAAK,EAAE,CAAC,EAAE,CAAC;gBAC/D,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,eAAe;QACb,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAI,MAAM,GAAG,SAAS,CAAC;QAEvB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,GAAG,UAAU,CAAC;YACpB,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,GAAG,SAAS,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACrC,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,GAAG,EAAE,EAAE,CAAC;YAC1B,MAAM,GAAG,SAAS,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,qBAAqB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,GAAG,EAAE,EAAE,CAAC;YAC1B,MAAM,GAAG,UAAU,CAAC;QACtB,CAAC;QAED,MAAM,sBAAsB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,WAAW;QAC/D,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,GAAG,sBAAsB,EAAE,CAAC;YAC3F,MAAM,GAAG,SAAS,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAC1C,CAAC;QAED,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,GAAQ,EAAE,IAAY;QAC3C,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;IACvE,CAAC;IAEO,iBAAiB,CAAC,KAAU,EAAE,QAAgB,EAAE,QAAa;QACnE,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,IAAI,CAAC,CAAC,OAAO,KAAK,IAAI,QAAQ,CAAC;YACpC,KAAK,KAAK,CAAC,CAAC,OAAO,KAAK,IAAI,QAAQ,CAAC;YACrC,KAAK,IAAI,CAAC,CAAC,OAAO,KAAK,GAAG,QAAQ,CAAC;YACnC,KAAK,KAAK,CAAC,CAAC,OAAO,KAAK,IAAI,QAAQ,CAAC;YACrC,KAAK,IAAI,CAAC,CAAC,OAAO,KAAK,GAAG,QAAQ,CAAC;YACnC,KAAK,KAAK,CAAC,CAAC,OAAO,KAAK,IAAI,QAAQ,CAAC;YACrC,KAAK,IAAI,CAAC,CAAC,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACtE,KAAK,UAAU,CAAC,CAAC,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;YACjE,KAAK,OAAO,CAAC,CAAC,OAAO,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YAC9D,OAAO,CAAC,CAAC,OAAO,KAAK,CAAC;QACxB,CAAC;IACH,CAAC;CACF,CAAA;AA1cY,4CAAgB;AAE3B;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;4CACpB;AAIX;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;IAC/B,IAAA,eAAK,GAAE;;oDACW;AAInB;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC;IAChC,IAAA,eAAK,GAAE;;qDAC6C;AAIrD;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACnD,IAAA,eAAK,GAAE;;wDACe;AAGvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;kDAC5B;AAIjB;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC9C,IAAA,eAAK,GAAE;;mDACU;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACnD,MAAM,oBAAN,MAAM;sDAAc;AAGlC;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qDAC7B;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;;uDACO;AAG3D;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,wBAAwB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;+DAQxE;AAGF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDAClD,KAAK,oBAAL,KAAK;oDAKd;AAIH;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC5C,IAAA,eAAK,GAAE;;kDACU;AAIlB;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IACrE,IAAA,eAAK,GAAE;kDACO,IAAI,oBAAJ,IAAI;uDAAC;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACvD,IAAI,oBAAJ,IAAI;uDAAC;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;wDACzB;AAGvB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;sDACzB;AAGrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;sDACzB;AAGrB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACvD,IAAI,oBAAJ,IAAI;qDAAC;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;kDACvD,IAAI,oBAAJ,IAAI;qDAAC;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDAC3C;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uDA6B/D;AAGF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;kDAoB1D;AAGF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;;mDACb;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;;mDACb;AAGlB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;mDAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;kDAC9B,IAAI,oBAAJ,IAAI;mDAAC;AAKhB;IAFC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,mDAAoB,EAAE,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;IACrE,IAAA,oBAAU,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;kDAC1B,mDAAoB,oBAApB,mDAAoB;kDAAC;2BAnJpB,gBAAgB;IAP5B,IAAA,gBAAM,EAAC,oBAAoB,CAAC;IAC5B,IAAA,eAAK,EAAC,CAAC,YAAY,CAAC,CAAC;IACrB,IAAA,eAAK,EAAC,CAAC,aAAa,CAAC,CAAC;IACtB,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,eAAe,CAAC,CAAC;IACxB,IAAA,eAAK,EAAC,CAAC,gBAAgB,CAAC,CAAC;IACzB,IAAA,eAAK,EAAC,CAAC,WAAW,CAAC,CAAC;GACR,gBAAgB,CA0c5B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\entities\\workflow-schedule.entity.ts"], "sourcesContent": ["import {\r\n  <PERSON>tity,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  ManyToOne,\r\n  JoinColumn,\r\n} from 'typeorm';\r\nimport { NotificationWorkflow } from './notification-workflow.entity';\r\n\r\n/**\r\n * Workflow Schedule Entity\r\n * \r\n * Represents scheduled triggers for notification workflows with\r\n * comprehensive scheduling configuration and execution tracking.\r\n */\r\n@Entity('workflow_schedules')\r\n@Index(['workflowId'])\r\n@Index(['triggerType'])\r\n@Index(['isActive'])\r\n@Index(['nextExecution'])\r\n@Index(['cronExpression'])\r\n@Index(['eventType'])\r\nexport class WorkflowSchedule {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  @Column({ name: 'workflow_id' })\r\n  @Index()\r\n  workflowId: string;\r\n\r\n  @Column({ name: 'trigger_type' })\r\n  @Index()\r\n  triggerType: 'cron' | 'event' | 'webhook' | 'manual';\r\n\r\n  @Column({ name: 'cron_expression', nullable: true })\r\n  @Index()\r\n  cronExpression: string;\r\n\r\n  @Column({ name: 'timezone', default: 'UTC' })\r\n  timezone: string;\r\n\r\n  @Column({ name: 'event_type', nullable: true })\r\n  @Index()\r\n  eventType: string;\r\n\r\n  @Column({ name: 'event_pattern', type: 'jsonb', nullable: true })\r\n  eventPattern: Record<string, any>;\r\n\r\n  @Column({ name: 'webhook_path', nullable: true })\r\n  webhookPath: string;\r\n\r\n  @Column({ name: 'webhook_method', default: 'POST' })\r\n  webhookMethod: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';\r\n\r\n  @Column({ name: 'webhook_authentication', type: 'jsonb', nullable: true })\r\n  webhookAuthentication: {\r\n    type?: 'none' | 'basic' | 'bearer' | 'api_key' | 'custom';\r\n    username?: string;\r\n    password?: string;\r\n    token?: string;\r\n    apiKey?: string;\r\n    customHeaders?: Record<string, string>;\r\n  };\r\n\r\n  @Column({ name: 'conditions', type: 'jsonb', nullable: true })\r\n  conditions: Array<{\r\n    field: string;\r\n    operator: string;\r\n    value: any;\r\n    description?: string;\r\n  }>;\r\n\r\n  @Column({ name: 'is_active', default: true })\r\n  @Index()\r\n  isActive: boolean;\r\n\r\n  @Column({ name: 'next_execution', type: 'timestamp', nullable: true })\r\n  @Index()\r\n  nextExecution: Date;\r\n\r\n  @Column({ name: 'last_execution', type: 'timestamp', nullable: true })\r\n  lastExecution: Date;\r\n\r\n  @Column({ name: 'execution_count', default: 0 })\r\n  executionCount: number;\r\n\r\n  @Column({ name: 'success_count', default: 0 })\r\n  successCount: number;\r\n\r\n  @Column({ name: 'failure_count', default: 0 })\r\n  failureCount: number;\r\n\r\n  @Column({ name: 'last_success', type: 'timestamp', nullable: true })\r\n  lastSuccess: Date;\r\n\r\n  @Column({ name: 'last_failure', type: 'timestamp', nullable: true })\r\n  lastFailure: Date;\r\n\r\n  @Column({ name: 'last_error', type: 'text', nullable: true })\r\n  lastError: string;\r\n\r\n  @Column({ name: 'configuration', type: 'jsonb', nullable: true })\r\n  configuration: {\r\n    maxExecutions?: number;\r\n    executionWindow?: {\r\n      start?: string;\r\n      end?: string;\r\n      timezone?: string;\r\n    };\r\n    retryPolicy?: {\r\n      enabled?: boolean;\r\n      maxRetries?: number;\r\n      backoffMultiplier?: number;\r\n      maxBackoffTime?: number;\r\n    };\r\n    notifications?: {\r\n      onSuccess?: string[];\r\n      onFailure?: string[];\r\n      onMissed?: string[];\r\n    };\r\n    concurrency?: {\r\n      maxConcurrent?: number;\r\n      queueOverflow?: 'reject' | 'queue' | 'replace';\r\n    };\r\n    businessRules?: {\r\n      skipWeekends?: boolean;\r\n      skipHolidays?: boolean;\r\n      businessHoursOnly?: boolean;\r\n      holidayCalendar?: string;\r\n    };\r\n  };\r\n\r\n  @Column({ name: 'metadata', type: 'jsonb', nullable: true })\r\n  metadata: {\r\n    description?: string;\r\n    tags?: string[];\r\n    owner?: string;\r\n    team?: string;\r\n    priority?: 'low' | 'medium' | 'high' | 'critical';\r\n    environment?: string;\r\n    region?: string;\r\n    dependencies?: string[];\r\n    monitoring?: {\r\n      alertOnFailure?: boolean;\r\n      alertOnMissed?: boolean;\r\n      healthCheckUrl?: string;\r\n    };\r\n    documentation?: {\r\n      purpose?: string;\r\n      requirements?: string[];\r\n      troubleshooting?: string;\r\n    };\r\n  };\r\n\r\n  @Column({ name: 'created_by' })\r\n  createdBy: string;\r\n\r\n  @Column({ name: 'updated_by' })\r\n  updatedBy: string;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  updatedAt: Date;\r\n\r\n  // Relations\r\n  @ManyToOne(() => NotificationWorkflow, workflow => workflow.schedules)\r\n  @JoinColumn({ name: 'workflow_id' })\r\n  workflow: NotificationWorkflow;\r\n\r\n  // Computed properties\r\n  get isCronSchedule(): boolean {\r\n    return this.triggerType === 'cron';\r\n  }\r\n\r\n  get isEventTrigger(): boolean {\r\n    return this.triggerType === 'event';\r\n  }\r\n\r\n  get isWebhookTrigger(): boolean {\r\n    return this.triggerType === 'webhook';\r\n  }\r\n\r\n  get successRate(): number {\r\n    if (this.executionCount === 0) return 0;\r\n    return (this.successCount / this.executionCount) * 100;\r\n  }\r\n\r\n  get failureRate(): number {\r\n    if (this.executionCount === 0) return 0;\r\n    return (this.failureCount / this.executionCount) * 100;\r\n  }\r\n\r\n  get isOverdue(): boolean {\r\n    return this.nextExecution && new Date() > this.nextExecution;\r\n  }\r\n\r\n  get daysSinceLastExecution(): number {\r\n    if (!this.lastExecution) return -1;\r\n    const now = new Date();\r\n    const diffTime = Math.abs(now.getTime() - this.lastExecution.getTime());\r\n    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));\r\n  }\r\n\r\n  get isHealthy(): boolean {\r\n    // Consider schedule healthy if success rate > 90% and no recent failures\r\n    const recentFailureThreshold = 24 * 60 * 60 * 1000; // 24 hours\r\n    const hasRecentFailure = this.lastFailure && \r\n      (Date.now() - this.lastFailure.getTime()) < recentFailureThreshold;\r\n    \r\n    return this.successRate >= 90 && !hasRecentFailure;\r\n  }\r\n\r\n  /**\r\n   * Get schedule summary\r\n   */\r\n  getScheduleSummary(): Record<string, any> {\r\n    return {\r\n      id: this.id,\r\n      workflowId: this.workflowId,\r\n      triggerType: this.triggerType,\r\n      cronExpression: this.cronExpression,\r\n      eventType: this.eventType,\r\n      webhookPath: this.webhookPath,\r\n      isActive: this.isActive,\r\n      nextExecution: this.nextExecution,\r\n      lastExecution: this.lastExecution,\r\n      executionCount: this.executionCount,\r\n      successRate: this.successRate,\r\n      failureRate: this.failureRate,\r\n      isHealthy: this.isHealthy,\r\n      isOverdue: this.isOverdue,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get execution statistics\r\n   */\r\n  getExecutionStats(): Record<string, any> {\r\n    return {\r\n      totalExecutions: this.executionCount,\r\n      successfulExecutions: this.successCount,\r\n      failedExecutions: this.failureCount,\r\n      successRate: this.successRate,\r\n      failureRate: this.failureRate,\r\n      lastExecution: this.lastExecution,\r\n      lastSuccess: this.lastSuccess,\r\n      lastFailure: this.lastFailure,\r\n      daysSinceLastExecution: this.daysSinceLastExecution,\r\n      isHealthy: this.isHealthy,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get trigger configuration\r\n   */\r\n  getTriggerConfig(): Record<string, any> {\r\n    const config: Record<string, any> = {\r\n      type: this.triggerType,\r\n    };\r\n\r\n    switch (this.triggerType) {\r\n      case 'cron':\r\n        config.cronExpression = this.cronExpression;\r\n        config.timezone = this.timezone;\r\n        config.nextExecution = this.nextExecution;\r\n        break;\r\n      case 'event':\r\n        config.eventType = this.eventType;\r\n        config.eventPattern = this.eventPattern;\r\n        config.conditions = this.conditions;\r\n        break;\r\n      case 'webhook':\r\n        config.webhookPath = this.webhookPath;\r\n        config.webhookMethod = this.webhookMethod;\r\n        config.authentication = this.webhookAuthentication;\r\n        break;\r\n    }\r\n\r\n    return config;\r\n  }\r\n\r\n  /**\r\n   * Check if schedule should execute now\r\n   */\r\n  shouldExecuteNow(): boolean {\r\n    if (!this.isActive) return false;\r\n    if (this.triggerType !== 'cron') return false;\r\n    if (!this.nextExecution) return false;\r\n    \r\n    return new Date() >= this.nextExecution;\r\n  }\r\n\r\n  /**\r\n   * Check if event matches this schedule\r\n   */\r\n  matchesEvent(eventType: string, eventData: any): boolean {\r\n    if (!this.isActive) return false;\r\n    if (this.triggerType !== 'event') return false;\r\n    if (this.eventType !== eventType) return false;\r\n\r\n    // Check event pattern\r\n    if (this.eventPattern) {\r\n      for (const [key, value] of Object.entries(this.eventPattern)) {\r\n        const eventValue = this.getNestedValue(eventData, key);\r\n        if (eventValue !== value) return false;\r\n      }\r\n    }\r\n\r\n    // Check conditions\r\n    if (this.conditions && this.conditions.length > 0) {\r\n      for (const condition of this.conditions) {\r\n        const fieldValue = this.getNestedValue(eventData, condition.field);\r\n        if (!this.evaluateCondition(fieldValue, condition.operator, condition.value)) {\r\n          return false;\r\n        }\r\n      }\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Check if webhook request matches this schedule\r\n   */\r\n  matchesWebhook(path: string, method: string): boolean {\r\n    if (!this.isActive) return false;\r\n    if (this.triggerType !== 'webhook') return false;\r\n    if (this.webhookPath !== path) return false;\r\n    if (this.webhookMethod !== method) return false;\r\n\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Record execution attempt\r\n   */\r\n  recordExecution(success: boolean, error?: string): void {\r\n    this.executionCount++;\r\n    this.lastExecution = new Date();\r\n\r\n    if (success) {\r\n      this.successCount++;\r\n      this.lastSuccess = new Date();\r\n      this.lastError = null;\r\n    } else {\r\n      this.failureCount++;\r\n      this.lastFailure = new Date();\r\n      this.lastError = error || 'Unknown error';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculate next execution time for cron schedules\r\n   */\r\n  calculateNextExecution(): Date | null {\r\n    if (this.triggerType !== 'cron' || !this.cronExpression) {\r\n      return null;\r\n    }\r\n\r\n    // Simple implementation - in production, use a proper cron parser\r\n    const now = new Date();\r\n    return new Date(now.getTime() + 60000); // Next minute for simplicity\r\n  }\r\n\r\n  /**\r\n   * Check if schedule is within execution window\r\n   */\r\n  isWithinExecutionWindow(): boolean {\r\n    const window = this.configuration?.executionWindow;\r\n    if (!window || !window.start || !window.end) {\r\n      return true; // No window restriction\r\n    }\r\n\r\n    const now = new Date();\r\n    const timezone = window.timezone || this.timezone || 'UTC';\r\n    \r\n    // Simple time window check - in production, use proper timezone handling\r\n    const currentHour = now.getHours();\r\n    const startHour = parseInt(window.start.split(':')[0]);\r\n    const endHour = parseInt(window.end.split(':')[0]);\r\n\r\n    return currentHour >= startHour && currentHour <= endHour;\r\n  }\r\n\r\n  /**\r\n   * Check if schedule should skip due to business rules\r\n   */\r\n  shouldSkipExecution(): boolean {\r\n    const businessRules = this.configuration?.businessRules;\r\n    if (!businessRules) return false;\r\n\r\n    const now = new Date();\r\n\r\n    // Check weekends\r\n    if (businessRules.skipWeekends) {\r\n      const dayOfWeek = now.getDay();\r\n      if (dayOfWeek === 0 || dayOfWeek === 6) { // Sunday or Saturday\r\n        return true;\r\n      }\r\n    }\r\n\r\n    // Check business hours\r\n    if (businessRules.businessHoursOnly && !this.isWithinExecutionWindow()) {\r\n      return true;\r\n    }\r\n\r\n    // Check holidays (simplified)\r\n    if (businessRules.skipHolidays) {\r\n      // In production, integrate with holiday calendar service\r\n      // For now, just check if it's a major holiday\r\n      const month = now.getMonth() + 1;\r\n      const day = now.getDate();\r\n      \r\n      // New Year's Day, Christmas\r\n      if ((month === 1 && day === 1) || (month === 12 && day === 25)) {\r\n        return true;\r\n      }\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Get schedule health status\r\n   */\r\n  getHealthStatus(): { status: string; issues: string[] } {\r\n    const issues: string[] = [];\r\n    let status = 'healthy';\r\n\r\n    if (!this.isActive) {\r\n      status = 'inactive';\r\n      issues.push('Schedule is inactive');\r\n    }\r\n\r\n    if (this.isOverdue) {\r\n      status = 'warning';\r\n      issues.push('Schedule is overdue');\r\n    }\r\n\r\n    if (this.successRate < 90) {\r\n      status = 'warning';\r\n      issues.push(`Low success rate: ${this.successRate.toFixed(1)}%`);\r\n    }\r\n\r\n    if (this.successRate < 50) {\r\n      status = 'critical';\r\n    }\r\n\r\n    const recentFailureThreshold = 24 * 60 * 60 * 1000; // 24 hours\r\n    if (this.lastFailure && (Date.now() - this.lastFailure.getTime()) < recentFailureThreshold) {\r\n      status = 'warning';\r\n      issues.push('Recent execution failure');\r\n    }\r\n\r\n    return { status, issues };\r\n  }\r\n\r\n  /**\r\n   * Private helper methods\r\n   */\r\n  private getNestedValue(obj: any, path: string): any {\r\n    return path.split('.').reduce((current, key) => current?.[key], obj);\r\n  }\r\n\r\n  private evaluateCondition(value: any, operator: string, expected: any): boolean {\r\n    switch (operator) {\r\n      case 'eq': return value == expected;\r\n      case 'neq': return value != expected;\r\n      case 'gt': return value > expected;\r\n      case 'gte': return value >= expected;\r\n      case 'lt': return value < expected;\r\n      case 'lte': return value <= expected;\r\n      case 'in': return Array.isArray(expected) && expected.includes(value);\r\n      case 'contains': return String(value).includes(String(expected));\r\n      case 'regex': return new RegExp(expected).test(String(value));\r\n      default: return false;\r\n    }\r\n  }\r\n}\r\n"], "version": 3}