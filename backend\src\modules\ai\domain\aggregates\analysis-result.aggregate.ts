import { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';
import { AnalysisResult, AnalysisType, AnalysisStatus } from '../entities/analysis-result.entity';
import { AnalysisResultFactory, CreateAnalysisResultRequest } from '../factories/analysis-result.factory';

/**
 * Analysis Result Aggregate
 * 
 * Aggregate service that manages Analysis Result domain operations.
 * Provides high-level business operations and ensures consistency
 * across multiple analysis results and related entities.
 */

export interface AnalysisResultQuery {
  requestId?: string;
  modelId?: UniqueEntityId;
  analysisType?: AnalysisType;
  status?: AnalysisStatus;
  minConfidence?: number;
  maxConfidence?: number;
  tags?: string[];
  correlationId?: string;
  parentAnalysisId?: UniqueEntityId;
  hasChildren?: boolean;
  createdAfter?: Date;
  createdBefore?: Date;
  completedAfter?: Date;
  completedBefore?: Date;
}

export interface AnalysisResultStatistics {
  totalResults: number;
  statusDistribution: Record<AnalysisStatus, number>;
  typeDistribution: Record<AnalysisType, number>;
  averageConfidence: number;
  averageProcessingTime: number;
  successRate: number;
  averageQualityScore: number;
  retryableFailures: number;
  completionRate: number;
}

export interface AnalysisResultBatch {
  results: AnalysisResult[];
  totalCount: number;
  averageConfidence: number;
  successCount: number;
  failureCount: number;
  pendingCount: number;
}

export class AnalysisResultAggregate {
  private results: Map<string, AnalysisResult> = new Map();

  constructor(results: AnalysisResult[] = []) {
    results.forEach(result => {
      this.results.set(result.id.toString(), result);
    });
  }

  /**
   * Creates a new analysis result and adds it to the aggregate
   */
  public createAnalysisResult(request: CreateAnalysisResultRequest, id?: UniqueEntityId): AnalysisResult {
    // Check for duplicate request IDs
    const existingResult = this.findByRequestId(request.requestId);
    if (existingResult) {
      throw new Error(`Analysis result with request ID '${request.requestId}' already exists`);
    }

    const result = AnalysisResultFactory.create(request, id);
    this.results.set(result.id.toString(), result);
    
    return result;
  }

  /**
   * Adds an existing analysis result to the aggregate
   */
  public addAnalysisResult(result: AnalysisResult): void {
    if (this.results.has(result.id.toString())) {
      throw new Error(`Analysis result with ID '${result.id.toString()}' already exists in aggregate`);
    }

    this.results.set(result.id.toString(), result);
  }

  /**
   * Removes an analysis result from the aggregate
   */
  public removeAnalysisResult(resultId: UniqueEntityId): void {
    const result = this.results.get(resultId.toString());
    if (!result) {
      throw new Error(`Analysis result with ID '${resultId.toString()}' not found`);
    }

    // Cancel the analysis if it's still processing
    if (!result.isTerminal()) {
      result.cancel();
    }

    this.results.delete(resultId.toString());
  }

  /**
   * Gets an analysis result by ID
   */
  public getAnalysisResult(resultId: UniqueEntityId): AnalysisResult | undefined {
    return this.results.get(resultId.toString());
  }

  /**
   * Gets all analysis results
   */
  public getAllAnalysisResults(): AnalysisResult[] {
    return Array.from(this.results.values());
  }

  /**
   * Finds analysis results by query criteria
   */
  public findAnalysisResults(query: AnalysisResultQuery): AnalysisResult[] {
    return Array.from(this.results.values()).filter(result => {
      if (query.requestId && result.requestId !== query.requestId) {
        return false;
      }

      if (query.modelId && !result.modelId.equals(query.modelId)) {
        return false;
      }

      if (query.analysisType && result.analysisType !== query.analysisType) {
        return false;
      }

      if (query.status && result.status !== query.status) {
        return false;
      }

      if (query.minConfidence !== undefined && result.confidence < query.minConfidence) {
        return false;
      }

      if (query.maxConfidence !== undefined && result.confidence > query.maxConfidence) {
        return false;
      }

      if (query.tags && !query.tags.every(tag => result.hasTag(tag))) {
        return false;
      }

      if (query.correlationId && result.correlationId !== query.correlationId) {
        return false;
      }

      if (query.parentAnalysisId) {
        if (!result.parentAnalysisId || !result.parentAnalysisId.equals(query.parentAnalysisId)) {
          return false;
        }
      }

      if (query.hasChildren !== undefined) {
        const hasChildren = result.childAnalysisIds.length > 0;
        if (query.hasChildren !== hasChildren) {
          return false;
        }
      }

      if (query.createdAfter && result.createdAt < query.createdAfter) {
        return false;
      }

      if (query.createdBefore && result.createdAt > query.createdBefore) {
        return false;
      }

      if (query.completedAfter && (!result.completedAt || result.completedAt < query.completedAfter)) {
        return false;
      }

      if (query.completedBefore && (!result.completedAt || result.completedAt > query.completedBefore)) {
        return false;
      }

      return true;
    });
  }

  /**
   * Finds an analysis result by request ID
   */
  public findByRequestId(requestId: string): AnalysisResult | undefined {
    return Array.from(this.results.values()).find(result => result.requestId === requestId);
  }

  /**
   * Gets analysis results by correlation ID
   */
  public findByCorrelationId(correlationId: string): AnalysisResult[] {
    return Array.from(this.results.values()).filter(result => 
      result.correlationId === correlationId
    );
  }

  /**
   * Gets child analysis results for a parent
   */
  public getChildResults(parentId: UniqueEntityId): AnalysisResult[] {
    return Array.from(this.results.values()).filter(result => 
      result.parentAnalysisId && result.parentAnalysisId.equals(parentId)
    );
  }

  /**
   * Gets analysis results by model ID
   */
  public getResultsByModel(modelId: UniqueEntityId): AnalysisResult[] {
    return Array.from(this.results.values()).filter(result => 
      result.modelId.equals(modelId)
    );
  }

  /**
   * Gets pending analysis results
   */
  public getPendingResults(): AnalysisResult[] {
    return Array.from(this.results.values()).filter(result => 
      result.status === AnalysisStatus.PENDING
    );
  }

  /**
   * Gets processing analysis results
   */
  public getProcessingResults(): AnalysisResult[] {
    return Array.from(this.results.values()).filter(result => 
      result.status === AnalysisStatus.PROCESSING
    );
  }

  /**
   * Gets completed analysis results
   */
  public getCompletedResults(): AnalysisResult[] {
    return Array.from(this.results.values()).filter(result => 
      result.status === AnalysisStatus.COMPLETED
    );
  }

  /**
   * Gets failed analysis results
   */
  public getFailedResults(): AnalysisResult[] {
    return Array.from(this.results.values()).filter(result => 
      result.status === AnalysisStatus.FAILED
    );
  }

  /**
   * Gets retryable failed results
   */
  public getRetryableFailedResults(): AnalysisResult[] {
    return this.getFailedResults().filter(result => result.isRetryable());
  }

  /**
   * Gets high confidence results
   */
  public getHighConfidenceResults(threshold: number = 0.8): AnalysisResult[] {
    return Array.from(this.results.values()).filter(result => 
      result.hasHighConfidence(threshold)
    );
  }

  /**
   * Gets results with low quality scores
   */
  public getLowQualityResults(threshold: number = 0.5): AnalysisResult[] {
    return Array.from(this.results.values()).filter(result => 
      result.getQualityScore() < threshold
    );
  }

  /**
   * Cancels all pending and processing results
   */
  public cancelAllActiveResults(): AnalysisResult[] {
    const activeResults = Array.from(this.results.values()).filter(result => 
      !result.isTerminal()
    );

    activeResults.forEach(result => result.cancel());
    return activeResults;
  }

  /**
   * Cancels results by query criteria
   */
  public cancelResults(query: AnalysisResultQuery): AnalysisResult[] {
    const matchingResults = this.findAnalysisResults(query).filter(result => 
      !result.isTerminal()
    );

    matchingResults.forEach(result => result.cancel());
    return matchingResults;
  }

  /**
   * Gets aggregate statistics
   */
  public getStatistics(): AnalysisResultStatistics {
    const results = Array.from(this.results.values());
    
    if (results.length === 0) {
      return this.createEmptyStatistics();
    }

    const statusDistribution = results.reduce((acc, result) => {
      acc[result.status] = (acc[result.status] || 0) + 1;
      return acc;
    }, {} as Record<AnalysisStatus, number>);

    const typeDistribution = results.reduce((acc, result) => {
      acc[result.analysisType] = (acc[result.analysisType] || 0) + 1;
      return acc;
    }, {} as Record<AnalysisType, number>);

    const totalConfidence = results.reduce((sum, result) => sum + result.confidence, 0);
    const totalProcessingTime = results.reduce((sum, result) => sum + result.processingTime, 0);
    const totalQualityScore = results.reduce((sum, result) => sum + result.getQualityScore(), 0);

    const successfulResults = results.filter(result => result.isSuccessful());
    const completedResults = results.filter(result => result.isTerminal());
    const retryableFailures = results.filter(result => 
      result.status === AnalysisStatus.FAILED && result.isRetryable()
    );

    return {
      totalResults: results.length,
      statusDistribution,
      typeDistribution,
      averageConfidence: totalConfidence / results.length,
      averageProcessingTime: totalProcessingTime / results.length,
      successRate: successfulResults.length / results.length,
      averageQualityScore: totalQualityScore / results.length,
      retryableFailures: retryableFailures.length,
      completionRate: completedResults.length / results.length,
    };
  }

  /**
   * Gets statistics for a specific time period
   */
  public getStatisticsForPeriod(startDate: Date, endDate: Date): AnalysisResultStatistics {
    const periodResults = Array.from(this.results.values()).filter(result => 
      result.createdAt >= startDate && result.createdAt <= endDate
    );

    const tempAggregate = new AnalysisResultAggregate(periodResults);
    return tempAggregate.getStatistics();
  }

  /**
   * Creates a batch of analysis results
   */
  public createBatch(requests: CreateAnalysisResultRequest[]): AnalysisResultBatch {
    const results: AnalysisResult[] = [];
    
    for (const request of requests) {
      try {
        const result = this.createAnalysisResult(request);
        results.push(result);
      } catch (error) {
        // Log error but continue with other requests
        console.warn(`Failed to create analysis result for request ${request.requestId}:`, error);
      }
    }

    const successCount = results.filter(r => r.isSuccessful()).length;
    const failureCount = results.filter(r => r.status === AnalysisStatus.FAILED).length;
    const pendingCount = results.filter(r => r.status === AnalysisStatus.PENDING).length;
    const totalConfidence = results.reduce((sum, r) => sum + r.confidence, 0);

    return {
      results,
      totalCount: results.length,
      averageConfidence: results.length > 0 ? totalConfidence / results.length : 0,
      successCount,
      failureCount,
      pendingCount,
    };
  }

  /**
   * Groups results by analysis type
   */
  public groupByAnalysisType(): Map<AnalysisType, AnalysisResult[]> {
    const groups = new Map<AnalysisType, AnalysisResult[]>();
    
    Array.from(this.results.values()).forEach(result => {
      if (!groups.has(result.analysisType)) {
        groups.set(result.analysisType, []);
      }
      groups.get(result.analysisType)!.push(result);
    });
    
    return groups;
  }

  /**
   * Groups results by status
   */
  public groupByStatus(): Map<AnalysisStatus, AnalysisResult[]> {
    const groups = new Map<AnalysisStatus, AnalysisResult[]>();
    
    Array.from(this.results.values()).forEach(result => {
      if (!groups.has(result.status)) {
        groups.set(result.status, []);
      }
      groups.get(result.status)!.push(result);
    });
    
    return groups;
  }

  /**
   * Gets the most recent results
   */
  public getMostRecentResults(limit: number = 10): AnalysisResult[] {
    return Array.from(this.results.values())
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, limit);
  }

  /**
   * Gets results sorted by confidence (highest first)
   */
  public getResultsByConfidence(limit?: number): AnalysisResult[] {
    const sorted = Array.from(this.results.values())
      .sort((a, b) => b.confidence - a.confidence);
    
    return limit ? sorted.slice(0, limit) : sorted;
  }

  /**
   * Gets results sorted by quality score (highest first)
   */
  public getResultsByQuality(limit?: number): AnalysisResult[] {
    const sorted = Array.from(this.results.values())
      .sort((a, b) => b.getQualityScore() - a.getQualityScore());
    
    return limit ? sorted.slice(0, limit) : sorted;
  }

  private createEmptyStatistics(): AnalysisResultStatistics {
    return {
      totalResults: 0,
      statusDistribution: {} as Record<AnalysisStatus, number>,
      typeDistribution: {} as Record<AnalysisType, number>,
      averageConfidence: 0,
      averageProcessingTime: 0,
      successRate: 0,
      averageQualityScore: 0,
      retryableFailures: 0,
      completionRate: 0,
    };
  }
}