209da122b787fd1f154e695a5b562e30
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PredictionResult = void 0;
const base_value_object_1 = require("../../../../shared-kernel/value-objects/base-value-object");
const confidence_score_value_object_1 = require("./confidence-score.value-object");
class PredictionResult extends base_value_object_1.BaseValueObject {
    constructor(prediction, confidence, alternatives, metadata, timestamp) {
        super({
            prediction,
            confidence,
            alternatives: alternatives || [],
            metadata: metadata || {},
            timestamp: timestamp || new Date(),
        });
    }
    validate() {
        super.validate();
        if (this._value.prediction === undefined || this._value.prediction === null) {
            throw new Error('Prediction value cannot be null or undefined');
        }
        if (!(this._value.confidence instanceof confidence_score_value_object_1.ConfidenceScore)) {
            throw new Error('Confidence must be a ConfidenceScore instance');
        }
        if (!Array.isArray(this._value.alternatives)) {
            throw new Error('Alternatives must be an array');
        }
        if (!(this._value.timestamp instanceof Date)) {
            throw new Error('Timestamp must be a Date instance');
        }
        // Validate alternatives
        for (const alternative of this._value.alternatives) {
            if (!alternative.hasOwnProperty('value') || !alternative.hasOwnProperty('confidence')) {
                throw new Error('Each alternative must have value and confidence properties');
            }
            if (!(alternative.confidence instanceof confidence_score_value_object_1.ConfidenceScore)) {
                throw new Error('Alternative confidence must be a ConfidenceScore instance');
            }
        }
        // Validate that alternatives are sorted by confidence (highest first)
        for (let i = 0; i < this._value.alternatives.length - 1; i++) {
            if (this._value.alternatives[i].confidence.isLessThan(this._value.alternatives[i + 1].confidence)) {
                throw new Error('Alternatives must be sorted by confidence in descending order');
            }
        }
    }
    /**
     * Creates a simple prediction result with just a value and confidence
     */
    static simple(prediction, confidence) {
        return new PredictionResult(prediction, new confidence_score_value_object_1.ConfidenceScore(confidence));
    }
    /**
     * Creates a prediction result with alternatives
     */
    static withAlternatives(prediction, confidence, alternatives) {
        const sortedAlternatives = alternatives
            .map(alt => ({ value: alt.value, confidence: new confidence_score_value_object_1.ConfidenceScore(alt.confidence) }))
            .sort((a, b) => b.confidence.value - a.confidence.value);
        return new PredictionResult(prediction, new confidence_score_value_object_1.ConfidenceScore(confidence), sortedAlternatives);
    }
    /**
     * Creates a binary classification result
     */
    static binary(prediction, confidence, positiveLabel = 'positive', negativeLabel = 'negative') {
        const label = prediction ? positiveLabel : negativeLabel;
        const alternativeLabel = prediction ? negativeLabel : positiveLabel;
        const alternativeConfidence = 1 - confidence;
        return new PredictionResult(label, new confidence_score_value_object_1.ConfidenceScore(confidence), [{ value: alternativeLabel, confidence: new confidence_score_value_object_1.ConfidenceScore(alternativeConfidence) }]);
    }
    /**
     * Creates a multi-class classification result
     */
    static multiClass(predictions, metadata) {
        if (predictions.length === 0) {
            throw new Error('At least one prediction is required');
        }
        // Sort by confidence (highest first)
        const sorted = predictions
            .map(pred => ({ value: pred.value, confidence: new confidence_score_value_object_1.ConfidenceScore(pred.confidence) }))
            .sort((a, b) => b.confidence.value - a.confidence.value);
        const topPrediction = sorted[0];
        const alternatives = sorted.slice(1);
        return new PredictionResult(topPrediction.value, topPrediction.confidence, alternatives, metadata);
    }
    /**
     * Creates a regression result
     */
    static regression(value, confidence, range, metadata) {
        const resultMetadata = { ...metadata };
        if (range) {
            resultMetadata.range = range;
            resultMetadata.withinRange = value >= range.min && value <= range.max;
        }
        return new PredictionResult(value, new confidence_score_value_object_1.ConfidenceScore(confidence), [], resultMetadata);
    }
    // Getters
    get prediction() {
        return this._value.prediction;
    }
    get confidence() {
        return this._value.confidence;
    }
    get alternatives() {
        return [...this._value.alternatives];
    }
    get metadata() {
        return { ...this._value.metadata };
    }
    get timestamp() {
        return this._value.timestamp;
    }
    /**
     * Checks if the prediction has high confidence
     */
    hasHighConfidence(threshold = 0.8) {
        return this._value.confidence.meetsThreshold(threshold);
    }
    /**
     * Checks if the prediction has low confidence
     */
    hasLowConfidence(threshold = 0.3) {
        return this._value.confidence.value < threshold;
    }
    /**
     * Gets the number of alternatives
     */
    getAlternativeCount() {
        return this._value.alternatives.length;
    }
    /**
     * Checks if there are alternatives available
     */
    hasAlternatives() {
        return this._value.alternatives.length > 0;
    }
    /**
     * Gets the top N alternatives
     */
    getTopAlternatives(n) {
        return this._value.alternatives.slice(0, n);
    }
    /**
     * Gets alternatives above a confidence threshold
     */
    getAlternativesAboveThreshold(threshold) {
        return this._value.alternatives.filter(alt => alt.confidence.meetsThreshold(threshold));
    }
    /**
     * Gets the confidence gap between the prediction and the best alternative
     */
    getConfidenceGap() {
        if (this._value.alternatives.length === 0) {
            return this._value.confidence.value;
        }
        const bestAlternative = this._value.alternatives[0];
        return this._value.confidence.value - bestAlternative.confidence.value;
    }
    /**
     * Checks if the prediction is significantly better than alternatives
     */
    isSignificantlyBetter(threshold = 0.1) {
        return this.getConfidenceGap() >= threshold;
    }
    /**
     * Gets the entropy of the prediction distribution
     */
    getEntropy() {
        const allPredictions = [
            { confidence: this._value.confidence },
            ...this._value.alternatives
        ];
        let entropy = 0;
        for (const pred of allPredictions) {
            const p = pred.confidence.value;
            if (p > 0) {
                entropy -= p * Math.log2(p);
            }
        }
        return entropy;
    }
    /**
     * Checks if the prediction is ambiguous (low confidence gap)
     */
    isAmbiguous(threshold = 0.1) {
        return this.getConfidenceGap() < threshold;
    }
    /**
     * Gets the age of the prediction in milliseconds
     */
    getAge() {
        return Date.now() - this._value.timestamp.getTime();
    }
    /**
     * Checks if the prediction is fresh (within time limit)
     */
    isFresh(maxAgeMs) {
        return this.getAge() <= maxAgeMs;
    }
    /**
     * Adds metadata to the prediction result
     */
    withMetadata(metadata) {
        return new PredictionResult(this._value.prediction, this._value.confidence, this._value.alternatives, { ...this._value.metadata, ...metadata }, this._value.timestamp);
    }
    /**
     * Updates the confidence score
     */
    withConfidence(confidence) {
        return new PredictionResult(this._value.prediction, confidence, this._value.alternatives, this._value.metadata, this._value.timestamp);
    }
    /**
     * Adds alternatives to the prediction result
     */
    withAlternatives(alternatives) {
        const sortedAlternatives = [...alternatives].sort((a, b) => b.confidence.value - a.confidence.value);
        return new PredictionResult(this._value.prediction, this._value.confidence, sortedAlternatives, this._value.metadata, this._value.timestamp);
    }
    /**
     * Compares this prediction result with another
     */
    compareTo(other) {
        return this._value.confidence.value - other._value.confidence.value;
    }
    /**
     * Checks if this prediction is better than another
     */
    isBetterThan(other) {
        return this._value.confidence.isGreaterThan(other._value.confidence);
    }
    /**
     * Converts to a human-readable string
     */
    toString() {
        const predictionStr = typeof this._value.prediction === 'object'
            ? JSON.stringify(this._value.prediction)
            : String(this._value.prediction);
        let result = `Prediction: ${predictionStr} (${this._value.confidence.toString()})`;
        if (this._value.alternatives.length > 0) {
            const altStr = this._value.alternatives
                .slice(0, 3) // Show top 3 alternatives
                .map(alt => `${alt.value} (${alt.confidence.toPercentageString()})`)
                .join(', ');
            result += ` | Alternatives: ${altStr}`;
        }
        return result;
    }
    /**
     * Converts to JSON representation
     */
    toJSON() {
        return {
            prediction: this._value.prediction,
            confidence: this._value.confidence.toJSON(),
            alternatives: this._value.alternatives.map(alt => ({
                value: alt.value,
                confidence: alt.confidence.toJSON(),
            })),
            metadata: this._value.metadata,
            timestamp: this._value.timestamp.toISOString(),
            hasHighConfidence: this.hasHighConfidence(),
            hasLowConfidence: this.hasLowConfidence(),
            confidenceGap: this.getConfidenceGap(),
            isAmbiguous: this.isAmbiguous(),
            entropy: this.getEntropy(),
            age: this.getAge(),
        };
    }
    /**
     * Creates a PredictionResult from JSON
     */
    static fromJSON(json) {
        const alternatives = json.alternatives?.map((alt) => ({
            value: alt.value,
            confidence: confidence_score_value_object_1.ConfidenceScore.fromJSON(alt.confidence),
        })) || [];
        return new PredictionResult(json.prediction, confidence_score_value_object_1.ConfidenceScore.fromJSON(json.confidence), alternatives, json.metadata, new Date(json.timestamp));
    }
    /**
     * Combines multiple prediction results using ensemble methods
     */
    static ensemble(results, method = 'average', weights) {
        if (results.length === 0) {
            throw new Error('Cannot create ensemble from empty results');
        }
        if (method === 'weighted' && (!weights || weights.length !== results.length)) {
            throw new Error('Weights array must have the same length as results array');
        }
        // For simplicity, we'll use the most confident prediction as the ensemble result
        // In a real implementation, this would depend on the specific ensemble method
        const sortedResults = [...results].sort((a, b) => b.confidence.value - a.confidence.value);
        const bestResult = sortedResults[0];
        // Combine metadata from all results
        const combinedMetadata = results.reduce((acc, result) => ({ ...acc, ...result.metadata }), {});
        combinedMetadata.ensembleMethod = method;
        combinedMetadata.ensembleSize = results.length;
        return new PredictionResult(bestResult.prediction, bestResult.confidence, bestResult.alternatives, combinedMetadata);
    }
}
exports.PredictionResult = PredictionResult;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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