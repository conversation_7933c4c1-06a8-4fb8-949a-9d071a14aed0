import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosRequestConfig, AxiosResponse, AxiosInstance } from 'axios';

/**
 * AI HTTP Client
 * 
 * Handles HTTP communication with AI providers and services.
 * Implements retry logic, timeout handling, request/response transformation,
 * and comprehensive error handling for reliable AI service integration.
 */
@Injectable()
export class AiHttpClient {
  private readonly logger = new Logger(AiHttpClient.name);
  private readonly defaultTimeout: number;
  private readonly defaultRetries: number;
  private readonly axiosInstance: AxiosInstance;

  constructor(
    private readonly configService: ConfigService,
  ) {
    this.defaultTimeout = this.configService.get<number>('ai.http.timeout', 30000);
    this.defaultRetries = this.configService.get<number>('ai.http.retries', 3);
    
    // Create axios instance with default configuration
    this.axiosInstance = axios.create({
      timeout: this.defaultTimeout,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Sentinel-AI-Client/1.0',
      },
    });
  }

  /**
   * Sends analysis request to AI provider
   */
  async sendAnalysisRequest(
    endpoint: string,
    payload: AiAnalysisPayload,
    options: RequestOptions = {}
  ): Promise<AiAnalysisResponse> {
    this.logger.debug(`Sending analysis request to: ${endpoint}`);

    try {
      const config = this.buildRequestConfig(endpoint, payload, options);
      const response = await this.executeRequest<AiAnalysisResponse>(config);
      
      this.logger.debug(`Analysis request completed: ${endpoint}`);
      return this.transformAnalysisResponse(response.data);

    } catch (error) {
      this.logger.error(`Analysis request failed: ${endpoint}`, error);
      throw new AiHttpError(`Analysis request failed: ${error.message}`, endpoint, error);
    }
  }

  /**
   * Sends training request to AI provider
   */
  async sendTrainingRequest(
    endpoint: string,
    payload: AiTrainingPayload,
    options: RequestOptions = {}
  ): Promise<AiTrainingResponse> {
    this.logger.debug(`Sending training request to: ${endpoint}`);

    try {
      const config = this.buildRequestConfig(endpoint, payload, {
        ...options,
        timeout: options.timeout || 300000, // 5 minutes for training
      });
      
      const response = await this.executeRequest<AiTrainingResponse>(config);
      
      this.logger.debug(`Training request completed: ${endpoint}`);
      return this.transformTrainingResponse(response.data);

    } catch (error) {
      this.logger.error(`Training request failed: ${endpoint}`, error);
      throw new AiHttpError(`Training request failed: ${error.message}`, endpoint, error);
    }
  }

  /**
   * Sends prediction request to AI provider
   */
  async sendPredictionRequest(
    endpoint: string,
    payload: AiPredictionPayload,
    options: RequestOptions = {}
  ): Promise<AiPredictionResponse> {
    this.logger.debug(`Sending prediction request to: ${endpoint}`);

    try {
      const config = this.buildRequestConfig(endpoint, payload, {
        ...options,
        timeout: options.timeout || 5000, // 5 seconds for predictions
      });
      
      const response = await this.executeRequest<AiPredictionResponse>(config);
      
      this.logger.debug(`Prediction request completed: ${endpoint}`);
      return this.transformPredictionResponse(response.data);

    } catch (error) {
      this.logger.error(`Prediction request failed: ${endpoint}`, error);
      throw new AiHttpError(`Prediction request failed: ${error.message}`, endpoint, error);
    }
  }

  /**
   * Sends health check request to AI provider
   */
  async sendHealthCheckRequest(
    endpoint: string,
    options: RequestOptions = {}
  ): Promise<AiHealthResponse> {
    this.logger.debug(`Sending health check to: ${endpoint}`);

    try {
      const config: AxiosRequestConfig = {
        method: 'GET',
        url: `${endpoint}/health`,
        timeout: options.timeout || 5000,
        headers: this.buildHeaders(options),
      };

      const response = await this.executeRequest<AiHealthResponse>(config);
      
      return {
        healthy: response.data.healthy || response.status === 200,
        status: response.data.status || 'unknown',
        responseTime: response.data.responseTime,
        version: response.data.version,
        timestamp: new Date(),
      };

    } catch (error) {
      this.logger.warn(`Health check failed: ${endpoint}`, error);
      return {
        healthy: false,
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date(),
      };
    }
  }

  /**
   * Sends batch request to AI provider
   */
  async sendBatchRequest(
    endpoint: string,
    payloads: AiBatchPayload[],
    options: RequestOptions = {}
  ): Promise<AiBatchResponse> {
    this.logger.debug(`Sending batch request to: ${endpoint}, count: ${payloads.length}`);

    try {
      const batchPayload = {
        requests: payloads,
        batchId: this.generateBatchId(),
        timestamp: new Date().toISOString(),
      };

      const config = this.buildRequestConfig(endpoint, batchPayload, {
        ...options,
        timeout: options.timeout || 120000, // 2 minutes for batch
      });

      const response = await this.executeRequest<AiBatchResponse>(config);
      
      this.logger.debug(`Batch request completed: ${endpoint}`);
      return this.transformBatchResponse(response.data);

    } catch (error) {
      this.logger.error(`Batch request failed: ${endpoint}`, error);
      throw new AiHttpError(`Batch request failed: ${error.message}`, endpoint, error);
    }
  }

  /**
   * Uploads file to AI provider
   */
  async uploadFile(
    endpoint: string,
    file: Buffer | string,
    filename: string,
    options: RequestOptions = {}
  ): Promise<AiFileUploadResponse> {
    this.logger.debug(`Uploading file to: ${endpoint}, filename: ${filename}`);

    try {
      const formData = new FormData();
      formData.append('file', new Blob([file]), filename);
      
      if (options.metadata) {
        formData.append('metadata', JSON.stringify(options.metadata));
      }

      const config: AxiosRequestConfig = {
        method: 'POST',
        url: endpoint,
        data: formData,
        timeout: options.timeout || 60000,
        headers: {
          ...this.buildHeaders(options),
          'Content-Type': 'multipart/form-data',
        },
      };

      const response = await this.executeRequest<AiFileUploadResponse>(config);
      
      this.logger.debug(`File upload completed: ${endpoint}`);
      return response.data;

    } catch (error) {
      this.logger.error(`File upload failed: ${endpoint}`, error);
      throw new AiHttpError(`File upload failed: ${error.message}`, endpoint, error);
    }
  }

  /**
   * Downloads file from AI provider
   */
  async downloadFile(
    endpoint: string,
    fileId: string,
    options: RequestOptions = {}
  ): Promise<AiFileDownloadResponse> {
    this.logger.debug(`Downloading file from: ${endpoint}, fileId: ${fileId}`);

    try {
      const config: AxiosRequestConfig = {
        method: 'GET',
        url: `${endpoint}/${fileId}`,
        responseType: 'arraybuffer',
        timeout: options.timeout || 60000,
        headers: this.buildHeaders(options),
      };

      const response = await this.executeRequest<ArrayBuffer>(config);
      
      return {
        data: Buffer.from(response.data),
        filename: this.extractFilename(response.headers),
        contentType: response.headers['content-type'],
        size: response.data.byteLength,
      };

    } catch (error) {
      this.logger.error(`File download failed: ${endpoint}`, error);
      throw new AiHttpError(`File download failed: ${error.message}`, endpoint, error);
    }
  }

  /**
   * Streams data to AI provider
   */
  async streamRequest(
    endpoint: string,
    payload: any,
    options: RequestOptions = {}
  ): Promise<ReadableStream> {
    this.logger.debug(`Starting stream request to: ${endpoint}`);

    try {
      const config = this.buildRequestConfig(endpoint, payload, {
        ...options,
        responseType: 'stream',
      });

      const response = await this.executeRequest(config);
      return response.data;

    } catch (error) {
      this.logger.error(`Stream request failed: ${endpoint}`, error);
      throw new AiHttpError(`Stream request failed: ${error.message}`, endpoint, error);
    }
  }

  // Private helper methods

  private buildRequestConfig(
    endpoint: string,
    payload: any,
    options: RequestOptions
  ): AxiosRequestConfig {
    return {
      method: options.method || 'POST',
      url: endpoint,
      data: payload,
      timeout: options.timeout || this.defaultTimeout,
      headers: this.buildHeaders(options),
      params: options.queryParams,
      responseType: options.responseType || 'json',
    };
  }

  private buildHeaders(options: RequestOptions): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'User-Agent': 'Sentinel-AI-Client/1.0',
      'X-Request-ID': this.generateRequestId(),
    };

    if (options.apiKey) {
      headers['Authorization'] = `Bearer ${options.apiKey}`;
    }

    if (options.customHeaders) {
      Object.assign(headers, options.customHeaders);
    }

    return headers;
  }

  private async executeRequest<T>(config: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    const maxRetries = config.retries || this.defaultRetries;
    let lastError: any;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const response = await this.axiosInstance.request<T>(config);
        return response;
      } catch (error) {
        lastError = error;
        
        // Don't retry on the last attempt
        if (attempt === maxRetries) {
          break;
        }

        // Don't retry on certain error types
        if (this.shouldNotRetry(error)) {
          break;
        }

        // Exponential backoff with jitter
        const delay = Math.min(1000 * Math.pow(2, attempt), 10000) + Math.random() * 1000;
        await this.sleep(delay);
        
        this.logger.debug(`Retrying request (attempt ${attempt + 1}/${maxRetries}) after ${delay}ms`);
      }
    }

    throw this.transformHttpError(lastError);
  }

  private shouldNotRetry(error: any): boolean {
    // Don't retry on client errors (4xx) except for specific cases
    if (error.response?.status >= 400 && error.response?.status < 500) {
      // Retry on rate limiting and authentication errors
      return ![401, 408, 429].includes(error.response.status);
    }
    
    // Don't retry on certain network errors
    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      return true;
    }
    
    return false;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private transformAnalysisResponse(data: any): AiAnalysisResponse {
    return {
      id: data.id || this.generateResponseId(),
      result: data.result || data.analysis || data.output,
      confidence: data.confidence || data.score || 0,
      metadata: {
        model: data.model,
        version: data.version,
        processingTime: data.processingTime,
        ...data.metadata,
      },
      timestamp: new Date(data.timestamp || Date.now()),
    };
  }

  private transformTrainingResponse(data: any): AiTrainingResponse {
    return {
      jobId: data.jobId || data.id,
      status: data.status || 'completed',
      modelId: data.modelId,
      metrics: data.metrics || {},
      artifacts: data.artifacts || [],
      metadata: data.metadata || {},
      timestamp: new Date(data.timestamp || Date.now()),
    };
  }

  private transformPredictionResponse(data: any): AiPredictionResponse {
    return {
      prediction: data.prediction || data.result || data.output,
      confidence: data.confidence || data.score || 0,
      alternatives: data.alternatives || [],
      metadata: {
        model: data.model,
        latency: data.latency,
        ...data.metadata,
      },
      timestamp: new Date(data.timestamp || Date.now()),
    };
  }

  private transformBatchResponse(data: any): AiBatchResponse {
    return {
      batchId: data.batchId,
      results: data.results || data.responses || [],
      summary: {
        total: data.total || data.results?.length || 0,
        successful: data.successful || 0,
        failed: data.failed || 0,
        processingTime: data.processingTime,
      },
      metadata: data.metadata || {},
      timestamp: new Date(data.timestamp || Date.now()),
    };
  }

  private transformHttpError(error: any): Error {
    if (error?.response) {
      // HTTP error response
      const status = error.response.status;
      const message = error.response.data?.message || error.response.statusText;
      return new AiHttpError(`HTTP ${status}: ${message}`, error.config?.url, error);
    }
    
    if (error?.code === 'ECONNABORTED') {
      return new AiHttpError('Request timeout', error.config?.url, error);
    }
    
    if (error?.code === 'ECONNREFUSED') {
      return new AiHttpError('Connection refused', error.config?.url, error);
    }
    
    return new AiHttpError(error?.message || 'Unknown HTTP error', error?.config?.url, error);
  }

  private extractFilename(headers: any): string {
    const contentDisposition = headers['content-disposition'];
    if (contentDisposition) {
      const match = contentDisposition.match(/filename="?([^"]+)"?/);
      if (match) return match[1];
    }
    return 'download';
  }

  private generateRequestId(): string {
    return `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateResponseId(): string {
    return `res-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateBatchId(): string {
    return `batch-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Type definitions
interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  timeout?: number;
  retries?: number;
  apiKey?: string;
  customHeaders?: Record<string, string>;
  queryParams?: Record<string, any>;
  responseType?: 'json' | 'text' | 'arraybuffer' | 'stream';
  metadata?: any;
}

interface AiAnalysisPayload {
  data: any;
  model?: string;
  parameters?: any;
  options?: any;
}

interface AiAnalysisResponse {
  id: string;
  result: any;
  confidence: number;
  metadata: any;
  timestamp: Date;
}

interface AiTrainingPayload {
  trainingData: any;
  modelConfig: any;
  parameters?: any;
}

interface AiTrainingResponse {
  jobId: string;
  status: string;
  modelId?: string;
  metrics: any;
  artifacts: any[];
  metadata: any;
  timestamp: Date;
}

interface AiPredictionPayload {
  input: any;
  model?: string;
  parameters?: any;
}

interface AiPredictionResponse {
  prediction: any;
  confidence: number;
  alternatives?: any[];
  metadata: any;
  timestamp: Date;
}

interface AiHealthResponse {
  healthy: boolean;
  status: string;
  responseTime?: number;
  version?: string;
  error?: string;
  timestamp: Date;
}

interface AiBatchPayload {
  id: string;
  data: any;
  type: string;
}

interface AiBatchResponse {
  batchId: string;
  results: any[];
  summary: {
    total: number;
    successful: number;
    failed: number;
    processingTime?: number;
  };
  metadata: any;
  timestamp: Date;
}

interface AiFileUploadResponse {
  fileId: string;
  filename: string;
  size: number;
  url?: string;
  metadata?: any;
}

interface AiFileDownloadResponse {
  data: Buffer;
  filename: string;
  contentType: string;
  size: number;
}

class AiHttpError extends Error {
  constructor(
    message: string,
    public readonly endpoint?: string,
    public readonly originalError?: any
  ) {
    super(message);
    this.name = 'AiHttpError';
  }
}
