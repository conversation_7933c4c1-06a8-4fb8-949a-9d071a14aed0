{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\events\\analysis-result-created.domain-event.ts", "mappings": ";;;AAAA,0FAAqF;AAIrF;;;;;;GAMG;AACH,MAAa,0BAA2B,SAAQ,mCAAe;IAC7D,YACkB,gBAAgC,EAChC,SAAiB,EACjB,OAAuB,EACvB,YAA0B,EAC1C,OAAwB,EACxB,UAAiB;QAEjB,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAPX,qBAAgB,GAAhB,gBAAgB,CAAgB;QAChC,cAAS,GAAT,SAAS,CAAQ;QACjB,YAAO,GAAP,OAAO,CAAgB;QACvB,iBAAY,GAAZ,YAAY,CAAc;IAK5C,CAAC;IAEM,YAAY;QACjB,OAAO,uBAAuB,CAAC;IACjC,CAAC;IAEM,eAAe;QACpB,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,YAAY;QACjB,OAAO;YACL,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE;YAClD,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YAChC,YAAY,EAAE,IAAI,CAAC,YAAY;SAChC,CAAC;IACJ,CAAC;CACF;AA5BD,gEA4BC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\events\\analysis-result-created.domain-event.ts"], "sourcesContent": ["import { BaseDomainEvent } from '../../../../shared-kernel/domain/base-domain-event';\r\nimport { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { AnalysisType } from '../entities/analysis-result.entity';\r\n\r\n/**\r\n * Analysis Result Created Domain Event\r\n * \r\n * Published when a new analysis result is created in the system.\r\n * This event can trigger various downstream processes such as\r\n * result indexing, notification sending, and workflow continuation.\r\n */\r\nexport class AnalysisResultCreatedEvent extends BaseDomainEvent {\r\n  constructor(\r\n    public readonly analysisResultId: UniqueEntityId,\r\n    public readonly requestId: string,\r\n    public readonly modelId: UniqueEntityId,\r\n    public readonly analysisType: AnalysisType,\r\n    eventId?: UniqueEntityId,\r\n    occurredOn?: Date\r\n  ) {\r\n    super(eventId, occurredOn);\r\n  }\r\n\r\n  public getEventName(): string {\r\n    return 'AnalysisResultCreated';\r\n  }\r\n\r\n  public getEventVersion(): string {\r\n    return '1.0';\r\n  }\r\n\r\n  public getEventData(): Record<string, any> {\r\n    return {\r\n      analysisResultId: this.analysisResultId.toString(),\r\n      requestId: this.requestId,\r\n      modelId: this.modelId.toString(),\r\n      analysisType: this.analysisType,\r\n    };\r\n  }\r\n}"], "version": 3}