d03f5ff609280e2c401c6755fb24506e
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var WorkflowStepProcessorFactory_1;
var _a, _b, _c, _d, _e, _f, _g, _h;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowStepProcessorFactory = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const notification_queue_management_service_1 = require("./notification-queue-management.service");
const notification_template_management_service_1 = require("./notification-template-management.service");
const email_notification_provider_1 = require("../providers/email-notification.provider");
const sms_notification_provider_1 = require("../providers/sms-notification.provider");
const slack_notification_provider_1 = require("../providers/slack-notification.provider");
const teams_notification_provider_1 = require("../providers/teams-notification.provider");
const webhook_notification_provider_1 = require("../providers/webhook-notification.provider");
/**
 * Notification Step Processor
 */
class NotificationStepProcessor {
    constructor(queueManagementService, templateManagementService, providers) {
        this.queueManagementService = queueManagementService;
        this.templateManagementService = templateManagementService;
        this.providers = providers;
        this.logger = new common_1.Logger(NotificationStepProcessor.name);
    }
    async execute(stepDefinition, context) {
        try {
            this.logger.debug(`Executing notification step: ${stepDefinition.id}`);
            const config = stepDefinition.config || {};
            // Prepare notification data
            const notificationData = {
                id: `${context.executionId}_${stepDefinition.id}_${Date.now()}`,
                alert: context.input.alert || {},
                rule: context.input.rule || {},
                message: await this.prepareMessage(config, context),
                channel: config.channel || 'email',
                recipients: await this.resolveRecipients(config.recipients, context),
                metadata: {
                    workflowId: context.workflowId,
                    executionId: context.executionId,
                    stepId: stepDefinition.id,
                    ...config.metadata,
                },
                user: context.input.user || {},
                metricValues: context.input.metricValues || {},
                context: context.context || {},
            };
            // Queue notification for delivery
            const priority = config.priority || 'medium';
            await this.queueManagementService.addNotificationToQueue(notificationData, priority);
            this.logger.debug(`Notification queued successfully: ${notificationData.id}`);
            return {
                notificationId: notificationData.id,
                status: 'queued',
                channel: notificationData.channel,
                recipients: notificationData.recipients.length,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`Notification step execution failed: ${error.message}`, error.stack);
            throw error;
        }
    }
    async prepareMessage(config, context) {
        if (config.templateId) {
            // Use template
            const template = await this.templateManagementService.getTemplate(config.templateId);
            return await this.templateManagementService.renderTemplate(template, context.input);
        }
        else if (config.message) {
            // Use direct message
            return this.interpolateMessage(config.message, context);
        }
        else {
            throw new Error('No message template or content specified');
        }
    }
    async resolveRecipients(recipients, context) {
        if (Array.isArray(recipients)) {
            return recipients;
        }
        else if (typeof recipients === 'string') {
            // Could be a field reference or expression
            if (recipients.startsWith('$')) {
                const fieldValue = this.getFieldValue(recipients.substring(1), context);
                return Array.isArray(fieldValue) ? fieldValue : [fieldValue];
            }
            return [recipients];
        }
        else if (recipients.field) {
            const fieldValue = this.getFieldValue(recipients.field, context);
            return Array.isArray(fieldValue) ? fieldValue : [fieldValue];
        }
        return [];
    }
    interpolateMessage(message, context) {
        return message.replace(/\{\{([^}]+)\}\}/g, (match, expression) => {
            try {
                const value = this.getFieldValue(expression.trim(), context);
                return String(value || '');
            }
            catch {
                return match;
            }
        });
    }
    getFieldValue(field, context) {
        const parts = field.split('.');
        let value = context;
        for (const part of parts) {
            if (value === null || value === undefined)
                return undefined;
            value = value[part];
        }
        return value;
    }
}
/**
 * Delay Step Processor
 */
class DelayStepProcessor {
    constructor() {
        this.logger = new common_1.Logger(DelayStepProcessor.name);
    }
    async execute(stepDefinition, context) {
        try {
            this.logger.debug(`Executing delay step: ${stepDefinition.id}`);
            const config = stepDefinition.config || {};
            const delay = config.delay || 1000; // Default 1 second
            await this.delay(delay);
            this.logger.debug(`Delay step completed: ${stepDefinition.id}`);
            return {
                delayed: delay,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`Delay step execution failed: ${error.message}`, error.stack);
            throw error;
        }
    }
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
/**
 * Condition Step Processor
 */
class ConditionStepProcessor {
    constructor() {
        this.logger = new common_1.Logger(ConditionStepProcessor.name);
    }
    async execute(stepDefinition, context) {
        try {
            this.logger.debug(`Executing condition step: ${stepDefinition.id}`);
            const config = stepDefinition.config || {};
            const condition = config.condition;
            if (!condition) {
                throw new Error('No condition specified for condition step');
            }
            // Simple condition evaluation
            const result = await this.evaluateCondition(condition, context);
            this.logger.debug(`Condition step result: ${result}`);
            return {
                condition: condition,
                result: result,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`Condition step execution failed: ${error.message}`, error.stack);
            throw error;
        }
    }
    async evaluateCondition(condition, context) {
        // Simple condition evaluation - in production, integrate with rule engine
        if (typeof condition === 'boolean') {
            return condition;
        }
        if (typeof condition === 'object' && condition.field && condition.operator && condition.hasOwnProperty('value')) {
            const fieldValue = this.getFieldValue(condition.field, context);
            const compareValue = condition.value;
            switch (condition.operator) {
                case 'eq': return fieldValue == compareValue;
                case 'neq': return fieldValue != compareValue;
                case 'gt': return fieldValue > compareValue;
                case 'gte': return fieldValue >= compareValue;
                case 'lt': return fieldValue < compareValue;
                case 'lte': return fieldValue <= compareValue;
                default: return false;
            }
        }
        return false;
    }
    getFieldValue(field, context) {
        const parts = field.split('.');
        let value = context;
        for (const part of parts) {
            if (value === null || value === undefined)
                return undefined;
            value = value[part];
        }
        return value;
    }
}
/**
 * Variable Step Processor
 */
class VariableStepProcessor {
    constructor() {
        this.logger = new common_1.Logger(VariableStepProcessor.name);
    }
    async execute(stepDefinition, context) {
        try {
            this.logger.debug(`Executing variable step: ${stepDefinition.id}`);
            const config = stepDefinition.config || {};
            const variables = config.variables || {};
            // Set variables in context
            for (const [key, value] of Object.entries(variables)) {
                context.variables[key] = this.resolveValue(value, context);
            }
            this.logger.debug(`Variable step completed: ${stepDefinition.id}`);
            return {
                variables: Object.keys(variables),
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`Variable step execution failed: ${error.message}`, error.stack);
            throw error;
        }
    }
    resolveValue(value, context) {
        if (typeof value === 'string' && value.startsWith('$')) {
            return this.getFieldValue(value.substring(1), context);
        }
        return value;
    }
    getFieldValue(field, context) {
        const parts = field.split('.');
        let value = context;
        for (const part of parts) {
            if (value === null || value === undefined)
                return undefined;
            value = value[part];
        }
        return value;
    }
}
/**
 * HTTP Request Step Processor
 */
class HttpRequestStepProcessor {
    constructor() {
        this.logger = new common_1.Logger(HttpRequestStepProcessor.name);
    }
    async execute(stepDefinition, context) {
        try {
            this.logger.debug(`Executing HTTP request step: ${stepDefinition.id}`);
            const config = stepDefinition.config || {};
            const url = this.interpolateString(config.url, context);
            const method = config.method || 'GET';
            const headers = config.headers || {};
            const body = config.body ? this.interpolateValue(config.body, context) : undefined;
            // Make HTTP request (simplified implementation)
            const response = await this.makeHttpRequest(url, method, headers, body);
            this.logger.debug(`HTTP request step completed: ${stepDefinition.id}`);
            return {
                url,
                method,
                status: response.status,
                data: response.data,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error(`HTTP request step execution failed: ${error.message}`, error.stack);
            throw error;
        }
    }
    async makeHttpRequest(url, method, headers, body) {
        // Simplified HTTP request implementation
        // In production, use a proper HTTP client like axios
        return {
            status: 200,
            data: { success: true },
        };
    }
    interpolateString(str, context) {
        return str.replace(/\{\{([^}]+)\}\}/g, (match, expression) => {
            try {
                const value = this.getFieldValue(expression.trim(), context);
                return String(value || '');
            }
            catch {
                return match;
            }
        });
    }
    interpolateValue(value, context) {
        if (typeof value === 'string') {
            return this.interpolateString(value, context);
        }
        else if (typeof value === 'object' && value !== null) {
            const result = {};
            for (const [key, val] of Object.entries(value)) {
                result[key] = this.interpolateValue(val, context);
            }
            return result;
        }
        return value;
    }
    getFieldValue(field, context) {
        const parts = field.split('.');
        let value = context;
        for (const part of parts) {
            if (value === null || value === undefined)
                return undefined;
            value = value[part];
        }
        return value;
    }
}
/**
 * Workflow Step Processor Factory
 *
 * Factory service for creating and managing workflow step processors including:
 * - Step processor registration and instantiation
 * - Type-based processor selection and execution
 * - Custom processor registration and management
 * - Performance optimization with processor caching
 * - Integration with notification infrastructure
 * - Extensible architecture for custom step types
 */
let WorkflowStepProcessorFactory = WorkflowStepProcessorFactory_1 = class WorkflowStepProcessorFactory {
    constructor(configService, queueManagementService, templateManagementService, emailProvider, smsProvider, slackProvider, teamsProvider, webhookProvider) {
        this.configService = configService;
        this.queueManagementService = queueManagementService;
        this.templateManagementService = templateManagementService;
        this.emailProvider = emailProvider;
        this.smsProvider = smsProvider;
        this.slackProvider = slackProvider;
        this.teamsProvider = teamsProvider;
        this.webhookProvider = webhookProvider;
        this.logger = new common_1.Logger(WorkflowStepProcessorFactory_1.name);
        this.processors = new Map();
        this.initializeProcessors();
    }
    /**
     * Get processor for step type
     */
    getProcessor(stepType) {
        const processor = this.processors.get(stepType);
        if (!processor) {
            throw new Error(`No processor found for step type: ${stepType}`);
        }
        return processor;
    }
    /**
     * Register custom processor
     */
    registerProcessor(stepType, processor) {
        this.processors.set(stepType, processor);
        this.logger.debug(`Registered processor for step type: ${stepType}`);
    }
    /**
     * Get available step types
     */
    getAvailableStepTypes() {
        return Array.from(this.processors.keys());
    }
    /**
     * Initialize built-in processors
     */
    initializeProcessors() {
        // Create provider map
        const providers = new Map();
        providers.set('email', this.emailProvider);
        providers.set('sms', this.smsProvider);
        providers.set('slack', this.slackProvider);
        providers.set('teams', this.teamsProvider);
        providers.set('webhook', this.webhookProvider);
        // Register built-in processors
        this.processors.set('notification', new NotificationStepProcessor(this.queueManagementService, this.templateManagementService, providers));
        this.processors.set('delay', new DelayStepProcessor());
        this.processors.set('condition', new ConditionStepProcessor());
        this.processors.set('variable', new VariableStepProcessor());
        this.processors.set('http_request', new HttpRequestStepProcessor());
        this.logger.log(`Initialized ${this.processors.size} step processors`);
    }
};
exports.WorkflowStepProcessorFactory = WorkflowStepProcessorFactory;
exports.WorkflowStepProcessorFactory = WorkflowStepProcessorFactory = WorkflowStepProcessorFactory_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object, typeof (_b = typeof notification_queue_management_service_1.NotificationQueueManagementService !== "undefined" && notification_queue_management_service_1.NotificationQueueManagementService) === "function" ? _b : Object, typeof (_c = typeof notification_template_management_service_1.NotificationTemplateManagementService !== "undefined" && notification_template_management_service_1.NotificationTemplateManagementService) === "function" ? _c : Object, typeof (_d = typeof email_notification_provider_1.EmailNotificationProvider !== "undefined" && email_notification_provider_1.EmailNotificationProvider) === "function" ? _d : Object, typeof (_e = typeof sms_notification_provider_1.SmsNotificationProvider !== "undefined" && sms_notification_provider_1.SmsNotificationProvider) === "function" ? _e : Object, typeof (_f = typeof slack_notification_provider_1.SlackNotificationProvider !== "undefined" && slack_notification_provider_1.SlackNotificationProvider) === "function" ? _f : Object, typeof (_g = typeof teams_notification_provider_1.TeamsNotificationProvider !== "undefined" && teams_notification_provider_1.TeamsNotificationProvider) === "function" ? _g : Object, typeof (_h = typeof webhook_notification_provider_1.WebhookNotificationProvider !== "undefined" && webhook_notification_provider_1.WebhookNotificationProvider) === "function" ? _h : Object])
], WorkflowStepProcessorFactory);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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