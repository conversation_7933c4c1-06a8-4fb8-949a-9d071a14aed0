import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ComplianceAssessment } from '../entities/compliance-assessment.entity';
import { ComplianceFramework } from '../entities/compliance-framework.entity';
import { LoggerService } from '../../../infrastructure/logging/logger.service';
import { DistributedCacheService } from '../../../infrastructure/cache/distributed-cache.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ConfigService } from '@nestjs/config';

/**
 * Compliance Assessment Service
 * 
 * Provides comprehensive compliance assessment management with:
 * - Multi-phase assessment workflow execution
 * - Automated scoring and compliance calculation
 * - Findings management and remediation tracking
 * - Risk assessment and gap analysis
 * - Approval workflow management
 * - Real-time assessment monitoring and reporting
 */
@Injectable()
export class ComplianceAssessmentService {
  constructor(
    @InjectRepository(ComplianceAssessment)
    private readonly assessmentRepository: Repository<ComplianceAssessment>,
    @InjectRepository(ComplianceFramework)
    private readonly frameworkRepository: Repository<ComplianceFramework>,
    private readonly cacheService: DistributedCacheService,
    private readonly eventEmitter: EventEmitter2,
    private readonly configService: ConfigService,
    private readonly logger: LoggerService,
  ) {}

  /**
   * Create a new compliance assessment
   */
  async createAssessment(createAssessmentDto: {
    frameworkId: string;
    name: string;
    description?: string;
    assessmentType: 'self_assessment' | 'internal_audit' | 'external_audit' | 'certification' | 'continuous_monitoring';
    methodology?: string;
    priority: 'low' | 'medium' | 'high' | 'critical';
    scope?: any;
    assessorId?: string;
    assessmentTeam?: any[];
    externalAuditor?: any;
    plannedStartDate?: Date;
    plannedEndDate?: Date;
    dueDate?: Date;
    phases?: any[];
  }): Promise<ComplianceAssessment> {
    try {
      // Validate framework exists
      const framework = await this.frameworkRepository.findOne({
        where: { id: createAssessmentDto.frameworkId },
      });

      if (!framework) {
        throw new NotFoundException(`Framework with ID ${createAssessmentDto.frameworkId} not found`);
      }

      // Create assessment entity
      const assessment = this.assessmentRepository.create({
        ...createAssessmentDto,
        assessmentDate: new Date(),
        phases: createAssessmentDto.phases || this.generateDefaultPhases(createAssessmentDto.assessmentType),
      });

      // Save assessment
      const savedAssessment = await this.assessmentRepository.save(assessment);

      // Cache assessment for performance
      await this.cacheAssessment(savedAssessment);

      // Emit assessment created event
      this.eventEmitter.emit('compliance.assessment.created', {
        assessmentId: savedAssessment.id,
        frameworkId: savedAssessment.frameworkId,
        assessmentType: savedAssessment.assessmentType,
        priority: savedAssessment.priority,
        dueDate: savedAssessment.dueDate,
      });

      // Initialize assessment workflow
      await this.initializeAssessmentWorkflow(savedAssessment);

      this.logger.log('Compliance assessment created successfully', {
        assessmentId: savedAssessment.id,
        frameworkId: savedAssessment.frameworkId,
        assessmentType: savedAssessment.assessmentType,
        priority: savedAssessment.priority,
      });

      return savedAssessment;

    } catch (error) {
      this.logger.error(
        `Failed to create compliance assessment: ${error instanceof Error ? error.message : 'Unknown error'}`,
        undefined,
        {
          frameworkId: createAssessmentDto.frameworkId,
          assessmentType: createAssessmentDto.assessmentType,
        }
      );
      throw error;
    }
  }

  /**
   * Get assessments with filtering and pagination
   */
  async getAssessments(filters: {
    frameworkId?: string;
    assessmentType?: string;
    status?: string;
    priority?: string;
    assessorId?: string;
    overdue?: boolean;
    startDate?: Date;
    endDate?: Date;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
  }): Promise<{
    assessments: ComplianceAssessment[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    try {
      const {
        frameworkId,
        assessmentType,
        status,
        priority,
        assessorId,
        overdue,
        startDate,
        endDate,
        page = 1,
        limit = 20,
        sortBy = 'assessmentDate',
        sortOrder = 'DESC',
      } = filters;

      // Build query
      let queryBuilder = this.assessmentRepository.createQueryBuilder('assessment');

      // Apply filters
      if (frameworkId) {
        queryBuilder = queryBuilder.andWhere('assessment.framework_id = :frameworkId', { frameworkId });
      }

      if (assessmentType) {
        queryBuilder = queryBuilder.andWhere('assessment.assessment_type = :assessmentType', { assessmentType });
      }

      if (status) {
        queryBuilder = queryBuilder.andWhere('assessment.status = :status', { status });
      }

      if (priority) {
        queryBuilder = queryBuilder.andWhere('assessment.priority = :priority', { priority });
      }

      if (assessorId) {
        queryBuilder = queryBuilder.andWhere('assessment.assessor_id = :assessorId', { assessorId });
      }

      if (overdue) {
        queryBuilder = queryBuilder.andWhere('assessment.due_date < :now AND assessment.status != :completed', {
          now: new Date(),
          completed: 'completed',
        });
      }

      if (startDate && endDate) {
        queryBuilder = queryBuilder.andWhere('assessment.assessment_date BETWEEN :startDate AND :endDate', {
          startDate,
          endDate,
        });
      }

      // Apply sorting
      queryBuilder = queryBuilder.orderBy(`assessment.${sortBy}`, sortOrder);

      // Apply pagination
      const offset = (page - 1) * limit;
      queryBuilder = queryBuilder.skip(offset).take(limit);

      // Include relations
      queryBuilder = queryBuilder.leftJoinAndSelect('assessment.framework', 'framework')
        .leftJoinAndSelect('assessment.assessor', 'assessor')
        .leftJoinAndSelect('assessment.approvedBy', 'approver');

      // Execute query
      const [assessments, total] = await queryBuilder.getManyAndCount();

      const totalPages = Math.ceil(total / limit);

      this.logger.debug('Retrieved compliance assessments', {
        total,
        page,
        limit,
        filters: Object.keys(filters).filter(key => filters[key] !== undefined),
      });

      return {
        assessments,
        total,
        page,
        limit,
        totalPages,
      };

    } catch (error) {
      this.logger.error(
        `Failed to retrieve compliance assessments: ${error instanceof Error ? error.message : 'Unknown error'}`,
        undefined,
        { filters }
      );
      throw error;
    }
  }

  /**
   * Get assessment by ID
   */
  async getAssessmentById(id: string): Promise<ComplianceAssessment> {
    try {
      // Try cache first
      const cacheKey = `assessment:${id}`;
      const cachedAssessment = await this.cacheService.get<ComplianceAssessment>(cacheKey);
      
      if (cachedAssessment) {
        return cachedAssessment;
      }

      const assessment = await this.assessmentRepository.findOne({
        where: { id },
        relations: ['framework', 'assessor', 'approvedBy', 'evidence'],
      });

      if (!assessment) {
        throw new NotFoundException(`Compliance assessment with ID ${id} not found`);
      }

      // Cache for future requests
      await this.cacheAssessment(assessment);

      return assessment;

    } catch (error) {
      this.logger.error(
        `Failed to retrieve compliance assessment: ${error instanceof Error ? error.message : 'Unknown error'}`,
        undefined,
        { assessmentId: id }
      );
      throw error;
    }
  }

  /**
   * Start assessment execution
   */
  async startAssessment(assessmentId: string): Promise<ComplianceAssessment> {
    try {
      const assessment = await this.getAssessmentById(assessmentId);

      if (assessment.status !== 'planned') {
        throw new BadRequestException(`Assessment must be in 'planned' status to start. Current status: ${assessment.status}`);
      }

      // Update assessment status and dates
      assessment.status = 'in_progress';
      assessment.actualStartDate = new Date();

      // Update first phase status
      if (assessment.phases && assessment.phases.length > 0) {
        assessment.phases[0].status = 'in_progress';
        assessment.phases[0].actualStartDate = new Date();
      }

      // Save updated assessment
      const updatedAssessment = await this.assessmentRepository.save(assessment);

      // Clear cache
      await this.cacheService.delete(`assessment:${assessmentId}`);

      // Emit assessment started event
      this.eventEmitter.emit('compliance.assessment.started', {
        assessmentId,
        frameworkId: assessment.frameworkId,
        assessorId: assessment.assessorId,
        startDate: assessment.actualStartDate,
      });

      this.logger.log('Assessment started successfully', {
        assessmentId,
        frameworkId: assessment.frameworkId,
        assessorId: assessment.assessorId,
      });

      return updatedAssessment;

    } catch (error) {
      this.logger.error(
        `Failed to start assessment: ${error instanceof Error ? error.message : 'Unknown error'}`,
        undefined,
        { assessmentId }
      );
      throw error;
    }
  }

  /**
   * Complete assessment phase
   */
  async completePhase(
    assessmentId: string,
    phaseIndex: number,
    phaseResults: {
      findings?: any[];
      evidence?: string[];
      notes?: string;
      score?: number;
    }
  ): Promise<ComplianceAssessment> {
    try {
      const assessment = await this.getAssessmentById(assessmentId);

      if (!assessment.phases || phaseIndex >= assessment.phases.length) {
        throw new BadRequestException('Invalid phase index');
      }

      const phase = assessment.phases[phaseIndex];
      
      if (phase.status !== 'in_progress') {
        throw new BadRequestException(`Phase must be in progress to complete. Current status: ${phase.status}`);
      }

      // Update phase completion
      phase.status = 'completed';
      phase.actualEndDate = new Date();

      // Add phase results to assessment
      if (phaseResults.findings) {
        assessment.findings = [...(assessment.findings || []), ...phaseResults.findings];
      }

      // Start next phase if available
      if (phaseIndex + 1 < assessment.phases.length) {
        const nextPhase = assessment.phases[phaseIndex + 1];
        nextPhase.status = 'in_progress';
        nextPhase.actualStartDate = new Date();
      } else {
        // All phases completed - finalize assessment
        await this.finalizeAssessment(assessment);
      }

      // Save updated assessment
      const updatedAssessment = await this.assessmentRepository.save(assessment);

      // Clear cache
      await this.cacheService.delete(`assessment:${assessmentId}`);

      // Emit phase completed event
      this.eventEmitter.emit('compliance.assessment.phase.completed', {
        assessmentId,
        phaseIndex,
        phaseName: phase.name,
        isLastPhase: phaseIndex + 1 >= assessment.phases.length,
      });

      this.logger.log('Assessment phase completed', {
        assessmentId,
        phaseIndex,
        phaseName: phase.name,
        findingsCount: phaseResults.findings?.length || 0,
      });

      return updatedAssessment;

    } catch (error) {
      this.logger.error(
        `Failed to complete assessment phase: ${error instanceof Error ? error.message : 'Unknown error'}`,
        undefined,
        { assessmentId, phaseIndex }
      );
      throw error;
    }
  }

  /**
   * Calculate assessment score and compliance percentage
   */
  async calculateAssessmentScore(assessmentId: string): Promise<{
    overallScore: number;
    compliancePercentage: number;
    maturityLevel: number;
    domainResults: any[];
    riskAssessment: any;
  }> {
    try {
      const assessment = await this.getAssessmentById(assessmentId);
      const framework = assessment.framework;

      if (!framework) {
        throw new BadRequestException('Framework not found for assessment');
      }

      // Calculate domain-level scores
      const domainResults = [];
      let totalScore = 0;
      let totalControls = 0;

      for (const domain of framework.domains) {
        const domainControls = domain.controls || [];
        const domainFindings = assessment.findings?.filter(f => f.domainId === domain.id) || [];
        
        // Calculate domain score based on findings and control implementation
        const domainScore = this.calculateDomainScore(domainControls, domainFindings);
        const compliancePercentage = this.calculateDomainCompliance(domainControls, domainFindings);

        domainResults.push({
          domainId: domain.id,
          domainName: domain.name,
          score: domainScore,
          compliancePercentage,
          controlsAssessed: domainControls.length,
          controlsPassed: domainControls.length - domainFindings.filter(f => f.severity === 'high' || f.severity === 'critical').length,
          controlsFailed: domainFindings.filter(f => f.severity === 'high' || f.severity === 'critical').length,
          findings: domainFindings,
        });

        totalScore += domainScore * (domain.weight || 1);
        totalControls += domainControls.length;
      }

      // Calculate overall scores
      const overallScore = totalControls > 0 ? Math.round(totalScore / framework.domains.length) : 0;
      const compliancePercentage = this.calculateOverallCompliance(domainResults);
      const maturityLevel = assessment.calculateMaturityLevel();

      // Perform risk assessment
      const riskAssessment = this.performRiskAssessment(assessment, domainResults);

      // Update assessment with calculated scores
      assessment.overallScore = overallScore;
      assessment.compliancePercentage = compliancePercentage;
      assessment.maturityLevel = maturityLevel;
      assessment.domainResults = domainResults;
      assessment.riskAssessment = riskAssessment;

      await this.assessmentRepository.save(assessment);

      // Clear cache
      await this.cacheService.delete(`assessment:${assessmentId}`);

      this.logger.log('Assessment score calculated', {
        assessmentId,
        overallScore,
        compliancePercentage,
        maturityLevel,
        riskLevel: riskAssessment.overallRiskLevel,
      });

      return {
        overallScore,
        compliancePercentage,
        maturityLevel,
        domainResults,
        riskAssessment,
      };

    } catch (error) {
      this.logger.error(
        `Failed to calculate assessment score: ${error instanceof Error ? error.message : 'Unknown error'}`,
        undefined,
        { assessmentId }
      );
      throw error;
    }
  }

  /**
   * Submit assessment for approval
   */
  async submitForApproval(assessmentId: string): Promise<ComplianceAssessment> {
    try {
      const assessment = await this.getAssessmentById(assessmentId);

      if (assessment.status !== 'completed') {
        throw new BadRequestException('Assessment must be completed before submission for approval');
      }

      // Update status
      assessment.status = 'under_review';

      // Initialize approval workflow if configured
      if (assessment.approvalWorkflow) {
        assessment.approvalWorkflow.currentStep = 1;
        assessment.approvalWorkflow.steps[0].status = 'pending';
      }

      // Save updated assessment
      const updatedAssessment = await this.assessmentRepository.save(assessment);

      // Clear cache
      await this.cacheService.delete(`assessment:${assessmentId}`);

      // Emit submission event
      this.eventEmitter.emit('compliance.assessment.submitted', {
        assessmentId,
        frameworkId: assessment.frameworkId,
        submittedAt: new Date(),
        approvers: assessment.approvalWorkflow?.steps.map(s => s.approver) || [],
      });

      this.logger.log('Assessment submitted for approval', {
        assessmentId,
        frameworkId: assessment.frameworkId,
      });

      return updatedAssessment;

    } catch (error) {
      this.logger.error(
        `Failed to submit assessment for approval: ${error instanceof Error ? error.message : 'Unknown error'}`,
        undefined,
        { assessmentId }
      );
      throw error;
    }
  }

  /**
   * Get assessment dashboard data
   */
  async getAssessmentDashboard(assessmentId: string): Promise<{
    assessment: ComplianceAssessment;
    progress: {
      overallProgress: number;
      phaseProgress: any[];
      completedPhases: number;
      totalPhases: number;
    };
    findings: {
      total: number;
      bySeverity: Record<string, number>;
      byStatus: Record<string, number>;
      critical: any[];
    };
    timeline: {
      plannedDuration: number;
      actualDuration: number;
      remainingDays: number;
      isOnTrack: boolean;
    };
    healthStatus: any;
  }> {
    try {
      const assessment = await this.getAssessmentById(assessmentId);

      // Calculate progress
      const progress = {
        overallProgress: assessment.getProgressPercentage(),
        phaseProgress: assessment.phases?.map(phase => ({
          name: phase.name,
          status: phase.status,
          progress: phase.status === 'completed' ? 100 : phase.status === 'in_progress' ? 50 : 0,
        })) || [],
        completedPhases: assessment.phases?.filter(p => p.status === 'completed').length || 0,
        totalPhases: assessment.phases?.length || 0,
      };

      // Analyze findings
      const findingsSummary = assessment.getFindingsSummary();
      const findings = {
        total: assessment.findings?.length || 0,
        bySeverity: findingsSummary,
        byStatus: assessment.findings?.reduce((acc, finding) => {
          acc[finding.status] = (acc[finding.status] || 0) + 1;
          return acc;
        }, {} as Record<string, number>) || {},
        critical: assessment.findings?.filter(f => f.severity === 'critical') || [],
      };

      // Calculate timeline
      const plannedDuration = assessment.plannedEndDate && assessment.plannedStartDate
        ? Math.ceil((assessment.plannedEndDate.getTime() - assessment.plannedStartDate.getTime()) / (1000 * 60 * 60 * 24))
        : 0;
      
      const actualDuration = assessment.getDurationInDays();
      const remainingDays = assessment.dueDate
        ? Math.ceil((assessment.dueDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
        : 0;

      const timeline = {
        plannedDuration,
        actualDuration,
        remainingDays,
        isOnTrack: !assessment.isOverdue() && remainingDays >= 0,
      };

      // Get health status
      const healthStatus = assessment.getHealthStatus();

      this.logger.debug('Assessment dashboard data retrieved', {
        assessmentId,
        overallProgress: progress.overallProgress,
        totalFindings: findings.total,
        healthStatus: healthStatus.status,
      });

      return {
        assessment,
        progress,
        findings,
        timeline,
        healthStatus,
      };

    } catch (error) {
      this.logger.error(
        `Failed to get assessment dashboard data: ${error instanceof Error ? error.message : 'Unknown error'}`,
        undefined,
        { assessmentId }
      );
      throw error;
    }
  }

  // Private helper methods

  private async cacheAssessment(assessment: ComplianceAssessment): Promise<void> {
    const cacheKey = `assessment:${assessment.id}`;
    await this.cacheService.set(cacheKey, assessment, { ttl: 1800 }); // 30 minutes
  }

  private generateDefaultPhases(assessmentType: string): any[] {
    const basePhases = [
      {
        phase: 'planning',
        name: 'Planning and Preparation',
        description: 'Assessment planning and scope definition',
        plannedStartDate: new Date(),
        plannedEndDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        status: 'not_started',
        deliverables: ['Assessment plan', 'Scope document', 'Resource allocation'],
      },
      {
        phase: 'execution',
        name: 'Assessment Execution',
        description: 'Control testing and evidence collection',
        plannedStartDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        plannedEndDate: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000), // 21 days
        status: 'not_started',
        deliverables: ['Test results', 'Evidence collection', 'Findings documentation'],
      },
      {
        phase: 'reporting',
        name: 'Reporting and Review',
        description: 'Report preparation and stakeholder review',
        plannedStartDate: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000),
        plannedEndDate: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000), // 28 days
        status: 'not_started',
        deliverables: ['Assessment report', 'Management presentation', 'Remediation plan'],
      },
    ];

    // Customize phases based on assessment type
    if (assessmentType === 'certification') {
      basePhases.push({
        phase: 'certification',
        name: 'Certification Review',
        description: 'External certification body review',
        plannedStartDate: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000),
        plannedEndDate: new Date(Date.now() + 35 * 24 * 60 * 60 * 1000), // 35 days
        status: 'not_started',
        deliverables: ['Certification decision', 'Certificate issuance', 'Surveillance schedule'],
      });
    }

    return basePhases;
  }

  private async initializeAssessmentWorkflow(assessment: ComplianceAssessment): Promise<void> {
    // Initialize workflow notifications and scheduling
    this.eventEmitter.emit('compliance.assessment.workflow.initialized', {
      assessmentId: assessment.id,
      phases: assessment.phases,
      dueDate: assessment.dueDate,
    });
  }

  private async finalizeAssessment(assessment: ComplianceAssessment): Promise<void> {
    assessment.status = 'completed';
    assessment.actualEndDate = new Date();

    // Calculate final scores
    await this.calculateAssessmentScore(assessment.id);
  }

  private calculateDomainScore(controls: any[], findings: any[]): number {
    if (controls.length === 0) return 100;

    const criticalFindings = findings.filter(f => f.severity === 'critical').length;
    const highFindings = findings.filter(f => f.severity === 'high').length;
    const mediumFindings = findings.filter(f => f.severity === 'medium').length;

    // Scoring algorithm
    let score = 100;
    score -= criticalFindings * 25;
    score -= highFindings * 15;
    score -= mediumFindings * 5;

    return Math.max(0, score);
  }

  private calculateDomainCompliance(controls: any[], findings: any[]): number {
    if (controls.length === 0) return 100;

    const failedControls = findings.filter(f => f.severity === 'high' || f.severity === 'critical').length;
    const passedControls = controls.length - failedControls;

    return Math.round((passedControls / controls.length) * 100);
  }

  private calculateOverallCompliance(domainResults: any[]): number {
    if (domainResults.length === 0) return 0;

    const totalCompliance = domainResults.reduce((sum, domain) => sum + domain.compliancePercentage, 0);
    return Math.round(totalCompliance / domainResults.length);
  }

  private performRiskAssessment(assessment: ComplianceAssessment, domainResults: any[]): any {
    const criticalFindings = assessment.findings?.filter(f => f.severity === 'critical').length || 0;
    const highFindings = assessment.findings?.filter(f => f.severity === 'high').length || 0;

    let overallRiskLevel: 'low' | 'medium' | 'high' | 'critical';
    let riskScore = 0;

    if (criticalFindings > 0) {
      overallRiskLevel = 'critical';
      riskScore = 90 + Math.min(10, criticalFindings * 2);
    } else if (highFindings > 2) {
      overallRiskLevel = 'high';
      riskScore = 70 + Math.min(20, highFindings * 3);
    } else if (highFindings > 0 || (assessment.compliancePercentage || 0) < 80) {
      overallRiskLevel = 'medium';
      riskScore = 40 + Math.min(30, (100 - (assessment.compliancePercentage || 0)));
    } else {
      overallRiskLevel = 'low';
      riskScore = Math.max(0, 40 - (assessment.compliancePercentage || 0) / 2);
    }

    return {
      overallRiskLevel,
      riskScore: Math.min(100, riskScore),
      riskFactors: [
        ...(criticalFindings > 0 ? [`${criticalFindings} critical findings`] : []),
        ...(highFindings > 0 ? [`${highFindings} high severity findings`] : []),
        ...((assessment.compliancePercentage || 0) < 80 ? ['Low compliance percentage'] : []),
      ],
    };
  }
}
