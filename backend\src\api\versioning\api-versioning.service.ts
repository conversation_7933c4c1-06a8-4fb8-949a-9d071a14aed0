import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';

import { LoggerService } from '../../infrastructure/logging/logger.service';
import { DistributedCacheService } from '../../infrastructure/cache/distributed-cache.service';

/**
 * API version information interface
 */
export interface ApiVersionInfo {
  version: string;
  status: 'active' | 'deprecated' | 'sunset';
  releaseDate: Date;
  deprecationDate?: Date;
  sunsetDate?: Date;
  supportedUntil?: Date;
  features: string[];
  breakingChanges: string[];
  migrationGuide?: string;
}

/**
 * Version compatibility interface
 */
export interface VersionCompatibility {
  sourceVersion: string;
  targetVersion: string;
  compatible: boolean;
  breakingChanges: string[];
  migrationRequired: boolean;
  migrationSteps?: string[];
}

/**
 * API deprecation warning interface
 */
export interface DeprecationWarning {
  version: string;
  feature?: string;
  endpoint?: string;
  deprecationDate: Date;
  sunsetDate?: Date;
  replacement?: string;
  migrationGuide?: string;
  severity: 'info' | 'warning' | 'critical';
}

/**
 * API versioning service providing comprehensive version management
 * Handles version detection, compatibility checks, and deprecation warnings
 */
@Injectable()
export class ApiVersioningService {
  private readonly logger = new Logger(ApiVersioningService.name);
  private readonly supportedVersions: Map<string, ApiVersionInfo> = new Map();
  private readonly versionMappings: Map<string, string> = new Map();
  private readonly deprecationWarnings: Map<string, DeprecationWarning[]> = new Map();

  constructor(
    private readonly configService: ConfigService,
    private readonly loggerService: LoggerService,
    private readonly cacheService: DistributedCacheService,
  ) {
    this.initializeVersions();
  }

  /**
   * Extract API version from request
   */
  extractVersion(request: Request): string {
    // Check header first (preferred method)
    const headerVersion = request.headers['api-version'] as string;
    if (headerVersion) {
      return this.normalizeVersion(headerVersion);
    }

    // Check URL path
    const pathMatch = request.path.match(/^\/api\/v(\d+(?:\.\d+)?)/);
    if (pathMatch) {
      return this.normalizeVersion(pathMatch[1]);
    }

    // Check query parameter
    const queryVersion = request.query['api-version'] as string;
    if (queryVersion) {
      return this.normalizeVersion(queryVersion);
    }

    // Check Accept header for version
    const acceptHeader = request.headers.accept;
    if (acceptHeader) {
      const versionMatch = acceptHeader.match(/application\/vnd\.sentinel\.v(\d+(?:\.\d+)?)/);
      if (versionMatch) {
        return this.normalizeVersion(versionMatch[1]);
      }
    }

    // Default to latest stable version
    return this.getLatestStableVersion();
  }

  /**
   * Validate API version
   */
  validateVersion(version: string): boolean {
    const normalizedVersion = this.normalizeVersion(version);
    return this.supportedVersions.has(normalizedVersion);
  }

  /**
   * Get version information
   */
  getVersionInfo(version: string): ApiVersionInfo | null {
    const normalizedVersion = this.normalizeVersion(version);
    return this.supportedVersions.get(normalizedVersion) || null;
  }

  /**
   * Check if version is deprecated
   */
  isVersionDeprecated(version: string): boolean {
    const versionInfo = this.getVersionInfo(version);
    return versionInfo?.status === 'deprecated' || versionInfo?.status === 'sunset';
  }

  /**
   * Get deprecation warnings for version
   */
  getDeprecationWarnings(version: string, endpoint?: string): DeprecationWarning[] {
    const normalizedVersion = this.normalizeVersion(version);
    const warnings = this.deprecationWarnings.get(normalizedVersion) || [];
    
    if (endpoint) {
      return warnings.filter(warning => 
        !warning.endpoint || warning.endpoint === endpoint
      );
    }
    
    return warnings;
  }

  /**
   * Check version compatibility
   */
  checkCompatibility(sourceVersion: string, targetVersion: string): VersionCompatibility {
    const source = this.getVersionInfo(sourceVersion);
    const target = this.getVersionInfo(targetVersion);

    if (!source || !target) {
      throw new BadRequestException('Invalid version specified');
    }

    const compatibility: VersionCompatibility = {
      sourceVersion,
      targetVersion,
      compatible: this.areVersionsCompatible(sourceVersion, targetVersion),
      breakingChanges: this.getBreakingChangesBetweenVersions(sourceVersion, targetVersion),
      migrationRequired: false,
    };

    compatibility.migrationRequired = compatibility.breakingChanges.length > 0;
    
    if (compatibility.migrationRequired) {
      compatibility.migrationSteps = this.getMigrationSteps(sourceVersion, targetVersion);
    }

    return compatibility;
  }

  /**
   * Get supported versions
   */
  getSupportedVersions(): ApiVersionInfo[] {
    return Array.from(this.supportedVersions.values())
      .sort((a, b) => this.compareVersions(b.version, a.version));
  }

  /**
   * Get latest stable version
   */
  getLatestStableVersion(): string {
    const activeVersions = Array.from(this.supportedVersions.values())
      .filter(v => v.status === 'active')
      .sort((a, b) => this.compareVersions(b.version, a.version));
    
    return activeVersions[0]?.version || '2.0';
  }

  /**
   * Transform response for version compatibility
   */
  async transformResponse(data: any, targetVersion: string, sourceVersion?: string): Promise<any> {
    if (!sourceVersion) {
      sourceVersion = this.getLatestStableVersion();
    }

    if (sourceVersion === targetVersion) {
      return data;
    }

    const cacheKey = `response_transform_${sourceVersion}_${targetVersion}_${JSON.stringify(data).substring(0, 100)}`;
    
    try {
      // Check cache first
      const cached = await this.cacheService.get(cacheKey);
      if (cached) {
        return cached;
      }

      // Apply version-specific transformations
      const transformed = await this.applyVersionTransformations(data, sourceVersion, targetVersion);

      // Cache for 5 minutes
      await this.cacheService.set(cacheKey, transformed, { ttl: 300 });

      return transformed;

    } catch (error) {
      this.loggerService.error('Response transformation failed', error instanceof Error ? error.message : String(error), {
        sourceVersion,
        targetVersion,
      });
      return data; // Return original data on transformation failure
    }
  }

  /**
   * Generate version migration guide
   */
  generateMigrationGuide(fromVersion: string, toVersion: string): {
    overview: string;
    breakingChanges: Array<{
      change: string;
      impact: string;
      solution: string;
      example?: string;
    }>;
    newFeatures: string[];
    deprecatedFeatures: Array<{
      feature: string;
      replacement?: string;
      timeline: string;
    }>;
    migrationSteps: Array<{
      step: number;
      title: string;
      description: string;
      code?: string;
    }>;
  } {
    const fromInfo = this.getVersionInfo(fromVersion);
    const toInfo = this.getVersionInfo(toVersion);

    if (!fromInfo || !toInfo) {
      throw new BadRequestException('Invalid version specified for migration guide');
    }

    return {
      overview: `Migration guide from API v${fromVersion} to v${toVersion}`,
      breakingChanges: this.getDetailedBreakingChanges(fromVersion, toVersion),
      newFeatures: toInfo.features.filter(f => !fromInfo.features.includes(f)),
      deprecatedFeatures: this.getDeprecatedFeatures(fromVersion, toVersion),
      migrationSteps: this.getDetailedMigrationSteps(fromVersion, toVersion),
    };
  }

  /**
   * Log version usage for analytics
   */
  async logVersionUsage(version: string, endpoint: string, userId?: string): Promise<void> {
    try {
      const logData = {
        version,
        endpoint,
        userId,
        timestamp: new Date(),
        userAgent: 'unknown', // Would be extracted from request
      };

      // Log for analytics
      this.logger.log('API version usage', logData);

      // Store in cache for analytics aggregation
      const usageKey = `version_usage_${version}_${new Date().toISOString().split('T')[0]}`;
      const currentUsage = await this.cacheService.get<number>(usageKey) || 0;
      await this.cacheService.set(usageKey, currentUsage + 1, { ttl: 86400 }); // 24 hours

    } catch (error) {
      this.logger.error('Failed to log version usage', { error: error instanceof Error ? error.message : String(error) });
    }
  }

  /**
   * Get version usage statistics
   */
  async getVersionUsageStats(timeRange: string = '7d'): Promise<{
    totalRequests: number;
    versionDistribution: Record<string, number>;
    topEndpoints: Array<{ endpoint: string; count: number; version: string }>;
    deprecatedVersionUsage: Record<string, number>;
  }> {
    try {
      // This would typically query a proper analytics database
      // For now, return mock data
      return {
        totalRequests: 10000,
        versionDistribution: {
          '2.0': 7500,
          '1.5': 2000,
          '1.0': 500,
        },
        topEndpoints: [
          { endpoint: '/api/v2/events', count: 2500, version: '2.0' },
          { endpoint: '/api/v2/vulnerabilities', count: 2000, version: '2.0' },
          { endpoint: '/api/v1/assets', count: 1500, version: '1.0' },
        ],
        deprecatedVersionUsage: {
          '1.0': 500,
          '1.5': 2000,
        },
      };

    } catch (error) {
      this.loggerService.error('Failed to get version usage stats', error instanceof Error ? error.message : String(error));
      return {
        totalRequests: 0,
        versionDistribution: {},
        topEndpoints: [],
        deprecatedVersionUsage: {},
      };
    }
  }

  // Private helper methods

  private initializeVersions(): void {
    const versions: ApiVersionInfo[] = [
      {
        version: '1.0',
        status: 'sunset',
        releaseDate: new Date('2023-01-01'),
        deprecationDate: new Date('2024-01-01'),
        sunsetDate: new Date('2024-06-01'),
        features: ['basic-events', 'basic-assets', 'basic-vulnerabilities'],
        breakingChanges: [],
      },
      {
        version: '1.5',
        status: 'deprecated',
        releaseDate: new Date('2023-06-01'),
        deprecationDate: new Date('2024-06-01'),
        sunsetDate: new Date('2025-01-01'),
        features: ['basic-events', 'basic-assets', 'basic-vulnerabilities', 'threat-intelligence', 'basic-correlation'],
        breakingChanges: ['event-schema-change', 'asset-id-format-change'],
      },
      {
        version: '2.0',
        status: 'active',
        releaseDate: new Date('2024-01-01'),
        features: [
          'advanced-events',
          'advanced-assets',
          'advanced-vulnerabilities',
          'threat-intelligence',
          'advanced-correlation',
          'real-time-streaming',
          'advanced-analytics',
          'ai-ml-integration',
          'incident-response',
          'compliance-audit',
        ],
        breakingChanges: [
          'complete-api-redesign',
          'new-authentication-model',
          'restructured-response-format',
          'new-error-handling',
        ],
      },
    ];

    versions.forEach(version => {
      this.supportedVersions.set(version.version, version);
    });

    // Initialize version mappings
    this.versionMappings.set('1', '1.0');
    this.versionMappings.set('1.0', '1.0');
    this.versionMappings.set('1.5', '1.5');
    this.versionMappings.set('2', '2.0');
    this.versionMappings.set('2.0', '2.0');

    // Initialize deprecation warnings
    this.initializeDeprecationWarnings();

    this.logger.log('API versions initialized', {
      supportedVersions: Array.from(this.supportedVersions.keys()),
      latestVersion: this.getLatestStableVersion(),
    });
  }

  private initializeDeprecationWarnings(): void {
    // Version 1.0 warnings
    this.deprecationWarnings.set('1.0', [
      {
        version: '1.0',
        deprecationDate: new Date('2024-01-01'),
        sunsetDate: new Date('2024-06-01'),
        replacement: 'v2.0',
        migrationGuide: '/docs/migration/v1-to-v2',
        severity: 'critical',
      },
    ]);

    // Version 1.5 warnings
    this.deprecationWarnings.set('1.5', [
      {
        version: '1.5',
        deprecationDate: new Date('2024-06-01'),
        sunsetDate: new Date('2025-01-01'),
        replacement: 'v2.0',
        migrationGuide: '/docs/migration/v1.5-to-v2',
        severity: 'warning',
      },
      {
        version: '1.5',
        endpoint: '/api/v1.5/events/legacy',
        feature: 'Legacy event format',
        deprecationDate: new Date('2024-03-01'),
        replacement: '/api/v2/events',
        severity: 'warning',
      },
    ]);
  }

  private normalizeVersion(version: string): string {
    // Remove 'v' prefix if present
    const cleanVersion = version.replace(/^v/, '');
    
    // Map to full version if needed
    return this.versionMappings.get(cleanVersion) || cleanVersion;
  }

  private compareVersions(a: string, b: string): number {
    const aParts = a.split('.').map(Number);
    const bParts = b.split('.').map(Number);
    
    for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {
      const aPart = aParts[i] || 0;
      const bPart = bParts[i] || 0;
      
      if (aPart > bPart) return 1;
      if (aPart < bPart) return -1;
    }
    
    return 0;
  }

  private areVersionsCompatible(sourceVersion: string, targetVersion: string): boolean {
    // Major version changes are not compatible
    const sourceMajor = parseInt(sourceVersion.split('.')[0]);
    const targetMajor = parseInt(targetVersion.split('.')[0]);
    
    return sourceMajor === targetMajor;
  }

  private getBreakingChangesBetweenVersions(fromVersion: string, toVersion: string): string[] {
    const toInfo = this.getVersionInfo(toVersion);
    return toInfo?.breakingChanges || [];
  }

  private getMigrationSteps(fromVersion: string, toVersion: string): string[] {
    // Return version-specific migration steps
    if (fromVersion === '1.0' && toVersion === '2.0') {
      return [
        'Update authentication to use JWT tokens',
        'Migrate to new response format',
        'Update error handling',
        'Migrate event schema',
        'Update asset ID format',
      ];
    }
    
    if (fromVersion === '1.5' && toVersion === '2.0') {
      return [
        'Update authentication headers',
        'Migrate to new response format',
        'Update error handling',
      ];
    }
    
    return [];
  }

  private async applyVersionTransformations(data: any, fromVersion: string, toVersion: string): Promise<any> {
    // Apply version-specific data transformations
    let transformed = { ...data };

    // Example transformations
    if (fromVersion === '1.0' && toVersion === '2.0') {
      // Transform v1.0 format to v2.0
      transformed = this.transformV1ToV2(transformed);
    } else if (fromVersion === '1.5' && toVersion === '2.0') {
      // Transform v1.5 format to v2.0
      transformed = this.transformV15ToV2(transformed);
    }

    return transformed;
  }

  private transformV1ToV2(data: any): any {
    // Example transformation from v1.0 to v2.0
    if (data.events) {
      data.events = data.events.map((event: any) => ({
        ...event,
        metadata: event.meta || {},
        timestamp: event.time || event.timestamp,
      }));
    }
    
    return data;
  }

  private transformV15ToV2(data: any): any {
    // Example transformation from v1.5 to v2.0
    if (data.pagination) {
      data.pagination = {
        ...data.pagination,
        hasMore: data.pagination.has_more,
      };
      delete data.pagination.has_more;
    }
    
    return data;
  }

  private getDetailedBreakingChanges(fromVersion: string, toVersion: string): Array<{
    change: string;
    impact: string;
    solution: string;
    example?: string;
  }> {
    // Return detailed breaking changes with solutions
    return [
      {
        change: 'Authentication model changed',
        impact: 'API keys no longer supported',
        solution: 'Migrate to JWT token authentication',
        example: 'Authorization: Bearer <jwt-token>',
      },
      {
        change: 'Response format restructured',
        impact: 'Response wrapper changed',
        solution: 'Update response parsing logic',
        example: '{ "success": true, "data": {...}, "metadata": {...} }',
      },
    ];
  }

  private getDeprecatedFeatures(fromVersion: string, toVersion: string): Array<{
    feature: string;
    replacement?: string;
    timeline: string;
  }> {
    return [
      {
        feature: 'Legacy event format',
        replacement: 'New structured event format',
        timeline: 'Deprecated in v1.5, removed in v2.0',
      },
    ];
  }

  private getDetailedMigrationSteps(fromVersion: string, toVersion: string): Array<{
    step: number;
    title: string;
    description: string;
    code?: string;
  }> {
    return [
      {
        step: 1,
        title: 'Update Authentication',
        description: 'Replace API key authentication with JWT tokens',
        code: `// Old (v1.x)
fetch('/api/v1/events', {
  headers: { 'X-API-Key': 'your-api-key' }
});

// New (v2.0)
fetch('/api/v2/events', {
  headers: { 'Authorization': 'Bearer your-jwt-token' }
});`,
      },
      {
        step: 2,
        title: 'Update Response Handling',
        description: 'Adapt to new response format structure',
        code: `// Old (v1.x)
const events = response.events;

// New (v2.0)
const events = response.data.events;`,
      },
    ];
  }
}
