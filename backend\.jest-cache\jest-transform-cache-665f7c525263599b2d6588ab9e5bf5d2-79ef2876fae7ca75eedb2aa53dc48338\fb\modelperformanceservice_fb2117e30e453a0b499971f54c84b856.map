{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\optimization\\model-performance.service.ts", "mappings": ";;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAE/C;;;;;;GAMG;AAEI,IAAM,uBAAuB,+BAA7B,MAAM,uBAAuB;IAKlC,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAJxC,WAAM,GAAG,IAAI,eAAM,CAAC,yBAAuB,CAAC,IAAI,CAAC,CAAC;QAClD,oBAAe,GAAG,IAAI,GAAG,EAAgC,CAAC;QAC1D,aAAQ,GAAG,IAAI,GAAG,EAAyB,CAAC;IAED,CAAC;IAE7D;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,OAAe;QACnC,MAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAE/C,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,wCAAwC;YACxC,OAAO;gBACL,QAAQ,EAAE,GAAG;gBACb,SAAS,EAAE,GAAG;gBACd,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,GAAG;gBACZ,cAAc,EAAE,IAAI;gBACpB,UAAU,EAAE,EAAE;gBACd,YAAY,EAAE,IAAI;gBAClB,aAAa,EAAE,CAAC;gBAChB,kBAAkB,EAAE,CAAC;gBACrB,cAAc,EAAE,CAAC;gBACjB,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC;QACJ,CAAC;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,OAAe;QAClC,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAExC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO;gBACL,cAAc,EAAE,IAAI;gBACpB,SAAS,EAAE,CAAC;gBACZ,SAAS,EAAE,QAAQ;gBACnB,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC;QACJ,CAAC;QAED,OAAO;YACL,cAAc,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;YAChE,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YACxC,WAAW,EAAE,IAAI,CAAC,WAAW;SAC9B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CACtB,OAAe,EACf,MAA0B;QAE1B,IAAI,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAE7C,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,GAAG;gBACL,OAAO;gBACP,eAAe,EAAE,EAAE;gBACnB,cAAc,EAAE,EAAE;gBAClB,cAAc,EAAE,EAAE;gBAClB,YAAY,EAAE,EAAE;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC;YACF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAC1C,CAAC;QAED,8BAA8B;QAC9B,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAClC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;gBACxB,KAAK,EAAE,MAAM,CAAC,QAAQ;gBACtB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACzC,CAAC;QAED,6BAA6B;QAC7B,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;gBACvB,KAAK,EAAE,MAAM,CAAC,OAAO;gBACrB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACxC,CAAC;QAED,uCAAuC;QACvC,IAAI,MAAM,CAAC,gBAAgB,KAAK,SAAS,EAAE,CAAC;YAC1C,8CAA8C;YAC9C,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;gBACxB,KAAK,EAAE,MAAM,CAAC,gBAAgB,GAAG,CAAC,EAAE,2BAA2B;gBAC/D,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,IAAI,EAAE,mBAAmB;aAC1B,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QAE9B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0CAA0C,OAAO,EAAE,CAAC,CAAC;IACzE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACjB,OAAe,EACf,OAAsB;QAEtB,IAAI,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAE7C,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,GAAG;gBACL,OAAO;gBACP,eAAe,EAAE,EAAE;gBACnB,cAAc,EAAE,EAAE;gBAClB,cAAc,EAAE,EAAE;gBAClB,YAAY,EAAE,EAAE;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC;YACF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAC1C,CAAC;QAED,iBAAiB;QACjB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;YACvB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,IAAI,EAAE,OAAO,CAAC,IAAI;SACnB,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEtC,iBAAiB;QACjB,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;gBACvB,KAAK,EAAE,OAAO,CAAC,OAAO;gBACtB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACxC,CAAC;QAED,iCAAiC;QACjC,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YACtC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;gBACrB,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YACH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC;QAED,mBAAmB;QACnB,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CAAC,OAAe;QACxC,MAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAE/C,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO;gBACL,aAAa,EAAE,QAAQ;gBACvB,YAAY,EAAE,QAAQ;gBACtB,cAAc,EAAE,QAAQ;gBACxB,eAAe,EAAE,QAAQ;aAC1B,CAAC;QACJ,CAAC;QAED,OAAO;YACL,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YAC1E,YAAY,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YACxE,cAAc,EAAE,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC;YAClD,eAAe,EAAE,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC;SACrD,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,QAAkB;QACpC,MAAM,WAAW,GAAsB,EAAE,CAAC;QAE1C,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACpD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACvD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAExD,WAAW,CAAC,IAAI,CAAC;gBACf,OAAO;gBACP,OAAO;gBACP,WAAW;gBACX,MAAM;gBACN,YAAY,EAAE,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,WAAW,CAAC;aAC/D,CAAC,CAAC;QACL,CAAC;QAED,OAAO,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB;QACxB,MAAM,MAAM,GAAuB,EAAE,CAAC;QAEtC,KAAK,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACnD,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAE5C,6BAA6B;YAC7B,IAAI,OAAO,CAAC,QAAQ,GAAG,GAAG,EAAE,CAAC;gBAC3B,MAAM,CAAC,IAAI,CAAC;oBACV,OAAO;oBACP,IAAI,EAAE,sBAAsB;oBAC5B,QAAQ,EAAE,OAAO,CAAC,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ;oBACpD,OAAO,EAAE,6BAA6B,CAAC,OAAO,CAAC,QAAQ,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG;oBAC5E,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;YACL,CAAC;YAED,qBAAqB;YACrB,IAAI,OAAO,CAAC,cAAc,GAAG,IAAI,EAAE,CAAC;gBAClC,MAAM,CAAC,IAAI,CAAC;oBACV,OAAO;oBACP,IAAI,EAAE,cAAc;oBACpB,QAAQ,EAAE,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ;oBAC5D,OAAO,EAAE,8BAA8B,OAAO,CAAC,cAAc,IAAI;oBACjE,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;YACL,CAAC;YAED,wBAAwB;YACxB,MAAM,SAAS,GAAG,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;YAC9E,IAAI,SAAS,GAAG,GAAG,EAAE,CAAC;gBACpB,MAAM,CAAC,IAAI,CAAC;oBACV,OAAO;oBACP,IAAI,EAAE,iBAAiB;oBACvB,QAAQ,EAAE,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ;oBAC7C,OAAO,EAAE,iCAAiC,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG;oBACzE,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,8BAA8B,CAAC,OAAe;QAClD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QACpD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACxD,MAAM,eAAe,GAAiC,EAAE,CAAC;QAEzD,2BAA2B;QAC3B,IAAI,OAAO,CAAC,QAAQ,GAAG,IAAI,EAAE,CAAC;YAC5B,eAAe,CAAC,IAAI,CAAC;gBACnB,IAAI,EAAE,sBAAsB;gBAC5B,QAAQ,EAAE,MAAM;gBAChB,WAAW,EAAE,sDAAsD;gBACnE,cAAc,EAAE,2BAA2B;gBAC3C,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;QACL,CAAC;QAED,0BAA0B;QAC1B,IAAI,OAAO,CAAC,cAAc,GAAG,IAAI,EAAE,CAAC;YAClC,eAAe,CAAC,IAAI,CAAC;gBACnB,IAAI,EAAE,sBAAsB;gBAC5B,QAAQ,EAAE,QAAQ;gBAClB,WAAW,EAAE,qDAAqD;gBAClE,cAAc,EAAE,0BAA0B;gBAC1C,MAAM,EAAE,QAAQ;aACjB,CAAC,CAAC;QACL,CAAC;QAED,uBAAuB;QACvB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QACvD,IAAI,WAAW,CAAC,cAAc,GAAG,IAAI,EAAE,CAAC;YACtC,eAAe,CAAC,IAAI,CAAC;gBACnB,IAAI,EAAE,mBAAmB;gBACzB,QAAQ,EAAE,QAAQ;gBAClB,WAAW,EAAE,oDAAoD;gBACjE,cAAc,EAAE,uBAAuB;gBACvC,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;QACL,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,yBAAyB;IAEjB,gBAAgB,CAAC,IAA0B;QACjD,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,oBAAoB;QAC5E,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,+BAA+B;QACvF,MAAM,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,gCAAgC;QAE1F,MAAM,aAAa,GAAG,cAAc,CAAC,MAAM,CAAC;QAC5C,MAAM,kBAAkB,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QACxE,MAAM,cAAc,GAAG,aAAa,GAAG,kBAAkB,CAAC;QAE1D,MAAM,cAAc,GAAG,eAAe,CAAC,MAAM,GAAG,CAAC;YAC/C,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,eAAe,CAAC,MAAM;YAC/E,CAAC,CAAC,IAAI,CAAC;QAET,MAAM,QAAQ,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC;YAC1C,CAAC,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,gBAAgB,CAAC,MAAM;YACjF,CAAC,CAAC,GAAG,CAAC;QAER,MAAM,UAAU,GAAG,aAAa,GAAG,CAAC;YAClC,CAAC,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC;YAC7E,CAAC,CAAC,CAAC,CAAC;QAEN,OAAO;YACL,QAAQ;YACR,SAAS,EAAE,QAAQ,GAAG,IAAI,EAAE,gBAAgB;YAC5C,MAAM,EAAE,QAAQ,GAAG,GAAG,EAAE,gBAAgB;YACxC,OAAO,EAAE,QAAQ,GAAG,IAAI,EAAE,gBAAgB;YAC1C,cAAc;YACd,UAAU;YACV,YAAY,EAAE,kBAAkB,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC;YAC7D,aAAa;YACb,kBAAkB;YAClB,cAAc;YACd,WAAW,EAAE,IAAI,CAAC,WAAW;SAC9B,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,OAAe,EAAE,IAAY;QACxD,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAEtC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,GAAG;gBACL,OAAO;gBACP,SAAS,EAAE,CAAC;gBACZ,aAAa,EAAE,CAAC;gBAChB,WAAW,EAAE,EAAE;gBACf,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC;YACF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACnC,CAAC;QAED,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC;QACvB,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;YACpB,IAAI;YACJ,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACnC,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;IAChC,CAAC;IAEO,kBAAkB,CAAC,IAAmB;QAC5C,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACjC,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;QAE/C,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QAC7E,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;QAE1E,MAAM,MAAM,GAAG,CAAC,SAAS,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC;QAEjD,IAAI,MAAM,GAAG,GAAG;YAAE,OAAO,YAAY,CAAC;QACtC,IAAI,MAAM,GAAG,CAAC,GAAG;YAAE,OAAO,YAAY,CAAC;QACvC,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,cAAc,CAAC,MAAgB;QACrC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtB,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAEpC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QACxE,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;QAErE,MAAM,MAAM,GAAG,CAAC,SAAS,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC;QAEjD,IAAI,MAAM,GAAG,IAAI;YAAE,OAAO,WAAW,CAAC;QACtC,IAAI,MAAM,GAAG,CAAC,IAAI;YAAE,OAAO,WAAW,CAAC;QACvC,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,uBAAuB,CAAC,IAA0B;QACxD,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QACtD,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;QAE1D,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9D,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,MAAM,eAAe,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC;QAC9F,MAAM,cAAc,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC;QAE3F,MAAM,MAAM,GAAG,eAAe,GAAG,cAAc,CAAC;QAEhD,IAAI,MAAM,GAAG,IAAI;YAAE,OAAO,WAAW,CAAC;QACtC,IAAI,MAAM,GAAG,CAAC,IAAI;YAAE,OAAO,WAAW,CAAC;QACvC,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,wBAAwB,CAAC,IAA0B;QACzD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,UAAU,GAAG,GAAG,GAAG,OAAO,CAAC;QACjC,MAAM,WAAW,GAAG,GAAG,GAAG,OAAO,CAAC;QAElC,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAC/C,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,UAAU,CACxC,CAAC,MAAM,CAAC;QAET,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAC9C,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,WAAW,IAAI,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,UAAU,CAChF,CAAC,MAAM,CAAC;QAET,IAAI,aAAa,KAAK,CAAC,EAAE,CAAC;YACxB,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,MAAM,MAAM,GAAG,CAAC,cAAc,GAAG,aAAa,CAAC,GAAG,aAAa,CAAC;QAEhE,IAAI,MAAM,GAAG,GAAG;YAAE,OAAO,WAAW,CAAC;QACrC,IAAI,MAAM,GAAG,CAAC,GAAG;YAAE,OAAO,WAAW,CAAC;QACtC,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,qBAAqB,CAC3B,OAAqB,EACrB,WAA6B;QAE7B,MAAM,aAAa,GAAG,OAAO,CAAC,QAAQ,CAAC;QACvC,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC,CAAC,CAAC;QACvE,MAAM,iBAAiB,GAAG,OAAO,CAAC,YAAY,CAAC;QAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,CAAC,cAAc,GAAG,GAAG,CAAC,CAAC,CAAC;QAEtE,OAAO,CACL,aAAa,GAAG,GAAG;YACnB,YAAY,GAAG,IAAI;YACnB,iBAAiB,GAAG,IAAI;YACxB,SAAS,GAAG,GAAG,CAChB,CAAC;IACJ,CAAC;IAEO,WAAW,CAAI,OAAY,EAAE,YAAoB,IAAI;QAC3D,IAAI,OAAO,CAAC,MAAM,GAAG,SAAS,EAAE,CAAC;YAC/B,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;CACF,CAAA;AA1dY,0DAAuB;kCAAvB,uBAAuB;IADnC,IAAA,mBAAU,GAAE;yDAMiC,sBAAa,oBAAb,sBAAa;GAL9C,uBAAuB,CA0dnC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\optimization\\model-performance.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\n\r\n/**\r\n * Model Performance Service\r\n * \r\n * Tracks and analyzes AI model performance metrics including\r\n * accuracy, latency, cost, and availability. Provides performance\r\n * optimization recommendations and trend analysis.\r\n */\r\n@Injectable()\r\nexport class ModelPerformanceService {\r\n  private readonly logger = new Logger(ModelPerformanceService.name);\r\n  private readonly performanceData = new Map<string, ModelPerformanceData>();\r\n  private readonly costData = new Map<string, ModelCostData>();\r\n\r\n  constructor(private readonly configService: ConfigService) {}\r\n\r\n  /**\r\n   * Gets performance metrics for a model\r\n   */\r\n  async getModelMetrics(modelId: string): Promise<ModelMetrics> {\r\n    const data = this.performanceData.get(modelId);\r\n    \r\n    if (!data) {\r\n      // Return default metrics for new models\r\n      return {\r\n        accuracy: 0.8,\r\n        precision: 0.8,\r\n        recall: 0.8,\r\n        f1Score: 0.8,\r\n        averageLatency: 2000,\r\n        throughput: 10,\r\n        availability: 0.95,\r\n        totalRequests: 0,\r\n        successfulRequests: 0,\r\n        failedRequests: 0,\r\n        lastUpdated: new Date(),\r\n      };\r\n    }\r\n\r\n    return this.calculateMetrics(data);\r\n  }\r\n\r\n  /**\r\n   * Gets cost metrics for a model\r\n   */\r\n  async getCostMetrics(modelId: string): Promise<ModelCostMetrics> {\r\n    const data = this.costData.get(modelId);\r\n    \r\n    if (!data) {\r\n      return {\r\n        costPerRequest: 0.01,\r\n        totalCost: 0,\r\n        costTrend: 'stable',\r\n        lastUpdated: new Date(),\r\n      };\r\n    }\r\n\r\n    return {\r\n      costPerRequest: data.totalCost / Math.max(data.totalRequests, 1),\r\n      totalCost: data.totalCost,\r\n      costTrend: this.calculateCostTrend(data),\r\n      lastUpdated: data.lastUpdated,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Updates model performance metrics\r\n   */\r\n  async updateModelMetrics(\r\n    modelId: string, \r\n    update: ModelMetricsUpdate\r\n  ): Promise<void> {\r\n    let data = this.performanceData.get(modelId);\r\n    \r\n    if (!data) {\r\n      data = {\r\n        modelId,\r\n        accuracyHistory: [],\r\n        latencyHistory: [],\r\n        requestHistory: [],\r\n        errorHistory: [],\r\n        createdAt: new Date(),\r\n        lastUpdated: new Date(),\r\n      };\r\n      this.performanceData.set(modelId, data);\r\n    }\r\n\r\n    // Update accuracy if provided\r\n    if (update.accuracy !== undefined) {\r\n      data.accuracyHistory.push({\r\n        value: update.accuracy,\r\n        timestamp: new Date(),\r\n      });\r\n      this.trimHistory(data.accuracyHistory);\r\n    }\r\n\r\n    // Update latency if provided\r\n    if (update.latency !== undefined) {\r\n      data.latencyHistory.push({\r\n        value: update.latency,\r\n        timestamp: new Date(),\r\n      });\r\n      this.trimHistory(data.latencyHistory);\r\n    }\r\n\r\n    // Update user satisfaction if provided\r\n    if (update.userSatisfaction !== undefined) {\r\n      // Store user satisfaction as a special metric\r\n      data.accuracyHistory.push({\r\n        value: update.userSatisfaction / 5, // Convert 1-5 scale to 0-1\r\n        timestamp: new Date(),\r\n        type: 'user_satisfaction',\r\n      });\r\n    }\r\n\r\n    data.lastUpdated = new Date();\r\n    \r\n    this.logger.debug(`Updated performance metrics for model: ${modelId}`);\r\n  }\r\n\r\n  /**\r\n   * Records a request completion\r\n   */\r\n  async recordRequest(\r\n    modelId: string,\r\n    request: RequestRecord\r\n  ): Promise<void> {\r\n    let data = this.performanceData.get(modelId);\r\n    \r\n    if (!data) {\r\n      data = {\r\n        modelId,\r\n        accuracyHistory: [],\r\n        latencyHistory: [],\r\n        requestHistory: [],\r\n        errorHistory: [],\r\n        createdAt: new Date(),\r\n        lastUpdated: new Date(),\r\n      };\r\n      this.performanceData.set(modelId, data);\r\n    }\r\n\r\n    // Record request\r\n    data.requestHistory.push({\r\n      timestamp: new Date(),\r\n      success: request.success,\r\n      latency: request.latency,\r\n      cost: request.cost,\r\n    });\r\n    this.trimHistory(data.requestHistory);\r\n\r\n    // Record latency\r\n    if (request.latency) {\r\n      data.latencyHistory.push({\r\n        value: request.latency,\r\n        timestamp: new Date(),\r\n      });\r\n      this.trimHistory(data.latencyHistory);\r\n    }\r\n\r\n    // Record error if request failed\r\n    if (!request.success && request.error) {\r\n      data.errorHistory.push({\r\n        error: request.error,\r\n        timestamp: new Date(),\r\n      });\r\n      this.trimHistory(data.errorHistory);\r\n    }\r\n\r\n    // Update cost data\r\n    if (request.cost) {\r\n      await this.updateCostData(modelId, request.cost);\r\n    }\r\n\r\n    data.lastUpdated = new Date();\r\n  }\r\n\r\n  /**\r\n   * Gets performance trends for a model\r\n   */\r\n  async getPerformanceTrends(modelId: string): Promise<PerformanceTrends> {\r\n    const data = this.performanceData.get(modelId);\r\n    \r\n    if (!data) {\r\n      return {\r\n        accuracyTrend: 'stable',\r\n        latencyTrend: 'stable',\r\n        errorRateTrend: 'stable',\r\n        throughputTrend: 'stable',\r\n      };\r\n    }\r\n\r\n    return {\r\n      accuracyTrend: this.calculateTrend(data.accuracyHistory.map(h => h.value)),\r\n      latencyTrend: this.calculateTrend(data.latencyHistory.map(h => h.value)),\r\n      errorRateTrend: this.calculateErrorRateTrend(data),\r\n      throughputTrend: this.calculateThroughputTrend(data),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Gets performance comparison between models\r\n   */\r\n  async compareModels(modelIds: string[]): Promise<ModelComparison[]> {\r\n    const comparisons: ModelComparison[] = [];\r\n    \r\n    for (const modelId of modelIds) {\r\n      const metrics = await this.getModelMetrics(modelId);\r\n      const costMetrics = await this.getCostMetrics(modelId);\r\n      const trends = await this.getPerformanceTrends(modelId);\r\n      \r\n      comparisons.push({\r\n        modelId,\r\n        metrics,\r\n        costMetrics,\r\n        trends,\r\n        overallScore: this.calculateOverallScore(metrics, costMetrics),\r\n      });\r\n    }\r\n    \r\n    return comparisons.sort((a, b) => b.overallScore - a.overallScore);\r\n  }\r\n\r\n  /**\r\n   * Gets performance alerts for models\r\n   */\r\n  async getPerformanceAlerts(): Promise<PerformanceAlert[]> {\r\n    const alerts: PerformanceAlert[] = [];\r\n    \r\n    for (const [modelId, data] of this.performanceData) {\r\n      const metrics = this.calculateMetrics(data);\r\n      \r\n      // Check accuracy degradation\r\n      if (metrics.accuracy < 0.8) {\r\n        alerts.push({\r\n          modelId,\r\n          type: 'accuracy_degradation',\r\n          severity: metrics.accuracy < 0.7 ? 'high' : 'medium',\r\n          message: `Model accuracy dropped to ${(metrics.accuracy * 100).toFixed(1)}%`,\r\n          timestamp: new Date(),\r\n        });\r\n      }\r\n      \r\n      // Check high latency\r\n      if (metrics.averageLatency > 5000) {\r\n        alerts.push({\r\n          modelId,\r\n          type: 'high_latency',\r\n          severity: metrics.averageLatency > 10000 ? 'high' : 'medium',\r\n          message: `Model latency increased to ${metrics.averageLatency}ms`,\r\n          timestamp: new Date(),\r\n        });\r\n      }\r\n      \r\n      // Check high error rate\r\n      const errorRate = metrics.failedRequests / Math.max(metrics.totalRequests, 1);\r\n      if (errorRate > 0.1) {\r\n        alerts.push({\r\n          modelId,\r\n          type: 'high_error_rate',\r\n          severity: errorRate > 0.2 ? 'high' : 'medium',\r\n          message: `Model error rate increased to ${(errorRate * 100).toFixed(1)}%`,\r\n          timestamp: new Date(),\r\n        });\r\n      }\r\n    }\r\n    \r\n    return alerts;\r\n  }\r\n\r\n  /**\r\n   * Gets optimization recommendations for a model\r\n   */\r\n  async getOptimizationRecommendations(modelId: string): Promise<OptimizationRecommendation[]> {\r\n    const metrics = await this.getModelMetrics(modelId);\r\n    const trends = await this.getPerformanceTrends(modelId);\r\n    const recommendations: OptimizationRecommendation[] = [];\r\n    \r\n    // Accuracy recommendations\r\n    if (metrics.accuracy < 0.85) {\r\n      recommendations.push({\r\n        type: 'accuracy_improvement',\r\n        priority: 'high',\r\n        description: 'Consider retraining the model with more diverse data',\r\n        expectedImpact: 'Improve accuracy by 5-10%',\r\n        effort: 'high',\r\n      });\r\n    }\r\n    \r\n    // Latency recommendations\r\n    if (metrics.averageLatency > 3000) {\r\n      recommendations.push({\r\n        type: 'latency_optimization',\r\n        priority: 'medium',\r\n        description: 'Optimize model parameters or use model quantization',\r\n        expectedImpact: 'Reduce latency by 20-30%',\r\n        effort: 'medium',\r\n      });\r\n    }\r\n    \r\n    // Cost recommendations\r\n    const costMetrics = await this.getCostMetrics(modelId);\r\n    if (costMetrics.costPerRequest > 0.02) {\r\n      recommendations.push({\r\n        type: 'cost_optimization',\r\n        priority: 'medium',\r\n        description: 'Consider using a more cost-effective model variant',\r\n        expectedImpact: 'Reduce cost by 30-50%',\r\n        effort: 'low',\r\n      });\r\n    }\r\n    \r\n    return recommendations;\r\n  }\r\n\r\n  // Private helper methods\r\n\r\n  private calculateMetrics(data: ModelPerformanceData): ModelMetrics {\r\n    const recentRequests = data.requestHistory.slice(-100); // Last 100 requests\r\n    const recentLatencies = data.latencyHistory.slice(-50); // Last 50 latency measurements\r\n    const recentAccuracies = data.accuracyHistory.slice(-20); // Last 20 accuracy measurements\r\n    \r\n    const totalRequests = recentRequests.length;\r\n    const successfulRequests = recentRequests.filter(r => r.success).length;\r\n    const failedRequests = totalRequests - successfulRequests;\r\n    \r\n    const averageLatency = recentLatencies.length > 0\r\n      ? recentLatencies.reduce((sum, l) => sum + l.value, 0) / recentLatencies.length\r\n      : 2000;\r\n    \r\n    const accuracy = recentAccuracies.length > 0\r\n      ? recentAccuracies.reduce((sum, a) => sum + a.value, 0) / recentAccuracies.length\r\n      : 0.8;\r\n    \r\n    const throughput = totalRequests > 0 \r\n      ? totalRequests / Math.max(1, (Date.now() - data.createdAt.getTime()) / 1000)\r\n      : 0;\r\n    \r\n    return {\r\n      accuracy,\r\n      precision: accuracy * 0.95, // Approximation\r\n      recall: accuracy * 0.9, // Approximation\r\n      f1Score: accuracy * 0.92, // Approximation\r\n      averageLatency,\r\n      throughput,\r\n      availability: successfulRequests / Math.max(totalRequests, 1),\r\n      totalRequests,\r\n      successfulRequests,\r\n      failedRequests,\r\n      lastUpdated: data.lastUpdated,\r\n    };\r\n  }\r\n\r\n  private async updateCostData(modelId: string, cost: number): Promise<void> {\r\n    let data = this.costData.get(modelId);\r\n    \r\n    if (!data) {\r\n      data = {\r\n        modelId,\r\n        totalCost: 0,\r\n        totalRequests: 0,\r\n        costHistory: [],\r\n        createdAt: new Date(),\r\n        lastUpdated: new Date(),\r\n      };\r\n      this.costData.set(modelId, data);\r\n    }\r\n    \r\n    data.totalCost += cost;\r\n    data.totalRequests++;\r\n    data.costHistory.push({\r\n      cost,\r\n      timestamp: new Date(),\r\n    });\r\n    \r\n    this.trimHistory(data.costHistory);\r\n    data.lastUpdated = new Date();\r\n  }\r\n\r\n  private calculateCostTrend(data: ModelCostData): 'increasing' | 'decreasing' | 'stable' {\r\n    if (data.costHistory.length < 10) {\r\n      return 'stable';\r\n    }\r\n    \r\n    const recent = data.costHistory.slice(-10);\r\n    const older = data.costHistory.slice(-20, -10);\r\n    \r\n    const recentAvg = recent.reduce((sum, c) => sum + c.cost, 0) / recent.length;\r\n    const olderAvg = older.reduce((sum, c) => sum + c.cost, 0) / older.length;\r\n    \r\n    const change = (recentAvg - olderAvg) / olderAvg;\r\n    \r\n    if (change > 0.1) return 'increasing';\r\n    if (change < -0.1) return 'decreasing';\r\n    return 'stable';\r\n  }\r\n\r\n  private calculateTrend(values: number[]): 'improving' | 'degrading' | 'stable' {\r\n    if (values.length < 5) {\r\n      return 'stable';\r\n    }\r\n    \r\n    const recent = values.slice(-5);\r\n    const older = values.slice(-10, -5);\r\n    \r\n    if (older.length === 0) {\r\n      return 'stable';\r\n    }\r\n    \r\n    const recentAvg = recent.reduce((sum, v) => sum + v, 0) / recent.length;\r\n    const olderAvg = older.reduce((sum, v) => sum + v, 0) / older.length;\r\n    \r\n    const change = (recentAvg - olderAvg) / olderAvg;\r\n    \r\n    if (change > 0.05) return 'improving';\r\n    if (change < -0.05) return 'degrading';\r\n    return 'stable';\r\n  }\r\n\r\n  private calculateErrorRateTrend(data: ModelPerformanceData): 'improving' | 'degrading' | 'stable' {\r\n    const recentRequests = data.requestHistory.slice(-20);\r\n    const olderRequests = data.requestHistory.slice(-40, -20);\r\n    \r\n    if (recentRequests.length === 0 || olderRequests.length === 0) {\r\n      return 'stable';\r\n    }\r\n    \r\n    const recentErrorRate = recentRequests.filter(r => !r.success).length / recentRequests.length;\r\n    const olderErrorRate = olderRequests.filter(r => !r.success).length / olderRequests.length;\r\n    \r\n    const change = recentErrorRate - olderErrorRate;\r\n    \r\n    if (change > 0.05) return 'degrading';\r\n    if (change < -0.05) return 'improving';\r\n    return 'stable';\r\n  }\r\n\r\n  private calculateThroughputTrend(data: ModelPerformanceData): 'improving' | 'degrading' | 'stable' {\r\n    const now = Date.now();\r\n    const oneHourAgo = now - 3600000;\r\n    const twoHoursAgo = now - 7200000;\r\n    \r\n    const recentRequests = data.requestHistory.filter(\r\n      r => r.timestamp.getTime() > oneHourAgo\r\n    ).length;\r\n    \r\n    const olderRequests = data.requestHistory.filter(\r\n      r => r.timestamp.getTime() > twoHoursAgo && r.timestamp.getTime() <= oneHourAgo\r\n    ).length;\r\n    \r\n    if (olderRequests === 0) {\r\n      return 'stable';\r\n    }\r\n    \r\n    const change = (recentRequests - olderRequests) / olderRequests;\r\n    \r\n    if (change > 0.2) return 'improving';\r\n    if (change < -0.2) return 'degrading';\r\n    return 'stable';\r\n  }\r\n\r\n  private calculateOverallScore(\r\n    metrics: ModelMetrics, \r\n    costMetrics: ModelCostMetrics\r\n  ): number {\r\n    const accuracyScore = metrics.accuracy;\r\n    const latencyScore = Math.max(0, 1 - (metrics.averageLatency / 10000));\r\n    const availabilityScore = metrics.availability;\r\n    const costScore = Math.max(0, 1 - (costMetrics.costPerRequest / 0.1));\r\n    \r\n    return (\r\n      accuracyScore * 0.3 +\r\n      latencyScore * 0.25 +\r\n      availabilityScore * 0.25 +\r\n      costScore * 0.2\r\n    );\r\n  }\r\n\r\n  private trimHistory<T>(history: T[], maxLength: number = 1000): void {\r\n    if (history.length > maxLength) {\r\n      history.splice(0, history.length - maxLength);\r\n    }\r\n  }\r\n}\r\n\r\n// Type definitions\r\ninterface ModelPerformanceData {\r\n  modelId: string;\r\n  accuracyHistory: Array<{ value: number; timestamp: Date; type?: string }>;\r\n  latencyHistory: Array<{ value: number; timestamp: Date }>;\r\n  requestHistory: Array<{ timestamp: Date; success: boolean; latency?: number; cost?: number }>;\r\n  errorHistory: Array<{ error: string; timestamp: Date }>;\r\n  createdAt: Date;\r\n  lastUpdated: Date;\r\n}\r\n\r\ninterface ModelCostData {\r\n  modelId: string;\r\n  totalCost: number;\r\n  totalRequests: number;\r\n  costHistory: Array<{ cost: number; timestamp: Date }>;\r\n  createdAt: Date;\r\n  lastUpdated: Date;\r\n}\r\n\r\ninterface ModelMetrics {\r\n  accuracy: number;\r\n  precision: number;\r\n  recall: number;\r\n  f1Score: number;\r\n  averageLatency: number;\r\n  throughput: number;\r\n  availability: number;\r\n  totalRequests: number;\r\n  successfulRequests: number;\r\n  failedRequests: number;\r\n  lastUpdated: Date;\r\n}\r\n\r\ninterface ModelCostMetrics {\r\n  costPerRequest: number;\r\n  totalCost: number;\r\n  costTrend: 'increasing' | 'decreasing' | 'stable';\r\n  lastUpdated: Date;\r\n}\r\n\r\ninterface ModelMetricsUpdate {\r\n  accuracy?: number;\r\n  latency?: number;\r\n  userSatisfaction?: number;\r\n}\r\n\r\ninterface RequestRecord {\r\n  success: boolean;\r\n  latency?: number;\r\n  cost?: number;\r\n  error?: string;\r\n}\r\n\r\ninterface PerformanceTrends {\r\n  accuracyTrend: 'improving' | 'degrading' | 'stable';\r\n  latencyTrend: 'improving' | 'degrading' | 'stable';\r\n  errorRateTrend: 'improving' | 'degrading' | 'stable';\r\n  throughputTrend: 'improving' | 'degrading' | 'stable';\r\n}\r\n\r\ninterface ModelComparison {\r\n  modelId: string;\r\n  metrics: ModelMetrics;\r\n  costMetrics: ModelCostMetrics;\r\n  trends: PerformanceTrends;\r\n  overallScore: number;\r\n}\r\n\r\ninterface PerformanceAlert {\r\n  modelId: string;\r\n  type: 'accuracy_degradation' | 'high_latency' | 'high_error_rate';\r\n  severity: 'low' | 'medium' | 'high';\r\n  message: string;\r\n  timestamp: Date;\r\n}\r\n\r\ninterface OptimizationRecommendation {\r\n  type: 'accuracy_improvement' | 'latency_optimization' | 'cost_optimization';\r\n  priority: 'low' | 'medium' | 'high';\r\n  description: string;\r\n  expectedImpact: string;\r\n  effort: 'low' | 'medium' | 'high';\r\n}"], "version": 3}