import { createParamDecorator, ExecutionContext } from '@nestjs/common';

/**
 * Interface for the authenticated user object
 */
export interface AuthenticatedUser {
  id: string;
  email: string;
  username?: string;
  roles: string[];
  permissions?: string[];
  tenantId?: string;
  isActive: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Current User decorator to extract the authenticated user from the request
 * 
 * @param data - Optional property name to extract from the user object
 * @returns The authenticated user object or a specific property
 * 
 * @example
 * ```typescript
 * @Get('/profile')
 * getProfile(@CurrentUser() user: AuthenticatedUser) {
 *   return user;
 * }
 * 
 * @Get('/user-id')
 * getUserId(@CurrentUser('id') userId: string) {
 *   return { userId };
 * }
 * ```
 */
export const CurrentUser = createParamDecorator(
  (data: keyof AuthenticatedUser | undefined, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user as AuthenticatedUser;
    
    if (!user) {
      return null;
    }
    
    return data ? user[data] : user;
  },
);

/**
 * Current User ID decorator - shorthand for getting just the user ID
 * 
 * @example
 * ```typescript
 * @Get('/my-data')
 * getMyData(@CurrentUserId() userId: string) {
 *   return this.service.getUserData(userId);
 * }
 * ```
 */
export const CurrentUserId = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user as AuthenticatedUser;
    return user?.id;
  },
);

/**
 * Current User Roles decorator - shorthand for getting user roles
 * 
 * @example
 * ```typescript
 * @Get('/check-roles')
 * checkRoles(@CurrentUserRoles() roles: string[]) {
 *   return { roles };
 * }
 * ```
 */
export const CurrentUserRoles = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user as AuthenticatedUser;
    return user?.roles || [];
  },
);
