38578a7009278fd70495df0a15cfeaf1
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const accuracy_score_value_object_1 = require("../accuracy-score.value-object");
describe('AccuracyScore Value Object', () => {
    describe('Construction', () => {
        it('should create a valid accuracy score', () => {
            const score = new accuracy_score_value_object_1.AccuracyScore(0.85);
            expect(score.value).toBe(0.85);
        });
        it('should accept minimum value', () => {
            const score = new accuracy_score_value_object_1.AccuracyScore(0.0);
            expect(score.value).toBe(0.0);
        });
        it('should accept maximum value', () => {
            const score = new accuracy_score_value_object_1.AccuracyScore(1.0);
            expect(score.value).toBe(1.0);
        });
        it('should throw error for negative values', () => {
            expect(() => new accuracy_score_value_object_1.AccuracyScore(-0.1)).toThrow('Accuracy score cannot be less than 0');
        });
        it('should throw error for values greater than 1', () => {
            expect(() => new accuracy_score_value_object_1.AccuracyScore(1.1)).toThrow('Accuracy score cannot be greater than 1');
        });
        it('should throw error for NaN', () => {
            expect(() => new accuracy_score_value_object_1.AccuracyScore(NaN)).toThrow('Accuracy score cannot be NaN');
        });
        it('should throw error for infinite values', () => {
            expect(() => new accuracy_score_value_object_1.AccuracyScore(Infinity)).toThrow('Accuracy score must be finite');
        });
        it('should throw error for non-number values', () => {
            expect(() => new accuracy_score_value_object_1.AccuracyScore('0.5')).toThrow('Accuracy score must be a number');
        });
    });
    describe('Factory Methods', () => {
        it('should create from predictions', () => {
            const score = accuracy_score_value_object_1.AccuracyScore.fromPredictions(85, 100);
            expect(score.value).toBe(0.85);
        });
        it('should throw error for invalid prediction counts', () => {
            expect(() => accuracy_score_value_object_1.AccuracyScore.fromPredictions(10, 0)).toThrow('Total predictions must be greater than zero');
            expect(() => accuracy_score_value_object_1.AccuracyScore.fromPredictions(-5, 10)).toThrow('Correct predictions cannot be negative');
            expect(() => accuracy_score_value_object_1.AccuracyScore.fromPredictions(15, 10)).toThrow('Correct predictions cannot exceed total predictions');
        });
        it('should create from percentage', () => {
            const score = accuracy_score_value_object_1.AccuracyScore.fromPercentage(85);
            expect(score.value).toBe(0.85);
        });
        it('should throw error for invalid percentage', () => {
            expect(() => accuracy_score_value_object_1.AccuracyScore.fromPercentage(-10)).toThrow('Percentage must be between 0 and 100');
            expect(() => accuracy_score_value_object_1.AccuracyScore.fromPercentage(110)).toThrow('Percentage must be between 0 and 100');
        });
        it('should create predefined accuracy levels', () => {
            expect(accuracy_score_value_object_1.AccuracyScore.perfect().value).toBe(1.0);
            expect(accuracy_score_value_object_1.AccuracyScore.zero().value).toBe(0.0);
            expect(accuracy_score_value_object_1.AccuracyScore.random().value).toBe(0.5);
        });
    });
    describe('Level Classification', () => {
        it('should classify accuracy levels correctly', () => {
            expect(new accuracy_score_value_object_1.AccuracyScore(0.3).getLevel()).toBe('poor');
            expect(new accuracy_score_value_object_1.AccuracyScore(0.6).getLevel()).toBe('fair');
            expect(new accuracy_score_value_object_1.AccuracyScore(0.75).getLevel()).toBe('good');
            expect(new accuracy_score_value_object_1.AccuracyScore(0.9).getLevel()).toBe('very_good');
            expect(new accuracy_score_value_object_1.AccuracyScore(0.98).getLevel()).toBe('excellent');
        });
        it('should check level predicates', () => {
            const poorScore = new accuracy_score_value_object_1.AccuracyScore(0.3);
            const fairScore = new accuracy_score_value_object_1.AccuracyScore(0.6);
            const goodScore = new accuracy_score_value_object_1.AccuracyScore(0.85);
            const excellentScore = new accuracy_score_value_object_1.AccuracyScore(0.98);
            expect(poorScore.isPoor()).toBe(true);
            expect(fairScore.isFair()).toBe(true);
            expect(goodScore.isGood()).toBe(true);
            expect(excellentScore.isExcellent()).toBe(true);
            expect(excellentScore.isPoor()).toBe(false);
            expect(poorScore.isGood()).toBe(false);
        });
        it('should check threshold compliance', () => {
            const score = new accuracy_score_value_object_1.AccuracyScore(0.75);
            expect(score.meetsThreshold(0.7)).toBe(true);
            expect(score.meetsThreshold(0.8)).toBe(false);
        });
    });
    describe('Conversions and Metrics', () => {
        it('should convert to percentage', () => {
            const score = new accuracy_score_value_object_1.AccuracyScore(0.85);
            expect(score.toPercentage()).toBe(85);
        });
        it('should convert to percentage string', () => {
            const score = new accuracy_score_value_object_1.AccuracyScore(0.8567);
            expect(score.toPercentageString()).toBe('85.7%');
            expect(score.toPercentageString(2)).toBe('85.67%');
        });
        it('should calculate error rate', () => {
            const score = new accuracy_score_value_object_1.AccuracyScore(0.85);
            expect(score.getErrorRate()).toBe(0.15);
            expect(score.getErrorRatePercentage()).toBe(15);
        });
        it('should convert to string', () => {
            const score = new accuracy_score_value_object_1.AccuracyScore(0.85);
            expect(score.toString()).toBe('85.0% (very_good)');
        });
    });
    describe('Comparison Operations', () => {
        it('should calculate improvement over baseline', () => {
            const current = new accuracy_score_value_object_1.AccuracyScore(0.85);
            const baseline = new accuracy_score_value_object_1.AccuracyScore(0.75);
            expect(current.improvementOver(baseline)).toBe(0.1);
        });
        it('should calculate relative improvement', () => {
            const current = new accuracy_score_value_object_1.AccuracyScore(0.9);
            const baseline = new accuracy_score_value_object_1.AccuracyScore(0.75);
            expect(current.relativeImprovementOver(baseline)).toBeCloseTo(0.2); // (0.9 - 0.75) / 0.75
        });
        it('should handle zero baseline in relative improvement', () => {
            const current = new accuracy_score_value_object_1.AccuracyScore(0.5);
            const baseline = new accuracy_score_value_object_1.AccuracyScore(0.0);
            expect(current.relativeImprovementOver(baseline)).toBe(Infinity);
        });
        it('should handle zero current and baseline in relative improvement', () => {
            const current = new accuracy_score_value_object_1.AccuracyScore(0.0);
            const baseline = new accuracy_score_value_object_1.AccuracyScore(0.0);
            expect(current.relativeImprovementOver(baseline)).toBe(0);
        });
        it('should check significant improvement', () => {
            const current = new accuracy_score_value_object_1.AccuracyScore(0.85);
            const baseline = new accuracy_score_value_object_1.AccuracyScore(0.75);
            expect(current.isSignificantlyBetterThan(baseline, 0.05)).toBe(true);
            expect(current.isSignificantlyBetterThan(baseline, 0.15)).toBe(false);
        });
        it('should compare accuracy scores', () => {
            const score1 = new accuracy_score_value_object_1.AccuracyScore(0.85);
            const score2 = new accuracy_score_value_object_1.AccuracyScore(0.75);
            expect(score1.isGreaterThan(score2)).toBe(true);
            expect(score2.isLessThan(score1)).toBe(true);
            expect(score1.isLessThan(score2)).toBe(false);
            expect(score2.isGreaterThan(score1)).toBe(false);
        });
        it('should calculate difference', () => {
            const score1 = new accuracy_score_value_object_1.AccuracyScore(0.85);
            const score2 = new accuracy_score_value_object_1.AccuracyScore(0.75);
            expect(score1.differenceFrom(score2)).toBe(0.1);
            expect(score2.differenceFrom(score1)).toBe(0.1);
        });
    });
    describe('Mathematical Operations', () => {
        it('should combine with another accuracy score', () => {
            const score1 = new accuracy_score_value_object_1.AccuracyScore(0.8);
            const score2 = new accuracy_score_value_object_1.AccuracyScore(0.6);
            const combined = score1.combineWith(score2, 0.7);
            expect(combined.value).toBeCloseTo(0.74); // 0.8 * 0.7 + 0.6 * 0.3
        });
        it('should throw error for invalid weight in combine', () => {
            const score1 = new accuracy_score_value_object_1.AccuracyScore(0.8);
            const score2 = new accuracy_score_value_object_1.AccuracyScore(0.6);
            expect(() => score1.combineWith(score2, -0.1)).toThrow('Weight must be between 0 and 1');
            expect(() => score1.combineWith(score2, 1.1)).toThrow('Weight must be between 0 and 1');
        });
        it('should round accuracy scores', () => {
            const score = new accuracy_score_value_object_1.AccuracyScore(0.8567);
            expect(score.round().value).toBe(0.857);
            expect(score.round(2).value).toBe(0.86);
        });
    });
    describe('Static Utility Methods', () => {
        it('should calculate average', () => {
            const scores = [
                new accuracy_score_value_object_1.AccuracyScore(0.8),
                new accuracy_score_value_object_1.AccuracyScore(0.6),
                new accuracy_score_value_object_1.AccuracyScore(0.9),
            ];
            const average = accuracy_score_value_object_1.AccuracyScore.average(scores);
            expect(average.value).toBeCloseTo(0.7667, 3);
        });
        it('should throw error for empty array in average', () => {
            expect(() => accuracy_score_value_object_1.AccuracyScore.average([])).toThrow('Cannot calculate average of empty array');
        });
        it('should calculate weighted average', () => {
            const scores = [
                new accuracy_score_value_object_1.AccuracyScore(0.8),
                new accuracy_score_value_object_1.AccuracyScore(0.6),
            ];
            const weights = [0.7, 0.3];
            const weightedAvg = accuracy_score_value_object_1.AccuracyScore.weightedAverage(scores, weights);
            expect(weightedAvg.value).toBeCloseTo(0.74);
        });
        it('should throw error for mismatched arrays in weighted average', () => {
            const scores = [new accuracy_score_value_object_1.AccuracyScore(0.8)];
            const weights = [0.7, 0.3];
            expect(() => accuracy_score_value_object_1.AccuracyScore.weightedAverage(scores, weights))
                .toThrow('Scores and weights arrays must have the same length');
        });
        it('should throw error for zero total weight', () => {
            const scores = [new accuracy_score_value_object_1.AccuracyScore(0.8), new accuracy_score_value_object_1.AccuracyScore(0.6)];
            const weights = [0, 0];
            expect(() => accuracy_score_value_object_1.AccuracyScore.weightedAverage(scores, weights))
                .toThrow('Total weight cannot be zero');
        });
        it('should find maximum and minimum', () => {
            const scores = [
                new accuracy_score_value_object_1.AccuracyScore(0.8),
                new accuracy_score_value_object_1.AccuracyScore(0.3),
                new accuracy_score_value_object_1.AccuracyScore(0.9),
            ];
            expect(accuracy_score_value_object_1.AccuracyScore.max(scores).value).toBe(0.9);
            expect(accuracy_score_value_object_1.AccuracyScore.min(scores).value).toBe(0.3);
        });
        it('should throw error for empty array in max/min', () => {
            expect(() => accuracy_score_value_object_1.AccuracyScore.max([])).toThrow('Cannot find maximum of empty array');
            expect(() => accuracy_score_value_object_1.AccuracyScore.min([])).toThrow('Cannot find minimum of empty array');
        });
    });
    describe('JSON Serialization', () => {
        it('should serialize to JSON', () => {
            const score = new accuracy_score_value_object_1.AccuracyScore(0.85);
            const json = score.toJSON();
            expect(json.value).toBe(0.85);
            expect(json.percentage).toBe(85);
            expect(json.level).toBe('very_good');
            expect(json.errorRate).toBe(0.15);
            expect(json.isGood).toBe(true);
            expect(json.isExcellent).toBe(false);
        });
        it('should deserialize from JSON', () => {
            const json = { value: 0.75 };
            const score = accuracy_score_value_object_1.AccuracyScore.fromJSON(json);
            expect(score.value).toBe(0.75);
        });
    });
    describe('Equality', () => {
        it('should be equal to another accuracy score with same value', () => {
            const score1 = new accuracy_score_value_object_1.AccuracyScore(0.85);
            const score2 = new accuracy_score_value_object_1.AccuracyScore(0.85);
            expect(score1.equals(score2)).toBe(true);
        });
        it('should not be equal to accuracy score with different value', () => {
            const score1 = new accuracy_score_value_object_1.AccuracyScore(0.85);
            const score2 = new accuracy_score_value_object_1.AccuracyScore(0.75);
            expect(score1.equals(score2)).toBe(false);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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