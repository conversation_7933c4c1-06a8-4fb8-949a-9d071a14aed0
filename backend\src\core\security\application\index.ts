/**
 * Core Security Application Layer Exports
 * 
 * Central export point for all security application services including
 * event processing, correlation, incident management, and vulnerability assessment.
 */

// Application Services
export * from './services/event-processing.service';
export * from './services/correlation.service';
export * from './services/incident-management.service';
export * from './services/vulnerability-assessment.service';
export * from './services/threat-hunting.service';
export * from './services/security-metrics.service';

// CQRS Query Handlers
export * from './queries/handlers/security-event-query.handler';
export * from './queries/handlers/vulnerability-query.handler';
export * from './queries/handlers/incident-query.handler';
export * from './queries/handlers/dashboard-query.handler';

// Integration Events
export * from './integration/events/threat-intelligence-integration.event';
export * from './integration/events/siem-integration.event';
export * from './integration/events/notification-integration.event';
export * from './integration/events/compliance-reposrting-integration.event';

// Service Interfaces and Types
export type {
  EventProcessingConfig,
  ProcessingResult,
  BatchProcessingResult,
} from './services/event-processing.service';

export type {
  CorrelationConfig,
  CorrelationRequest,
  CorrelationResult,
} from './services/correlation.service';

export type {
  CreateIncidentRequest,
  UpdateIncidentRequest,
  ResponseTeamAssignment,
  IncidentSearchCriteria,
  IncidentStatistics,
} from './services/incident-management.service';

export type {
  VulnerabilityAssessmentRequest,
  VulnerabilityAssessmentResult,
  BulkAssessmentRequest,
  BulkAssessmentResult,
} from './services/vulnerability-assessment.service';
