747caf12cda982fa67dd6456de837542
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var AiHttpClient_1;
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AiHttpClient = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const axios_1 = __importDefault(require("axios"));
/**
 * AI HTTP Client
 *
 * Handles HTTP communication with AI providers and services.
 * Implements retry logic, timeout handling, request/response transformation,
 * and comprehensive error handling for reliable AI service integration.
 */
let AiHttpClient = AiHttpClient_1 = class AiHttpClient {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(AiHttpClient_1.name);
        this.defaultTimeout = this.configService.get('ai.http.timeout', 30000);
        this.defaultRetries = this.configService.get('ai.http.retries', 3);
        // Create axios instance with default configuration
        this.axiosInstance = axios_1.default.create({
            timeout: this.defaultTimeout,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Sentinel-AI-Client/1.0',
            },
        });
    }
    /**
     * Sends analysis request to AI provider
     */
    async sendAnalysisRequest(endpoint, payload, options = {}) {
        this.logger.debug(`Sending analysis request to: ${endpoint}`);
        try {
            const config = this.buildRequestConfig(endpoint, payload, options);
            const response = await this.executeRequest(config);
            this.logger.debug(`Analysis request completed: ${endpoint}`);
            return this.transformAnalysisResponse(response.data);
        }
        catch (error) {
            this.logger.error(`Analysis request failed: ${endpoint}`, error);
            throw new AiHttpError(`Analysis request failed: ${error.message}`, endpoint, error);
        }
    }
    /**
     * Sends training request to AI provider
     */
    async sendTrainingRequest(endpoint, payload, options = {}) {
        this.logger.debug(`Sending training request to: ${endpoint}`);
        try {
            const config = this.buildRequestConfig(endpoint, payload, {
                ...options,
                timeout: options.timeout || 300000, // 5 minutes for training
            });
            const response = await this.executeRequest(config);
            this.logger.debug(`Training request completed: ${endpoint}`);
            return this.transformTrainingResponse(response.data);
        }
        catch (error) {
            this.logger.error(`Training request failed: ${endpoint}`, error);
            throw new AiHttpError(`Training request failed: ${error.message}`, endpoint, error);
        }
    }
    /**
     * Sends prediction request to AI provider
     */
    async sendPredictionRequest(endpoint, payload, options = {}) {
        this.logger.debug(`Sending prediction request to: ${endpoint}`);
        try {
            const config = this.buildRequestConfig(endpoint, payload, {
                ...options,
                timeout: options.timeout || 5000, // 5 seconds for predictions
            });
            const response = await this.executeRequest(config);
            this.logger.debug(`Prediction request completed: ${endpoint}`);
            return this.transformPredictionResponse(response.data);
        }
        catch (error) {
            this.logger.error(`Prediction request failed: ${endpoint}`, error);
            throw new AiHttpError(`Prediction request failed: ${error.message}`, endpoint, error);
        }
    }
    /**
     * Sends health check request to AI provider
     */
    async sendHealthCheckRequest(endpoint, options = {}) {
        this.logger.debug(`Sending health check to: ${endpoint}`);
        try {
            const config = {
                method: 'GET',
                url: `${endpoint}/health`,
                timeout: options.timeout || 5000,
                headers: this.buildHeaders(options),
            };
            const response = await this.executeRequest(config);
            return {
                healthy: response.data.healthy || response.status === 200,
                status: response.data.status || 'unknown',
                responseTime: response.data.responseTime,
                version: response.data.version,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.warn(`Health check failed: ${endpoint}`, error);
            return {
                healthy: false,
                status: 'unhealthy',
                error: error.message,
                timestamp: new Date(),
            };
        }
    }
    /**
     * Sends batch request to AI provider
     */
    async sendBatchRequest(endpoint, payloads, options = {}) {
        this.logger.debug(`Sending batch request to: ${endpoint}, count: ${payloads.length}`);
        try {
            const batchPayload = {
                requests: payloads,
                batchId: this.generateBatchId(),
                timestamp: new Date().toISOString(),
            };
            const config = this.buildRequestConfig(endpoint, batchPayload, {
                ...options,
                timeout: options.timeout || 120000, // 2 minutes for batch
            });
            const response = await this.executeRequest(config);
            this.logger.debug(`Batch request completed: ${endpoint}`);
            return this.transformBatchResponse(response.data);
        }
        catch (error) {
            this.logger.error(`Batch request failed: ${endpoint}`, error);
            throw new AiHttpError(`Batch request failed: ${error.message}`, endpoint, error);
        }
    }
    /**
     * Uploads file to AI provider
     */
    async uploadFile(endpoint, file, filename, options = {}) {
        this.logger.debug(`Uploading file to: ${endpoint}, filename: ${filename}`);
        try {
            const formData = new FormData();
            formData.append('file', new Blob([file]), filename);
            if (options.metadata) {
                formData.append('metadata', JSON.stringify(options.metadata));
            }
            const config = {
                method: 'POST',
                url: endpoint,
                data: formData,
                timeout: options.timeout || 60000,
                headers: {
                    ...this.buildHeaders(options),
                    'Content-Type': 'multipart/form-data',
                },
            };
            const response = await this.executeRequest(config);
            this.logger.debug(`File upload completed: ${endpoint}`);
            return response.data;
        }
        catch (error) {
            this.logger.error(`File upload failed: ${endpoint}`, error);
            throw new AiHttpError(`File upload failed: ${error.message}`, endpoint, error);
        }
    }
    /**
     * Downloads file from AI provider
     */
    async downloadFile(endpoint, fileId, options = {}) {
        this.logger.debug(`Downloading file from: ${endpoint}, fileId: ${fileId}`);
        try {
            const config = {
                method: 'GET',
                url: `${endpoint}/${fileId}`,
                responseType: 'arraybuffer',
                timeout: options.timeout || 60000,
                headers: this.buildHeaders(options),
            };
            const response = await this.executeRequest(config);
            return {
                data: Buffer.from(response.data),
                filename: this.extractFilename(response.headers),
                contentType: response.headers['content-type'],
                size: response.data.byteLength,
            };
        }
        catch (error) {
            this.logger.error(`File download failed: ${endpoint}`, error);
            throw new AiHttpError(`File download failed: ${error.message}`, endpoint, error);
        }
    }
    /**
     * Streams data to AI provider
     */
    async streamRequest(endpoint, payload, options = {}) {
        this.logger.debug(`Starting stream request to: ${endpoint}`);
        try {
            const config = this.buildRequestConfig(endpoint, payload, {
                ...options,
                responseType: 'stream',
            });
            const response = await this.executeRequest(config);
            return response.data;
        }
        catch (error) {
            this.logger.error(`Stream request failed: ${endpoint}`, error);
            throw new AiHttpError(`Stream request failed: ${error.message}`, endpoint, error);
        }
    }
    // Private helper methods
    buildRequestConfig(endpoint, payload, options) {
        return {
            method: options.method || 'POST',
            url: endpoint,
            data: payload,
            timeout: options.timeout || this.defaultTimeout,
            headers: this.buildHeaders(options),
            params: options.queryParams,
            responseType: options.responseType || 'json',
        };
    }
    buildHeaders(options) {
        const headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'Sentinel-AI-Client/1.0',
            'X-Request-ID': this.generateRequestId(),
        };
        if (options.apiKey) {
            headers['Authorization'] = `Bearer ${options.apiKey}`;
        }
        if (options.customHeaders) {
            Object.assign(headers, options.customHeaders);
        }
        return headers;
    }
    async executeRequest(config) {
        const maxRetries = config.retries || this.defaultRetries;
        let lastError;
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                const response = await this.axiosInstance.request(config);
                return response;
            }
            catch (error) {
                lastError = error;
                // Don't retry on the last attempt
                if (attempt === maxRetries) {
                    break;
                }
                // Don't retry on certain error types
                if (this.shouldNotRetry(error)) {
                    break;
                }
                // Exponential backoff with jitter
                const delay = Math.min(1000 * Math.pow(2, attempt), 10000) + Math.random() * 1000;
                await this.sleep(delay);
                this.logger.debug(`Retrying request (attempt ${attempt + 1}/${maxRetries}) after ${delay}ms`);
            }
        }
        throw this.transformHttpError(lastError);
    }
    shouldNotRetry(error) {
        // Don't retry on client errors (4xx) except for specific cases
        if (error.response?.status >= 400 && error.response?.status < 500) {
            // Retry on rate limiting and authentication errors
            return ![401, 408, 429].includes(error.response.status);
        }
        // Don't retry on certain network errors
        if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
            return true;
        }
        return false;
    }
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    transformAnalysisResponse(data) {
        return {
            id: data.id || this.generateResponseId(),
            result: data.result || data.analysis || data.output,
            confidence: data.confidence || data.score || 0,
            metadata: {
                model: data.model,
                version: data.version,
                processingTime: data.processingTime,
                ...data.metadata,
            },
            timestamp: new Date(data.timestamp || Date.now()),
        };
    }
    transformTrainingResponse(data) {
        return {
            jobId: data.jobId || data.id,
            status: data.status || 'completed',
            modelId: data.modelId,
            metrics: data.metrics || {},
            artifacts: data.artifacts || [],
            metadata: data.metadata || {},
            timestamp: new Date(data.timestamp || Date.now()),
        };
    }
    transformPredictionResponse(data) {
        return {
            prediction: data.prediction || data.result || data.output,
            confidence: data.confidence || data.score || 0,
            alternatives: data.alternatives || [],
            metadata: {
                model: data.model,
                latency: data.latency,
                ...data.metadata,
            },
            timestamp: new Date(data.timestamp || Date.now()),
        };
    }
    transformBatchResponse(data) {
        return {
            batchId: data.batchId,
            results: data.results || data.responses || [],
            summary: {
                total: data.total || data.results?.length || 0,
                successful: data.successful || 0,
                failed: data.failed || 0,
                processingTime: data.processingTime,
            },
            metadata: data.metadata || {},
            timestamp: new Date(data.timestamp || Date.now()),
        };
    }
    transformHttpError(error) {
        if (error?.response) {
            // HTTP error response
            const status = error.response.status;
            const message = error.response.data?.message || error.response.statusText;
            return new AiHttpError(`HTTP ${status}: ${message}`, error.config?.url, error);
        }
        if (error?.code === 'ECONNABORTED') {
            return new AiHttpError('Request timeout', error.config?.url, error);
        }
        if (error?.code === 'ECONNREFUSED') {
            return new AiHttpError('Connection refused', error.config?.url, error);
        }
        return new AiHttpError(error?.message || 'Unknown HTTP error', error?.config?.url, error);
    }
    extractFilename(headers) {
        const contentDisposition = headers['content-disposition'];
        if (contentDisposition) {
            const match = contentDisposition.match(/filename="?([^"]+)"?/);
            if (match)
                return match[1];
        }
        return 'download';
    }
    generateRequestId() {
        return `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    generateResponseId() {
        return `res-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    generateBatchId() {
        return `batch-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
};
exports.AiHttpClient = AiHttpClient;
exports.AiHttpClient = AiHttpClient = AiHttpClient_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], AiHttpClient);
class AiHttpError extends Error {
    constructor(message, endpoint, originalError) {
        super(message);
        this.endpoint = endpoint;
        this.originalError = originalError;
        this.name = 'AiHttpError';
    }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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