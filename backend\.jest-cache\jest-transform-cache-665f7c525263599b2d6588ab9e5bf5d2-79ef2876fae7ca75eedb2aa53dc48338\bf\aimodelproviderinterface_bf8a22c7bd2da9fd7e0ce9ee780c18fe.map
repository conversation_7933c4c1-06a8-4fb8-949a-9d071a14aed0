{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\services\\ai-model-provider.interface.ts", "mappings": ";;;AA8KA,iCAAiC;AACpB,QAAA,iBAAiB,GAAG,MAAM,CAAC,mBAAmB,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\services\\ai-model-provider.interface.ts"], "sourcesContent": ["/**\r\n * AI Model Provider Interface\r\n * \r\n * Defines the contract for AI model providers that integrate with external\r\n * AI services and platforms. Supports multiple provider types with\r\n * standardized operations for model management and inference.\r\n */\r\nexport interface AiModelProvider {\r\n  /**\r\n   * Gets the provider type identifier\r\n   */\r\n  getProviderType(): string;\r\n\r\n  /**\r\n   * Gets the provider name\r\n   */\r\n  getProviderName(): string;\r\n\r\n  /**\r\n   * Gets the provider version\r\n   */\r\n  getProviderVersion(): string;\r\n\r\n  /**\r\n   * Checks if the provider is healthy and available\r\n   */\r\n  isHealthy(): Promise<boolean>;\r\n\r\n  /**\r\n   * Gets provider health status with details\r\n   */\r\n  getHealthStatus(): Promise<ProviderHealthStatus>;\r\n\r\n  /**\r\n   * Lists available models from the provider\r\n   */\r\n  listAvailableModels(): Promise<AvailableModel[]>;\r\n\r\n  /**\r\n   * Gets detailed information about a specific model\r\n   */\r\n  getModelInfo(modelId: string): Promise<ModelInfo>;\r\n\r\n  /**\r\n   * Loads a model for inference\r\n   */\r\n  loadModel(modelId: string, configuration?: ModelConfiguration): Promise<LoadedModel>;\r\n\r\n  /**\r\n   * Unloads a model to free resources\r\n   */\r\n  unloadModel(modelId: string): Promise<void>;\r\n\r\n  /**\r\n   * Performs inference using a loaded model\r\n   */\r\n  predict(\r\n    modelId: string,\r\n    input: InferenceInput,\r\n    options?: InferenceOptions\r\n  ): Promise<InferenceResult>;\r\n\r\n  /**\r\n   * Performs batch inference for multiple inputs\r\n   */\r\n  batchPredict(\r\n    modelId: string,\r\n    inputs: InferenceInput[],\r\n    options?: InferenceOptions\r\n  ): Promise<InferenceResult[]>;\r\n\r\n  /**\r\n   * Starts a training job\r\n   */\r\n  trainModel(\r\n    trainingRequest: TrainingRequest\r\n  ): Promise<TrainingJob>;\r\n\r\n  /**\r\n   * Gets the status of a training job\r\n   */\r\n  getTrainingStatus(jobId: string): Promise<TrainingStatus>;\r\n\r\n  /**\r\n   * Cancels a training job\r\n   */\r\n  cancelTraining(jobId: string): Promise<void>;\r\n\r\n  /**\r\n   * Deploys a trained model\r\n   */\r\n  deployModel(\r\n    modelId: string,\r\n    deploymentConfig: DeploymentConfiguration\r\n  ): Promise<DeploymentResult>;\r\n\r\n  /**\r\n   * Updates model configuration\r\n   */\r\n  updateModelConfiguration(\r\n    modelId: string,\r\n    configuration: Partial<ModelConfiguration>\r\n  ): Promise<void>;\r\n\r\n  /**\r\n   * Gets model performance metrics\r\n   */\r\n  getModelMetrics(modelId: string): Promise<ModelMetrics>;\r\n\r\n  /**\r\n   * Gets provider resource usage\r\n   */\r\n  getResourceUsage(): Promise<ResourceUsage>;\r\n\r\n  /**\r\n   * Gets provider pricing information\r\n   */\r\n  getPricingInfo(): Promise<PricingInfo>;\r\n\r\n  /**\r\n   * Validates input data format\r\n   */\r\n  validateInput(input: InferenceInput, modelId: string): Promise<ValidationResult>;\r\n\r\n  /**\r\n   * Gets supported input formats for a model\r\n   */\r\n  getSupportedInputFormats(modelId: string): Promise<string[]>;\r\n\r\n  /**\r\n   * Gets supported output formats for a model\r\n   */\r\n  getSupportedOutputFormats(modelId: string): Promise<string[]>;\r\n\r\n  /**\r\n   * Estimates inference cost\r\n   */\r\n  estimateInferenceCost(\r\n    modelId: string,\r\n    inputSize: number,\r\n    options?: InferenceOptions\r\n  ): Promise<CostEstimate>;\r\n\r\n  /**\r\n   * Estimates training cost\r\n   */\r\n  estimateTrainingCost(trainingRequest: TrainingRequest): Promise<CostEstimate>;\r\n\r\n  /**\r\n   * Gets provider capabilities\r\n   */\r\n  getCapabilities(): Promise<ProviderCapabilities>;\r\n\r\n  /**\r\n   * Gets provider limits and quotas\r\n   */\r\n  getLimits(): Promise<ProviderLimits>;\r\n\r\n  /**\r\n   * Handles provider-specific authentication\r\n   */\r\n  authenticate(credentials: ProviderCredentials): Promise<AuthenticationResult>;\r\n\r\n  /**\r\n   * Refreshes authentication tokens\r\n   */\r\n  refreshAuthentication(): Promise<void>;\r\n\r\n  /**\r\n   * Handles graceful shutdown\r\n   */\r\n  shutdown(): Promise<void>;\r\n}\r\n\r\n// Token for dependency injection\r\nexport const AI_MODEL_PROVIDER = Symbol('AI_MODEL_PROVIDER');\r\n\r\n// Type definitions\r\ninterface ProviderHealthStatus {\r\n  isHealthy: boolean;\r\n  status: 'healthy' | 'degraded' | 'unhealthy';\r\n  lastChecked: Date;\r\n  responseTime: number;\r\n  errorRate: number;\r\n  availableModels: number;\r\n  activeConnections: number;\r\n  details: Record<string, any>;\r\n}\r\n\r\ninterface AvailableModel {\r\n  id: string;\r\n  name: string;\r\n  version: string;\r\n  type: string;\r\n  description: string;\r\n  capabilities: string[];\r\n  supportedTasks: string[];\r\n  inputFormats: string[];\r\n  outputFormats: string[];\r\n  maxInputSize: number;\r\n  maxOutputSize: number;\r\n  averageLatency: number;\r\n  costPerRequest: number;\r\n  isAvailable: boolean;\r\n  metadata: Record<string, any>;\r\n}\r\n\r\ninterface ModelInfo {\r\n  id: string;\r\n  name: string;\r\n  version: string;\r\n  type: string;\r\n  description: string;\r\n  provider: string;\r\n  capabilities: ModelCapabilities;\r\n  configuration: ModelConfiguration;\r\n  performance: ModelPerformance;\r\n  pricing: ModelPricing;\r\n  limits: ModelLimits;\r\n  metadata: Record<string, any>;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n}\r\n\r\ninterface ModelCapabilities {\r\n  supportedTasks: string[];\r\n  inputTypes: string[];\r\n  outputTypes: string[];\r\n  languages: string[];\r\n  domains: string[];\r\n  features: string[];\r\n}\r\n\r\ninterface ModelConfiguration {\r\n  parameters: Record<string, any>;\r\n  hyperparameters: Record<string, any>;\r\n  preprocessing: PreprocessingConfig;\r\n  postprocessing: PostprocessingConfig;\r\n  optimization: OptimizationConfig;\r\n}\r\n\r\ninterface PreprocessingConfig {\r\n  normalization: boolean;\r\n  tokenization: TokenizationConfig;\r\n  augmentation: AugmentationConfig;\r\n  filtering: FilteringConfig;\r\n}\r\n\r\ninterface PostprocessingConfig {\r\n  formatting: string;\r\n  filtering: boolean;\r\n  aggregation: AggregationConfig;\r\n  validation: ValidationConfig;\r\n}\r\n\r\ninterface OptimizationConfig {\r\n  batchSize: number;\r\n  maxConcurrency: number;\r\n  caching: boolean;\r\n  compression: boolean;\r\n}\r\n\r\ninterface TokenizationConfig {\r\n  tokenizer: string;\r\n  maxTokens: number;\r\n  padding: boolean;\r\n  truncation: boolean;\r\n}\r\n\r\ninterface AugmentationConfig {\r\n  enabled: boolean;\r\n  techniques: string[];\r\n  parameters: Record<string, any>;\r\n}\r\n\r\ninterface FilteringConfig {\r\n  enabled: boolean;\r\n  rules: FilterRule[];\r\n}\r\n\r\ninterface FilterRule {\r\n  field: string;\r\n  operator: string;\r\n  value: any;\r\n}\r\n\r\ninterface AggregationConfig {\r\n  method: string;\r\n  parameters: Record<string, any>;\r\n}\r\n\r\ninterface ValidationConfig {\r\n  enabled: boolean;\r\n  rules: ValidationRule[];\r\n}\r\n\r\ninterface ValidationRule {\r\n  field: string;\r\n  type: string;\r\n  constraints: Record<string, any>;\r\n}\r\n\r\ninterface LoadedModel {\r\n  modelId: string;\r\n  loadedAt: Date;\r\n  memoryUsage: number;\r\n  status: 'loading' | 'ready' | 'error';\r\n  configuration: ModelConfiguration;\r\n  metadata: Record<string, any>;\r\n}\r\n\r\ninterface InferenceInput {\r\n  data: any;\r\n  format: string;\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\ninterface InferenceOptions {\r\n  timeout?: number;\r\n  priority?: 'low' | 'normal' | 'high';\r\n  streaming?: boolean;\r\n  callbacks?: InferenceCallbacks;\r\n  configuration?: Partial<ModelConfiguration>;\r\n}\r\n\r\ninterface InferenceCallbacks {\r\n  onProgress?: (progress: number) => void;\r\n  onPartialResult?: (result: any) => void;\r\n  onError?: (error: Error) => void;\r\n}\r\n\r\ninterface InferenceResult {\r\n  output: any;\r\n  confidence: number;\r\n  metadata: InferenceMetadata;\r\n  processingTime: number;\r\n  cost: number;\r\n  modelVersion: string;\r\n}\r\n\r\ninterface InferenceMetadata {\r\n  modelId: string;\r\n  requestId: string;\r\n  timestamp: Date;\r\n  inputSize: number;\r\n  outputSize: number;\r\n  processingSteps: ProcessingStep[];\r\n  performance: PerformanceMetrics;\r\n}\r\n\r\ninterface ProcessingStep {\r\n  name: string;\r\n  duration: number;\r\n  memoryUsage: number;\r\n  details: Record<string, any>;\r\n}\r\n\r\ninterface PerformanceMetrics {\r\n  latency: number;\r\n  throughput: number;\r\n  memoryUsage: number;\r\n  cpuUsage: number;\r\n  gpuUsage?: number;\r\n}\r\n\r\ninterface TrainingRequest {\r\n  modelType: string;\r\n  datasetId: string;\r\n  configuration: TrainingConfiguration;\r\n  hyperparameters: Record<string, any>;\r\n  validationSplit: number;\r\n  maxEpochs: number;\r\n  earlyStoppingCriteria: EarlyStoppingCriteria;\r\n  callbacks: TrainingCallbacks;\r\n  metadata: Record<string, any>;\r\n}\r\n\r\ninterface TrainingConfiguration {\r\n  batchSize: number;\r\n  learningRate: number;\r\n  optimizer: string;\r\n  lossFunction: string;\r\n  metrics: string[];\r\n  regularization: RegularizationConfig;\r\n}\r\n\r\ninterface RegularizationConfig {\r\n  l1: number;\r\n  l2: number;\r\n  dropout: number;\r\n  batchNorm: boolean;\r\n}\r\n\r\ninterface EarlyStoppingCriteria {\r\n  metric: string;\r\n  patience: number;\r\n  minDelta: number;\r\n  mode: 'min' | 'max';\r\n}\r\n\r\ninterface TrainingCallbacks {\r\n  onEpochEnd?: (epoch: number, metrics: Record<string, number>) => void;\r\n  onBatchEnd?: (batch: number, metrics: Record<string, number>) => void;\r\n  onTrainingEnd?: (finalMetrics: Record<string, number>) => void;\r\n}\r\n\r\ninterface TrainingJob {\r\n  jobId: string;\r\n  modelId: string;\r\n  status: 'queued' | 'running' | 'completed' | 'failed' | 'cancelled';\r\n  progress: number;\r\n  startedAt: Date;\r\n  estimatedCompletion?: Date;\r\n  currentEpoch: number;\r\n  totalEpochs: number;\r\n  metrics: Record<string, number>;\r\n  logs: string[];\r\n  cost: number;\r\n}\r\n\r\ninterface TrainingStatus {\r\n  jobId: string;\r\n  status: string;\r\n  progress: number;\r\n  currentEpoch: number;\r\n  totalEpochs: number;\r\n  elapsedTime: number;\r\n  estimatedTimeRemaining: number;\r\n  metrics: Record<string, number>;\r\n  logs: string[];\r\n  error?: string;\r\n}\r\n\r\ninterface DeploymentConfiguration {\r\n  environment: 'development' | 'staging' | 'production';\r\n  scaling: ScalingConfig;\r\n  monitoring: MonitoringConfig;\r\n  security: SecurityConfig;\r\n}\r\n\r\ninterface ScalingConfig {\r\n  minInstances: number;\r\n  maxInstances: number;\r\n  targetUtilization: number;\r\n  scaleUpCooldown: number;\r\n  scaleDownCooldown: number;\r\n}\r\n\r\ninterface MonitoringConfig {\r\n  enabled: boolean;\r\n  metricsInterval: number;\r\n  alertThresholds: Record<string, number>;\r\n}\r\n\r\ninterface SecurityConfig {\r\n  authentication: boolean;\r\n  authorization: boolean;\r\n  encryption: boolean;\r\n  rateLimiting: RateLimitConfig;\r\n}\r\n\r\ninterface RateLimitConfig {\r\n  requestsPerMinute: number;\r\n  requestsPerHour: number;\r\n  burstLimit: number;\r\n}\r\n\r\ninterface DeploymentResult {\r\n  deploymentId: string;\r\n  modelId: string;\r\n  endpoint: string;\r\n  status: 'deploying' | 'deployed' | 'failed';\r\n  deployedAt: Date;\r\n  configuration: DeploymentConfiguration;\r\n  healthCheckUrl: string;\r\n  metricsUrl: string;\r\n}\r\n\r\ninterface ModelMetrics {\r\n  accuracy: number;\r\n  precision: number;\r\n  recall: number;\r\n  f1Score: number;\r\n  latency: LatencyMetrics;\r\n  throughput: number;\r\n  errorRate: number;\r\n  uptime: number;\r\n  requestCount: number;\r\n  lastUpdated: Date;\r\n}\r\n\r\ninterface LatencyMetrics {\r\n  average: number;\r\n  p50: number;\r\n  p95: number;\r\n  p99: number;\r\n  min: number;\r\n  max: number;\r\n}\r\n\r\ninterface ResourceUsage {\r\n  cpu: ResourceMetric;\r\n  memory: ResourceMetric;\r\n  gpu?: ResourceMetric;\r\n  storage: ResourceMetric;\r\n  network: NetworkMetrics;\r\n}\r\n\r\ninterface ResourceMetric {\r\n  current: number;\r\n  average: number;\r\n  peak: number;\r\n  unit: string;\r\n}\r\n\r\ninterface NetworkMetrics {\r\n  inbound: number;\r\n  outbound: number;\r\n  unit: string;\r\n}\r\n\r\ninterface PricingInfo {\r\n  currency: string;\r\n  inference: InferencePricing;\r\n  training: TrainingPricing;\r\n  storage: StoragePricing;\r\n  dataTransfer: DataTransferPricing;\r\n}\r\n\r\ninterface InferencePricing {\r\n  perRequest: number;\r\n  perToken?: number;\r\n  perSecond?: number;\r\n  minimumCharge: number;\r\n}\r\n\r\ninterface TrainingPricing {\r\n  perHour: number;\r\n  perEpoch?: number;\r\n  setupFee: number;\r\n}\r\n\r\ninterface StoragePricing {\r\n  perGBPerMonth: number;\r\n  minimumStorage: number;\r\n}\r\n\r\ninterface DataTransferPricing {\r\n  perGB: number;\r\n  freeAllowance: number;\r\n}\r\n\r\ninterface ValidationResult {\r\n  isValid: boolean;\r\n  errors: ValidationError[];\r\n  warnings: ValidationWarning[];\r\n}\r\n\r\ninterface ValidationError {\r\n  field: string;\r\n  message: string;\r\n  code: string;\r\n}\r\n\r\ninterface ValidationWarning {\r\n  field: string;\r\n  message: string;\r\n  suggestion: string;\r\n}\r\n\r\ninterface CostEstimate {\r\n  totalCost: number;\r\n  breakdown: CostBreakdown;\r\n  currency: string;\r\n  estimatedAt: Date;\r\n}\r\n\r\ninterface CostBreakdown {\r\n  inference?: number;\r\n  training?: number;\r\n  storage?: number;\r\n  dataTransfer?: number;\r\n  other?: Record<string, number>;\r\n}\r\n\r\ninterface ProviderCapabilities {\r\n  supportedModelTypes: string[];\r\n  supportedTasks: string[];\r\n  maxConcurrentRequests: number;\r\n  maxModelSize: number;\r\n  supportsBatchInference: boolean;\r\n  supportsStreaming: boolean;\r\n  supportsTraining: boolean;\r\n  supportsFineTuning: boolean;\r\n  supportsDeployment: boolean;\r\n  regions: string[];\r\n}\r\n\r\ninterface ProviderLimits {\r\n  requestsPerMinute: number;\r\n  requestsPerHour: number;\r\n  requestsPerDay: number;\r\n  maxRequestSize: number;\r\n  maxResponseSize: number;\r\n  maxConcurrentTrainingJobs: number;\r\n  maxModelsPerAccount: number;\r\n  quotas: Record<string, number>;\r\n}\r\n\r\ninterface ProviderCredentials {\r\n  apiKey?: string;\r\n  accessToken?: string;\r\n  secretKey?: string;\r\n  region?: string;\r\n  endpoint?: string;\r\n  additionalParams?: Record<string, any>;\r\n}\r\n\r\ninterface AuthenticationResult {\r\n  isAuthenticated: boolean;\r\n  token?: string;\r\n  expiresAt?: Date;\r\n  permissions: string[];\r\n  error?: string;\r\n}\r\n\r\ninterface ModelPerformance {\r\n  accuracy: number;\r\n  latency: number;\r\n  throughput: number;\r\n  reliability: number;\r\n  costEfficiency: number;\r\n  lastEvaluated: Date;\r\n}\r\n\r\ninterface ModelPricing {\r\n  perRequest: number;\r\n  perToken?: number;\r\n  currency: string;\r\n  billingModel: 'pay-per-use' | 'subscription' | 'reserved';\r\n}\r\n\r\ninterface ModelLimits {\r\n  maxInputSize: number;\r\n  maxOutputSize: number;\r\n  maxConcurrentRequests: number;\r\n  rateLimits: RateLimitConfig;\r\n}"], "version": 3}