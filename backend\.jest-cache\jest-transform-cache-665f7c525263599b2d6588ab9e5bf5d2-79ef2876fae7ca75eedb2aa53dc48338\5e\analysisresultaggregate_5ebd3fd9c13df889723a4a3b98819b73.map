{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\aggregates\\analysis-result.aggregate.ts", "mappings": ";;;AACA,+EAAkG;AAClG,kFAA0G;AAgD1G,MAAa,uBAAuB;IAGlC,YAAY,UAA4B,EAAE;QAFlC,YAAO,GAAgC,IAAI,GAAG,EAAE,CAAC;QAGvD,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACvB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,OAAoC,EAAE,EAAmB;QACnF,kCAAkC;QAClC,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC/D,IAAI,cAAc,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,oCAAoC,OAAO,CAAC,SAAS,kBAAkB,CAAC,CAAC;QAC3F,CAAC;QAED,MAAM,MAAM,GAAG,+CAAqB,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QACzD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC,CAAC;QAE/C,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,iBAAiB,CAAC,MAAsB;QAC7C,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC;YAC3C,MAAM,IAAI,KAAK,CAAC,4BAA4B,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,+BAA+B,CAAC,CAAC;QACnG,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,QAAwB;QAClD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;QACrD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,4BAA4B,QAAQ,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;QAChF,CAAC;QAED,+CAA+C;QAC/C,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,CAAC;YACzB,MAAM,CAAC,MAAM,EAAE,CAAC;QAClB,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACI,iBAAiB,CAAC,QAAwB;QAC/C,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACI,qBAAqB;QAC1B,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,KAA0B;QACnD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YACvD,IAAI,KAAK,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,KAAK,KAAK,CAAC,SAAS,EAAE,CAAC;gBAC5D,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,KAAK,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC3D,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,KAAK,CAAC,YAAY,IAAI,MAAM,CAAC,YAAY,KAAK,KAAK,CAAC,YAAY,EAAE,CAAC;gBACrE,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,KAAK,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,EAAE,CAAC;gBACnD,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,KAAK,CAAC,aAAa,KAAK,SAAS,IAAI,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;gBACjF,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,KAAK,CAAC,aAAa,KAAK,SAAS,IAAI,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;gBACjF,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;gBAC/D,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,KAAK,CAAC,aAAa,IAAI,MAAM,CAAC,aAAa,KAAK,KAAK,CAAC,aAAa,EAAE,CAAC;gBACxE,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,KAAK,CAAC,gBAAgB,EAAE,CAAC;gBAC3B,IAAI,CAAC,MAAM,CAAC,gBAAgB,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE,CAAC;oBACxF,OAAO,KAAK,CAAC;gBACf,CAAC;YACH,CAAC;YAED,IAAI,KAAK,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;gBACpC,MAAM,WAAW,GAAG,MAAM,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;gBACvD,IAAI,KAAK,CAAC,WAAW,KAAK,WAAW,EAAE,CAAC;oBACtC,OAAO,KAAK,CAAC;gBACf,CAAC;YACH,CAAC;YAED,IAAI,KAAK,CAAC,YAAY,IAAI,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC;gBAChE,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,KAAK,CAAC,aAAa,IAAI,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;gBAClE,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,KAAK,CAAC,cAAc,IAAI,CAAC,CAAC,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC,cAAc,CAAC,EAAE,CAAC;gBAC/F,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,KAAK,CAAC,eAAe,IAAI,CAAC,CAAC,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC,eAAe,CAAC,EAAE,CAAC;gBACjG,OAAO,KAAK,CAAC;YACf,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,SAAiB;QACtC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC;IAC1F,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,aAAqB;QAC9C,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CACvD,MAAM,CAAC,aAAa,KAAK,aAAa,CACvC,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,QAAwB;QAC7C,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CACvD,MAAM,CAAC,gBAAgB,IAAI,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,CACpE,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,iBAAiB,CAAC,OAAuB;QAC9C,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CACvD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAC/B,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,iBAAiB;QACtB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CACvD,MAAM,CAAC,MAAM,KAAK,uCAAc,CAAC,OAAO,CACzC,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,oBAAoB;QACzB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CACvD,MAAM,CAAC,MAAM,KAAK,uCAAc,CAAC,UAAU,CAC5C,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,mBAAmB;QACxB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CACvD,MAAM,CAAC,MAAM,KAAK,uCAAc,CAAC,SAAS,CAC3C,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,gBAAgB;QACrB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CACvD,MAAM,CAAC,MAAM,KAAK,uCAAc,CAAC,MAAM,CACxC,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,yBAAyB;QAC9B,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACI,wBAAwB,CAAC,YAAoB,GAAG;QACrD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CACvD,MAAM,CAAC,iBAAiB,CAAC,SAAS,CAAC,CACpC,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,YAAoB,GAAG;QACjD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CACvD,MAAM,CAAC,eAAe,EAAE,GAAG,SAAS,CACrC,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,sBAAsB;QAC3B,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CACtE,CAAC,MAAM,CAAC,UAAU,EAAE,CACrB,CAAC;QAEF,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;QACjD,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,KAA0B;QAC7C,MAAM,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CACtE,CAAC,MAAM,CAAC,UAAU,EAAE,CACrB,CAAC;QAEF,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;QACnD,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,aAAa;QAClB,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;QAElD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACtC,CAAC;QAED,MAAM,kBAAkB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;YACxD,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACnD,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAoC,CAAC,CAAC;QAEzC,MAAM,gBAAgB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;YACtD,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAC/D,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAkC,CAAC,CAAC;QAEvC,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QACpF,MAAM,mBAAmB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QAC5F,MAAM,iBAAiB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC,CAAC;QAE7F,MAAM,iBAAiB,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;QAC1E,MAAM,gBAAgB,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;QACvE,MAAM,iBAAiB,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAChD,MAAM,CAAC,MAAM,KAAK,uCAAc,CAAC,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,CAChE,CAAC;QAEF,OAAO;YACL,YAAY,EAAE,OAAO,CAAC,MAAM;YAC5B,kBAAkB;YAClB,gBAAgB;YAChB,iBAAiB,EAAE,eAAe,GAAG,OAAO,CAAC,MAAM;YACnD,qBAAqB,EAAE,mBAAmB,GAAG,OAAO,CAAC,MAAM;YAC3D,WAAW,EAAE,iBAAiB,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM;YACtD,mBAAmB,EAAE,iBAAiB,GAAG,OAAO,CAAC,MAAM;YACvD,iBAAiB,EAAE,iBAAiB,CAAC,MAAM;YAC3C,cAAc,EAAE,gBAAgB,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM;SACzD,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,sBAAsB,CAAC,SAAe,EAAE,OAAa;QAC1D,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CACtE,MAAM,CAAC,SAAS,IAAI,SAAS,IAAI,MAAM,CAAC,SAAS,IAAI,OAAO,CAC7D,CAAC;QAEF,MAAM,aAAa,GAAG,IAAI,uBAAuB,CAAC,aAAa,CAAC,CAAC;QACjE,OAAO,aAAa,CAAC,aAAa,EAAE,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,QAAuC;QACxD,MAAM,OAAO,GAAqB,EAAE,CAAC;QAErC,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;gBAClD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,6CAA6C;gBAC7C,OAAO,CAAC,IAAI,CAAC,gDAAgD,OAAO,CAAC,SAAS,GAAG,EAAE,KAAK,CAAC,CAAC;YAC5F,CAAC;QACH,CAAC;QAED,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC,MAAM,CAAC;QAClE,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,uCAAc,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC;QACpF,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,uCAAc,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QACrF,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QAE1E,OAAO;YACL,OAAO;YACP,UAAU,EAAE,OAAO,CAAC,MAAM;YAC1B,iBAAiB,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC5E,YAAY;YACZ,YAAY;YACZ,YAAY;SACb,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,mBAAmB;QACxB,MAAM,MAAM,GAAG,IAAI,GAAG,EAAkC,CAAC;QAEzD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACjD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC;gBACrC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;YACtC,CAAC;YACD,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,CAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,aAAa;QAClB,MAAM,MAAM,GAAG,IAAI,GAAG,EAAoC,CAAC;QAE3D,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACjD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC/B,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;YAChC,CAAC;YACD,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,QAAgB,EAAE;QAC5C,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;aACrC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;aAC7D,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IACrB,CAAC;IAED;;OAEG;IACI,sBAAsB,CAAC,KAAc;QAC1C,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;aAC7C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;QAE/C,OAAO,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACjD,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,KAAc;QACvC,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;aAC7C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QAE7D,OAAO,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACjD,CAAC;IAEO,qBAAqB;QAC3B,OAAO;YACL,YAAY,EAAE,CAAC;YACf,kBAAkB,EAAE,EAAoC;YACxD,gBAAgB,EAAE,EAAkC;YACpD,iBAAiB,EAAE,CAAC;YACpB,qBAAqB,EAAE,CAAC;YACxB,WAAW,EAAE,CAAC;YACd,mBAAmB,EAAE,CAAC;YACtB,iBAAiB,EAAE,CAAC;YACpB,cAAc,EAAE,CAAC;SAClB,CAAC;IACJ,CAAC;CACF;AAhaD,0DAgaC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\aggregates\\analysis-result.aggregate.ts"], "sourcesContent": ["import { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { AnalysisResult, AnalysisType, AnalysisStatus } from '../entities/analysis-result.entity';\r\nimport { AnalysisResultFactory, CreateAnalysisResultRequest } from '../factories/analysis-result.factory';\r\n\r\n/**\r\n * Analysis Result Aggregate\r\n * \r\n * Aggregate service that manages Analysis Result domain operations.\r\n * Provides high-level business operations and ensures consistency\r\n * across multiple analysis results and related entities.\r\n */\r\n\r\nexport interface AnalysisResultQuery {\r\n  requestId?: string;\r\n  modelId?: UniqueEntityId;\r\n  analysisType?: AnalysisType;\r\n  status?: AnalysisStatus;\r\n  minConfidence?: number;\r\n  maxConfidence?: number;\r\n  tags?: string[];\r\n  correlationId?: string;\r\n  parentAnalysisId?: UniqueEntityId;\r\n  hasChildren?: boolean;\r\n  createdAfter?: Date;\r\n  createdBefore?: Date;\r\n  completedAfter?: Date;\r\n  completedBefore?: Date;\r\n}\r\n\r\nexport interface AnalysisResultStatistics {\r\n  totalResults: number;\r\n  statusDistribution: Record<AnalysisStatus, number>;\r\n  typeDistribution: Record<AnalysisType, number>;\r\n  averageConfidence: number;\r\n  averageProcessingTime: number;\r\n  successRate: number;\r\n  averageQualityScore: number;\r\n  retryableFailures: number;\r\n  completionRate: number;\r\n}\r\n\r\nexport interface AnalysisResultBatch {\r\n  results: AnalysisResult[];\r\n  totalCount: number;\r\n  averageConfidence: number;\r\n  successCount: number;\r\n  failureCount: number;\r\n  pendingCount: number;\r\n}\r\n\r\nexport class AnalysisResultAggregate {\r\n  private results: Map<string, AnalysisResult> = new Map();\r\n\r\n  constructor(results: AnalysisResult[] = []) {\r\n    results.forEach(result => {\r\n      this.results.set(result.id.toString(), result);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Creates a new analysis result and adds it to the aggregate\r\n   */\r\n  public createAnalysisResult(request: CreateAnalysisResultRequest, id?: UniqueEntityId): AnalysisResult {\r\n    // Check for duplicate request IDs\r\n    const existingResult = this.findByRequestId(request.requestId);\r\n    if (existingResult) {\r\n      throw new Error(`Analysis result with request ID '${request.requestId}' already exists`);\r\n    }\r\n\r\n    const result = AnalysisResultFactory.create(request, id);\r\n    this.results.set(result.id.toString(), result);\r\n    \r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Adds an existing analysis result to the aggregate\r\n   */\r\n  public addAnalysisResult(result: AnalysisResult): void {\r\n    if (this.results.has(result.id.toString())) {\r\n      throw new Error(`Analysis result with ID '${result.id.toString()}' already exists in aggregate`);\r\n    }\r\n\r\n    this.results.set(result.id.toString(), result);\r\n  }\r\n\r\n  /**\r\n   * Removes an analysis result from the aggregate\r\n   */\r\n  public removeAnalysisResult(resultId: UniqueEntityId): void {\r\n    const result = this.results.get(resultId.toString());\r\n    if (!result) {\r\n      throw new Error(`Analysis result with ID '${resultId.toString()}' not found`);\r\n    }\r\n\r\n    // Cancel the analysis if it's still processing\r\n    if (!result.isTerminal()) {\r\n      result.cancel();\r\n    }\r\n\r\n    this.results.delete(resultId.toString());\r\n  }\r\n\r\n  /**\r\n   * Gets an analysis result by ID\r\n   */\r\n  public getAnalysisResult(resultId: UniqueEntityId): AnalysisResult | undefined {\r\n    return this.results.get(resultId.toString());\r\n  }\r\n\r\n  /**\r\n   * Gets all analysis results\r\n   */\r\n  public getAllAnalysisResults(): AnalysisResult[] {\r\n    return Array.from(this.results.values());\r\n  }\r\n\r\n  /**\r\n   * Finds analysis results by query criteria\r\n   */\r\n  public findAnalysisResults(query: AnalysisResultQuery): AnalysisResult[] {\r\n    return Array.from(this.results.values()).filter(result => {\r\n      if (query.requestId && result.requestId !== query.requestId) {\r\n        return false;\r\n      }\r\n\r\n      if (query.modelId && !result.modelId.equals(query.modelId)) {\r\n        return false;\r\n      }\r\n\r\n      if (query.analysisType && result.analysisType !== query.analysisType) {\r\n        return false;\r\n      }\r\n\r\n      if (query.status && result.status !== query.status) {\r\n        return false;\r\n      }\r\n\r\n      if (query.minConfidence !== undefined && result.confidence < query.minConfidence) {\r\n        return false;\r\n      }\r\n\r\n      if (query.maxConfidence !== undefined && result.confidence > query.maxConfidence) {\r\n        return false;\r\n      }\r\n\r\n      if (query.tags && !query.tags.every(tag => result.hasTag(tag))) {\r\n        return false;\r\n      }\r\n\r\n      if (query.correlationId && result.correlationId !== query.correlationId) {\r\n        return false;\r\n      }\r\n\r\n      if (query.parentAnalysisId) {\r\n        if (!result.parentAnalysisId || !result.parentAnalysisId.equals(query.parentAnalysisId)) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      if (query.hasChildren !== undefined) {\r\n        const hasChildren = result.childAnalysisIds.length > 0;\r\n        if (query.hasChildren !== hasChildren) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      if (query.createdAfter && result.createdAt < query.createdAfter) {\r\n        return false;\r\n      }\r\n\r\n      if (query.createdBefore && result.createdAt > query.createdBefore) {\r\n        return false;\r\n      }\r\n\r\n      if (query.completedAfter && (!result.completedAt || result.completedAt < query.completedAfter)) {\r\n        return false;\r\n      }\r\n\r\n      if (query.completedBefore && (!result.completedAt || result.completedAt > query.completedBefore)) {\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Finds an analysis result by request ID\r\n   */\r\n  public findByRequestId(requestId: string): AnalysisResult | undefined {\r\n    return Array.from(this.results.values()).find(result => result.requestId === requestId);\r\n  }\r\n\r\n  /**\r\n   * Gets analysis results by correlation ID\r\n   */\r\n  public findByCorrelationId(correlationId: string): AnalysisResult[] {\r\n    return Array.from(this.results.values()).filter(result => \r\n      result.correlationId === correlationId\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Gets child analysis results for a parent\r\n   */\r\n  public getChildResults(parentId: UniqueEntityId): AnalysisResult[] {\r\n    return Array.from(this.results.values()).filter(result => \r\n      result.parentAnalysisId && result.parentAnalysisId.equals(parentId)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Gets analysis results by model ID\r\n   */\r\n  public getResultsByModel(modelId: UniqueEntityId): AnalysisResult[] {\r\n    return Array.from(this.results.values()).filter(result => \r\n      result.modelId.equals(modelId)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Gets pending analysis results\r\n   */\r\n  public getPendingResults(): AnalysisResult[] {\r\n    return Array.from(this.results.values()).filter(result => \r\n      result.status === AnalysisStatus.PENDING\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Gets processing analysis results\r\n   */\r\n  public getProcessingResults(): AnalysisResult[] {\r\n    return Array.from(this.results.values()).filter(result => \r\n      result.status === AnalysisStatus.PROCESSING\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Gets completed analysis results\r\n   */\r\n  public getCompletedResults(): AnalysisResult[] {\r\n    return Array.from(this.results.values()).filter(result => \r\n      result.status === AnalysisStatus.COMPLETED\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Gets failed analysis results\r\n   */\r\n  public getFailedResults(): AnalysisResult[] {\r\n    return Array.from(this.results.values()).filter(result => \r\n      result.status === AnalysisStatus.FAILED\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Gets retryable failed results\r\n   */\r\n  public getRetryableFailedResults(): AnalysisResult[] {\r\n    return this.getFailedResults().filter(result => result.isRetryable());\r\n  }\r\n\r\n  /**\r\n   * Gets high confidence results\r\n   */\r\n  public getHighConfidenceResults(threshold: number = 0.8): AnalysisResult[] {\r\n    return Array.from(this.results.values()).filter(result => \r\n      result.hasHighConfidence(threshold)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Gets results with low quality scores\r\n   */\r\n  public getLowQualityResults(threshold: number = 0.5): AnalysisResult[] {\r\n    return Array.from(this.results.values()).filter(result => \r\n      result.getQualityScore() < threshold\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Cancels all pending and processing results\r\n   */\r\n  public cancelAllActiveResults(): AnalysisResult[] {\r\n    const activeResults = Array.from(this.results.values()).filter(result => \r\n      !result.isTerminal()\r\n    );\r\n\r\n    activeResults.forEach(result => result.cancel());\r\n    return activeResults;\r\n  }\r\n\r\n  /**\r\n   * Cancels results by query criteria\r\n   */\r\n  public cancelResults(query: AnalysisResultQuery): AnalysisResult[] {\r\n    const matchingResults = this.findAnalysisResults(query).filter(result => \r\n      !result.isTerminal()\r\n    );\r\n\r\n    matchingResults.forEach(result => result.cancel());\r\n    return matchingResults;\r\n  }\r\n\r\n  /**\r\n   * Gets aggregate statistics\r\n   */\r\n  public getStatistics(): AnalysisResultStatistics {\r\n    const results = Array.from(this.results.values());\r\n    \r\n    if (results.length === 0) {\r\n      return this.createEmptyStatistics();\r\n    }\r\n\r\n    const statusDistribution = results.reduce((acc, result) => {\r\n      acc[result.status] = (acc[result.status] || 0) + 1;\r\n      return acc;\r\n    }, {} as Record<AnalysisStatus, number>);\r\n\r\n    const typeDistribution = results.reduce((acc, result) => {\r\n      acc[result.analysisType] = (acc[result.analysisType] || 0) + 1;\r\n      return acc;\r\n    }, {} as Record<AnalysisType, number>);\r\n\r\n    const totalConfidence = results.reduce((sum, result) => sum + result.confidence, 0);\r\n    const totalProcessingTime = results.reduce((sum, result) => sum + result.processingTime, 0);\r\n    const totalQualityScore = results.reduce((sum, result) => sum + result.getQualityScore(), 0);\r\n\r\n    const successfulResults = results.filter(result => result.isSuccessful());\r\n    const completedResults = results.filter(result => result.isTerminal());\r\n    const retryableFailures = results.filter(result => \r\n      result.status === AnalysisStatus.FAILED && result.isRetryable()\r\n    );\r\n\r\n    return {\r\n      totalResults: results.length,\r\n      statusDistribution,\r\n      typeDistribution,\r\n      averageConfidence: totalConfidence / results.length,\r\n      averageProcessingTime: totalProcessingTime / results.length,\r\n      successRate: successfulResults.length / results.length,\r\n      averageQualityScore: totalQualityScore / results.length,\r\n      retryableFailures: retryableFailures.length,\r\n      completionRate: completedResults.length / results.length,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Gets statistics for a specific time period\r\n   */\r\n  public getStatisticsForPeriod(startDate: Date, endDate: Date): AnalysisResultStatistics {\r\n    const periodResults = Array.from(this.results.values()).filter(result => \r\n      result.createdAt >= startDate && result.createdAt <= endDate\r\n    );\r\n\r\n    const tempAggregate = new AnalysisResultAggregate(periodResults);\r\n    return tempAggregate.getStatistics();\r\n  }\r\n\r\n  /**\r\n   * Creates a batch of analysis results\r\n   */\r\n  public createBatch(requests: CreateAnalysisResultRequest[]): AnalysisResultBatch {\r\n    const results: AnalysisResult[] = [];\r\n    \r\n    for (const request of requests) {\r\n      try {\r\n        const result = this.createAnalysisResult(request);\r\n        results.push(result);\r\n      } catch (error) {\r\n        // Log error but continue with other requests\r\n        console.warn(`Failed to create analysis result for request ${request.requestId}:`, error);\r\n      }\r\n    }\r\n\r\n    const successCount = results.filter(r => r.isSuccessful()).length;\r\n    const failureCount = results.filter(r => r.status === AnalysisStatus.FAILED).length;\r\n    const pendingCount = results.filter(r => r.status === AnalysisStatus.PENDING).length;\r\n    const totalConfidence = results.reduce((sum, r) => sum + r.confidence, 0);\r\n\r\n    return {\r\n      results,\r\n      totalCount: results.length,\r\n      averageConfidence: results.length > 0 ? totalConfidence / results.length : 0,\r\n      successCount,\r\n      failureCount,\r\n      pendingCount,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Groups results by analysis type\r\n   */\r\n  public groupByAnalysisType(): Map<AnalysisType, AnalysisResult[]> {\r\n    const groups = new Map<AnalysisType, AnalysisResult[]>();\r\n    \r\n    Array.from(this.results.values()).forEach(result => {\r\n      if (!groups.has(result.analysisType)) {\r\n        groups.set(result.analysisType, []);\r\n      }\r\n      groups.get(result.analysisType)!.push(result);\r\n    });\r\n    \r\n    return groups;\r\n  }\r\n\r\n  /**\r\n   * Groups results by status\r\n   */\r\n  public groupByStatus(): Map<AnalysisStatus, AnalysisResult[]> {\r\n    const groups = new Map<AnalysisStatus, AnalysisResult[]>();\r\n    \r\n    Array.from(this.results.values()).forEach(result => {\r\n      if (!groups.has(result.status)) {\r\n        groups.set(result.status, []);\r\n      }\r\n      groups.get(result.status)!.push(result);\r\n    });\r\n    \r\n    return groups;\r\n  }\r\n\r\n  /**\r\n   * Gets the most recent results\r\n   */\r\n  public getMostRecentResults(limit: number = 10): AnalysisResult[] {\r\n    return Array.from(this.results.values())\r\n      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())\r\n      .slice(0, limit);\r\n  }\r\n\r\n  /**\r\n   * Gets results sorted by confidence (highest first)\r\n   */\r\n  public getResultsByConfidence(limit?: number): AnalysisResult[] {\r\n    const sorted = Array.from(this.results.values())\r\n      .sort((a, b) => b.confidence - a.confidence);\r\n    \r\n    return limit ? sorted.slice(0, limit) : sorted;\r\n  }\r\n\r\n  /**\r\n   * Gets results sorted by quality score (highest first)\r\n   */\r\n  public getResultsByQuality(limit?: number): AnalysisResult[] {\r\n    const sorted = Array.from(this.results.values())\r\n      .sort((a, b) => b.getQualityScore() - a.getQualityScore());\r\n    \r\n    return limit ? sorted.slice(0, limit) : sorted;\r\n  }\r\n\r\n  private createEmptyStatistics(): AnalysisResultStatistics {\r\n    return {\r\n      totalResults: 0,\r\n      statusDistribution: {} as Record<AnalysisStatus, number>,\r\n      typeDistribution: {} as Record<AnalysisType, number>,\r\n      averageConfidence: 0,\r\n      averageProcessingTime: 0,\r\n      successRate: 0,\r\n      averageQualityScore: 0,\r\n      retryableFailures: 0,\r\n      completionRate: 0,\r\n    };\r\n  }\r\n}"], "version": 3}