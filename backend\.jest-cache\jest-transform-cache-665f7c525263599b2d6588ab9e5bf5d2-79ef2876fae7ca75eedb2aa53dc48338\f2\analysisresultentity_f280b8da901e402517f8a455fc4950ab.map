{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\entities\\analysis-result.entity.ts", "mappings": ";;;AAAA,8FAAyF;AAEzF,yGAA4F;AAC5F,uHAAyG;AACzG,yGAA4F;AA6B5F,IAAY,YAaX;AAbD,WAAY,YAAY;IACtB,qDAAqC,CAAA;IACrC,qEAAqD,CAAA;IACrD,uDAAuC,CAAA;IACvC,2DAA2C,CAAA;IAC3C,iDAAiC,CAAA;IACjC,yCAAyB,CAAA;IACzB,yCAAyB,CAAA;IACzB,6CAA6B,CAAA;IAC7B,iEAAiD,CAAA;IACjD,2DAA2C,CAAA;IAC3C,6CAA6B,CAAA;IAC7B,2DAA2C,CAAA;AAC7C,CAAC,EAbW,YAAY,4BAAZ,YAAY,QAavB;AAED,IAAY,cAQX;AARD,WAAY,cAAc;IACxB,qCAAmB,CAAA;IACnB,2CAAyB,CAAA;IACzB,yCAAuB,CAAA;IACvB,mCAAiB,CAAA;IACjB,yCAAuB,CAAA;IACvB,qCAAmB,CAAA;IACnB,qCAAmB,CAAA;AACrB,CAAC,EARW,cAAc,8BAAd,cAAc,QAQzB;AAgED,IAAY,aAQX;AARD,WAAY,aAAa;IACvB,sDAAqC,CAAA;IACrC,4CAA2B,CAAA;IAC3B,sDAAqC,CAAA;IACrC,gDAA+B,CAAA;IAC/B,kDAAiC,CAAA;IACjC,gDAA+B,CAAA;IAC/B,8CAA6B,CAAA;AAC/B,CAAC,EARW,aAAa,6BAAb,aAAa,QAQxB;AAED,MAAa,cAAe,SAAQ,uCAAsC;IACxE,YAAoB,KAA0B,EAAE,EAAmB;QACjE,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QACjB,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAEM,MAAM,CAAC,MAAM,CAAC,KAAgF,EAAE,EAAmB;QACxH,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC;YACxC,GAAG,KAAK;YACR,gBAAgB,EAAE,EAAE;YACpB,SAAS,EAAE,GAAG;YACd,SAAS,EAAE,GAAG;SACf,EAAE,EAAE,CAAC,CAAC;QAEP,cAAc,CAAC,cAAc,CAAC,IAAI,iEAA0B,CAC1D,cAAc,CAAC,EAAE,EACjB,KAAK,CAAC,SAAS,EACf,KAAK,CAAC,OAAO,EACb,KAAK,CAAC,YAAY,CACnB,CAAC,CAAC;QAEH,OAAO,cAAc,CAAC;IACxB,CAAC;IAEM,MAAM,CAAC,YAAY,CAAC,KAA0B,EAAE,EAAkB;QACvE,OAAO,IAAI,cAAc,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IACvC,CAAC;IAED,UAAU;IACV,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;IAC9B,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;IAC5B,CAAC;IAED,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;IACjC,CAAC;IAED,IAAI,SAAS;QACX,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;IACrC,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;IACtC,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;IACnC,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC3B,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;IACpC,CAAC;IAED,IAAI,IAAI;QACN,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;IAClC,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;IACrC,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IAC1C,CAAC;IAED,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;IAC9E,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;IAC9B,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;IAC9B,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;IAChC,CAAC;IAED,iBAAiB;IAEjB;;OAEG;IACI,eAAe;QACpB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,cAAc,CAAC,OAAO,EAAE,CAAC;YACjD,MAAM,IAAI,KAAK,CAAC,+CAA+C,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QACtF,CAAC;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QACzC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,cAAc,CAAC,UAAU,CAAC;QAC9C,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAElC,IAAI,CAAC,cAAc,CAAC,IAAI,8EAAgC,CACtD,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,KAAK,CAAC,SAAS,EACpB,cAAc,EACd,cAAc,CAAC,UAAU,CAC1B,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,QAAQ,CAAC,UAA0B,EAAE,cAAsB,EAAE,UAAkB;QACpF,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,cAAc,CAAC,UAAU,EAAE,CAAC;YACpD,MAAM,IAAI,KAAK,CAAC,uCAAuC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QAC9E,CAAC;QAED,IAAI,UAAU,GAAG,CAAC,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,cAAc,GAAG,CAAC,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QACzC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,cAAc,CAAC,SAAS,CAAC;QAC7C,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;QACnC,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,cAAc,CAAC;QAC3C,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;QACnC,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QACpC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAElC,IAAI,CAAC,cAAc,CAAC,IAAI,8EAAgC,CACtD,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,KAAK,CAAC,SAAS,EACpB,cAAc,EACd,cAAc,CAAC,SAAS,CACzB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,IAAI,CAAC,YAA0B;QACpC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,cAAc,CAAC,SAAS,EAAE,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QACzC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC;QAC1C,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,YAAY,CAAC;QACvC,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QACpC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAElC,IAAI,CAAC,cAAc,CAAC,IAAI,8EAAgC,CACtD,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,KAAK,CAAC,SAAS,EACpB,cAAc,EACd,cAAc,CAAC,MAAM,CACtB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM;QACX,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,cAAc,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,cAAc,CAAC,MAAM,EAAE,CAAC;YAClG,MAAM,IAAI,KAAK,CAAC,qCAAqC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QAC5E,CAAC;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QACzC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,cAAc,CAAC,SAAS,CAAC;QAC7C,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QACpC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAElC,IAAI,CAAC,cAAc,CAAC,IAAI,8EAAgC,CACtD,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,KAAK,CAAC,SAAS,EACpB,cAAc,EACd,cAAc,CAAC,SAAS,CACzB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,QAAmC;QACvD,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACpB,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ;YACtB,GAAG,QAAQ;SACZ,CAAC;QACF,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAElC,IAAI,CAAC,cAAc,CAAC,IAAI,iEAA0B,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC;IACjG,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,GAAW;QACvB,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACzC,CAAC;QAED,MAAM,aAAa,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAC/C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;YAC7C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACpC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAElC,IAAI,CAAC,cAAc,CAAC,IAAI,iEAA0B,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;QAC7F,CAAC;IACH,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,GAAW;QAC1B,MAAM,aAAa,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAErD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACjB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACjC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAElC,IAAI,CAAC,cAAc,CAAC,IAAI,iEAA0B,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;QAC7F,CAAC;IACH,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,OAAuB;QAC7C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;YAChE,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC1C,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAElC,IAAI,CAAC,cAAc,CAAC,IAAI,iEAA0B,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC;QACjG,CAAC;IACH,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,OAAuB;QAChD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;QAE9E,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACjB,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC7C,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAElC,IAAI,CAAC,cAAc,CAAC,IAAI,iEAA0B,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC;QACjG,CAAC;IACH,CAAC;IAED;;OAEG;IACI,UAAU;QACf,OAAO;YACL,cAAc,CAAC,SAAS;YACxB,cAAc,CAAC,MAAM;YACrB,cAAc,CAAC,SAAS;YACxB,cAAc,CAAC,OAAO;SACvB,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,YAAY;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,cAAc,CAAC,SAAS,CAAC;IACxD,CAAC;IAED;;OAEG;IACI,iBAAiB,CAAC,YAAoB,GAAG;QAC9C,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,SAAS,CAAC;IAC5C,CAAC;IAED;;OAEG;IACI,WAAW;QAChB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YAC5B,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;IAC3E,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,GAAW;QACvB,MAAM,aAAa,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAC/C,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACI,eAAe;QACpB,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;QAE/C,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,OAAO,CAAC,CAAC;QACX,CAAC;QAED,MAAM,OAAO,GAAG;YACd,WAAW,EAAE,GAAG;YAChB,iBAAiB,EAAE,GAAG;YACtB,gBAAgB,EAAE,GAAG;YACrB,iBAAiB,EAAE,GAAG;SACvB,CAAC;QAEF,OAAO,CACL,cAAc,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW;YAChD,cAAc,CAAC,iBAAiB,GAAG,OAAO,CAAC,iBAAiB;YAC5D,cAAc,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB;YAC1D,cAAc,CAAC,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,CAC7D,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,WAAW;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,SAAS,IAAI,KAAK,CAAC;IACrD,CAAC;IAED;;OAEG;IACI,UAAU;QAWf,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE;YACtB,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS;YAC/B,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY;YACrC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;YACzB,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YACjC,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc;YACzC,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE;YACpC,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE;YACjC,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE;SAC7B,CAAC;IACJ,CAAC;IAES,kBAAkB;QAC1B,KAAK,CAAC,kBAAkB,EAAE,CAAC;QAE3B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtE,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC;YACnE,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;YAC/D,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;YAC3D,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAChD,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,cAAc,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;YAC7E,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,cAAc,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;YAC5E,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;CACF;AAraD,wCAqaC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\entities\\analysis-result.entity.ts"], "sourcesContent": ["import { BaseAggregateRoot } from '../../../../shared-kernel/domain/base-aggregate-root';\r\nimport { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { AnalysisResultCreatedEvent } from '../events/analysis-result-created.domain-event';\r\nimport { AnalysisResultStatusChangedEvent } from '../events/analysis-result-status-changed.domain-event';\r\nimport { AnalysisResultUpdatedEvent } from '../events/analysis-result-updated.domain-event';\r\n\r\n/**\r\n * Analysis Result Entity\r\n * \r\n * Domain entity representing the result of an AI analysis operation.\r\n * Contains the input data, output data, metadata, and processing information.\r\n */\r\n\r\nexport interface AnalysisResultProps {\r\n  requestId: string;\r\n  modelId: UniqueEntityId;\r\n  analysisType: AnalysisType;\r\n  inputData: AnalysisInput;\r\n  outputData: AnalysisOutput;\r\n  confidence: number;\r\n  processingTime: number;\r\n  status: AnalysisStatus;\r\n  metadata: AnalysisMetadata;\r\n  tags: string[];\r\n  correlationId?: string;\r\n  parentAnalysisId?: UniqueEntityId;\r\n  childAnalysisIds: UniqueEntityId[];\r\n  errorDetails?: ErrorDetails;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n  completedAt?: Date;\r\n}\r\n\r\nexport enum AnalysisType {\r\n  THREAT_DETECTION = 'threat_detection',\r\n  VULNERABILITY_ASSESSMENT = 'vulnerability_assessment',\r\n  ANOMALY_DETECTION = 'anomaly_detection',\r\n  PATTERN_RECOGNITION = 'pattern_recognition',\r\n  CLASSIFICATION = 'classification',\r\n  REGRESSION = 'regression',\r\n  CLUSTERING = 'clustering',\r\n  NLP_ANALYSIS = 'nlp_analysis',\r\n  RELATIONSHIP_INFERENCE = 'relationship_inference',\r\n  PREDICTIVE_ANALYSIS = 'predictive_analysis',\r\n  RISK_SCORING = 'risk_scoring',\r\n  BEHAVIORAL_ANALYSIS = 'behavioral_analysis'\r\n}\r\n\r\nexport enum AnalysisStatus {\r\n  PENDING = 'pending',\r\n  PROCESSING = 'processing',\r\n  COMPLETED = 'completed',\r\n  FAILED = 'failed',\r\n  CANCELLED = 'cancelled',\r\n  TIMEOUT = 'timeout',\r\n  PARTIAL = 'partial'\r\n}\r\n\r\nexport interface AnalysisInput {\r\n  data: Record<string, any>;\r\n  format: string;\r\n  size: number;\r\n  checksum: string;\r\n  preprocessingApplied: string[];\r\n  validationRules: string[];\r\n}\r\n\r\nexport interface AnalysisOutput {\r\n  results: Record<string, any>;\r\n  format: string;\r\n  size: number;\r\n  checksum: string;\r\n  postprocessingApplied: string[];\r\n  validationStatus: string;\r\n  qualityScore: number;\r\n}\r\n\r\nexport interface AnalysisMetadata {\r\n  version: string;\r\n  algorithm: string;\r\n  parameters: Record<string, any>;\r\n  environment: string;\r\n  resourceUsage: ResourceUsage;\r\n  performanceMetrics: PerformanceMetrics;\r\n  qualityMetrics: QualityMetrics;\r\n}\r\n\r\nexport interface ResourceUsage {\r\n  cpuTime: number;\r\n  memoryUsage: number;\r\n  gpuTime?: number;\r\n  networkIO: number;\r\n  diskIO: number;\r\n}\r\n\r\nexport interface PerformanceMetrics {\r\n  throughput: number;\r\n  latency: number;\r\n  accuracy: number;\r\n  precision: number;\r\n  recall: number;\r\n  f1Score: number;\r\n}\r\n\r\nexport interface QualityMetrics {\r\n  dataQuality: number;\r\n  resultReliability: number;\r\n  consistencyScore: number;\r\n  completenessScore: number;\r\n}\r\n\r\nexport interface ErrorDetails {\r\n  code: string;\r\n  message: string;\r\n  stack?: string;\r\n  context: Record<string, any>;\r\n  retryable: boolean;\r\n  category: ErrorCategory;\r\n}\r\n\r\nexport enum ErrorCategory {\r\n  INPUT_VALIDATION = 'input_validation',\r\n  MODEL_ERROR = 'model_error',\r\n  PROCESSING_ERROR = 'processing_error',\r\n  TIMEOUT_ERROR = 'timeout_error',\r\n  RESOURCE_ERROR = 'resource_error',\r\n  NETWORK_ERROR = 'network_error',\r\n  SYSTEM_ERROR = 'system_error'\r\n}\r\n\r\nexport class AnalysisResult extends BaseAggregateRoot<AnalysisResultProps> {\r\n  private constructor(props: AnalysisResultProps, id?: UniqueEntityId) {\r\n    super(props, id);\r\n    this.validateInvariants();\r\n  }\r\n\r\n  public static create(props: Omit<AnalysisResultProps, 'createdAt' | 'updatedAt' | 'childAnalysisIds'>, id?: UniqueEntityId): AnalysisResult {\r\n    const now = new Date();\r\n    const analysisResult = new AnalysisResult({\r\n      ...props,\r\n      childAnalysisIds: [],\r\n      createdAt: now,\r\n      updatedAt: now\r\n    }, id);\r\n\r\n    analysisResult.addDomainEvent(new AnalysisResultCreatedEvent(\r\n      analysisResult.id,\r\n      props.requestId,\r\n      props.modelId,\r\n      props.analysisType\r\n    ));\r\n\r\n    return analysisResult;\r\n  }\r\n\r\n  public static reconstitute(props: AnalysisResultProps, id: UniqueEntityId): AnalysisResult {\r\n    return new AnalysisResult(props, id);\r\n  }\r\n\r\n  // Getters\r\n  get requestId(): string {\r\n    return this.props.requestId;\r\n  }\r\n\r\n  get modelId(): UniqueEntityId {\r\n    return this.props.modelId;\r\n  }\r\n\r\n  get analysisType(): AnalysisType {\r\n    return this.props.analysisType;\r\n  }\r\n\r\n  get inputData(): AnalysisInput {\r\n    return { ...this.props.inputData };\r\n  }\r\n\r\n  get outputData(): AnalysisOutput {\r\n    return { ...this.props.outputData };\r\n  }\r\n\r\n  get confidence(): number {\r\n    return this.props.confidence;\r\n  }\r\n\r\n  get processingTime(): number {\r\n    return this.props.processingTime;\r\n  }\r\n\r\n  get status(): AnalysisStatus {\r\n    return this.props.status;\r\n  }\r\n\r\n  get metadata(): AnalysisMetadata {\r\n    return { ...this.props.metadata };\r\n  }\r\n\r\n  get tags(): string[] {\r\n    return [...this.props.tags];\r\n  }\r\n\r\n  get correlationId(): string | undefined {\r\n    return this.props.correlationId;\r\n  }\r\n\r\n  get parentAnalysisId(): UniqueEntityId | undefined {\r\n    return this.props.parentAnalysisId;\r\n  }\r\n\r\n  get childAnalysisIds(): UniqueEntityId[] {\r\n    return [...this.props.childAnalysisIds];\r\n  }\r\n\r\n  get errorDetails(): ErrorDetails | undefined {\r\n    return this.props.errorDetails ? { ...this.props.errorDetails } : undefined;\r\n  }\r\n\r\n  get createdAt(): Date {\r\n    return this.props.createdAt;\r\n  }\r\n\r\n  get updatedAt(): Date {\r\n    return this.props.updatedAt;\r\n  }\r\n\r\n  get completedAt(): Date | undefined {\r\n    return this.props.completedAt;\r\n  }\r\n\r\n  // Domain methods\r\n\r\n  /**\r\n   * Marks the analysis as processing\r\n   */\r\n  public startProcessing(): void {\r\n    if (this.props.status !== AnalysisStatus.PENDING) {\r\n      throw new Error(`Cannot start processing analysis in status: ${this.props.status}`);\r\n    }\r\n\r\n    const previousStatus = this.props.status;\r\n    this.props.status = AnalysisStatus.PROCESSING;\r\n    this.props.updatedAt = new Date();\r\n\r\n    this.addDomainEvent(new AnalysisResultStatusChangedEvent(\r\n      this.id,\r\n      this.props.requestId,\r\n      previousStatus,\r\n      AnalysisStatus.PROCESSING\r\n    ));\r\n  }\r\n\r\n  /**\r\n   * Completes the analysis with results\r\n   */\r\n  public complete(outputData: AnalysisOutput, processingTime: number, confidence: number): void {\r\n    if (this.props.status !== AnalysisStatus.PROCESSING) {\r\n      throw new Error(`Cannot complete analysis in status: ${this.props.status}`);\r\n    }\r\n\r\n    if (confidence < 0 || confidence > 1) {\r\n      throw new Error('Confidence must be between 0 and 1');\r\n    }\r\n\r\n    if (processingTime < 0) {\r\n      throw new Error('Processing time cannot be negative');\r\n    }\r\n\r\n    const previousStatus = this.props.status;\r\n    this.props.status = AnalysisStatus.COMPLETED;\r\n    this.props.outputData = outputData;\r\n    this.props.processingTime = processingTime;\r\n    this.props.confidence = confidence;\r\n    this.props.completedAt = new Date();\r\n    this.props.updatedAt = new Date();\r\n\r\n    this.addDomainEvent(new AnalysisResultStatusChangedEvent(\r\n      this.id,\r\n      this.props.requestId,\r\n      previousStatus,\r\n      AnalysisStatus.COMPLETED\r\n    ));\r\n  }\r\n\r\n  /**\r\n   * Marks the analysis as failed with error details\r\n   */\r\n  public fail(errorDetails: ErrorDetails): void {\r\n    if (this.props.status === AnalysisStatus.COMPLETED) {\r\n      throw new Error('Cannot fail a completed analysis');\r\n    }\r\n\r\n    const previousStatus = this.props.status;\r\n    this.props.status = AnalysisStatus.FAILED;\r\n    this.props.errorDetails = errorDetails;\r\n    this.props.completedAt = new Date();\r\n    this.props.updatedAt = new Date();\r\n\r\n    this.addDomainEvent(new AnalysisResultStatusChangedEvent(\r\n      this.id,\r\n      this.props.requestId,\r\n      previousStatus,\r\n      AnalysisStatus.FAILED\r\n    ));\r\n  }\r\n\r\n  /**\r\n   * Cancels the analysis\r\n   */\r\n  public cancel(): void {\r\n    if (this.props.status === AnalysisStatus.COMPLETED || this.props.status === AnalysisStatus.FAILED) {\r\n      throw new Error(`Cannot cancel analysis in status: ${this.props.status}`);\r\n    }\r\n\r\n    const previousStatus = this.props.status;\r\n    this.props.status = AnalysisStatus.CANCELLED;\r\n    this.props.completedAt = new Date();\r\n    this.props.updatedAt = new Date();\r\n\r\n    this.addDomainEvent(new AnalysisResultStatusChangedEvent(\r\n      this.id,\r\n      this.props.requestId,\r\n      previousStatus,\r\n      AnalysisStatus.CANCELLED\r\n    ));\r\n  }\r\n\r\n  /**\r\n   * Updates the analysis metadata\r\n   */\r\n  public updateMetadata(metadata: Partial<AnalysisMetadata>): void {\r\n    this.props.metadata = {\r\n      ...this.props.metadata,\r\n      ...metadata,\r\n    };\r\n    this.props.updatedAt = new Date();\r\n\r\n    this.addDomainEvent(new AnalysisResultUpdatedEvent(this.id, this.props.requestId, 'metadata'));\r\n  }\r\n\r\n  /**\r\n   * Adds a tag to the analysis\r\n   */\r\n  public addTag(tag: string): void {\r\n    if (!tag || tag.trim().length === 0) {\r\n      throw new Error('Tag cannot be empty');\r\n    }\r\n\r\n    const normalizedTag = tag.trim().toLowerCase();\r\n    if (!this.props.tags.includes(normalizedTag)) {\r\n      this.props.tags.push(normalizedTag);\r\n      this.props.updatedAt = new Date();\r\n\r\n      this.addDomainEvent(new AnalysisResultUpdatedEvent(this.id, this.props.requestId, 'tags'));\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Removes a tag from the analysis\r\n   */\r\n  public removeTag(tag: string): void {\r\n    const normalizedTag = tag.trim().toLowerCase();\r\n    const index = this.props.tags.indexOf(normalizedTag);\r\n    \r\n    if (index !== -1) {\r\n      this.props.tags.splice(index, 1);\r\n      this.props.updatedAt = new Date();\r\n\r\n      this.addDomainEvent(new AnalysisResultUpdatedEvent(this.id, this.props.requestId, 'tags'));\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Adds a child analysis ID\r\n   */\r\n  public addChildAnalysis(childId: UniqueEntityId): void {\r\n    if (!this.props.childAnalysisIds.some(id => id.equals(childId))) {\r\n      this.props.childAnalysisIds.push(childId);\r\n      this.props.updatedAt = new Date();\r\n\r\n      this.addDomainEvent(new AnalysisResultUpdatedEvent(this.id, this.props.requestId, 'children'));\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Removes a child analysis ID\r\n   */\r\n  public removeChildAnalysis(childId: UniqueEntityId): void {\r\n    const index = this.props.childAnalysisIds.findIndex(id => id.equals(childId));\r\n    \r\n    if (index !== -1) {\r\n      this.props.childAnalysisIds.splice(index, 1);\r\n      this.props.updatedAt = new Date();\r\n\r\n      this.addDomainEvent(new AnalysisResultUpdatedEvent(this.id, this.props.requestId, 'children'));\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Checks if the analysis is in a terminal state\r\n   */\r\n  public isTerminal(): boolean {\r\n    return [\r\n      AnalysisStatus.COMPLETED,\r\n      AnalysisStatus.FAILED,\r\n      AnalysisStatus.CANCELLED,\r\n      AnalysisStatus.TIMEOUT\r\n    ].includes(this.props.status);\r\n  }\r\n\r\n  /**\r\n   * Checks if the analysis was successful\r\n   */\r\n  public isSuccessful(): boolean {\r\n    return this.props.status === AnalysisStatus.COMPLETED;\r\n  }\r\n\r\n  /**\r\n   * Checks if the analysis has high confidence\r\n   */\r\n  public hasHighConfidence(threshold: number = 0.8): boolean {\r\n    return this.props.confidence >= threshold;\r\n  }\r\n\r\n  /**\r\n   * Gets the analysis duration in milliseconds\r\n   */\r\n  public getDuration(): number | undefined {\r\n    if (!this.props.completedAt) {\r\n      return undefined;\r\n    }\r\n\r\n    return this.props.completedAt.getTime() - this.props.createdAt.getTime();\r\n  }\r\n\r\n  /**\r\n   * Checks if the analysis has a specific tag\r\n   */\r\n  public hasTag(tag: string): boolean {\r\n    const normalizedTag = tag.trim().toLowerCase();\r\n    return this.props.tags.includes(normalizedTag);\r\n  }\r\n\r\n  /**\r\n   * Gets the quality score based on various metrics\r\n   */\r\n  public getQualityScore(): number {\r\n    const { qualityMetrics } = this.props.metadata;\r\n    \r\n    if (!qualityMetrics) {\r\n      return 0;\r\n    }\r\n\r\n    const weights = {\r\n      dataQuality: 0.3,\r\n      resultReliability: 0.3,\r\n      consistencyScore: 0.2,\r\n      completenessScore: 0.2\r\n    };\r\n\r\n    return (\r\n      qualityMetrics.dataQuality * weights.dataQuality +\r\n      qualityMetrics.resultReliability * weights.resultReliability +\r\n      qualityMetrics.consistencyScore * weights.consistencyScore +\r\n      qualityMetrics.completenessScore * weights.completenessScore\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Checks if the error is retryable\r\n   */\r\n  public isRetryable(): boolean {\r\n    return this.props.errorDetails?.retryable ?? false;\r\n  }\r\n\r\n  /**\r\n   * Gets a summary of the analysis result\r\n   */\r\n  public getSummary(): {\r\n    id: string;\r\n    requestId: string;\r\n    analysisType: AnalysisType;\r\n    status: AnalysisStatus;\r\n    confidence: number;\r\n    processingTime: number;\r\n    qualityScore: number;\r\n    isSuccessful: boolean;\r\n    duration?: number;\r\n  } {\r\n    return {\r\n      id: this.id.toString(),\r\n      requestId: this.props.requestId,\r\n      analysisType: this.props.analysisType,\r\n      status: this.props.status,\r\n      confidence: this.props.confidence,\r\n      processingTime: this.props.processingTime,\r\n      qualityScore: this.getQualityScore(),\r\n      isSuccessful: this.isSuccessful(),\r\n      duration: this.getDuration(),\r\n    };\r\n  }\r\n\r\n  protected validateInvariants(): void {\r\n    super.validateInvariants();\r\n\r\n    if (!this.props.requestId || this.props.requestId.trim().length === 0) {\r\n      throw new Error('Request ID is required');\r\n    }\r\n\r\n    if (!this.props.modelId) {\r\n      throw new Error('Model ID is required');\r\n    }\r\n\r\n    if (!Object.values(AnalysisType).includes(this.props.analysisType)) {\r\n      throw new Error('Invalid analysis type');\r\n    }\r\n\r\n    if (!Object.values(AnalysisStatus).includes(this.props.status)) {\r\n      throw new Error('Invalid analysis status');\r\n    }\r\n\r\n    if (this.props.confidence < 0 || this.props.confidence > 1) {\r\n      throw new Error('Confidence must be between 0 and 1');\r\n    }\r\n\r\n    if (this.props.processingTime < 0) {\r\n      throw new Error('Processing time cannot be negative');\r\n    }\r\n\r\n    if (!this.props.inputData) {\r\n      throw new Error('Input data is required');\r\n    }\r\n\r\n    if (!this.props.metadata) {\r\n      throw new Error('Metadata is required');\r\n    }\r\n\r\n    if (!Array.isArray(this.props.tags)) {\r\n      throw new Error('Tags must be an array');\r\n    }\r\n\r\n    if (!Array.isArray(this.props.childAnalysisIds)) {\r\n      throw new Error('Child analysis IDs must be an array');\r\n    }\r\n\r\n    if (this.props.status === AnalysisStatus.COMPLETED && !this.props.outputData) {\r\n      throw new Error('Output data is required for completed analysis');\r\n    }\r\n\r\n    if (this.props.status === AnalysisStatus.FAILED && !this.props.errorDetails) {\r\n      throw new Error('Error details are required for failed analysis');\r\n    }\r\n  }\r\n}"], "version": 3}