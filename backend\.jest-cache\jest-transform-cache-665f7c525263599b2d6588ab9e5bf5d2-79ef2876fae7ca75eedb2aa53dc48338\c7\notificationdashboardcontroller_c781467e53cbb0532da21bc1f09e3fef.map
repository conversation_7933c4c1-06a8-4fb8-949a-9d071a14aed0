{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\controllers\\notification-dashboard.controller.ts", "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAQwB;AACxB,6CAMyB;AACzB,qEAAgE;AAChE,+DAA2D;AAC3D,2EAA8D;AAC9D,yFAA2E;AAC3E,uFAAmF;AACnF,mFAA+E;AAC/E,+FAA0F;AAC1F,oEAA+D;AAC/D,iEAAuD;AAEvD;;;;;;;;;;;;;;;;;GAiBG;AAMI,IAAM,+BAA+B,GAArC,MAAM,+BAA+B;IAC1C,YACmB,gBAA8C;QAA9C,qBAAgB,GAAhB,gBAAgB,CAA8B;IAC9D,CAAC;IAEJ;;OAEG;IA6CG,AAAN,KAAK,CAAC,oBAAoB,CACD,KAAwB,EAChC,IAAU;QAEzB,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IAyCG,AAAN,KAAK,CAAC,kBAAkB,CACJ,OAAe,EACd,WAAmB,KAAK,EAC5B,IAAU;QAEzB,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;IACjF,CAAC;IAED;;OAEG;IA4CG,AAAN,KAAK,CAAC,0BAA0B,CACL,iBAA0B,KAAK,EACpC,YAAoB,KAAK,EAC9B,IAAU;QAEzB,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,0BAA0B,CAAC,cAAc,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IACjG,CAAC;IAED;;OAEG;IA6CG,AAAN,KAAK,CAAC,yBAAyB,CACT,YAAoB,KAAK,EAC3B,OAAe,EACP,kBAA2B,KAAK,EAC3C,IAAU;QAEzB,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,yBAAyB,CAAC,SAAS,EAAE,OAAO,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;IAC1G,CAAC;IAED;;OAEG;IA6CG,AAAN,KAAK,CAAC,8BAA8B,CACd,YAAoB,KAAK,EACzB,SAAiB,EACT,oBAA6B,KAAK,EAC/C,IAAU;QAEzB,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,SAAS,EAAE,SAAS,EAAE,iBAAiB,EAAE,IAAI,CAAC,CAAC;IACnH,CAAC;IAED;;OAEG;IA8CG,AAAN,KAAK,CAAC,iBAAiB,CACJ,MAAc,EACZ,QAAgB,EACf,YAAoB,KAAK,EAC9B,IAAU;QAEzB,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IAC1F,CAAC;IAED;;OAEG;IA2CG,AAAN,KAAK,CAAC,wBAAwB,CACH,iBAA0B,KAAK,EACpC,YAAoB,IAAI,EAC7B,IAAU;QAEzB,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,cAAc,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IAC/F,CAAC;IAED;;OAEG;IA6CG,AAAN,KAAK,CAAC,6BAA6B,CACb,YAAoB,KAAK,EACf,sBAA+B,KAAK,EACnD,IAAU;QAEzB,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,6BAA6B,CAAC,SAAS,EAAE,mBAAmB,EAAE,IAAI,CAAC,CAAC;IACzG,CAAC;CACF,CAAA;AAxbY,0EAA+B;AAoDpC;IA5CL,IAAA,YAAG,EAAC,UAAU,CAAC;IACf,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,wBAAwB;QACjC,WAAW,EAAE,8FAA8F;KAC5G,CAAC;IACD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,uCAAuC,EAAE,CAAC;IACpH,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,yCAAyC,EAAE,CAAC;IAC/H,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,2CAA2C;QACxD,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,OAAO,EAAE;oBACP,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,kBAAkB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBACtC,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBAC/B,eAAe,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBACnC,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBAC7B,YAAY,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;qBACjC;iBACF;gBACD,SAAS,EAAE;oBACT,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBAC3B,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBACzB,gBAAgB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;qBACrC;iBACF;gBACD,MAAM,EAAE;oBACN,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;iBAC1B;gBACD,MAAM,EAAE;oBACN,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;iBAC1B;aACF;SACF;KACF,CAAC;IACD,IAAA,uBAAK,EAAC,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC;IAClC,IAAA,wBAAe,EAAC,oCAAgB,CAAC;IAE/B,WAAA,IAAA,cAAK,EAAC,uBAAc,CAAC,CAAA;IACrB,WAAA,IAAA,oCAAW,GAAE,CAAA;;yDADgB,uCAAiB,oBAAjB,uCAAiB,oDAC1B,kBAAI,oBAAJ,kBAAI;;2EAG1B;AA6CK;IAxCL,IAAA,YAAG,EAAC,kBAAkB,CAAC;IACvB,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,uBAAuB;QAChC,WAAW,EAAE,8EAA8E;KAC5F,CAAC;IACD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,4CAA4C,EAAE,CAAC;IACvH,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,gCAAgC,EAAE,CAAC;IAC5G,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,0CAA0C;QACvD,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gBAC7B,OAAO,EAAE;oBACP,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,YAAY,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBAChC,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBAC7B,eAAe,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBACnC,UAAU,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBAC9B,iBAAiB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;qBACtC;iBACF;gBACD,SAAS,EAAE;oBACT,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,UAAU,EAAE;4BACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BACxB,OAAO,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;4BAC5B,YAAY,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BAChC,UAAU,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;yBAC/B;qBACF;iBACF;aACF;SACF;KACF,CAAC;IACD,IAAA,uBAAK,EAAC,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC;IAEhC,WAAA,IAAA,cAAK,EAAC,SAAS,CAAC,CAAA;IAChB,WAAA,IAAA,cAAK,EAAC,UAAU,CAAC,CAAA;IACjB,WAAA,IAAA,oCAAW,GAAE,CAAA;;yEAAO,kBAAI,oBAAJ,kBAAI;;yEAG1B;AAgDK;IA3CL,IAAA,YAAG,EAAC,QAAQ,CAAC;IACb,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,+BAA+B;QACxC,WAAW,EAAE,wGAAwG;KACtH,CAAC;IACD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,6BAA6B,EAAE,CAAC;IAChH,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,2CAA2C,EAAE,CAAC;IACxH,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,kDAAkD;QAC/D,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,OAAO,EAAE;oBACP,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,OAAO,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;wBAC5B,gBAAgB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBACpC,cAAc,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBAClC,gBAAgB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;qBACrC;iBACF;gBACD,SAAS,EAAE;oBACT,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,UAAU,EAAE;4BACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BACxB,OAAO,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;4BAC5B,YAAY,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BAChC,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BAC1B,mBAAmB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BACvC,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;yBAChC;qBACF;iBACF;gBACD,eAAe,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gBACnC,aAAa,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;aAC5D;SACF;KACF,CAAC;IACD,IAAA,uBAAK,EAAC,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC;IAClC,IAAA,wBAAe,EAAC,oCAAgB,CAAC;IAE/B,WAAA,IAAA,cAAK,EAAC,gBAAgB,CAAC,CAAA;IACvB,WAAA,IAAA,cAAK,EAAC,WAAW,CAAC,CAAA;IAClB,WAAA,IAAA,oCAAW,GAAE,CAAA;;0EAAO,kBAAI,oBAAJ,kBAAI;;iFAG1B;AAiDK;IA5CL,IAAA,YAAG,EAAC,MAAM,CAAC;IACX,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,8BAA8B;QACvC,WAAW,EAAE,oGAAoG;KAClH,CAAC;IACD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,yCAAyC,EAAE,CAAC;IACtH,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,+CAA+C,EAAE,CAAC;IAC1H,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,4BAA4B,EAAE,CAAC;IAChH,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,iDAAiD;QAC9D,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,OAAO,EAAE;oBACP,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBAC7B,sBAAsB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBAC1C,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBAC7B,iBAAiB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;qBACtC;iBACF;gBACD,OAAO,EAAE;oBACP,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,UAAU,EAAE;4BACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BACxB,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BAC/B,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BAC7B,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;yBAC3B;qBACF;iBACF;gBACD,SAAS,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;gBACvD,MAAM,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;gBACpD,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gBAC5B,YAAY,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;aACjC;SACF;KACF,CAAC;IACD,IAAA,uBAAK,EAAC,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC;IAClC,IAAA,wBAAe,EAAC,oCAAgB,CAAC;IAE/B,WAAA,IAAA,cAAK,EAAC,WAAW,CAAC,CAAA;IAClB,WAAA,IAAA,cAAK,EAAC,SAAS,CAAC,CAAA;IAChB,WAAA,IAAA,cAAK,EAAC,iBAAiB,CAAC,CAAA;IACxB,WAAA,IAAA,oCAAW,GAAE,CAAA;;kFAAO,kBAAI,oBAAJ,kBAAI;;gFAG1B;AAiDK;IA5CL,IAAA,YAAG,EAAC,aAAa,CAAC;IAClB,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,mCAAmC;QAC5C,WAAW,EAAE,6FAA6F;KAC3G,CAAC;IACD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,gDAAgD,EAAE,CAAC;IAC7H,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,8CAA8C,EAAE,CAAC;IAC3H,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,yCAAyC,EAAE,CAAC;IAC/H,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,sDAAsD;QACnE,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,OAAO,EAAE;oBACP,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,eAAe,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBACnC,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBAC/B,UAAU,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBAC9B,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;qBAC9B;iBACF;gBACD,SAAS,EAAE;oBACT,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,UAAU,EAAE;4BACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BACxB,YAAY,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BAChC,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BAC/B,UAAU,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BAC9B,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;yBAC5B;qBACF;iBACF;gBACD,MAAM,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;gBACpD,UAAU,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gBAC9B,UAAU,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;aAC/B;SACF;KACF,CAAC;IACD,IAAA,uBAAK,EAAC,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC;IAClC,IAAA,wBAAe,EAAC,oCAAgB,CAAC;IAE/B,WAAA,IAAA,cAAK,EAAC,WAAW,CAAC,CAAA;IAClB,WAAA,IAAA,cAAK,EAAC,WAAW,CAAC,CAAA;IAClB,WAAA,IAAA,cAAK,EAAC,mBAAmB,CAAC,CAAA;IAC1B,WAAA,IAAA,oCAAW,GAAE,CAAA;;kFAAO,kBAAI,oBAAJ,kBAAI;;qFAG1B;AAkDK;IA7CL,IAAA,YAAG,EAAC,QAAQ,CAAC;IACb,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,qBAAqB;QAC9B,WAAW,EAAE,8FAA8F;KAC5G,CAAC;IACD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,iDAAiD,EAAE,CAAC;IAC3H,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,0BAA0B,EAAE,CAAC;IACtG,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,0CAA0C,EAAE,CAAC;IACvH,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,wCAAwC;QACrD,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,OAAO,EAAE;oBACP,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBAC/B,YAAY,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBAChC,cAAc,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBAClC,iBAAiB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;qBACtC;iBACF;gBACD,MAAM,EAAE;oBACN,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,UAAU,EAAE;4BACV,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BACtB,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BAC5B,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BAC5B,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BAC1B,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BAC/B,eAAe,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;yBACpC;qBACF;iBACF;gBACD,MAAM,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;gBACpD,WAAW,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;gBACzD,UAAU,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;aAC/B;SACF;KACF,CAAC;IACD,IAAA,uBAAK,EAAC,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC;IAClC,IAAA,wBAAe,EAAC,oCAAgB,CAAC;IAE/B,WAAA,IAAA,cAAK,EAAC,QAAQ,CAAC,CAAA;IACf,WAAA,IAAA,cAAK,EAAC,UAAU,CAAC,CAAA;IACjB,WAAA,IAAA,cAAK,EAAC,WAAW,CAAC,CAAA;IAClB,WAAA,IAAA,oCAAW,GAAE,CAAA;;iFAAO,kBAAI,oBAAJ,kBAAI;;wEAG1B;AA+CK;IA1CL,IAAA,YAAG,EAAC,QAAQ,CAAC;IACb,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,6BAA6B;QACtC,WAAW,EAAE,wGAAwG;KACtH,CAAC;IACD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,4BAA4B,EAAE,CAAC;IAC/G,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,yCAAyC,EAAE,CAAC;IACtH,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,gDAAgD;QAC7D,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,OAAO,EAAE;oBACP,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBAC/B,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBAC7B,cAAc,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBAClC,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;qBAChC;iBACF;gBACD,MAAM,EAAE;oBACN,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,UAAU,EAAE;4BACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BACxB,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BACzB,cAAc,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BAClC,iBAAiB,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BACrC,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;yBAC9B;qBACF;iBACF;gBACD,OAAO,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;gBACrD,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;aAChC;SACF;KACF,CAAC;IACD,IAAA,uBAAK,EAAC,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC;IAClC,IAAA,wBAAe,EAAC,oCAAgB,CAAC;IAE/B,WAAA,IAAA,cAAK,EAAC,gBAAgB,CAAC,CAAA;IACvB,WAAA,IAAA,cAAK,EAAC,WAAW,CAAC,CAAA;IAClB,WAAA,IAAA,oCAAW,GAAE,CAAA;;0EAAO,kBAAI,oBAAJ,kBAAI;;+EAG1B;AAiDK;IA5CL,IAAA,YAAG,EAAC,WAAW,CAAC;IAChB,IAAA,sBAAY,EAAC;QACZ,OAAO,EAAE,kCAAkC;QAC3C,WAAW,EAAE,+GAA+G;KAC7H,CAAC;IACD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,6CAA6C,EAAE,CAAC;IAC1H,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,qBAAqB,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,sCAAsC,EAAE,CAAC;IAC9H,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,mBAAU,CAAC,EAAE;QACrB,WAAW,EAAE,qDAAqD;QAClE,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,OAAO,EAAE;oBACP,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,cAAc,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBAClC,eAAe,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBACnC,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBAC5B,aAAa,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;qBAClC;iBACF;gBACD,SAAS,EAAE;oBACT,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,UAAU,EAAE;4BACV,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BACtB,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BACxB,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BACzB,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BAC/B,aAAa,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BACjC,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;yBAC5B;qBACF;iBACF;gBACD,KAAK,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;gBACnD,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gBAC/B,YAAY,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;aAC3D;SACF;KACF,CAAC;IACD,IAAA,uBAAK,EAAC,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC;IAClC,IAAA,wBAAe,EAAC,oCAAgB,CAAC;IAE/B,WAAA,IAAA,cAAK,EAAC,WAAW,CAAC,CAAA;IAClB,WAAA,IAAA,cAAK,EAAC,qBAAqB,CAAC,CAAA;IAC5B,WAAA,IAAA,oCAAW,GAAE,CAAA;;0EAAO,kBAAI,oBAAJ,kBAAI;;oFAG1B;0CAvbU,+BAA+B;IAL3C,IAAA,iBAAO,EAAC,wBAAwB,CAAC;IACjC,IAAA,mBAAU,EAAC,wBAAwB,CAAC;IACpC,IAAA,kBAAS,EAAC,6BAAY,EAAE,wBAAU,CAAC;IACnC,IAAA,wBAAe,EAAC,wCAAkB,CAAC;IACnC,IAAA,uBAAa,GAAE;yDAGuB,6DAA4B,oBAA5B,6DAA4B;GAFtD,+BAA+B,CAwb3C", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\controllers\\notification-dashboard.controller.ts"], "sourcesContent": ["import {\r\n  Controller,\r\n  Get,\r\n  Query,\r\n  UseGuards,\r\n  UseInterceptors,\r\n  HttpStatus,\r\n  ValidationPipe,\r\n} from '@nestjs/common';\r\nimport {\r\n  ApiTags,\r\n  ApiOperation,\r\n  ApiResponse,\r\n  ApiQuery,\r\n  ApiBearerAuth,\r\n} from '@nestjs/swagger';\r\nimport { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';\r\nimport { RolesGuard } from '../../auth/guards/roles.guard';\r\nimport { Roles } from '../../auth/decorators/roles.decorator';\r\nimport { CurrentUser } from '../../auth/decorators/current-user.decorator';\r\nimport { LoggingInterceptor } from '../../common/interceptors/logging.interceptor';\r\nimport { CacheInterceptor } from '../../common/interceptors/cache.interceptor';\r\nimport { NotificationDashboardService } from '../services/notification-dashboard.service';\r\nimport { DashboardQueryDto } from '../dto/dashboard-query.dto';\r\nimport { User } from '../../auth/entities/user.entity';\r\n\r\n/**\r\n * Notification Dashboard Controller\r\n * \r\n * Provides comprehensive REST API endpoints for notification monitoring dashboards including:\r\n * - Real-time dashboard data aggregation with live metrics and status updates\r\n * - Health status dashboard APIs with provider monitoring and circuit breaker status\r\n * - Cost analytics dashboard endpoints with budget tracking and optimization insights\r\n * - Performance metrics APIs with trend analysis and comparative benchmarking\r\n * - Alert dashboard endpoints with real-time status updates and escalation tracking\r\n * \r\n * Features:\r\n * - Real-time data aggregation with WebSocket integration\r\n * - Multi-dimensional analytics with flexible filtering and grouping\r\n * - Performance optimization with intelligent caching and data compression\r\n * - Role-based access control with granular dashboard permissions\r\n * - Export capabilities with multiple format support\r\n * - Integration with monitoring infrastructure and alerting systems\r\n */\r\n@ApiTags('Notification Dashboard')\r\n@Controller('notification-dashboard')\r\n@UseGuards(JwtAuthGuard, RolesGuard)\r\n@UseInterceptors(LoggingInterceptor)\r\n@ApiBearerAuth()\r\nexport class NotificationDashboardController {\r\n  constructor(\r\n    private readonly dashboardService: NotificationDashboardService\r\n  ) {}\r\n\r\n  /**\r\n   * Get dashboard overview with key metrics\r\n   */\r\n  @Get('overview')\r\n  @ApiOperation({\r\n    summary: 'Get dashboard overview',\r\n    description: 'Retrieve comprehensive dashboard overview with key performance metrics and status indicators',\r\n  })\r\n  @ApiQuery({ name: 'timeRange', required: false, type: String, description: 'Time range for metrics (default: 24h)' })\r\n  @ApiQuery({ name: 'includeComparison', required: false, type: Boolean, description: 'Include comparison with previous period' })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Dashboard overview retrieved successfully',\r\n    schema: {\r\n      type: 'object',\r\n      properties: {\r\n        summary: {\r\n          type: 'object',\r\n          properties: {\r\n            totalNotifications: { type: 'number' },\r\n            successRate: { type: 'number' },\r\n            avgDeliveryTime: { type: 'number' },\r\n            totalCost: { type: 'number' },\r\n            activeAlerts: { type: 'number' },\r\n          },\r\n        },\r\n        providers: {\r\n          type: 'object',\r\n          properties: {\r\n            healthy: { type: 'number' },\r\n            total: { type: 'number' },\r\n            healthPercentage: { type: 'number' },\r\n          },\r\n        },\r\n        trends: {\r\n          type: 'array',\r\n          items: { type: 'object' },\r\n        },\r\n        alerts: {\r\n          type: 'array',\r\n          items: { type: 'object' },\r\n        },\r\n      },\r\n    },\r\n  })\r\n  @Roles('user', 'admin', 'operator')\r\n  @UseInterceptors(CacheInterceptor)\r\n  async getDashboardOverview(\r\n    @Query(ValidationPipe) query: DashboardQueryDto,\r\n    @CurrentUser() user: User\r\n  ) {\r\n    return await this.dashboardService.getDashboardOverview(query, user);\r\n  }\r\n\r\n  /**\r\n   * Get real-time metrics\r\n   */\r\n  @Get('metrics/realtime')\r\n  @ApiOperation({\r\n    summary: 'Get real-time metrics',\r\n    description: 'Retrieve real-time notification metrics with live updates and current status',\r\n  })\r\n  @ApiQuery({ name: 'metrics', required: false, type: String, description: 'Comma-separated list of metrics to include' })\r\n  @ApiQuery({ name: 'interval', required: false, type: String, description: 'Update interval (default: 30s)' })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Real-time metrics retrieved successfully',\r\n    schema: {\r\n      type: 'object',\r\n      properties: {\r\n        timestamp: { type: 'string' },\r\n        metrics: {\r\n          type: 'object',\r\n          properties: {\r\n            deliveryRate: { type: 'number' },\r\n            errorRate: { type: 'number' },\r\n            avgResponseTime: { type: 'number' },\r\n            queueDepth: { type: 'number' },\r\n            activeConnections: { type: 'number' },\r\n          },\r\n        },\r\n        providers: {\r\n          type: 'array',\r\n          items: {\r\n            type: 'object',\r\n            properties: {\r\n              name: { type: 'string' },\r\n              healthy: { type: 'boolean' },\r\n              responseTime: { type: 'number' },\r\n              throughput: { type: 'number' },\r\n            },\r\n          },\r\n        },\r\n      },\r\n    },\r\n  })\r\n  @Roles('user', 'admin', 'operator')\r\n  async getRealTimeMetrics(\r\n    @Query('metrics') metrics: string,\r\n    @Query('interval') interval: string = '30s',\r\n    @CurrentUser() user: User\r\n  ) {\r\n    return await this.dashboardService.getRealTimeMetrics(metrics, interval, user);\r\n  }\r\n\r\n  /**\r\n   * Get provider health dashboard\r\n   */\r\n  @Get('health')\r\n  @ApiOperation({\r\n    summary: 'Get provider health dashboard',\r\n    description: 'Retrieve comprehensive provider health status with circuit breaker information and performance metrics',\r\n  })\r\n  @ApiQuery({ name: 'includeHistory', required: false, type: Boolean, description: 'Include health history data' })\r\n  @ApiQuery({ name: 'timeRange', required: false, type: String, description: 'Time range for health data (default: 24h)' })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Provider health dashboard retrieved successfully',\r\n    schema: {\r\n      type: 'object',\r\n      properties: {\r\n        overall: {\r\n          type: 'object',\r\n          properties: {\r\n            healthy: { type: 'boolean' },\r\n            healthyProviders: { type: 'number' },\r\n            totalProviders: { type: 'number' },\r\n            healthPercentage: { type: 'number' },\r\n          },\r\n        },\r\n        providers: {\r\n          type: 'array',\r\n          items: {\r\n            type: 'object',\r\n            properties: {\r\n              name: { type: 'string' },\r\n              healthy: { type: 'boolean' },\r\n              responseTime: { type: 'number' },\r\n              uptime: { type: 'number' },\r\n              circuitBreakerState: { type: 'string' },\r\n              lastChecked: { type: 'string' },\r\n            },\r\n          },\r\n        },\r\n        circuitBreakers: { type: 'object' },\r\n        healthHistory: { type: 'array', items: { type: 'object' } },\r\n      },\r\n    },\r\n  })\r\n  @Roles('user', 'admin', 'operator')\r\n  @UseInterceptors(CacheInterceptor)\r\n  async getProviderHealthDashboard(\r\n    @Query('includeHistory') includeHistory: boolean = false,\r\n    @Query('timeRange') timeRange: string = '24h',\r\n    @CurrentUser() user: User\r\n  ) {\r\n    return await this.dashboardService.getProviderHealthDashboard(includeHistory, timeRange, user);\r\n  }\r\n\r\n  /**\r\n   * Get cost analytics dashboard\r\n   */\r\n  @Get('cost')\r\n  @ApiOperation({\r\n    summary: 'Get cost analytics dashboard',\r\n    description: 'Retrieve comprehensive cost analytics with budget tracking, optimization insights, and forecasting',\r\n  })\r\n  @ApiQuery({ name: 'timeRange', required: false, type: String, description: 'Time range for cost data (default: 30d)' })\r\n  @ApiQuery({ name: 'groupBy', required: false, type: String, description: 'Group cost data by provider, channel, or time' })\r\n  @ApiQuery({ name: 'includeForecast', required: false, type: Boolean, description: 'Include cost forecast data' })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Cost analytics dashboard retrieved successfully',\r\n    schema: {\r\n      type: 'object',\r\n      properties: {\r\n        summary: {\r\n          type: 'object',\r\n          properties: {\r\n            totalCost: { type: 'number' },\r\n            avgCostPerNotification: { type: 'number' },\r\n            costTrend: { type: 'string' },\r\n            budgetUtilization: { type: 'number' },\r\n          },\r\n        },\r\n        budgets: {\r\n          type: 'array',\r\n          items: {\r\n            type: 'object',\r\n            properties: {\r\n              name: { type: 'string' },\r\n              utilization: { type: 'number' },\r\n              remaining: { type: 'number' },\r\n              status: { type: 'string' },\r\n            },\r\n          },\r\n        },\r\n        breakdown: { type: 'array', items: { type: 'object' } },\r\n        trends: { type: 'array', items: { type: 'object' } },\r\n        forecast: { type: 'object' },\r\n        optimization: { type: 'object' },\r\n      },\r\n    },\r\n  })\r\n  @Roles('user', 'admin', 'operator')\r\n  @UseInterceptors(CacheInterceptor)\r\n  async getCostAnalyticsDashboard(\r\n    @Query('timeRange') timeRange: string = '30d',\r\n    @Query('groupBy') groupBy: string,\r\n    @Query('includeForecast') includeForecast: boolean = false,\r\n    @CurrentUser() user: User\r\n  ) {\r\n    return await this.dashboardService.getCostAnalyticsDashboard(timeRange, groupBy, includeForecast, user);\r\n  }\r\n\r\n  /**\r\n   * Get performance metrics dashboard\r\n   */\r\n  @Get('performance')\r\n  @ApiOperation({\r\n    summary: 'Get performance metrics dashboard',\r\n    description: 'Retrieve comprehensive performance metrics with trend analysis and comparative benchmarking',\r\n  })\r\n  @ApiQuery({ name: 'timeRange', required: false, type: String, description: 'Time range for performance data (default: 24h)' })\r\n  @ApiQuery({ name: 'providers', required: false, type: String, description: 'Comma-separated list of providers to include' })\r\n  @ApiQuery({ name: 'includeComparison', required: false, type: Boolean, description: 'Include comparison with previous period' })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Performance metrics dashboard retrieved successfully',\r\n    schema: {\r\n      type: 'object',\r\n      properties: {\r\n        summary: {\r\n          type: 'object',\r\n          properties: {\r\n            avgDeliveryTime: { type: 'number' },\r\n            successRate: { type: 'number' },\r\n            throughput: { type: 'number' },\r\n            errorRate: { type: 'number' },\r\n          },\r\n        },\r\n        providers: {\r\n          type: 'array',\r\n          items: {\r\n            type: 'object',\r\n            properties: {\r\n              name: { type: 'string' },\r\n              deliveryTime: { type: 'number' },\r\n              successRate: { type: 'number' },\r\n              throughput: { type: 'number' },\r\n              ranking: { type: 'number' },\r\n            },\r\n          },\r\n        },\r\n        trends: { type: 'array', items: { type: 'object' } },\r\n        comparison: { type: 'object' },\r\n        benchmarks: { type: 'object' },\r\n      },\r\n    },\r\n  })\r\n  @Roles('user', 'admin', 'operator')\r\n  @UseInterceptors(CacheInterceptor)\r\n  async getPerformanceMetricsDashboard(\r\n    @Query('timeRange') timeRange: string = '24h',\r\n    @Query('providers') providers: string,\r\n    @Query('includeComparison') includeComparison: boolean = false,\r\n    @CurrentUser() user: User\r\n  ) {\r\n    return await this.dashboardService.getPerformanceMetricsDashboard(timeRange, providers, includeComparison, user);\r\n  }\r\n\r\n  /**\r\n   * Get alert dashboard\r\n   */\r\n  @Get('alerts')\r\n  @ApiOperation({\r\n    summary: 'Get alert dashboard',\r\n    description: 'Retrieve comprehensive alert dashboard with real-time status updates and escalation tracking',\r\n  })\r\n  @ApiQuery({ name: 'status', required: false, type: String, description: 'Filter by alert status (active, resolved, etc.)' })\r\n  @ApiQuery({ name: 'severity', required: false, type: String, description: 'Filter by alert severity' })\r\n  @ApiQuery({ name: 'timeRange', required: false, type: String, description: 'Time range for alert data (default: 24h)' })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Alert dashboard retrieved successfully',\r\n    schema: {\r\n      type: 'object',\r\n      properties: {\r\n        summary: {\r\n          type: 'object',\r\n          properties: {\r\n            totalAlerts: { type: 'number' },\r\n            activeAlerts: { type: 'number' },\r\n            criticalAlerts: { type: 'number' },\r\n            avgResolutionTime: { type: 'number' },\r\n          },\r\n        },\r\n        alerts: {\r\n          type: 'array',\r\n          items: {\r\n            type: 'object',\r\n            properties: {\r\n              id: { type: 'string' },\r\n              ruleName: { type: 'string' },\r\n              severity: { type: 'string' },\r\n              status: { type: 'string' },\r\n              triggeredAt: { type: 'string' },\r\n              escalationLevel: { type: 'number' },\r\n            },\r\n          },\r\n        },\r\n        trends: { type: 'array', items: { type: 'object' } },\r\n        escalations: { type: 'array', items: { type: 'object' } },\r\n        resolution: { type: 'object' },\r\n      },\r\n    },\r\n  })\r\n  @Roles('user', 'admin', 'operator')\r\n  @UseInterceptors(CacheInterceptor)\r\n  async getAlertDashboard(\r\n    @Query('status') status: string,\r\n    @Query('severity') severity: string,\r\n    @Query('timeRange') timeRange: string = '24h',\r\n    @CurrentUser() user: User\r\n  ) {\r\n    return await this.dashboardService.getAlertDashboard(status, severity, timeRange, user);\r\n  }\r\n\r\n  /**\r\n   * Get queue metrics dashboard\r\n   */\r\n  @Get('queues')\r\n  @ApiOperation({\r\n    summary: 'Get queue metrics dashboard',\r\n    description: 'Retrieve comprehensive queue metrics with depth monitoring, processing rates, and performance analysis',\r\n  })\r\n  @ApiQuery({ name: 'includeHistory', required: false, type: Boolean, description: 'Include queue history data' })\r\n  @ApiQuery({ name: 'timeRange', required: false, type: String, description: 'Time range for queue data (default: 1h)' })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Queue metrics dashboard retrieved successfully',\r\n    schema: {\r\n      type: 'object',\r\n      properties: {\r\n        summary: {\r\n          type: 'object',\r\n          properties: {\r\n            totalQueues: { type: 'number' },\r\n            totalJobs: { type: 'number' },\r\n            processingRate: { type: 'number' },\r\n            avgWaitTime: { type: 'number' },\r\n          },\r\n        },\r\n        queues: {\r\n          type: 'array',\r\n          items: {\r\n            type: 'object',\r\n            properties: {\r\n              name: { type: 'string' },\r\n              depth: { type: 'number' },\r\n              processingRate: { type: 'number' },\r\n              avgProcessingTime: { type: 'number' },\r\n              errorRate: { type: 'number' },\r\n            },\r\n          },\r\n        },\r\n        history: { type: 'array', items: { type: 'object' } },\r\n        performance: { type: 'object' },\r\n      },\r\n    },\r\n  })\r\n  @Roles('user', 'admin', 'operator')\r\n  @UseInterceptors(CacheInterceptor)\r\n  async getQueueMetricsDashboard(\r\n    @Query('includeHistory') includeHistory: boolean = false,\r\n    @Query('timeRange') timeRange: string = '1h',\r\n    @CurrentUser() user: User\r\n  ) {\r\n    return await this.dashboardService.getQueueMetricsDashboard(includeHistory, timeRange, user);\r\n  }\r\n\r\n  /**\r\n   * Get template analytics dashboard\r\n   */\r\n  @Get('templates')\r\n  @ApiOperation({\r\n    summary: 'Get template analytics dashboard',\r\n    description: 'Retrieve comprehensive template analytics with usage patterns, performance metrics, and optimization insights',\r\n  })\r\n  @ApiQuery({ name: 'timeRange', required: false, type: String, description: 'Time range for template data (default: 30d)' })\r\n  @ApiQuery({ name: 'includeOptimization', required: false, type: Boolean, description: 'Include optimization recommendations' })\r\n  @ApiResponse({\r\n    status: HttpStatus.OK,\r\n    description: 'Template analytics dashboard retrieved successfully',\r\n    schema: {\r\n      type: 'object',\r\n      properties: {\r\n        summary: {\r\n          type: 'object',\r\n          properties: {\r\n            totalTemplates: { type: 'number' },\r\n            activeTemplates: { type: 'number' },\r\n            avgUsage: { type: 'number' },\r\n            topPerforming: { type: 'string' },\r\n          },\r\n        },\r\n        templates: {\r\n          type: 'array',\r\n          items: {\r\n            type: 'object',\r\n            properties: {\r\n              id: { type: 'string' },\r\n              name: { type: 'string' },\r\n              usage: { type: 'number' },\r\n              successRate: { type: 'number' },\r\n              avgRenderTime: { type: 'number' },\r\n              ranking: { type: 'number' },\r\n            },\r\n          },\r\n        },\r\n        usage: { type: 'array', items: { type: 'object' } },\r\n        performance: { type: 'object' },\r\n        optimization: { type: 'array', items: { type: 'object' } },\r\n      },\r\n    },\r\n  })\r\n  @Roles('user', 'admin', 'operator')\r\n  @UseInterceptors(CacheInterceptor)\r\n  async getTemplateAnalyticsDashboard(\r\n    @Query('timeRange') timeRange: string = '30d',\r\n    @Query('includeOptimization') includeOptimization: boolean = false,\r\n    @CurrentUser() user: User\r\n  ) {\r\n    return await this.dashboardService.getTemplateAnalyticsDashboard(timeRange, includeOptimization, user);\r\n  }\r\n}\r\n"], "version": 3}