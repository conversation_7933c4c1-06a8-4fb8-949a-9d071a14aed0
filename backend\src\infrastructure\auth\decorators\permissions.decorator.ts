import { SetMetadata } from '@nestjs/common';

/**
 * Permissions decorator for permission-based access control
 * 
 * @param permissions - Array of permission names required to access the resource
 * @example
 * ```typescript
 * @Permissions('read:users', 'write:users')
 * @Get('/users')
 * getUsers() {
 *   // Only users with 'read:users' and 'write:users' permissions can access this
 * }
 * ```
 */
export const Permissions = (...permissions: string[]) => SetMetadata('permissions', permissions);

/**
 * Permission constants for commonly used permissions
 */
export const PERMISSIONS = {
  // User management
  READ_USERS: 'read:users',
  WRITE_USERS: 'write:users',
  DELETE_USERS: 'delete:users',
  
  // Security events
  READ_EVENTS: 'read:events',
  WRITE_EVENTS: 'write:events',
  DELETE_EVENTS: 'delete:events',
  
  // Vulnerabilities
  READ_VULNERABILITIES: 'read:vulnerabilities',
  WRITE_VULNERABILITIES: 'write:vulnerabilities',
  DELETE_VULNERABILITIES: 'delete:vulnerabilities',
  
  // Assets
  READ_ASSETS: 'read:assets',
  WRITE_ASSETS: 'write:assets',
  DELETE_ASSETS: 'delete:assets',
  
  // Threats
  READ_THREATS: 'read:threats',
  WRITE_THREATS: 'write:threats',
  DELETE_THREATS: 'delete:threats',
  
  // Incidents
  READ_INCIDENTS: 'read:incidents',
  WRITE_INCIDENTS: 'write:incidents',
  DELETE_INCIDENTS: 'delete:incidents',
  
  // Reports
  READ_REPORTS: 'read:reports',
  WRITE_REPORTS: 'write:reports',
  DELETE_REPORTS: 'delete:reports',
  
  // Compliance
  READ_COMPLIANCE: 'read:compliance',
  WRITE_COMPLIANCE: 'write:compliance',
  DELETE_COMPLIANCE: 'delete:compliance',
  
  // System administration
  ADMIN_SYSTEM: 'admin:system',
  ADMIN_USERS: 'admin:users',
  ADMIN_ROLES: 'admin:roles',
  ADMIN_PERMISSIONS: 'admin:permissions',
} as const;

/**
 * Type for permission values
 */
export type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS];

/**
 * Permission operation types
 */
export enum PermissionOperation {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  ADMIN = 'admin',
}

/**
 * Permission resource types
 */
export enum PermissionResource {
  USERS = 'users',
  EVENTS = 'events',
  VULNERABILITIES = 'vulnerabilities',
  ASSETS = 'assets',
  THREATS = 'threats',
  INCIDENTS = 'incidents',
  REPORTS = 'reports',
  COMPLIANCE = 'compliance',
  SYSTEM = 'system',
  ROLES = 'roles',
  PERMISSIONS = 'permissions',
}

/**
 * Helper function to build permission strings
 * 
 * @param operation - The operation type
 * @param resource - The resource type
 * @returns Permission string in format "operation:resource"
 */
export function buildPermission(operation: PermissionOperation, resource: PermissionResource): string {
  return `${operation}:${resource}`;
}
