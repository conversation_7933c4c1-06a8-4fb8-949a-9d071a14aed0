{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\value-objects\\confidence-score.value-object.ts", "mappings": ";;;AAAA,iGAA4F;AAE5F;;;;;;GAMG;AACH,MAAa,eAAgB,SAAQ,mCAAuB;IAS1D,YAAY,KAAa;QACvB,KAAK,CAAC,KAAK,CAAC,CAAC;IACf,CAAC;IAES,QAAQ;QAChB,KAAK,CAAC,QAAQ,EAAE,CAAC;QAEjB,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,GAAG,eAAe,CAAC,cAAc,EAAE,CAAC;YACjD,MAAM,IAAI,KAAK,CAAC,wCAAwC,eAAe,CAAC,cAAc,EAAE,CAAC,CAAC;QAC5F,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,GAAG,eAAe,CAAC,cAAc,EAAE,CAAC;YACjD,MAAM,IAAI,KAAK,CAAC,2CAA2C,eAAe,CAAC,cAAc,EAAE,CAAC,CAAC;QAC/F,CAAC;IACH,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,cAAc,CAAC,UAAkB;QAC7C,IAAI,UAAU,GAAG,CAAC,IAAI,UAAU,GAAG,GAAG,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QACD,OAAO,IAAI,eAAe,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,YAAY,CAAC,SAAiB,EAAE,WAAmB;QAC/D,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;QACD,IAAI,SAAS,GAAG,CAAC,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;QACD,OAAO,IAAI,eAAe,CAAC,SAAS,GAAG,WAAW,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,GAAG;QACf,OAAO,IAAI,eAAe,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,MAAM;QAClB,OAAO,IAAI,eAAe,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,IAAI;QAChB,OAAO,IAAI,eAAe,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,OAAO;QACnB,OAAO,IAAI,eAAe,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,OAAO;QACnB,OAAO,IAAI,eAAe,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YACtB,OAAO,UAAU,CAAC;QACpB,CAAC;aAAM,IAAI,IAAI,CAAC,MAAM,GAAG,eAAe,CAAC,aAAa,EAAE,CAAC;YACvD,OAAO,KAAK,CAAC;QACf,CAAC;aAAM,IAAI,IAAI,CAAC,MAAM,GAAG,eAAe,CAAC,gBAAgB,EAAE,CAAC;YAC1D,OAAO,QAAQ,CAAC;QAClB,CAAC;aAAM,IAAI,IAAI,CAAC,MAAM,GAAG,eAAe,CAAC,cAAc,EAAE,CAAC;YACxD,OAAO,MAAM,CAAC;QAChB,CAAC;aAAM,CAAC;YACN,OAAO,WAAW,CAAC;QACrB,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK;QACV,OAAO,IAAI,CAAC,MAAM,GAAG,eAAe,CAAC,aAAa,CAAC;IACrD,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,OAAO,IAAI,CAAC,MAAM,IAAI,eAAe,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,GAAG,eAAe,CAAC,gBAAgB,CAAC;IACxG,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO,IAAI,CAAC,MAAM,IAAI,eAAe,CAAC,cAAc,CAAC;IACvD,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,SAAiB;QACrC,OAAO,IAAI,CAAC,MAAM,IAAI,SAAS,CAAC;IAClC,CAAC;IAED;;OAEG;IACI,YAAY;QACjB,OAAO,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,WAAmB,CAAC;QAC5C,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC;IACrD,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,KAAsB,EAAE,SAAiB,GAAG;QAC7D,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;QACzE,OAAO,IAAI,eAAe,CAAC,aAAa,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACI,OAAO;QACZ,OAAO,IAAI,eAAe,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACI,QAAQ,CAAC,MAAc;QAC5B,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACpC,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,eAAe,CAAC,cAAc,CAAC,CAAC,CAAC;IAC/E,CAAC;IAED;;OAEG;IACI,GAAG,CAAC,KAAsB;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1C,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,eAAe,CAAC,cAAc,CAAC,CAAC,CAAC;IAC/E,CAAC;IAED;;OAEG;IACI,QAAQ,CAAC,KAAsB;QACpC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1C,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,eAAe,CAAC,cAAc,CAAC,CAAC,CAAC;IAC/E,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,KAAsB;QAC1C,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,KAAsB;QACzC,OAAO,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;IACpC,CAAC;IAED;;OAEG;IACI,UAAU,CAAC,KAAsB;QACtC,OAAO,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;IACpC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,WAAmB,CAAC;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QACtC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC;QAC1D,OAAO,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,OAAO,GAAG,IAAI,CAAC,kBAAkB,EAAE,KAAK,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC;IAC7D,CAAC;IAED;;OAEG;IACI,MAAM;QACX,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,UAAU,EAAE,IAAI,CAAC,YAAY,EAAE;YAC/B,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;YACtB,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE;YACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE;YACzB,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE;SACpB,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,QAAQ,CAAC,IAAyB;QAC9C,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,OAAO,CAAC,MAAyB;QAC7C,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC;QAED,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjE,OAAO,IAAI,eAAe,CAAC,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,eAAe,CAAC,MAAyB,EAAE,OAAiB;QACxE,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;QAED,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,EAAE,CAAC,CAAC,CAAC;QACrE,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACjD,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;QACjG,OAAO,IAAI,eAAe,CAAC,WAAW,GAAG,WAAW,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,GAAG,CAAC,MAAyB;QACzC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;QAChE,OAAO,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,GAAG,CAAC,MAAyB;QACzC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;QAChE,OAAO,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC;IACvC,CAAC;;AA1TH,0CA2TC;AA1TwB,8BAAc,GAAG,GAAG,CAAC;AACrB,8BAAc,GAAG,GAAG,CAAC;AAE5C,8BAA8B;AACP,6BAAa,GAAG,GAAG,CAAC;AACpB,gCAAgB,GAAG,GAAG,CAAC;AACvB,8BAAc,GAAG,GAAG,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\value-objects\\confidence-score.value-object.ts"], "sourcesContent": ["import { BaseValueObject } from '../../../../shared-kernel/value-objects/base-value-object';\r\n\r\n/**\r\n * Confidence Score Value Object\r\n * \r\n * Represents a confidence score for AI predictions and analysis results.\r\n * Ensures the score is within valid bounds (0.0 to 1.0) and provides\r\n * utility methods for confidence level classification.\r\n */\r\nexport class ConfidenceScore extends BaseValueObject<number> {\r\n  public static readonly MIN_CONFIDENCE = 0.0;\r\n  public static readonly MAX_CONFIDENCE = 1.0;\r\n  \r\n  // Confidence level thresholds\r\n  public static readonly LOW_THRESHOLD = 0.3;\r\n  public static readonly MEDIUM_THRESHOLD = 0.7;\r\n  public static readonly HIGH_THRESHOLD = 0.9;\r\n\r\n  constructor(value: number) {\r\n    super(value);\r\n  }\r\n\r\n  protected validate(): void {\r\n    super.validate();\r\n    \r\n    if (typeof this._value !== 'number') {\r\n      throw new Error('Confidence score must be a number');\r\n    }\r\n\r\n    if (isNaN(this._value)) {\r\n      throw new Error('Confidence score cannot be NaN');\r\n    }\r\n\r\n    if (!isFinite(this._value)) {\r\n      throw new Error('Confidence score must be finite');\r\n    }\r\n\r\n    if (this._value < ConfidenceScore.MIN_CONFIDENCE) {\r\n      throw new Error(`Confidence score cannot be less than ${ConfidenceScore.MIN_CONFIDENCE}`);\r\n    }\r\n\r\n    if (this._value > ConfidenceScore.MAX_CONFIDENCE) {\r\n      throw new Error(`Confidence score cannot be greater than ${ConfidenceScore.MAX_CONFIDENCE}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Creates a confidence score from a percentage (0-100)\r\n   */\r\n  public static fromPercentage(percentage: number): ConfidenceScore {\r\n    if (percentage < 0 || percentage > 100) {\r\n      throw new Error('Percentage must be between 0 and 100');\r\n    }\r\n    return new ConfidenceScore(percentage / 100);\r\n  }\r\n\r\n  /**\r\n   * Creates a confidence score from a fraction (numerator/denominator)\r\n   */\r\n  public static fromFraction(numerator: number, denominator: number): ConfidenceScore {\r\n    if (denominator === 0) {\r\n      throw new Error('Denominator cannot be zero');\r\n    }\r\n    if (numerator < 0 || denominator < 0) {\r\n      throw new Error('Numerator and denominator must be non-negative');\r\n    }\r\n    return new ConfidenceScore(numerator / denominator);\r\n  }\r\n\r\n  /**\r\n   * Creates a low confidence score\r\n   */\r\n  public static low(): ConfidenceScore {\r\n    return new ConfidenceScore(ConfidenceScore.LOW_THRESHOLD);\r\n  }\r\n\r\n  /**\r\n   * Creates a medium confidence score\r\n   */\r\n  public static medium(): ConfidenceScore {\r\n    return new ConfidenceScore(ConfidenceScore.MEDIUM_THRESHOLD);\r\n  }\r\n\r\n  /**\r\n   * Creates a high confidence score\r\n   */\r\n  public static high(): ConfidenceScore {\r\n    return new ConfidenceScore(ConfidenceScore.HIGH_THRESHOLD);\r\n  }\r\n\r\n  /**\r\n   * Creates a maximum confidence score\r\n   */\r\n  public static maximum(): ConfidenceScore {\r\n    return new ConfidenceScore(ConfidenceScore.MAX_CONFIDENCE);\r\n  }\r\n\r\n  /**\r\n   * Creates a minimum confidence score\r\n   */\r\n  public static minimum(): ConfidenceScore {\r\n    return new ConfidenceScore(ConfidenceScore.MIN_CONFIDENCE);\r\n  }\r\n\r\n  /**\r\n   * Gets the confidence level as a string\r\n   */\r\n  public getLevel(): 'very_low' | 'low' | 'medium' | 'high' | 'very_high' {\r\n    if (this._value < 0.2) {\r\n      return 'very_low';\r\n    } else if (this._value < ConfidenceScore.LOW_THRESHOLD) {\r\n      return 'low';\r\n    } else if (this._value < ConfidenceScore.MEDIUM_THRESHOLD) {\r\n      return 'medium';\r\n    } else if (this._value < ConfidenceScore.HIGH_THRESHOLD) {\r\n      return 'high';\r\n    } else {\r\n      return 'very_high';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Checks if the confidence is low\r\n   */\r\n  public isLow(): boolean {\r\n    return this._value < ConfidenceScore.LOW_THRESHOLD;\r\n  }\r\n\r\n  /**\r\n   * Checks if the confidence is medium\r\n   */\r\n  public isMedium(): boolean {\r\n    return this._value >= ConfidenceScore.LOW_THRESHOLD && this._value < ConfidenceScore.MEDIUM_THRESHOLD;\r\n  }\r\n\r\n  /**\r\n   * Checks if the confidence is high\r\n   */\r\n  public isHigh(): boolean {\r\n    return this._value >= ConfidenceScore.HIGH_THRESHOLD;\r\n  }\r\n\r\n  /**\r\n   * Checks if the confidence meets a minimum threshold\r\n   */\r\n  public meetsThreshold(threshold: number): boolean {\r\n    return this._value >= threshold;\r\n  }\r\n\r\n  /**\r\n   * Gets the confidence as a percentage\r\n   */\r\n  public toPercentage(): number {\r\n    return this._value * 100;\r\n  }\r\n\r\n  /**\r\n   * Gets the confidence as a percentage string\r\n   */\r\n  public toPercentageString(decimals: number = 1): string {\r\n    return `${(this._value * 100).toFixed(decimals)}%`;\r\n  }\r\n\r\n  /**\r\n   * Combines this confidence with another using weighted average\r\n   */\r\n  public combineWith(other: ConfidenceScore, weight: number = 0.5): ConfidenceScore {\r\n    if (weight < 0 || weight > 1) {\r\n      throw new Error('Weight must be between 0 and 1');\r\n    }\r\n    \r\n    const combinedValue = this._value * weight + other._value * (1 - weight);\r\n    return new ConfidenceScore(combinedValue);\r\n  }\r\n\r\n  /**\r\n   * Gets the inverse confidence (1 - confidence)\r\n   */\r\n  public inverse(): ConfidenceScore {\r\n    return new ConfidenceScore(1 - this._value);\r\n  }\r\n\r\n  /**\r\n   * Multiplies this confidence by a factor\r\n   */\r\n  public multiply(factor: number): ConfidenceScore {\r\n    if (factor < 0) {\r\n      throw new Error('Factor cannot be negative');\r\n    }\r\n    \r\n    const result = this._value * factor;\r\n    return new ConfidenceScore(Math.min(result, ConfidenceScore.MAX_CONFIDENCE));\r\n  }\r\n\r\n  /**\r\n   * Adds another confidence score (capped at maximum)\r\n   */\r\n  public add(other: ConfidenceScore): ConfidenceScore {\r\n    const result = this._value + other._value;\r\n    return new ConfidenceScore(Math.min(result, ConfidenceScore.MAX_CONFIDENCE));\r\n  }\r\n\r\n  /**\r\n   * Subtracts another confidence score (floored at minimum)\r\n   */\r\n  public subtract(other: ConfidenceScore): ConfidenceScore {\r\n    const result = this._value - other._value;\r\n    return new ConfidenceScore(Math.max(result, ConfidenceScore.MIN_CONFIDENCE));\r\n  }\r\n\r\n  /**\r\n   * Gets the absolute difference between this and another confidence score\r\n   */\r\n  public differenceFrom(other: ConfidenceScore): number {\r\n    return Math.abs(this._value - other._value);\r\n  }\r\n\r\n  /**\r\n   * Checks if this confidence is greater than another\r\n   */\r\n  public isGreaterThan(other: ConfidenceScore): boolean {\r\n    return this._value > other._value;\r\n  }\r\n\r\n  /**\r\n   * Checks if this confidence is less than another\r\n   */\r\n  public isLessThan(other: ConfidenceScore): boolean {\r\n    return this._value < other._value;\r\n  }\r\n\r\n  /**\r\n   * Rounds the confidence to a specified number of decimal places\r\n   */\r\n  public round(decimals: number = 2): ConfidenceScore {\r\n    const factor = Math.pow(10, decimals);\r\n    const rounded = Math.round(this._value * factor) / factor;\r\n    return new ConfidenceScore(rounded);\r\n  }\r\n\r\n  /**\r\n   * Converts to a human-readable string\r\n   */\r\n  public toString(): string {\r\n    return `${this.toPercentageString()} (${this.getLevel()})`;\r\n  }\r\n\r\n  /**\r\n   * Converts to JSON representation\r\n   */\r\n  public toJSON(): Record<string, any> {\r\n    return {\r\n      value: this._value,\r\n      percentage: this.toPercentage(),\r\n      level: this.getLevel(),\r\n      isHigh: this.isHigh(),\r\n      isMedium: this.isMedium(),\r\n      isLow: this.isLow(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Creates a ConfidenceScore from JSON\r\n   */\r\n  public static fromJSON(json: Record<string, any>): ConfidenceScore {\r\n    return new ConfidenceScore(json.value);\r\n  }\r\n\r\n  /**\r\n   * Calculates the average of multiple confidence scores\r\n   */\r\n  public static average(scores: ConfidenceScore[]): ConfidenceScore {\r\n    if (scores.length === 0) {\r\n      throw new Error('Cannot calculate average of empty array');\r\n    }\r\n    \r\n    const sum = scores.reduce((acc, score) => acc + score._value, 0);\r\n    return new ConfidenceScore(sum / scores.length);\r\n  }\r\n\r\n  /**\r\n   * Calculates the weighted average of multiple confidence scores\r\n   */\r\n  public static weightedAverage(scores: ConfidenceScore[], weights: number[]): ConfidenceScore {\r\n    if (scores.length === 0) {\r\n      throw new Error('Cannot calculate weighted average of empty array');\r\n    }\r\n    \r\n    if (scores.length !== weights.length) {\r\n      throw new Error('Scores and weights arrays must have the same length');\r\n    }\r\n    \r\n    const totalWeight = weights.reduce((acc, weight) => acc + weight, 0);\r\n    if (totalWeight === 0) {\r\n      throw new Error('Total weight cannot be zero');\r\n    }\r\n    \r\n    const weightedSum = scores.reduce((acc, score, index) => acc + score._value * weights[index], 0);\r\n    return new ConfidenceScore(weightedSum / totalWeight);\r\n  }\r\n\r\n  /**\r\n   * Gets the maximum confidence from an array\r\n   */\r\n  public static max(scores: ConfidenceScore[]): ConfidenceScore {\r\n    if (scores.length === 0) {\r\n      throw new Error('Cannot find maximum of empty array');\r\n    }\r\n    \r\n    const maxValue = Math.max(...scores.map(score => score._value));\r\n    return new ConfidenceScore(maxValue);\r\n  }\r\n\r\n  /**\r\n   * Gets the minimum confidence from an array\r\n   */\r\n  public static min(scores: ConfidenceScore[]): ConfidenceScore {\r\n    if (scores.length === 0) {\r\n      throw new Error('Cannot find minimum of empty array');\r\n    }\r\n    \r\n    const minValue = Math.min(...scores.map(score => score._value));\r\n    return new ConfidenceScore(minValue);\r\n  }\r\n}"], "version": 3}