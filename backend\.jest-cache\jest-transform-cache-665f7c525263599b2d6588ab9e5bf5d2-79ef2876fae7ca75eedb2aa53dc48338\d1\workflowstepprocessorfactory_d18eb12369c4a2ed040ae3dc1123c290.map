{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\services\\workflow-step-processor.factory.ts", "mappings": ";;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAC/C,mGAA6F;AAC7F,yGAAmG;AACnG,0FAAqF;AACrF,sFAAiF;AACjF,0FAAqF;AACrF,0FAAqF;AACrF,8FAAyF;AASzF;;GAEG;AACH,MAAM,yBAAyB;IAG7B,YACmB,sBAA0D,EAC1D,yBAAgE,EAChE,SAA2B;QAF3B,2BAAsB,GAAtB,sBAAsB,CAAoC;QAC1D,8BAAyB,GAAzB,yBAAyB,CAAuC;QAChE,cAAS,GAAT,SAAS,CAAkB;QAL7B,WAAM,GAAG,IAAI,eAAM,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;IAMlE,CAAC;IAEJ,KAAK,CAAC,OAAO,CAAC,cAAmB,EAAE,OAAY;QAC7C,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,cAAc,CAAC,EAAE,EAAE,CAAC,CAAC;YAEvE,MAAM,MAAM,GAAG,cAAc,CAAC,MAAM,IAAI,EAAE,CAAC;YAE3C,4BAA4B;YAC5B,MAAM,gBAAgB,GAAG;gBACvB,EAAE,EAAE,GAAG,OAAO,CAAC,WAAW,IAAI,cAAc,CAAC,EAAE,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;gBAC/D,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE;gBAChC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE;gBAC9B,OAAO,EAAE,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,CAAC;gBACnD,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,OAAO;gBAClC,UAAU,EAAE,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC;gBACpE,QAAQ,EAAE;oBACR,UAAU,EAAE,OAAO,CAAC,UAAU;oBAC9B,WAAW,EAAE,OAAO,CAAC,WAAW;oBAChC,MAAM,EAAE,cAAc,CAAC,EAAE;oBACzB,GAAG,MAAM,CAAC,QAAQ;iBACnB;gBACD,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE;gBAC9B,YAAY,EAAE,OAAO,CAAC,KAAK,CAAC,YAAY,IAAI,EAAE;gBAC9C,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,EAAE;aAC/B,CAAC;YAEF,kCAAkC;YAClC,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,QAAQ,CAAC;YAC7C,MAAM,IAAI,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;YAErF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,gBAAgB,CAAC,EAAE,EAAE,CAAC,CAAC;YAC9E,OAAO;gBACL,cAAc,EAAE,gBAAgB,CAAC,EAAE;gBACnC,MAAM,EAAE,QAAQ;gBAChB,OAAO,EAAE,gBAAgB,CAAC,OAAO;gBACjC,UAAU,EAAE,gBAAgB,CAAC,UAAU,CAAC,MAAM;gBAC9C,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACvF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,MAAW,EAAE,OAAY;QACpD,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACtB,eAAe;YACf,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACrF,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,cAAc,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;QACtF,CAAC;aAAM,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YAC1B,qBAAqB;YACrB,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC1D,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,UAAe,EAAE,OAAY;QAC3D,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YAC9B,OAAO,UAAU,CAAC;QACpB,CAAC;aAAM,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;YAC1C,2CAA2C;YAC3C,IAAI,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC/B,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;gBACxE,OAAO,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;YAC/D,CAAC;YACD,OAAO,CAAC,UAAU,CAAC,CAAC;QACtB,CAAC;aAAM,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;YAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YACjE,OAAO,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;QAC/D,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,kBAAkB,CAAC,OAAe,EAAE,OAAY;QACtD,OAAO,OAAO,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,KAAK,EAAE,UAAU,EAAE,EAAE;YAC/D,IAAI,CAAC;gBACH,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,CAAC;gBAC7D,OAAO,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;YAC7B,CAAC;YAAC,MAAM,CAAC;gBACP,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,aAAa,CAAC,KAAa,EAAE,OAAY;QAC/C,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAI,KAAK,GAAG,OAAO,CAAC;QACpB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;gBAAE,OAAO,SAAS,CAAC;YAC5D,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AAED;;GAEG;AACH,MAAM,kBAAkB;IAAxB;QACmB,WAAM,GAAG,IAAI,eAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IA0BhE,CAAC;IAxBC,KAAK,CAAC,OAAO,CAAC,cAAmB,EAAE,OAAY;QAC7C,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,cAAc,CAAC,EAAE,EAAE,CAAC,CAAC;YAEhE,MAAM,MAAM,GAAG,cAAc,CAAC,MAAM,IAAI,EAAE,CAAC;YAC3C,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,mBAAmB;YAEvD,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAExB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,cAAc,CAAC,EAAE,EAAE,CAAC,CAAC;YAChE,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAChF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;CACF;AAED;;GAEG;AACH,MAAM,sBAAsB;IAA5B;QACmB,WAAM,GAAG,IAAI,eAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;IA8DpE,CAAC;IA5DC,KAAK,CAAC,OAAO,CAAC,cAAmB,EAAE,OAAY;QAC7C,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,cAAc,CAAC,EAAE,EAAE,CAAC,CAAC;YAEpE,MAAM,MAAM,GAAG,cAAc,CAAC,MAAM,IAAI,EAAE,CAAC;YAC3C,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;YAEnC,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;YAC/D,CAAC;YAED,8BAA8B;YAC9B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAEhE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,MAAM,EAAE,CAAC,CAAC;YACtD,OAAO;gBACL,SAAS,EAAE,SAAS;gBACpB,MAAM,EAAE,MAAM;gBACd,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACpF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,SAAc,EAAE,OAAY;QAC1D,0EAA0E;QAC1E,IAAI,OAAO,SAAS,KAAK,SAAS,EAAE,CAAC;YACnC,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,IAAI,OAAO,SAAS,KAAK,QAAQ,IAAI,SAAS,CAAC,KAAK,IAAI,SAAS,CAAC,QAAQ,IAAI,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;YAChH,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAChE,MAAM,YAAY,GAAG,SAAS,CAAC,KAAK,CAAC;YAErC,QAAQ,SAAS,CAAC,QAAQ,EAAE,CAAC;gBAC3B,KAAK,IAAI,CAAC,CAAC,OAAO,UAAU,IAAI,YAAY,CAAC;gBAC7C,KAAK,KAAK,CAAC,CAAC,OAAO,UAAU,IAAI,YAAY,CAAC;gBAC9C,KAAK,IAAI,CAAC,CAAC,OAAO,UAAU,GAAG,YAAY,CAAC;gBAC5C,KAAK,KAAK,CAAC,CAAC,OAAO,UAAU,IAAI,YAAY,CAAC;gBAC9C,KAAK,IAAI,CAAC,CAAC,OAAO,UAAU,GAAG,YAAY,CAAC;gBAC5C,KAAK,KAAK,CAAC,CAAC,OAAO,UAAU,IAAI,YAAY,CAAC;gBAC9C,OAAO,CAAC,CAAC,OAAO,KAAK,CAAC;YACxB,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,aAAa,CAAC,KAAa,EAAE,OAAY;QAC/C,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAI,KAAK,GAAG,OAAO,CAAC;QACpB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;gBAAE,OAAO,SAAS,CAAC;YAC5D,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AAED;;GAEG;AACH,MAAM,qBAAqB;IAA3B;QACmB,WAAM,GAAG,IAAI,eAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;IA0CnE,CAAC;IAxCC,KAAK,CAAC,OAAO,CAAC,cAAmB,EAAE,OAAY;QAC7C,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,cAAc,CAAC,EAAE,EAAE,CAAC,CAAC;YAEnE,MAAM,MAAM,GAAG,cAAc,CAAC,MAAM,IAAI,EAAE,CAAC;YAC3C,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,IAAI,EAAE,CAAC;YAEzC,2BAA2B;YAC3B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;gBACrD,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAC7D,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,cAAc,CAAC,EAAE,EAAE,CAAC,CAAC;YACnE,OAAO;gBACL,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;gBACjC,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACnF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,KAAU,EAAE,OAAY;QAC3C,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACvD,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QACzD,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,aAAa,CAAC,KAAa,EAAE,OAAY;QAC/C,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAI,KAAK,GAAG,OAAO,CAAC;QACpB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;gBAAE,OAAO,SAAS,CAAC;YAC5D,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AAED;;GAEG;AACH,MAAM,wBAAwB;IAA9B;QACmB,WAAM,GAAG,IAAI,eAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;IAwEtE,CAAC;IAtEC,KAAK,CAAC,OAAO,CAAC,cAAmB,EAAE,OAAY;QAC7C,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,cAAc,CAAC,EAAE,EAAE,CAAC,CAAC;YAEvE,MAAM,MAAM,GAAG,cAAc,CAAC,MAAM,IAAI,EAAE,CAAC;YAC3C,MAAM,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YACxD,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,KAAK,CAAC;YACtC,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,EAAE,CAAC;YACrC,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAEnF,gDAAgD;YAChD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;YAExE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,cAAc,CAAC,EAAE,EAAE,CAAC,CAAC;YACvE,OAAO;gBACL,GAAG;gBACH,MAAM;gBACN,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACvF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,GAAW,EAAE,MAAc,EAAE,OAAY,EAAE,IAAS;QAChF,yCAAyC;QACzC,qDAAqD;QACrD,OAAO;YACL,MAAM,EAAE,GAAG;YACX,IAAI,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;SACxB,CAAC;IACJ,CAAC;IAEO,iBAAiB,CAAC,GAAW,EAAE,OAAY;QACjD,OAAO,GAAG,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,KAAK,EAAE,UAAU,EAAE,EAAE;YAC3D,IAAI,CAAC;gBACH,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,CAAC;gBAC7D,OAAO,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;YAC7B,CAAC;YAAC,MAAM,CAAC;gBACP,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB,CAAC,KAAU,EAAE,OAAY;QAC/C,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,OAAO,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAChD,CAAC;aAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACvD,MAAM,MAAM,GAAG,EAAE,CAAC;YAClB,KAAK,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC/C,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YACpD,CAAC;YACD,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,aAAa,CAAC,KAAa,EAAE,OAAY;QAC/C,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAI,KAAK,GAAG,OAAO,CAAC;QACpB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;gBAAE,OAAO,SAAS,CAAC;YAC5D,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AAED;;;;;;;;;;GAUG;AAEI,IAAM,4BAA4B,oCAAlC,MAAM,4BAA4B;IAIvC,YACmB,aAA4B,EAC5B,sBAA0D,EAC1D,yBAAgE,EAChE,aAAwC,EACxC,WAAoC,EACpC,aAAwC,EACxC,aAAwC,EACxC,eAA4C;QAP5C,kBAAa,GAAb,aAAa,CAAe;QAC5B,2BAAsB,GAAtB,sBAAsB,CAAoC;QAC1D,8BAAyB,GAAzB,yBAAyB,CAAuC;QAChE,kBAAa,GAAb,aAAa,CAA2B;QACxC,gBAAW,GAAX,WAAW,CAAyB;QACpC,kBAAa,GAAb,aAAa,CAA2B;QACxC,kBAAa,GAAb,aAAa,CAA2B;QACxC,oBAAe,GAAf,eAAe,CAA6B;QAX9C,WAAM,GAAG,IAAI,eAAM,CAAC,8BAA4B,CAAC,IAAI,CAAC,CAAC;QACvD,eAAU,GAAwC,IAAI,GAAG,EAAE,CAAC;QAY3E,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,QAAgB;QAC3B,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAChD,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,qCAAqC,QAAQ,EAAE,CAAC,CAAC;QACnE,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,QAAgB,EAAE,SAAiC;QACnE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QACzC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,QAAQ,EAAE,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACK,oBAAoB;QAC1B,sBAAsB;QACtB,MAAM,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;QAC5B,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3C,SAAS,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QACvC,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3C,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3C,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAE/C,+BAA+B;QAC/B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,yBAAyB,CAC/D,IAAI,CAAC,sBAAsB,EAC3B,IAAI,CAAC,yBAAyB,EAC9B,SAAS,CACV,CAAC,CAAC;QACH,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,kBAAkB,EAAE,CAAC,CAAC;QACvD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,sBAAsB,EAAE,CAAC,CAAC;QAC/D,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,qBAAqB,EAAE,CAAC,CAAC;QAC7D,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,wBAAwB,EAAE,CAAC,CAAC;QAEpE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,IAAI,CAAC,UAAU,CAAC,IAAI,kBAAkB,CAAC,CAAC;IACzE,CAAC;CACF,CAAA;AApEY,oEAA4B;uCAA5B,4BAA4B;IADxC,IAAA,mBAAU,GAAE;yDAMuB,sBAAa,oBAAb,sBAAa,oDACJ,0EAAkC,oBAAlC,0EAAkC,oDAC/B,gFAAqC,oBAArC,gFAAqC,oDACjD,uDAAyB,oBAAzB,uDAAyB,oDAC3B,mDAAuB,oBAAvB,mDAAuB,oDACrB,uDAAyB,oBAAzB,uDAAyB,oDACzB,uDAAyB,oBAAzB,uDAAyB,oDACvB,2DAA2B,oBAA3B,2DAA2B;GAZpD,4BAA4B,CAoExC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\services\\workflow-step-processor.factory.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { NotificationQueueManagementService } from './notification-queue-management.service';\r\nimport { NotificationTemplateManagementService } from './notification-template-management.service';\r\nimport { EmailNotificationProvider } from '../providers/email-notification.provider';\r\nimport { SmsNotificationProvider } from '../providers/sms-notification.provider';\r\nimport { SlackNotificationProvider } from '../providers/slack-notification.provider';\r\nimport { TeamsNotificationProvider } from '../providers/teams-notification.provider';\r\nimport { WebhookNotificationProvider } from '../providers/webhook-notification.provider';\r\n\r\n/**\r\n * Workflow Step Processor Interface\r\n */\r\nexport interface IWorkflowStepProcessor {\r\n  execute(stepDefinition: any, context: any): Promise<any>;\r\n}\r\n\r\n/**\r\n * Notification Step Processor\r\n */\r\nclass NotificationStepProcessor implements IWorkflowStepProcessor {\r\n  private readonly logger = new Logger(NotificationStepProcessor.name);\r\n\r\n  constructor(\r\n    private readonly queueManagementService: NotificationQueueManagementService,\r\n    private readonly templateManagementService: NotificationTemplateManagementService,\r\n    private readonly providers: Map<string, any>\r\n  ) {}\r\n\r\n  async execute(stepDefinition: any, context: any): Promise<any> {\r\n    try {\r\n      this.logger.debug(`Executing notification step: ${stepDefinition.id}`);\r\n\r\n      const config = stepDefinition.config || {};\r\n      \r\n      // Prepare notification data\r\n      const notificationData = {\r\n        id: `${context.executionId}_${stepDefinition.id}_${Date.now()}`,\r\n        alert: context.input.alert || {},\r\n        rule: context.input.rule || {},\r\n        message: await this.prepareMessage(config, context),\r\n        channel: config.channel || 'email',\r\n        recipients: await this.resolveRecipients(config.recipients, context),\r\n        metadata: {\r\n          workflowId: context.workflowId,\r\n          executionId: context.executionId,\r\n          stepId: stepDefinition.id,\r\n          ...config.metadata,\r\n        },\r\n        user: context.input.user || {},\r\n        metricValues: context.input.metricValues || {},\r\n        context: context.context || {},\r\n      };\r\n\r\n      // Queue notification for delivery\r\n      const priority = config.priority || 'medium';\r\n      await this.queueManagementService.addNotificationToQueue(notificationData, priority);\r\n\r\n      this.logger.debug(`Notification queued successfully: ${notificationData.id}`);\r\n      return {\r\n        notificationId: notificationData.id,\r\n        status: 'queued',\r\n        channel: notificationData.channel,\r\n        recipients: notificationData.recipients.length,\r\n        timestamp: new Date(),\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Notification step execution failed: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  private async prepareMessage(config: any, context: any): Promise<any> {\r\n    if (config.templateId) {\r\n      // Use template\r\n      const template = await this.templateManagementService.getTemplate(config.templateId);\r\n      return await this.templateManagementService.renderTemplate(template, context.input);\r\n    } else if (config.message) {\r\n      // Use direct message\r\n      return this.interpolateMessage(config.message, context);\r\n    } else {\r\n      throw new Error('No message template or content specified');\r\n    }\r\n  }\r\n\r\n  private async resolveRecipients(recipients: any, context: any): Promise<string[]> {\r\n    if (Array.isArray(recipients)) {\r\n      return recipients;\r\n    } else if (typeof recipients === 'string') {\r\n      // Could be a field reference or expression\r\n      if (recipients.startsWith('$')) {\r\n        const fieldValue = this.getFieldValue(recipients.substring(1), context);\r\n        return Array.isArray(fieldValue) ? fieldValue : [fieldValue];\r\n      }\r\n      return [recipients];\r\n    } else if (recipients.field) {\r\n      const fieldValue = this.getFieldValue(recipients.field, context);\r\n      return Array.isArray(fieldValue) ? fieldValue : [fieldValue];\r\n    }\r\n    return [];\r\n  }\r\n\r\n  private interpolateMessage(message: string, context: any): string {\r\n    return message.replace(/\\{\\{([^}]+)\\}\\}/g, (match, expression) => {\r\n      try {\r\n        const value = this.getFieldValue(expression.trim(), context);\r\n        return String(value || '');\r\n      } catch {\r\n        return match;\r\n      }\r\n    });\r\n  }\r\n\r\n  private getFieldValue(field: string, context: any): any {\r\n    const parts = field.split('.');\r\n    let value = context;\r\n    for (const part of parts) {\r\n      if (value === null || value === undefined) return undefined;\r\n      value = value[part];\r\n    }\r\n    return value;\r\n  }\r\n}\r\n\r\n/**\r\n * Delay Step Processor\r\n */\r\nclass DelayStepProcessor implements IWorkflowStepProcessor {\r\n  private readonly logger = new Logger(DelayStepProcessor.name);\r\n\r\n  async execute(stepDefinition: any, context: any): Promise<any> {\r\n    try {\r\n      this.logger.debug(`Executing delay step: ${stepDefinition.id}`);\r\n\r\n      const config = stepDefinition.config || {};\r\n      const delay = config.delay || 1000; // Default 1 second\r\n\r\n      await this.delay(delay);\r\n\r\n      this.logger.debug(`Delay step completed: ${stepDefinition.id}`);\r\n      return {\r\n        delayed: delay,\r\n        timestamp: new Date(),\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Delay step execution failed: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  private delay(ms: number): Promise<void> {\r\n    return new Promise(resolve => setTimeout(resolve, ms));\r\n  }\r\n}\r\n\r\n/**\r\n * Condition Step Processor\r\n */\r\nclass ConditionStepProcessor implements IWorkflowStepProcessor {\r\n  private readonly logger = new Logger(ConditionStepProcessor.name);\r\n\r\n  async execute(stepDefinition: any, context: any): Promise<any> {\r\n    try {\r\n      this.logger.debug(`Executing condition step: ${stepDefinition.id}`);\r\n\r\n      const config = stepDefinition.config || {};\r\n      const condition = config.condition;\r\n\r\n      if (!condition) {\r\n        throw new Error('No condition specified for condition step');\r\n      }\r\n\r\n      // Simple condition evaluation\r\n      const result = await this.evaluateCondition(condition, context);\r\n\r\n      this.logger.debug(`Condition step result: ${result}`);\r\n      return {\r\n        condition: condition,\r\n        result: result,\r\n        timestamp: new Date(),\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Condition step execution failed: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  private async evaluateCondition(condition: any, context: any): Promise<boolean> {\r\n    // Simple condition evaluation - in production, integrate with rule engine\r\n    if (typeof condition === 'boolean') {\r\n      return condition;\r\n    }\r\n\r\n    if (typeof condition === 'object' && condition.field && condition.operator && condition.hasOwnProperty('value')) {\r\n      const fieldValue = this.getFieldValue(condition.field, context);\r\n      const compareValue = condition.value;\r\n\r\n      switch (condition.operator) {\r\n        case 'eq': return fieldValue == compareValue;\r\n        case 'neq': return fieldValue != compareValue;\r\n        case 'gt': return fieldValue > compareValue;\r\n        case 'gte': return fieldValue >= compareValue;\r\n        case 'lt': return fieldValue < compareValue;\r\n        case 'lte': return fieldValue <= compareValue;\r\n        default: return false;\r\n      }\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  private getFieldValue(field: string, context: any): any {\r\n    const parts = field.split('.');\r\n    let value = context;\r\n    for (const part of parts) {\r\n      if (value === null || value === undefined) return undefined;\r\n      value = value[part];\r\n    }\r\n    return value;\r\n  }\r\n}\r\n\r\n/**\r\n * Variable Step Processor\r\n */\r\nclass VariableStepProcessor implements IWorkflowStepProcessor {\r\n  private readonly logger = new Logger(VariableStepProcessor.name);\r\n\r\n  async execute(stepDefinition: any, context: any): Promise<any> {\r\n    try {\r\n      this.logger.debug(`Executing variable step: ${stepDefinition.id}`);\r\n\r\n      const config = stepDefinition.config || {};\r\n      const variables = config.variables || {};\r\n\r\n      // Set variables in context\r\n      for (const [key, value] of Object.entries(variables)) {\r\n        context.variables[key] = this.resolveValue(value, context);\r\n      }\r\n\r\n      this.logger.debug(`Variable step completed: ${stepDefinition.id}`);\r\n      return {\r\n        variables: Object.keys(variables),\r\n        timestamp: new Date(),\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error(`Variable step execution failed: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  private resolveValue(value: any, context: any): any {\r\n    if (typeof value === 'string' && value.startsWith('$')) {\r\n      return this.getFieldValue(value.substring(1), context);\r\n    }\r\n    return value;\r\n  }\r\n\r\n  private getFieldValue(field: string, context: any): any {\r\n    const parts = field.split('.');\r\n    let value = context;\r\n    for (const part of parts) {\r\n      if (value === null || value === undefined) return undefined;\r\n      value = value[part];\r\n    }\r\n    return value;\r\n  }\r\n}\r\n\r\n/**\r\n * HTTP Request Step Processor\r\n */\r\nclass HttpRequestStepProcessor implements IWorkflowStepProcessor {\r\n  private readonly logger = new Logger(HttpRequestStepProcessor.name);\r\n\r\n  async execute(stepDefinition: any, context: any): Promise<any> {\r\n    try {\r\n      this.logger.debug(`Executing HTTP request step: ${stepDefinition.id}`);\r\n\r\n      const config = stepDefinition.config || {};\r\n      const url = this.interpolateString(config.url, context);\r\n      const method = config.method || 'GET';\r\n      const headers = config.headers || {};\r\n      const body = config.body ? this.interpolateValue(config.body, context) : undefined;\r\n\r\n      // Make HTTP request (simplified implementation)\r\n      const response = await this.makeHttpRequest(url, method, headers, body);\r\n\r\n      this.logger.debug(`HTTP request step completed: ${stepDefinition.id}`);\r\n      return {\r\n        url,\r\n        method,\r\n        status: response.status,\r\n        data: response.data,\r\n        timestamp: new Date(),\r\n      };\r\n\r\n    } catch (error) {\r\n      this.logger.error(`HTTP request step execution failed: ${error.message}`, error.stack);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  private async makeHttpRequest(url: string, method: string, headers: any, body: any): Promise<any> {\r\n    // Simplified HTTP request implementation\r\n    // In production, use a proper HTTP client like axios\r\n    return {\r\n      status: 200,\r\n      data: { success: true },\r\n    };\r\n  }\r\n\r\n  private interpolateString(str: string, context: any): string {\r\n    return str.replace(/\\{\\{([^}]+)\\}\\}/g, (match, expression) => {\r\n      try {\r\n        const value = this.getFieldValue(expression.trim(), context);\r\n        return String(value || '');\r\n      } catch {\r\n        return match;\r\n      }\r\n    });\r\n  }\r\n\r\n  private interpolateValue(value: any, context: any): any {\r\n    if (typeof value === 'string') {\r\n      return this.interpolateString(value, context);\r\n    } else if (typeof value === 'object' && value !== null) {\r\n      const result = {};\r\n      for (const [key, val] of Object.entries(value)) {\r\n        result[key] = this.interpolateValue(val, context);\r\n      }\r\n      return result;\r\n    }\r\n    return value;\r\n  }\r\n\r\n  private getFieldValue(field: string, context: any): any {\r\n    const parts = field.split('.');\r\n    let value = context;\r\n    for (const part of parts) {\r\n      if (value === null || value === undefined) return undefined;\r\n      value = value[part];\r\n    }\r\n    return value;\r\n  }\r\n}\r\n\r\n/**\r\n * Workflow Step Processor Factory\r\n * \r\n * Factory service for creating and managing workflow step processors including:\r\n * - Step processor registration and instantiation\r\n * - Type-based processor selection and execution\r\n * - Custom processor registration and management\r\n * - Performance optimization with processor caching\r\n * - Integration with notification infrastructure\r\n * - Extensible architecture for custom step types\r\n */\r\n@Injectable()\r\nexport class WorkflowStepProcessorFactory {\r\n  private readonly logger = new Logger(WorkflowStepProcessorFactory.name);\r\n  private readonly processors: Map<string, IWorkflowStepProcessor> = new Map();\r\n\r\n  constructor(\r\n    private readonly configService: ConfigService,\r\n    private readonly queueManagementService: NotificationQueueManagementService,\r\n    private readonly templateManagementService: NotificationTemplateManagementService,\r\n    private readonly emailProvider: EmailNotificationProvider,\r\n    private readonly smsProvider: SmsNotificationProvider,\r\n    private readonly slackProvider: SlackNotificationProvider,\r\n    private readonly teamsProvider: TeamsNotificationProvider,\r\n    private readonly webhookProvider: WebhookNotificationProvider\r\n  ) {\r\n    this.initializeProcessors();\r\n  }\r\n\r\n  /**\r\n   * Get processor for step type\r\n   */\r\n  getProcessor(stepType: string): IWorkflowStepProcessor {\r\n    const processor = this.processors.get(stepType);\r\n    if (!processor) {\r\n      throw new Error(`No processor found for step type: ${stepType}`);\r\n    }\r\n    return processor;\r\n  }\r\n\r\n  /**\r\n   * Register custom processor\r\n   */\r\n  registerProcessor(stepType: string, processor: IWorkflowStepProcessor): void {\r\n    this.processors.set(stepType, processor);\r\n    this.logger.debug(`Registered processor for step type: ${stepType}`);\r\n  }\r\n\r\n  /**\r\n   * Get available step types\r\n   */\r\n  getAvailableStepTypes(): string[] {\r\n    return Array.from(this.processors.keys());\r\n  }\r\n\r\n  /**\r\n   * Initialize built-in processors\r\n   */\r\n  private initializeProcessors(): void {\r\n    // Create provider map\r\n    const providers = new Map();\r\n    providers.set('email', this.emailProvider);\r\n    providers.set('sms', this.smsProvider);\r\n    providers.set('slack', this.slackProvider);\r\n    providers.set('teams', this.teamsProvider);\r\n    providers.set('webhook', this.webhookProvider);\r\n\r\n    // Register built-in processors\r\n    this.processors.set('notification', new NotificationStepProcessor(\r\n      this.queueManagementService,\r\n      this.templateManagementService,\r\n      providers\r\n    ));\r\n    this.processors.set('delay', new DelayStepProcessor());\r\n    this.processors.set('condition', new ConditionStepProcessor());\r\n    this.processors.set('variable', new VariableStepProcessor());\r\n    this.processors.set('http_request', new HttpRequestStepProcessor());\r\n\r\n    this.logger.log(`Initialized ${this.processors.size} step processors`);\r\n  }\r\n}\r\n"], "version": 3}