2f0786b1602665385c6c644ffa2964e5
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var AIServiceProvider_1;
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIServiceProvider = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const opossum_1 = require("opossum");
const axios_1 = __importDefault(require("axios"));
const logger_service_1 = require("../../../../infrastructure/logging/logger.service");
/**
 * AI Service Provider
 * Handles communication with external AI services and models
 */
let AIServiceProvider = AIServiceProvider_1 = class AIServiceProvider {
    constructor(configService, loggerService) {
        this.configService = configService;
        this.loggerService = loggerService;
        this.logger = new common_1.Logger(AIServiceProvider_1.name);
        this.circuitBreakers = new Map();
        this.httpClients = new Map();
    }
    /**
     * Make a prediction using the specified model
     * @param model Model configuration
     * @param input Input data for prediction
     * @returns Prediction result
     */
    async predict(model, input) {
        const startTime = Date.now();
        try {
            this.logger.debug('Making AI prediction', {
                modelId: model.id,
                modelName: model.name,
                provider: model.provider,
                inputSize: JSON.stringify(input).length,
            });
            // Get or create circuit breaker for this model
            const circuitBreaker = this.getCircuitBreaker(model);
            // Make prediction through circuit breaker
            const result = await circuitBreaker.fire(input);
            const processingTime = Date.now() - startTime;
            // Update model usage statistics
            const tokensUsed = this.estimateTokenUsage(input, result);
            const cost = model.calculateEstimatedCost(tokensUsed, tokensUsed);
            model.updateUsageStats(tokensUsed, processingTime, cost, true);
            this.logger.log('AI prediction completed successfully', {
                modelId: model.id,
                processingTime,
                tokensUsed,
                cost,
            });
            return {
                result: result.prediction,
                confidence: result.confidence,
                processingTime,
                tokensUsed,
                cost,
            };
        }
        catch (error) {
            const processingTime = Date.now() - startTime;
            model.updateUsageStats(0, processingTime, 0, false);
            this.logger.error('AI prediction failed', {
                modelId: model.id,
                error: error.message,
                processingTime,
            });
            throw error;
        }
    }
    /**
     * Make batch predictions
     * @param model Model configuration
     * @param inputs Array of input data
     * @returns Array of prediction results
     */
    async batchPredict(model, inputs) {
        if (!model.supportsBatch) {
            // Process sequentially if batch not supported
            const results = [];
            for (const input of inputs) {
                const result = await this.predict(model, input);
                results.push(result);
            }
            return results;
        }
        const startTime = Date.now();
        try {
            this.logger.debug('Making batch AI predictions', {
                modelId: model.id,
                batchSize: inputs.length,
            });
            const circuitBreaker = this.getCircuitBreaker(model);
            const batchResult = await circuitBreaker.fire({ batch: inputs });
            const totalProcessingTime = Date.now() - startTime;
            const results = batchResult.predictions.map((pred, index) => ({
                result: pred.prediction,
                confidence: pred.confidence,
                processingTime: totalProcessingTime / inputs.length,
                tokensUsed: this.estimateTokenUsage(inputs[index], pred),
                cost: model.calculateEstimatedCost(this.estimateTokenUsage(inputs[index], pred), this.estimateTokenUsage(inputs[index], pred)),
            }));
            // Update model usage statistics
            const totalTokens = results.reduce((sum, r) => sum + (r.tokensUsed || 0), 0);
            const totalCost = results.reduce((sum, r) => sum + (r.cost || 0), 0);
            model.updateUsageStats(totalTokens, totalProcessingTime, totalCost, true);
            this.logger.log('Batch AI predictions completed successfully', {
                modelId: model.id,
                batchSize: inputs.length,
                totalProcessingTime,
                totalTokens,
                totalCost,
            });
            return results;
        }
        catch (error) {
            const processingTime = Date.now() - startTime;
            model.updateUsageStats(0, processingTime, 0, false);
            this.logger.error('Batch AI predictions failed', {
                modelId: model.id,
                batchSize: inputs.length,
                error: error.message,
                processingTime,
            });
            throw error;
        }
    }
    /**
     * Stream predictions (for supported models)
     * @param model Model configuration
     * @param input Input data
     * @param onChunk Callback for each chunk
     * @returns Final result
     */
    async streamPredict(model, input, onChunk) {
        if (!model.supportsStreaming) {
            throw new Error('Model does not support streaming');
        }
        const startTime = Date.now();
        try {
            this.logger.debug('Starting streaming AI prediction', {
                modelId: model.id,
                modelName: model.name,
            });
            const httpClient = this.getHttpClient(model);
            const response = await this.makeStreamingRequest(httpClient, model, input, onChunk);
            const processingTime = Date.now() - startTime;
            const tokensUsed = this.estimateTokenUsage(input, response);
            const cost = model.calculateEstimatedCost(tokensUsed, tokensUsed);
            model.updateUsageStats(tokensUsed, processingTime, cost, true);
            return {
                result: response.prediction,
                confidence: response.confidence,
                processingTime,
                tokensUsed,
                cost,
            };
        }
        catch (error) {
            const processingTime = Date.now() - startTime;
            model.updateUsageStats(0, processingTime, 0, false);
            this.logger.error('Streaming AI prediction failed', {
                modelId: model.id,
                error: error.message,
                processingTime,
            });
            throw error;
        }
    }
    /**
     * Test model connectivity and health
     * @param model Model configuration
     * @returns Health status
     */
    async testModel(model) {
        const startTime = Date.now();
        try {
            // Simple test prediction
            const testInput = this.getTestInput(model.type);
            await this.predict(model, testInput);
            const latency = Date.now() - startTime;
            return {
                healthy: true,
                latency,
            };
        }
        catch (error) {
            const latency = Date.now() - startTime;
            return {
                healthy: false,
                latency,
                error: error.message,
            };
        }
    }
    /**
     * Get or create circuit breaker for model
     * @param model Model configuration
     * @returns Circuit breaker instance
     */
    getCircuitBreaker(model) {
        const key = `${model.provider}-${model.name}`;
        if (!this.circuitBreakers.has(key)) {
            const options = {
                timeout: 30000, // 30 seconds
                errorThresholdPercentage: 50,
                resetTimeout: 30000,
                ...model.circuitBreaker,
            };
            const breaker = new opossum_1.CircuitBreaker((input) => this.makeModelRequest(model, input), options);
            breaker.on('open', () => {
                this.logger.warn('Circuit breaker opened for model', {
                    modelId: model.id,
                    modelName: model.name,
                });
            });
            breaker.on('halfOpen', () => {
                this.logger.log('Circuit breaker half-open for model', {
                    modelId: model.id,
                    modelName: model.name,
                });
            });
            breaker.on('close', () => {
                this.logger.log('Circuit breaker closed for model', {
                    modelId: model.id,
                    modelName: model.name,
                });
            });
            this.circuitBreakers.set(key, breaker);
        }
        return this.circuitBreakers.get(key);
    }
    /**
     * Get or create HTTP client for model
     * @param model Model configuration
     * @returns Axios instance
     */
    getHttpClient(model) {
        const key = `${model.provider}-${model.name}`;
        if (!this.httpClients.has(key)) {
            const client = axios_1.default.create({
                baseURL: model.endpointUrl,
                timeout: 30000,
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'Sentinel-AI/1.0',
                },
            });
            // Add authentication
            this.addAuthentication(client, model);
            // Add request/response interceptors
            this.addInterceptors(client, model);
            this.httpClients.set(key, client);
        }
        return this.httpClients.get(key);
    }
    /**
     * Make actual model request
     * @param model Model configuration
     * @param input Input data
     * @returns Prediction result
     */
    async makeModelRequest(model, input) {
        const httpClient = this.getHttpClient(model);
        switch (model.provider) {
            case 'openai':
                return await this.makeOpenAIRequest(httpClient, model, input);
            case 'azure_openai':
                return await this.makeAzureOpenAIRequest(httpClient, model, input);
            case 'anthropic':
                return await this.makeAnthropicRequest(httpClient, model, input);
            case 'custom':
                return await this.makeCustomRequest(httpClient, model, input);
            default:
                throw new Error(`Unsupported provider: ${model.provider}`);
        }
    }
    /**
     * Make OpenAI API request
     * @param client HTTP client
     * @param model Model configuration
     * @param input Input data
     * @returns Response
     */
    async makeOpenAIRequest(client, model, input) {
        const payload = {
            model: model.version,
            messages: this.formatInputForOpenAI(input, model.type),
            temperature: model.parameters?.temperature || 0.7,
            max_tokens: model.parameters?.maxTokens || 1000,
            top_p: model.parameters?.topP || 1,
            frequency_penalty: model.parameters?.frequencyPenalty || 0,
            presence_penalty: model.parameters?.presencePenalty || 0,
        };
        const response = await client.post('/v1/chat/completions', payload);
        return this.parseOpenAIResponse(response.data, model.type);
    }
    /**
     * Make custom model request
     * @param client HTTP client
     * @param model Model configuration
     * @param input Input data
     * @returns Response
     */
    async makeCustomRequest(client, model, input) {
        const response = await client.post('/predict', {
            input,
            model_type: model.type,
            parameters: model.parameters,
        });
        return response.data;
    }
    /**
     * Add authentication to HTTP client
     * @param client HTTP client
     * @param model Model configuration
     */
    addAuthentication(client, model) {
        if (model.apiKey) {
            switch (model.provider) {
                case 'openai':
                case 'azure_openai':
                    client.defaults.headers.common['Authorization'] = `Bearer ${model.apiKey}`;
                    break;
                case 'anthropic':
                    client.defaults.headers.common['x-api-key'] = model.apiKey;
                    break;
                default:
                    client.defaults.headers.common['Authorization'] = `Bearer ${model.apiKey}`;
            }
        }
    }
    /**
     * Add request/response interceptors
     * @param client HTTP client
     * @param model Model configuration
     */
    addInterceptors(client, model) {
        // Request interceptor
        client.interceptors.request.use((config) => {
            this.logger.debug('Making AI API request', {
                modelId: model.id,
                url: config.url,
                method: config.method,
            });
            return config;
        }, (error) => {
            this.logger.error('AI API request error', {
                modelId: model.id,
                error: error.message,
            });
            return Promise.reject(error);
        });
        // Response interceptor
        client.interceptors.response.use((response) => {
            this.logger.debug('AI API response received', {
                modelId: model.id,
                status: response.status,
                responseSize: JSON.stringify(response.data).length,
            });
            return response;
        }, (error) => {
            this.logger.error('AI API response error', {
                modelId: model.id,
                status: error.response?.status,
                error: error.message,
            });
            return Promise.reject(error);
        });
    }
    /**
     * Format input for OpenAI API
     * @param input Input data
     * @param modelType Model type
     * @returns Formatted messages
     */
    formatInputForOpenAI(input, modelType) {
        const systemPrompt = this.getSystemPrompt(modelType);
        const userPrompt = this.formatUserPrompt(input, modelType);
        return [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt },
        ];
    }
    /**
     * Parse OpenAI response
     * @param response OpenAI response
     * @param modelType Model type
     * @returns Parsed result
     */
    parseOpenAIResponse(response, modelType) {
        const content = response.choices[0]?.message?.content;
        if (!content) {
            throw new Error('No content in OpenAI response');
        }
        try {
            const parsed = JSON.parse(content);
            return {
                prediction: parsed.prediction || parsed.result,
                confidence: parsed.confidence || 0.5,
                reasoning: parsed.reasoning,
                factors: parsed.factors,
            };
        }
        catch (error) {
            // Fallback for non-JSON responses
            return {
                prediction: content,
                confidence: 0.5,
                reasoning: 'Raw text response',
            };
        }
    }
    /**
     * Get system prompt for model type
     * @param modelType Model type
     * @returns System prompt
     */
    getSystemPrompt(modelType) {
        const prompts = {
            vulnerability_scanner: 'You are a cybersecurity expert specializing in vulnerability analysis. Analyze the provided vulnerability data and return a JSON response with severity assessment, confidence score, and reasoning.',
            threat_classifier: 'You are a threat intelligence analyst. Classify the provided threat data and return a JSON response with classification, confidence score, and analysis factors.',
            risk_assessor: 'You are a risk assessment specialist. Evaluate the provided data and return a JSON response with risk score, confidence level, and contributing factors.',
            nlp_processor: 'You are a natural language processing expert. Process the provided text and return a structured JSON response with extracted information and confidence scores.',
        };
        return prompts[modelType] || 'You are an AI assistant. Analyze the provided data and return a structured JSON response.';
    }
    /**
     * Format user prompt for model type
     * @param input Input data
     * @param modelType Model type
     * @returns Formatted prompt
     */
    formatUserPrompt(input, modelType) {
        return `Please analyze the following data:\n\n${JSON.stringify(input, null, 2)}\n\nProvide your analysis in JSON format.`;
    }
    /**
     * Estimate token usage
     * @param input Input data
     * @param output Output data
     * @returns Estimated token count
     */
    estimateTokenUsage(input, output) {
        const inputText = JSON.stringify(input);
        const outputText = JSON.stringify(output);
        // Rough estimation: 1 token ≈ 4 characters
        return Math.ceil((inputText.length + outputText.length) / 4);
    }
    /**
     * Get test input for model type
     * @param modelType Model type
     * @returns Test input
     */
    getTestInput(modelType) {
        const testInputs = {
            vulnerability_scanner: {
                description: 'Test vulnerability',
                cvssScore: 5.0,
                affectedSoftware: 'test-software',
            },
            threat_classifier: {
                indicator: 'test-indicator',
                type: 'ip_address',
                context: 'test',
            },
            risk_assessor: {
                asset: 'test-asset',
                vulnerabilities: ['test-vuln'],
                environment: 'test',
            },
        };
        return testInputs[modelType] || { test: true };
    }
    /**
     * Make streaming request (placeholder implementation)
     * @param client HTTP client
     * @param model Model configuration
     * @param input Input data
     * @param onChunk Chunk callback
     * @returns Response
     */
    async makeStreamingRequest(client, model, input, onChunk) {
        // This would implement actual streaming for supported providers
        // For now, fall back to regular request
        const response = await this.makeModelRequest(model, input);
        onChunk(response);
        return response;
    }
};
exports.AIServiceProvider = AIServiceProvider;
exports.AIServiceProvider = AIServiceProvider = AIServiceProvider_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object, typeof (_b = typeof logger_service_1.LoggerService !== "undefined" && logger_service_1.LoggerService) === "function" ? _b : Object])
], AIServiceProvider);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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