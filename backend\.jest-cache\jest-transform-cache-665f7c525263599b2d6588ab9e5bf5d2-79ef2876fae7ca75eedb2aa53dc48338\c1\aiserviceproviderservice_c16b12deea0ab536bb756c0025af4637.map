{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai-ml\\infrastructure\\services\\ai-service-provider.service.ts", "mappings": ";;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAC/C,qCAAyC;AACzC,kDAA4D;AAE5D,sFAAkF;AAElF;;;GAGG;AAEI,IAAM,iBAAiB,yBAAvB,MAAM,iBAAiB;IAK5B,YACmB,aAA4B,EAC5B,aAA4B;QAD5B,kBAAa,GAAb,aAAa,CAAe;QAC5B,kBAAa,GAAb,aAAa,CAAe;QAN9B,WAAM,GAAG,IAAI,eAAM,CAAC,mBAAiB,CAAC,IAAI,CAAC,CAAC;QAC5C,oBAAe,GAAG,IAAI,GAAG,EAA0B,CAAC;QACpD,gBAAW,GAAG,IAAI,GAAG,EAAyB,CAAC;IAK7D,CAAC;IAEJ;;;;;OAKG;IACH,KAAK,CAAC,OAAO,CACX,KAAyB,EACzB,KAA0B;QAQ1B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE;gBACxC,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,SAAS,EAAE,KAAK,CAAC,IAAI;gBACrB,QAAQ,EAAE,KAAK,CAAC,QAAQ;gBACxB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,MAAM;aACxC,CAAC,CAAC;YAEH,+CAA+C;YAC/C,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAErD,0CAA0C;YAC1C,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE9C,gCAAgC;YAChC,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,KAAK,CAAC,sBAAsB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;YAElE,KAAK,CAAC,gBAAgB,CAAC,UAAU,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAE/D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sCAAsC,EAAE;gBACtD,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,cAAc;gBACd,UAAU;gBACV,IAAI;aACL,CAAC,CAAC;YAEH,OAAO;gBACL,MAAM,EAAE,MAAM,CAAC,UAAU;gBACzB,UAAU,EAAE,MAAM,CAAC,UAAU;gBAC7B,cAAc;gBACd,UAAU;gBACV,IAAI;aACL,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC9C,KAAK,CAAC,gBAAgB,CAAC,CAAC,EAAE,cAAc,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;YAEpD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE;gBACxC,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,cAAc;aACf,CAAC,CAAC;YAEH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,YAAY,CAChB,KAAyB,EACzB,MAA6B;QAQ7B,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;YACzB,8CAA8C;YAC9C,MAAM,OAAO,GAAG,EAAE,CAAC;YACnB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBAC3B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBAChD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvB,CAAC;YACD,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;gBAC/C,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,SAAS,EAAE,MAAM,CAAC,MAAM;aACzB,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,WAAW,GAAG,MAAM,cAAc,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;YACjE,MAAM,mBAAmB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAEnD,MAAM,OAAO,GAAG,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;gBAC5D,MAAM,EAAE,IAAI,CAAC,UAAU;gBACvB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,cAAc,EAAE,mBAAmB,GAAG,MAAM,CAAC,MAAM;gBACnD,UAAU,EAAE,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;gBACxD,IAAI,EAAE,KAAK,CAAC,sBAAsB,CAChC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,EAC5C,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAC7C;aACF,CAAC,CAAC,CAAC;YAEJ,gCAAgC;YAChC,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC7E,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACrE,KAAK,CAAC,gBAAgB,CAAC,WAAW,EAAE,mBAAmB,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;YAE1E,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6CAA6C,EAAE;gBAC7D,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,SAAS,EAAE,MAAM,CAAC,MAAM;gBACxB,mBAAmB;gBACnB,WAAW;gBACX,SAAS;aACV,CAAC,CAAC;YAEH,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC9C,KAAK,CAAC,gBAAgB,CAAC,CAAC,EAAE,cAAc,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;YAEpD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;gBAC/C,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,SAAS,EAAE,MAAM,CAAC,MAAM;gBACxB,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,cAAc;aACf,CAAC,CAAC;YAEH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,aAAa,CACjB,KAAyB,EACzB,KAA0B,EAC1B,OAA6B;QAQ7B,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACtD,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE;gBACpD,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,SAAS,EAAE,KAAK,CAAC,IAAI;aACtB,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAC7C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YACpF,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE9C,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YAC5D,MAAM,IAAI,GAAG,KAAK,CAAC,sBAAsB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;YAElE,KAAK,CAAC,gBAAgB,CAAC,UAAU,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAE/D,OAAO;gBACL,MAAM,EAAE,QAAQ,CAAC,UAAU;gBAC3B,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,cAAc;gBACd,UAAU;gBACV,IAAI;aACL,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC9C,KAAK,CAAC,gBAAgB,CAAC,CAAC,EAAE,cAAc,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;YAEpD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;gBAClD,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,cAAc;aACf,CAAC,CAAC;YAEH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,SAAS,CAAC,KAAyB;QAKvC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,yBAAyB;YACzB,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YAErC,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAEvC,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO;aACR,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAEvC,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO;gBACP,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;;;OAIG;IACK,iBAAiB,CAAC,KAAyB;QACjD,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;QAE9C,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YACnC,MAAM,OAAO,GAAG;gBACd,OAAO,EAAE,KAAK,EAAE,aAAa;gBAC7B,wBAAwB,EAAE,EAAE;gBAC5B,YAAY,EAAE,KAAK;gBACnB,GAAG,KAAK,CAAC,cAAc;aACxB,CAAC;YAEF,MAAM,OAAO,GAAG,IAAI,wBAAc,CAChC,CAAC,KAAU,EAAE,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAK,CAAC,EACnD,OAAO,CACR,CAAC;YAEF,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;gBACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kCAAkC,EAAE;oBACnD,OAAO,EAAE,KAAK,CAAC,EAAE;oBACjB,SAAS,EAAE,KAAK,CAAC,IAAI;iBACtB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,EAAE,CAAC,UAAU,EAAE,GAAG,EAAE;gBAC1B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qCAAqC,EAAE;oBACrD,OAAO,EAAE,KAAK,CAAC,EAAE;oBACjB,SAAS,EAAE,KAAK,CAAC,IAAI;iBACtB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;gBACvB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kCAAkC,EAAE;oBAClD,OAAO,EAAE,KAAK,CAAC,EAAE;oBACjB,SAAS,EAAE,KAAK,CAAC,IAAI;iBACtB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACvC,CAAC;IAED;;;;OAIG;IACK,aAAa,CAAC,KAAyB;QAC7C,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;QAE9C,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YAC/B,MAAM,MAAM,GAAG,eAAK,CAAC,MAAM,CAAC;gBAC1B,OAAO,EAAE,KAAK,CAAC,WAAW;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;oBAClC,YAAY,EAAE,iBAAiB;iBAChC;aACF,CAAC,CAAC;YAEH,qBAAqB;YACrB,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YAEtC,oCAAoC;YACpC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YAEpC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QACpC,CAAC;QAED,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACnC,CAAC;IAED;;;;;OAKG;IACK,KAAK,CAAC,gBAAgB,CAAC,KAAyB,EAAE,KAAU;QAClE,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAE7C,QAAQ,KAAK,CAAC,QAAQ,EAAE,CAAC;YACvB,KAAK,QAAQ;gBACX,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YAChE,KAAK,cAAc;gBACjB,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YACrE,KAAK,WAAW;gBACd,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YACnE,KAAK,QAAQ;gBACX,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YAChE;gBACE,MAAM,IAAI,KAAK,CAAC,yBAAyB,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACK,KAAK,CAAC,iBAAiB,CAC7B,MAAqB,EACrB,KAAyB,EACzB,KAAU;QAEV,MAAM,OAAO,GAAG;YACd,KAAK,EAAE,KAAK,CAAC,OAAO;YACpB,QAAQ,EAAE,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC;YACtD,WAAW,EAAE,KAAK,CAAC,UAAU,EAAE,WAAW,IAAI,GAAG;YACjD,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE,SAAS,IAAI,IAAI;YAC/C,KAAK,EAAE,KAAK,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC;YAClC,iBAAiB,EAAE,KAAK,CAAC,UAAU,EAAE,gBAAgB,IAAI,CAAC;YAC1D,gBAAgB,EAAE,KAAK,CAAC,UAAU,EAAE,eAAe,IAAI,CAAC;SACzD,CAAC;QAEF,MAAM,QAAQ,GAAkB,MAAM,MAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE,OAAO,CAAC,CAAC;QACnF,OAAO,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;IAC7D,CAAC;IAED;;;;;;OAMG;IACK,KAAK,CAAC,iBAAiB,CAC7B,MAAqB,EACrB,KAAyB,EACzB,KAAU;QAEV,MAAM,QAAQ,GAAkB,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE;YAC5D,KAAK;YACL,UAAU,EAAE,KAAK,CAAC,IAAI;YACtB,UAAU,EAAE,KAAK,CAAC,UAAU;SAC7B,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC;IAED;;;;OAIG;IACK,iBAAiB,CAAC,MAAqB,EAAE,KAAyB;QACxE,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;YACjB,QAAQ,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACvB,KAAK,QAAQ,CAAC;gBACd,KAAK,cAAc;oBACjB,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAU,KAAK,CAAC,MAAM,EAAE,CAAC;oBAC3E,MAAM;gBACR,KAAK,WAAW;oBACd,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;oBAC3D,MAAM;gBACR;oBACE,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAU,KAAK,CAAC,MAAM,EAAE,CAAC;YAC/E,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;;OAIG;IACK,eAAe,CAAC,MAAqB,EAAE,KAAyB;QACtE,sBAAsB;QACtB,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAC7B,CAAC,MAAM,EAAE,EAAE;YACT,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE;gBACzC,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,GAAG,EAAE,MAAM,CAAC,GAAG;gBACf,MAAM,EAAE,MAAM,CAAC,MAAM;aACtB,CAAC,CAAC;YACH,OAAO,MAAM,CAAC;QAChB,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;YACR,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE;gBACxC,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC,CACF,CAAC;QAEF,uBAAuB;QACvB,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC9B,CAAC,QAAQ,EAAE,EAAE;YACX,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;gBAC5C,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,MAAM;aACnD,CAAC,CAAC;YACH,OAAO,QAAQ,CAAC;QAClB,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;YACR,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE;gBACzC,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,MAAM,EAAE,KAAK,CAAC,QAAQ,EAAE,MAAM;gBAC9B,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACK,oBAAoB,CAAC,KAAU,EAAE,SAAiB;QACxD,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QACrD,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QAE3D,OAAO;YACL,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE;YACzC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE;SACtC,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACK,mBAAmB,CAAC,QAAa,EAAE,SAAiB;QAC1D,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC;QACtD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACnC,OAAO;gBACL,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,MAAM;gBAC9C,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,GAAG;gBACpC,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,OAAO,EAAE,MAAM,CAAC,OAAO;aACxB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,kCAAkC;YAClC,OAAO;gBACL,UAAU,EAAE,OAAO;gBACnB,UAAU,EAAE,GAAG;gBACf,SAAS,EAAE,mBAAmB;aAC/B,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;;;OAIG;IACK,eAAe,CAAC,SAAiB;QACvC,MAAM,OAAO,GAAG;YACd,qBAAqB,EAAE,sMAAsM;YAC7N,iBAAiB,EAAE,kKAAkK;YACrL,aAAa,EAAE,0JAA0J;YACzK,aAAa,EAAE,iKAAiK;SACjL,CAAC;QAEF,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI,2FAA2F,CAAC;IAC3H,CAAC;IAED;;;;;OAKG;IACK,gBAAgB,CAAC,KAAU,EAAE,SAAiB;QACpD,OAAO,yCAAyC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,2CAA2C,CAAC;IAC5H,CAAC;IAED;;;;;OAKG;IACK,kBAAkB,CAAC,KAAU,EAAE,MAAW;QAChD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACxC,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAE1C,2CAA2C;QAC3C,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;IAC/D,CAAC;IAED;;;;OAIG;IACK,YAAY,CAAC,SAAiB;QACpC,MAAM,UAAU,GAAG;YACjB,qBAAqB,EAAE;gBACrB,WAAW,EAAE,oBAAoB;gBACjC,SAAS,EAAE,GAAG;gBACd,gBAAgB,EAAE,eAAe;aAClC;YACD,iBAAiB,EAAE;gBACjB,SAAS,EAAE,gBAAgB;gBAC3B,IAAI,EAAE,YAAY;gBAClB,OAAO,EAAE,MAAM;aAChB;YACD,aAAa,EAAE;gBACb,KAAK,EAAE,YAAY;gBACnB,eAAe,EAAE,CAAC,WAAW,CAAC;gBAC9B,WAAW,EAAE,MAAM;aACpB;SACF,CAAC;QAEF,OAAO,UAAU,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IACjD,CAAC;IAED;;;;;;;OAOG;IACK,KAAK,CAAC,oBAAoB,CAChC,MAAqB,EACrB,KAAyB,EACzB,KAAU,EACV,OAA6B;QAE7B,gEAAgE;QAChE,wCAAwC;QACxC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAC3D,OAAO,CAAC,QAAQ,CAAC,CAAC;QAClB,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF,CAAA;AArlBY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;yDAOuB,sBAAa,oBAAb,sBAAa,oDACb,8BAAa,oBAAb,8BAAa;GAPpC,iBAAiB,CAqlB7B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai-ml\\infrastructure\\services\\ai-service-provider.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { CircuitBreaker } from 'opossum';\r\nimport axios, { AxiosInstance, AxiosResponse } from 'axios';\r\nimport { ModelConfiguration } from '../../domain/entities/model-configuration.entity';\r\nimport { LoggerService } from '../../../../infrastructure/logging/logger.service';\r\n\r\n/**\r\n * AI Service Provider\r\n * Handles communication with external AI services and models\r\n */\r\n@Injectable()\r\nexport class AIServiceProvider {\r\n  private readonly logger = new Logger(AIServiceProvider.name);\r\n  private readonly circuitBreakers = new Map<string, CircuitBreaker>();\r\n  private readonly httpClients = new Map<string, AxiosInstance>();\r\n\r\n  constructor(\r\n    private readonly configService: ConfigService,\r\n    private readonly loggerService: LoggerService,\r\n  ) {}\r\n\r\n  /**\r\n   * Make a prediction using the specified model\r\n   * @param model Model configuration\r\n   * @param input Input data for prediction\r\n   * @returns Prediction result\r\n   */\r\n  async predict(\r\n    model: ModelConfiguration,\r\n    input: Record<string, any>,\r\n  ): Promise<{\r\n    result: any;\r\n    confidence: number;\r\n    processingTime: number;\r\n    tokensUsed?: number;\r\n    cost?: number;\r\n  }> {\r\n    const startTime = Date.now();\r\n\r\n    try {\r\n      this.logger.debug('Making AI prediction', {\r\n        modelId: model.id,\r\n        modelName: model.name,\r\n        provider: model.provider,\r\n        inputSize: JSON.stringify(input).length,\r\n      });\r\n\r\n      // Get or create circuit breaker for this model\r\n      const circuitBreaker = this.getCircuitBreaker(model);\r\n\r\n      // Make prediction through circuit breaker\r\n      const result = await circuitBreaker.fire(input);\r\n      const processingTime = Date.now() - startTime;\r\n\r\n      // Update model usage statistics\r\n      const tokensUsed = this.estimateTokenUsage(input, result);\r\n      const cost = model.calculateEstimatedCost(tokensUsed, tokensUsed);\r\n\r\n      model.updateUsageStats(tokensUsed, processingTime, cost, true);\r\n\r\n      this.logger.log('AI prediction completed successfully', {\r\n        modelId: model.id,\r\n        processingTime,\r\n        tokensUsed,\r\n        cost,\r\n      });\r\n\r\n      return {\r\n        result: result.prediction,\r\n        confidence: result.confidence,\r\n        processingTime,\r\n        tokensUsed,\r\n        cost,\r\n      };\r\n    } catch (error) {\r\n      const processingTime = Date.now() - startTime;\r\n      model.updateUsageStats(0, processingTime, 0, false);\r\n\r\n      this.logger.error('AI prediction failed', {\r\n        modelId: model.id,\r\n        error: error.message,\r\n        processingTime,\r\n      });\r\n\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Make batch predictions\r\n   * @param model Model configuration\r\n   * @param inputs Array of input data\r\n   * @returns Array of prediction results\r\n   */\r\n  async batchPredict(\r\n    model: ModelConfiguration,\r\n    inputs: Record<string, any>[],\r\n  ): Promise<Array<{\r\n    result: any;\r\n    confidence: number;\r\n    processingTime: number;\r\n    tokensUsed?: number;\r\n    cost?: number;\r\n  }>> {\r\n    if (!model.supportsBatch) {\r\n      // Process sequentially if batch not supported\r\n      const results = [];\r\n      for (const input of inputs) {\r\n        const result = await this.predict(model, input);\r\n        results.push(result);\r\n      }\r\n      return results;\r\n    }\r\n\r\n    const startTime = Date.now();\r\n\r\n    try {\r\n      this.logger.debug('Making batch AI predictions', {\r\n        modelId: model.id,\r\n        batchSize: inputs.length,\r\n      });\r\n\r\n      const circuitBreaker = this.getCircuitBreaker(model);\r\n      const batchResult = await circuitBreaker.fire({ batch: inputs });\r\n      const totalProcessingTime = Date.now() - startTime;\r\n\r\n      const results = batchResult.predictions.map((pred, index) => ({\r\n        result: pred.prediction,\r\n        confidence: pred.confidence,\r\n        processingTime: totalProcessingTime / inputs.length,\r\n        tokensUsed: this.estimateTokenUsage(inputs[index], pred),\r\n        cost: model.calculateEstimatedCost(\r\n          this.estimateTokenUsage(inputs[index], pred),\r\n          this.estimateTokenUsage(inputs[index], pred),\r\n        ),\r\n      }));\r\n\r\n      // Update model usage statistics\r\n      const totalTokens = results.reduce((sum, r) => sum + (r.tokensUsed || 0), 0);\r\n      const totalCost = results.reduce((sum, r) => sum + (r.cost || 0), 0);\r\n      model.updateUsageStats(totalTokens, totalProcessingTime, totalCost, true);\r\n\r\n      this.logger.log('Batch AI predictions completed successfully', {\r\n        modelId: model.id,\r\n        batchSize: inputs.length,\r\n        totalProcessingTime,\r\n        totalTokens,\r\n        totalCost,\r\n      });\r\n\r\n      return results;\r\n    } catch (error) {\r\n      const processingTime = Date.now() - startTime;\r\n      model.updateUsageStats(0, processingTime, 0, false);\r\n\r\n      this.logger.error('Batch AI predictions failed', {\r\n        modelId: model.id,\r\n        batchSize: inputs.length,\r\n        error: error.message,\r\n        processingTime,\r\n      });\r\n\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Stream predictions (for supported models)\r\n   * @param model Model configuration\r\n   * @param input Input data\r\n   * @param onChunk Callback for each chunk\r\n   * @returns Final result\r\n   */\r\n  async streamPredict(\r\n    model: ModelConfiguration,\r\n    input: Record<string, any>,\r\n    onChunk: (chunk: any) => void,\r\n  ): Promise<{\r\n    result: any;\r\n    confidence: number;\r\n    processingTime: number;\r\n    tokensUsed?: number;\r\n    cost?: number;\r\n  }> {\r\n    if (!model.supportsStreaming) {\r\n      throw new Error('Model does not support streaming');\r\n    }\r\n\r\n    const startTime = Date.now();\r\n\r\n    try {\r\n      this.logger.debug('Starting streaming AI prediction', {\r\n        modelId: model.id,\r\n        modelName: model.name,\r\n      });\r\n\r\n      const httpClient = this.getHttpClient(model);\r\n      const response = await this.makeStreamingRequest(httpClient, model, input, onChunk);\r\n      const processingTime = Date.now() - startTime;\r\n\r\n      const tokensUsed = this.estimateTokenUsage(input, response);\r\n      const cost = model.calculateEstimatedCost(tokensUsed, tokensUsed);\r\n\r\n      model.updateUsageStats(tokensUsed, processingTime, cost, true);\r\n\r\n      return {\r\n        result: response.prediction,\r\n        confidence: response.confidence,\r\n        processingTime,\r\n        tokensUsed,\r\n        cost,\r\n      };\r\n    } catch (error) {\r\n      const processingTime = Date.now() - startTime;\r\n      model.updateUsageStats(0, processingTime, 0, false);\r\n\r\n      this.logger.error('Streaming AI prediction failed', {\r\n        modelId: model.id,\r\n        error: error.message,\r\n        processingTime,\r\n      });\r\n\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Test model connectivity and health\r\n   * @param model Model configuration\r\n   * @returns Health status\r\n   */\r\n  async testModel(model: ModelConfiguration): Promise<{\r\n    healthy: boolean;\r\n    latency: number;\r\n    error?: string;\r\n  }> {\r\n    const startTime = Date.now();\r\n\r\n    try {\r\n      // Simple test prediction\r\n      const testInput = this.getTestInput(model.type);\r\n      await this.predict(model, testInput);\r\n\r\n      const latency = Date.now() - startTime;\r\n\r\n      return {\r\n        healthy: true,\r\n        latency,\r\n      };\r\n    } catch (error) {\r\n      const latency = Date.now() - startTime;\r\n\r\n      return {\r\n        healthy: false,\r\n        latency,\r\n        error: error.message,\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get or create circuit breaker for model\r\n   * @param model Model configuration\r\n   * @returns Circuit breaker instance\r\n   */\r\n  private getCircuitBreaker(model: ModelConfiguration): CircuitBreaker {\r\n    const key = `${model.provider}-${model.name}`;\r\n\r\n    if (!this.circuitBreakers.has(key)) {\r\n      const options = {\r\n        timeout: 30000, // 30 seconds\r\n        errorThresholdPercentage: 50,\r\n        resetTimeout: 30000,\r\n        ...model.circuitBreaker,\r\n      };\r\n\r\n      const breaker = new CircuitBreaker(\r\n        (input: any) => this.makeModelRequest(model, input),\r\n        options,\r\n      );\r\n\r\n      breaker.on('open', () => {\r\n        this.logger.warn('Circuit breaker opened for model', {\r\n          modelId: model.id,\r\n          modelName: model.name,\r\n        });\r\n      });\r\n\r\n      breaker.on('halfOpen', () => {\r\n        this.logger.log('Circuit breaker half-open for model', {\r\n          modelId: model.id,\r\n          modelName: model.name,\r\n        });\r\n      });\r\n\r\n      breaker.on('close', () => {\r\n        this.logger.log('Circuit breaker closed for model', {\r\n          modelId: model.id,\r\n          modelName: model.name,\r\n        });\r\n      });\r\n\r\n      this.circuitBreakers.set(key, breaker);\r\n    }\r\n\r\n    return this.circuitBreakers.get(key);\r\n  }\r\n\r\n  /**\r\n   * Get or create HTTP client for model\r\n   * @param model Model configuration\r\n   * @returns Axios instance\r\n   */\r\n  private getHttpClient(model: ModelConfiguration): AxiosInstance {\r\n    const key = `${model.provider}-${model.name}`;\r\n\r\n    if (!this.httpClients.has(key)) {\r\n      const client = axios.create({\r\n        baseURL: model.endpointUrl,\r\n        timeout: 30000,\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'User-Agent': 'Sentinel-AI/1.0',\r\n        },\r\n      });\r\n\r\n      // Add authentication\r\n      this.addAuthentication(client, model);\r\n\r\n      // Add request/response interceptors\r\n      this.addInterceptors(client, model);\r\n\r\n      this.httpClients.set(key, client);\r\n    }\r\n\r\n    return this.httpClients.get(key);\r\n  }\r\n\r\n  /**\r\n   * Make actual model request\r\n   * @param model Model configuration\r\n   * @param input Input data\r\n   * @returns Prediction result\r\n   */\r\n  private async makeModelRequest(model: ModelConfiguration, input: any): Promise<any> {\r\n    const httpClient = this.getHttpClient(model);\r\n\r\n    switch (model.provider) {\r\n      case 'openai':\r\n        return await this.makeOpenAIRequest(httpClient, model, input);\r\n      case 'azure_openai':\r\n        return await this.makeAzureOpenAIRequest(httpClient, model, input);\r\n      case 'anthropic':\r\n        return await this.makeAnthropicRequest(httpClient, model, input);\r\n      case 'custom':\r\n        return await this.makeCustomRequest(httpClient, model, input);\r\n      default:\r\n        throw new Error(`Unsupported provider: ${model.provider}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Make OpenAI API request\r\n   * @param client HTTP client\r\n   * @param model Model configuration\r\n   * @param input Input data\r\n   * @returns Response\r\n   */\r\n  private async makeOpenAIRequest(\r\n    client: AxiosInstance,\r\n    model: ModelConfiguration,\r\n    input: any,\r\n  ): Promise<any> {\r\n    const payload = {\r\n      model: model.version,\r\n      messages: this.formatInputForOpenAI(input, model.type),\r\n      temperature: model.parameters?.temperature || 0.7,\r\n      max_tokens: model.parameters?.maxTokens || 1000,\r\n      top_p: model.parameters?.topP || 1,\r\n      frequency_penalty: model.parameters?.frequencyPenalty || 0,\r\n      presence_penalty: model.parameters?.presencePenalty || 0,\r\n    };\r\n\r\n    const response: AxiosResponse = await client.post('/v1/chat/completions', payload);\r\n    return this.parseOpenAIResponse(response.data, model.type);\r\n  }\r\n\r\n  /**\r\n   * Make custom model request\r\n   * @param client HTTP client\r\n   * @param model Model configuration\r\n   * @param input Input data\r\n   * @returns Response\r\n   */\r\n  private async makeCustomRequest(\r\n    client: AxiosInstance,\r\n    model: ModelConfiguration,\r\n    input: any,\r\n  ): Promise<any> {\r\n    const response: AxiosResponse = await client.post('/predict', {\r\n      input,\r\n      model_type: model.type,\r\n      parameters: model.parameters,\r\n    });\r\n\r\n    return response.data;\r\n  }\r\n\r\n  /**\r\n   * Add authentication to HTTP client\r\n   * @param client HTTP client\r\n   * @param model Model configuration\r\n   */\r\n  private addAuthentication(client: AxiosInstance, model: ModelConfiguration): void {\r\n    if (model.apiKey) {\r\n      switch (model.provider) {\r\n        case 'openai':\r\n        case 'azure_openai':\r\n          client.defaults.headers.common['Authorization'] = `Bearer ${model.apiKey}`;\r\n          break;\r\n        case 'anthropic':\r\n          client.defaults.headers.common['x-api-key'] = model.apiKey;\r\n          break;\r\n        default:\r\n          client.defaults.headers.common['Authorization'] = `Bearer ${model.apiKey}`;\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Add request/response interceptors\r\n   * @param client HTTP client\r\n   * @param model Model configuration\r\n   */\r\n  private addInterceptors(client: AxiosInstance, model: ModelConfiguration): void {\r\n    // Request interceptor\r\n    client.interceptors.request.use(\r\n      (config) => {\r\n        this.logger.debug('Making AI API request', {\r\n          modelId: model.id,\r\n          url: config.url,\r\n          method: config.method,\r\n        });\r\n        return config;\r\n      },\r\n      (error) => {\r\n        this.logger.error('AI API request error', {\r\n          modelId: model.id,\r\n          error: error.message,\r\n        });\r\n        return Promise.reject(error);\r\n      },\r\n    );\r\n\r\n    // Response interceptor\r\n    client.interceptors.response.use(\r\n      (response) => {\r\n        this.logger.debug('AI API response received', {\r\n          modelId: model.id,\r\n          status: response.status,\r\n          responseSize: JSON.stringify(response.data).length,\r\n        });\r\n        return response;\r\n      },\r\n      (error) => {\r\n        this.logger.error('AI API response error', {\r\n          modelId: model.id,\r\n          status: error.response?.status,\r\n          error: error.message,\r\n        });\r\n        return Promise.reject(error);\r\n      },\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Format input for OpenAI API\r\n   * @param input Input data\r\n   * @param modelType Model type\r\n   * @returns Formatted messages\r\n   */\r\n  private formatInputForOpenAI(input: any, modelType: string): any[] {\r\n    const systemPrompt = this.getSystemPrompt(modelType);\r\n    const userPrompt = this.formatUserPrompt(input, modelType);\r\n\r\n    return [\r\n      { role: 'system', content: systemPrompt },\r\n      { role: 'user', content: userPrompt },\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Parse OpenAI response\r\n   * @param response OpenAI response\r\n   * @param modelType Model type\r\n   * @returns Parsed result\r\n   */\r\n  private parseOpenAIResponse(response: any, modelType: string): any {\r\n    const content = response.choices[0]?.message?.content;\r\n    if (!content) {\r\n      throw new Error('No content in OpenAI response');\r\n    }\r\n\r\n    try {\r\n      const parsed = JSON.parse(content);\r\n      return {\r\n        prediction: parsed.prediction || parsed.result,\r\n        confidence: parsed.confidence || 0.5,\r\n        reasoning: parsed.reasoning,\r\n        factors: parsed.factors,\r\n      };\r\n    } catch (error) {\r\n      // Fallback for non-JSON responses\r\n      return {\r\n        prediction: content,\r\n        confidence: 0.5,\r\n        reasoning: 'Raw text response',\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get system prompt for model type\r\n   * @param modelType Model type\r\n   * @returns System prompt\r\n   */\r\n  private getSystemPrompt(modelType: string): string {\r\n    const prompts = {\r\n      vulnerability_scanner: 'You are a cybersecurity expert specializing in vulnerability analysis. Analyze the provided vulnerability data and return a JSON response with severity assessment, confidence score, and reasoning.',\r\n      threat_classifier: 'You are a threat intelligence analyst. Classify the provided threat data and return a JSON response with classification, confidence score, and analysis factors.',\r\n      risk_assessor: 'You are a risk assessment specialist. Evaluate the provided data and return a JSON response with risk score, confidence level, and contributing factors.',\r\n      nlp_processor: 'You are a natural language processing expert. Process the provided text and return a structured JSON response with extracted information and confidence scores.',\r\n    };\r\n\r\n    return prompts[modelType] || 'You are an AI assistant. Analyze the provided data and return a structured JSON response.';\r\n  }\r\n\r\n  /**\r\n   * Format user prompt for model type\r\n   * @param input Input data\r\n   * @param modelType Model type\r\n   * @returns Formatted prompt\r\n   */\r\n  private formatUserPrompt(input: any, modelType: string): string {\r\n    return `Please analyze the following data:\\n\\n${JSON.stringify(input, null, 2)}\\n\\nProvide your analysis in JSON format.`;\r\n  }\r\n\r\n  /**\r\n   * Estimate token usage\r\n   * @param input Input data\r\n   * @param output Output data\r\n   * @returns Estimated token count\r\n   */\r\n  private estimateTokenUsage(input: any, output: any): number {\r\n    const inputText = JSON.stringify(input);\r\n    const outputText = JSON.stringify(output);\r\n    \r\n    // Rough estimation: 1 token ≈ 4 characters\r\n    return Math.ceil((inputText.length + outputText.length) / 4);\r\n  }\r\n\r\n  /**\r\n   * Get test input for model type\r\n   * @param modelType Model type\r\n   * @returns Test input\r\n   */\r\n  private getTestInput(modelType: string): Record<string, any> {\r\n    const testInputs = {\r\n      vulnerability_scanner: {\r\n        description: 'Test vulnerability',\r\n        cvssScore: 5.0,\r\n        affectedSoftware: 'test-software',\r\n      },\r\n      threat_classifier: {\r\n        indicator: 'test-indicator',\r\n        type: 'ip_address',\r\n        context: 'test',\r\n      },\r\n      risk_assessor: {\r\n        asset: 'test-asset',\r\n        vulnerabilities: ['test-vuln'],\r\n        environment: 'test',\r\n      },\r\n    };\r\n\r\n    return testInputs[modelType] || { test: true };\r\n  }\r\n\r\n  /**\r\n   * Make streaming request (placeholder implementation)\r\n   * @param client HTTP client\r\n   * @param model Model configuration\r\n   * @param input Input data\r\n   * @param onChunk Chunk callback\r\n   * @returns Response\r\n   */\r\n  private async makeStreamingRequest(\r\n    client: AxiosInstance,\r\n    model: ModelConfiguration,\r\n    input: any,\r\n    onChunk: (chunk: any) => void,\r\n  ): Promise<any> {\r\n    // This would implement actual streaming for supported providers\r\n    // For now, fall back to regular request\r\n    const response = await this.makeModelRequest(model, input);\r\n    onChunk(response);\r\n    return response;\r\n  }\r\n}\r\n"], "version": 3}