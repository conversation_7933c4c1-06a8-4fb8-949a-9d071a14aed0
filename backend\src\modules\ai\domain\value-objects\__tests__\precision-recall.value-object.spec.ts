import { PrecisionRecall } from '../precision-recall.value-object';

describe('PrecisionRecall Value Object', () => {
  describe('Construction', () => {
    it('should create a valid precision-recall pair', () => {
      const pr = new PrecisionRecall(0.85, 0.75);
      expect(pr.precision).toBe(0.85);
      expect(pr.recall).toBe(0.75);
    });

    it('should accept minimum values', () => {
      const pr = new PrecisionRecall(0.0, 0.0);
      expect(pr.precision).toBe(0.0);
      expect(pr.recall).toBe(0.0);
    });

    it('should accept maximum values', () => {
      const pr = new PrecisionRecall(1.0, 1.0);
      expect(pr.precision).toBe(1.0);
      expect(pr.recall).toBe(1.0);
    });

    it('should throw error for invalid precision', () => {
      expect(() => new PrecisionRecall(-0.1, 0.5)).toThrow('Precision must be between 0 and 1');
      expect(() => new PrecisionRecall(1.1, 0.5)).toThrow('Precision must be between 0 and 1');
    });

    it('should throw error for invalid recall', () => {
      expect(() => new PrecisionRecall(0.5, -0.1)).toThrow('Recall must be between 0 and 1');
      expect(() => new PrecisionRecall(0.5, 1.1)).toThrow('Recall must be between 0 and 1');
    });

    it('should throw error for NaN values', () => {
      expect(() => new PrecisionRecall(NaN, 0.5)).toThrow('Precision and recall cannot be NaN');
      expect(() => new PrecisionRecall(0.5, NaN)).toThrow('Precision and recall cannot be NaN');
    });

    it('should throw error for infinite values', () => {
      expect(() => new PrecisionRecall(Infinity, 0.5)).toThrow('Precision and recall must be finite');
      expect(() => new PrecisionRecall(0.5, Infinity)).toThrow('Precision and recall must be finite');
    });

    it('should throw error for non-number values', () => {
      expect(() => new PrecisionRecall('0.5' as any, 0.5)).toThrow('Precision and recall must be numbers');
      expect(() => new PrecisionRecall(0.5, '0.5' as any)).toThrow('Precision and recall must be numbers');
    });
  });

  describe('Factory Methods', () => {
    it('should create from confusion matrix', () => {
      const pr = PrecisionRecall.fromConfusionMatrix(80, 20, 10); // TP=80, FP=20, FN=10
      expect(pr.precision).toBe(0.8); // 80 / (80 + 20)
      expect(pr.recall).toBeCloseTo(0.8889, 4); // 80 / (80 + 10)
    });

    it('should handle zero denominators in confusion matrix', () => {
      const pr1 = PrecisionRecall.fromConfusionMatrix(0, 0, 10); // No positive predictions
      expect(pr1.precision).toBe(0);
      expect(pr1.recall).toBe(0);

      const pr2 = PrecisionRecall.fromConfusionMatrix(0, 20, 0); // No actual positives
      expect(pr2.precision).toBe(0);
      expect(pr2.recall).toBe(0);
    });

    it('should throw error for negative confusion matrix values', () => {
      expect(() => PrecisionRecall.fromConfusionMatrix(-1, 20, 10))
        .toThrow('Confusion matrix values cannot be negative');
    });

    it('should create from percentages', () => {
      const pr = PrecisionRecall.fromPercentages(85, 75);
      expect(pr.precision).toBe(0.85);
      expect(pr.recall).toBe(0.75);
    });

    it('should throw error for invalid percentages', () => {
      expect(() => PrecisionRecall.fromPercentages(-10, 75))
        .toThrow('Percentages must be between 0 and 100');
      expect(() => PrecisionRecall.fromPercentages(85, 110))
        .toThrow('Percentages must be between 0 and 100');
    });

    it('should create predefined precision-recall pairs', () => {
      const perfect = PrecisionRecall.perfect();
      expect(perfect.precision).toBe(1.0);
      expect(perfect.recall).toBe(1.0);

      const zero = PrecisionRecall.zero();
      expect(zero.precision).toBe(0.0);
      expect(zero.recall).toBe(0.0);
    });
  });

  describe('F-Score Calculations', () => {
    it('should calculate F1 score', () => {
      const pr = new PrecisionRecall(0.8, 0.6);
      const f1 = pr.getF1Score();
      expect(f1).toBeCloseTo(0.6857, 4); // 2 * 0.8 * 0.6 / (0.8 + 0.6)
    });

    it('should handle zero precision and recall in F1', () => {
      const pr = new PrecisionRecall(0.0, 0.0);
      expect(pr.getF1Score()).toBe(0);
    });

    it('should calculate F-beta scores', () => {
      const pr = new PrecisionRecall(0.8, 0.6);
      
      const f2 = pr.getF2Score(); // Beta = 2, favors recall
      const f05 = pr.getF05Score(); // Beta = 0.5, favors precision
      
      expect(f2).toBeCloseTo(0.6316, 4);
      expect(f05).toBeCloseTo(0.7407, 4);
    });

    it('should throw error for invalid beta', () => {
      const pr = new PrecisionRecall(0.8, 0.6);
      expect(() => pr.getFBetaScore(0)).toThrow('Beta must be greater than 0');
      expect(() => pr.getFBetaScore(-1)).toThrow('Beta must be greater than 0');
    });

    it('should calculate harmonic mean', () => {
      const pr = new PrecisionRecall(0.8, 0.6);
      expect(pr.getHarmonicMean()).toBe(pr.getF1Score());
    });

    it('should calculate arithmetic mean', () => {
      const pr = new PrecisionRecall(0.8, 0.6);
      expect(pr.getArithmeticMean()).toBe(0.7);
    });

    it('should calculate geometric mean', () => {
      const pr = new PrecisionRecall(0.8, 0.6);
      expect(pr.getGeometricMean()).toBeCloseTo(0.6928, 4); // sqrt(0.8 * 0.6)
    });
  });

  describe('Comparison and Analysis', () => {
    it('should check which metric is higher', () => {
      const pr1 = new PrecisionRecall(0.8, 0.6);
      const pr2 = new PrecisionRecall(0.6, 0.8);
      
      expect(pr1.isPrecisionHigher()).toBe(true);
      expect(pr1.isRecallHigher()).toBe(false);
      expect(pr2.isPrecisionHigher()).toBe(false);
      expect(pr2.isRecallHigher()).toBe(true);
    });

    it('should check if balanced', () => {
      const balanced = new PrecisionRecall(0.8, 0.82);
      const unbalanced = new PrecisionRecall(0.8, 0.6);
      
      expect(balanced.isBalanced(0.05)).toBe(true);
      expect(unbalanced.isBalanced(0.05)).toBe(false);
    });

    it('should calculate trade-off ratio', () => {
      const pr = new PrecisionRecall(0.8, 0.4);
      expect(pr.getTradeOffRatio()).toBe(2.0); // 0.8 / 0.4
    });

    it('should handle zero recall in trade-off ratio', () => {
      const pr = new PrecisionRecall(0.8, 0.0);
      expect(pr.getTradeOffRatio()).toBe(Infinity);
    });

    it('should handle zero precision and recall in trade-off ratio', () => {
      const pr = new PrecisionRecall(0.0, 0.0);
      expect(pr.getTradeOffRatio()).toBe(1);
    });

    it('should check threshold compliance', () => {
      const pr = new PrecisionRecall(0.8, 0.6);
      
      expect(pr.meetsBothThresholds(0.7, 0.5)).toBe(true);
      expect(pr.meetsBothThresholds(0.9, 0.5)).toBe(false);
      expect(pr.meetsEitherThreshold(0.9, 0.5)).toBe(true);
      expect(pr.meetsEitherThreshold(0.9, 0.7)).toBe(false);
    });
  });

  describe('Conversions', () => {
    it('should convert to percentages', () => {
      const pr = new PrecisionRecall(0.85, 0.75);
      
      expect(pr.getPrecisionPercentage()).toBe(85);
      expect(pr.getRecallPercentage()).toBe(75);
      expect(pr.getPrecisionPercentageString()).toBe('85.0%');
      expect(pr.getRecallPercentageString()).toBe('75.0%');
      expect(pr.getPrecisionPercentageString(2)).toBe('85.00%');
    });

    it('should convert to string', () => {
      const pr = new PrecisionRecall(0.85, 0.75);
      const str = pr.toString();
      
      expect(str).toContain('Precision: 85.0%');
      expect(str).toContain('Recall: 75.0%');
      expect(str).toContain('F1:');
    });
  });

  describe('Mathematical Operations', () => {
    it('should combine with another precision-recall pair', () => {
      const pr1 = new PrecisionRecall(0.8, 0.6);
      const pr2 = new PrecisionRecall(0.6, 0.8);
      const combined = pr1.combineWith(pr2, 0.7);
      
      expect(combined.precision).toBeCloseTo(0.74); // 0.8 * 0.7 + 0.6 * 0.3
      expect(combined.recall).toBeCloseTo(0.66); // 0.6 * 0.7 + 0.8 * 0.3
    });

    it('should throw error for invalid weight in combine', () => {
      const pr1 = new PrecisionRecall(0.8, 0.6);
      const pr2 = new PrecisionRecall(0.6, 0.8);
      
      expect(() => pr1.combineWith(pr2, -0.1)).toThrow('Weight must be between 0 and 1');
      expect(() => pr1.combineWith(pr2, 1.1)).toThrow('Weight must be between 0 and 1');
    });

    it('should calculate distances', () => {
      const pr1 = new PrecisionRecall(0.8, 0.6);
      const pr2 = new PrecisionRecall(0.6, 0.8);
      
      const euclidean = pr1.distanceFrom(pr2);
      const manhattan = pr1.manhattanDistanceFrom(pr2);
      
      expect(euclidean).toBeCloseTo(0.2828, 4); // sqrt((0.8-0.6)^2 + (0.6-0.8)^2)
      expect(manhattan).toBe(0.4); // |0.8-0.6| + |0.6-0.8|
    });

    it('should compare based on F1 score', () => {
      const pr1 = new PrecisionRecall(0.9, 0.7); // F1 ≈ 0.788
      const pr2 = new PrecisionRecall(0.7, 0.9); // F1 ≈ 0.788
      const pr3 = new PrecisionRecall(0.6, 0.6); // F1 = 0.6
      
      expect(pr1.isBetterThan(pr3)).toBe(true);
      expect(pr3.isBetterThan(pr1)).toBe(false);
    });

    it('should round precision and recall', () => {
      const pr = new PrecisionRecall(0.8567, 0.7234);
      const rounded = pr.round(2);
      
      expect(rounded.precision).toBe(0.86);
      expect(rounded.recall).toBe(0.72);
    });
  });

  describe('Static Utility Methods', () => {
    it('should calculate average', () => {
      const pairs = [
        new PrecisionRecall(0.8, 0.6),
        new PrecisionRecall(0.6, 0.8),
        new PrecisionRecall(0.9, 0.7),
      ];
      
      const average = PrecisionRecall.average(pairs);
      expect(average.precision).toBeCloseTo(0.7667, 3);
      expect(average.recall).toBeCloseTo(0.7, 3);
    });

    it('should throw error for empty array in average', () => {
      expect(() => PrecisionRecall.average([])).toThrow('Cannot calculate average of empty array');
    });

    it('should calculate weighted average', () => {
      const pairs = [
        new PrecisionRecall(0.8, 0.6),
        new PrecisionRecall(0.6, 0.8),
      ];
      const weights = [0.7, 0.3];
      
      const weightedAvg = PrecisionRecall.weightedAverage(pairs, weights);
      expect(weightedAvg.precision).toBeCloseTo(0.74);
      expect(weightedAvg.recall).toBeCloseTo(0.66);
    });

    it('should throw error for mismatched arrays in weighted average', () => {
      const pairs = [new PrecisionRecall(0.8, 0.6)];
      const weights = [0.7, 0.3];
      
      expect(() => PrecisionRecall.weightedAverage(pairs, weights))
        .toThrow('Pairs and weights arrays must have the same length');
    });

    it('should throw error for zero total weight', () => {
      const pairs = [new PrecisionRecall(0.8, 0.6), new PrecisionRecall(0.6, 0.8)];
      const weights = [0, 0];
      
      expect(() => PrecisionRecall.weightedAverage(pairs, weights))
        .toThrow('Total weight cannot be zero');
    });

    it('should find best metrics', () => {
      const pairs = [
        new PrecisionRecall(0.8, 0.6), // F1 ≈ 0.686
        new PrecisionRecall(0.6, 0.8), // F1 ≈ 0.686
        new PrecisionRecall(0.9, 0.7), // F1 ≈ 0.788
      ];
      
      const bestF1 = PrecisionRecall.bestF1(pairs);
      const bestPrecision = PrecisionRecall.bestPrecision(pairs);
      const bestRecall = PrecisionRecall.bestRecall(pairs);
      
      expect(bestF1.precision).toBe(0.9);
      expect(bestPrecision.precision).toBe(0.9);
      expect(bestRecall.recall).toBe(0.8);
    });

    it('should throw error for empty array in best methods', () => {
      expect(() => PrecisionRecall.bestF1([])).toThrow('Cannot find best F1 of empty array');
      expect(() => PrecisionRecall.bestPrecision([])).toThrow('Cannot find best precision of empty array');
      expect(() => PrecisionRecall.bestRecall([])).toThrow('Cannot find best recall of empty array');
    });
  });

  describe('JSON Serialization', () => {
    it('should serialize to JSON', () => {
      const pr = new PrecisionRecall(0.85, 0.75);
      const json = pr.toJSON();
      
      expect(json.precision).toBe(0.85);
      expect(json.recall).toBe(0.75);
      expect(json.precisionPercentage).toBe(85);
      expect(json.recallPercentage).toBe(75);
      expect(json.f1Score).toBeCloseTo(0.7969, 4);
      expect(json.isBalanced).toBe(false);
    });

    it('should deserialize from JSON', () => {
      const json = { precision: 0.85, recall: 0.75 };
      const pr = PrecisionRecall.fromJSON(json);
      
      expect(pr.precision).toBe(0.85);
      expect(pr.recall).toBe(0.75);
    });
  });

  describe('Equality', () => {
    it('should be equal to another precision-recall pair with same values', () => {
      const pr1 = new PrecisionRecall(0.85, 0.75);
      const pr2 = new PrecisionRecall(0.85, 0.75);
      
      expect(pr1.equals(pr2)).toBe(true);
    });

    it('should not be equal to precision-recall pair with different values', () => {
      const pr1 = new PrecisionRecall(0.85, 0.75);
      const pr2 = new PrecisionRecall(0.75, 0.85);
      
      expect(pr1.equals(pr2)).toBe(false);
    });
  });
});