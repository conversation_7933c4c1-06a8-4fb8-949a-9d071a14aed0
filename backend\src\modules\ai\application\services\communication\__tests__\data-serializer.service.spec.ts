import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { DataSerializerService } from '../data-serializer.service';

describe('DataSerializerService', () => {
  let service: DataSerializerService;
  let configService: jest.Mocked<ConfigService>;

  beforeEach(async () => {
    const mockConfigService = {
      get: jest.fn().mockImplementation((key: string, defaultValue?: any) => {
        const config = {
          'ai.serialization.compression.enabled': true,
          'ai.serialization.compression.level': 6,
          'ai.serialization.compression.threshold': 1024,
        };
        return config[key] || defaultValue;
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DataSerializerService,
        { provide: ConfigService, useValue: mockConfigService },
      ],
    }).compile();

    service = module.get<DataSerializerService>(DataSerializerService);
    configService = module.get(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('JSON Serialization', () => {
    describe('serializeToJson', () => {
      it('should serialize simple object to JSON', async () => {
        const data = { name: 'test', value: 123, active: true };

        const result = await service.serializeToJson(data);

        expect(result).toMatchObject({
          format: 'json',
          compressed: false,
          originalSize: expect.any(Number),
          compressedSize: expect.any(Number),
          compressionRatio: 1,
          processingTime: expect.any(Number),
          metadata: expect.objectContaining({
            encoding: 'utf8',
          }),
        });

        expect(result.data).toBeInstanceOf(Buffer);
        expect(result.originalSize).toBeGreaterThan(0);
      });

      it('should serialize large object with compression', async () => {
        // Create large object that exceeds compression threshold
        const largeData = {
          items: Array.from({ length: 100 }, (_, i) => ({
            id: i,
            name: `Item ${i}`,
            description: 'This is a long description that will help make the data large enough to trigger compression',
            metadata: { created: new Date(), updated: new Date(), tags: ['tag1', 'tag2', 'tag3'] },
          })),
        };

        const result = await service.serializeToJson(largeData);

        expect(result.compressed).toBe(true);
        expect(result.compressionType).toBe('gzip');
        expect(result.compressionRatio).toBeGreaterThan(1);
        expect(result.compressedSize).toBeLessThan(result.originalSize);
      });

      it('should serialize with custom options', async () => {
        const data = { name: 'test', value: 123 };
        const options = {
          space: 2,
          forceCompression: true,
          compressionType: 'deflate' as const,
          metadata: { source: 'test' },
        };

        const result = await service.serializeToJson(data, options);

        expect(result.compressed).toBe(true);
        expect(result.compressionType).toBe('deflate');
        expect(result.metadata).toMatchObject({
          encoding: 'utf8',
          source: 'test',
        });
      });

      it('should handle serialization errors', async () => {
        const circularData = {};
        circularData['self'] = circularData; // Create circular reference

        await expect(service.serializeToJson(circularData)).rejects.toThrow('JSON serialization failed');
      });
    });

    describe('deserializeFromJson', () => {
      it('should deserialize JSON data', async () => {
        const originalData = { name: 'test', value: 123, active: true };
        const serialized = await service.serializeToJson(originalData);

        const result = await service.deserializeFromJson(serialized);

        expect(result).toEqual(originalData);
      });

      it('should deserialize compressed JSON data', async () => {
        const originalData = { name: 'test', value: 123 };
        const serialized = await service.serializeToJson(originalData, { forceCompression: true });

        const result = await service.deserializeFromJson(serialized);

        expect(result).toEqual(originalData);
      });

      it('should deserialize from Buffer', async () => {
        const originalData = { name: 'test', value: 123 };
        const jsonString = JSON.stringify(originalData);
        const buffer = Buffer.from(jsonString, 'utf8');

        const result = await service.deserializeFromJson(buffer);

        expect(result).toEqual(originalData);
      });

      it('should handle deserialization errors', async () => {
        const invalidBuffer = Buffer.from('invalid json', 'utf8');

        await expect(service.deserializeFromJson(invalidBuffer)).rejects.toThrow('JSON deserialization failed');
      });
    });
  });

  describe('Protocol Buffers Serialization', () => {
    describe('serializeToProtobuf', () => {
      it('should serialize data to Protocol Buffers format', async () => {
        const data = { name: 'test', value: 123 };
        const schema = 'TestMessage';

        const result = await service.serializeToProtobuf(data, schema);

        expect(result).toMatchObject({
          format: 'protobuf',
          schema: 'TestMessage',
          compressed: false,
          originalSize: expect.any(Number),
          compressedSize: expect.any(Number),
          processingTime: expect.any(Number),
          metadata: expect.objectContaining({
            schema: 'TestMessage',
          }),
        });

        expect(result.data).toBeInstanceOf(Buffer);
      });

      it('should serialize with compression', async () => {
        const data = { name: 'test', value: 123 };
        const schema = 'TestMessage';
        const options = { forceCompression: true };

        const result = await service.serializeToProtobuf(data, schema, options);

        expect(result.compressed).toBe(true);
        expect(result.compressionType).toBe('gzip');
      });
    });

    describe('deserializeFromProtobuf', () => {
      it('should deserialize Protocol Buffers data', async () => {
        const originalData = { name: 'test', value: 123 };
        const schema = 'TestMessage';
        const serialized = await service.serializeToProtobuf(originalData, schema);

        const result = await service.deserializeFromProtobuf(serialized, schema);

        expect(result).toEqual(originalData);
      });

      it('should handle schema mismatch', async () => {
        const originalData = { name: 'test', value: 123 };
        const schema = 'TestMessage';
        const serialized = await service.serializeToProtobuf(originalData, schema);

        await expect(
          service.deserializeFromProtobuf(serialized, 'DifferentSchema')
        ).rejects.toThrow('Schema mismatch');
      });
    });
  });

  describe('Binary Serialization', () => {
    describe('serializeToBinary', () => {
      it('should serialize string to binary', async () => {
        const data = 'Hello, World!';
        const encoding = 'base64';

        const result = await service.serializeToBinary(data, encoding);

        expect(result).toMatchObject({
          format: 'binary',
          compressed: false,
          originalSize: expect.any(Number),
          compressedSize: expect.any(Number),
          processingTime: expect.any(Number),
          metadata: expect.objectContaining({
            encoding: 'base64',
          }),
        });

        expect(result.data).toBeInstanceOf(Buffer);
      });

      it('should serialize Buffer to binary', async () => {
        const data = Buffer.from('Hello, World!', 'utf8');
        const encoding = 'hex';

        const result = await service.serializeToBinary(data, encoding);

        expect(result.format).toBe('binary');
        expect(result.metadata.encoding).toBe('hex');
      });

      it('should serialize object to binary', async () => {
        const data = { name: 'test', value: 123 };
        const encoding = 'base64';

        const result = await service.serializeToBinary(data, encoding);

        expect(result.format).toBe('binary');
        expect(result.originalSize).toBeGreaterThan(0);
      });
    });

    describe('deserializeFromBinary', () => {
      it('should deserialize binary data', async () => {
        const originalData = 'Hello, World!';
        const encoding = 'base64';
        const serialized = await service.serializeToBinary(originalData, encoding);

        const result = await service.deserializeFromBinary(serialized, encoding);

        expect(result).toBeInstanceOf(Buffer);
        expect(result.toString('utf8')).toBe(originalData);
      });

      it('should deserialize from Buffer', async () => {
        const originalBuffer = Buffer.from('Hello, World!', 'utf8');

        const result = await service.deserializeFromBinary(originalBuffer, 'utf8');

        expect(result).toEqual(originalBuffer);
      });
    });
  });

  describe('Compression', () => {
    describe('compressData', () => {
      it('should compress data using gzip', async () => {
        // Use larger data that will actually compress well
        const data = Buffer.from('Hello, World! '.repeat(100), 'utf8');

        const compressed = await service.compressData(data, 'gzip');

        expect(compressed).toBeInstanceOf(Buffer);
        expect(compressed.length).toBeLessThan(data.length);
      });

      it('should compress data using deflate', async () => {
        // Use larger data that will actually compress well
        const data = Buffer.from('Hello, World! '.repeat(100), 'utf8');

        const compressed = await service.compressData(data, 'deflate');

        expect(compressed).toBeInstanceOf(Buffer);
        expect(compressed.length).toBeLessThan(data.length);
      });

      it('should handle unsupported compression type', async () => {
        const data = Buffer.from('Hello, World!', 'utf8');

        await expect(
          service.compressData(data, 'unsupported' as any)
        ).rejects.toThrow('Unsupported compression type');
      });
    });

    describe('decompressData', () => {
      it('should decompress gzip data', async () => {
        const originalData = Buffer.from('Hello, World! This is a test string for compression.', 'utf8');
        const compressed = await service.compressData(originalData, 'gzip');

        const decompressed = await service.decompressData(compressed, 'gzip');

        expect(decompressed).toEqual(originalData);
      });

      it('should decompress deflate data', async () => {
        const originalData = Buffer.from('Hello, World! This is a test string for compression.', 'utf8');
        const compressed = await service.compressData(originalData, 'deflate');

        const decompressed = await service.decompressData(compressed, 'deflate');

        expect(decompressed).toEqual(originalData);
      });

      it('should handle unsupported decompression type', async () => {
        const data = Buffer.from('Hello, World!', 'utf8');

        await expect(
          service.decompressData(data, 'unsupported' as any)
        ).rejects.toThrow('Unsupported compression type');
      });
    });
  });

  describe('Batch Operations', () => {
    describe('batchSerialize', () => {
      it('should batch serialize multiple items', async () => {
        const items = [
          { data: { name: 'item1', value: 1 }, format: 'json' as const },
          { data: { name: 'item2', value: 2 }, format: 'json' as const },
          { data: 'binary data', format: 'binary' as const, options: { encoding: 'base64' as const } },
        ];

        const result = await service.batchSerialize(items);

        expect(result.results).toHaveLength(3);
        expect(result.errors).toHaveLength(0);
        expect(result.summary).toMatchObject({
          total: 3,
          successful: 3,
          failed: 0,
          totalOriginalSize: expect.any(Number),
          totalCompressedSize: expect.any(Number),
          overallCompressionRatio: expect.any(Number),
          processingTime: expect.any(Number),
        });

        result.results.forEach(item => {
          expect(item).toMatchObject({
            data: expect.any(Buffer),
            format: expect.any(String),
            originalSize: expect.any(Number),
            compressedSize: expect.any(Number),
            processingTime: expect.any(Number),
          });
        });
      });

      it('should handle batch serialization errors', async () => {
        const items = [
          { data: { name: 'valid' }, format: 'json' as const },
          { data: {}, format: 'unsupported' as any }, // Invalid format
          { data: { name: 'valid2' }, format: 'json' as const },
        ];

        const result = await service.batchSerialize(items);

        expect(result.results).toHaveLength(3);
        expect(result.errors).toHaveLength(1);
        expect(result.summary).toMatchObject({
          total: 3,
          successful: 2,
          failed: 1,
        });

        expect(result.errors[0]).toMatchObject({
          index: 1,
          error: expect.any(Error),
        });
      });
    });

    describe('batchDeserialize', () => {
      it('should batch deserialize multiple items', async () => {
        // First serialize some data
        const originalItems = [
          { data: { name: 'item1', value: 1 }, format: 'json' as const },
          { data: { name: 'item2', value: 2 }, format: 'json' as const },
        ];

        const serialized = await service.batchSerialize(originalItems);

        // Then deserialize
        const deserializeItems = serialized.results.map((item, index) => ({
          data: item,
          format: originalItems[index].format,
        }));

        const result = await service.batchDeserialize(deserializeItems);

        expect(result.results).toHaveLength(2);
        expect(result.errors).toHaveLength(0);
        expect(result.summary).toMatchObject({
          total: 2,
          successful: 2,
          failed: 0,
          processingTime: expect.any(Number),
        });

        expect(result.results[0]).toEqual({ name: 'item1', value: 1 });
        expect(result.results[1]).toEqual({ name: 'item2', value: 2 });
      });

      it('should handle batch deserialization errors', async () => {
        const items = [
          { data: Buffer.from('{"valid": true}', 'utf8'), format: 'json' as const },
          { data: Buffer.from('invalid json', 'utf8'), format: 'json' as const },
          { data: Buffer.from('{"valid2": true}', 'utf8'), format: 'json' as const },
        ];

        const result = await service.batchDeserialize(items);

        expect(result.results).toHaveLength(3);
        expect(result.errors).toHaveLength(1);
        expect(result.summary).toMatchObject({
          total: 3,
          successful: 2,
          failed: 1,
        });

        expect(result.errors[0]).toMatchObject({
          index: 1,
          error: expect.any(Error),
        });
      });
    });
  });

  describe('Configuration and Statistics', () => {
    describe('getSerializationStats', () => {
      it('should return serialization statistics', () => {
        const stats = service.getSerializationStats();

        expect(stats).toMatchObject({
          compressionEnabled: true,
          compressionLevel: 6,
          compressionThreshold: 1024,
          supportedFormats: ['json', 'protobuf', 'binary'],
          supportedCompressionTypes: ['gzip', 'deflate'],
          supportedEncodings: ['base64', 'hex', 'utf8'],
        });
      });
    });

    it('should use configured compression settings', async () => {
      const mockConfigService = {
        get: jest.fn().mockImplementation((key: string, defaultValue?: any) => {
          const config = {
            'ai.serialization.compression.enabled': false,
            'ai.serialization.compression.level': 9,
            'ai.serialization.compression.threshold': 2048,
          };
          return config[key] || defaultValue;
        }),
      };

      // Create new instance to pick up new config
      const newService = new DataSerializerService(mockConfigService as any);
      const stats = newService.getSerializationStats();

      expect(stats).toMatchObject({
        compressionEnabled: false,
        compressionLevel: 9,
        compressionThreshold: 2048,
      });
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle null data', async () => {
      const nullResult = await service.serializeToJson(null);

      expect(nullResult.data).toBeInstanceOf(Buffer);

      const deserializedNull = await service.deserializeFromJson(nullResult);

      expect(deserializedNull).toBeNull();
    });

    it('should handle undefined data by treating it as null', async () => {
      // JSON.stringify(undefined) returns undefined, so we expect this to be handled gracefully
      // In practice, undefined should be converted to null for JSON serialization
      const data = { value: undefined, name: 'test' };
      const result = await service.serializeToJson(data);

      expect(result.data).toBeInstanceOf(Buffer);

      const deserialized = await service.deserializeFromJson(result);
      expect(deserialized).toEqual({ name: 'test' }); // undefined values are omitted in JSON
    });

    it('should handle empty data', async () => {
      const emptyObject = {};
      const emptyArray = [];
      const emptyString = '';

      const objectResult = await service.serializeToJson(emptyObject);
      const arrayResult = await service.serializeToJson(emptyArray);
      const stringResult = await service.serializeToBinary(emptyString);

      expect(objectResult.data).toBeInstanceOf(Buffer);
      expect(arrayResult.data).toBeInstanceOf(Buffer);
      expect(stringResult.data).toBeInstanceOf(Buffer);

      const deserializedObject = await service.deserializeFromJson(objectResult);
      const deserializedArray = await service.deserializeFromJson(arrayResult);

      expect(deserializedObject).toEqual({});
      expect(deserializedArray).toEqual([]);
    });

    it('should handle very large data', async () => {
      // Create a large object
      const largeData = {
        items: Array.from({ length: 10000 }, (_, i) => ({
          id: i,
          data: `Item ${i} with some additional data to make it larger`,
        })),
      };

      const result = await service.serializeToJson(largeData);

      expect(result.compressed).toBe(true);
      expect(result.compressionRatio).toBeGreaterThan(1);

      const deserialized = await service.deserializeFromJson(result);
      expect(deserialized.items).toHaveLength(10000);
      expect(deserialized.items[0]).toEqual(largeData.items[0]);
    });
  });
});