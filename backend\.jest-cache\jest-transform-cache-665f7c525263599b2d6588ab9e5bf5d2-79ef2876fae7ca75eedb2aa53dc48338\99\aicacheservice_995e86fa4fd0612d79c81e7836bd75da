f3635ca8d28d4a334a51d2604efccc77
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AiCacheService_1;
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AiCacheService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
/**
 * AI Cache Service
 *
 * Provides caching capabilities for AI operations including
 * analysis results, model metadata, and request responses.
 * Implements intelligent cache strategies and performance optimization.
 */
let AiCacheService = AiCacheService_1 = class AiCacheService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(AiCacheService_1.name);
        this.cache = new Map();
        this.defaultTtl = this.configService.get('ai.cache.defaultTtl', 3600000); // 1 hour
    }
    /**
     * Gets a value from cache
     */
    async get(key) {
        const entry = this.cache.get(key);
        if (!entry) {
            return null;
        }
        // Check if entry has expired
        if (this.isExpired(entry)) {
            this.cache.delete(key);
            return null;
        }
        // Update access time for LRU
        entry.lastAccessed = new Date();
        entry.accessCount++;
        this.logger.debug(`Cache hit for key: ${key}`);
        return entry.value;
    }
    /**
     * Sets a value in cache
     */
    async set(key, value, ttl) {
        const expiresAt = new Date(Date.now() + (ttl || this.defaultTtl));
        const entry = {
            key,
            value,
            createdAt: new Date(),
            lastAccessed: new Date(),
            expiresAt,
            accessCount: 0,
            size: this.calculateSize(value),
        };
        this.cache.set(key, entry);
        this.logger.debug(`Cache set for key: ${key}, TTL: ${ttl || this.defaultTtl}ms`);
        // Cleanup expired entries periodically
        if (this.cache.size % 100 === 0) {
            await this.cleanup();
        }
    }
    /**
     * Deletes a value from cache
     */
    async delete(key) {
        const deleted = this.cache.delete(key);
        if (deleted) {
            this.logger.debug(`Cache deleted for key: ${key}`);
        }
        return deleted;
    }
    /**
     * Checks if a key exists in cache
     */
    async has(key) {
        const entry = this.cache.get(key);
        if (!entry) {
            return false;
        }
        if (this.isExpired(entry)) {
            this.cache.delete(key);
            return false;
        }
        return true;
    }
    /**
     * Clears all cache entries
     */
    async clear() {
        this.cache.clear();
        this.logger.log('Cache cleared');
    }
    /**
     * Gets cache statistics
     */
    async getStats() {
        const entries = Array.from(this.cache.values());
        const now = new Date();
        const stats = {
            totalEntries: entries.length,
            expiredEntries: entries.filter(entry => this.isExpired(entry)).length,
            totalSize: entries.reduce((sum, entry) => sum + entry.size, 0),
            averageAccessCount: entries.length > 0
                ? entries.reduce((sum, entry) => sum + entry.accessCount, 0) / entries.length
                : 0,
            oldestEntry: entries.length > 0
                ? Math.min(...entries.map(entry => entry.createdAt.getTime()))
                : null,
            newestEntry: entries.length > 0
                ? Math.max(...entries.map(entry => entry.createdAt.getTime()))
                : null,
        };
        return stats;
    }
    /**
     * Performs cache health check
     */
    async checkHealth() {
        try {
            const testKey = 'health-check-' + Date.now();
            const testValue = { test: true };
            // Test write
            await this.set(testKey, testValue, 1000);
            // Test read
            const retrieved = await this.get(testKey);
            // Test delete
            await this.delete(testKey);
            const stats = await this.getStats();
            return {
                status: 'healthy',
                responseTime: 0, // In-memory cache is instant
                stats,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error('Cache health check failed', error);
            return {
                status: 'unhealthy',
                error: error.message,
                timestamp: new Date(),
            };
        }
    }
    /**
     * Invalidates cache entries matching a pattern
     */
    async invalidatePattern(pattern) {
        const regex = new RegExp(pattern);
        let deletedCount = 0;
        for (const [key] of this.cache) {
            if (regex.test(key)) {
                this.cache.delete(key);
                deletedCount++;
            }
        }
        this.logger.debug(`Invalidated ${deletedCount} cache entries matching pattern: ${pattern}`);
        return deletedCount;
    }
    /**
     * Gets or sets a value with a factory function
     */
    async getOrSet(key, factory, ttl) {
        const cached = await this.get(key);
        if (cached !== null) {
            return cached;
        }
        const value = await factory();
        await this.set(key, value, ttl);
        return value;
    }
    /**
     * Refreshes cache entry with new TTL
     */
    async refresh(key, ttl) {
        const entry = this.cache.get(key);
        if (!entry) {
            return false;
        }
        entry.expiresAt = new Date(Date.now() + (ttl || this.defaultTtl));
        this.logger.debug(`Cache refreshed for key: ${key}`);
        return true;
    }
    // Private helper methods
    isExpired(entry) {
        return new Date() > entry.expiresAt;
    }
    calculateSize(value) {
        // Simple size calculation - can be improved
        try {
            return JSON.stringify(value).length;
        }
        catch {
            return 0;
        }
    }
    async cleanup() {
        const expiredKeys = [];
        for (const [key, entry] of this.cache) {
            if (this.isExpired(entry)) {
                expiredKeys.push(key);
            }
        }
        for (const key of expiredKeys) {
            this.cache.delete(key);
        }
        if (expiredKeys.length > 0) {
            this.logger.debug(`Cleaned up ${expiredKeys.length} expired cache entries`);
        }
    }
};
exports.AiCacheService = AiCacheService;
exports.AiCacheService = AiCacheService = AiCacheService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], AiCacheService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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