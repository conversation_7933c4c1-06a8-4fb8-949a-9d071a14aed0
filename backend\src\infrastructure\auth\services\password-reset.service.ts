import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { PasswordService } from './password.service';
import { User } from '../../../modules/user-management/domain/entities/user.entity';
import { AuditService } from '../../logging/audit/audit.service';
import * as crypto from 'crypto';

/**
 * Password reset service that handles password reset functionality
 * Provides secure password reset with token-based verification
 */
@Injectable()
export class PasswordResetService {
  private readonly logger = new Logger(PasswordResetService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly passwordService: PasswordService,
    private readonly configService: ConfigService,
    private readonly auditService: AuditService,
  ) {}

  /**
   * Initiate password reset process
   * @param email User email address
   * @param ipAddress Client IP address
   * @param userAgent Client user agent
   * @returns Success message (always returns success for security)
   */
  async initiatePasswordReset(
    email: string,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<{ message: string; resetToken?: string }> {
    try {
      this.logger.debug('Initiating password reset', {
        email,
        ipAddress,
      });

      // Always return success message for security (don't reveal if email exists)
      const successMessage = 'If an account with that email exists, a password reset link has been sent.';

      // Find user by email
      const user = await this.userRepository.findOne({
        where: { email: email.toLowerCase().trim() },
      });

      if (!user) {
        this.logger.warn('Password reset requested for non-existent email', {
          email,
          ipAddress,
        });

        // Log suspicious activity
        await this.auditService.logSystemEvent(
          'password_reset_invalid_email',
          'authentication',
          null,
          { email, ipAddress, userAgent },
        );

        return { message: successMessage };
      }

      // Check if user account is active
      if (user.status !== 'active') {
        this.logger.warn('Password reset requested for inactive account', {
          userId: user.id,
          status: user.status,
          ipAddress,
        });

        await this.auditService.logUserAction(
          user.id,
          'password_reset_inactive_account',
          'authentication',
          null,
          { status: user.status },
          ipAddress,
          userAgent,
        );

        return { message: successMessage };
      }

      // Check rate limiting
      if (await this.isRateLimited(user)) {
        this.logger.warn('Password reset rate limited', {
          userId: user.id,
          ipAddress,
        });

        await this.auditService.logUserAction(
          user.id,
          'password_reset_rate_limited',
          'authentication',
          null,
          {},
          ipAddress,
          userAgent,
        );

        return { message: successMessage };
      }

      // Generate reset token
      const resetToken = this.generateResetToken();
      const tokenExpiry = this.getTokenExpiry();

      // Save reset token to user
      user.passwordResetToken = await this.hashToken(resetToken);
      user.passwordResetExpires = tokenExpiry;

      await this.userRepository.save(user);

      // Log password reset initiation
      await this.auditService.logUserAction(
        user.id,
        'password_reset_initiated',
        'authentication',
        null,
        { tokenExpiry },
        ipAddress,
        userAgent,
      );

      this.logger.log('Password reset initiated successfully', {
        userId: user.id,
        email: user.email,
        ipAddress,
      });

      // In development, return the token for testing
      const isDevelopment = this.configService.get('NODE_ENV') === 'development';
      
      return {
        message: successMessage,
        ...(isDevelopment && { resetToken }),
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error('Error initiating password reset', {
        email,
        error: errorMessage,
        ipAddress,
      });

      // Return success message even on error for security
      return { message: 'If an account with that email exists, a password reset link has been sent.' };
    }
  }

  /**
   * Verify password reset token
   * @param token Reset token
   * @returns Token verification result
   */
  async verifyResetToken(token: string): Promise<{ valid: boolean; userId?: string }> {
    try {
      this.logger.debug('Verifying password reset token');

      const hashedToken = await this.hashToken(token);

      const user = await this.userRepository.findOne({
        where: {
          passwordResetToken: hashedToken,
        },
      });

      if (!user) {
        this.logger.warn('Invalid password reset token');
        return { valid: false };
      }

      // Check if token is expired
      if (!user.passwordResetExpires || new Date() > user.passwordResetExpires) {
        this.logger.warn('Expired password reset token', {
          userId: user.id,
          expiry: user.passwordResetExpires,
        });

        // Clear expired token
        await this.clearResetToken(user);

        return { valid: false };
      }

      this.logger.debug('Password reset token verified successfully', {
        userId: user.id,
      });

      return { valid: true, userId: user.id };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error('Error verifying password reset token', {
        error: errorMessage,
      });
      return { valid: false };
    }
  }

  /**
   * Reset password using token
   * @param token Reset token
   * @param newPassword New password
   * @param ipAddress Client IP address
   * @param userAgent Client user agent
   * @returns Success result
   */
  async resetPassword(
    token: string,
    newPassword: string,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<{ success: boolean; message: string }> {
    try {
      this.logger.debug('Resetting password with token');

      // Verify token
      const tokenVerification = await this.verifyResetToken(token);
      if (!tokenVerification.valid || !tokenVerification.userId) {
        throw new BadRequestException('Invalid or expired reset token');
      }

      const user = await this.userRepository.findOne({
        where: { id: tokenVerification.userId },
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Validate new password
      const passwordValidation = this.passwordService.validatePasswordStrength(newPassword);
      if (!passwordValidation.isValid) {
        throw new BadRequestException({
          message: 'Password does not meet security requirements',
          errors: passwordValidation.errors,
        });
      }

      // Hash new password
      const newPasswordHash = await this.passwordService.hashPassword(newPassword);

      // Update user password and clear reset token
      user.passwordHash = newPasswordHash;
      user.passwordChangedAt = new Date();
      user.passwordResetToken = null;
      user.passwordResetExpires = null;
      user.failedLoginAttempts = 0; // Reset failed attempts
      user.lockedUntil = null; // Unlock account if locked

      await this.userRepository.save(user);

      // Log password reset completion
      await this.auditService.logUserAction(
        user.id,
        'password_reset_completed',
        'authentication',
        null,
        {},
        ipAddress,
        userAgent,
      );

      this.logger.log('Password reset completed successfully', {
        userId: user.id,
        ipAddress,
      });

      return {
        success: true,
        message: 'Password has been reset successfully',
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error('Error resetting password', {
        error: errorMessage,
        ipAddress,
      });

      if (error instanceof BadRequestException || error instanceof NotFoundException) {
        throw error;
      }

      throw new BadRequestException('Failed to reset password');
    }
  }

  /**
   * Clear password reset token for a user
   * @param user User entity
   */
  private async clearResetToken(user: User): Promise<void> {
    user.passwordResetToken = null;
    user.passwordResetExpires = null;
    await this.userRepository.save(user);
  }

  /**
   * Generate a secure reset token
   * @returns Reset token string
   */
  private generateResetToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Hash a token for storage
   * @param token Token to hash
   * @returns Hashed token
   */
  private async hashToken(token: string): Promise<string> {
    return await this.passwordService.hashPassword(token);
  }

  /**
   * Get token expiry date
   * @returns Expiry date
   */
  private getTokenExpiry(): Date {
    const authConfig = this.configService.get('auth');
    const expiryMinutes = authConfig.passwordReset.tokenExpiryMinutes || 60;
    
    const expiry = new Date();
    expiry.setMinutes(expiry.getMinutes() + expiryMinutes);
    
    return expiry;
  }

  /**
   * Check if user is rate limited for password reset
   * @param user User entity
   * @returns Whether user is rate limited
   */
  private async isRateLimited(user: User): Promise<boolean> {
    const authConfig = this.configService.get('auth');
    const rateLimitMinutes = authConfig.passwordReset.rateLimitMinutes || 15;

    if (!user.passwordResetExpires) {
      return false;
    }

    const timeSinceLastRequest = new Date().getTime() - user.passwordResetExpires.getTime();
    const rateLimitMs = rateLimitMinutes * 60 * 1000;

    return timeSinceLastRequest < rateLimitMs;
  }
}
