5c1c232444b4aa53b21eaa066252f461
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalysisResultCreatedEvent = void 0;
const base_domain_event_1 = require("../../../../shared-kernel/domain/base-domain-event");
/**
 * Analysis Result Created Domain Event
 *
 * Published when a new analysis result is created in the system.
 * This event can trigger various downstream processes such as
 * result indexing, notification sending, and workflow continuation.
 */
class AnalysisResultCreatedEvent extends base_domain_event_1.BaseDomainEvent {
    constructor(analysisResultId, requestId, modelId, analysisType, eventId, occurredOn) {
        super(eventId, occurredOn);
        this.analysisResultId = analysisResultId;
        this.requestId = requestId;
        this.modelId = modelId;
        this.analysisType = analysisType;
    }
    getEventName() {
        return 'AnalysisResultCreated';
    }
    getEventVersion() {
        return '1.0';
    }
    getEventData() {
        return {
            analysisResultId: this.analysisResultId.toString(),
            requestId: this.requestId,
            modelId: this.modelId.toString(),
            analysisType: this.analysisType,
        };
    }
}
exports.AnalysisResultCreatedEvent = AnalysisResultCreatedEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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