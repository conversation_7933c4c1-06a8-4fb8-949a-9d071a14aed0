{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\entities\\notification-workflow.entity.ts", "mappings": ";;;;;;;;;;;;;AAAA,qCAQiB;AACjB,2EAAgE;AAChE,yEAA8D;AAE9D;;;;;GAKG;AAQI,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IAgJ/B,sBAAsB;IACtB,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,MAAM,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK,CAAC;IACtF,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,KAAK,CAAC;IACvF,CAAC;IAED,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,KAAK,CAAC;IACzF,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,CAAC;IAClC,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,MAAM,KAAK,OAAO,CAAC;IACjC,CAAC;IAED,IAAI,UAAU;QACZ,IAAI,IAAI,CAAC,QAAQ,EAAE,UAAU,EAAE,CAAC;YAC9B,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;QAClC,CAAC;QAED,8DAA8D;QAC9D,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,MAAM,mBAAmB,GAAG,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAC9D,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAC5C,IAAI,KAAK,CAAC;QACX,MAAM,gBAAgB,GAAG,IAAI,CAAC,UAAU,EAAE,aAAa;YACrD,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC;QAEpE,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAChE,OAAO,KAAK,CAAC;QACf,CAAC;aAAM,IAAI,SAAS,IAAI,EAAE,IAAI,CAAC,CAAC,mBAAmB,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC1E,OAAO,QAAQ,CAAC;QAClB,CAAC;aAAM,CAAC;YACN,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAChD,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,aAAa,EAAE,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC;SACnE,CAAC,CAAC,IAAI,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACH,YAAY;QACV,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QACvE,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,QAAgB;QAC3B,OAAO,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,KAAK,CAAC;IAC/E,CAAC;IAED;;OAEG;IACH,uBAAuB;QACrB,MAAM,QAAQ,GAAG,EAAE,CAAC;QAEpB,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;YACrC,IAAI,IAAI,CAAC,IAAI,KAAK,cAAc,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC;gBACzD,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACrC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ;YACxB,IAAI,CAAC,UAAU,EAAE,SAAS;YAC1B,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,MAAM,GAAG,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,IAAI,IAAI,CAAC,QAAQ,EAAE,iBAAiB,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC;QACzC,CAAC;QAED,kDAAkD;QAClD,IAAI,QAAQ,GAAG,CAAC,CAAC;QAEjB,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;YACrC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;gBAClB,KAAK,cAAc;oBACjB,QAAQ,IAAI,KAAK,CAAC,CAAC,aAAa;oBAChC,MAAM;gBACR,KAAK,OAAO;oBACV,QAAQ,IAAI,IAAI,CAAC,MAAM,EAAE,KAAK,IAAI,KAAK,CAAC,CAAC,mBAAmB;oBAC5D,MAAM;gBACR,KAAK,WAAW;oBACd,QAAQ,IAAI,IAAI,CAAC,CAAC,YAAY;oBAC9B,MAAM;gBACR,KAAK,cAAc;oBACjB,QAAQ,IAAI,KAAK,CAAC,CAAC,aAAa;oBAChC,MAAM;gBACR;oBACE,QAAQ,IAAI,KAAK,CAAC,CAAC,qBAAqB;YAC5C,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAC/C,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;QAClC,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;YAC/B,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACxC,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjE,MAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAC/C,CAAC;QAED,6BAA6B;QAC7B,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YACvD,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YAClG,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,MAAM,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,UAAU,CAAC,SAAS,sBAAsB,CAAC,CAAC;YAC9E,CAAC;QACH,CAAC;QAED,2BAA2B;QAC3B,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAE3D,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACnC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACtD,MAAM,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,EAAE,wCAAwC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;gBACxF,CAAC;gBAED,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;oBACnB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;wBAChC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;4BACvC,MAAM,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,EAAE,wCAAwC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;wBAC1F,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC;IAChD,CAAC;CACF,CAAA;AA1VY,oDAAoB;AAE/B;IADC,IAAA,gCAAsB,EAAC,MAAM,CAAC;;gDACpB;AAIX;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IACxB,IAAA,eAAK,GAAE;;kDACK;AAGb;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yDAC1C;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;;wDA0C5C;AAIF;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;IAC5C,IAAA,eAAK,GAAE;;oDAC6C;AAIrD;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC5C,IAAA,eAAK,GAAE;;sDAC+E;AAGvF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;kDACpD;AAGf;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;;qDAC9B;AAIhB;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;IAC/C,IAAA,eAAK,GAAE;;wDACY;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;2DA0B/D;AAGF;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;sDAmB1D;AAIF;IAFC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;IAC9B,IAAA,eAAK,GAAE;;uDACU;AAGlB;IADC,IAAA,gBAAM,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;;uDACb;AAIlB;IAFC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;IACxC,IAAA,eAAK,GAAE;kDACG,IAAI,oBAAJ,IAAI;uDAAC;AAIhB;IAFC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;IACxC,IAAA,eAAK,GAAE;kDACG,IAAI,oBAAJ,IAAI;uDAAC;AAIhB;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,6CAAiB,EAAE,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC;;wDACpC;AAGhC;IADC,IAAA,mBAAS,EAAC,GAAG,EAAE,CAAC,2CAAgB,EAAE,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;;uDACnC;+BA9InB,oBAAoB;IAPhC,IAAA,gBAAM,EAAC,wBAAwB,CAAC;IAChC,IAAA,eAAK,EAAC,CAAC,QAAQ,CAAC,CAAC;IACjB,IAAA,eAAK,EAAC,CAAC,UAAU,CAAC,CAAC;IACnB,IAAA,eAAK,EAAC,CAAC,WAAW,CAAC,CAAC;IACpB,IAAA,eAAK,EAAC,CAAC,YAAY,CAAC,CAAC;IACrB,IAAA,eAAK,EAAC,CAAC,WAAW,CAAC,CAAC;IACpB,IAAA,eAAK,EAAC,CAAC,WAAW,CAAC,CAAC;GACR,oBAAoB,CA0VhC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\reporting\\alerting\\entities\\notification-workflow.entity.ts"], "sourcesContent": ["import {\r\n  <PERSON><PERSON><PERSON>,\r\n  PrimaryGeneratedColumn,\r\n  Column,\r\n  CreateDateColumn,\r\n  UpdateDateColumn,\r\n  Index,\r\n  OneToMany,\r\n} from 'typeorm';\r\nimport { WorkflowExecution } from './workflow-execution.entity';\r\nimport { WorkflowSchedule } from './workflow-schedule.entity';\r\n\r\n/**\r\n * Notification Workflow Entity\r\n * \r\n * Represents a notification workflow with comprehensive definition,\r\n * configuration, and execution tracking capabilities.\r\n */\r\n@Entity('notification_workflows')\r\n@Index(['status'])\r\n@Index(['category'])\r\n@Index(['createdBy'])\r\n@Index(['isTemplate'])\r\n@Index(['createdAt'])\r\n@Index(['updatedAt'])\r\nexport class NotificationWorkflow {\r\n  @PrimaryGeneratedColumn('uuid')\r\n  id: string;\r\n\r\n  @Column({ name: 'name' })\r\n  @Index()\r\n  name: string;\r\n\r\n  @Column({ name: 'description', type: 'text', nullable: true })\r\n  description: string;\r\n\r\n  @Column({ name: 'definition', type: 'jsonb' })\r\n  definition: {\r\n    startStep: string;\r\n    steps: Array<{\r\n      id: string;\r\n      type: string;\r\n      config: Record<string, any>;\r\n      nextStep?: string;\r\n      nextSteps?: Array<{\r\n        stepId: string;\r\n        condition?: any;\r\n      }>;\r\n      errorHandling?: {\r\n        retry?: {\r\n          maxAttempts: number;\r\n          delay: number;\r\n        };\r\n        fallbackStep?: string;\r\n        continueOnError?: boolean;\r\n      };\r\n    }>;\r\n    triggers?: Array<{\r\n      type: 'cron' | 'event' | 'webhook' | 'manual';\r\n      cron?: string;\r\n      timezone?: string;\r\n      eventType?: string;\r\n      eventPattern?: Record<string, any>;\r\n      webhookPath?: string;\r\n      conditions?: Array<{\r\n        field: string;\r\n        operator: string;\r\n        value: any;\r\n      }>;\r\n    }>;\r\n    errorHandling?: {\r\n      retry?: {\r\n        maxAttempts: number;\r\n        delay: number;\r\n      };\r\n      continueOnError?: boolean;\r\n      parallelFailureHandling?: 'fail_all' | 'continue_on_partial_success';\r\n    };\r\n  };\r\n\r\n  @Column({ name: 'status', default: 'draft' })\r\n  @Index()\r\n  status: 'draft' | 'active' | 'inactive' | 'archived';\r\n\r\n  @Column({ name: 'category', nullable: true })\r\n  @Index()\r\n  category: 'incident_response' | 'monitoring' | 'maintenance' | 'compliance' | 'custom';\r\n\r\n  @Column({ name: 'tags', type: 'text', array: true, default: '{}' })\r\n  tags: string[];\r\n\r\n  @Column({ name: 'version', default: '1.0.0' })\r\n  version: string;\r\n\r\n  @Column({ name: 'is_template', default: false })\r\n  @Index()\r\n  isTemplate: boolean;\r\n\r\n  @Column({ name: 'configuration', type: 'jsonb', nullable: true })\r\n  configuration: {\r\n    timeout?: number;\r\n    maxConcurrentExecutions?: number;\r\n    retryPolicy?: {\r\n      enabled: boolean;\r\n      maxRetries: number;\r\n      backoffMultiplier: number;\r\n    };\r\n    notifications?: {\r\n      onSuccess?: string[];\r\n      onFailure?: string[];\r\n      onTimeout?: string[];\r\n    };\r\n    variables?: Record<string, any>;\r\n    permissions?: {\r\n      execute?: string[];\r\n      modify?: string[];\r\n      view?: string[];\r\n    };\r\n    monitoring?: {\r\n      enabled: boolean;\r\n      alertOnFailure: boolean;\r\n      alertOnTimeout: boolean;\r\n      metricsCollection: boolean;\r\n    };\r\n  };\r\n\r\n  @Column({ name: 'metadata', type: 'jsonb', nullable: true })\r\n  metadata: {\r\n    author?: string;\r\n    version?: string;\r\n    documentation?: string;\r\n    changelog?: Array<{\r\n      version: string;\r\n      date: string;\r\n      changes: string[];\r\n    }>;\r\n    dependencies?: string[];\r\n    estimatedDuration?: number;\r\n    complexity?: 'low' | 'medium' | 'high';\r\n    businessImpact?: 'low' | 'medium' | 'high' | 'critical';\r\n    maintenanceSchedule?: {\r\n      reviewFrequency: string;\r\n      lastReviewed?: string;\r\n      nextReview?: string;\r\n    };\r\n  };\r\n\r\n  @Column({ name: 'created_by' })\r\n  @Index()\r\n  createdBy: string;\r\n\r\n  @Column({ name: 'updated_by' })\r\n  updatedBy: string;\r\n\r\n  @CreateDateColumn({ name: 'created_at' })\r\n  @Index()\r\n  createdAt: Date;\r\n\r\n  @UpdateDateColumn({ name: 'updated_at' })\r\n  @Index()\r\n  updatedAt: Date;\r\n\r\n  // Relations\r\n  @OneToMany(() => WorkflowExecution, execution => execution.workflow)\r\n  executions: WorkflowExecution[];\r\n\r\n  @OneToMany(() => WorkflowSchedule, schedule => schedule.workflow)\r\n  schedules: WorkflowSchedule[];\r\n\r\n  // Computed properties\r\n  get stepCount(): number {\r\n    return this.definition?.steps?.length || 0;\r\n  }\r\n\r\n  get hasScheduledTriggers(): boolean {\r\n    return this.definition?.triggers?.some(trigger => trigger.type === 'cron') || false;\r\n  }\r\n\r\n  get hasEventTriggers(): boolean {\r\n    return this.definition?.triggers?.some(trigger => trigger.type === 'event') || false;\r\n  }\r\n\r\n  get hasWebhookTriggers(): boolean {\r\n    return this.definition?.triggers?.some(trigger => trigger.type === 'webhook') || false;\r\n  }\r\n\r\n  get isActive(): boolean {\r\n    return this.status === 'active';\r\n  }\r\n\r\n  get isDraft(): boolean {\r\n    return this.status === 'draft';\r\n  }\r\n\r\n  get complexity(): 'low' | 'medium' | 'high' {\r\n    if (this.metadata?.complexity) {\r\n      return this.metadata.complexity;\r\n    }\r\n\r\n    // Auto-calculate complexity based on step count and structure\r\n    const stepCount = this.stepCount;\r\n    const hasConditionalLogic = this.definition?.steps?.some(step => \r\n      step.nextSteps && step.nextSteps.length > 1\r\n    ) || false;\r\n    const hasErrorHandling = this.definition?.errorHandling || \r\n      this.definition?.steps?.some(step => step.errorHandling) || false;\r\n\r\n    if (stepCount <= 3 && !hasConditionalLogic && !hasErrorHandling) {\r\n      return 'low';\r\n    } else if (stepCount <= 10 && (!hasConditionalLogic || !hasErrorHandling)) {\r\n      return 'medium';\r\n    } else {\r\n      return 'high';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get workflow summary\r\n   */\r\n  getWorkflowSummary(): Record<string, any> {\r\n    return {\r\n      id: this.id,\r\n      name: this.name,\r\n      description: this.description,\r\n      status: this.status,\r\n      category: this.category,\r\n      version: this.version,\r\n      isTemplate: this.isTemplate,\r\n      stepCount: this.stepCount,\r\n      complexity: this.complexity,\r\n      hasScheduledTriggers: this.hasScheduledTriggers,\r\n      hasEventTriggers: this.hasEventTriggers,\r\n      hasWebhookTriggers: this.hasWebhookTriggers,\r\n      createdAt: this.createdAt,\r\n      updatedAt: this.updatedAt,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get trigger summary\r\n   */\r\n  getTriggerSummary(): Array<Record<string, any>> {\r\n    return this.definition?.triggers?.map(trigger => ({\r\n      type: trigger.type,\r\n      cron: trigger.cron,\r\n      eventType: trigger.eventType,\r\n      webhookPath: trigger.webhookPath,\r\n      hasConditions: trigger.conditions && trigger.conditions.length > 0,\r\n    })) || [];\r\n  }\r\n\r\n  /**\r\n   * Get step types used in workflow\r\n   */\r\n  getStepTypes(): string[] {\r\n    const stepTypes = this.definition?.steps?.map(step => step.type) || [];\r\n    return [...new Set(stepTypes)];\r\n  }\r\n\r\n  /**\r\n   * Check if workflow uses specific step type\r\n   */\r\n  usesStepType(stepType: string): boolean {\r\n    return this.definition?.steps?.some(step => step.type === stepType) || false;\r\n  }\r\n\r\n  /**\r\n   * Get notification channels used in workflow\r\n   */\r\n  getNotificationChannels(): string[] {\r\n    const channels = [];\r\n    \r\n    this.definition?.steps?.forEach(step => {\r\n      if (step.type === 'notification' && step.config?.channel) {\r\n        channels.push(step.config.channel);\r\n      }\r\n    });\r\n\r\n    return [...new Set(channels)];\r\n  }\r\n\r\n  /**\r\n   * Check if workflow is ready for execution\r\n   */\r\n  isReadyForExecution(): boolean {\r\n    return this.status === 'active' && \r\n           this.definition?.startStep && \r\n           this.definition?.steps?.length > 0;\r\n  }\r\n\r\n  /**\r\n   * Get estimated execution duration\r\n   */\r\n  getEstimatedDuration(): number {\r\n    if (this.metadata?.estimatedDuration) {\r\n      return this.metadata.estimatedDuration;\r\n    }\r\n\r\n    // Simple estimation based on step count and types\r\n    let duration = 0;\r\n    \r\n    this.definition?.steps?.forEach(step => {\r\n      switch (step.type) {\r\n        case 'notification':\r\n          duration += 30000; // 30 seconds\r\n          break;\r\n        case 'delay':\r\n          duration += step.config?.delay || 60000; // Default 1 minute\r\n          break;\r\n        case 'condition':\r\n          duration += 5000; // 5 seconds\r\n          break;\r\n        case 'http_request':\r\n          duration += 15000; // 15 seconds\r\n          break;\r\n        default:\r\n          duration += 10000; // 10 seconds default\r\n      }\r\n    });\r\n\r\n    return duration;\r\n  }\r\n\r\n  /**\r\n   * Validate workflow definition\r\n   */\r\n  validateDefinition(): { valid: boolean; errors: string[] } {\r\n    const errors: string[] = [];\r\n\r\n    if (!this.definition) {\r\n      errors.push('Workflow definition is required');\r\n      return { valid: false, errors };\r\n    }\r\n\r\n    if (!this.definition.startStep) {\r\n      errors.push('Start step is required');\r\n    }\r\n\r\n    if (!this.definition.steps || this.definition.steps.length === 0) {\r\n      errors.push('At least one step is required');\r\n    }\r\n\r\n    // Validate start step exists\r\n    if (this.definition.startStep && this.definition.steps) {\r\n      const startStepExists = this.definition.steps.some(step => step.id === this.definition.startStep);\r\n      if (!startStepExists) {\r\n        errors.push(`Start step '${this.definition.startStep}' not found in steps`);\r\n      }\r\n    }\r\n\r\n    // Validate step references\r\n    if (this.definition.steps) {\r\n      const stepIds = this.definition.steps.map(step => step.id);\r\n      \r\n      this.definition.steps.forEach(step => {\r\n        if (step.nextStep && !stepIds.includes(step.nextStep)) {\r\n          errors.push(`Step '${step.id}' references non-existent next step '${step.nextStep}'`);\r\n        }\r\n\r\n        if (step.nextSteps) {\r\n          step.nextSteps.forEach(nextStep => {\r\n            if (!stepIds.includes(nextStep.stepId)) {\r\n              errors.push(`Step '${step.id}' references non-existent next step '${nextStep.stepId}'`);\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n\r\n    return { valid: errors.length === 0, errors };\r\n  }\r\n}\r\n"], "version": 3}