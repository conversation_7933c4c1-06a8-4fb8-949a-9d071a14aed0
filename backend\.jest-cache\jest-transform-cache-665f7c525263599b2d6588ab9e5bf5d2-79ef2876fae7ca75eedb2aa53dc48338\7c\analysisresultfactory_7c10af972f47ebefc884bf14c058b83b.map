{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\factories\\analysis-result.factory.ts", "mappings": ";;;AAAA,yHAAuG;AACvG,+EAY4C;AA0C5C,MAAa,qBAAqB;IAChC;;OAEG;IACI,MAAM,CAAC,MAAM,CAAC,OAAoC,EAAE,EAAmB;QAC5E,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;QAEpC,MAAM,KAAK,GAA8E;YACvF,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE;YACnC,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,UAAU,EAAE,IAAI,CAAC,mBAAmB,EAAE;YACtC,UAAU,EAAE,CAAC;YACb,cAAc,EAAE,CAAC;YACjB,MAAM,EAAE,uCAAc,CAAC,OAAO;YAC9B,QAAQ,EAAE,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,QAAQ,CAAC;YACtD,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAChF,aAAa,EAAE,OAAO,CAAC,aAAa,EAAE,IAAI,EAAE;YAC5C,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;YAC1C,YAAY,EAAE,SAAS;YACvB,WAAW,EAAE,SAAS;SACvB,CAAC;QAEF,OAAO,uCAAc,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,YAAY,CAAC,IAAwB;QACjD,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;QAEtC,MAAM,KAAK,GAAwB;YACjC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,WAAW,EAAE,IAAI,CAAC,WAAW;SAC9B,CAAC;QAEF,MAAM,EAAE,GAAG,8CAAc,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC9C,OAAO,uCAAc,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,mBAAmB;QAChC,OAAO;YACL,OAAO,EAAE,EAAE;YACX,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,CAAC;YACP,QAAQ,EAAE,EAAE;YACZ,qBAAqB,EAAE,EAAE;YACzB,gBAAgB,EAAE,SAAS;YAC3B,YAAY,EAAE,CAAC;SAChB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,qBAAqB,CAAC,OAAmC;QACtE,OAAO;YACL,OAAO,EAAE,OAAO,EAAE,OAAO,IAAI,OAAO;YACpC,SAAS,EAAE,OAAO,EAAE,SAAS,IAAI,SAAS;YAC1C,UAAU,EAAE,OAAO,EAAE,UAAU,IAAI,EAAE;YACrC,WAAW,EAAE,OAAO,EAAE,WAAW,IAAI,YAAY;YACjD,aAAa,EAAE,OAAO,EAAE,aAAa,IAAI,IAAI,CAAC,0BAA0B,EAAE;YAC1E,kBAAkB,EAAE,OAAO,EAAE,kBAAkB,IAAI,IAAI,CAAC,+BAA+B,EAAE;YACzF,cAAc,EAAE,OAAO,EAAE,cAAc,IAAI,IAAI,CAAC,2BAA2B,EAAE;SAC9E,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,0BAA0B;QACvC,OAAO;YACL,OAAO,EAAE,CAAC;YACV,WAAW,EAAE,CAAC;YACd,OAAO,EAAE,CAAC;YACV,SAAS,EAAE,CAAC;YACZ,MAAM,EAAE,CAAC;SACV,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,+BAA+B;QAC5C,OAAO;YACL,UAAU,EAAE,CAAC;YACb,OAAO,EAAE,CAAC;YACV,QAAQ,EAAE,CAAC;YACX,SAAS,EAAE,CAAC;YACZ,MAAM,EAAE,CAAC;YACT,OAAO,EAAE,CAAC;SACX,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,2BAA2B;QACxC,OAAO;YACL,WAAW,EAAE,CAAC;YACd,iBAAiB,EAAE,CAAC;YACpB,gBAAgB,EAAE,CAAC;YACnB,iBAAiB,EAAE,CAAC;SACrB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,qBAAqB,CAAC,OAAoC;QACvE,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChE,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YACnC,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,qCAAY,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;YAChE,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAE1C,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC;YAC9E,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvE,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,iBAAiB,CAAC,SAAwB;QACvD,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9D,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,SAAS,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClE,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,oBAAoB,CAAC,EAAE,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,eAAe,CAAC,EAAE,CAAC;YAC9C,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,0BAA0B,CAAC,IAAwB;QAChE,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,8CAAc,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;YACjD,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1D,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,qCAAY,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;YAC7D,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,uCAAc,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YACzD,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;YAC/C,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC1C,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,YAAY,IAAI,CAAC,EAAE,CAAC;YACzD,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,YAAY,IAAI,CAAC,EAAE,CAAC;YACzD,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,YAAY,IAAI,CAAC,EAAE,CAAC;YAC5D,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;QACrE,CAAC;QAED,8BAA8B;QAC9B,IAAI,IAAI,CAAC,MAAM,KAAK,uCAAc,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACjE,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,KAAK,uCAAc,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YAChE,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,gBAAgB,CAAC,SAAgD;QAC7E,MAAM,cAAc,GAAgC;YAClD,SAAS,EAAE,kBAAkB;YAC7B,OAAO,EAAE,8CAAc,CAAC,QAAQ,EAAE;YAClC,YAAY,EAAE,qCAAY,CAAC,cAAc;YACzC,SAAS,EAAE;gBACT,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;gBACtB,MAAM,EAAE,MAAM;gBACd,IAAI,EAAE,GAAG;gBACT,QAAQ,EAAE,eAAe;gBACzB,oBAAoB,EAAE,EAAE;gBACxB,eAAe,EAAE,EAAE;aACpB;YACD,IAAI,EAAE,CAAC,MAAM,CAAC;YACd,aAAa,EAAE,sBAAsB;SACtC,CAAC;QAEF,MAAM,OAAO,GAAG,EAAE,GAAG,cAAc,EAAE,GAAG,SAAS,EAAE,CAAC;QACpD,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,yBAAyB,CAAC,SAAgD;QACtF,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAExD,mBAAmB;QACnB,cAAc,CAAC,eAAe,EAAE,CAAC;QAEjC,0BAA0B;QAC1B,MAAM,UAAU,GAAmB;YACjC,OAAO,EAAE,EAAE,UAAU,EAAE,aAAa,EAAE,UAAU,EAAE,IAAI,EAAE;YACxD,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,GAAG;YACT,QAAQ,EAAE,iBAAiB;YAC3B,qBAAqB,EAAE,CAAC,eAAe,CAAC;YACxC,gBAAgB,EAAE,QAAQ;YAC1B,YAAY,EAAE,GAAG;SAClB,CAAC;QAEF,cAAc,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAEhD,OAAO,cAAc,CAAC;IACxB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,sBAAsB,CAAC,SAAgD;QACnF,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAExD,mBAAmB;QACnB,cAAc,CAAC,eAAe,EAAE,CAAC;QAEjC,uBAAuB;QACvB,MAAM,YAAY,GAAiB;YACjC,IAAI,EAAE,YAAY;YAClB,OAAO,EAAE,6BAA6B;YACtC,OAAO,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;YACvB,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,kBAAyB;SACpC,CAAC;QAEF,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAElC,OAAO,cAAc,CAAC;IACxB,CAAC;CACF;AA3UD,sDA2UC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\domain\\factories\\analysis-result.factory.ts"], "sourcesContent": ["import { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';\r\nimport { \r\n  AnalysisResult, \r\n  AnalysisResultProps, \r\n  AnalysisType,\r\n  AnalysisStatus,\r\n  AnalysisInput,\r\n  AnalysisOutput,\r\n  AnalysisMetadata,\r\n  ErrorDetails,\r\n  ResourceUsage,\r\n  PerformanceMetrics,\r\n  QualityMetrics\r\n} from '../entities/analysis-result.entity';\r\n\r\n/**\r\n * Analysis Result Factory\r\n * \r\n * Factory for creating Analysis Result entities with proper validation\r\n * and default values. Encapsulates the complex creation logic\r\n * and ensures consistent analysis result initialization.\r\n */\r\n\r\nexport interface CreateAnalysisResultRequest {\r\n  requestId: string;\r\n  modelId: UniqueEntityId;\r\n  analysisType: AnalysisType;\r\n  inputData: AnalysisInput;\r\n  metadata?: Partial<AnalysisMetadata>;\r\n  tags?: string[];\r\n  correlationId?: string;\r\n  parentAnalysisId?: UniqueEntityId;\r\n}\r\n\r\nexport interface ReconstitutionData {\r\n  id: string;\r\n  requestId: string;\r\n  modelId: UniqueEntityId;\r\n  analysisType: AnalysisType;\r\n  inputData: AnalysisInput;\r\n  outputData: AnalysisOutput;\r\n  confidence: number;\r\n  processingTime: number;\r\n  status: AnalysisStatus;\r\n  metadata: AnalysisMetadata;\r\n  tags: string[];\r\n  correlationId?: string;\r\n  parentAnalysisId?: UniqueEntityId;\r\n  childAnalysisIds: UniqueEntityId[];\r\n  errorDetails?: ErrorDetails;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n  completedAt?: Date;\r\n}\r\n\r\nexport class AnalysisResultFactory {\r\n  /**\r\n   * Creates a new Analysis Result entity\r\n   */\r\n  public static create(request: CreateAnalysisResultRequest, id?: UniqueEntityId): AnalysisResult {\r\n    this.validateCreateRequest(request);\r\n\r\n    const props: Omit<AnalysisResultProps, 'createdAt' | 'updatedAt' | 'childAnalysisIds'> = {\r\n      requestId: request.requestId.trim(),\r\n      modelId: request.modelId,\r\n      analysisType: request.analysisType,\r\n      inputData: request.inputData,\r\n      outputData: this.createDefaultOutput(),\r\n      confidence: 0,\r\n      processingTime: 0,\r\n      status: AnalysisStatus.PENDING,\r\n      metadata: this.createDefaultMetadata(request.metadata),\r\n      tags: request.tags ? [...request.tags.map(tag => tag.trim().toLowerCase())] : [],\r\n      correlationId: request.correlationId?.trim(),\r\n      parentAnalysisId: request.parentAnalysisId,\r\n      errorDetails: undefined,\r\n      completedAt: undefined,\r\n    };\r\n\r\n    return AnalysisResult.create(props, id);\r\n  }\r\n\r\n  /**\r\n   * Reconstitutes an Analysis Result entity from persistence data\r\n   */\r\n  public static reconstitute(data: ReconstitutionData): AnalysisResult {\r\n    this.validateReconstitutionData(data);\r\n\r\n    const props: AnalysisResultProps = {\r\n      requestId: data.requestId,\r\n      modelId: data.modelId,\r\n      analysisType: data.analysisType,\r\n      inputData: data.inputData,\r\n      outputData: data.outputData,\r\n      confidence: data.confidence,\r\n      processingTime: data.processingTime,\r\n      status: data.status,\r\n      metadata: data.metadata,\r\n      tags: data.tags,\r\n      correlationId: data.correlationId,\r\n      parentAnalysisId: data.parentAnalysisId,\r\n      childAnalysisIds: data.childAnalysisIds,\r\n      errorDetails: data.errorDetails,\r\n      createdAt: data.createdAt,\r\n      updatedAt: data.updatedAt,\r\n      completedAt: data.completedAt,\r\n    };\r\n\r\n    const id = UniqueEntityId.fromString(data.id);\r\n    return AnalysisResult.reconstitute(props, id);\r\n  }\r\n\r\n  /**\r\n   * Creates a default output structure\r\n   */\r\n  private static createDefaultOutput(): AnalysisOutput {\r\n    return {\r\n      results: {},\r\n      format: 'json',\r\n      size: 0,\r\n      checksum: '',\r\n      postprocessingApplied: [],\r\n      validationStatus: 'pending',\r\n      qualityScore: 0,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Creates default metadata\r\n   */\r\n  private static createDefaultMetadata(partial?: Partial<AnalysisMetadata>): AnalysisMetadata {\r\n    return {\r\n      version: partial?.version ?? '1.0.0',\r\n      algorithm: partial?.algorithm ?? 'unknown',\r\n      parameters: partial?.parameters ?? {},\r\n      environment: partial?.environment ?? 'production',\r\n      resourceUsage: partial?.resourceUsage ?? this.createDefaultResourceUsage(),\r\n      performanceMetrics: partial?.performanceMetrics ?? this.createDefaultPerformanceMetrics(),\r\n      qualityMetrics: partial?.qualityMetrics ?? this.createDefaultQualityMetrics(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Creates default resource usage\r\n   */\r\n  private static createDefaultResourceUsage(): ResourceUsage {\r\n    return {\r\n      cpuTime: 0,\r\n      memoryUsage: 0,\r\n      gpuTime: 0,\r\n      networkIO: 0,\r\n      diskIO: 0,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Creates default performance metrics\r\n   */\r\n  private static createDefaultPerformanceMetrics(): PerformanceMetrics {\r\n    return {\r\n      throughput: 0,\r\n      latency: 0,\r\n      accuracy: 0,\r\n      precision: 0,\r\n      recall: 0,\r\n      f1Score: 0,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Creates default quality metrics\r\n   */\r\n  private static createDefaultQualityMetrics(): QualityMetrics {\r\n    return {\r\n      dataQuality: 0,\r\n      resultReliability: 0,\r\n      consistencyScore: 0,\r\n      completenessScore: 0,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Validates create request\r\n   */\r\n  private static validateCreateRequest(request: CreateAnalysisResultRequest): void {\r\n    if (!request.requestId || request.requestId.trim().length === 0) {\r\n      throw new Error('Request ID is required');\r\n    }\r\n\r\n    if (request.requestId.length > 255) {\r\n      throw new Error('Request ID cannot exceed 255 characters');\r\n    }\r\n\r\n    if (!request.modelId) {\r\n      throw new Error('Model ID is required');\r\n    }\r\n\r\n    if (!Object.values(AnalysisType).includes(request.analysisType)) {\r\n      throw new Error('Invalid analysis type');\r\n    }\r\n\r\n    if (!request.inputData) {\r\n      throw new Error('Input data is required');\r\n    }\r\n\r\n    this.validateInputData(request.inputData);\r\n\r\n    if (request.tags && request.tags.some(tag => !tag || tag.trim().length === 0)) {\r\n      throw new Error('All tags must be non-empty strings');\r\n    }\r\n\r\n    if (request.correlationId && request.correlationId.trim().length === 0) {\r\n      throw new Error('Correlation ID cannot be empty if provided');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validates input data\r\n   */\r\n  private static validateInputData(inputData: AnalysisInput): void {\r\n    if (!inputData.data) {\r\n      throw new Error('Input data content is required');\r\n    }\r\n\r\n    if (!inputData.format || inputData.format.trim().length === 0) {\r\n      throw new Error('Input data format is required');\r\n    }\r\n\r\n    if (inputData.size < 0) {\r\n      throw new Error('Input data size cannot be negative');\r\n    }\r\n\r\n    if (!inputData.checksum || inputData.checksum.trim().length === 0) {\r\n      throw new Error('Input data checksum is required');\r\n    }\r\n\r\n    if (!Array.isArray(inputData.preprocessingApplied)) {\r\n      throw new Error('Preprocessing applied must be an array');\r\n    }\r\n\r\n    if (!Array.isArray(inputData.validationRules)) {\r\n      throw new Error('Validation rules must be an array');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validates reconstitution data\r\n   */\r\n  private static validateReconstitutionData(data: ReconstitutionData): void {\r\n    if (!data.id || !UniqueEntityId.isValid(data.id)) {\r\n      throw new Error('Valid ID is required for reconstitution');\r\n    }\r\n\r\n    if (!data.requestId || data.requestId.trim().length === 0) {\r\n      throw new Error('Request ID is required');\r\n    }\r\n\r\n    if (!data.modelId) {\r\n      throw new Error('Model ID is required');\r\n    }\r\n\r\n    if (!Object.values(AnalysisType).includes(data.analysisType)) {\r\n      throw new Error('Invalid analysis type');\r\n    }\r\n\r\n    if (!Object.values(AnalysisStatus).includes(data.status)) {\r\n      throw new Error('Invalid analysis status');\r\n    }\r\n\r\n    if (!data.inputData) {\r\n      throw new Error('Input data is required');\r\n    }\r\n\r\n    if (!data.metadata) {\r\n      throw new Error('Metadata is required');\r\n    }\r\n\r\n    if (data.confidence < 0 || data.confidence > 1) {\r\n      throw new Error('Confidence must be between 0 and 1');\r\n    }\r\n\r\n    if (data.processingTime < 0) {\r\n      throw new Error('Processing time cannot be negative');\r\n    }\r\n\r\n    if (!Array.isArray(data.tags)) {\r\n      throw new Error('Tags must be an array');\r\n    }\r\n\r\n    if (!Array.isArray(data.childAnalysisIds)) {\r\n      throw new Error('Child analysis IDs must be an array');\r\n    }\r\n\r\n    if (!data.createdAt || !(data.createdAt instanceof Date)) {\r\n      throw new Error('Valid creation date is required');\r\n    }\r\n\r\n    if (!data.updatedAt || !(data.updatedAt instanceof Date)) {\r\n      throw new Error('Valid update date is required');\r\n    }\r\n\r\n    if (data.completedAt && !(data.completedAt instanceof Date)) {\r\n      throw new Error('Completed date must be a valid Date if provided');\r\n    }\r\n\r\n    // Status-specific validations\r\n    if (data.status === AnalysisStatus.COMPLETED && !data.outputData) {\r\n      throw new Error('Output data is required for completed analysis');\r\n    }\r\n\r\n    if (data.status === AnalysisStatus.FAILED && !data.errorDetails) {\r\n      throw new Error('Error details are required for failed analysis');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Creates a test Analysis Result with minimal configuration\r\n   */\r\n  public static createForTesting(overrides?: Partial<CreateAnalysisResultRequest>): AnalysisResult {\r\n    const defaultRequest: CreateAnalysisResultRequest = {\r\n      requestId: 'test-request-123',\r\n      modelId: UniqueEntityId.generate(),\r\n      analysisType: AnalysisType.CLASSIFICATION,\r\n      inputData: {\r\n        data: { test: 'data' },\r\n        format: 'json',\r\n        size: 100,\r\n        checksum: 'test-checksum',\r\n        preprocessingApplied: [],\r\n        validationRules: [],\r\n      },\r\n      tags: ['test'],\r\n      correlationId: 'test-correlation-123',\r\n    };\r\n\r\n    const request = { ...defaultRequest, ...overrides };\r\n    return this.create(request);\r\n  }\r\n\r\n  /**\r\n   * Creates an Analysis Result with completed status for testing\r\n   */\r\n  public static createCompletedForTesting(overrides?: Partial<CreateAnalysisResultRequest>): AnalysisResult {\r\n    const analysisResult = this.createForTesting(overrides);\r\n    \r\n    // Start processing\r\n    analysisResult.startProcessing();\r\n    \r\n    // Complete with test data\r\n    const outputData: AnalysisOutput = {\r\n      results: { prediction: 'test-result', confidence: 0.95 },\r\n      format: 'json',\r\n      size: 200,\r\n      checksum: 'output-checksum',\r\n      postprocessingApplied: ['normalization'],\r\n      validationStatus: 'passed',\r\n      qualityScore: 0.9,\r\n    };\r\n    \r\n    analysisResult.complete(outputData, 1500, 0.95);\r\n    \r\n    return analysisResult;\r\n  }\r\n\r\n  /**\r\n   * Creates an Analysis Result with failed status for testing\r\n   */\r\n  public static createFailedForTesting(overrides?: Partial<CreateAnalysisResultRequest>): AnalysisResult {\r\n    const analysisResult = this.createForTesting(overrides);\r\n    \r\n    // Start processing\r\n    analysisResult.startProcessing();\r\n    \r\n    // Fail with test error\r\n    const errorDetails: ErrorDetails = {\r\n      code: 'TEST_ERROR',\r\n      message: 'Test error for unit testing',\r\n      context: { test: true },\r\n      retryable: true,\r\n      category: 'processing_error' as any,\r\n    };\r\n    \r\n    analysisResult.fail(errorDetails);\r\n    \r\n    return analysisResult;\r\n  }\r\n}"], "version": 3}